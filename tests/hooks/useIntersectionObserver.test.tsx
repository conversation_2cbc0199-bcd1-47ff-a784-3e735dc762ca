import { renderHook, act } from '@testing-library/react';
import { useIntersectionObserver } from '@/hooks/useIntersectionObserver';

// Mock IntersectionObserver
const mockIntersectionObserver = jest.fn();
const mockObserve = jest.fn();
const mockUnobserve = jest.fn();
const mockDisconnect = jest.fn();

beforeEach(() => {
  mockIntersectionObserver.mockImplementation((callback) => ({
    observe: mockObserve,
    unobserve: mockUnobserve,
    disconnect: mockDisconnect,
    callback,
  }));

  (window as any).IntersectionObserver = mockIntersectionObserver;
});

afterEach(() => {
  jest.clearAllMocks();
});

describe('useIntersectionObserver', () => {
  it('should initialize with correct default values', () => {
    const { result } = renderHook(() => useIntersectionObserver());
    
    const [setRef, isVisible, hasTriggered] = result.current;
    
    expect(typeof setRef).toBe('function');
    expect(isVisible).toBe(false);
    expect(hasTriggered).toBe(false);
  });

  it('should initialize with custom initial values', () => {
    const { result } = renderHook(() => 
      useIntersectionObserver({ initialInView: true })
    );
    
    const [, isVisible] = result.current;
    expect(isVisible).toBe(true);
  });

  it('should create IntersectionObserver with correct options', () => {
    const options = {
      threshold: 0.5,
      rootMargin: '100px',
      triggerOnce: true,
    };

    renderHook(() => useIntersectionObserver(options));

    expect(mockIntersectionObserver).toHaveBeenCalledWith(
      expect.any(Function),
      {
        threshold: 0.5,
        rootMargin: '100px',
      }
    );
  });

  it('should observe element when ref is set', () => {
    const { result } = renderHook(() => useIntersectionObserver());
    const [setRef] = result.current;
    
    const mockElement = document.createElement('div');
    
    act(() => {
      setRef(mockElement);
    });

    expect(mockObserve).toHaveBeenCalledWith(mockElement);
  });

  it('should unobserve previous element when ref changes', () => {
    const { result } = renderHook(() => useIntersectionObserver());
    const [setRef] = result.current;
    
    const firstElement = document.createElement('div');
    const secondElement = document.createElement('div');
    
    act(() => {
      setRef(firstElement);
    });

    act(() => {
      setRef(secondElement);
    });

    expect(mockUnobserve).toHaveBeenCalledWith(firstElement);
    expect(mockObserve).toHaveBeenCalledWith(secondElement);
  });

  it('should handle visibility changes correctly', () => {
    let intersectionCallback: (entries: any[]) => void;
    
    mockIntersectionObserver.mockImplementation((callback) => {
      intersectionCallback = callback;
      return {
        observe: mockObserve,
        unobserve: mockUnobserve,
        disconnect: mockDisconnect,
      };
    });

    const { result } = renderHook(() => useIntersectionObserver());
    const [setRef] = result.current;
    
    const mockElement = document.createElement('div');
    
    act(() => {
      setRef(mockElement);
    });

    // Simulate intersection
    act(() => {
      intersectionCallback([{ isIntersecting: true }]);
    });

    const [, isVisible, hasTriggered] = result.current;
    expect(isVisible).toBe(true);
    expect(hasTriggered).toBe(false);
  });

  it('should handle triggerOnce option correctly', () => {
    let intersectionCallback: (entries: any[]) => void;
    
    mockIntersectionObserver.mockImplementation((callback) => {
      intersectionCallback = callback;
      return {
        observe: mockObserve,
        unobserve: mockUnobserve,
        disconnect: mockDisconnect,
      };
    });

    const { result } = renderHook(() => 
      useIntersectionObserver({ triggerOnce: true })
    );
    const [setRef] = result.current;
    
    const mockElement = document.createElement('div');
    
    act(() => {
      setRef(mockElement);
    });

    // Simulate intersection
    act(() => {
      intersectionCallback([{ isIntersecting: true }]);
    });

    const [, , hasTriggered] = result.current;
    expect(hasTriggered).toBe(true);
  });

  it('should handle delay option correctly', async () => {
    jest.useFakeTimers();
    
    let intersectionCallback: (entries: any[]) => void;
    
    mockIntersectionObserver.mockImplementation((callback) => {
      intersectionCallback = callback;
      return {
        observe: mockObserve,
        unobserve: mockUnobserve,
        disconnect: mockDisconnect,
      };
    });

    const { result } = renderHook(() => 
      useIntersectionObserver({ delay: 1000 })
    );
    const [setRef] = result.current;
    
    const mockElement = document.createElement('div');
    
    act(() => {
      setRef(mockElement);
    });

    // Simulate intersection
    act(() => {
      intersectionCallback([{ isIntersecting: true }]);
    });

    // Should not be visible immediately
    expect(result.current[1]).toBe(false);

    // Fast-forward time
    act(() => {
      jest.advanceTimersByTime(1000);
    });

    // Should be visible after delay
    expect(result.current[1]).toBe(true);

    jest.useRealTimers();
  });

  it('should skip observation when skip option is true', () => {
    renderHook(() => useIntersectionObserver({ skip: true }));

    expect(mockIntersectionObserver).not.toHaveBeenCalled();
  });

  it('should disconnect observer on unmount', () => {
    const { unmount } = renderHook(() => useIntersectionObserver());

    unmount();

    expect(mockDisconnect).toHaveBeenCalled();
  });
}); 