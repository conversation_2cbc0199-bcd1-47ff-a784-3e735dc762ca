/**
 * Test Utilities for Enterprise Testing
 * Provides helper functions for testing React components, APIs, and business logic
 */

/// <reference types="jest" />

import React from 'react';
import { render, RenderOptions } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

// Test utilities without NextAuth dependency
const createTestQueryClient = () =>
  new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });

const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  const queryClient = createTestQueryClient();

  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

const customRender = (
  ui: React.ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => render(ui, { wrapper: AllTheProviders, ...options });

export * from '@testing-library/react';
export { customRender as render };

// Mock session data for tests
export const mockSession = {
  user: {
    id: 'test-user-id',
    username: 'testuser',
    email: '<EMAIL>',
    role: { id: 'admin', name: 'Administrator', permissions: ['admin'] },
  },
  expires: '2024-12-31T23:59:59.999Z',
}

// Mock NextAuth session
export const mockUseSession = {
  data: null,
  status: 'loading',
  update: jest.fn(),
}

// Mock API response helper
export const mockApiResponse = <T,>(data: T, options: {
  status?: number
  ok?: boolean
  headers?: Record<string, string>
} = {}) => {
  const { status = 200, ok = true, headers = {} } = options
  
  return Promise.resolve({
    ok,
    status,
    headers: new Headers(headers),
    json: () => Promise.resolve(data),
    text: () => Promise.resolve(JSON.stringify(data)),
  } as Response)
}

// Mock failed API response
export const mockApiError = (message: string, status: number = 500) => {
  return Promise.resolve({
    ok: false,
    status,
    headers: new Headers(),
    json: () => Promise.resolve({ error: message }),
    text: () => Promise.resolve(JSON.stringify({ error: message })),
  } as Response)
}

// Wait for async operations
export const waitForAsync = () => new Promise(resolve => setTimeout(resolve, 0))

// Mock fetch implementation
export const mockFetch = (responses: Array<any>) => {
  let callCount = 0
  return jest.fn().mockImplementation(() => {
    const response = responses[callCount] || responses[responses.length - 1]
    callCount++
    return response
  })
}

// Create mock user data
export const createMockUser = (overrides: Partial<typeof mockSession.user> = {}) => ({
  ...mockSession.user,
  ...overrides,
})

// Create mock quote data
export const createMockQuote = (overrides: any = {}) => ({
  id: 'quote-1',
  quoteNumber: 'QUO-001',
  totalAmount: 1000,
  customerName: 'Test Customer',
  phoneNumber: '+254700000000',
  email: '<EMAIL>',
  status: 'draft',
  notes: 'Test quote',
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  issuedAt: null,
  validUntil: null,
  pdfUrl: null,
  discountAmount: 0,
  discountType: null,
  discountValue: 0,
  subtotalAmount: 1000,
  items: [],
  ...overrides,
})

// Create mock invoice data
export const createMockInvoice = (overrides: any = {}) => ({
  id: 'invoice-1',
  invoiceNumber: 'INV-001',
  totalAmount: 1000,
  amountPaid: 0,
  balance: 1000,
  customerName: 'Test Customer',
  phoneNumber: '+254700000000',
  email: '<EMAIL>',
  status: 'draft',
  notes: 'Test invoice',
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  issuedAt: null,
  dueDate: null,
  paidAt: null,
  pdfUrl: null,
  quoteId: null,
  items: [],
  ...overrides,
})

// Create mock receipt data
export const createMockReceipt = (overrides: any = {}) => ({
  id: 'receipt-1',
  receiptNumber: 'REC-001',
  totalAmount: 1000,
  amountPaid: 1000,
  balance: 0,
  customerName: 'Test Customer',
  phoneNumber: '+254700000000',
  email: '<EMAIL>',
  status: 'paid',
  notes: 'Test receipt',
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  issuedAt: new Date().toISOString(),
  paidAt: new Date().toISOString(),
  transactionId: 'TXN-001',
  items: [],
  ...overrides,
})

// Create mock service data
export const createMockService = (overrides: any = {}) => ({
  id: 'service-1',
  name: 'Test Service',
  description: 'A test service',
  price: 100,
  category: 'DIGITAL_MARKETING',
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  ...overrides,
})

// Test data generators
export const generateTestData = {
  users: (count: number) => Array.from({ length: count }, (_, i) => 
    createMockUser({ id: `user-${i + 1}`, username: `user${i + 1}` })
  ),
  quotes: (count: number) => Array.from({ length: count }, (_, i) => 
    createMockQuote({ id: `quote-${i + 1}`, quoteNumber: `QUO-${String(i + 1).padStart(3, '0')}` })
  ),
  invoices: (count: number) => Array.from({ length: count }, (_, i) => 
    createMockInvoice({ id: `invoice-${i + 1}`, invoiceNumber: `INV-${String(i + 1).padStart(3, '0')}` })
  ),
  receipts: (count: number) => Array.from({ length: count }, (_, i) => 
    createMockReceipt({ id: `receipt-${i + 1}`, receiptNumber: `REC-${String(i + 1).padStart(3, '0')}` })
  ),
  services: (count: number) => Array.from({ length: count }, (_, i) => 
    createMockService({ id: `service-${i + 1}`, name: `Service ${i + 1}` })
  ),
}

// Custom test matchers
export const expectToBeWithinRange = (received: number, floor: number, ceiling: number) => {
  expect(received).toBeGreaterThanOrEqual(floor)
  expect(received).toBeLessThanOrEqual(ceiling)
}

// API testing helpers
export const testApiRoute = async (
  handler: Function,
  options: {
    method?: string
    body?: any
    headers?: Record<string, string>
    query?: Record<string, string>
  } = {}
) => {
  const { method = 'GET', body, headers = {}, query = {} } = options

  const req = {
    method,
    headers: new Headers(headers),
    json: () => Promise.resolve(body),
    nextUrl: {
      pathname: '/test',
      searchParams: new URLSearchParams(query),
    },
  }

  return handler(req)
}

// Database testing helpers
export const cleanupDatabase = async () => {
  // This would clean up test database tables
  // Implementation depends on your test database setup
  console.log('Cleaning up test database...')
}

// Performance testing helpers
export const measurePerformance = async (fn: () => Promise<any>) => {
  const start = performance.now()
  const result = await fn()
  const end = performance.now()
  return {
    result,
    duration: end - start,
  }
}

// Error boundary testing
export class TestErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Test error boundary caught error:', error, errorInfo)
  }

  render() {
    if (this.state.hasError) {
      return <div data-testid="error-boundary">Something went wrong</div>
    }

    return this.props.children
  }
} 