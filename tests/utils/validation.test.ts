/**
 * Validation Utilities Tests
 * Simple tests to demonstrate our enterprise testing infrastructure
 */

describe('Validation Utilities', () => {
  describe('Phone Number Validation', () => {
    it('should format Kenyan phone numbers correctly', () => {
      expect(validatePhoneNumber('0701234567')).toBe('+254701234567')
      expect(validatePhoneNumber('701234567')).toBe('+254701234567')
      expect(validatePhoneNumber('+254701234567')).toBe('+254701234567')
    })

    it('should throw error for invalid phone numbers', () => {
      expect(() => validatePhoneNumber('123')).toThrow()
      expect(() => validatePhoneNumber('abcdefghij')).toThrow()
      expect(() => validatePhoneNumber('')).toThrow()
    })
  })

  describe('Input Sanitization', () => {
    it('should sanitize user input properly', () => {
      expect(sanitizeInput('  Hello World  ')).toBe('Hello World')
      expect(sanitizeInput('<script>alert("xss")</script>')).not.toContain('<script>')
      expect(sanitizeInput('')).toBe('')
    })

    it('should handle null and undefined inputs', () => {
      expect(sanitizeInput(null)).toBe('')
      expect(sanitizeInput(undefined)).toBe('')
    })
  })

  describe('Currency Formatting', () => {
    it('should format currency correctly', () => {
      expect(formatCurrency(1000)).toBe('KES 1,000.00')
      expect(formatCurrency(1500.5)).toBe('KES 1,500.50')
      expect(formatCurrency(0)).toBe('KES 0.00')
    })

    it('should handle negative amounts', () => {
      expect(formatCurrency(-100)).toBe('-KES 100.00')
    })

    it('should handle large amounts', () => {
      expect(formatCurrency(1000000)).toBe('KES 1,000,000.00')
    })
  })

  describe('Performance Tests', () => {
    it('should validate phone numbers within performance threshold', () => {
      const startTime = performance.now()
      
      for (let i = 0; i < 1000; i++) {
        validatePhoneNumber('0701234567')
      }
      
      const endTime = performance.now()
      const duration = endTime - startTime
      
      // Should complete 1000 validations in under 100ms
      expect(duration).toBeLessThan(100)
    })

    it('should sanitize inputs efficiently', () => {
      const testInput = 'This is a test string with some content'
      const startTime = performance.now()
      
      for (let i = 0; i < 5000; i++) {
        sanitizeInput(testInput)
      }
      
      const endTime = performance.now()
      const duration = endTime - startTime
      
      // Should complete 5000 sanitizations in under 50ms
      expect(duration).toBeLessThan(50)
    })
  })

  describe('Edge Cases', () => {
    it('should handle edge cases gracefully', () => {
      // Very long phone number
      expect(() => validatePhoneNumber('1'.repeat(20))).toThrow()
      
      // Very long input string
      const longString = 'a'.repeat(10000)
      expect(sanitizeInput(longString)).toBeDefined()
      
      // Special characters in currency
      expect(formatCurrency(NaN)).toBe('KES 0.00')
      expect(formatCurrency(Infinity)).toBe('KES 0.00')
    })
  })
})

// Mock implementations for utilities that don't exist yet
function validatePhoneNumber(phone: string): string {
  if (!phone || typeof phone !== 'string') {
    throw new Error('Invalid phone number')
  }
  
  // Remove all non-digits
  const cleaned = phone.replace(/\D/g, '')
  
  if (cleaned.length < 9 || cleaned.length > 15) {
    throw new Error('Invalid phone number length')
  }
  
  // Handle Kenyan numbers
  if (cleaned.startsWith('254')) {
    return `+${cleaned}`
  } else if (cleaned.startsWith('0') && cleaned.length === 10) {
    return `+254${cleaned.substring(1)}`
  } else if (cleaned.length === 9) {
    return `+254${cleaned}`
  }
  
  throw new Error('Unsupported phone number format')
}

function sanitizeInput(input: any): string {
  if (input === null || input === undefined) {
    return ''
  }
  
  if (typeof input !== 'string') {
    input = String(input)
  }
  
  // Basic XSS protection
  return input
    .trim()
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/<[^>]*>/g, '')
}

function formatCurrency(amount: number): string {
  if (isNaN(amount) || !isFinite(amount)) {
    amount = 0
  }
  
  const isNegative = amount < 0
  const absoluteAmount = Math.abs(amount)
  
  const formatted = new Intl.NumberFormat('en-KE', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(absoluteAmount)
  
  return isNegative ? `-KES ${formatted}` : `KES ${formatted}`
} 