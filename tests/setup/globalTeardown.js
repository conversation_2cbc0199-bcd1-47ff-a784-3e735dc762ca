/**
 * Global Jest Teardown
 * Runs once after all tests
 */

module.exports = async () => {
  console.log('🧹 Cleaning up test environment...')

  // Clean up any global resources
  if (global.testServer) {
    console.log('🛑 Stopping test server...')
    await global.testServer.close()
  }

  // Clear test database if using separate test DB
  if (process.env.DATABASE_URL_TEST && process.env.DATABASE_URL_TEST !== process.env.DATABASE_URL) {
    try {
      const { execSync } = require('child_process')
      console.log('🗄️  Cleaning test database...')
      execSync('npx prisma db push --force-reset', { 
        stdio: 'pipe',
        env: process.env 
      })
      console.log('✅ Test database cleaned')
    } catch (error) {
      console.warn('⚠️  Warning: Could not clean test database:', error.message)
    }
  }

  console.log('✅ Test environment cleanup complete')
} 