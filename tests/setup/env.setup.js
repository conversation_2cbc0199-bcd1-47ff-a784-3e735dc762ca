// Environment setup for Jest tests
process.env.NODE_ENV = 'test';

// Set default test database URL if not provided
if (!process.env.DATABASE_URL) {
  process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/test_db';
}

// Suppress console warnings for cleaner test output
const originalWarn = console.warn;
console.warn = (...args) => {
  // Suppress specific Prisma warnings that are expected in test environment
  if (args[0] && typeof args[0] === 'string' && 
      (args[0].includes('PrismaClient') || 
       args[0].includes('database connection') ||
       args[0].includes('browser environment'))) {
    return;
  }
  originalWarn.apply(console, args);
}; 