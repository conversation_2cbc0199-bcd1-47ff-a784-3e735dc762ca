/**
 * Global Jest Setup
 * Runs once before all tests
 */

const { execSync } = require('child_process')
const { config } = require('dotenv')

module.exports = async () => {
  console.log('🧪 Setting up test environment...')

  // Load test environment variables
  config({ path: '.env.test.local' })
  config({ path: '.env.test' })
  config({ path: '.env.local' })
  config({ path: '.env' })

  // Set test-specific environment variables
  process.env.NODE_ENV = 'test'
  process.env.NEXTAUTH_URL = 'http://localhost:3000'
  process.env.NEXTAUTH_SECRET = 'test-secret-key-for-testing-only'
  
  // Use test database if specified
  if (process.env.DATABASE_URL_TEST) {
    process.env.DATABASE_URL = process.env.DATABASE_URL_TEST
    console.log('📊 Using test database...')
  } else {
    console.log('⚠️  Warning: No test database specified, using development database')
  }

  // Generate Prisma client for tests
  try {
    console.log('🔄 Generating Prisma client...')
    execSync('npx prisma generate', { 
      stdio: 'pipe',
      env: process.env 
    })
    console.log('✅ Prisma client generated')
  } catch (error) {
    console.warn('⚠️  Warning: Could not generate Prisma client:', error.message)
  }

  // Setup test database if using a separate test DB
  if (process.env.DATABASE_URL_TEST && process.env.DATABASE_URL_TEST !== process.env.DATABASE_URL) {
    try {
      console.log('🗄️  Setting up test database schema...')
      execSync('npx prisma db push --force-reset', { 
        stdio: 'pipe',
        env: process.env 
      })
      console.log('✅ Test database schema ready')
    } catch (error) {
      console.warn('⚠️  Warning: Could not setup test database:', error.message)
    }
  }

  // Set global timeout for async operations
  global.testTimeout = 30000

  console.log('✅ Test environment setup complete')
} 