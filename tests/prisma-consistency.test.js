const { describe, test, expect } = require('@jest/globals');
const fs = require('fs');
const { execSync } = require('child_process');

// Fix for Prisma client in test environment
process.env.NODE_ENV = 'test';

describe('Prisma Schema Consistency', () => {
  test('Schema file should be valid and parseable', () => {
    try {
      execSync('npx prisma validate', { stdio: 'pipe' });
    } catch (error) {
      fail(`Prisma schema validation failed: ${error.message}`);
    }
  });

  test('Critical models should exist in schema', () => {
    const schemaContent = fs.readFileSync('prisma/schema.prisma', 'utf8');
    
    // Define critical models that must exist
    const criticalModels = [
      'User',
      'Client', 
      'Project',
      'Order',
      'Quote',
      'Service',
      'TeamMember',
      'BlogPost',
      'SiteScript'
    ];

    criticalModels.forEach(model => {
      const modelRegex = new RegExp(`model\\s+${model}\\s*{`, 'g');
      expect(schemaContent).toMatch(modelRegex);
    });
  });

  test('No duplicate model names in schema', () => {
    const schemaContent = fs.readFileSync('prisma/schema.prisma', 'utf8');
    const modelMatches = schemaContent.match(/model\s+(\w+)\s*{/g);
    
    if (modelMatches) {
      const modelNames = modelMatches.map(match => 
        match.replace(/model\s+(\w+)\s*{/, '$1')
      );
      
      const uniqueNames = new Set(modelNames);
      expect(uniqueNames.size).toBe(modelNames.length);
    }
  });

  test('All models should have required fields', () => {
    const schemaContent = fs.readFileSync('prisma/schema.prisma', 'utf8');
    
    // Extract each model block
    const modelBlocks = schemaContent.match(/model\s+\w+\s*{[^}]*}/g) || [];
    
    modelBlocks.forEach(block => {
      const modelName = block.match(/model\s+(\w+)/)[1];
      
      // Each model should have an id field
      expect(block).toMatch(/\bid\s+\w+.*@id/);
      
      // Most models should have timestamps (with some exceptions)
      const timestampExceptions = [
        'Settings', 
        'ColorPalette',
        'ProjectMember',    // Uses joinedAt instead of createdAt
        'InvoiceItem',      // Junction table, doesn't need updatedAt
        'QuoteItem',        // Junction table, doesn't need updatedAt  
        'ReceiptItem'       // Junction table, doesn't need updatedAt
      ];
      
      if (!timestampExceptions.includes(modelName)) {
        expect(block).toMatch(/createdAt\s+DateTime.*@default\(now\(\)\)/);
        expect(block).toMatch(/updatedAt\s+DateTime.*@updatedAt/);
      }
    });
  });

  test('All models in schema should be properly named', () => {
    const schemaContent = fs.readFileSync('prisma/schema.prisma', 'utf8');
    const modelMatches = schemaContent.match(/model\s+(\w+)\s*{/g);
    
    expect(modelMatches).toBeTruthy();
    expect(modelMatches.length).toBeGreaterThan(0);

    const modelNames = modelMatches.map(match => 
      match.replace(/model\s+(\w+)\s*{/, '$1')
    );

    console.log(`✅ Schema contains ${modelNames.length} models`);

    // Check naming conventions
    modelNames.forEach(modelName => {
      // Should be PascalCase and singular
      expect(modelName).toMatch(/^[A-Z][a-zA-Z]*$/);
      
      // Should not be obviously plural (common mistakes)
      const problematicPluralEndings = ['Users', 'Posts', 'Orders', 'Clients'];
      problematicPluralEndings.forEach(ending => {
        if (modelName === ending) {
          fail(`Model "${modelName}" should be singular (e.g., "${ending.slice(0, -1)}")`);
        }
      });
    });
  });
});

describe('API Routes Prisma Usage', () => {
  test('All Prisma queries should use valid model names', () => {
    let apiFiles = [];
    
    try {
      apiFiles = execSync('find src/app/api -name "*.ts" -o -name "*.js"', { encoding: 'utf8' })
        .split('\n').filter(Boolean);
    } catch (error) {
      // Skip if no API files found
      return;
    }

    const invalidQueries = [];
    const commonMistakes = [
      { pattern: /prisma\.\w+s\./, description: 'Plural model name' },
      { pattern: /prisma\.\w*_\w*\./, description: 'Snake_case model name' },
      { pattern: /prisma\.Posts\./, description: 'Should be blogPost' },
      { pattern: /prisma\.Services\./, description: 'Should be service' },
      { pattern: /prisma\.Users\./, description: 'Should be user' }
    ];

    apiFiles.forEach(file => {
      const content = fs.readFileSync(file, 'utf8');
      
      commonMistakes.forEach(mistake => {
        const matches = content.match(mistake.pattern);
        if (matches) {
          invalidQueries.push({ 
            file: file.replace(process.cwd() + '/', ''), 
            matches: matches,
            issue: mistake.description
          });
        }
      });
    });

    if (invalidQueries.length > 0) {
      console.log('❌ Invalid Prisma queries found:');
      invalidQueries.forEach(query => {
        console.log(`  ${query.file}: ${query.matches[0]} (${query.issue})`);
      });
    }

    expect(invalidQueries).toHaveLength(0);
  });

  test('Schema contains expected number of models', () => {
    const schemaContent = fs.readFileSync('prisma/schema.prisma', 'utf8');
    const modelMatches = schemaContent.match(/model\s+(\w+)\s*{/g);
    
    expect(modelMatches).toBeTruthy();
    
    // We should have a reasonable number of models (not too few, not too many)
    expect(modelMatches.length).toBeGreaterThan(10); // At least basic models
    expect(modelMatches.length).toBeLessThan(100);   // Not excessively many
    
    console.log(`📊 Total models in schema: ${modelMatches.length}`);
  });
}); 