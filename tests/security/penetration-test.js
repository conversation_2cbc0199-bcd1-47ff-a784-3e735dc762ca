#!/usr/bin/env node

/**
 * 🔓 AUTHENTICATION PENETRATION TEST
 * Real-world authentication flow testing with actual credentials
 */

const https = require('https');
const http = require('http');
const crypto = require('crypto');

const BASE_URL = process.env.TEST_URL || 'http://localhost:3000';
const TEST_RESULTS = [];

// Test credentials - these should be actual working credentials
const VALID_CREDENTIALS = {
  username: process.env.TEST_USERNAME || 'admin',
  password: process.env.TEST_PASSWORD || 'admin123'
};

// Utility functions
function makeRequest(path, options = {}) {
  return new Promise((resolve, reject) => {
    const url = new URL(path, BASE_URL);
    const isHttps = url.protocol === 'https:';
    const client = isHttps ? https : http;
    
    const requestOptions = {
      hostname: url.hostname,
      port: url.port || (isHttps ? 443 : 80),
      path: url.pathname + url.search,
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Penetration-Test-Bot/1.0',
        ...options.headers
      }
    };

    const req = client.request(requestOptions, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          status: res.statusCode,
          headers: res.headers,
          data: data,
          json: () => {
            try {
              return JSON.parse(data);
            } catch {
              return null;
            }
          }
        });
      });
    });

    req.on('error', reject);
    req.on('timeout', () => reject(new Error('Request timeout')));
    req.setTimeout(15000);

    if (options.body) {
      req.write(typeof options.body === 'string' ? options.body : JSON.stringify(options.body));
    }

    req.end();
  });
}

function logResult(test, passed, details = '') {
  const status = passed ? '✅ PASS' : '❌ FAIL';
  console.log(`${status} ${test}${details ? ` - ${details}` : ''}`);
  TEST_RESULTS.push({ test, passed, details });
}

// Extract session cookie from response
function extractSessionCookie(response) {
  const setCookie = response.headers['set-cookie'];
  if (!setCookie) return null;
  
  const cookieArray = Array.isArray(setCookie) ? setCookie : [setCookie];
  for (const cookie of cookieArray) {
    const match = cookie.match(/mocky-session=([^;]+)/);
    if (match) return match[1];
  }
  return null;
}

async function runPenetrationTest() {
  console.log(`
🔓 AUTHENTICATION PENETRATION TEST
==================================
Target: ${BASE_URL}
Credentials: ${VALID_CREDENTIALS.username}:${VALID_CREDENTIALS.password.replace(/./g, '*')}
Date: ${new Date().toISOString()}
`);

  let sessionCookie = null;
  let testsPassed = 0;
  let totalTests = 0;

  // ==========================================
  // TEST 1: SUCCESSFUL LOGIN FLOW
  // ==========================================
  console.log('\n🔐 TEST 1: SUCCESSFUL LOGIN FLOW');
  totalTests++;

  try {
    const loginResponse = await makeRequest('/api/auth/login', {
      method: 'POST',
      body: VALID_CREDENTIALS
    });

    const loginData = loginResponse.json();
    sessionCookie = extractSessionCookie(loginResponse);

    if (loginResponse.status === 200 && loginData?.success && sessionCookie) {
      logResult('Successful login with valid credentials', true, 
               `Status: ${loginResponse.status}, Session: ${sessionCookie.substring(0, 20)}...`);
      testsPassed++;
    } else {
      logResult('Successful login with valid credentials', false, 
               `Status: ${loginResponse.status}, Success: ${loginData?.success}, Cookie: ${!!sessionCookie}`);
    }
  } catch (error) {
    logResult('Successful login with valid credentials', false, error.message);
  }

  // ==========================================
  // TEST 2: SESSION VALIDATION
  // ==========================================
  console.log('\n🍪 TEST 2: SESSION VALIDATION');
  totalTests++;

  if (sessionCookie) {
    try {
      const meResponse = await makeRequest('/api/auth/me', {
        headers: {
          'Cookie': `mocky-session=${sessionCookie}`
        }
      });

      const meData = meResponse.json();

      if (meResponse.status === 200 && meData?.success && meData?.user) {
        logResult('Session validation with valid cookie', true, 
                 `User: ${meData.user.username}, Role: ${meData.user.role}`);
        testsPassed++;
      } else {
        logResult('Session validation with valid cookie', false, 
                 `Status: ${meResponse.status}, Success: ${meData?.success}`);
      }
    } catch (error) {
      logResult('Session validation with valid cookie', false, error.message);
    }
  } else {
    logResult('Session validation with valid cookie', false, 'No session cookie from login');
  }

  // ==========================================
  // TEST 3: PROTECTED ROUTE ACCESS
  // ==========================================
  console.log('\n🛡️ TEST 3: PROTECTED ROUTE ACCESS');
  totalTests++;

  if (sessionCookie) {
    try {
      const dashboardResponse = await makeRequest('/admin/dashboard', {
        headers: {
          'Cookie': `mocky-session=${sessionCookie}`
        }
      });

      // Should return 200 (success) or redirect to dashboard content
      const hasAccess = dashboardResponse.status === 200 || 
                       (dashboardResponse.status === 307 && 
                        !dashboardResponse.headers.location?.includes('/admin/login'));

      if (hasAccess) {
        logResult('Authenticated access to protected route', true, 
                 `Status: ${dashboardResponse.status}`);
        testsPassed++;
      } else {
        logResult('Authenticated access to protected route', false, 
                 `Status: ${dashboardResponse.status}, Location: ${dashboardResponse.headers.location || 'none'}`);
      }
    } catch (error) {
      logResult('Authenticated access to protected route', false, error.message);
    }
  } else {
    logResult('Authenticated access to protected route', false, 'No session cookie available');
  }

  // ==========================================
  // TEST 4: API ROUTE ACCESS
  // ==========================================
  console.log('\n🔌 TEST 4: AUTHENTICATED API ACCESS');
  totalTests++;

  if (sessionCookie) {
    try {
      const apiResponse = await makeRequest('/api/admin/users', {
        headers: {
          'Cookie': `mocky-session=${sessionCookie}`
        }
      });

      // Should return 200 (success) or appropriate data response
      if (apiResponse.status === 200) {
        logResult('Authenticated API route access', true, 
                 `Status: ${apiResponse.status}`);
        testsPassed++;
      } else {
        logResult('Authenticated API route access', false, 
                 `Status: ${apiResponse.status}`);
      }
    } catch (error) {
      logResult('Authenticated API route access', false, error.message);
    }
  } else {
    logResult('Authenticated API route access', false, 'No session cookie available');
  }

  // ==========================================
  // TEST 5: SESSION PERSISTENCE
  // ==========================================
  console.log('\n⏰ TEST 5: SESSION PERSISTENCE');
  totalTests++;

  if (sessionCookie) {
    try {
      // Wait a moment and test again
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const persistenceResponse = await makeRequest('/api/auth/me', {
        headers: {
          'Cookie': `mocky-session=${sessionCookie}`
        }
      });

      const persistenceData = persistenceResponse.json();

      if (persistenceResponse.status === 200 && persistenceData?.success) {
        logResult('Session persists across requests', true, 
                 `Status: ${persistenceResponse.status}`);
        testsPassed++;
      } else {
        logResult('Session persists across requests', false, 
                 `Status: ${persistenceResponse.status}`);
      }
    } catch (error) {
      logResult('Session persists across requests', false, error.message);
    }
  } else {
    logResult('Session persists across requests', false, 'No session cookie available');
  }

  // ==========================================
  // TEST 6: UNAUTHORIZED ACCESS PREVENTION
  // ==========================================
  console.log('\n🚫 TEST 6: UNAUTHORIZED ACCESS PREVENTION');
  totalTests++;

  try {
    const unauthorizedResponse = await makeRequest('/admin/dashboard');
    
    // Should redirect to login (307) or return unauthorized (401/403)
    const isBlocked = unauthorizedResponse.status === 307 || 
                     unauthorizedResponse.status === 401 || 
                     unauthorizedResponse.status === 403;

    if (isBlocked) {
      logResult('Blocks unauthorized access to protected routes', true, 
               `Status: ${unauthorizedResponse.status}`);
      testsPassed++;
    } else {
      logResult('Blocks unauthorized access to protected routes', false, 
               `Status: ${unauthorizedResponse.status} (should be 307, 401, or 403)`);
    }
  } catch (error) {
    logResult('Blocks unauthorized access to protected routes', false, error.message);
  }

  // ==========================================
  // TEST 7: INVALID SESSION HANDLING
  // ==========================================
  console.log('\n🔧 TEST 7: INVALID SESSION HANDLING');
  totalTests++;

  try {
    const invalidSessionResponse = await makeRequest('/api/auth/me', {
      headers: {
        'Cookie': 'mocky-session=invalid.jwt.token.here'
      }
    });

    if (invalidSessionResponse.status === 401) {
      logResult('Handles invalid session tokens correctly', true, 
               `Status: ${invalidSessionResponse.status}`);
      testsPassed++;
    } else {
      logResult('Handles invalid session tokens correctly', false, 
               `Status: ${invalidSessionResponse.status} (should be 401)`);
    }
  } catch (error) {
    logResult('Handles invalid session tokens correctly', false, error.message);
  }

  // ==========================================
  // TEST 8: LOGOUT FUNCTIONALITY
  // ==========================================
  console.log('\n🚪 TEST 8: LOGOUT FUNCTIONALITY');
  totalTests++;

  if (sessionCookie) {
    try {
      const logoutResponse = await makeRequest('/api/auth/logout', {
        method: 'POST',
        headers: {
          'Cookie': `mocky-session=${sessionCookie}`
        }
      });

      const logoutData = logoutResponse.json();

      if (logoutResponse.status === 200 && logoutData?.success) {
        logResult('Logout endpoint works correctly', true, 
                 `Status: ${logoutResponse.status}`);
        
        // Test if session is actually invalidated
        const postLogoutResponse = await makeRequest('/api/auth/me', {
          headers: {
            'Cookie': `mocky-session=${sessionCookie}`
          }
        });

        if (postLogoutResponse.status === 401) {
          logResult('Session invalidated after logout', true, 
                   `Status: ${postLogoutResponse.status}`);
          testsPassed++;
        } else {
          logResult('Session invalidated after logout', false, 
                   `Status: ${postLogoutResponse.status} (session still valid)`);
        }
      } else {
        logResult('Logout endpoint works correctly', false, 
                 `Status: ${logoutResponse.status}, Success: ${logoutData?.success}`);
      }
    } catch (error) {
      logResult('Logout functionality', false, error.message);
    }
  } else {
    logResult('Logout functionality', false, 'No session cookie available');
  }

  // ==========================================
  // TEST 9: BRUTE FORCE PROTECTION
  // ==========================================
  console.log('\n🛡️ TEST 9: BRUTE FORCE PROTECTION');
  totalTests++;

  try {
    const attempts = [];
    for (let i = 0; i < 6; i++) {
      attempts.push(
        makeRequest('/api/auth/login', {
          method: 'POST',
          headers: {
            'X-Forwarded-For': '*************',
            'X-Real-IP': '*************'
          },
          body: {
            username: 'testuser',
            password: 'wrongpassword'
          }
        })
      );
    }

    const responses = await Promise.all(attempts.map(p => p.catch(e => ({ error: e.message }))));
    const rateLimited = responses.some(r => r.status === 429);

    if (rateLimited) {
      logResult('Implements brute force protection', true, 
               `Rate limiting activated after multiple failed attempts`);
      testsPassed++;
    } else {
      logResult('Implements brute force protection', false, 
               'No rate limiting detected after 6 failed attempts');
    }
  } catch (error) {
    logResult('Implements brute force protection', false, error.message);
  }

  // ==========================================
  // TEST 10: SECURITY HEADERS
  // ==========================================
  console.log('\n🔒 TEST 10: SECURITY HEADERS');
  totalTests++;

  try {
    const headersResponse = await makeRequest('/admin/dashboard');
    
    const securityHeaders = [
      'x-content-type-options',
      'x-frame-options',
      'x-xss-protection'
    ];

    const presentHeaders = securityHeaders.filter(header => headersResponse.headers[header]);
    
    if (presentHeaders.length >= 2) {
      logResult('Implements security headers', true, 
               `Headers present: ${presentHeaders.join(', ')}`);
      testsPassed++;
    } else {
      logResult('Implements security headers', false, 
               `Only ${presentHeaders.length}/3 security headers present`);
    }
  } catch (error) {
    logResult('Implements security headers', false, error.message);
  }

  // ==========================================
  // FINAL RESULTS
  // ==========================================
  console.log('\n' + '='.repeat(60));
  console.log('🏆 PENETRATION TEST RESULTS');
  console.log('='.repeat(60));

  const successRate = (testsPassed / totalTests) * 100;
  
  console.log(`
📊 SUMMARY:
- Total Tests: ${totalTests}
- Passed: ${testsPassed}
- Failed: ${totalTests - testsPassed}
- Success Rate: ${successRate.toFixed(1)}%
`);

  // Security rating
  let rating;
  if (successRate >= 95) rating = 'A+';
  else if (successRate >= 90) rating = 'A';
  else if (successRate >= 85) rating = 'A-';
  else if (successRate >= 80) rating = 'B+';
  else if (successRate >= 75) rating = 'B';
  else if (successRate >= 70) rating = 'B-';
  else if (successRate >= 60) rating = 'C';
  else rating = 'F';

  console.log(`🎯 AUTHENTICATION SECURITY RATING: ${rating} (${successRate.toFixed(1)}%)`);

  // Show failed tests
  const failedTests = TEST_RESULTS.filter(t => !t.passed);
  if (failedTests.length > 0) {
    console.log('\n❌ FAILED TESTS:');
    failedTests.forEach(test => {
      console.log(`   - ${test.test}: ${test.details}`);
    });
  }

  // Recommendations
  console.log('\n🔧 SECURITY RECOMMENDATIONS:');
  if (successRate >= 90) {
    console.log('   ✅ Excellent authentication security!');
    console.log('   ✅ System is production-ready');
    console.log('   ✅ Consider implementing additional monitoring');
  } else if (successRate >= 80) {
    console.log('   ⚠️  Good security with minor improvements needed');
    console.log('   ⚠️  Address failed tests before production');
    console.log('   ⚠️  Implement comprehensive logging');
  } else {
    console.log('   🚨 CRITICAL: Authentication system needs immediate attention');
    console.log('   🚨 DO NOT deploy to production until issues are resolved');
    console.log('   🚨 Implement missing security controls');
  }

  console.log('\n🔍 DETAILED ANALYSIS:');
  console.log(`   - Login Flow: ${TEST_RESULTS.find(t => t.test.includes('Successful login'))?.passed ? 'WORKING' : 'BROKEN'}`);
  console.log(`   - Session Management: ${TEST_RESULTS.find(t => t.test.includes('Session validation'))?.passed ? 'WORKING' : 'BROKEN'}`);
  console.log(`   - Route Protection: ${TEST_RESULTS.find(t => t.test.includes('protected route'))?.passed ? 'WORKING' : 'BROKEN'}`);
  console.log(`   - Logout Functionality: ${TEST_RESULTS.find(t => t.test.includes('Logout endpoint'))?.passed ? 'WORKING' : 'BROKEN'}`);
  console.log(`   - Brute Force Protection: ${TEST_RESULTS.find(t => t.test.includes('brute force'))?.passed ? 'WORKING' : 'NEEDS IMPROVEMENT'}`);

  process.exit(testsPassed === totalTests ? 0 : 1);
}

// Run the penetration test
if (require.main === module) {
  runPenetrationTest().catch(error => {
    console.error('❌ Penetration test failed:', error);
    process.exit(1);
  });
}

module.exports = { runPenetrationTest }; 