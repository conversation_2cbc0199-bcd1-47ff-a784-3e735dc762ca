#!/usr/bin/env node

/**
 * 🔐 MANUAL AUTHENTICATION SECURITY AUDIT
 * Enterprise-grade security testing for production deployment
 * 
 * This script performs comprehensive authentication testing without Jest dependencies
 */

const https = require('https');
const http = require('http');
const crypto = require('crypto');

// Configuration
const BASE_URL = process.env.TEST_URL || 'http://localhost:3000';
const TEST_RESULTS = [];
let TOTAL_TESTS = 0;
let PASSED_TESTS = 0;
let FAILED_TESTS = 0;

// Test utilities
function makeRequest(path, options = {}) {
  return new Promise((resolve, reject) => {
    const url = new URL(path, BASE_URL);
    const isHttps = url.protocol === 'https:';
    const client = isHttps ? https : http;
    
    const requestOptions = {
      hostname: url.hostname,
      port: url.port || (isHttps ? 443 : 80),
      path: url.pathname + url.search,
      method: options.method || 'GET',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Security-Audit-Bot/1.0',
        ...options.headers
      }
    };

    const req = client.request(requestOptions, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        resolve({
          status: res.statusCode,
          headers: res.headers,
          data: data,
          json: () => {
            try {
              return JSON.parse(data);
            } catch {
              return null;
            }
          }
        });
      });
    });

    req.on('error', reject);
    req.on('timeout', () => reject(new Error('Request timeout')));
    req.setTimeout(10000); // 10 second timeout

    if (options.body) {
      req.write(typeof options.body === 'string' ? options.body : JSON.stringify(options.body));
    }

    req.end();
  });
}

function logTest(category, name, passed, details = '') {
  TOTAL_TESTS++;
  if (passed) {
    PASSED_TESTS++;
    console.log(`✅ [${category}] ${name}`);
  } else {
    FAILED_TESTS++;
    console.log(`❌ [${category}] ${name} - ${details}`);
  }
  
  TEST_RESULTS.push({
    category,
    name,
    passed,
    details
  });
}

function logSection(title) {
  console.log(`\n${'='.repeat(60)}`);
  console.log(`🔐 ${title}`);
  console.log(`${'='.repeat(60)}`);
}

// ==========================================
// 1. LOGIN FUNCTIONALITY TESTS
// ==========================================
async function testLoginFunctionality() {
  logSection('LOGIN FUNCTIONALITY TESTS');

  try {
    // Test 1: Valid credentials structure
    const validLoginResponse = await makeRequest('/api/auth/login', {
      method: 'POST',
      body: {
        username: 'admin',
        password: 'SecurePassword123!'
      }
    });

    logTest('LOGIN', 'API endpoint responds', validLoginResponse.status !== undefined, 
           `Status: ${validLoginResponse.status}`);

    // Test 2: Invalid credentials handling
    const invalidLoginResponse = await makeRequest('/api/auth/login', {
      method: 'POST',
      body: {
        username: 'admin',
        password: 'wrongpassword'
      }
    });

    logTest('LOGIN', 'Rejects invalid credentials', 
           invalidLoginResponse.status === 401 || invalidLoginResponse.status === 403,
           `Status: ${invalidLoginResponse.status}`);

    // Test 3: Empty credentials validation
    const emptyCredsResponse = await makeRequest('/api/auth/login', {
      method: 'POST',
      body: {
        username: '',
        password: ''
      }
    });

    logTest('LOGIN', 'Validates empty credentials', 
           emptyCredsResponse.status === 400,
           `Status: ${emptyCredsResponse.status}`);

    // Test 4: Malformed request handling
    const malformedResponse = await makeRequest('/api/auth/login', {
      method: 'POST',
      body: 'invalid-json'
    });

    logTest('LOGIN', 'Handles malformed requests', 
           malformedResponse.status >= 400,
           `Status: ${malformedResponse.status}`);

  } catch (error) {
    logTest('LOGIN', 'Login functionality tests', false, error.message);
  }
}

// ==========================================
// 2. RATE LIMITING TESTS
// ==========================================
async function testRateLimiting() {
  logSection('RATE LIMITING & BRUTE FORCE PROTECTION');

  try {
    const attempts = [];
    const clientIP = '*************';

    // Make multiple rapid login attempts
    for (let i = 0; i < 6; i++) {
      attempts.push(
        makeRequest('/api/auth/login', {
          method: 'POST',
          headers: {
            'X-Forwarded-For': clientIP,
            'X-Real-IP': clientIP
          },
          body: {
            username: 'testuser',
            password: 'wrongpassword'
          }
        })
      );
    }

    const responses = await Promise.all(attempts.map(p => p.catch(e => ({ error: e.message }))));
    
    // Check if rate limiting kicked in
    const rateLimitedResponses = responses.filter(r => r.status === 429);
    
    logTest('RATE_LIMIT', 'Implements rate limiting', 
           rateLimitedResponses.length > 0,
           `${rateLimitedResponses.length}/6 requests rate limited`);

    // Test rate limit headers
    const lastResponse = responses[responses.length - 1];
    if (lastResponse.headers) {
      const hasRateLimitHeaders = 
        lastResponse.headers['x-ratelimit-limit'] || 
        lastResponse.headers['retry-after'] ||
        lastResponse.headers['x-ratelimit-remaining'];
      
      logTest('RATE_LIMIT', 'Provides rate limit headers', 
             !!hasRateLimitHeaders,
             'Rate limit headers present');
    }

  } catch (error) {
    logTest('RATE_LIMIT', 'Rate limiting tests', false, error.message);
  }
}

// ==========================================
// 3. SESSION MANAGEMENT TESTS
// ==========================================
async function testSessionManagement() {
  logSection('SESSION MANAGEMENT TESTS');

  try {
    // Test 1: Session endpoint without authentication
    const sessionResponse = await makeRequest('/api/auth/me');
    
    logTest('SESSION', 'Requires authentication for session info', 
           sessionResponse.status === 401,
           `Status: ${sessionResponse.status}`);

    // Test 2: Invalid session token handling
    const invalidSessionResponse = await makeRequest('/api/auth/me', {
      headers: {
        'Cookie': 'mocky-session=invalid.jwt.token'
      }
    });

    logTest('SESSION', 'Handles invalid session tokens', 
           invalidSessionResponse.status === 401,
           `Status: ${invalidSessionResponse.status}`);

    // Test 3: Session cookie security flags (check response headers)
    const loginAttempt = await makeRequest('/api/auth/login', {
      method: 'POST',
      body: {
        username: 'admin',
        password: 'admin123'
      }
    });

    if (loginAttempt.headers['set-cookie']) {
      const cookieHeader = loginAttempt.headers['set-cookie'];
      const hasHttpOnly = cookieHeader.includes('HttpOnly');
      const hasSameSite = cookieHeader.includes('SameSite');
      const hasSecure = cookieHeader.includes('Secure') || process.env.NODE_ENV !== 'production';

      logTest('SESSION', 'Sets secure cookie flags', 
             hasHttpOnly && hasSameSite,
             `HttpOnly: ${hasHttpOnly}, SameSite: ${hasSameSite}, Secure: ${hasSecure}`);
    }

  } catch (error) {
    logTest('SESSION', 'Session management tests', false, error.message);
  }
}

// ==========================================
// 4. PROTECTED ROUTES TESTS
// ==========================================
async function testProtectedRoutes() {
  logSection('PROTECTED ROUTES TESTS');

  const protectedRoutes = [
    '/admin/dashboard',
    '/admin/users',
    '/admin/settings',
    '/admin/blog',
    '/admin/catalogue'
  ];

  try {
    for (const route of protectedRoutes) {
      const response = await makeRequest(route);
      
      // Should redirect to login (307) or return unauthorized (401)
      const isProtected = response.status === 307 || response.status === 401 || response.status === 403;
      
      logTest('PROTECTED_ROUTES', `${route} requires authentication`, 
             isProtected,
             `Status: ${response.status}`);
    }

    // Test admin API routes
    const adminApiRoutes = [
      '/api/admin/users',
      '/api/admin/settings',
      '/api/admin/analytics'
    ];

    for (const route of adminApiRoutes) {
      const response = await makeRequest(route);
      
      logTest('PROTECTED_ROUTES', `${route} API requires authentication`, 
             response.status === 401 || response.status === 403,
             `Status: ${response.status}`);
    }

  } catch (error) {
    logTest('PROTECTED_ROUTES', 'Protected routes tests', false, error.message);
  }
}

// ==========================================
// 5. SECURITY HEADERS TESTS
// ==========================================
async function testSecurityHeaders() {
  logSection('SECURITY HEADERS TESTS');

  try {
    const response = await makeRequest('/admin/dashboard');
    
    const expectedHeaders = {
      'x-content-type-options': 'nosniff',
      'x-frame-options': 'DENY',
      'x-xss-protection': '1; mode=block'
    };

    Object.entries(expectedHeaders).forEach(([header, expectedValue]) => {
      const actualValue = response.headers[header];
      const isPresent = actualValue === expectedValue;
      
      logTest('SECURITY_HEADERS', `${header} header`, 
             isPresent,
             `Expected: ${expectedValue}, Got: ${actualValue || 'missing'}`);
    });

    // Test for additional security headers
    const additionalHeaders = [
      'referrer-policy',
      'strict-transport-security',
      'content-security-policy'
    ];

    additionalHeaders.forEach(header => {
      const isPresent = !!response.headers[header];
      logTest('SECURITY_HEADERS', `${header} header present`, 
             isPresent,
             isPresent ? 'Present' : 'Missing');
    });

  } catch (error) {
    logTest('SECURITY_HEADERS', 'Security headers tests', false, error.message);
  }
}

// ==========================================
// 6. LOGOUT FUNCTIONALITY TESTS
// ==========================================
async function testLogoutFunctionality() {
  logSection('LOGOUT FUNCTIONALITY TESTS');

  try {
    // Test logout endpoint
    const logoutResponse = await makeRequest('/api/auth/logout', {
      method: 'POST'
    });

    logTest('LOGOUT', 'Logout endpoint responds', 
           logoutResponse.status === 200,
           `Status: ${logoutResponse.status}`);

    // Test logout clears cookies
    if (logoutResponse.headers['set-cookie']) {
      const cookieHeader = logoutResponse.headers['set-cookie'];
      const clearsSession = cookieHeader.includes('mocky-session=') && 
                          (cookieHeader.includes('Max-Age=0') || cookieHeader.includes('expires='));
      
      logTest('LOGOUT', 'Clears session cookies', 
             clearsSession,
             'Session cookie cleared in response');
    }

    // Test GET method also works for logout
    const logoutGetResponse = await makeRequest('/api/auth/logout');
    
    logTest('LOGOUT', 'Supports GET method for logout', 
           logoutGetResponse.status === 200,
           `Status: ${logoutGetResponse.status}`);

  } catch (error) {
    logTest('LOGOUT', 'Logout functionality tests', false, error.message);
  }
}

// ==========================================
// 7. SECURITY VULNERABILITY TESTS
// ==========================================
async function testSecurityVulnerabilities() {
  logSection('SECURITY VULNERABILITY TESTS');

  try {
    // Test SQL injection attempts
    const sqlInjectionPayloads = [
      "admin'; DROP TABLE users; --",
      "admin' OR '1'='1",
      "admin' UNION SELECT * FROM users --"
    ];

    for (const payload of sqlInjectionPayloads) {
      const response = await makeRequest('/api/auth/login', {
        method: 'POST',
        body: {
          username: payload,
          password: 'test'
        }
      });

      // Should not return 200 (successful login) with SQL injection
      logTest('SECURITY_VULN', `SQL injection protection (${payload.substring(0, 20)}...)`, 
             response.status !== 200,
             `Status: ${response.status}`);
    }

    // Test XSS attempts
    const xssPayloads = [
      '<script>alert("xss")</script>',
      'javascript:alert("xss")',
      '<img src="x" onerror="alert(1)">'
    ];

    for (const payload of xssPayloads) {
      const response = await makeRequest('/api/auth/login', {
        method: 'POST',
        body: {
          username: payload,
          password: 'test'
        }
      });

      // Should handle XSS attempts safely
      logTest('SECURITY_VULN', `XSS protection (${payload.substring(0, 20)}...)`, 
             response.status >= 400 || response.status === 401,
             `Status: ${response.status}`);
    }

    // Test oversized requests
    const largePayload = 'x'.repeat(1024 * 1024); // 1MB
    const oversizedResponse = await makeRequest('/api/auth/login', {
      method: 'POST',
      body: {
        username: largePayload,
        password: 'test'
      }
    });

    logTest('SECURITY_VULN', 'Handles oversized requests', 
           oversizedResponse.status >= 400,
           `Status: ${oversizedResponse.status}`);

  } catch (error) {
    logTest('SECURITY_VULN', 'Security vulnerability tests', false, error.message);
  }
}

// ==========================================
// 8. EDGE CASES TESTS
// ==========================================
async function testEdgeCases() {
  logSection('EDGE CASES TESTS');

  try {
    // Test concurrent requests
    const concurrentRequests = Array(10).fill(null).map((_, i) => 
      makeRequest('/api/auth/me', {
        headers: {
          'X-Request-ID': `concurrent-${i}`,
          'Cookie': `mocky-session=test-token-${i}`
        }
      })
    );

    const responses = await Promise.all(concurrentRequests.map(p => p.catch(e => ({ error: e.message }))));
    const successfulResponses = responses.filter(r => r.status !== undefined);

    logTest('EDGE_CASES', 'Handles concurrent requests', 
           successfulResponses.length >= 8, // At least 80% success
           `${successfulResponses.length}/10 requests handled`);

    // Test malformed headers
    const malformedHeaderResponse = await makeRequest('/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Malformed-Header': '\x00\x01\x02invalid'
      },
      body: {
        username: 'test',
        password: 'test'
      }
    });

    logTest('EDGE_CASES', 'Handles malformed headers', 
           malformedHeaderResponse.status !== 500,
           `Status: ${malformedHeaderResponse.status}`);

    // Test request timeout behavior
    const timeoutTest = new Promise((resolve) => {
      setTimeout(() => resolve({ status: 'timeout' }), 5000);
    });

    const quickRequest = makeRequest('/api/auth/me');
    const result = await Promise.race([quickRequest, timeoutTest]);

    logTest('EDGE_CASES', 'Responds within reasonable time', 
           result.status !== 'timeout',
           'Request completed within 5 seconds');

  } catch (error) {
    logTest('EDGE_CASES', 'Edge cases tests', false, error.message);
  }
}

// ==========================================
// MAIN AUDIT FUNCTION
// ==========================================
async function runSecurityAudit() {
  console.log(`
🔐 AUTHENTICATION SECURITY AUDIT
=====================================
Target: ${BASE_URL}
Date: ${new Date().toISOString()}
Environment: ${process.env.NODE_ENV || 'development'}
`);

  try {
    await testLoginFunctionality();
    await testRateLimiting();
    await testSessionManagement();
    await testProtectedRoutes();
    await testSecurityHeaders();
    await testLogoutFunctionality();
    await testSecurityVulnerabilities();
    await testEdgeCases();

    // Generate final report
    logSection('SECURITY AUDIT RESULTS');
    
    console.log(`
📊 TEST SUMMARY:
- Total Tests: ${TOTAL_TESTS}
- Passed: ${PASSED_TESTS}
- Failed: ${FAILED_TESTS}
- Success Rate: ${((PASSED_TESTS / TOTAL_TESTS) * 100).toFixed(1)}%
`);

    // Calculate security rating
    const securityScore = (PASSED_TESTS / TOTAL_TESTS) * 100;
    let securityRating;
    
    if (securityScore >= 95) securityRating = 'A+';
    else if (securityScore >= 90) securityRating = 'A';
    else if (securityScore >= 85) securityRating = 'A-';
    else if (securityScore >= 80) securityRating = 'B+';
    else if (securityScore >= 75) securityRating = 'B';
    else if (securityScore >= 70) securityRating = 'B-';
    else if (securityScore >= 65) securityRating = 'C+';
    else if (securityScore >= 60) securityRating = 'C';
    else securityRating = 'F';

    console.log(`🏆 SECURITY RATING: ${securityRating} (${securityScore.toFixed(1)}%)`);

    // Show failed tests
    if (FAILED_TESTS > 0) {
      console.log('\n❌ FAILED TESTS:');
      TEST_RESULTS
        .filter(test => !test.passed)
        .forEach(test => {
          console.log(`   - [${test.category}] ${test.name}: ${test.details}`);
        });
    }

    // Security recommendations
    console.log('\n🔧 RECOMMENDATIONS:');
    if (securityScore < 90) {
      console.log('   - Address failed security tests immediately');
      console.log('   - Implement missing security headers');
      console.log('   - Strengthen input validation');
    }
    if (securityScore < 80) {
      console.log('   - Review authentication flow');
      console.log('   - Implement additional rate limiting');
      console.log('   - Add comprehensive logging');
    }
    if (securityScore >= 90) {
      console.log('   - Excellent security posture!');
      console.log('   - Consider implementing additional monitoring');
      console.log('   - Regular security audits recommended');
    }

    process.exit(FAILED_TESTS > 0 ? 1 : 0);

  } catch (error) {
    console.error('❌ Security audit failed:', error.message);
    process.exit(1);
  }
}

// Run the audit
if (require.main === module) {
  runSecurityAudit();
}

module.exports = { runSecurityAudit }; 