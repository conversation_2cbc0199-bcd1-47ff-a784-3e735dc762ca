#!/bin/bash

echo "🧹 Clearing Next.js cache and build artifacts..."

# Remove .next directory
if [ -d ".next" ]; then
    rm -rf .next
    echo "✅ Removed .next directory"
fi

# Clear node_modules/.cache if it exists
if [ -d "node_modules/.cache" ]; then
    rm -rf node_modules/.cache
    echo "✅ Removed node_modules/.cache"
fi

# Clear npm cache
npm cache clean --force 2>/dev/null
echo "✅ Cleared npm cache"

echo ""
echo "🎉 Cache cleared successfully!"
echo "💡 Now restart your development server:"
echo "   npm run dev"
echo ""
echo "🌐 If you're still seeing the session error:"
echo "   1. Hard refresh your browser (Ctrl+Shift+R or Cmd+Shift+R)"
echo "   2. Clear browser cache"
echo "   3. Try incognito/private browsing mode" 