#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class PrismaValidator {
  constructor() {
    this.errors = [];
    this.warnings = [];
    this.info = [];
  }

  // Extract model names from schema
  extractSchemaModels() {
    try {
      const schemaContent = fs.readFileSync('prisma/schema.prisma', 'utf8');
      const modelMatches = schemaContent.match(/model\s+(\w+)\s*{/g);
      return modelMatches ? modelMatches.map(match => 
        match.replace(/model\s+(\w+)\s*{/, '$1')
      ) : [];
    } catch (error) {
      this.errors.push({
        type: 'SCHEMA_READ_ERROR',
        message: 'Cannot read prisma/schema.prisma file'
      });
      return [];
    }
  }

  // Find all Prisma queries in codebase
  findPrismaQueries() {
    try {
      const files = execSync('find src -name "*.ts" -o -name "*.js" | grep -v node_modules', { encoding: 'utf8' })
        .split('\n').filter(Boolean);
      
      const queries = [];
      files.forEach(file => {
        try {
          const content = fs.readFileSync(file, 'utf8');
          
          // Match patterns like prisma.modelName.
          const matches = content.matchAll(/prisma\.(\w+)\./g);
          for (const match of matches) {
            const lineNumber = content.substring(0, match.index).split('\n').length;
            queries.push({
              file: file.replace(process.cwd() + '/', ''),
              model: match[1],
              line: lineNumber,
              fullMatch: match[0]
            });
          }
        } catch (err) {
          // Skip files that can't be read
        }
      });
      return queries;
    } catch (error) {
      this.warnings.push({
        type: 'CODE_SCAN_ERROR',
        message: 'Could not scan codebase for Prisma queries'
      });
      return [];
    }
  }

  // Validate model existence
  validateModelExistence() {
    const schemaModels = this.extractSchemaModels();
    const codeQueries = this.findPrismaQueries();

    // Convert schema models to expected Prisma client names (camelCase)
    const prismaClientModels = schemaModels.map(model => 
      model.charAt(0).toLowerCase() + model.slice(1)
    );

    codeQueries.forEach(query => {
      if (!prismaClientModels.includes(query.model)) {
        this.errors.push({
          type: 'MISSING_MODEL',
          message: `Model "${query.model}" used in ${query.file}:${query.line} but not defined in schema`,
          file: query.file,
          line: query.line,
          model: query.model,
          suggestion: `Add model ${query.model.charAt(0).toUpperCase() + query.model.slice(1)} to prisma/schema.prisma`
        });
      }
    });

    this.info.push({
      type: 'STATS',
      message: `Found ${schemaModels.length} models in schema, ${codeQueries.length} Prisma queries in codebase`
    });
  }

  // Check naming conventions
  validateNamingConventions() {
    const codeQueries = this.findPrismaQueries();
    
    codeQueries.forEach(query => {
      // Check for plural model names (common mistake)
      if (query.model.endsWith('s') && query.model.length > 3) {
        // Skip models that are legitimately plural (like 'settings', 'analytics')
        const legitimatePluralModels = ['settings', 'analytics', 'graphics', 'logistics'];
        if (!legitimatePluralModels.includes(query.model.toLowerCase())) {
          this.warnings.push({
            type: 'NAMING_CONVENTION',
            message: `Possible plural model name "${query.model}" in ${query.file}:${query.line}. Use singular names.`,
            file: query.file,
            line: query.line,
            suggestion: `Consider changing to "${query.model.slice(0, -1)}"`
          });
        }
      }

      // Check for snake_case (should be camelCase)
      if (query.model.includes('_')) {
        const camelCased = query.model.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
        this.warnings.push({
          type: 'NAMING_CONVENTION',
          message: `Snake_case model name "${query.model}" in ${query.file}:${query.line}. Use camelCase.`,
          file: query.file,
          line: query.line,
          suggestion: `Change to "${camelCased}"`
        });
      }
    });
  }

  // Validate database sync
  validateDatabaseSync() {
    try {
      // Check if DATABASE_URL is available
      if (!process.env.DATABASE_URL) {
        this.warnings.push({
          type: 'NO_DATABASE_URL',
          message: 'DATABASE_URL not set, skipping database sync validation'
        });
        return;
      }

      // Try to generate a diff to check for drift
      try {
        const output = execSync('npx prisma migrate diff --from-schema-datamodel prisma/schema.prisma --to-schema-datasource prisma/schema.prisma', 
          { encoding: 'utf8', stdio: 'pipe' });
        
        if (output.trim()) {
          this.warnings.push({
            type: 'POTENTIAL_SCHEMA_DRIFT',
            message: 'Detected potential differences between schema and database',
            suggestion: 'Run: npx prisma db push to sync, or npx prisma db pull to update schema'
          });
        }
      } catch (diffError) {
        // If diff command fails, try a simpler validation
        try {
          execSync('npx prisma validate', { stdio: 'pipe' });
          this.info.push({
            type: 'SCHEMA_VALID',
            message: 'Prisma schema validation passed'
          });
        } catch (validateError) {
          this.errors.push({
            type: 'SCHEMA_INVALID',
            message: 'Prisma schema validation failed',
            details: validateError.message
          });
        }
      }
    } catch (error) {
      this.warnings.push({
        type: 'DB_CONNECTION',
        message: 'Cannot connect to database for sync validation',
        suggestion: 'Ensure DATABASE_URL is correct and database is accessible'
      });
    }
  }

  // Check for common anti-patterns
  validateBestPractices() {
    const codeQueries = this.findPrismaQueries();
    const queryTypes = {};

    codeQueries.forEach(query => {
      if (!queryTypes[query.model]) {
        queryTypes[query.model] = new Set();
      }
      // Extract query type (findMany, create, update, etc.)
      const nextPart = query.fullMatch; // This would need more sophisticated parsing
      queryTypes[query.model].add(query.file);
    });

    // Check for models used in many files (might need service layer)
    Object.entries(queryTypes).forEach(([model, files]) => {
      if (files.size > 10) {
        this.info.push({
          type: 'ARCHITECTURE_SUGGESTION',
          message: `Model "${model}" is used in ${files.size} files. Consider creating a dedicated service.`,
          suggestion: `Create src/services/${model}Service.ts to centralize database operations`
        });
      }
    });
  }

  // Generate auto-fix suggestions
  generateAutoFixes() {
    const fixes = [];
    
    this.errors.forEach(error => {
      if (error.type === 'MISSING_MODEL') {
        fixes.push({
          type: 'ADD_MODEL',
          message: `Add missing model to schema`,
          command: `echo "model ${error.model.charAt(0).toUpperCase() + error.model.slice(1)} {\n  id String @id @default(cuid())\n  createdAt DateTime @default(now())\n  updatedAt DateTime @updatedAt\n}" >> prisma/schema.prisma`
        });
      }
    });

    if (fixes.length > 0) {
      this.info.push({
        type: 'AUTO_FIXES',
        message: `${fixes.length} auto-fixes available`,
        fixes: fixes
      });
    }
  }

  // Format and display results
  displayResults() {
    console.log('\n🔍 Prisma Consistency Validation Report\n');
    console.log('=' .repeat(50));

    // Display errors
    if (this.errors.length > 0) {
      console.log('\n❌ CRITICAL ERRORS:');
      this.errors.forEach((error, index) => {
        console.log(`\n${index + 1}. ${error.message}`);
        if (error.file) console.log(`   File: ${error.file}:${error.line}`);
        if (error.suggestion) console.log(`   💡 Fix: ${error.suggestion}`);
      });
    }

    // Display warnings
    if (this.warnings.length > 0) {
      console.log('\n⚠️  WARNINGS:');
      this.warnings.forEach((warning, index) => {
        console.log(`\n${index + 1}. ${warning.message}`);
        if (warning.file) console.log(`   File: ${warning.file}:${warning.line}`);
        if (warning.suggestion) console.log(`   💡 Suggestion: ${warning.suggestion}`);
      });
    }

    // Display info
    if (this.info.length > 0) {
      console.log('\nℹ️  INFORMATION:');
      this.info.forEach(info => {
        console.log(`  • ${info.message}`);
        if (info.fixes) {
          info.fixes.forEach(fix => {
            console.log(`    Fix: ${fix.command}`);
          });
        }
      });
    }

    // Summary
    console.log('\n' + '=' .repeat(50));
    console.log(`📊 SUMMARY: ${this.errors.length} errors, ${this.warnings.length} warnings`);
    
    if (this.errors.length === 0 && this.warnings.length === 0) {
      console.log('✅ All Prisma consistency checks passed!');
    } else if (this.errors.length === 0) {
      console.log('⚠️  Validation passed with warnings');
    } else {
      console.log('❌ Validation failed - please fix errors before proceeding');
    }
  }

  // Run all validations
  async validate() {
    console.log('🚀 Starting Prisma consistency validation...');
    
    this.validateModelExistence();
    this.validateNamingConventions();
    this.validateDatabaseSync();
    this.validateBestPractices();
    this.generateAutoFixes();

    this.displayResults();

    return this.errors.length === 0;
  }
}

// Main execution
async function main() {
  const validator = new PrismaValidator();
  const isValid = await validator.validate();
  
  // Exit with error code if validation failed
  process.exit(isValid ? 0 : 1);
}

// Run if called directly
if (require.main === module) {
  main().catch(error => {
    console.error('❌ Validation script failed:', error.message);
    process.exit(1);
  });
}

module.exports = { PrismaValidator }; 