import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// Enhanced testimonials data with more variety
const testimonialsData = [
  {
    name: "<PERSON>",
    location: "Nairobi, Kenya",
    project: "Website Development, 2 weeks",
    testimonial: "These guys always do awesome work - their developers know their stuff. Plus, the customer service is top-notch; they're responsive, helpful, and always ready to go the extra mile to make me happy.",
    rating: 5,
    company: "TechHub Kenya",
    active: true,
    order: 1
  },
  {
    name: "<PERSON>",
    location: "Mombasa, Kenya",
    project: "Branding, 1 month",
    testimonial: "You are the best! Thanks for another great branding project; I submitted it to my board yesterday and expect great feedback, as always. The logo design and brand guidelines are perfect.",
    rating: 5,
    company: "Coastal Ventures",
    active: true,
    order: 2
  },
  {
    name: "<PERSON>",
    location: "Kisumu, Kenya",
    project: "E-commerce Website, 3 weeks",
    testimonial: "For my retail business, I needed an online store that was both detailed and professionally built. Mocky Digital's service was excellent. The developer assigned to my project had a deep understanding of e-commerce and provided a comprehensive solution.",
    rating: 5,
    company: "Lake Region Retail",
    active: true,
    order: 3
  },
  {
    name: "<PERSON>",
    location: "Nakuru, Kenya",
    project: "Digital Marketing, 2 months",
    testimonial: "Working with Mocky Digital transformed our online presence completely. Their digital marketing strategies increased our website traffic by 300% and our conversion rates doubled. The team is professional, creative, and delivers results.",
    rating: 5,
    company: "Highland Tours",
    active: true,
    order: 4
  },
  {
    name: "James",
    location: "Eldoret, Kenya",
    project: "Logo Design, 1 week",
    testimonial: "I needed a professional logo for my startup and Mocky Digital delivered beyond my expectations. The designer captured the essence of my brand perfectly. The process was smooth and the communication was excellent throughout.",
    rating: 5,
    company: "Upland Innovations",
    active: true,
    order: 5
  },
  {
    name: "Catherine",
    location: "Thika, Kenya",
    project: "UI/UX Design, 3 weeks",
    testimonial: "The user interface design for our app was exactly what we needed. Mocky Digital's attention to detail and understanding of user experience principles resulted in a design that our customers love. Highly recommended for any UI/UX work.",
    rating: 5,
    company: "Central Tech Solutions",
    active: true,
    order: 6
  },
  {
    name: "David",
    location: "Machakos, Kenya",
    project: "Website Redesign, 4 weeks",
    testimonial: "Our old website was outdated and not mobile-friendly. Mocky Digital completely redesigned it with a modern, responsive design that looks great on all devices. Our bounce rate decreased by 40% and user engagement increased significantly.",
    rating: 5,
    company: "Eastern Logistics",
    active: true,
    order: 7
  },
  {
    name: "Grace",
    location: "Nyeri, Kenya",
    project: "Social Media Management, 6 months",
    testimonial: "Mocky Digital has been managing our social media accounts for 6 months now, and the results speak for themselves. Our follower count has tripled, engagement is through the roof, and we're getting more inquiries than ever before.",
    rating: 5,
    company: "Mountain Coffee Co.",
    active: true,
    order: 8
  }
];

interface SeedOptions {
  clearExisting?: boolean;
  force?: boolean;
  verbose?: boolean;
}

async function parseCommandLineArgs(): Promise<SeedOptions> {
  const args = process.argv.slice(2);
  const options: SeedOptions = {
    clearExisting: false,
    force: false,
    verbose: false
  };

  for (const arg of args) {
    switch (arg) {
      case '--clear':
      case '-c':
        options.clearExisting = true;
        break;
      case '--force':
      case '-f':
        options.force = true;
        break;
      case '--verbose':
      case '-v':
        options.verbose = true;
        break;
      case '--help':
      case '-h':
        console.log(`
📝 Testimonials Seeding Script

Usage: npm run seed:testimonials [options]

Options:
  --clear, -c     Clear existing testimonials before seeding
  --force, -f     Force seed even if testimonials exist (implies --clear)
  --verbose, -v   Show detailed output
  --help, -h      Show this help message

Examples:
  npm run seed:testimonials                    # Seed only if no testimonials exist
  npm run seed:testimonials -- --clear        # Clear existing and seed new ones
  npm run seed:testimonials -- --force        # Force clear and seed
  npm run seed:testimonials -- --verbose      # Show detailed output
        `);
        process.exit(0);
    }
  }

  if (options.force) {
    options.clearExisting = true;
  }

  return options;
}

async function clearExistingTestimonials(verbose: boolean = false) {
  try {
    const count = await prisma.testimonial.count();
    if (count > 0) {
      if (verbose) {
        console.log(`🗑️  Found ${count} existing testimonials. Clearing...`);
      }
      
      await prisma.testimonial.deleteMany();
      
      if (verbose) {
        console.log(`✅ Cleared ${count} existing testimonials`);
      }
    } else {
      if (verbose) {
        console.log('ℹ️  No existing testimonials found to clear');
      }
    }
  } catch (error) {
    console.error('❌ Error clearing existing testimonials:', error);
    throw error;
  }
}

async function seedTestimonials(options: SeedOptions) {
  try {
    console.log('🌱 Starting testimonials seeding...');
    
    if (options.verbose) {
      console.log(`📋 Options: ${JSON.stringify(options, null, 2)}`);
    }

    // Check if testimonials already exist
    const existingCount = await prisma.testimonial.count();
    
    if (existingCount > 0 && !options.clearExisting) {
      console.log(`⚠️  Found ${existingCount} existing testimonials.`);
      console.log('💡 Use --clear flag to clear existing testimonials first, or --force to clear and seed.');
      console.log('💡 Run with --help for more options.');
      return;
    }

    // Clear existing testimonials if requested
    if (options.clearExisting) {
      await clearExistingTestimonials(options.verbose);
    }

    // Create testimonials
    console.log(`📝 Creating ${testimonialsData.length} testimonials...`);
    
    let successCount = 0;
    let errorCount = 0;

    for (const testimonialData of testimonialsData) {
      try {
        const testimonial = await prisma.testimonial.create({
          data: testimonialData
        });
        
        successCount++;
        
        if (options.verbose) {
          console.log(`✅ Created testimonial: ${testimonial.name} (${testimonial.company}) - Rating: ${testimonial.rating}/5`);
        } else {
          console.log(`✅ Created testimonial: ${testimonial.name} (${testimonial.company})`);
        }
      } catch (error) {
        errorCount++;
        console.error(`❌ Failed to create testimonial for ${testimonialData.name}:`, error);
      }
    }

    console.log('🎉 Testimonials seeding completed!');
    
    // Display summary
    const totalTestimonials = await prisma.testimonial.count();
    const activeTestimonials = await prisma.testimonial.count({ where: { active: true } });
    
    console.log('\n📊 Summary:');
    console.log(`   Total testimonials: ${totalTestimonials}`);
    console.log(`   Active testimonials: ${activeTestimonials}`);
    console.log(`   Inactive testimonials: ${totalTestimonials - activeTestimonials}`);
    console.log(`   Successfully created: ${successCount}`);
    
    if (errorCount > 0) {
      console.log(`   Errors: ${errorCount}`);
    }

    // Show average rating
    const avgRating = await prisma.testimonial.aggregate({
      _avg: { rating: true },
      where: { active: true }
    });
    
    if (avgRating._avg.rating) {
      console.log(`   Average rating: ${avgRating._avg.rating.toFixed(1)}/5`);
    }

  } catch (error) {
    console.error('❌ Error seeding testimonials:', error);
    throw error;
  }
}

async function main() {
  try {
    const options = await parseCommandLineArgs();
    await seedTestimonials(options);
  } catch (error) {
    console.error('❌ Seeding failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seed function
main();
