const { PrismaClient } = require('@prisma/client');

async function verifyDatabase() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🔍 Verifying Database After Reset...\n');
    
    // Test connection
    console.log('1️⃣ Testing database connection...');
    await prisma.$connect();
    console.log('✅ Database connected successfully\n');
    
    // Check each major model
    const models = [
      { name: 'BlogPost', model: prisma.blogPost },
      { name: 'Category', model: prisma.category },
      { name: 'WebsitePortfolio', model: prisma.websitePortfolio },
      { name: 'Catalogue', model: prisma.catalogue },
      { name: 'Service', model: prisma.service },
      { name: 'Transaction', model: prisma.transaction },
      { name: 'Receipt', model: prisma.receipt },
      { name: 'Invoice', model: prisma.invoice },
      { name: 'Quote', model: prisma.quote },
      { name: 'User', model: prisma.user },
      { name: 'Role', model: prisma.role },
      { name: 'Testimonial', model: prisma.testimonial },
      { name: 'TeamMember', model: prisma.teamMember },
      { name: 'SiteSettings', model: prisma.siteSettings },
      { name: 'Lead', model: prisma.lead },
      { name: 'EventTracking', model: prisma.eventTracking },
      { name: 'SeoPage', model: prisma.seoPage }
    ];
    
    console.log('2️⃣ Checking table structure...');
    for (const { name, model } of models) {
      try {
        const count = await model.count();
        console.log(`   ✅ ${name}: ${count} records`);
      } catch (error) {
        console.log(`   ❌ ${name}: Error - ${error.message}`);
      }
    }
    
    console.log('\n3️⃣ Testing basic operations...');
    
    // Test creating a test record (and cleaning up)
    try {
      const testCategory = await prisma.category.create({
        data: {
          name: 'Test Category',
          slug: 'test-category-' + Date.now(),
          description: 'Test category for verification'
        }
      });
      console.log('   ✅ Create operation: Working');
      
      await prisma.category.delete({
        where: { id: testCategory.id }
      });
      console.log('   ✅ Delete operation: Working');
    } catch (error) {
      console.log(`   ❌ CRUD operations: Error - ${error.message}`);
    }
    
    console.log('\n4️⃣ Database Summary:');
    const totalTables = models.length;
    console.log(`   📊 Total tables checked: ${totalTables}`);
    console.log(`   🎯 Database URL: ${process.env.DATABASE_URL ? 'Set' : 'Not set'}`);
    console.log(`   🏗️  Schema generated: ${new Date().toISOString()}`);
    
    console.log('\n🎉 Database verification complete!');
    console.log('🚀 Your database is ready for use.');
    
  } catch (error) {
    console.error('❌ Database verification failed:', error.message);
    console.error('Full error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

verifyDatabase().catch(console.error); 