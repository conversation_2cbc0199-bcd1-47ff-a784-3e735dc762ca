#!/bin/bash

# 🚀 Mocky Production Deployment Script
# Usage: ./deploy-mocky.sh [environment]

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_DIR="/home/<USER>/mocky"
DOMAIN="mocky.co.ke"
NODE_ENV="${1:-production}"

echo -e "${BLUE}🚀 Starting Mocky Deployment to ${DOMAIN}${NC}"
echo "Environment: $NODE_ENV"
echo "============================================"

# Function to print status
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if running as correct user
if [ "$USER" != "don" ]; then
    print_error "This script should be run as user 'don'"
    exit 1
fi

# Navigate to project directory
cd $PROJECT_DIR || {
    print_error "Project directory not found: $PROJECT_DIR"
    exit 1
}

print_status "Changed to project directory: $PROJECT_DIR"

# Check for uncommitted changes
if [ -n "$(git status --porcelain)" ]; then
    print_warning "You have uncommitted changes. Consider committing them first."
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# Pull latest changes
print_status "Pulling latest changes from git..."
git pull origin main || {
    print_error "Failed to pull from git"
    exit 1
}

# Check environment file
if [ ! -f ".env" ]; then
    print_error ".env file not found! Please create production environment file."
    exit 1
fi

# Verify critical environment variables
print_status "Checking environment variables..."
ENV_ISSUES=0

check_env_var() {
    if ! grep -q "^$1=" .env; then
        print_error "Missing environment variable: $1"
        ENV_ISSUES=$((ENV_ISSUES + 1))
    fi
}

# Check critical variables
check_env_var "DATABASE_URL"
check_env_var "NEXTAUTH_SECRET"
check_env_var "ADMIN_USERNAME"
check_env_var "ADMIN_PASSWORD"

if [ $ENV_ISSUES -gt 0 ]; then
    print_error "Please fix environment variable issues before deploying"
    exit 1
fi

print_status "Environment variables check passed"

# Install dependencies
print_status "Installing dependencies..."
npm ci --production=false || {
    print_error "Failed to install dependencies"
    exit 1
}

# Run security audit
print_status "Running security audit..."
npm audit --audit-level=high || {
    print_warning "Security vulnerabilities found. Review and fix if critical."
}

# Run tests (if available)
if npm run test:all >/dev/null 2>&1; then
    print_status "Running tests..."
    npm run test:all || {
        print_error "Tests failed!"
        read -p "Continue deployment anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    }
fi

# Build the application
print_status "Building application..."
NODE_ENV=$NODE_ENV npm run build || {
    print_error "Build failed!"
    exit 1
}

# Database migrations
print_status "Running database migrations..."
npm run prisma:generate || {
    print_error "Failed to generate Prisma client"
    exit 1
}

npm run prisma:migrate || {
    print_error "Database migration failed!"
    exit 1
}

# Create backup before deployment
print_status "Creating database backup..."
BACKUP_FILE="backup_$(date +%Y%m%d_%H%M%S).sql"
if command -v pg_dump >/dev/null 2>&1; then
    pg_dump $(grep DATABASE_URL .env | cut -d '=' -f2 | tr -d '"') > "./backups/$BACKUP_FILE" || {
        print_warning "Database backup failed, but continuing..."
    }
    print_status "Database backup created: $BACKUP_FILE"
else
    print_warning "pg_dump not found, skipping database backup"
fi

# Stop existing application
print_status "Stopping existing application..."
pm2 stop mocky 2>/dev/null || print_warning "Application was not running"

# Start application with PM2
print_status "Starting application with PM2..."
NODE_ENV=$NODE_ENV pm2 start npm --name "mocky" -- start || {
    print_error "Failed to start application"
    exit 1
}

# Save PM2 configuration
pm2 save

# Wait for application to start
print_status "Waiting for application to start..."
sleep 5

# Check if application is running
if pm2 show mocky >/dev/null 2>&1; then
    print_status "Application is running successfully"
else
    print_error "Application failed to start properly"
    pm2 logs mocky --lines 20
    exit 1
fi

# Test application health
print_status "Testing application health..."
HEALTH_CHECK_URL="http://localhost:3000/api/health"
if curl -f -s $HEALTH_CHECK_URL >/dev/null 2>&1; then
    print_status "Health check passed"
else
    print_warning "Health check endpoint not available or failed"
fi

# Reload nginx if available
if command -v nginx >/dev/null 2>&1; then
    print_status "Reloading nginx configuration..."
    sudo nginx -t && sudo systemctl reload nginx || {
        print_warning "Nginx reload failed"
    }
else
    print_warning "Nginx not found, skipping reload"
fi

# Clear any caches
print_status "Clearing application caches..."
if npm run cache:clear >/dev/null 2>&1; then
    npm run cache:clear
else
    print_warning "Cache clear command not available"
fi

# Final status check
print_status "Performing final status check..."
pm2 status
echo

# Success message
echo "============================================"
echo -e "${GREEN}🎉 Deployment completed successfully!${NC}"
echo
echo "📋 Deployment Summary:"
echo "  Domain: https://$DOMAIN"
echo "  Environment: $NODE_ENV"
echo "  Application: Running on PM2"
echo "  Database: Migrated and backed up"
echo
echo "🔍 Monitoring Commands:"
echo "  pm2 status           - Check application status"
echo "  pm2 logs mocky       - View application logs"
echo "  pm2 monit           - Real-time monitoring"
echo
echo "🌐 Your site should be available at: https://$DOMAIN"
echo "🔧 Admin panel: https://$DOMAIN/admin"
echo

# Optional: Open site in browser (if running on desktop)
if command -v xdg-open >/dev/null 2>&1; then
    read -p "Open site in browser? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        xdg-open "https://$DOMAIN"
    fi
fi

print_status "Deployment script completed!" 