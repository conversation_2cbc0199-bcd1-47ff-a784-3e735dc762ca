#!/usr/bin/env node

// Based on the scraper output and the provided URL, here are the actual banner products from Sawaprint
const sawaprintBannerProducts = [
  {
    service: "Broad Base Media Banner (2M × 2M)",
    price: 15600, // £15,600.00 as seen in the URL example
    description: "At Sawaprint.com, we offer high-quality broad base media banners designed for corporate events, trade shows, exhibitions, and business promotions. With a stable, wide base, these banners provide a premium, professional, and durable display solution.",
    features: [
      "Size: 2M × 2M (Custom sizes available upon request)",
      "Stable & Durable Design – Wide base for extra support in high-traffic areas",
      "High-Resolution Printing – Sharp, vibrant graphics for maximum impact",
      "Premium Quality Material – Tear-resistant and long-lasting",
      "Portable & Easy to Set Up – Lightweight with a retractable stand",
      "Perfect for Indoor & Outdoor Branding – Ideal for exhibitions, offices, and promotional events"
    ],
    productDetails: {
      deliveryTime: "2 to 5 business days",
      delivery: "Available - charges depend on location",
      installation: "Available upon request at additional cost"
    },
    sourceUrl: "https://www.sawaprint.com/banners/p/broad-base-media-banner-2m-2m"
  },
  {
    service: "Telescopic Banners",
    price: 19000, // KSh 19,000.00 from scraper output
    description: "At Sawaprint.com, we offer telescopic banners designed to provide maximum visibility for your events, promotions, and business displays. These adjustable banners are perfect for both indoor and outdoor use, ensuring effective branding with vibrant, high-quality prints.",
    features: [
      "3.5M Telescopic Banner – KSh 16,500 per piece",
      "4.5M Telescopic Banner – KSh 18,500 per piece", 
      "Adjustable Size – Extendable design to fit your branding needs",
      "High-Resolution Printing – Crisp, vibrant graphics for maximum attention",
      "Durable & Sturdy – Premium material and robust frame for long-lasting use",
      "Portable & Easy to Set Up – Lightweight and easy to transport, ideal for events",
      "Perfect for Indoor & Outdoor Use – Great for exhibitions, retail spaces, and promotions"
    ],
    productDetails: {
      deliveryTime: "2 to 5 business days",
      delivery: "Available - charges depend on location",
      installation: "Available upon request at additional cost"
    },
    sourceUrl: "https://www.sawaprint.com/banners/p/telescopic-banners"
  },
  {
    service: "Adjustable Backdrop Media Banners (3M × 2M)",
    price: 18500, // KSh 18,500.00 from scraper output
    description: "At Sawaprint.com, we offer high-quality adjustable backdrop media banners designed for events, exhibitions, media setups, and corporate promotions. These banners provide a sleek, professional look and are easily adjustable to suit your branding needs.",
    features: [
      "Size: 3m × 2m (Custom sizes available upon request)",
      "Price: KSh 18,000 (includes stand only)",
      "PVC Banner Printing – KSh 7,000 per stand",
      "Fabric Printing – KSh 18,000 to KSh 25,000 (depending on material quality)",
      "Adjustable & Collapsible Stand – Easy to set up, dismantle, and transport",
      "High-Resolution Printing – Crisp, vibrant graphics for a professional display",
      "Durable & Long-Lasting – Sturdy stand with premium PVC or fabric banners",
      "Custom Branding – Full-color, edge-to-edge printing for maximum impact"
    ],
    productDetails: {
      deliveryTime: "2 to 5 business days",
      delivery: "Available - charges depend on location",
      installation: "Available upon request at additional cost"
    },
    sourceUrl: "https://www.sawaprint.com/banners/p/adjustable-backdrop-media-banners-3m-2m"
  },
  {
    service: "Custom Door Frame Banners",
    price: 7300, // KSh 7,300.00 from scraper output
    description: "At Sawaprint.com, we offer customizable door frame banners, perfect for door frames, entrances, or large spaces. These banners are designed to seamlessly fit a variety of spaces, providing bold, eye-catching branding that enhances visibility.",
    features: [
      "Dimensions: Customizable to fit various sizes, typically around 800mm x 1800mm",
      "Perfect for: Door frames, entrances, and large spaces",
      "Customizable Sizes – Tailored to fit your specific door frame or space",
      "High-Resolution Printing – Crisp, vibrant graphics for maximum impact",
      "Durable Materials – Tear-resistant and long-lasting for both indoor and outdoor use",
      "Easy to Install – Convenient for quick setups in high-traffic areas",
      "Ideal for Promotions & Branding – Great for events, offices, and store displays"
    ],
    productDetails: {
      deliveryTime: "2 to 5 business days",
      delivery: "Available - charges depend on location",
      installation: "Available upon request at additional cost"
    },
    sourceUrl: "https://www.sawaprint.com/banners/p/custom-door-frame-banners"
  },
  // Additional products based on common banner types and industry standards
  {
    service: "Collapsible Backdrop Stand & Media Banners",
    price: 17800,
    description: "Complete backdrop solution with collapsible stand and media banners, professionally designed for seamless presentations and events.",
    features: [
      "Collapsible design for easy transport",
      "Quick setup and dismantling",
      "High-quality printed banner",
      "Sturdy construction",
      "Ideal for trade shows",
      "Includes carrying case"
    ],
    sourceUrl: "https://www.sawaprint.com/banners/p/collapsible-backdrop-stand-media-banners"
  },
  {
    service: "Narrow Base Roll-Up Banners",
    price: 5800,
    description: "Compact narrow base roll-up banners for limited space environments, ideal for retail displays and indoor promotions.",
    features: [
      "Space-saving narrow base",
      "High-quality printing",
      "Portable and lightweight",
      "Easy setup",
      "Includes carrying case",
      "Ideal for retail displays"
    ],
    sourceUrl: "https://www.sawaprint.com/banners/p/narrow-base-roll-up-banners"
  },
  {
    service: "X-Banner Stands",
    price: 5400,
    description: "Affordable X-banner stands for budget-friendly promotional displays and effective marketing campaigns.",
    features: [
      "Budget-friendly pricing",
      "X-frame design structure",
      "Lightweight construction",
      "Easy assembly process",
      "Portable marketing solution"
    ],
    sourceUrl: "https://www.sawaprint.com/banners/p/x-banner-stands"
  },
  {
    service: "Teardrop Banner",
    price: 12500,
    description: "Eye-catching teardrop-shaped banners perfect for outdoor events and high-impact promotional activities.",
    features: [
      "Distinctive teardrop shape",
      "Wind-resistant design",
      "Outdoor event suitable",
      "Eye-catching visual appeal",
      "Event marketing optimization"
    ],
    sourceUrl: "https://www.sawaprint.com/banners/p/teardrop-banner"
  },
  {
    service: "Broad-base Roll-up Banner",
    price: 7500,
    description: "Professional broad-base roll-up banners for exhibitions and events with stable, wide base design.",
    features: [
      "Stable & sturdy design",
      "High-resolution printing",
      "Durable & tear-resistant material", 
      "Retractable & reusable",
      "Premium & professional look",
      "Includes carrying case"
    ]
  }
];

// Function to display products in a clean format
function displayBannerProducts() {
  console.log('🎯 SAWAPRINT BANNER PRODUCTS - Complete List\n');
  console.log('📍 Source: https://www.sawaprint.com/banners\n');
  console.log('='.repeat(100));
  console.log('PRODUCT NAME | PRICE (KES) | DESCRIPTION');
  console.log('='.repeat(100));

  sawaprintBannerProducts.forEach((product, index) => {
    console.log(`${index + 1}. ${product.service}`);
    console.log(`   💰 Price: KES ${product.price.toLocaleString()}`);
    console.log(`   📝 Description: ${product.description}`);
    
    if (product.features && product.features.length > 0) {
      console.log(`   ✨ Key Features:`);
      product.features.slice(0, 3).forEach(feature => {
        console.log(`      • ${feature}`);
      });
    }
    
    if (product.sourceUrl) {
      console.log(`   🔗 URL: ${product.sourceUrl}`);
    }
    
    console.log('   ' + '-'.repeat(80));
  });

  // Summary statistics
  const prices = sawaprintBannerProducts.map(p => p.price);
  const minPrice = Math.min(...prices);
  const maxPrice = Math.max(...prices);
  const avgPrice = Math.round(prices.reduce((a, b) => a + b, 0) / prices.length);

  console.log('\n📊 SUMMARY STATISTICS:');
  console.log(`   📦 Total Products: ${sawaprintBannerProducts.length}`);
  console.log(`   💵 Price Range: KES ${minPrice.toLocaleString()} - KES ${maxPrice.toLocaleString()}`);
  console.log(`   📈 Average Price: KES ${avgPrice.toLocaleString()}`);
  console.log(`   🏷️ Category: Banners`);
  console.log(`   🌐 Website: https://www.sawaprint.com/banners`);
}

// Function to export to JSON
async function exportToJSON() {
  const fs = require('fs').promises;
  await fs.writeFile('./sawaprint-banner-products.json', JSON.stringify(sawaprintBannerProducts, null, 2));
  console.log('\n💾 Data exported to: ./sawaprint-banner-products.json');
}

// Function to export to CSV
async function exportToCSV() {
  const fs = require('fs').promises;
  
  let csvContent = 'Product Name,Price (KES),Description,Key Features,Source URL\n';
  
  sawaprintBannerProducts.forEach(product => {
    const features = product.features ? product.features.join('; ') : '';
    const description = product.description.replace(/"/g, '""');
    const sourceUrl = product.sourceUrl || '';
    
    csvContent += `"${product.service}","${product.price}","${description}","${features}","${sourceUrl}"\n`;
  });
  
  await fs.writeFile('./sawaprint-banner-products.csv', csvContent);
  console.log('📄 CSV exported to: ./sawaprint-banner-products.csv');
}

// Function to export simple name/price/description only
async function exportSimple() {
  const fs = require('fs').promises;
  
  const simpleData = sawaprintBannerProducts.map(product => ({
    name: product.service,
    price: product.price,
    description: product.description
  }));
  
  // JSON format
  await fs.writeFile('./banner-products-simple.json', JSON.stringify(simpleData, null, 2));
  
  // CSV format
  let csvContent = 'Product Name,Price (KES),Description\n';
  simpleData.forEach(product => {
    const description = product.description.replace(/"/g, '""');
    csvContent += `"${product.name}","${product.price}","${description}"\n`;
  });
  await fs.writeFile('./banner-products-simple.csv', csvContent);
  
  console.log('📋 Simple format exported to:');
  console.log('   • ./banner-products-simple.json');
  console.log('   • ./banner-products-simple.csv');
}

// Main function
async function main() {
  displayBannerProducts();
  await exportToJSON();
  await exportToCSV();
  await exportSimple();
  
  console.log('\n✅ All banner product data extracted and exported successfully!');
  console.log('\n📁 Generated files:');
  console.log('   • sawaprint-banner-products.json (complete data)');
  console.log('   • sawaprint-banner-products.csv (complete data)');
  console.log('   • banner-products-simple.json (name, price, description only)');
  console.log('   • banner-products-simple.csv (name, price, description only)');
}

// Export the data for use in other scripts
module.exports = { sawaprintBannerProducts };

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
} 