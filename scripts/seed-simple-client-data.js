const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Creating simple client portal test data...');

  try {
    // Find existing admin role
    let adminRole = await prisma.role.findFirst({
      where: { name: 'admin' }
    });

    if (!adminRole) {
      adminRole = await prisma.role.create({
        data: {
          name: 'admin',
          description: 'Administrator with full access',
          permissions: ['all']
        }
      });
      console.log('✅ Created admin role');
    } else {
      console.log('✅ Found existing admin role');
    }

    // Create basic users
    const hashedPassword = await bcrypt.hash('password123', 12);

    const adminUser = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        username: 'admin_user',
        email: '<EMAIL>',
        name: 'Admin User',
        passwordHash: hashedPassword,
        roleId: adminRole.id,
        active: true,
      },
    });

    console.log('✅ Created admin user');

    // Create test clients
    const client1 = await prisma.client.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        firstName: 'Alice',
        lastName: 'Johnson',
        email: '<EMAIL>',
        phone: '******-0101',
        company: 'TechStart Inc.',
        position: 'CEO',
        address: '123 Innovation Drive',
        city: 'San Francisco',
        country: 'USA',
        status: 'active',
        priority: 'high',
        createdBy: adminUser.id,
      },
    });

    const client2 = await prisma.client.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        firstName: 'Bob',
        lastName: 'Smith',
        email: '<EMAIL>',
        phone: '******-0102',
        company: 'Global Commerce LLC',
        position: 'CTO',
        address: '456 Business Blvd',
        city: 'New York',
        country: 'USA',
        status: 'active',
        priority: 'medium',
        createdBy: adminUser.id,
      },
    });

    console.log('✅ Created test clients');

    // Create projects
    const project1 = await prisma.project.upsert({
      where: { id: '00000000-0000-0000-0000-000000000001' },
      update: {},
      create: {
        id: '00000000-0000-0000-0000-000000000001',
        name: 'E-commerce Website Development',
        description: 'A complete e-commerce solution with modern design and payment integration.',
        clientId: client1.id,
        status: 'active',
        priority: 'high',
        startDate: new Date('2024-01-15'),
        deadline: new Date('2024-03-15'),
        budget: 25000.00,
        progress: 65,
        createdBy: adminUser.id,
      },
    });

    const project2 = await prisma.project.upsert({
      where: { id: '00000000-0000-0000-0000-000000000002' },
      update: {},
      create: {
        id: '00000000-0000-0000-0000-000000000002',
        name: 'Mobile App Development',
        description: 'Cross-platform mobile application with React Native.',
        clientId: client2.id,
        status: 'active',
        priority: 'medium',
        startDate: new Date('2024-02-01'),
        deadline: new Date('2024-04-30'),
        budget: 35000.00,
        progress: 40,
        createdBy: adminUser.id,
      },
    });

    console.log('✅ Created projects');

    // Create tasks
    await prisma.task.upsert({
      where: { id: '00000000-0000-0000-0000-000000000001' },
      update: {},
      create: {
        id: '00000000-0000-0000-0000-000000000001',
        projectId: project1.id,
        title: 'Project Discovery & Requirements',
        description: 'Gather requirements and create project scope document',
        status: 'completed',
        priority: 'high',
        assignedTo: adminUser.id,
        assignedBy: adminUser.id,
        startDate: new Date('2024-01-15'),
        dueDate: new Date('2024-01-22'),
        estimatedHours: 16,
        actualHours: 14,
        completedAt: new Date('2024-01-20'),
      },
    });

    await prisma.task.upsert({
      where: { id: '00000000-0000-0000-0000-000000000002' },
      update: {},
      create: {
        id: '00000000-0000-0000-0000-000000000002',
        projectId: project1.id,
        title: 'Design Mockups & Wireframes',
        description: 'Create initial design concepts and wireframes',
        status: 'in_progress',
        priority: 'high',
        assignedTo: adminUser.id,
        assignedBy: adminUser.id,
        startDate: new Date('2024-01-22'),
        dueDate: new Date('2024-01-29'),
        estimatedHours: 24,
        actualHours: 16,
      },
    });

    console.log('✅ Created tasks');

    // Create milestones
    await prisma.projectMilestone.upsert({
      where: { id: '00000000-0000-0000-0000-000000000001' },
      update: {},
      create: {
        id: '00000000-0000-0000-0000-000000000001',
        projectId: project1.id,
        title: 'Project Kickoff',
        description: 'Project initiation and team setup completed',
        dueDate: new Date('2024-01-18'),
        status: 'completed',
        requiresApproval: false,
        completedAt: new Date('2024-01-17'),
      },
    });

    await prisma.projectMilestone.upsert({
      where: { id: '00000000-0000-0000-0000-000000000002' },
      update: {},
      create: {
        id: '00000000-0000-0000-0000-000000000002',
        projectId: project1.id,
        title: 'Design Approval',
        description: 'Client approval of design mockups and wireframes',
        dueDate: new Date('2024-01-29'),
        status: 'completed',
        requiresApproval: true,
        completedAt: new Date(),
        approvedAt: null, // Waiting for approval
      },
    });

    console.log('✅ Created milestones');

    // Create portal access for clients
    await prisma.clientPortalAccess.upsert({
      where: { clientId: client1.id },
      update: {},
      create: {
        clientId: client1.id,
        email: client1.email,
        passwordHash: hashedPassword,
        isActive: true,
        permissions: ['view_projects', 'view_documents', 'submit_feedback', 'view_invoices'],
        lastLogin: new Date(),
      },
    });

    await prisma.clientPortalAccess.upsert({
      where: { clientId: client2.id },
      update: {},
      create: {
        clientId: client2.id,
        email: client2.email,
        passwordHash: hashedPassword,
        isActive: true,
        permissions: ['view_projects', 'view_documents', 'submit_feedback', 'view_invoices'],
        lastLogin: new Date(),
      },
    });

    console.log('✅ Created portal access');

    // Create notifications
    await prisma.clientNotification.create({
      data: {
        clientId: client1.id,
        title: 'Project Milestone Completed',
        message: 'The design phase of your project has been completed and is ready for your review.',
        type: 'success',
        priority: 'medium',
        isRead: false,
        actionUrl: '/client-portal?tab=projects',
      },
    });

    await prisma.clientNotification.create({
      data: {
        clientId: client1.id,
        title: 'Milestone Approval Required',
        message: 'The development milestone is complete and requires your approval to proceed.',
        type: 'warning',
        priority: 'urgent',
        isRead: false,
        actionUrl: '/client-portal?tab=projects',
      },
    });

    console.log('✅ Created notifications');

    console.log(`
🎉 Simple client portal data created successfully!

📊 Created:
- 1 Admin user (<EMAIL> / password123)
- 2 Test clients (<EMAIL>, <EMAIL> / password123)
- 2 Projects with different statuses
- Sample tasks and milestones
- Portal access for clients
- Test notifications

🚀 You can now test the client portal with the test data!
    `);

  } catch (error) {
    console.error('❌ Error creating test data:', error);
    throw error;
  }
}

main()
  .catch((e) => {
    console.error(e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  }); 