#!/bin/bash

# Enhanced Production Deployment Script with Prisma Migration Safety
# This script provides maximum safety for production database updates
# Usage: ./scripts/enhanced-production-deploy.sh [--auto-approve]

set -e  # Exit on any error

# Configuration
BACKUP_DIR="./backups"
LOG_FILE="deployment-$(date +%Y%m%d_%H%M%S).log"
SLACK_WEBHOOK="${SLACK_WEBHOOK_URL}"  # Set this in your environment

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
}

# Send notification to Slack
notify_slack() {
    local message="$1"
    local status="$2"  # success, warning, error
    
    if [ -n "$SLACK_WEBHOOK" ]; then
        local color="good"
        case $status in
            "error") color="danger" ;;
            "warning") color="warning" ;;
        esac
        
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"attachments\":[{\"color\":\"$color\",\"text\":\"$message\"}]}" \
            "$SLACK_WEBHOOK" || true
    fi
}

# Create comprehensive database backup
create_backup() {
    log "Creating comprehensive database backup..."
    mkdir -p "$BACKUP_DIR"
    
    local timestamp=$(date +%Y%m%d_%H%M%S)
    local backup_file="$BACKUP_DIR/production_backup_$timestamp.dump"
    
    # Create binary backup
    if command -v pg_dump >/dev/null 2>&1; then
        pg_dump "$DATABASE_URL" -F c -f "$backup_file"
        success "Database backup created: $backup_file"
        echo "$backup_file" > "$BACKUP_DIR/latest_backup.txt"
    else
        error "pg_dump not found. Cannot create backup."
        exit 1
    fi
    
    # Also create SQL backup for easier inspection
    pg_dump "$DATABASE_URL" > "$BACKUP_DIR/production_backup_$timestamp.sql"
    
    # Verify backup integrity
    if [ -f "$backup_file" ] && [ -s "$backup_file" ]; then
        success "Backup verification passed"
    else
        error "Backup verification failed"
        exit 1
    fi
}

# Check for schema changes
check_schema_changes() {
    log "Checking for Prisma schema changes..."
    
    # Generate migration preview
    local migration_preview=$(npx prisma migrate diff \
        --from-schema-datasource "$DATABASE_URL" \
        --to-schema-datamodel prisma/schema.prisma || echo "No differences")
    
    if [ "$migration_preview" != "No differences" ]; then
        warning "Schema changes detected:"
        echo "$migration_preview"
        echo ""
        
        # Count critical operations
        local drops=$(echo "$migration_preview" | grep -c "DROP" || echo "0")
        local alters=$(echo "$migration_preview" | grep -c "ALTER" || echo "0")
        local creates=$(echo "$migration_preview" | grep -c "CREATE" || echo "0")
        
        log "Migration impact: $creates creates, $alters alters, $drops drops"
        
        if [ "$drops" -gt 0 ]; then
            warning "⚠️  DESTRUCTIVE OPERATIONS DETECTED: $drops DROP statements"
            echo "This migration contains potentially destructive operations."
            echo "Please review carefully before proceeding."
        fi
        
        # Require manual approval unless --auto-approve is passed
        if [ "$1" != "--auto-approve" ]; then
            echo ""
            read -p "Do you want to proceed with this migration? (yes/no): " -r
            if [[ ! $REPLY =~ ^[Yy][Ee][Ss]$ ]]; then
                log "Migration cancelled by user"
                exit 0
            fi
        fi
        
        return 0
    else
        log "No schema changes detected"
        return 1
    fi
}

# Pre-deployment validation
validate_environment() {
    log "Validating deployment environment..."
    
    # Check required tools
    local required_tools=("git" "npm" "npx" "pg_dump" "curl")
    for tool in "${required_tools[@]}"; do
        if ! command -v "$tool" >/dev/null 2>&1; then
            error "Required tool not found: $tool"
            exit 1
        fi
    done
    
    # Check environment variables
    if [ -z "$DATABASE_URL" ]; then
        error "DATABASE_URL environment variable not set"
        exit 1
    fi
    
    # Test database connection
    if ! npx prisma db execute --stdin <<< "SELECT 1;" >/dev/null 2>&1; then
        error "Cannot connect to database"
        exit 1
    fi
    
    # Check Node.js version
    local node_version=$(node --version | cut -d'v' -f2)
    local required_version="18.0.0"
    if ! npx semver "$node_version" -r ">=$required_version" >/dev/null 2>&1; then
        warning "Node.js version $node_version may not be compatible (recommended: >=$required_version)"
    fi
    
    success "Environment validation passed"
}

# Deploy application
deploy_application() {
    log "Starting application deployment..."
    
    # Pull latest changes
    log "Pulling latest changes from repository..."
    git pull origin main
    
    # Install dependencies
    log "Installing dependencies..."
    npm ci --production=false
    
    # Run our validation
    log "Running Prisma consistency validation..."
    npm run db:validate || {
        error "Prisma validation failed"
        exit 1
    }
    
    # Generate Prisma client
    log "Generating Prisma client..."
    npx prisma generate
    
    # Apply database migrations if needed
    if check_schema_changes "$1"; then
        log "Applying database migrations..."
        npx prisma migrate deploy || {
            error "Migration failed"
            log "Attempting to restore from backup..."
            restore_backup
            exit 1
        }
        success "Database migrations applied successfully"
    fi
    
    # Build application
    log "Building application..."
    npm run build || {
        error "Build failed"
        exit 1
    }
    
    # Restart application
    log "Restarting application..."
    if command -v pm2 >/dev/null 2>&1; then
        pm2 restart mocky-digital || pm2 start ecosystem.config.js
    else
        warning "PM2 not found, manual application restart may be required"
    fi
    
    success "Application deployment completed"
}

# Restore from backup
restore_backup() {
    local backup_file=$(cat "$BACKUP_DIR/latest_backup.txt" 2>/dev/null || echo "")
    
    if [ -n "$backup_file" ] && [ -f "$backup_file" ]; then
        warning "Restoring database from backup: $backup_file"
        pg_restore -d "$DATABASE_URL" --clean --if-exists "$backup_file"
        success "Database restored from backup"
    else
        error "No backup file found for restoration"
    fi
}

# Post-deployment verification
verify_deployment() {
    log "Verifying deployment..."
    
    # Test application health
    local health_check_url="http://localhost:3000/api/health"
    if curl -f "$health_check_url" >/dev/null 2>&1; then
        success "Application health check passed"
    else
        error "Application health check failed"
        return 1
    fi
    
    # Test database connection
    if npx prisma db execute --stdin <<< "SELECT 1;" >/dev/null 2>&1; then
        success "Database connection verified"
    else
        error "Database connection failed"
        return 1
    fi
    
    # Run Prisma tests
    if npm run test:prisma >/dev/null 2>&1; then
        success "Prisma consistency tests passed"
    else
        warning "Prisma tests failed (non-critical)"
    fi
    
    # Test critical API endpoints
    local critical_endpoints=("/api/admin/stats" "/api/blog" "/api/catalogue")
    for endpoint in "${critical_endpoints[@]}"; do
        if curl -f "http://localhost:3000$endpoint" >/dev/null 2>&1; then
            success "Endpoint $endpoint is responsive"
        else
            warning "Endpoint $endpoint is not responsive"
        fi
    done
    
    success "Deployment verification completed"
}

# Main deployment function
main() {
    log "Starting enhanced production deployment..."
    notify_slack "🚀 Starting production deployment" "warning"
    
    # Validate environment
    validate_environment
    
    # Create backup
    create_backup
    
    # Deploy application
    deploy_application "$1"
    
    # Verify deployment
    if verify_deployment; then
        success "🎉 Production deployment completed successfully!"
        notify_slack "✅ Production deployment completed successfully" "success"
        
        # Cleanup old backups (keep last 10)
        find "$BACKUP_DIR" -name "production_backup_*.dump" -type f | sort -r | tail -n +11 | xargs rm -f
        
        log "Deployment summary saved to: $LOG_FILE"
    else
        error "🚨 Deployment verification failed"
        notify_slack "❌ Production deployment failed verification" "error"
        
        # Consider automatic rollback
        read -p "Would you like to rollback to the previous backup? (yes/no): " -r
        if [[ $REPLY =~ ^[Yy][Ee][Ss]$ ]]; then
            restore_backup
        fi
        
        exit 1
    fi
}

# Handle script arguments
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "Enhanced Production Deployment Script"
    echo ""
    echo "Usage: $0 [--auto-approve]"
    echo ""
    echo "Options:"
    echo "  --auto-approve    Skip manual approval for migrations"
    echo "  --help, -h        Show this help message"
    echo ""
    echo "Environment variables:"
    echo "  DATABASE_URL      Production database connection string"
    echo "  SLACK_WEBHOOK_URL Optional Slack webhook for notifications"
    exit 0
fi

# Run main function
main "$1" 