const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

async function resetUsers() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🗑️ Deleting all users...');
    
    // Delete activity logs first (foreign key constraint)
    await prisma.activityLog.deleteMany({});
    console.log('✅ Activity logs deleted');
    
    // Delete all users
    await prisma.user.deleteMany({});
    console.log('✅ All users deleted');
    
    // Find admin role
    let adminRole = await prisma.role.findFirst({ where: { name: 'admin' } });
    
    if (!adminRole) {
      adminRole = await prisma.role.create({
        data: {
          name: 'admin',
          description: 'Administrator',
          permissions: ['*']
        }
      });
    }
    
    console.log('👑 Creating fresh admin user...');
    
    // Get from env or use defaults
    const username = process.env.ADMIN_USERNAME || 'admin';
    const password = process.env.ADMIN_PASSWORD || 'Jack75522r';
    const email = process.env.ADMIN_EMAIL || '<EMAIL>';
    
    // Hash password
    const passwordHash = await bcrypt.hash(password, 12);
    
    // Create admin user
    const adminUser = await prisma.user.create({
      data: {
        username,
        email,
        name: 'System Administrator',
        passwordHash,
        roleId: adminRole.id,
        active: true
      }
    });
    
    console.log('✅ Fresh admin user created!');
    console.log(`   Username: ${username}`);
    console.log(`   Email: ${email}`);
    console.log(`   Password: ${password}`);
    
    // Test the password
    const testPassword = await bcrypt.compare(password, adminUser.passwordHash);
    console.log(`   Password test: ${testPassword ? '✅ SUCCESS' : '❌ FAILED'}`);
    
    console.log('\n🎉 USER RESET COMPLETE!');
    console.log('Now try logging in at: https://mocky.co.ke/admin/login');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

resetUsers(); 