#!/bin/bash

echo "🔍 Nginx Configuration <PERSON><PERSON><PERSON><PERSON> and Fix Script"
echo "================================================"

# Check if running as root
if [ "$EUID" -ne 0 ]; then
    echo "❌ Please run this script as root (sudo ./fix-nginx-config.sh)"
    exit 1
fi

echo "📋 1. Checking current nginx status..."
systemctl status nginx --no-pager -l

echo -e "\n📋 2. Testing nginx configuration..."
nginx -t

echo -e "\n📋 3. Checking current nginx sites..."
echo "Sites available:"
ls -la /etc/nginx/sites-available/ 2>/dev/null || echo "Directory not found"
echo "Sites enabled:"
ls -la /etc/nginx/sites-enabled/ 2>/dev/null || echo "Directory not found"

echo -e "\n📋 4. Checking if PM2 app is running..."
pm2 status

echo -e "\n📋 5. Testing localhost connection..."
curl -I http://localhost:3000 || echo "❌ Cannot connect to localhost:3000"

echo -e "\n🔧 6. Creating proper nginx configuration for mocky.co.ke..."

# Create the nginx site configuration
cat > /etc/nginx/sites-available/mocky.co.ke << 'EOF'
# Redirect HTTP to HTTPS
server {
    listen 80;
    listen [::]:80;
    server_name mocky.co.ke www.mocky.co.ke;
    return 301 https://$server_name$request_uri;
}

# Main HTTPS Server
server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name mocky.co.ke www.mocky.co.ke;
    
    # SSL Configuration (will be configured by Certbot)
    # ssl_certificate /etc/letsencrypt/live/mocky.co.ke/fullchain.pem;
    # ssl_certificate_key /etc/letsencrypt/live/mocky.co.ke/privkey.pem;
    
    # Security Headers
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header X-XSS-Protection "1; mode=block" always;
    
    # Gzip Compression
    gzip on;
    gzip_vary on;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json
        image/svg+xml;
    
    # Static Files Caching (including logo)
    location ~* \.(jpg|jpeg|gif|png|svg|ico|webp|css|js|woff|woff2|ttf|eot)$ {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary Accept-Encoding;
    }
    
    # API Routes
    location /api {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # Admin Routes
    location /admin {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    # Next.js Application
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    # Block access to sensitive files
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    location ~ /(\.env|package\.json|tsconfig\.json)$ {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    # Error pages
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
    
    # Logging
    access_log /var/log/nginx/mocky.co.ke.access.log;
    error_log /var/log/nginx/mocky.co.ke.error.log;
}

# Temporary HTTP server for development/testing
server {
    listen 80;
    listen [::]:80;
    server_name localhost;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
EOF

echo "✅ Created nginx configuration for mocky.co.ke"

echo -e "\n🔧 7. Enabling the site..."
# Remove default site if exists
if [ -L "/etc/nginx/sites-enabled/default" ]; then
    rm /etc/nginx/sites-enabled/default
    echo "✅ Removed default site"
fi

# Enable new site
ln -sf /etc/nginx/sites-available/mocky.co.ke /etc/nginx/sites-enabled/
echo "✅ Enabled mocky.co.ke site"

echo -e "\n🔧 8. Testing new configuration..."
nginx -t

if [ $? -eq 0 ]; then
    echo "✅ Configuration is valid"
    echo -e "\n🔧 9. Reloading nginx..."
    systemctl reload nginx
    echo "✅ Nginx reloaded"
    
    echo -e "\n🧪 10. Testing the logo access..."
    sleep 2
    echo "Testing localhost logo access:"
    curl -I http://localhost/images/logo.png || echo "❌ Failed"
    
    echo -e "\nTesting direct localhost:"
    curl -s http://localhost | head -5
    
    echo -e "\n✅ Setup complete!"
    echo "📝 Next steps:"
    echo "   1. Your logo should now be accessible at: http://mocky.co.ke/images/logo.png"
    echo "   2. Install SSL certificate with: sudo certbot --nginx -d mocky.co.ke -d www.mocky.co.ke"
    echo "   3. Monitor logs: sudo tail -f /var/log/nginx/mocky.co.ke.error.log"
else
    echo "❌ Configuration has errors. Please check and try again."
    exit 1
fi 