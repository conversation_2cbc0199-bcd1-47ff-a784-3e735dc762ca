const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function seedPaperTypes() {
  console.log('🌱 Seeding paper types...');

  // Paper types from the pricing table image
  const paperTypes = [
    { 
      name: 'ART/MATT PAPER', 
      grammage: '130G', 
      oneSidedPrice: 20, 
      twoSidedPrice: 30,
      category: 'paper',
      order: 1
    },
    { 
      name: 'ART/MATT CARD', 
      grammage: '300G', 
      oneSidedPrice: 28, 
      twoSidedPrice: 45,
      category: 'card',
      order: 2
    },
    { 
      name: 'ART/MATT CARD', 
      grammage: '250G', 
      oneSidedPrice: 27, 
      twoSidedPrice: 40,
      category: 'card',
      order: 3
    },
    { 
      name: 'TIC-TAC', 
      grammage: '', 
      oneSidedPrice: 35, 
      twoSidedPrice: 0, // Not available for double-sided
      category: 'specialty',
      order: 4
    },
    { 
      name: 'ART/MATT PAPER', 
      grammage: '150G', 
      oneSidedPrice: 24, 
      twoSidedPrice: 38,
      category: 'paper',
      order: 5
    },
    { 
      name: 'ART/MATT PAPER', 
      grammage: '170G', 
      oneSidedPrice: 26, 
      twoSidedPrice: 44,
      category: 'paper',
      order: 6
    },
    { 
      name: 'LAMINATION', 
      grammage: '', 
      oneSidedPrice: 5, 
      twoSidedPrice: 10,
      category: 'finishing',
      order: 7
    },
    { 
      name: 'ROUND CORNER', 
      grammage: '', 
      oneSidedPrice: 1, 
      twoSidedPrice: 0, // Not applicable for double-sided
      category: 'finishing',
      order: 8
    },
    { 
      name: 'BOND A3', 
      grammage: '80GMS', 
      oneSidedPrice: 12, 
      twoSidedPrice: 20,
      category: 'bond',
      order: 9
    },
    { 
      name: 'BOND A4', 
      grammage: '80GMS', 
      oneSidedPrice: 8, 
      twoSidedPrice: 15,
      category: 'bond',
      order: 10
    },
    { 
      name: 'BOND B/W', 
      grammage: '80GMS', 
      oneSidedPrice: 5, 
      twoSidedPrice: 10,
      category: 'bond',
      order: 11
    }
  ];

  for (const paperType of paperTypes) {
    const uniqueId = `${paperType.name.toLowerCase().replace(/[^a-z0-9]/g, '-')}-${paperType.grammage.toLowerCase().replace(/[^a-z0-9]/g, '-')}`;
    
    try {
      await prisma.paperType.upsert({
        where: { 
          id: uniqueId
        },
        update: {
          name: paperType.name,
          grammage: paperType.grammage,
          oneSidedPrice: paperType.oneSidedPrice,
          twoSidedPrice: paperType.twoSidedPrice,
          category: paperType.category,
          order: paperType.order,
          active: true,
          updatedAt: new Date()
        },
        create: {
          id: uniqueId,
          name: paperType.name,
          grammage: paperType.grammage,
          oneSidedPrice: paperType.oneSidedPrice,
          twoSidedPrice: paperType.twoSidedPrice,
          category: paperType.category,
          order: paperType.order,
          active: true
        }
      });
      
      console.log(`✅ Seeded: ${paperType.name} ${paperType.grammage} - 1-sided: KSh ${paperType.oneSidedPrice}, 2-sided: KSh ${paperType.twoSidedPrice}`);
    } catch (error) {
      console.error(`❌ Error seeding ${paperType.name} ${paperType.grammage}:`, error);
    }
  }

  console.log('✅ Paper types seeding completed!');
}

async function seedSampleProducts() {
  console.log('Updating sample products with pricing types...');

  const updates = [
    {
      service: 'Business Cards',
      pricingType: 'paper_print',
      unitType: 'piece',
      minQuantity: 100,
      maxQuantity: 5000
    },
    {
      service: 'Flyers',
      pricingType: 'paper_print',
      unitType: 'piece',
      minQuantity: 50,
      maxQuantity: 10000
    },
    {
      service: 'Banner Print',
      pricingType: 'banner_meter',
      unitType: 'meter',
      pricePerMeter: 100,
      minMeters: 0.5,
      maxMeters: 50
    },
    {
      service: 'Vinyl Banner',
      pricingType: 'banner_meter',
      unitType: 'meter',
      pricePerMeter: 150,
      minMeters: 1,
      maxMeters: 100
    },
    {
      service: 'Roll-up Banner',
      pricingType: 'fixed',
      unitType: 'piece',
      minQuantity: 1,
      maxQuantity: 50
    }
  ];

  for (const update of updates) {
    try {
      const product = await prisma.catalogue.findFirst({
        where: { service: { contains: update.service, mode: 'insensitive' } }
      });

      if (product) {
        await prisma.catalogue.update({
          where: { id: product.id },
          data: {
            pricingType: update.pricingType,
            unitType: update.unitType,
            pricePerMeter: update.pricePerMeter || null,
            minQuantity: update.minQuantity || null,
            maxQuantity: update.maxQuantity || null,
            minMeters: update.minMeters || null,
            maxMeters: update.maxMeters || null
          }
        });
        console.log(`✓ Updated ${update.service} pricing type to ${update.pricingType}`);
      } else {
        console.log(`? Product not found: ${update.service}`);
      }
    } catch (error) {
      console.error(`✗ Failed to update ${update.service}:`, error.message);
    }
  }
}

async function main() {
  try {
    await seedPaperTypes();
    await seedSampleProducts();
    console.log('\n✅ Seeding completed successfully!');
  } catch (error) {
    console.error('❌ Seeding failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main(); 