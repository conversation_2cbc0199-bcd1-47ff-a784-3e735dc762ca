#!/usr/bin/env node

console.log('🔍 Comprehensive Admin System Issues Check\n');

console.log('🎯 CHECKING FOR RELATED ISSUES:');
console.log('===============================');

const issues = {
  csrf: [],
  apiResponse: [],
  navigation: [],
  security: [],
  consistency: []
};

console.log('\n1. 🔒 CSRF TOKEN ISSUES:');
console.log('========================');

console.log('\n✅ ALREADY FIXED:');
console.log('• Services page - useCsrfToken hook implemented');
console.log('• Receipts page - useCsrfToken hook implemented');
console.log('• Quotes page - useCsrfToken hook implemented');
console.log('• Invoices page - useCsrfToken hook implemented');
console.log('• Posts page - fetchWithCsrf implemented');
console.log('• Roles page - fetchWithCsrf implemented');

console.log('\n❌ POTENTIAL CSRF ISSUES FOUND:');
console.log('• TeamAdmin component - /api/upload POST without CSRF');
console.log('• TeamAdmin component - /api/team POST/PUT/DELETE without CSRF');

issues.csrf.push({
  file: 'src/components/admin/TeamAdmin.tsx',
  lines: ['93: /api/upload POST', '101: /api/team POST', '107: /api/team PUT', '117: /api/team DELETE'],
  severity: 'HIGH',
  description: 'Team admin operations missing CSRF protection'
});

console.log('\n2. 📊 API RESPONSE STRUCTURE ISSUES:');
console.log('====================================');

console.log('\n✅ ALREADY FIXED:');
console.log('• Receipts creation - Fixed response.data extraction');

console.log('\n❌ POTENTIAL API RESPONSE ISSUES:');
console.log('• Quotes navigation - May have similar response structure issue');
console.log('• Invoices navigation - Mixed response handling patterns');
console.log('• Leads navigation - Potential response extraction issue');

issues.apiResponse.push({
  file: 'src/app/admin/quotes/new/page.tsx',
  line: '249: router.push(`/admin/quotes/${quote.id}`)',
  severity: 'MEDIUM',
  description: 'Quote navigation may have response extraction issue like receipts'
});

issues.apiResponse.push({
  file: 'src/app/admin/invoices/new/page.tsx',
  line: '410: router.push(`/admin/invoices/${invoice.id}`)',
  severity: 'MEDIUM',
  description: 'Invoice navigation inconsistent - line 181 uses data.data.id, line 410 uses invoice.id'
});

issues.apiResponse.push({
  file: 'src/app/admin/leads/new/page.tsx',
  line: '140: router.push(`/admin/leads/${lead.id}`)',
  severity: 'MEDIUM',
  description: 'Lead navigation may need response structure check'
});

console.log('\n3. 🔄 NAVIGATION INCONSISTENCIES:');
console.log('=================================');

console.log('\n❌ INCONSISTENT PATTERNS FOUND:');
issues.navigation.push({
  description: 'Invoice creation has two different response handling patterns',
  files: ['src/app/admin/invoices/new/page.tsx'],
  details: 'Line 181: data.data.id vs Line 410: invoice.id - suggests API inconsistency'
});

issues.navigation.push({
  description: 'Multiple components handle receipt navigation differently',
  files: [
    'src/app/admin/receipts/new/page.tsx',
    'src/components/admin/QuickReceiptForm.tsx',
    'src/components/admin/QuickReceiptGenerator.tsx'
  ],
  details: 'Should all use consistent response extraction pattern'
});

console.log('\n4. 🛡️  SECURITY & AUTHENTICATION:');
console.log('==================================');

console.log('\n❌ POTENTIAL SECURITY ISSUES:');
issues.security.push({
  file: 'src/components/admin/TeamAdmin.tsx',
  issue: 'File upload without CSRF protection',
  endpoint: '/api/upload',
  severity: 'HIGH',
  description: 'File uploads are particularly sensitive and need CSRF protection'
});

issues.security.push({
  description: 'Team management operations without CSRF',
  endpoints: ['/api/team POST', '/api/team PUT', '/api/team DELETE'],
  severity: 'HIGH'
});

console.log('\n5. 📊 API ENDPOINT INCONSISTENCIES:');
console.log('===================================');

console.log('\n❌ RESPONSE FORMAT INCONSISTENCIES:');
issues.consistency.push({
  description: 'Mixed API response formats across endpoints',
  examples: [
    'Receipts: { success: true, data: {...} }',
    'Some endpoints: Direct object return',
    'Others: Wrapped in success/data structure'
  ],
  impact: 'Frontend parsing confusion and navigation errors'
});

console.log('\n🚨 PRIORITY ISSUES TO FIX:');
console.log('==========================');

console.log('\n🔥 HIGH PRIORITY:');
console.log('1. TeamAdmin CSRF Protection - File uploads are security critical');
console.log('2. Team management CSRF - User data modification needs protection');

console.log('\n⚠️  MEDIUM PRIORITY:');
console.log('3. API response structure standardization');
console.log('4. Navigation pattern consistency');
console.log('5. Quote/Invoice/Lead response extraction verification');

console.log('\n📋 DETAILED ANALYSIS:');
console.log('=====================');

console.log('\n🔍 CSRF Issues Analysis:');
console.log('TeamAdmin component missing CSRF protection:');
console.log('• File upload to /api/upload (line 93)');
console.log('• Team member creation (line 101)');
console.log('• Team member updates (line 107)');
console.log('• Team member deletion (line 117)');
console.log('');
console.log('💡 Fix: Add useCsrfToken hook like other admin pages');

console.log('\n🔍 API Response Issues Analysis:');
console.log('Inconsistent response handling in invoices:');
console.log('• Line 181: const data = await response.json(); router.push(`/admin/invoices/${data.data.id}`)');
console.log('• Line 410: const invoice = result.data || result; router.push(`/admin/invoices/${invoice.id}`)');
console.log('');
console.log('💡 Fix: Standardize to use consistent extraction pattern');

console.log('\n🔍 Navigation Pattern Analysis:');
console.log('Receipt navigation patterns across components:');
console.log('• receipts/new/page.tsx: Fixed with response.data extraction');
console.log('• QuickReceiptForm.tsx: May need same fix');
console.log('• QuickReceiptGenerator.tsx: May need same fix');
console.log('');
console.log('💡 Fix: Apply same response extraction pattern');

console.log('\n🛠️  RECOMMENDED FIXES:');
console.log('======================');

console.log('\n1. 🔒 Fix TeamAdmin CSRF:');
console.log('   • Import useCsrfToken hook');
console.log('   • Add CSRF headers to all POST/PUT/DELETE requests');
console.log('   • Include credentials in fetch requests');

console.log('\n2. 📊 Standardize API Responses:');
console.log('   • Audit all admin API endpoints');
console.log('   • Ensure consistent { success: true, data: {...} } format');
console.log('   • Update frontend extraction accordingly');

console.log('\n3. 🔄 Fix Navigation Inconsistencies:');
console.log('   • Review all router.push() calls after API responses');
console.log('   • Apply consistent response.data extraction pattern');
console.log('   • Add validation for response structure');

console.log('\n4. 🧪 Testing Recommendations:');
console.log('   • Test all admin form submissions after CSRF fix');
console.log('   • Verify navigation works after API response fixes');
console.log('   • Check browser network tab for proper CSRF headers');

console.log('\n📊 SUMMARY:');
console.log('===========');
console.log(`🔒 CSRF Issues: ${issues.csrf.length} found`);
console.log(`📊 API Response Issues: ${issues.apiResponse.length} found`);
console.log(`🔄 Navigation Issues: ${issues.navigation.length} found`);
console.log(`🛡️  Security Issues: ${issues.security.length} found`);
console.log(`📋 Consistency Issues: ${issues.consistency.length} found`);

console.log('\n🎯 NEXT ACTIONS:');
console.log('================');
console.log('1. Fix TeamAdmin CSRF protection (HIGH PRIORITY)');
console.log('2. Check and fix Quote/Invoice/Lead navigation patterns');
console.log('3. Standardize API response formats across all endpoints');
console.log('4. Test all admin functionality after fixes');

module.exports = { issues }; 