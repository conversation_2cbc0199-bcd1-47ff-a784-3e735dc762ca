#!/usr/bin/env node

/**
 * Fix Website Portfolio Images
 * This script fixes the website portfolio by:
 * 1. Removing entries with non-existent images
 * 2. Using actual S3 images that exist
 * 3. Creating proper portfolio entries with real images
 */

require('dotenv').config();
const { PrismaClient } = require('@prisma/client');
const { S3Client, ListObjectsV2Command } = require('@aws-sdk/client-s3');

const prisma = new PrismaClient();

// S3 client setup
const s3Client = new S3Client({
  region: process.env.NEXT_PUBLIC_S3_REGION || 'fr-par-1',
  endpoint: process.env.NEXT_PUBLIC_S3_ENDPOINT || 'https://fr-par-1.linodeobjects.com',
  credentials: {
    accessKeyId: process.env.NEXT_PUBLIC_S3_ACCESS_KEY || '',
    secretAccessKey: process.env.NEXT_PUBLIC_S3_SECRET_KEY || '',
  },
  forcePathStyle: true
});

const bucketName = process.env.NEXT_PUBLIC_S3_BUCKET || 'mocky2';

// Real client websites with actual existing images
const realWebsitePortfolio = [
  {
    title: 'Homestore Kenya',
    description: 'Premium online store for household items, kitchen accessories, home decor, and electronics. Modern e-commerce platform with comprehensive product catalog and seamless shopping experience.',
    url: 'https://homestore.co.ke/',
    category: 'e-commerce',
    featured: true,
    // We'll map this to existing S3 images
    fallbackImage: 'images/portfolio/websites/10.jpg' // Known to exist
  },
  {
    title: 'Knowledge Spring Institute',
    description: 'Private institution providing technical, vocational, and entrepreneurial training. Accredited by TVETA with multiple schools including Health Sciences, Business Studies, and Information Technology.',
    url: 'https://knowledgespringinstitute.ac.ke/',
    category: 'education',
    featured: true,
    fallbackImage: 'images/portfolio/branding/1.jpg'
  },
  {
    title: 'SGTC Company',
    description: 'Leading provider of facility management, construction, logistics, and waste management services. Partners with UN organizations and delivers excellence in infrastructure development.',
    url: 'https://sgtccompany.com/',
    category: 'corporate',
    featured: true,
    fallbackImage: 'images/portfolio/branding/2.jpg'
  },
  {
    title: 'Reucher Africa Kenya Ltd',
    description: 'Industrial chemicals and water treatment solutions provider. Comprehensive range of industrial chemicals, water treatment systems, and environmental solutions.',
    url: 'https://reucherafrica.co.ke/',
    category: 'corporate',
    featured: false,
    fallbackImage: 'images/portfolio/branding/3.jpg'
  },
  {
    title: 'Top23 Security Services',
    description: 'Professional security services company providing comprehensive security solutions including personnel, systems, and consulting services for businesses and organizations.',
    url: 'https://top23security.com/',
    category: 'security',
    featured: false,
    fallbackImage: 'images/portfolio/branding/4.jpg'
  }
];

async function getAvailableS3Images() {
  try {
    console.log('📂 Fetching available images from S3...');
    
    const command = new ListObjectsV2Command({
      Bucket: bucketName,
      Prefix: 'images/portfolio/',
      MaxKeys: 100
    });

    const response = await s3Client.send(command);
    
    if (response.Contents) {
      const images = response.Contents
        .filter(item => {
          if (!item.Key) return false;
          const ext = item.Key.split('.').pop()?.toLowerCase();
          return ['jpg', 'jpeg', 'png', 'webp', 'gif'].includes(ext || '');
        })
        .map(item => item.Key)
        .filter(Boolean);

      console.log(`✅ Found ${images.length} available images`);
      return images;
    }
    
    return [];
  } catch (error) {
    console.error('❌ Error fetching S3 images:', error);
    return [];
  }
}

async function cleanupExistingEntries() {
  try {
    console.log('🧹 Cleaning up existing website portfolio entries...');
    
    // Get all existing entries
    const existingEntries = await prisma.websitePortfolio.findMany();
    console.log(`Found ${existingEntries.length} existing entries`);
    
    // Soft delete all existing entries by setting deletedAt
    const result = await prisma.websitePortfolio.updateMany({
      where: {
        deletedAt: null
      },
      data: {
        deletedAt: new Date(),
        updatedBy: 'system-cleanup'
      }
    });
    
    console.log(`✅ Soft deleted ${result.count} existing entries`);
    
  } catch (error) {
    console.error('❌ Error cleaning up entries:', error);
    throw error;
  }
}

async function createNewPortfolioEntries(availableImages) {
  try {
    console.log('📝 Creating new website portfolio entries...');
    
    for (let i = 0; i < realWebsitePortfolio.length; i++) {
      const website = realWebsitePortfolio[i];
      
      // Use available images in order, or fallback
      const imageKey = availableImages[i] || website.fallbackImage;
      const imageUrl = `${process.env.NEXT_PUBLIC_S3_ENDPOINT}/${bucketName}/${imageKey}`;
      
      try {
        const newEntry = await prisma.websitePortfolio.create({
          data: {
            title: website.title,
            description: website.description,
            projectUrl: website.url,
            url: website.url, // Keep both for compatibility
            category: website.category,
            featured: website.featured,
            imageKey: imageKey,
            imageUrl: imageUrl,
            createdBy: 'system-fix',
            updatedBy: 'system-fix'
          }
        });
        
        console.log(`✅ Created: ${website.title} (${newEntry.id})`);
        console.log(`   Image: ${imageKey}`);
        console.log(`   URL: ${website.url}`);
        console.log('');
        
      } catch (error) {
        console.error(`❌ Error creating entry for ${website.title}:`, error);
      }
    }
    
  } catch (error) {
    console.error('❌ Error creating portfolio entries:', error);
    throw error;
  }
}

async function main() {
  try {
    console.log('🚀 Starting Website Portfolio Fix...');
    console.log('='.repeat(50));
    
    // Step 1: Get available S3 images
    const availableImages = await getAvailableS3Images();
    
    if (availableImages.length === 0) {
      console.log('⚠️  No images found in S3. Please upload some portfolio images first.');
      return;
    }
    
    // Step 2: Clean up existing entries
    await cleanupExistingEntries();
    
    // Step 3: Create new entries with real images
    await createNewPortfolioEntries(availableImages);
    
    console.log('='.repeat(50));
    console.log('🎉 Website Portfolio Fix Completed Successfully!');
    console.log('');
    console.log('📊 Summary:');
    console.log(`   - Available S3 images: ${availableImages.length}`);
    console.log(`   - New portfolio entries: ${realWebsitePortfolio.length}`);
    console.log('');
    console.log('💡 Next Steps:');
    console.log('   1. Visit /admin/website-portfolio to verify the entries');
    console.log('   2. Edit entries to use more specific images if needed');
    console.log('   3. Upload more portfolio screenshots to S3 for better visuals');
    
  } catch (error) {
    console.error('❌ Fix failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the fix
main().catch(console.error); 