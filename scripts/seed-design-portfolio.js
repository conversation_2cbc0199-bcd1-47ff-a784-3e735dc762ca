const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function seedDesignPortfolio() {
  try {
    console.log('🌱 Seeding design portfolio items...');
    const items = [
      {
        title: 'Modern Logo for Tech Startup',
        description: 'A sleek, modern logo designed for a technology startup.',
        category: 'logos',
        imageSrc: 'https://fr-par-1.linodeobjects.com/mocky2/portfolio/design/logo-tech-startup.jpg',
        featured: true,
        url: '#',
      },
      {
        title: 'Business Card for Consulting Firm',
        description: 'Professional business card design for a consulting firm.',
        category: 'cards',
        imageSrc: 'https://fr-par-1.linodeobjects.com/mocky2/portfolio/design/business-card-consulting.jpg',
        featured: false,
        url: '#',
      },
      {
        title: 'Branding Package for Coffee Shop',
        description: 'Complete branding package including logo, menu, and signage.',
        category: 'branding',
        imageSrc: 'https://fr-par-1.linodeobjects.com/mocky2/portfolio/design/branding-coffee-shop.jpg',
        featured: true,
        url: '#',
      },
      {
        title: 'Promotional Flier for Event',
        description: 'Eye-catching flier for a local music event.',
        category: 'fliers',
        imageSrc: 'https://fr-par-1.linodeobjects.com/mocky2/portfolio/design/flier-music-event.jpg',
        featured: false,
        url: '#',
      },
      {
        title: 'Corporate Letterhead Design',
        description: 'Clean and professional letterhead for a corporate client.',
        category: 'letterheads',
        imageSrc: 'https://fr-par-1.linodeobjects.com/mocky2/portfolio/design/letterhead-corporate.jpg',
        featured: false,
        url: '#',
      },
      {
        title: 'Company Profile Brochure',
        description: 'A multi-page company profile brochure for a business.',
        category: 'profiles',
        imageSrc: 'https://fr-par-1.linodeobjects.com/mocky2/portfolio/design/profile-brochure.jpg',
        featured: false,
        url: '#',
      },
    ];

    for (const item of items) {
      await prisma.websitePortfolio.create({
        data: item,
      });
      console.log(`✅ Seeded: ${item.title}`);
    }

    console.log('🎉 Design portfolio seeding complete!');
  } catch (error) {
    console.error('❌ Error seeding design portfolio:', error);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  seedDesignPortfolio();
} 