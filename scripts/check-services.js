#!/usr/bin/env node

const { PrismaClient } = require('@prisma/client');

async function checkServices() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🔍 Checking Services in Database\n');
    
    const services = await prisma.service.findMany({
      select: {
        id: true,
        name: true,
        category: true,
        price: true,
        createdAt: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    });
    
    console.log(`📊 Total Services Found: ${services.length}\n`);
    
    if (services.length === 0) {
      console.log('❌ No services found in database');
      return;
    }
    
    console.log('📋 Services List:');
    console.log('================');
    
    services.forEach((service, index) => {
      console.log(`\n${index + 1}. ${service.name}`);
      console.log(`   ID: ${service.id}`);
      console.log(`   Category: ${service.category}`);
      console.log(`   Price: KES ${service.price.toLocaleString()}`);
      console.log(`   Created: ${service.createdAt.toISOString().split('T')[0]}`);
    });
    
    console.log('\n✅ Service check complete');
    
    // Check for the specific problematic service ID
    const problematicId = 'ec86fa06-909c-4f67-bfbd-2380771db50d';
    const problematicService = await prisma.service.findUnique({
      where: { id: problematicId }
    });
    
    console.log(`\n🔍 Checking problematic service ID: ${problematicId}`);
    if (problematicService) {
      console.log(`✅ Service exists: ${problematicService.name}`);
    } else {
      console.log('❌ Service with this ID does not exist');
      console.log('💡 This explains the "Service not found" error');
    }
    
  } catch (error) {
    console.error('❌ Error checking services:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkServices(); 