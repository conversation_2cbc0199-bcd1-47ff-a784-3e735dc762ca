#!/usr/bin/env node

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function updateCatalogueBranding() {
  console.log('🎨 Updating Catalogue Branding: Sawaprint → Mocky Digital\n');
  
  try {
    // First, let's see what needs to be updated
    const itemsWithSawaprint = await prisma.catalogue.findMany({
      where: {
        OR: [
          { description: { contains: 'Sawaprint', mode: 'insensitive' } },
          { description: { contains: 'sawaprint.com', mode: 'insensitive' } }
        ]
      },
      select: {
        id: true,
        service: true,
        description: true
      }
    });

    console.log(`📋 Found ${itemsWithSawaprint.length} items that need branding updates:`);
    console.log('='.repeat(80));

    if (itemsWithSawaprint.length === 0) {
      console.log('✅ No items found with Sawaprint branding. All good!');
      return;
    }

    // Show what will be updated
    itemsWithSawaprint.forEach((item, index) => {
      console.log(`${index + 1}. ${item.service} (ID: ${item.id})`);
      console.log(`   Current: ${item.description?.substring(0, 100)}...`);
      console.log('   ---');
    });

    console.log('\n🔄 Starting branding updates...\n');

    let updateCount = 0;
    let errorCount = 0;

    // Update each item
    for (const item of itemsWithSawaprint) {
      try {
        // Replace all variations of Sawaprint branding
        let updatedDescription = item.description || '';
        
        // Replace various forms of Sawaprint references
        updatedDescription = updatedDescription
          .replace(/At Sawaprint\.com,/gi, 'At Mocky Digital,')
          .replace(/Sawaprint\.com/gi, 'Mocky Digital')
          .replace(/Sawaprint/gi, 'Mocky Digital')
          .replace(/sawaprint\.com/gi, 'Mocky Digital')
          .replace(/sawaprint/gi, 'Mocky Digital');

        // Update the item in the database
        const updatedItem = await prisma.catalogue.update({
          where: { id: item.id },
          data: { description: updatedDescription }
        });

        console.log(`✅ Updated: ${item.service}`);
        console.log(`   New: ${updatedDescription.substring(0, 100)}...`);
        console.log('');
        updateCount++;

      } catch (error) {
        console.error(`❌ Error updating ${item.service}:`, error.message);
        errorCount++;
      }
    }

    console.log('='.repeat(80));
    console.log('🎉 BRANDING UPDATE COMPLETE!');
    console.log(`✅ Successfully updated: ${updateCount} items`);
    console.log(`❌ Errors: ${errorCount} items`);

    if (updateCount > 0) {
      console.log('\n📊 UPDATED BRANDING SUMMARY:');
      console.log('• "At Sawaprint.com," → "At Mocky Digital,"');
      console.log('• "Sawaprint.com" → "Mocky Digital"');
      console.log('• "Sawaprint" → "Mocky Digital"');
      
      console.log('\n🔍 Verifying updates...');
      
      // Verify no Sawaprint references remain
      const remainingRefs = await prisma.catalogue.findMany({
        where: {
          OR: [
            { description: { contains: 'Sawaprint', mode: 'insensitive' } },
            { description: { contains: 'sawaprint.com', mode: 'insensitive' } }
          ]
        },
        select: { id: true, service: true }
      });

      if (remainingRefs.length === 0) {
        console.log('✅ Perfect! No Sawaprint references remain in the catalogue.');
      } else {
        console.log(`⚠️  Warning: ${remainingRefs.length} items still contain Sawaprint references:`);
        remainingRefs.forEach(item => {
          console.log(`   • ${item.service} (ID: ${item.id})`);
        });
      }
    }

  } catch (error) {
    console.error('❌ Error during branding update:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Function to show the updated catalogue
async function showUpdatedCatalogue() {
  console.log('\n📋 UPDATED CATALOGUE PREVIEW:');
  console.log('='.repeat(80));

  try {
    const allItems = await prisma.catalogue.findMany({
      where: { category: 'Banners' },
      select: {
        id: true,
        service: true,
        description: true,
        price: true,
        popular: true
      },
      orderBy: { price: 'desc' }
    });

    allItems.forEach((item, index) => {
      const popularMark = item.popular ? '⭐' : '  ';
      console.log(`${popularMark} ${index + 1}. ${item.service}`);
      console.log(`     💰 KES ${item.price.toLocaleString()}`);
      console.log(`     📝 ${item.description?.substring(0, 120)}...`);
      console.log('     ---');
    });

    console.log(`\n✨ All ${allItems.length} banner products now feature Mocky Digital branding!`);

  } catch (error) {
    console.error('❌ Error showing updated catalogue:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Main function
async function main() {
  console.log('🎯 CATALOGUE BRANDING UPDATE TOOL\n');
  
  await updateCatalogueBranding();
  await showUpdatedCatalogue();
  
  console.log('\n🚀 Your catalogue now features consistent Mocky Digital branding!');
  console.log('💡 Next steps:');
  console.log('   • Review the updated descriptions in your admin panel');
  console.log('   • Add product images to enhance the catalogue');
  console.log('   • Consider adding more product categories');
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { updateCatalogueBranding, showUpdatedCatalogue }; 