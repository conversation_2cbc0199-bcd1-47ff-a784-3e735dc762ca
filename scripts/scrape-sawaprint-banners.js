#!/usr/bin/env node

/**
 * Script to scrape Sawaprint banner data and seed into database
 * Based on: https://www.sawaprint.com/banners
 * Run with: node scripts/scrape-sawaprint-banners.js
 */

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

// Banner data scraped from Sawaprint website
const sawarintBanners = [
  {
    service: 'Broad-base Roll-up Banner',
    description: 'Professional broad-base roll-up banner with stable base for indoor events and marketing displays',
    price: 7500, // KSH converted from £7,500.00 (assuming 1 GBP = 1 KSH for local pricing)
    designFee: 2000,
    category: 'Banners',
    features: ['Broad Stable Base', 'Roll-up Mechanism', 'Portable Design', 'Professional Display'],
    popular: true,
    maxQuantity: 100,
    minQuantity: 1,
    pricingType: 'fixed',
    unitId: null // Will be set to piece unit
  },
  {
    service: 'Telescopic Banners',
    description: 'Adjustable telescopic banners with extendable height for versatile indoor and outdoor displays',
    price: 19000,
    designFee: 3000,
    category: 'Banners',
    features: ['Telescopic Height Adjustment', 'Durable Construction', 'Weather Resistant', 'Easy Setup'],
    popular: false,
    maxQuantity: 50,
    minQuantity: 1,
    pricingType: 'fixed',
    unitId: null
  },
  {
    service: 'Adjustable Backdrop Media Banners (3M × 2M)',
    description: 'Large format adjustable backdrop banners perfect for events, exhibitions and media walls',
    price: 18500,
    designFee: 4000,
    category: 'Banners',
    features: ['3M × 2M Size', 'Adjustable Framework', 'Media Wall Ready', 'Professional Finish'],
    popular: true,
    maxQuantity: 25,
    minQuantity: 1,
    pricingType: 'fixed',
    unitId: null
  },
  {
    service: 'Custom Door Frame Banners',
    description: 'Custom door frame banners designed to fit standard doorways for maximum visibility',
    price: 7300,
    designFee: 2500,
    category: 'Banners',
    features: ['Door Frame Fit', 'Custom Design', 'High Visibility', 'Easy Installation'],
    popular: false,
    maxQuantity: 75,
    minQuantity: 1,
    pricingType: 'fixed',
    unitId: null
  },
  {
    service: 'Broad Base Media Banner (2M × 2M)',
    description: 'Square format media banner with broad base for stable outdoor and indoor displays',
    price: 15600,
    designFee: 3500,
    category: 'Banners',
    features: ['2M × 2M Square Format', 'Broad Stable Base', 'Media Display Ready', 'Professional Quality'],
    popular: true,
    maxQuantity: 30,
    minQuantity: 1,
    pricingType: 'fixed',
    unitId: null
  },
  {
    service: 'Collapsible Backdrop Stand & Media Banners',
    description: 'Collapsible backdrop stand system with media banners for portable professional displays',
    price: 17800,
    designFee: 4000,
    category: 'Banners',
    features: ['Collapsible Design', 'Portable Setup', 'Professional Backdrop', 'Media Display System'],
    popular: false,
    maxQuantity: 20,
    minQuantity: 1,
    pricingType: 'fixed',
    unitId: null
  },
  {
    service: 'Narrow Base Roll-Up Banners',
    description: 'Compact narrow base roll-up banners ideal for small spaces and tabletop displays',
    price: 5800,
    designFee: 1800,
    category: 'Banners',
    features: ['Narrow Base Design', 'Space Efficient', 'Roll-up Mechanism', 'Tabletop Ready'],
    popular: true,
    maxQuantity: 150,
    minQuantity: 1,
    pricingType: 'fixed',
    unitId: null
  },
  {
    service: 'X-Banner Stands',
    description: 'Lightweight X-banner stands with adjustable frame for quick setup and portability',
    price: 5400,
    designFee: 1500,
    category: 'Banners',
    features: ['X-Frame Design', 'Lightweight', 'Quick Setup', 'Adjustable Size'],
    popular: true,
    maxQuantity: 200,
    minQuantity: 1,
    pricingType: 'fixed',
    unitId: null
  },
  {
    service: 'Teardrop Banner',
    description: 'Eye-catching teardrop shaped banners with unique curved design for outdoor advertising',
    price: 12500,
    designFee: 3000,
    category: 'Banners',
    features: ['Teardrop Shape', 'Wind Resistant', 'Outdoor Durable', 'Eye-catching Design'],
    popular: true,
    maxQuantity: 100,
    minQuantity: 1,
    pricingType: 'fixed',
    unitId: null
  }
];

async function scrapeSawaprintBanners() {
  try {
    console.log('🎯 Starting Sawaprint banners scraping and seeding...');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    console.log('📍 Source: https://www.sawaprint.com/banners');
    console.log('');

    // Get the piece unit for banners
    const pieceUnit = await prisma.unit.findUnique({
      where: { name: 'piece' }
    });

    if (!pieceUnit) {
      console.log('⚠️  Piece unit not found. Creating it...');
      const newUnit = await prisma.unit.create({
        data: {
          name: 'piece',
          displayName: 'Piece',
          plural: 'Pieces',
          shortForm: 'pcs',
          category: 'quantity',
          active: true,
          order: 1
        }
      });
      console.log('✅ Created piece unit');
    }

    const unitId = pieceUnit?.id || (await prisma.unit.findUnique({ where: { name: 'piece' } })).id;

    let createdCount = 0;
    let skippedCount = 0;
    let updatedCount = 0;

    for (const bannerData of sawarintBanners) {
      // Set the unit ID
      bannerData.unitId = unitId;

      // Check if banner already exists
      const existingBanner = await prisma.catalogue.findFirst({
        where: { service: bannerData.service }
      });

      if (existingBanner) {
        // Update existing banner with new data
        await prisma.catalogue.update({
          where: { id: existingBanner.id },
          data: {
            ...bannerData,
            updatedAt: new Date()
          }
        });
        console.log(`🔄 Updated: "${bannerData.service}" - KSH ${bannerData.price.toLocaleString()}`);
        updatedCount++;
      } else {
        // Create new banner
        const newBanner = await prisma.catalogue.create({
          data: bannerData
        });
        console.log(`✅ Created: "${newBanner.service}" - KSH ${bannerData.price.toLocaleString()}`);
        createdCount++;
      }
    }

    console.log('');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    console.log('🎉 Sawaprint banners seeding completed!');
    console.log(`📊 Summary:`);
    console.log(`   ✅ Created: ${createdCount} banners`);
    console.log(`   🔄 Updated: ${updatedCount} banners`);
    console.log(`   ⏭️  Skipped: ${skippedCount} banners`);

    // Display all banner categories in database
    const bannerCategories = await prisma.catalogue.findMany({
      where: { category: 'Banners' },
      orderBy: { price: 'asc' }
    });

    console.log(`   📝 Total banners in database: ${bannerCategories.length}`);
    console.log('');
    console.log('📋 All Banner Products:');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    
    bannerCategories.forEach((banner, index) => {
      const price = typeof banner.price === 'number' ? banner.price : parseFloat(banner.price) || 0;
      console.log(`${(index + 1).toString().padStart(2, ' ')}. ${banner.service}`);
      console.log(`    Price: KSH ${price.toLocaleString()}`);
      console.log(`    Category: ${banner.category}`);
      console.log(`    Popular: ${banner.popular ? '⭐ Yes' : 'No'}`);
      if (banner.features && banner.features.length > 0) {
        console.log(`    Features: ${banner.features.slice(0, 2).join(', ')}${banner.features.length > 2 ? '...' : ''}`);
      }
      console.log('');
    });

    console.log('🚀 Banner catalogue is now ready!');
    console.log('💡 You can view them at: /admin/catalogue');
    console.log('🌐 Public catalogue: /catalogue');
    console.log('');
    console.log('📈 Price Range Summary:');
    const prices = bannerCategories.map(b => typeof b.price === 'number' ? b.price : parseFloat(b.price) || 0);
      const minPrice = Math.min(...prices);
      const maxPrice = Math.max(...prices);
    const avgPrice = prices.reduce((a, b) => a + b, 0) / prices.length;
    
    console.log(`   💰 Lowest Price: KSH ${minPrice.toLocaleString()}`);
    console.log(`   💎 Highest Price: KSH ${maxPrice.toLocaleString()}`);
    console.log(`   📊 Average Price: KSH ${Math.round(avgPrice).toLocaleString()}`);

  } catch (error) {
    console.error('❌ Error scraping and seeding Sawaprint banners:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Handle script execution
if (require.main === module) {
  scrapeSawaprintBanners()
    .then(() => {
      console.log('\n✨ Sawaprint banner scraping completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Sawaprint banner scraping failed:', error);
      process.exit(1);
    });
}

module.exports = { scrapeSawaprintBanners, sawarintBanners }; 