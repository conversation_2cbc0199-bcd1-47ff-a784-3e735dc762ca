const { execSync } = require('child_process');
const fs = require('fs');

try {
  console.log('🔍 CHECKING RECENT GIT CHANGES THAT MIGHT AFFECT AUTH\n');
  
  // Get recent commits
  console.log('📝 Recent commits:');
  console.log('─'.repeat(50));
  try {
    const commits = execSync('git log --oneline -10', { encoding: 'utf8' });
    console.log(commits);
  } catch (error) {
    console.log('❌ Could not fetch git commits:', error.message);
  }
  
  // Check for recent changes in auth-related files
  console.log('\n🔧 Auth-related files modified recently:');
  console.log('─'.repeat(50));
  
  const authFiles = [
    'src/app/api/auth/[...nextauth]/route.ts',
    'src/utils/passwordUtils.ts', 
    'src/middleware.ts',
    'src/middleware/adminAuth.ts',
    'src/app/admin/login/page.tsx',
    'src/utils/auth.ts',
    'src/utils/apiAuth.ts',
    'package.json',
    'next.config.js'
  ];
  
  authFiles.forEach(file => {
    try {
      const stat = fs.statSync(file);
      const modifiedTime = stat.mtime;
      const now = new Date();
      const hoursDiff = (now - modifiedTime) / (1000 * 60 * 60);
      
      if (hoursDiff < 48) { // Modified in last 48 hours
        console.log(`⚠️  ${file} - Modified ${hoursDiff.toFixed(1)} hours ago`);
      } else {
        console.log(`✅ ${file} - Last modified ${Math.floor(hoursDiff/24)} days ago`);
      }
    } catch (error) {
      console.log(`❌ ${file} - Could not check: ${error.message}`);
    }
  });
  
  // Check for any changes in the last pull
  console.log('\n🔄 Changes from last git pull:');
  console.log('─'.repeat(50));
  try {
    const diff = execSync('git diff HEAD~1 HEAD --name-only', { encoding: 'utf8' });
    const changedFiles = diff.trim().split('\n').filter(f => f);
    
    if (changedFiles.length === 0) {
      console.log('ℹ️  No files changed in last commit');
    } else {
      changedFiles.forEach(file => {
        console.log(`📁 ${file}`);
        
        // Check if it's an auth-related file
        const isAuthRelated = authFiles.some(authFile => file.includes(authFile) || authFile.includes(file));
        if (isAuthRelated) {
          console.log(`   🚨 AUTH-RELATED FILE CHANGED!`);
          
          // Show the actual changes
          try {
            const fileDiff = execSync(`git diff HEAD~1 HEAD -- "${file}"`, { encoding: 'utf8' });
            if (fileDiff) {
              console.log(`   📋 Changes in ${file}:`);
              console.log('   ' + fileDiff.split('\n').slice(0, 20).join('\n   '));
              if (fileDiff.split('\n').length > 20) {
                console.log('   ... (truncated)');
              }
            }
          } catch (diffError) {
            console.log(`   ❌ Could not show diff: ${diffError.message}`);
          }
        }
      });
    }
  } catch (error) {
    console.log('❌ Could not fetch diff:', error.message);
  }
  
  // Check environment variables that might affect auth
  console.log('\n🌍 Environment variables check:');
  console.log('─'.repeat(50));
  const authEnvVars = [
    'NEXTAUTH_SECRET',
    'NEXTAUTH_URL', 
    'DATABASE_URL',
    'ADMIN_USERNAME',
    'ADMIN_PASSWORD',
    'ADMIN_EMAIL'
  ];
  
  authEnvVars.forEach(envVar => {
    const value = process.env[envVar];
    if (value) {
      console.log(`✅ ${envVar}: [SET]`);
    } else {
      console.log(`❌ ${envVar}: NOT SET`);
    }
  });
  
  console.log('\n═'.repeat(50));
  console.log('✅ GIT CHANGE CHECK COMPLETE');
  
} catch (error) {
  console.error('❌ Error checking git changes:', error);
} 