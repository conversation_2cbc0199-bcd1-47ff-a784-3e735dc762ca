require('dotenv').config();
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function forceLogout() {
  console.log('🔒 Force logging out all users...\n');
  
  try {
    // 1. Update all users' lastLogin to null and set active to false
    const updatedUsers = await prisma.$executeRaw`
      UPDATE users
      SET "lastLogin" = NULL;
    `;
    console.log('✅ Reset all users\' login status');
    
    // 2. Generate new JWT secret suggestion
    const crypto = require('crypto');
    const newSecret = crypto.randomBytes(32).toString('base64');
    
    console.log('\n⚠️  IMPORTANT: To invalidate all existing JWT tokens:');
    console.log('1. Update your .env file with this new secret:');
    console.log('NEXTAUTH_SECRET=' + newSecret);
    console.log('JWT_SECRET=' + newSecret);
    console.log('\n2. Restart your application\n');
    
    console.log('🎉 Force logout complete!');
    console.log('All users will need to log in again.');
    
  } catch (error) {
    console.error('❌ Error during force logout:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

forceLogout().catch(console.error); 