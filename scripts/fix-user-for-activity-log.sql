-- Fix for Activity Log Foreign Key Constraint
-- Creates admin role and user to prevent foreign key errors

DO $$
DECLARE
    admin_role_id UUID;
    admin_user_id UUID;
BEGIN
    -- Generate UUIDs
    admin_role_id := gen_random_uuid();
    admin_user_id := gen_random_uuid();
    
    -- Create admin role if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM roles WHERE name = 'admin') THEN
        INSERT INTO roles (
            id,
            name,
            description,
            permissions,
            created_at,
            updated_at
        ) VALUES (
            admin_role_id,
            'admin',
            'System Administrator Role',
            ARRAY['*'],
            NOW(),
            NOW()
        );
        RAISE NOTICE 'Admin role created successfully';
    ELSE
        SELECT id INTO admin_role_id FROM roles WHERE name = 'admin' LIMIT 1;
        RAISE NOTICE 'Admin role already exists';
    END IF;

    -- Create admin user if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM users WHERE email = '<EMAIL>') THEN
        INSERT INTO users (
            id,
            email,
            username,
            name,
            "passwordHash",
            active,
            "roleId",
            created_at,
            updated_at
        ) VALUES (
            admin_user_id,
            '<EMAIL>',
            'admin',
            'System Administrator',
            '$2a$12$LQv3c1yqBWVHxkd0LQ1Mu.VCRVZNt3Sa8J93o3M80qeoXacjUAPza', -- bcrypt hash for 'admin123'
            true,
            admin_role_id,
            NOW(),
            NOW()
        );
        RAISE NOTICE 'Admin user created successfully';
    ELSE
        RAISE NOTICE 'Admin user already exists';
    END IF;
    
    RAISE NOTICE 'User setup completed - you can <NAME_EMAIL> / admin123 to login';
END $$; 