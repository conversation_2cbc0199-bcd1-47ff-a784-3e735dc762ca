#!/usr/bin/env node

/**
 * Complete blog seeding script - categories and sample posts
 * Run with: node scripts/seed-blog-complete.js
 */

const { seedBlogCategories } = require('./seed-blog-categories');
const { seedSampleBlogPosts } = require('./seed-sample-blog-posts');

async function seedCompleteBlogs() {
  try {
    console.log('🚀 Starting complete blog seeding process...');
    console.log('═══════════════════════════════════════════════════════════════');
    
    // Step 1: Seed blog categories
    console.log('\n📁 STEP 1: Seeding Blog Categories');
    console.log('───────────────────────────────────────────────────────────────');
    await seedBlogCategories();
    
    // Step 2: Seed sample blog posts
    console.log('\n📝 STEP 2: Seeding Sample Blog Posts');
    console.log('───────────────────────────────────────────────────────────────');
    await seedSampleBlogPosts();
    
    console.log('\n═══════════════════════════════════════════════════════════════');
    console.log('🎉 COMPLETE BLOG SEEDING FINISHED SUCCESSFULLY!');
    console.log('═══════════════════════════════════════════════════════════════');
    
    console.log('\n📊 What was created:');
    console.log('   📁 20 Blog Categories covering all service areas');
    console.log('   📝 3+ Sample Blog Posts with full content');
    console.log('   🏷️  SEO-optimized titles and descriptions');
    console.log('   🔗 Proper category associations');
    console.log('   📱 Mobile-friendly content structure');
    
    console.log('\n🔗 Access your blog content:');
    console.log('   🔧 Admin Panel: http://localhost:3000/admin/blog');
    console.log('   📂 Categories: http://localhost:3000/admin/blog/categories');
    console.log('   🌐 Public Blog: http://localhost:3000/blog');
    
    console.log('\n💡 Next steps:');
    console.log('   1. Review and customize the sample content');
    console.log('   2. Add featured images to blog posts');
    console.log('   3. Create more posts using the categories');
    console.log('   4. Set up your blog content strategy');
    
  } catch (error) {
    console.error('\n❌ Complete blog seeding failed:', error);
    throw error;
  }
}

// Handle script execution
if (require.main === module) {
  seedCompleteBlogs()
    .then(() => {
      console.log('\n✨ All done! Your blog is ready to go!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Seeding process failed:', error);
      process.exit(1);
    });
}

module.exports = { seedCompleteBlogs }; 