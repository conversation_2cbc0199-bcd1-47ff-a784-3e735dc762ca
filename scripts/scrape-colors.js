#!/usr/bin/env node

// Use ts-node to handle TypeScript imports
require('ts-node/register');
const { scrapeTrendingPalettes, generateFallbackPalettes } = require('../src/services/colorScrapingService');
const fs = require('fs').promises;
const path = require('path');

// Configuration
const CONFIG = {
  outputDir: path.join(process.cwd(), 'data'),
  outputFile: 'color-palettes.json',
  backupFile: 'color-palettes-backup.json',
  logFile: 'color-scraping.log',
  maxRetries: 3,
  retryDelay: 5000, // 5 seconds
};

/**
 * Logger utility
 */
class Logger {
  static log(level, message, data = null) {
    const timestamp = new Date().toISOString();
    const logEntry = {
      timestamp,
      level,
      message,
      data
    };

    console.log(`[${timestamp}] ${level.toUpperCase()}: ${message}`);
    if (data) {
      console.log(JSON.stringify(data, null, 2));
    }

    // Write to log file
    this.writeToLogFile(logEntry);
  }

  static async writeToLogFile(logEntry) {
    try {
      await fs.mkdir(CONFIG.outputDir, { recursive: true });
      const logPath = path.join(CONFIG.outputDir, CONFIG.logFile);
      const logLine = JSON.stringify(logEntry) + '\n';
      await fs.appendFile(logPath, logLine);
    } catch (error) {
      console.error('Failed to write to log file:', error);
    }
  }

  static info(message, data) { this.log('info', message, data); }
  static warn(message, data) { this.log('warn', message, data); }
  static error(message, data) { this.log('error', message, data); }
  static success(message, data) { this.log('success', message, data); }
}

/**
 * Sleep utility
 */
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Ensure output directory exists
 */
async function ensureOutputDir() {
  try {
    await fs.mkdir(CONFIG.outputDir, { recursive: true });
    Logger.info('Output directory ensured');
  } catch (error) {
    Logger.error('Failed to create output directory', error);
    throw error;
  }
}

/**
 * Load existing palettes for comparison
 */
async function loadExistingPalettes() {
  try {
    const filePath = path.join(CONFIG.outputDir, CONFIG.outputFile);
    const data = await fs.readFile(filePath, 'utf8');
    const parsed = JSON.parse(data);
    Logger.info(`Loaded ${parsed.palettes?.length || 0} existing palettes`);
    return parsed;
  } catch (error) {
    Logger.warn('No existing palettes file found or failed to read', error.message);
    return { palettes: [], lastUpdated: null };
  }
}

/**
 * Save palettes to file with backup
 */
async function savePalettes(data) {
  try {
    const outputPath = path.join(CONFIG.outputDir, CONFIG.outputFile);
    const backupPath = path.join(CONFIG.outputDir, CONFIG.backupFile);

    // Create backup of existing file
    try {
      await fs.copyFile(outputPath, backupPath);
      Logger.info('Created backup of existing palettes');
    } catch (error) {
      Logger.warn('No existing file to backup');
    }

    // Save new data
    await fs.writeFile(outputPath, JSON.stringify(data, null, 2));
    Logger.success(`Saved ${data.palettes.length} palettes to ${CONFIG.outputFile}`);

    return true;
  } catch (error) {
    Logger.error('Failed to save palettes', error);
    throw error;
  }
}

/**
 * Compare palettes to detect changes
 */
function comparePalettes(oldPalettes, newPalettes) {
  const oldIds = new Set(oldPalettes.map(p => p.id));
  const newIds = new Set(newPalettes.map(p => p.id));

  const added = newPalettes.filter(p => !oldIds.has(p.id));
  const removed = oldPalettes.filter(p => !newIds.has(p.id));
  const unchanged = newPalettes.filter(p => oldIds.has(p.id));

  return {
    added: added.length,
    removed: removed.length,
    unchanged: unchanged.length,
    total: newPalettes.length,
    hasChanges: added.length > 0 || removed.length > 0
  };
}

/**
 * Main scraping function with retry logic
 */
async function scrapeWithRetry() {
  let lastError = null;

  for (let attempt = 1; attempt <= CONFIG.maxRetries; attempt++) {
    try {
      Logger.info(`Scraping attempt ${attempt}/${CONFIG.maxRetries}`);

      const result = await scrapeTrendingPalettes();

      if (result.success && result.palettes.length > 0) {
        Logger.success(`Successfully scraped ${result.palettes.length} palettes on attempt ${attempt}`);
        return result;
      } else {
        throw new Error(result.error || 'No palettes returned');
      }

    } catch (error) {
      lastError = error;
      Logger.warn(`Attempt ${attempt} failed: ${error.message}`);

      if (attempt < CONFIG.maxRetries) {
        Logger.info(`Waiting ${CONFIG.retryDelay}ms before retry...`);
        await sleep(CONFIG.retryDelay);
      }
    }
  }

  throw lastError;
}

/**
 * Main execution function
 */
async function main() {
  const startTime = Date.now();
  Logger.info('Starting color palette scraping process');

  try {
    // Ensure output directory exists
    await ensureOutputDir();

    // Load existing palettes
    const existingData = await loadExistingPalettes();

    // Attempt to scrape new palettes
    let scrapingResult;
    let usedFallback = false;

    try {
      scrapingResult = await scrapeWithRetry();
    } catch (error) {
      Logger.error('All scraping attempts failed, using fallback palettes', error);
      scrapingResult = {
        success: false,
        palettes: generateFallbackPalettes(),
        error: error.message,
        timestamp: new Date()
      };
      usedFallback = true;
    }

    // Compare with existing data
    const comparison = comparePalettes(existingData.palettes || [], scrapingResult.palettes);
    Logger.info('Palette comparison', comparison);

    // Prepare data to save
    const dataToSave = {
      palettes: scrapingResult.palettes,
      lastUpdated: new Date().toISOString(),
      scrapingSuccess: scrapingResult.success,
      usedFallback: usedFallback,
      error: scrapingResult.error || null,
      stats: {
        totalPalettes: scrapingResult.palettes.length,
        ...comparison,
        executionTime: Date.now() - startTime
      }
    };

    // Save palettes
    await savePalettes(dataToSave);

    // Log final results
    const duration = Date.now() - startTime;
    Logger.success('Color palette scraping completed', {
      duration: `${duration}ms`,
      palettes: scrapingResult.palettes.length,
      changes: comparison.hasChanges,
      fallback: usedFallback
    });

    // Exit with appropriate code
    process.exit(usedFallback ? 1 : 0);

  } catch (error) {
    Logger.error('Fatal error in scraping process', error);
    process.exit(1);
  }
}

// Handle process signals
process.on('SIGINT', () => {
  Logger.info('Received SIGINT, shutting down gracefully...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  Logger.info('Received SIGTERM, shutting down gracefully...');
  process.exit(0);
});

// Run if called directly
if (require.main === module) {
  main().catch(error => {
    Logger.error('Unhandled error in main process', error);
    process.exit(1);
  });
}

module.exports = {
  main,
  scrapeWithRetry,
  comparePalettes,
  Logger
};
