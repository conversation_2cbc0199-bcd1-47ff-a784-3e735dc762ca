#!/usr/bin/env node

const { PrismaClient } = require('@prisma/client');
const puppeteer = require('puppeteer');

const prisma = new PrismaClient();

// Function to get existing banner products from database
async function getExistingBannerProducts() {
  console.log('🔍 Fetching existing banner products from database...');
  
  try {
    const bannerProducts = await prisma.catalogue.findMany({
      where: {
        OR: [
          { category: 'Banners' },
          { service: { contains: 'banner', mode: 'insensitive' } },
          { service: { contains: 'Banner', mode: 'insensitive' } }
        ]
      },
      select: {
        service: true,
        price: true,
        description: true,
        category: true,
        features: true
      },
      orderBy: {
        service: 'asc'
      }
    });

    console.log(`📋 Found ${bannerProducts.length} banner products in database:`);
    console.log('=' .repeat(80));
    
    bannerProducts.forEach((product, index) => {
      console.log(`${index + 1}. ${product.service}`);
      console.log(`   Price: KES ${product.price.toLocaleString()}`);
      console.log(`   Category: ${product.category}`);
      console.log(`   Description: ${product.description || 'No description'}`);
      console.log('   ---');
    });

    return bannerProducts;
  } catch (error) {
    console.error('❌ Error fetching banner products:', error);
    return [];
  }
}

// Function to scrape fresh banner products from Sawaprint
async function scrapeSawaprintBanners() {
  console.log('🕷️ Scraping fresh banner products from Sawaprint.com...');
  
  const browser = await puppeteer.launch({
    headless: true,
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });

  try {
    const page = await browser.newPage();
    
    // Set user agent to avoid bot detection
    await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36');
    
    // Navigate to banners page
    console.log('📄 Loading banners page...');
    await page.goto('https://www.sawaprint.com/banners', { 
      waitUntil: 'networkidle2',
      timeout: 30000 
    });

    // Wait for page to load
    await page.waitForTimeout(3000);

    // Extract all banner products
    const bannerProducts = await page.evaluate(() => {
      const products = [];
      
      // Try multiple selectors to find product containers
      const productSelectors = [
        '.product-grid .product-item',
        '.catalogue-item',
        '.banner-product',
        '[class*="product"]',
        'a[href*="banner"]',
        'div[class*="item"]'
      ];

      let productElements = [];
      
      // Try each selector until we find products
      for (const selector of productSelectors) {
        productElements = document.querySelectorAll(selector);
        if (productElements.length > 0) {
          console.log(`Found ${productElements.length} products with selector: ${selector}`);
          break;
        }
      }

      // If no specific product containers, look for links to banner products
      if (productElements.length === 0) {
        productElements = document.querySelectorAll('a[href*="/banners/p/"]');
        console.log(`Found ${productElements.length} banner product links`);
      }

      // Extract text content that might contain product info
      if (productElements.length === 0) {
        console.log('No product containers found, extracting from page text...');
        const textContent = document.body.innerText;
        
        // Look for banner-related terms and prices
        const bannerKeywords = ['banner', 'Banner', 'roll-up', 'backdrop', 'stand'];
        const pricePattern = /£[\d,]+\.?\d*/g;
        const kesPattern = /KES\s*[\d,]+/g;
        
        const prices = [...textContent.matchAll(pricePattern), ...textContent.matchAll(kesPattern)];
        
        // Basic extraction from text content
        bannerKeywords.forEach(keyword => {
          const regex = new RegExp(`[^\n]*${keyword}[^\n]*`, 'gi');
          const matches = textContent.match(regex);
          if (matches) {
            matches.forEach(match => {
              if (match.length > 10 && match.length < 200) {
                products.push({
                  service: match.trim(),
                  rawText: match,
                  extractionMethod: 'text-content'
                });
              }
            });
          }
        });
      } else {
        // Extract from product elements
        productElements.forEach((element, index) => {
          try {
            const linkElement = element.tagName === 'A' ? element : element.querySelector('a');
            const href = linkElement ? linkElement.href : '';
            
            // Extract title from various possible elements
            const titleSelectors = ['h1', 'h2', 'h3', 'h4', '.title', '.product-title', '.name'];
            let title = '';
            
            for (const selector of titleSelectors) {
              const titleEl = element.querySelector(selector);
              if (titleEl && titleEl.textContent.trim()) {
                title = titleEl.textContent.trim();
                break;
              }
            }

            // If no title found, try the link text or element text
            if (!title && linkElement) {
              title = linkElement.textContent.trim();
            }
            
            if (!title) {
              title = element.textContent.trim().split('\n')[0];
            }

            // Extract price
            const priceSelectors = ['.price', '.cost', '.amount', '[class*="price"]'];
            let price = '';
            
            for (const selector of priceSelectors) {
              const priceEl = element.querySelector(selector);
              if (priceEl && priceEl.textContent.trim()) {
                price = priceEl.textContent.trim();
                break;
              }
            }

            // Extract description
            const descSelectors = ['.description', '.desc', 'p', '.summary'];
            let description = '';
            
            for (const selector of descSelectors) {
              const descEl = element.querySelector(selector);
              if (descEl && descEl.textContent.trim() && descEl.textContent.length > 20) {
                description = descEl.textContent.trim();
                break;
              }
            }

            if (title && title.length > 3) {
              products.push({
                service: title,
                price: price,
                description: description,
                url: href,
                extractionMethod: 'dom-elements'
              });
            }
          } catch (error) {
            console.error('Error extracting product:', error);
          }
        });
      }

      return products;
    });

    console.log(`✅ Extracted ${bannerProducts.length} banner products from Sawaprint`);
    
    // Process and clean the data
    const cleanedProducts = [];
    
    bannerProducts.forEach((product, index) => {
      let cleanedPrice = null;
      let cleanedService = product.service;
      let cleanedDescription = product.description || '';

      // Extract price from text
      if (product.price) {
        const priceMatch = product.price.match(/£([\d,]+\.?\d*)/);
        const kesMatch = product.price.match(/KES\s*([\d,]+)/);
        
        if (priceMatch) {
          // Convert pounds to KES (approximate rate: 1 GBP = 160 KES)
          cleanedPrice = Math.round(parseFloat(priceMatch[1].replace(',', '')) * 160);
        } else if (kesMatch) {
          cleanedPrice = parseInt(kesMatch[1].replace(',', ''));
        }
      }

      // Try to extract price from service name if not found
      if (!cleanedPrice && cleanedService) {
        const servicePriceMatch = cleanedService.match(/£([\d,]+\.?\d*)/);
        const serviceKesMatch = cleanedService.match(/KES\s*([\d,]+)/);
        
        if (servicePriceMatch) {
          cleanedPrice = Math.round(parseFloat(servicePriceMatch[1].replace(',', '')) * 160);
          cleanedService = cleanedService.replace(/£[\d,]+\.?\d*/, '').trim();
        } else if (serviceKesMatch) {
          cleanedPrice = parseInt(serviceKesMatch[1].replace(',', ''));
          cleanedService = cleanedService.replace(/KES\s*[\d,]+/, '').trim();
        }
      }

      // Default price if none found
      if (!cleanedPrice) {
        cleanedPrice = 10000; // Default banner price
      }

      // Clean service name
      cleanedService = cleanedService
        .replace(/^\d+\.\s*/, '') // Remove leading numbers
        .replace(/\s+/g, ' ') // Normalize spaces
        .trim();

      if (cleanedService && cleanedService.length > 3) {
        cleanedProducts.push({
          service: cleanedService,
          price: cleanedPrice,
          description: cleanedDescription,
          category: 'Banners',
          extractionMethod: product.extractionMethod,
          url: product.url
        });
      }
    });

    return cleanedProducts;

  } catch (error) {
    console.error('❌ Error scraping banners:', error);
    return [];
  } finally {
    await browser.close();
  }
}

// Main function
async function main() {
  console.log('🎯 Banner Products Extraction Tool\n');

  try {
    // Get existing products from database
    const existingProducts = await getExistingBannerProducts();
    
    console.log('\n' + '='.repeat(80));
    console.log('📊 EXISTING BANNER PRODUCTS SUMMARY');
    console.log('='.repeat(80));
    
    if (existingProducts.length > 0) {
      console.log(`✅ Found ${existingProducts.length} banner products in database:`);
      
      const summary = existingProducts.map((product, index) => {
        return {
          [`${index + 1}. ${product.service}`]: `KES ${product.price.toLocaleString()}`,
          description: product.description?.substring(0, 100) + (product.description?.length > 100 ? '...' : '')
        };
      });
      
      console.table(summary);
      
      // Price range
      const prices = existingProducts.map(p => p.price);
      const minPrice = Math.min(...prices);
      const maxPrice = Math.max(...prices);
      const avgPrice = Math.round(prices.reduce((a, b) => a + b, 0) / prices.length);
      
      console.log(`\n💰 Price Range: KES ${minPrice.toLocaleString()} - KES ${maxPrice.toLocaleString()}`);
      console.log(`📈 Average Price: KES ${avgPrice.toLocaleString()}`);
    } else {
      console.log('❌ No banner products found in database');
    }

    // Scrape fresh data from Sawaprint
    console.log('\n' + '='.repeat(80));
    console.log('🔄 SCRAPING FRESH DATA FROM SAWAPRINT');
    console.log('='.repeat(80));
    
    const scrapedProducts = await scrapeSawaprintBanners();
    
    if (scrapedProducts.length > 0) {
      console.log(`✅ Successfully scraped ${scrapedProducts.length} banner products:`);
      
      scrapedProducts.forEach((product, index) => {
        console.log(`${index + 1}. ${product.service}`);
        console.log(`   Price: KES ${product.price.toLocaleString()}`);
        console.log(`   Method: ${product.extractionMethod}`);
        if (product.description) {
          console.log(`   Description: ${product.description.substring(0, 100)}${product.description.length > 100 ? '...' : ''}`);
        }
        if (product.url) {
          console.log(`   URL: ${product.url}`);
        }
        console.log('   ---');
      });

      // Save scraped data to file
      const fs = require('fs').promises;
      await fs.writeFile('./scraped-banners.json', JSON.stringify(scrapedProducts, null, 2));
      console.log('\n💾 Scraped data saved to: ./scraped-banners.json');
    } else {
      console.log('❌ No products could be scraped from Sawaprint');
    }

  } catch (error) {
    console.error('❌ Error in main function:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { getExistingBannerProducts, scrapeSawaprintBanners }; 