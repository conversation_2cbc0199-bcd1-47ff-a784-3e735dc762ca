const { execSync } = require('child_process');
const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');
const fs = require('fs');

const prisma = new PrismaClient();

async function fixAuthIssue() {
  try {
    console.log('🔧 COMPREHENSIVE AUTHENTICATION FIX\n');
    console.log('═'.repeat(60));
    
    // Step 1: Clear Next.js cache and rebuilt
    console.log('\n1️⃣ CLEARING NEXT.JS CACHE AND REBUILDING');
    console.log('─'.repeat(40));
    
    try {
      console.log('   🗑️  Removing .next directory...');
      execSync('rm -rf .next', { stdio: 'inherit' });
      
      console.log('   🗑️  Clearing node modules cache...');
      execSync('rm -rf node_modules/.cache', { stdio: 'inherit' });
      
      console.log('   🔨 Rebuilding application...');
      execSync('npm run build', { stdio: 'inherit' });
      
      console.log('✅ Cache cleared and app rebuilt');
    } catch (error) {
      console.log('⚠️  Build failed, continuing with other fixes...');
    }
    
    // Step 2: Verify and fix admin user
    console.log('\n2️⃣ VERIFYING AND FIXING ADMIN USER');
    console.log('─'.repeat(40));
    
    const adminUser = await prisma.user.findFirst({
      where: { username: 'admin' },
      include: { role: true }
    });
    
    if (!adminUser) {
      console.log('❌ Admin user not found! Creating...');
      
      // Get or create admin role
      let adminRole = await prisma.role.findFirst({ where: { name: 'admin' } });
      if (!adminRole) {
        adminRole = await prisma.role.create({
          data: {
            name: 'admin',
            description: 'Administrator with full access',
            permissions: ['*']
          }
        });
      }
      
      // Create admin user
      const passwordHash = await bcrypt.hash('Jack75522r', 12);
      await prisma.user.create({
        data: {
          username: 'admin',
          email: '<EMAIL>',
          name: 'System Administrator',
          passwordHash,
          roleId: adminRole.id,
          active: true
        }
      });
      
      console.log('✅ Admin user created');
    } else {
      console.log('✅ Admin user found');
      
      // Ensure password is correct
      const passwordMatch = await bcrypt.compare('Jack75522r', adminUser.passwordHash);
      if (!passwordMatch) {
        console.log('🔧 Fixing admin password...');
        const newHash = await bcrypt.hash('Jack75522r', 12);
        await prisma.user.update({
          where: { id: adminUser.id },
          data: { passwordHash: newHash }
        });
        console.log('✅ Admin password fixed');
      }
      
      // Ensure user is active
      if (!adminUser.active) {
        await prisma.user.update({
          where: { id: adminUser.id },
          data: { active: true }
        });
        console.log('✅ Admin user activated');
      }
    }
    
    // Step 3: Check and fix environment variables
    console.log('\n3️⃣ CHECKING ENVIRONMENT VARIABLES');
    console.log('─'.repeat(40));
    
    const requiredEnvVars = {
      'NEXTAUTH_SECRET': process.env.NEXTAUTH_SECRET,
      'NEXTAUTH_URL': process.env.NEXTAUTH_URL,
      'DATABASE_URL': process.env.DATABASE_URL
    };
    
    let envIssues = false;
    Object.entries(requiredEnvVars).forEach(([key, value]) => {
      if (value) {
        console.log(`✅ ${key}: [SET]`);
      } else {
        console.log(`❌ ${key}: NOT SET`);
        envIssues = true;
      }
    });
    
    if (envIssues) {
      console.log('\n⚠️  ENVIRONMENT VARIABLE ISSUES DETECTED!');
      console.log('Please ensure these variables are set in your environment:');
      console.log('- NEXTAUTH_SECRET (should be a long random string)');
      console.log('- NEXTAUTH_URL (should be https://mocky.co.ke)');
      console.log('- DATABASE_URL (should point to your database)');
    }
    
    // Step 4: Restart the application
    console.log('\n4️⃣ RESTARTING APPLICATION');
    console.log('─'.repeat(40));
    
    try {
      console.log('   🔄 Restarting PM2 process...');
      execSync('pm2 restart all', { stdio: 'inherit' });
      console.log('✅ Application restarted');
    } catch (error) {
      console.log('⚠️  Could not restart PM2, please restart manually');
    }
    
    // Step 5: Create a test login script
    console.log('\n5️⃣ CREATING TEST LOGIN SCRIPT');
    console.log('─'.repeat(40));
    
    const testScript = `
const testLogin = async () => {
  const response = await fetch('https://mocky.co.ke/api/auth/signin', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      username: 'admin',
      password: 'Jack75522r',
      callbackUrl: '/admin/dashboard'
    })
  });
  
  console.log('Login test response:', response.status);
  const data = await response.text();
  console.log('Response data:', data);
};

// Run in browser console to test
testLogin();
    `;
    
    fs.writeFileSync('test-browser-login.js', testScript.trim());
    console.log('✅ Test script created: test-browser-login.js');
    
    console.log('\n═'.repeat(60));
    console.log('🎉 AUTHENTICATION FIX COMPLETED!');
    console.log('');
    console.log('📋 NEXT STEPS:');
    console.log('1. Clear your browser cache and cookies');
    console.log('2. Try logging in with:');
    console.log('   • Username: admin');
    console.log('   • Password: Jack75522r');
    console.log('3. If still failing, check browser console for errors');
    console.log('4. Run the test script in browser console: test-browser-login.js');
    console.log('');
    console.log('🌐 Login URL: https://mocky.co.ke/admin/login');
    
  } catch (error) {
    console.error('❌ Fix failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

fixAuthIssue(); 