#!/usr/bin/env node

/**
 * Populate Database with Realistic Data
 * 
 * This script populates the database with:
 * - Multiple users with different roles
 * - Clients and projects
 * - Services and catalogue items
 * - Blog posts and categories
 * - Invoices, quotes, and receipts
 * - Team members and testimonials
 */

const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function populateDatabase() {
  console.log('🚀 Populating Database with Realistic Data...\n');

  try {
    // Check if data already exists
    const existingUsers = await prisma.user.count();
    if (existingUsers > 1) {
      console.log('⚠️  Database already has data. Clearing existing data...');
      await clearExistingData();
    }

    // 1. Create additional users
    console.log('1️⃣ Creating users...');
    await createUsers();

    // 2. Create clients
    console.log('2️⃣ Creating clients...');
    await createClients();

    // 3. Create services and catalogue
    console.log('3️⃣ Creating services and catalogue...');
    await createServices();
    await createCatalogue();

    // 4. Create projects and tasks
    console.log('4️⃣ Creating projects and tasks...');
    await createProjects();

    // 5. Create blog posts
    console.log('5️⃣ Creating blog posts...');
    await createBlogPosts();

    // 6. Create team members
    console.log('6️⃣ Creating team members...');
    await createTeamMembers();

    // 7. Create testimonials
    console.log('7️⃣ Creating testimonials...');
    await createTestimonials();

    // 8. Create business documents
    console.log('8️⃣ Creating invoices, quotes, and receipts...');
    await createBusinessDocuments();

    // 9. Create portfolio items
    console.log('9️⃣ Creating portfolio items...');
    await createPortfolioItems();

    // 10. Show summary
    await showDataSummary();

    console.log('\n🎉 Database population complete!');
    console.log('\n👥 Login Credentials:');
    console.log('   Admin: admin / admin123');
    console.log('   Manager: sarah.manager / manager123');
    console.log('   Developer: john.dev / developer123');
    console.log('   Designer: emma.design / designer123');

  } catch (error) {
    console.error('\n❌ Database population failed:', error);
    console.log('\n🔧 Error details:', error.stack);
  } finally {
    await prisma.$disconnect();
  }
}

async function clearExistingData() {
  // Clear in correct order to respect foreign keys
  await prisma.receiptItem.deleteMany();
  await prisma.receipt.deleteMany();
  await prisma.quoteItem.deleteMany();
  await prisma.quote.deleteMany();
  await prisma.invoiceItem.deleteMany();
  await prisma.invoice.deleteMany();
  await prisma.timeEntry.deleteMany();
  await prisma.task.deleteMany();
  await prisma.projectMember.deleteMany();
  await prisma.project.deleteMany();
  await prisma.client.deleteMany();
  await prisma.activityLog.deleteMany();
  await prisma.blogPost.deleteMany();
  await prisma.websitePortfolio.deleteMany();
  await prisma.teamMember.deleteMany();
  await prisma.testimonial.deleteMany();
  await prisma.catalogue.deleteMany();
  await prisma.service.deleteMany();
  
  // Keep admin user, delete others
  await prisma.user.deleteMany({
    where: { username: { not: 'admin' } }
  });
  
  console.log('   ✅ Existing data cleared');
}

async function createUsers() {
  const users = [
    {
      username: 'sarah.manager',
      email: '<EMAIL>',
      name: 'Sarah Johnson',
      password: 'manager123',
      role: 'admin' // Manager with admin rights
    },
    {
      username: 'john.dev',
      email: '<EMAIL>', 
      name: 'John Smith',
      password: 'developer123',
      role: 'user'
    },
    {
      username: 'emma.design',
      email: '<EMAIL>',
      name: 'Emma Wilson',
      password: 'designer123', 
      role: 'user'
    },
    {
      username: 'client.demo',
      email: '<EMAIL>',
      name: 'Demo Client',
      password: 'client123',
      role: 'user'
    }
  ];

  for (const userData of users) {
    const role = await prisma.role.findFirst({
      where: { name: userData.role }
    });

    const hashedPassword = await bcrypt.hash(userData.password, 12);
    
    await prisma.user.create({
      data: {
        username: userData.username,
        email: userData.email,
        name: userData.name,
        passwordHash: hashedPassword,
        roleId: role.id,
        active: true
      }
    });
    
    console.log(`   ✅ Created user: ${userData.name} (${userData.username})`);
  }
}

async function createClients() {
  const clients = [
    {
      name: 'TechCorp Solutions',
      email: '<EMAIL>',
      phone: '******-0123',
      company: 'TechCorp Solutions Inc.',
      address: '123 Business St, Tech City, TC 12345',
      notes: 'Large enterprise client, prefers email communication'
    },
    {
      name: 'StartupXYZ',
      email: '<EMAIL>',
      phone: '******-0456',
      company: 'StartupXYZ LLC',
      address: '456 Innovation Ave, Startup Valley, SV 67890',
      notes: 'Fast-growing startup, needs scalable solutions'
    },
    {
      name: 'Local Restaurant Chain',
      email: '<EMAIL>',
      phone: '******-0789',
      company: 'Local Chains Restaurant Group',
      address: '789 Food Court, Restaurant Row, RR 54321',
      notes: 'Restaurant industry, seasonal marketing needs'
    },
    {
      name: 'Healthcare Plus',
      email: '<EMAIL>',
      phone: '******-0321',
      company: 'Healthcare Plus Medical Center',
      address: '321 Medical Dr, Health City, HC 98765',
      notes: 'Healthcare industry, HIPAA compliance required'
    },
    {
      name: 'E-commerce Boutique',
      email: '<EMAIL>',
      phone: '******-0654',
      company: 'Boutique Online Store',
      address: '654 Commerce Blvd, Shopping District, SD 13579',
      notes: 'E-commerce focus, mobile-first approach needed'
    }
  ];

  for (const clientData of clients) {
    await prisma.client.create({
      data: clientData
    });
    console.log(`   ✅ Created client: ${clientData.name}`);
  }
}

async function createServices() {
  const services = [
    {
      name: 'Website Development',
      description: 'Custom website development using modern technologies',
      price: 2500.00,
      category: 'Development'
    },
    {
      name: 'Mobile App Development',
      description: 'Native and cross-platform mobile applications',
      price: 5000.00,
      category: 'Development'
    },
    {
      name: 'UI/UX Design',
      description: 'User interface and experience design services',
      price: 1500.00,
      category: 'Design'
    },
    {
      name: 'SEO Optimization',
      description: 'Search engine optimization and digital marketing',
      price: 800.00,
      category: 'Marketing'
    },
    {
      name: 'Brand Identity Design',
      description: 'Logo design and brand identity packages',
      price: 1200.00,
      category: 'Design'
    },
    {
      name: 'E-commerce Platform',
      description: 'Complete e-commerce solution with payment integration',
      price: 3500.00,
      category: 'Development'
    },
    {
      name: 'Social Media Management',
      description: 'Monthly social media content and management',
      price: 600.00,
      category: 'Marketing'
    },
    {
      name: 'Technical Consulting',
      description: 'Technical consultation and architecture planning',
      price: 150.00,
      category: 'Consulting'
    }
  ];

  for (const serviceData of services) {
    await prisma.service.create({
      data: serviceData
    });
    console.log(`   ✅ Created service: ${serviceData.name}`);
  }
}

async function createCatalogue() {
  const catalogueItems = [
    {
      service: 'Basic Website Package',
      description: 'Professional 5-page website with responsive design',
      price: 1500,
      category: 'Web Development',
      features: ['Responsive Design', 'SEO Optimized', 'Contact Forms', 'Social Media Integration', '1 Year Support'],
      icon: 'globe',
      popular: true
    },
    {
      service: 'E-commerce Starter',
      description: 'Complete online store setup with payment processing',
      price: 2800,
      category: 'E-commerce',
      features: ['Product Catalog', 'Shopping Cart', 'Payment Gateway', 'Inventory Management', 'Order Tracking'],
      icon: 'shopping-cart',
      popular: true
    },
    {
      service: 'Logo Design Premium',
      description: 'Professional logo design with brand guidelines',
      price: 800,
      category: 'Design',
      features: ['3 Concepts', 'Unlimited Revisions', 'Vector Files', 'Brand Guidelines', 'Social Media Kit'],
      icon: 'palette'
    },
    {
      service: 'SEO Boost Package',
      description: 'Comprehensive SEO optimization for better rankings',
      price: 1200,
      category: 'Marketing',
      features: ['Keyword Research', 'On-Page SEO', 'Technical SEO', 'Content Strategy', 'Monthly Reports'],
      icon: 'trending-up'
    },
    {
      service: 'Mobile App MVP',
      description: 'Minimum viable product for iOS and Android',
      price: 4500,
      category: 'Mobile Development',
      features: ['Cross-Platform', 'User Authentication', 'Push Notifications', 'Analytics', 'App Store Submission'],
      icon: 'smartphone',
      popular: true
    },
    {
      service: 'Social Media Starter',
      description: 'Social media setup and content creation',
      price: 600,
      category: 'Marketing',
      features: ['Profile Setup', 'Content Calendar', 'Post Creation', 'Engagement Strategy', 'Monthly Analytics'],
      icon: 'share-2'
    }
  ];

  for (const item of catalogueItems) {
    await prisma.catalogue.create({
      data: item
    });
    console.log(`   ✅ Created catalogue item: ${item.service}`);
  }
}

async function createProjects() {
  const clients = await prisma.client.findMany();
  const users = await prisma.user.findMany();
  
  const projects = [
    {
      name: 'TechCorp Website Redesign',
      description: 'Complete website redesign with modern UI/UX and improved performance',
      clientId: clients[0].id,
      status: 'in_progress',
      priority: 'high',
      budget: 15000.00,
      startDate: new Date('2024-01-15'),
      endDate: new Date('2024-04-15')
    },
    {
      name: 'StartupXYZ Mobile App',
      description: 'Cross-platform mobile application for customer engagement',
      clientId: clients[1].id,
      status: 'planning',
      priority: 'medium',
      budget: 25000.00,
      startDate: new Date('2024-02-01'),
      endDate: new Date('2024-08-01')
    },
    {
      name: 'Restaurant Chain E-commerce',
      description: 'Online ordering system with delivery integration',
      clientId: clients[2].id,
      status: 'completed',
      priority: 'high',
      budget: 12000.00,
      startDate: new Date('2023-10-01'),
      endDate: new Date('2024-01-01')
    },
    {
      name: 'Healthcare Plus Portal',
      description: 'Patient portal with appointment booking and medical records',
      clientId: clients[3].id,
      status: 'in_progress',
      priority: 'high',
      budget: 35000.00,
      startDate: new Date('2024-01-01'),
      endDate: new Date('2024-07-01')
    }
  ];

  for (const projectData of projects) {
    const project = await prisma.project.create({
      data: projectData
    });

    // Add project members
    const projectMembers = users.slice(0, 3); // Add first 3 users to each project
    for (const user of projectMembers) {
      await prisma.projectMember.create({
        data: {
          projectId: project.id,
          userId: user.id,
          role: user.username.includes('admin') ? 'lead' : 'member'
        }
      });
    }

    // Create tasks for each project
    await createTasksForProject(project.id, users);
    
    console.log(`   ✅ Created project: ${projectData.name}`);
  }
}

async function createTasksForProject(projectId, users) {
  const tasks = [
    {
      title: 'Project Setup and Planning',
      description: 'Initial project setup, requirements gathering, and planning phase',
      status: 'completed',
      priority: 'high',
      estimatedHours: 16.0,
      actualHours: 18.0
    },
    {
      title: 'UI/UX Design',
      description: 'Create wireframes, mockups, and user interface designs',
      status: 'in_progress',
      priority: 'high',
      estimatedHours: 40.0,
      actualHours: 25.0
    },
    {
      title: 'Frontend Development',
      description: 'Implement responsive frontend using modern frameworks',
      status: 'todo',
      priority: 'medium',
      estimatedHours: 60.0
    },
    {
      title: 'Backend Development',
      description: 'Develop API endpoints and database integration',
      status: 'todo',
      priority: 'medium',
      estimatedHours: 50.0
    },
    {
      title: 'Testing and QA',
      description: 'Comprehensive testing and quality assurance',
      status: 'todo',
      priority: 'high',
      estimatedHours: 24.0
    }
  ];

  for (let i = 0; i < tasks.length; i++) {
    const taskData = tasks[i];
    const assignedUser = users[i % users.length];
    
    await prisma.task.create({
      data: {
        ...taskData,
        projectId,
        assignedTo: assignedUser.id,
        startDate: new Date(),
        dueDate: new Date(Date.now() + (7 * 24 * 60 * 60 * 1000)), // 7 days from now
        tags: ['development', 'web', 'client-work']
      }
    });
  }
}

async function createBlogPosts() {
  const blogPosts = [
    {
      title: 'The Future of Web Development: Trends to Watch in 2024',
      slug: 'future-web-development-trends-2024',
      content: `Web development continues to evolve at a rapid pace. In this post, we explore the key trends shaping the industry in 2024, from AI integration to progressive web apps.

## Key Trends

### 1. AI-Powered Development
Artificial intelligence is revolutionizing how we build websites and applications. From automated code generation to intelligent user experiences, AI is becoming an integral part of the development process.

### 2. Jamstack Architecture
The Jamstack approach continues to gain popularity, offering better performance, security, and developer experience.

### 3. Web3 Integration
Blockchain technology and Web3 concepts are finding their way into mainstream web development.

## Conclusion

Staying ahead of these trends is crucial for any development team looking to deliver cutting-edge solutions.`,
      excerpt: 'Explore the key web development trends shaping 2024, from AI integration to Jamstack architecture.',
      author: 'John Smith',
      category: 'Web Development',
      tags: ['web development', 'trends', 'AI', 'jamstack'],
      status: 'published',
      publishedAt: new Date('2024-01-15'),
      readingTime: 5
    },
    {
      title: 'Building Responsive E-commerce Sites: Best Practices',
      slug: 'responsive-ecommerce-best-practices',
      content: `Creating a successful e-commerce website requires careful attention to responsive design, user experience, and performance optimization.

## Essential Elements

### Mobile-First Design
With over 60% of e-commerce traffic coming from mobile devices, a mobile-first approach is essential.

### Performance Optimization
Fast loading times directly impact conversion rates and user satisfaction.

### Secure Payment Processing
Security is paramount in e-commerce development.`,
      excerpt: 'Learn the best practices for building responsive e-commerce websites that convert.',
      author: 'Emma Wilson',
      category: 'E-commerce',
      tags: ['e-commerce', 'responsive design', 'UX'],
      status: 'published',
      publishedAt: new Date('2024-01-20'),
      readingTime: 7
    },
    {
      title: 'SEO Strategies That Actually Work in 2024',
      slug: 'seo-strategies-2024',
      content: `Search engine optimization continues to evolve. Here are the strategies that are delivering real results in 2024.

## Core Strategies

### Content Quality
High-quality, relevant content remains the foundation of good SEO.

### Technical SEO
Website performance, mobile-friendliness, and core web vitals are crucial ranking factors.

### User Experience
Google's focus on user experience signals makes UX optimization essential for SEO success.`,
      excerpt: 'Discover the SEO strategies that are actually working in 2024.',
      author: 'Sarah Johnson',
      category: 'Marketing',
      tags: ['SEO', 'digital marketing', 'content strategy'],
      status: 'published',
      publishedAt: new Date('2024-01-25'),
      readingTime: 6
    }
  ];

  for (const postData of blogPosts) {
    await prisma.blogPost.create({
      data: postData
    });
    console.log(`   ✅ Created blog post: ${postData.title}`);
  }
}

async function createTeamMembers() {
  const teamMembers = [
    {
      name: 'Sarah Johnson',
      role: 'Project Manager & Co-Founder',
      bio: 'Sarah brings over 8 years of experience in project management and business development. She ensures every project is delivered on time and exceeds client expectations.',
      imageKey: 'team/sarah-johnson.jpg',
      linkedinUrl: 'https://linkedin.com/in/sarah-johnson',
      emailAddress: '<EMAIL>',
      order: 1
    },
    {
      name: 'John Smith',
      role: 'Senior Full-Stack Developer',
      bio: 'John is a passionate developer with expertise in React, Node.js, and cloud technologies. He loves creating scalable solutions that solve real-world problems.',
      imageKey: 'team/john-smith.jpg',
      linkedinUrl: 'https://linkedin.com/in/john-smith-dev',
      githubUrl: 'https://github.com/johnsmith',
      emailAddress: '<EMAIL>',
      order: 2
    },
    {
      name: 'Emma Wilson',
      role: 'UI/UX Designer',
      bio: 'Emma specializes in creating beautiful, user-centered designs that enhance the user experience. Her work has been featured in several design publications.',
      imageKey: 'team/emma-wilson.jpg',
      linkedinUrl: 'https://linkedin.com/in/emma-wilson-design',
      emailAddress: '<EMAIL>',
      order: 3
    },
    {
      name: 'Michael Chen',
      role: 'DevOps Engineer',
      bio: 'Michael ensures our applications run smoothly in production. He specializes in cloud infrastructure, CI/CD, and performance optimization.',
      imageKey: 'team/michael-chen.jpg',
      linkedinUrl: 'https://linkedin.com/in/michael-chen-devops',
      githubUrl: 'https://github.com/michaelchen',
      emailAddress: '<EMAIL>',
      order: 4
    }
  ];

  for (const memberData of teamMembers) {
    await prisma.teamMember.create({
      data: memberData
    });
    console.log(`   ✅ Created team member: ${memberData.name}`);
  }
}

async function createTestimonials() {
  const testimonials = [
    {
      name: 'David Thompson',
      company: 'TechCorp Solutions',
      location: 'San Francisco, CA',
      project: 'Corporate Website Redesign',
      testimonial: 'Mocky Digital transformed our online presence completely. The new website not only looks amazing but has increased our lead generation by 300%. Their team was professional, responsive, and delivered exactly what we needed.',
      rating: 5,
      order: 1
    },
    {
      name: 'Lisa Rodriguez',
      company: 'StartupXYZ',
      location: 'Austin, TX',
      project: 'Mobile App Development',
      testimonial: 'Working with Mocky Digital was a game-changer for our startup. They built us a beautiful, functional mobile app that our users love. The project was completed on time and within budget.',
      rating: 5,
      order: 2
    },
    {
      name: 'Robert Kim',
      company: 'Local Restaurant Chain',
      location: 'Chicago, IL',
      project: 'E-commerce Platform',
      testimonial: 'The online ordering system Mocky Digital built for us has revolutionized our business. We saw a 250% increase in online orders within the first month. Highly recommended!',
      rating: 5,
      order: 3
    },
    {
      name: 'Jennifer Martinez',
      company: 'Healthcare Plus',
      location: 'Miami, FL',
      project: 'Patient Portal Development',
      testimonial: 'Mocky Digital understood our complex healthcare requirements and delivered a secure, user-friendly patient portal. Their attention to detail and compliance knowledge was impressive.',
      rating: 5,
      order: 4
    },
    {
      name: 'Alex Johnson',
      company: 'E-commerce Boutique',
      location: 'Seattle, WA',
      project: 'Brand Identity & Website',
      testimonial: 'From logo design to a complete e-commerce website, Mocky Digital helped us establish a strong online presence. Our sales have tripled since launching the new site.',
      rating: 5,
      order: 5
    }
  ];

  for (const testimonialData of testimonials) {
    await prisma.testimonial.create({
      data: testimonialData
    });
    console.log(`   ✅ Created testimonial: ${testimonialData.name}`);
  }
}

async function createBusinessDocuments() {
  const clients = await prisma.client.findMany();
  const users = await prisma.user.findMany();
  const services = await prisma.service.findMany();

  // Create invoices
  for (let i = 0; i < 5; i++) {
    const client = clients[i % clients.length];
    const user = users[0]; // Admin user
    const service = services[i % services.length];

    const invoice = await prisma.invoice.create({
      data: {
        invoiceNumber: `INV-2024-${String(i + 1).padStart(3, '0')}`,
        clientId: client.id,
        userId: user.id,
        totalAmount: service.price,
        taxAmount: service.price * 0.1,
        status: i < 3 ? 'paid' : 'pending',
        dueDate: new Date(Date.now() + (30 * 24 * 60 * 60 * 1000)),
        issuedAt: new Date(),
        paidAt: i < 3 ? new Date() : null
      }
    });

    await prisma.invoiceItem.create({
      data: {
        invoiceId: invoice.id,
        serviceId: service.id,
        description: service.description,
        quantity: 1,
        unitPrice: service.price,
        totalPrice: service.price
      }
    });
  }

  // Create quotes
  for (let i = 0; i < 3; i++) {
    const client = clients[i % clients.length];
    const user = users[0];
    const service = services[i % services.length];

    const quote = await prisma.quote.create({
      data: {
        quoteNumber: `QUO-2024-${String(i + 1).padStart(3, '0')}`,
        clientId: client.id,
        userId: user.id,
        totalAmount: service.price,
        taxAmount: service.price * 0.1,
        status: 'pending',
        validUntil: new Date(Date.now() + (30 * 24 * 60 * 60 * 1000))
      }
    });

    await prisma.quoteItem.create({
      data: {
        quoteId: quote.id,
        serviceId: service.id,
        description: service.description,
        quantity: 1,
        unitPrice: service.price,
        totalPrice: service.price
      }
    });
  }

  // Create receipts
  for (let i = 0; i < 4; i++) {
    const client = clients[i % clients.length];
    const user = users[0];
    const service = services[i % services.length];

    const receipt = await prisma.receipt.create({
      data: {
        receiptNumber: `REC-2024-${String(i + 1).padStart(3, '0')}`,
        clientId: client.id,
        userId: user.id,
        totalAmount: service.price,
        taxAmount: service.price * 0.1,
        paymentMethod: i % 2 === 0 ? 'credit_card' : 'bank_transfer',
        issuedAt: new Date()
      }
    });

    await prisma.receiptItem.create({
      data: {
        receiptId: receipt.id,
        serviceId: service.id,
        description: service.description,
        quantity: 1,
        unitPrice: service.price,
        totalPrice: service.price
      }
    });
  }

  console.log('   ✅ Created business documents (invoices, quotes, receipts)');
}

async function createPortfolioItems() {
  const portfolioItems = [
    {
      title: 'TechCorp Corporate Website',
      description: 'Modern corporate website with advanced features and CMS integration',
      imageUrl: '/portfolio/techcorp-website.jpg',
      projectUrl: 'https://techcorp-demo.com',
      category: 'Web Development',
      tags: ['React', 'Next.js', 'CMS', 'Corporate'],
      featured: true,
      order: 1
    },
    {
      title: 'StartupXYZ Mobile App',
      description: 'Cross-platform mobile application with real-time features',
      imageUrl: '/portfolio/startupxyz-app.jpg',
      projectUrl: 'https://app.startupxyz.com',
      category: 'Mobile Development',
      tags: ['React Native', 'Mobile', 'Startup', 'Real-time'],
      featured: true,
      order: 2
    },
    {
      title: 'Restaurant Chain E-commerce',
      description: 'Online ordering system with delivery integration and payment processing',
      imageUrl: '/portfolio/restaurant-ecommerce.jpg',
      projectUrl: 'https://order.localchains.com',
      category: 'E-commerce',
      tags: ['E-commerce', 'Payments', 'Delivery', 'Restaurant'],
      featured: true,
      order: 3
    },
    {
      title: 'Healthcare Plus Patient Portal',
      description: 'Secure patient portal with appointment booking and medical records',
      imageUrl: '/portfolio/healthcare-portal.jpg',
      category: 'Healthcare',
      tags: ['Healthcare', 'Security', 'Portal', 'HIPAA'],
      featured: false,
      order: 4
    },
    {
      title: 'Boutique Brand Identity',
      description: 'Complete brand identity design including logo, guidelines, and marketing materials',
      imageUrl: '/portfolio/boutique-branding.jpg',
      category: 'Branding',
      tags: ['Branding', 'Logo Design', 'Identity', 'Marketing'],
      featured: false,
      order: 5
    }
  ];

  for (const itemData of portfolioItems) {
    await prisma.websitePortfolio.create({
      data: itemData
    });
    console.log(`   ✅ Created portfolio item: ${itemData.title}`);
  }
}

async function showDataSummary() {
  console.log('\n📊 Database Population Summary:');
  
  const counts = await Promise.all([
    prisma.user.count(),
    prisma.client.count(),
    prisma.project.count(),
    prisma.task.count(),
    prisma.service.count(),
    prisma.catalogue.count(),
    prisma.blogPost.count(),
    prisma.teamMember.count(),
    prisma.testimonial.count(),
    prisma.invoice.count(),
    prisma.quote.count(),
    prisma.receipt.count(),
    prisma.websitePortfolio.count()
  ]);

  const [users, clients, projects, tasks, services, catalogue, blogPosts, teamMembers, testimonials, invoices, quotes, receipts, portfolio] = counts;

  console.log(`   👥 Users: ${users}`);
  console.log(`   🏢 Clients: ${clients}`);
  console.log(`   📋 Projects: ${projects}`);
  console.log(`   ✅ Tasks: ${tasks}`);
  console.log(`   🛠️  Services: ${services}`);
  console.log(`   📦 Catalogue Items: ${catalogue}`);
  console.log(`   📝 Blog Posts: ${blogPosts}`);
  console.log(`   👨‍💼 Team Members: ${teamMembers}`);
  console.log(`   💬 Testimonials: ${testimonials}`);
  console.log(`   🧾 Invoices: ${invoices}`);
  console.log(`   💰 Quotes: ${quotes}`);
  console.log(`   🧾 Receipts: ${receipts}`);
  console.log(`   🎨 Portfolio Items: ${portfolio}`);
}

// Run the script
if (require.main === module) {
  populateDatabase().catch(console.error);
}

module.exports = { populateDatabase }; 