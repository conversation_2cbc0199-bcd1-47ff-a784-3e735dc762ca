#!/bin/bash

# Validate Prisma Migration Script
# Tests the migration without applying it to production

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check migration status
check_migration_status() {
    log "Checking migration status..."
    
    if ! npx prisma migrate status; then
        error "Failed to check migration status"
        return 1
    fi
    
    success "Migration status check completed"
}

# Validate schema
validate_schema() {
    log "Validating Prisma schema..."
    
    if ! npx prisma validate; then
        error "Schema validation failed"
        return 1
    fi
    
    success "Schema validation passed"
}

# Check for pending migrations
check_pending_migrations() {
    log "Checking for pending migrations..."
    
    # Get migration status and check for pending migrations
    local status_output=$(npx prisma migrate status 2>&1)
    
    if echo "$status_output" | grep -q "Following migration have not yet been applied"; then
        log "Found pending migrations:"
        echo "$status_output" | grep -A 10 "Following migration have not yet been applied"
        return 0
    elif echo "$status_output" | grep -q "Database schema is up to date"; then
        log "No pending migrations found"
        return 0
    else
        warning "Could not determine migration status clearly"
        echo "$status_output"
        return 1
    fi
}

# Test migration in dry-run mode (if supported)
test_migration_dry_run() {
    log "Testing migration (validation only)..."
    
    # Check if we can generate a client
    if ! npx prisma generate; then
        error "Failed to generate Prisma client"
        return 1
    fi
    
    success "Prisma client generation successful"
}

# Check database connection
test_database_connection() {
    log "Testing database connection..."
    
    if ! npm run prisma:migrate -- status > /dev/null 2>&1; then
        error "Cannot connect to database"
        error "Please check your DATABASE_URL environment variable"
        return 1
    fi
    
    success "Database connection successful"
}

# Estimate migration impact
estimate_migration_impact() {
    log "Estimating migration impact..."
    
    # Check the migration file
    local migration_file="prisma/migrations/20250615011826_add_performance_indexes/migration.sql"
    
    if [ -f "$migration_file" ]; then
        local index_count=$(grep -c "CREATE INDEX" "$migration_file" || true)
        local unique_count=$(grep -c "CREATE UNIQUE INDEX" "$migration_file" || true)
        
        log "Migration will create:"
        log "  - $index_count regular indexes"
        log "  - $unique_count unique indexes"
        log "  - Total SQL statements: $(wc -l < "$migration_file")"
        
        warning "Note: Unique constraints may fail if duplicate data exists"
        
        success "Migration impact assessment completed"
    else
        warning "Migration file not found"
        return 1
    fi
}

# Main validation function
main() {
    log "Starting Prisma migration validation..."
    echo ""
    
    validate_schema || exit 1
    test_database_connection || exit 1
    check_migration_status || exit 1
    check_pending_migrations || exit 1
    test_migration_dry_run || exit 1
    estimate_migration_impact || exit 1
    
    echo ""
    success "All validation checks passed!"
    
    echo ""
    echo "=== MIGRATION VALIDATION SUMMARY ==="
    echo "✅ Schema validation passed"
    echo "✅ Database connection successful"
    echo "✅ Migration status checked"
    echo "✅ Prisma client generation successful"
    echo "✅ Migration impact assessed"
    echo ""
    echo "You can now safely run the migration with:"
    echo "  npx prisma migrate deploy"
    echo ""
    echo "Or use the full upgrade script:"
    echo "  ./scripts/phase1-security-upgrade.sh"
}

# Run validation
main 