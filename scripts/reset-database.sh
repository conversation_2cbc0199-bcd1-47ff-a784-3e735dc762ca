#!/bin/bash

echo "🔥 Database Reset Script - Drop All Tables & Recreate Fresh"
echo "⚠️  WARNING: This will DELETE ALL DATA in the database!"
echo ""

# Change to project directory
cd /var/www/mocky

echo "📍 Working directory: $(pwd)"
echo ""

# Check if this is development environment
if [ "$NODE_ENV" = "production" ]; then
    echo "❌ PRODUCTION ENVIRONMENT DETECTED!"
    echo "This script should not be run in production."
    echo "Exiting for safety..."
    exit 1
fi

echo "🔍 Current database status:"
echo "DATABASE_URL: ${DATABASE_URL:0:20}..."
echo ""

echo "1️⃣ Backing up current schema..."
mkdir -p ./backups/$(date +%Y%m%d)
cp prisma/schema.prisma ./backups/$(date +%Y%m%d)/schema_backup_$(date +%H%M%S).prisma
echo "✅ Schema backed up"

echo ""
echo "2️⃣ Generating fresh Prisma client..."
npx prisma generate
echo "✅ Prisma client generated"

echo ""
echo "3️⃣ Resetting database (drops all tables)..."
npx prisma migrate reset --force --skip-seed
echo "✅ Database reset complete"

echo ""
echo "4️⃣ Applying all migrations (recreates tables)..."
npx prisma migrate deploy
echo "✅ Migrations applied"

echo ""
echo "5️⃣ Verifying database structure..."
npx prisma migrate status
echo "✅ Migration status checked"

echo ""
echo "6️⃣ Running database seeding..."

# Run the main seed file
if [ -f "prisma/seed.ts" ]; then
    echo "Running main TypeScript seeder..."
    npx prisma db seed
    echo "✅ Main seed completed"
elif [ -f "prisma/seed.js" ]; then
    echo "Running main JavaScript seeder..."
    npx prisma db seed
    echo "✅ Main seed completed"
fi

# Run additional seed scripts
echo ""
echo "7️⃣ Running additional seed scripts..."

if [ -f "seed-pricing.js" ]; then
    echo "Seeding pricing data..."
    node seed-pricing.js
    echo "✅ Pricing data seeded"
fi

if [ -f "seed-design-portfolio.js" ]; then
    echo "Seeding portfolio data..."
    node seed-design-portfolio.js
    echo "✅ Portfolio data seeded"
fi

if [ -f "scripts/seed-all-data.js" ]; then
    echo "Running comprehensive data seeder..."
    node scripts/seed-all-data.js
    echo "✅ All data seeded"
fi

if [ -f "scripts/seed-services.ts" ]; then
    echo "Seeding services data..."
    npx ts-node scripts/seed-services.ts
    echo "✅ Services data seeded"
fi

echo ""
echo "8️⃣ Running verification..."
if [ -f "verify-database.js" ]; then
    node verify-database.js
else
    echo "ℹ️  Verification script not found, skipping..."
fi

echo ""
echo "🎉 Database reset complete!"
echo ""
echo "📊 Final database status:"
npx prisma migrate status

echo ""
echo "🔗 Database URL: ${DATABASE_URL:0:30}..."
echo "📝 All tables have been recreated with the latest schema"
echo "🌱 Database has been seeded with initial data"
echo "🚀 You can now start your application with fresh data"

echo ""
echo "🔍 Quick verification:"
node -e "
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();
(async () => {
  try {
    const counts = {
      catalogue: await prisma.catalogue.count(),
      blogPosts: await prisma.blogPost.count(),
      services: await prisma.service.count(),
      testimonials: await prisma.testimonial.count()
    };
    console.log('📊 Record counts:', counts);
    console.log('✅ Database verification passed!');
  } catch (error) {
    console.error('❌ Verification failed:', error.message);
  } finally {
    await prisma.\$disconnect();
  }
})();
" 