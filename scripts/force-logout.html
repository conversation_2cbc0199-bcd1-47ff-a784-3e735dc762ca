<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Force Logout - Mocky Digital</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .container {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            padding: 2rem;
            border-radius: 10px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        .status {
            font-size: 1.2rem;
            margin: 1rem 0;
        }
        .success {
            color: #4ade80;
        }
        .error {
            color: #f87171;
        }
        .loading {
            color: #fbbf24;
        }
        button {
            background: #dc2626;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 1rem;
            cursor: pointer;
            margin: 10px;
            transition: background 0.2s;
        }
        button:hover {
            background: #b91c1c;
        }
        .login-btn {
            background: #059669;
        }
        .login-btn:hover {
            background: #047857;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Force Logout</h1>
        <div id="status" class="status loading">Initiating logout process...</div>
        
        <div id="actions" style="display: none;">
            <button onclick="forceLogout()">Force Logout Again</button>
            <button onclick="goToLogin()" class="login-btn">Go to Login</button>
        </div>
    </div>

    <script>
        async function forceLogout() {
            const statusEl = document.getElementById('status');
            const actionsEl = document.getElementById('actions');
            
            statusEl.className = 'status loading';
            statusEl.textContent = 'Clearing session data...';
            actionsEl.style.display = 'none';

            try {
                // Step 1: Call logout API
                statusEl.textContent = '1/4 Calling logout API...';
                const response = await fetch('/api/auth/logout', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'include'
                });

                // Step 2: Clear all cookies manually
                statusEl.textContent = '2/4 Clearing cookies...';
                const cookiesToClear = [
                    'mocky-session',
                    'next-auth.session-token',
                    'next-auth.csrf-token',
                    '__Secure-next-auth.session-token',
                    'auth-token',
                    'session'
                ];

                cookiesToClear.forEach(name => {
                    // Clear for current domain
                    document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=${window.location.hostname}`;
                    // Clear for parent domain
                    document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.${window.location.hostname}`;
                    // Clear without domain
                    document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
                });

                // Step 3: Clear local/session storage
                statusEl.textContent = '3/4 Clearing storage...';
                try {
                    localStorage.clear();
                    sessionStorage.clear();
                } catch (e) {
                    console.log('Storage clear error (expected in some browsers):', e);
                }

                // Step 4: Clear cache if supported
                statusEl.textContent = '4/4 Clearing cache...';
                if ('caches' in window) {
                    try {
                        const cacheNames = await caches.keys();
                        await Promise.all(
                            cacheNames.map(cacheName => caches.delete(cacheName))
                        );
                    } catch (e) {
                        console.log('Cache clear error:', e);
                    }
                }

                // Success
                statusEl.className = 'status success';
                statusEl.textContent = '✅ Logout successful! All session data cleared.';
                actionsEl.style.display = 'block';

                // Auto-redirect to login after 3 seconds
                setTimeout(() => {
                    window.location.href = '/admin/login';
                }, 3000);

            } catch (error) {
                console.error('Logout error:', error);
                statusEl.className = 'status error';
                statusEl.textContent = '❌ Error during logout. Redirecting to login...';
                actionsEl.style.display = 'block';
                
                // Still redirect to login even if there's an error
                setTimeout(() => {
                    window.location.href = '/admin/login';
                }, 2000);
            }
        }

        function goToLogin() {
            window.location.href = '/admin/login';
        }

        // Auto-start logout process
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(forceLogout, 500);
        });
    </script>
</body>
</html> 