#!/usr/bin/env node

/**
 * Script to seed blog categories related to services offered
 * Run with: node scripts/seed-blog-categories.js
 */

const { PrismaClient } = require('@prisma/client');
const slugify = require('slugify');

const prisma = new PrismaClient();

// Blog categories related to services offered
const blogCategories = [
  {
    name: 'Web Development',
    description: 'Articles about web development, frameworks, best practices, and modern web technologies. Covering topics from frontend to backend development.'
  },
  {
    name: 'Mobile App Development',
    description: 'Insights into mobile app development for iOS and Android, including React Native, Flutter, and native development approaches.'
  },
  {
    name: 'Graphic Design',
    description: 'Design inspiration, tutorials, and trends in graphic design including logo design, branding, and visual identity creation.'
  },
  {
    name: 'Digital Marketing',
    description: 'Digital marketing strategies, social media marketing, SEO tips, content marketing, and online advertising best practices.'
  },
  {
    name: 'E-commerce',
    description: 'E-commerce development, online store optimization, payment integration, and digital commerce trends and strategies.'
  },
  {
    name: 'SEO & Analytics',
    description: 'Search engine optimization techniques, Google Analytics insights, website performance optimization, and digital analytics.'
  },
  {
    name: 'Branding & Identity',
    description: 'Brand development, corporate identity design, brand strategy, and building strong brand presence in the digital age.'
  },
  {
    name: 'User Experience (UX)',
    description: 'User experience design principles, usability testing, user research, and creating intuitive digital experiences.'
  },
  {
    name: 'Business Technology',
    description: 'Technology solutions for businesses, digital transformation, productivity tools, and leveraging technology for growth.'
  },
  {
    name: 'Print Design',
    description: 'Print design techniques, business card design, brochure creation, and bridging the gap between digital and print media.'
  },
  {
    name: 'Social Media',
    description: 'Social media strategy, content creation for social platforms, community management, and social media advertising.'
  },
  {
    name: 'Content Strategy',
    description: 'Content marketing strategies, copywriting tips, content planning, and creating engaging content for digital platforms.'
  },
  {
    name: 'Freelancing & Business',
    description: 'Freelancing tips, business development, client management, pricing strategies, and building a successful creative business.'
  },
  {
    name: 'Industry Trends',
    description: 'Latest trends in technology, design, and digital marketing. Keeping up with industry developments and future predictions.'
  },
  {
    name: 'Case Studies',
    description: 'Real-world project case studies, client success stories, and detailed breakdowns of completed projects and their outcomes.'
  },
  {
    name: 'Tutorials & How-To',
    description: 'Step-by-step tutorials, how-to guides, and practical instructions for various design and development tasks.'
  },
  {
    name: 'Tools & Resources',
    description: 'Reviews and recommendations of design tools, development resources, productivity apps, and industry software.'
  },
  {
    name: 'Client Education',
    description: 'Educational content for clients about the design and development process, project timelines, and collaboration best practices.'
  },
  {
    name: 'Portfolio Showcase',
    description: 'Showcasing completed projects, design processes, and highlighting the creative work and achievements of the team.'
  },
  {
    name: 'Company News',
    description: 'Company updates, team announcements, new service offerings, and behind-the-scenes content about the business.'
  }
];

async function seedBlogCategories() {
  try {
    console.log('🌱 Starting blog categories seeding...');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');

    // Check if blog categories already exist
    const existingCategories = await prisma.blogCategory.findMany();
    
    if (existingCategories.length > 0) {
      console.log(`⚠️  Found ${existingCategories.length} existing blog categories.`);
      console.log('   Choose an option:');
      console.log('   1. Skip seeding (keep existing categories)');
      console.log('   2. Add new categories only (skip duplicates)');
      console.log('   3. Clear all and reseed');
      
      // For automation, we'll choose option 2 (add new only)
      console.log('   → Automatically choosing option 2: Add new categories only');
    }

    let createdCount = 0;
    let skippedCount = 0;

    for (const categoryData of blogCategories) {
      // Generate slug
      const slug = slugify(categoryData.name, { 
        lower: true, 
        strict: true,
        remove: /[*+~.()'"!:@]/g
      });

      // Check if category already exists
      const existingCategory = await prisma.blogCategory.findUnique({
        where: { slug }
      });

      if (existingCategory) {
        console.log(`⏭️  Skipping "${categoryData.name}" (already exists)`);
        skippedCount++;
        continue;
      }

      // Create the category
      const newCategory = await prisma.blogCategory.create({
        data: {
          name: categoryData.name,
          slug: slug,
          description: categoryData.description
        }
      });

      console.log(`✅ Created: "${newCategory.name}" (${newCategory.slug})`);
      createdCount++;
    }

    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    console.log('🎉 Blog categories seeding completed!');
    console.log(`📊 Summary:`);
    console.log(`   ✅ Created: ${createdCount} categories`);
    console.log(`   ⏭️  Skipped: ${skippedCount} categories`);
    console.log(`   📝 Total in database: ${createdCount + existingCategories.length} categories`);

    // Display all categories
    const allCategories = await prisma.blogCategory.findMany({
      orderBy: { name: 'asc' }
    });

    console.log('\n📋 All Blog Categories:');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    allCategories.forEach((category, index) => {
      console.log(`${(index + 1).toString().padStart(2, ' ')}. ${category.name}`);
      console.log(`    Slug: ${category.slug}`);
      if (category.description) {
        console.log(`    Description: ${category.description.substring(0, 80)}${category.description.length > 80 ? '...' : ''}`);
      }
      console.log('');
    });

    console.log('🚀 Blog categories are now ready for use in your blog posts!');
    console.log('💡 You can access them at: /admin/blog/categories');

  } catch (error) {
    console.error('❌ Error seeding blog categories:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Handle script execution
if (require.main === module) {
  seedBlogCategories()
    .then(() => {
      console.log('\n✨ Script completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Script failed:', error);
      process.exit(1);
    });
}

module.exports = { seedBlogCategories, blogCategories }; 