// Script to seed design services pricing data for the Kenyan market
const { PrismaClient } = require('@prisma/client');
require('dotenv').config();

// Initialize Prisma client
const prisma = new PrismaClient();

// Define design services with pricing relevant to the Kenyan market
const designServices = [
  {
    name: 'Business Card Design',
    description: 'Professional business card design with your branding and contact information',
    price: 1500,
    category: 'design'
  },
  {
    name: 'Logo Design',
    description: 'Custom logo design for your brand identity',
    price: 3000,
    category: 'design'
  },
  {
    name: 'Flyer Design',
    description: 'Eye-catching flyer design for events and promotions',
    price: 2000,
    category: 'design'
  },
  {
    name: 'Brochure Design',
    description: 'Professional brochure design for marketing materials',
    price: 2500,
    category: 'design'
  },
  {
    name: 'Letterhead Design',
    description: 'Corporate letterhead design with your brand elements',
    price: 1200,
    category: 'design'
  },
  {
    name: 'Banner Design',
    description: 'Large format banner design for outdoor advertising',
    price: 1800,
    category: 'design'
  },
  {
    name: 'Poster Design',
    description: 'Creative poster design for events and advertisements',
    price: 1600,
    category: 'design'
  },
  {
    name: 'Social Media Graphics',
    description: 'Custom graphics for social media posts and campaigns',
    price: 1000,
    category: 'design'
  },
  {
    name: 'Packaging Design',
    description: 'Product packaging design and label creation',
    price: 4000,
    category: 'design'
  },
  {
    name: 'Menu Design',
    description: 'Restaurant menu design with professional layout',
    price: 2200,
    category: 'design'
  }
];

// Main function to seed the database
async function seedDesignServices() {
  console.log('🌱 Seeding design services...');
  
  try {
    // Check if services already exist
    const existingServices = await prisma.service.findMany({
      where: {
        category: 'design'
      }
    });

    console.log(`Found ${existingServices.length} existing design services`);

    if (existingServices.length > 0) {
      console.log('⚠️  Design services already exist. Do you want to:');
      console.log('1. Skip seeding (press Ctrl+C)');
      console.log('2. Continue to add more services');
      
      // Wait for 3 seconds to allow user to cancel
      await new Promise(resolve => setTimeout(resolve, 3000));
    }

    let created = 0;
    let skipped = 0;

    for (const service of designServices) {
      try {
        // Check if this specific service already exists
        const existing = await prisma.service.findFirst({
          where: {
            name: service.name,
            category: 'design'
          }
        });

        if (existing) {
          console.log(`⏭️  Skipping "${service.name}" - already exists`);
          skipped++;
          continue;
        }

        // Create the service
        const createdService = await prisma.service.create({
          data: service
        });

        console.log(`✅ Created: ${createdService.name} - KSh ${createdService.price.toLocaleString()}`);
        created++;
      } catch (error) {
        console.error(`❌ Error creating "${service.name}":`, error.message);
      }
    }

    console.log('\n📊 Seeding Summary:');
    console.log(`✅ Created: ${created} services`);
    console.log(`⏭️  Skipped: ${skipped} services`);
    console.log(`📝 Total design services in database: ${existingServices.length + created}`);

    // Display all design services
    const allDesignServices = await prisma.service.findMany({
      where: {
        category: 'design'
      },
      orderBy: {
        name: 'asc'
      }
    });

    console.log('\n🎨 All Design Services:');
    allDesignServices.forEach((service, index) => {
      console.log(`${index + 1}. ${service.name} - KSh ${service.price.toLocaleString()}`);
      if (service.description) {
        console.log(`   ${service.description}`);
      }
    });

  } catch (error) {
    console.error('❌ Error seeding design services:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seeding function
seedDesignServices()
  .then(() => {
    console.log('\n🎉 Design services seeding completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 Seeding failed:', error);
    process.exit(1);
  });
