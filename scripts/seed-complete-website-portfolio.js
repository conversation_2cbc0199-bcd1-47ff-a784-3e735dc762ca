#!/usr/bin/env node

/**
 * Complete Website Portfolio Seeding Script
 * Merges all portfolio seeding functionality into one comprehensive script
 * Run with: node scripts/seed-complete-website-portfolio.js
 */

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

// Real client websites (Kenyan/African market) - Updated with verified working sites
const realClientWebsites = [
  {
    title: 'Wasonga Law',
    description: 'C.O Wasonga & Co. Advocates - Professional legal services specializing in corporate law, employment relations, real estate, constitutional litigation, and devolution law in Kenya.',
    url: 'https://wasongalaw.co.ke/',
    imageUrl: 'https://placehold.co/600x400/blue/white?text=Wasonga+Law',
    imageKey: 'portfolio/wasonga-law.jpg',
    category: 'Legal',
    featured: true,
    tags: ['Legal Services', 'Corporate Law', 'Employment Law', 'Real Estate', 'Constitutional Law', 'Kenya']
  },
  {
    title: 'Pure Gift Organization',
    description: 'Non-profit organization website dedicated to charitable giving, community development, and social impact in East Africa.',
    url: 'https://pure-gift.org/',
    imageUrl: 'https://placehold.co/600x400/green/white?text=Pure+Gift',
    imageKey: 'portfolio/pure-gift.jpg',
    category: 'Non-Profit',
    featured: true,
    tags: ['Non-Profit', 'Charity', 'Community Development', 'East Africa', 'Social Impact']
  },
  {
    title: 'MRL Motors',
    description: 'Mombasa Rickshaw LTD - Leading supplier of high-quality motorcycles and tuk-tuks in Kenya, offering durable and cost-effective transportation solutions since 2019.',
    url: 'https://mrlmotors.co.ke/',
    imageUrl: 'https://placehold.co/600x400/orange/white?text=MRL+Motors',
    imageKey: 'portfolio/mrl-motors.jpg',
    category: 'Automotive',
    featured: true,
    tags: ['Motorcycles', 'Tuk-Tuks', 'Transportation', 'Vehicle Sales', 'Kenya', 'Mombasa']
  },
  {
    title: 'Reucher Africa Kenya Ltd',
    description: 'Premier supplier of industrial chemicals, essential oils, water treatment solutions, and reverse osmosis systems serving various industries across Kenya and East Africa.',
    url: 'https://reucherafricakenyaltd.co.ke/',
    imageUrl: 'https://placehold.co/600x400/teal/white?text=Reucher+Africa',
    imageKey: 'portfolio/reucher-africa.jpg',
    category: 'Industrial',
    featured: true,
    tags: ['Industrial Chemicals', 'Water Treatment', 'Essential Oils', 'RO Systems', 'Manufacturing', 'Kenya']
  },
  {
    title: 'World Space Energy',
    description: 'WSE - Innovative communications solutions provider specializing in aviation, military, government, and commercial sectors with cutting-edge technology solutions.',
    url: 'https://wse.org.za/',
    imageUrl: 'https://placehold.co/600x400/navy/white?text=World+Space+Energy',
    imageKey: 'portfolio/world-space-energy.jpg',
    category: 'Technology',
    featured: true,
    tags: ['Communications', 'Aviation', 'Military', 'Government', 'Technology Solutions', 'South Africa']
  },
  {
    title: 'Homestore Kenya',
    description: 'E-commerce platform for household items and home appliances with nationwide delivery across Kenya, offering quality products at affordable prices.',
    url: 'https://homestore.co.ke/',
    imageUrl: 'https://placehold.co/600x400/purple/white?text=Homestore',
    imageKey: 'portfolio/homestore-kenya.jpg',
    category: 'E-commerce',
    featured: true,
    tags: ['E-commerce', 'Household Items', 'Home Appliances', 'Kenya', 'Online Shopping', 'Delivery']
  },
  {
    title: 'Marsabit Walk Movement',
    description: 'Community-driven organization promoting health, wellness, and social development through walking initiatives and community engagement in Marsabit County.',
    url: 'https://marsabitwalkmovement.org/',
    imageUrl: 'https://placehold.co/600x400/green/white?text=Marsabit+Walk',
    imageKey: 'portfolio/marsabit-walk.jpg',
    category: 'Health & Fitness',
    featured: false,
    tags: ['Health', 'Wellness', 'Community', 'Walking', 'Marsabit', 'Social Development']
  },
  {
    title: 'Knowledge Spring Institute',
    description: 'Educational institution providing quality academic programs and professional development courses, fostering knowledge and skills development in Kenya.',
    url: 'https://knowledgespringinstitute.ac.ke/',
    imageUrl: 'https://placehold.co/600x400/blue/white?text=Knowledge+Spring',
    imageKey: 'portfolio/knowledge-spring.jpg',
    category: 'Education',
    featured: false,
    tags: ['Education', 'Academic Programs', 'Professional Development', 'Institute', 'Kenya', 'Learning']
  },
  {
    title: 'NMIOS Organization',
    description: 'Non-governmental organization focused on social development, community empowerment, and sustainable development initiatives across Kenya.',
    url: 'https://nmios.org/',
    imageUrl: 'https://placehold.co/600x400/orange/white?text=NMIOS',
    imageKey: 'portfolio/nmios.jpg',
    category: 'Non-Profit',
    featured: false,
    tags: ['NGO', 'Social Development', 'Community Empowerment', 'Sustainable Development', 'Kenya']
  },
  {
    title: 'Knight Swift Logistics Limited',
    description: 'Cutting-edge last-mile delivery service provider focused on fast, reliable, and efficient solutions with real-time tracking, route optimization, and technology-driven approach.',
    url: 'https://knightswiftlogisticslimited.com/',
    imageUrl: 'https://placehold.co/600x400/red/white?text=Knight+Swift',
    imageKey: 'portfolio/knight-swift.jpg',
    category: 'Logistics',
    featured: true,
    tags: ['Logistics', 'Last-Mile Delivery', 'Real-Time Tracking', 'Route Optimization', 'E-commerce', 'Kenya']
  },
  {
    title: 'I4Food Organization',
    description: 'Institute for Food Systems & Climate (IFCA) - Bridging the gap between innovative research and practical solutions for sustainable food systems and climate adaptation in Africa.',
    url: 'https://i4food.org/',
    imageUrl: 'https://placehold.co/600x400/green/white?text=I4Food',
    imageKey: 'portfolio/i4food.jpg',
    category: 'Research',
    featured: true,
    tags: ['Food Systems', 'Climate Research', 'Sustainable Agriculture', 'Food Security', 'Research Institute', 'Africa']
  },
  {
    title: 'WeShop254',
    description: 'E-commerce platform specializing in home and living products including duvets, pillows, cookware, and household items with quality products at affordable prices in Kenya.',
    url: 'https://weshop254.co.ke/',
    imageUrl: 'https://placehold.co/600x400/purple/white?text=WeShop254',
    imageKey: 'portfolio/weshop254.jpg',
    category: 'E-commerce',
    featured: false,
    tags: ['E-commerce', 'Home Products', 'Living Products', 'Household Items', 'Kenya', 'Online Shopping']
  }
];

// Function to check if a website already exists
async function websiteExists(url) {
  const existing = await prisma.websitePortfolio.findFirst({
    where: { url: url }
  });
  return !!existing;
}

// Function to create or update a website portfolio item
async function createOrUpdateWebsiteItem(website, source = 'client') {
  try {
    // Check if website exists
    const existing = await prisma.websitePortfolio.findFirst({
      where: { url: website.url }
    });

    if (existing) {
      // Update existing website
      const updatedWebsite = await prisma.websitePortfolio.update({
        where: { id: existing.id },
        data: {
          title: website.title,
          description: website.description,
          imageUrl: website.imageUrl,
          imageKey: website.imageKey,
          category: website.category,
          featured: website.featured,
          tags: website.tags
        }
      });
      console.log(`🔄 Updated ${source}: ${updatedWebsite.title}`);
      return updatedWebsite;
    } else {
      // Create new website
      const newWebsite = await prisma.websitePortfolio.create({
        data: website
      });
      console.log(`✅ Created ${source}: ${newWebsite.title}`);
      return newWebsite;
    }
  } catch (error) {
    console.error(`❌ Error processing ${source} "${website.title}":`, error.message);
    return null;
  }
}

// Function to remove non-working example websites
async function removeExampleWebsites() {
  try {
    const exampleUrls = [
      'https://fashionstore.example.com',
      'https://restaurant.example.com',
      'https://realestate.example.com',
      'https://healthclinic.example.com',
      'https://eduplatform.example.com',
      'https://corporate.example.com',
      'https://fitnessgym.example.com',
      'https://travelagency.example.com',
      'https://techstartup.example.com',
      'https://nonprofit.example.com',
      'https://photography.example.com',
      'https://legalfirm.example.com',
      'https://eventmanager.example.com',
      'https://financial.example.com',
      'https://manufacturing.example.com',
      'https://saasapp.example.com',
      'https://galaidh.com',
      'https://top23security.com',
      'https://reucherafrica.co.ke',
      'https://i4food.org',
      'https://weshop254.co.ke',
      'https://knightswiftlogistics.com',
      'https://mrlmotors.com'
    ];

    const deletedCount = await prisma.websitePortfolio.deleteMany({
      where: {
        url: {
          in: exampleUrls
        }
      }
    });

    console.log(`🗑️ Removed ${deletedCount.count} non-working/example websites`);
    return deletedCount.count;
  } catch (error) {
    console.error('❌ Error removing example websites:', error);
    return 0;
  }
}

// Main seeding function
async function seedCompleteWebsitePortfolio() {
  console.log('🚀 Starting Complete Website Portfolio Update...');
  console.log('═══════════════════════════════════════════════════════════════');

  try {
    let totalCreated = 0;
    let totalUpdated = 0;
    let totalRemoved = 0;

    // Remove non-working example websites
    console.log('\n🗑️ Removing Non-Working Example Websites...');
    console.log('───────────────────────────────────────────────────────────────');
    totalRemoved = await removeExampleWebsites();

    console.log('\n🇰🇪 Processing Real Client Website Portfolio Items...');
    console.log('───────────────────────────────────────────────────────────────');

    // Process real client websites
    for (const website of realClientWebsites) {
      const exists = await websiteExists(website.url);
      const result = await createOrUpdateWebsiteItem(website, 'client');
      
      if (result) {
        if (exists) {
          totalUpdated++;
        } else {
          totalCreated++;
        }
      }
    }

    // Generate comprehensive statistics
    console.log('\n🎉 Website Portfolio Update Completed Successfully!');
    console.log('═══════════════════════════════════════════════════════════════');
    
    // Summary statistics
    console.log('📊 UPDATE SUMMARY:');
    console.log(`   Total Websites Processed: ${realClientWebsites.length}`);
    console.log(`   ✅ Websites Created: ${totalCreated}`);
    console.log(`   🔄 Websites Updated: ${totalUpdated}`);
    console.log(`   🗑️ Websites Removed: ${totalRemoved}`);

    // Get final database statistics
    const finalCount = await prisma.websitePortfolio.count();
    const featuredCount = await prisma.websitePortfolio.count({
      where: { featured: true }
    });

    console.log('\n📈 DATABASE STATISTICS:');
    console.log(`   Total Portfolio Items: ${finalCount}`);
    console.log(`   Featured Items: ${featuredCount}`);
    console.log(`   Regular Items: ${finalCount - featuredCount}`);

    // Category breakdown
    const categoryStats = await prisma.websitePortfolio.groupBy({
      by: ['category'],
      _count: { id: true },
      orderBy: { _count: { id: 'desc' } }
    });

    console.log('\n🏷️ CATEGORY BREAKDOWN:');
    categoryStats.forEach((stat, index) => {
      const category = stat.category || 'Uncategorized';
      const count = stat._count.id;
      const percentage = ((count / finalCount) * 100).toFixed(1);
      console.log(`   ${(index + 1).toString().padStart(2, ' ')}. ${category.padEnd(20)} ${count.toString().padStart(2)} items (${percentage}%)`);
    });

    // Recent items
    const recentItems = await prisma.websitePortfolio.findMany({
      take: 5,
      orderBy: { updatedAt: 'desc' },
      select: { title: true, category: true, featured: true, url: true }
    });

    console.log('\n📅 RECENTLY UPDATED:');
    recentItems.forEach((item, index) => {
      const featured = item.featured ? '⭐' : '  ';
      const domain = new URL(item.url).hostname;
      console.log(`   ${index + 1}. ${featured} ${item.title.substring(0, 30).padEnd(32)} (${item.category}) - ${domain}`);
    });

    console.log('\n💡 ACCESS YOUR PORTFOLIO:');
    console.log('   Admin Panel: /admin/website-portfolio');
    console.log('   Public View: /portfolio');
    console.log('═══════════════════════════════════════════════════════════════');

  } catch (error) {
    console.error('❌ Error during website portfolio update:', error);
    throw error;
  }
}

// Handle script execution
if (require.main === module) {
  seedCompleteWebsitePortfolio()
    .then(() => {
      console.log('\n✨ Complete website portfolio update finished successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Update failed:', error);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
}

module.exports = { 
  seedCompleteWebsitePortfolio,
  realClientWebsites
}; 