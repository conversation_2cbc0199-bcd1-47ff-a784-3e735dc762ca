#!/usr/bin/env node

/**
 * Performance Optimization Setup Script
 * 
 * This script helps set up and configure the performance optimizations
 * for the Financial Suite application.
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

function log(message, type = 'INFO') {
  const colors = {
    INFO: '\x1b[36m',
    SUCCESS: '\x1b[32m',
    WARNING: '\x1b[33m',
    ERROR: '\x1b[31m',
    RESET: '\x1b[0m'
  };
  
  console.log(`${colors[type]}[${type}]${colors.RESET} ${message}`);
}

async function checkPrerequisites() {
  log('Checking prerequisites...');
  
  const checks = [
    { name: 'Node.js', check: () => process.version },
    { name: 'npm', check: () => {
      try {
        require('child_process').execSync('npm --version', { stdio: 'pipe' });
        return true;
      } catch {
        return false;
      }
    }},
    { name: 'Sharp (for image optimization)', check: () => {
      try {
        require('sharp');
        return true;
      } catch {
        return false;
      }
    }},
  ];
  
  let allPassed = true;
  
  for (const check of checks) {
    try {
      const result = check.check();
      if (result) {
        log(`✓ ${check.name}: ${typeof result === 'string' ? result : 'Available'}`, 'SUCCESS');
      } else {
        log(`✗ ${check.name}: Not available`, 'ERROR');
        allPassed = false;
      }
    } catch (error) {
      log(`✗ ${check.name}: Error - ${error.message}`, 'ERROR');
      allPassed = false;
    }
  }
  
  return allPassed;
}

async function setupEnvironmentVariables() {
  log('Setting up environment variables...');
  
  const envPath = path.join(process.cwd(), '.env');
  const envExamplePath = path.join(process.cwd(), '.env.example');
  
  // Check if .env exists
  if (!fs.existsSync(envPath)) {
    if (fs.existsSync(envExamplePath)) {
      log('Copying .env.example to .env...', 'INFO');
      fs.copyFileSync(envExamplePath, envPath);
    } else {
      log('Creating new .env file...', 'INFO');
      fs.writeFileSync(envPath, '# Financial Suite Environment Variables\n');
    }
  }
  
  const envContent = fs.readFileSync(envPath, 'utf8');
  let updatedContent = envContent;
  
  // Performance-related environment variables
  const performanceVars = [
    { key: 'DB_POOL_MAX', default: '20', description: 'Maximum database connections' },
    { key: 'DB_POOL_MIN', default: '5', description: 'Minimum database connections' },
    { key: 'DB_IDLE_TIMEOUT', default: '30000', description: 'Database idle timeout (ms)' },
    { key: 'REDIS_HOST', default: 'localhost', description: 'Redis host (optional)' },
    { key: 'REDIS_PORT', default: '6379', description: 'Redis port' },
    { key: 'CDN_ENABLED', default: 'false', description: 'Enable CDN optimization' },
    { key: 'CDN_PROVIDER', default: 'cloudflare', description: 'CDN provider (cloudflare/aws/custom)' },
  ];
  
  log('Configuring performance variables...');
  
  for (const variable of performanceVars) {
    if (!envContent.includes(`${variable.key}=`)) {
      const value = await question(`${variable.description} (${variable.key}) [${variable.default}]: `);
      const finalValue = value.trim() || variable.default;
      updatedContent += `\n${variable.key}="${finalValue}"`;
      log(`Set ${variable.key}=${finalValue}`, 'SUCCESS');
    } else {
      log(`${variable.key} already configured`, 'INFO');
    }
  }
  
  if (updatedContent !== envContent) {
    fs.writeFileSync(envPath, updatedContent);
    log('Environment variables updated', 'SUCCESS');
  }
}

async function setupRedis() {
  log('Redis Setup', 'INFO');
  
  const useRedis = await question('Do you want to set up Redis for caching? (y/N): ');
  
  if (useRedis.toLowerCase() === 'y' || useRedis.toLowerCase() === 'yes') {
    log('Redis setup instructions:', 'INFO');
    console.log(`
1. Install Redis:
   - Ubuntu/Debian: sudo apt install redis-server
   - macOS: brew install redis
   - Windows: Download from https://redis.io/download
   
2. Start Redis:
   - Linux/macOS: redis-server
   - Windows: redis-server.exe
   
3. Test connection:
   - redis-cli ping
   
4. Configure Redis in .env:
   - REDIS_HOST=localhost
   - REDIS_PORT=6379
   - REDIS_PASSWORD= (if authentication is enabled)
    `);
    
    const redisConfigured = await question('Have you configured Redis? (y/N): ');
    if (redisConfigured.toLowerCase() === 'y') {
      log('Redis configuration noted', 'SUCCESS');
    }
  } else {
    log('Skipping Redis setup - will use in-memory cache fallback', 'WARNING');
  }
}

async function setupCDN() {
  log('CDN Setup', 'INFO');
  
  const useCDN = await question('Do you want to set up CDN for image optimization? (y/N): ');
  
  if (useCDN.toLowerCase() === 'y' || useCDN.toLowerCase() === 'yes') {
    const provider = await question('CDN Provider (cloudflare/aws/custom) [cloudflare]: ');
    const finalProvider = provider.trim() || 'cloudflare';
    
    log(`Setting up ${finalProvider} CDN...`, 'INFO');
    
    switch (finalProvider) {
      case 'cloudflare':
        console.log(`
Cloudflare Setup:
1. Sign up at https://cloudflare.com
2. Add your domain to Cloudflare
3. Get your Zone ID from the dashboard
4. Create an API token with Zone:Zone:Read and Zone:Cache Purge permissions
5. Configure in .env:
   - CDN_ENABLED=true
   - CDN_PROVIDER=cloudflare
   - CDN_BASE_URL=https://your-domain.com
   - CDN_ZONE=your-zone-id
   - CLOUDFLARE_API_TOKEN=your-api-token
        `);
        break;
        
      case 'aws':
        console.log(`
AWS CloudFront Setup:
1. Create a CloudFront distribution
2. Configure origin to point to your application
3. Set up behaviors for image optimization
4. Configure in .env:
   - CDN_ENABLED=true
   - CDN_PROVIDER=aws
   - CDN_BASE_URL=https://your-distribution.cloudfront.net
        `);
        break;
        
      default:
        console.log(`
Custom CDN Setup:
1. Configure your CDN to point to your application
2. Set up image optimization if supported
3. Configure in .env:
   - CDN_ENABLED=true
   - CDN_PROVIDER=custom
   - CDN_BASE_URL=https://your-cdn-domain.com
        `);
    }
  } else {
    log('Skipping CDN setup - images will be served directly', 'WARNING');
  }
}

async function setupMonitoring() {
  log('Performance Monitoring Setup', 'INFO');
  
  const setupMonitoring = await question('Do you want to set up performance monitoring? (Y/n): ');
  
  if (setupMonitoring.toLowerCase() !== 'n' && setupMonitoring.toLowerCase() !== 'no') {
    // Create logs directory
    const logsDir = path.join(process.cwd(), 'logs');
    if (!fs.existsSync(logsDir)) {
      fs.mkdirSync(logsDir, { recursive: true });
      log('Created logs directory', 'SUCCESS');
    }
    
    log('Performance monitoring configured', 'SUCCESS');
    console.log(`
Monitoring Commands:
- Start monitoring: npm run monitor:performance
- Run as background process: npm run monitor:start
- View logs: npm run monitor:logs
- Stop monitoring: npm run monitor:stop

Health Check Endpoints:
- Basic: GET /api/health
- Detailed: POST /api/health?detailed=true
    `);
  }
}

async function runTests() {
  log('Running performance tests...', 'INFO');
  
  try {
    // Test database connection
    log('Testing database connection...', 'INFO');
    const { checkDatabaseHealth } = require('../src/lib/db');
    const dbHealth = await checkDatabaseHealth();
    
    if (dbHealth.healthy) {
      log('✓ Database connection successful', 'SUCCESS');
    } else {
      log(`✗ Database connection failed: ${dbHealth.error}`, 'ERROR');
    }
  } catch (error) {
    log(`Database test error: ${error.message}`, 'WARNING');
  }
  
  try {
    // Test Redis connection
    log('Testing Redis connection...', 'INFO');
    const { getRedisClient } = require('../src/lib/redis');
    const redis = getRedisClient();
    
    if (redis) {
      await redis.ping();
      log('✓ Redis connection successful', 'SUCCESS');
    } else {
      log('Redis not configured - using in-memory cache', 'WARNING');
    }
  } catch (error) {
    log(`Redis test error: ${error.message}`, 'WARNING');
  }
  
  // Test image optimization
  try {
    log('Testing image optimization...', 'INFO');
    const sharp = require('sharp');
    
    // Create a test image buffer
    const testBuffer = await sharp({
      create: {
        width: 100,
        height: 100,
        channels: 3,
        background: { r: 255, g: 0, b: 0 }
      }
    }).png().toBuffer();
    
    // Test WebP conversion
    const webpBuffer = await sharp(testBuffer).webp().toBuffer();
    
    if (webpBuffer.length > 0) {
      log('✓ Image optimization working', 'SUCCESS');
    }
  } catch (error) {
    log(`Image optimization test error: ${error.message}`, 'ERROR');
  }
}

async function main() {
  console.log(`
╔══════════════════════════════════════════════════════════════╗
║                 Financial Suite Performance Setup           ║
║                                                              ║
║  This script will help you configure performance            ║
║  optimizations for your Financial Suite application.        ║
╚══════════════════════════════════════════════════════════════╝
  `);
  
  try {
    // Check prerequisites
    const prereqsPassed = await checkPrerequisites();
    if (!prereqsPassed) {
      log('Please install missing prerequisites before continuing', 'ERROR');
      process.exit(1);
    }
    
    // Setup steps
    await setupEnvironmentVariables();
    await setupRedis();
    await setupCDN();
    await setupMonitoring();
    
    // Run tests
    const runTestsNow = await question('Run performance tests now? (Y/n): ');
    if (runTestsNow.toLowerCase() !== 'n') {
      await runTests();
    }
    
    log('Performance optimization setup completed!', 'SUCCESS');
    console.log(`
Next Steps:
1. Review your .env file and adjust settings as needed
2. Restart your application to apply changes
3. Monitor performance using: npm run monitor:performance
4. Check health status at: http://localhost:3000/api/health

For detailed documentation, see: docs/PERFORMANCE_OPTIMIZATIONS.md
    `);
    
  } catch (error) {
    log(`Setup error: ${error.message}`, 'ERROR');
    process.exit(1);
  } finally {
    rl.close();
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { main };
