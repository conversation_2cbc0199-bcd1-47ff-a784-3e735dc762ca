#!/usr/bin/env node

const { PrismaClient } = require('@prisma/client');
const { sawaprintBannerProducts } = require('./extract-banner-prices');

const prisma = new PrismaClient();

// Enhanced banner products with additional catalogue-specific data
const catalogueBannerProducts = sawaprintBannerProducts.map(product => ({
  service: product.service,
  price: product.price,
  description: product.description,
  features: product.features || [
    'High-quality printing',
    'Durable materials',
    'Professional design',
    'Easy setup',
    'Custom branding available'
  ],
  icon: 'flag', // FontAwesome icon name without 'fa-' prefix
  popular: product.service.includes('Broad Base Media Banner') || product.service.includes('Telescopic'), // Mark some as popular
  category: 'Banners',
  imageUrl: null, // Will be set to null initially, can be updated later
  imageUrl2: null,
  imageUrl3: null
}));

async function seedCatalogueBanners() {
  console.log('🌱 Seeding catalogue with Sawaprint banner products...');
  
  try {
    // Clear existing banner items
    console.log('🗑️  Clearing existing banner items from catalogue...');
    const deletedCount = await prisma.catalogue.deleteMany({
      where: { category: 'Banners' }
    });
    console.log(`✅ Deleted ${deletedCount.count} existing banner items`);
    
    // Insert banner products
    console.log(`📝 Inserting ${catalogueBannerProducts.length} banner products...`);
    
    let successCount = 0;
    let errorCount = 0;
    
    for (const [index, product] of catalogueBannerProducts.entries()) {
      try {
        const createdProduct = await prisma.catalogue.create({
          data: {
            service: product.service,
            price: product.price,
            description: product.description,
            features: product.features,
            icon: product.icon,
            popular: product.popular,
            category: product.category,
            imageUrl: product.imageUrl,
            imageUrl2: product.imageUrl2,
            imageUrl3: product.imageUrl3
          }
        });
        
        console.log(`✅ ${index + 1}. Added: ${product.service} - KES ${product.price.toLocaleString()}`);
        successCount++;
        
      } catch (error) {
        console.error(`❌ Error adding ${product.service}:`, error.message);
        errorCount++;
      }
    }
    
    console.log('\n🎉 Catalogue banner seeding completed!');
    console.log(`📊 Results: ${successCount} successful, ${errorCount} errors`);
    
    // Summary statistics
    if (successCount > 0) {
      const prices = catalogueBannerProducts.map(p => p.price);
      const minPrice = Math.min(...prices);
      const maxPrice = Math.max(...prices);
      const avgPrice = Math.round(prices.reduce((a, b) => a + b, 0) / prices.length);
      
      console.log('\n📈 CATALOGUE SUMMARY:');
      console.log(`💰 Price Range: KES ${minPrice.toLocaleString()} - KES ${maxPrice.toLocaleString()}`);
      console.log(`📊 Average Price: KES ${avgPrice.toLocaleString()}`);
      console.log(`🏷️  Category: Banners`);
      console.log(`📦 Total Products: ${successCount}`);
      
      // Show popular items
      const popularItems = catalogueBannerProducts.filter(p => p.popular);
      if (popularItems.length > 0) {
        console.log(`⭐ Popular Items: ${popularItems.length}`);
        popularItems.forEach(item => {
          console.log(`   • ${item.service} - KES ${item.price.toLocaleString()}`);
        });
      }
    }
    
  } catch (error) {
    console.error('❌ Seeding failed:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Verify the seeding by checking what's in the database
async function verifyCatalogueSeeding() {
  console.log('\n🔍 Verifying catalogue seeding...');
  
  try {
    const bannerCount = await prisma.catalogue.count({
      where: { category: 'Banners' }
    });
    
    const allCatalogueItems = await prisma.catalogue.findMany({
      where: { category: 'Banners' },
      select: {
        id: true,
        service: true,
        price: true,
        category: true,
        popular: true,
        createdAt: true
      },
      orderBy: { price: 'desc' }
    });
    
    console.log(`✅ Found ${bannerCount} banner items in catalogue:`);
    console.log('='.repeat(80));
    
    allCatalogueItems.forEach((item, index) => {
      const popularMark = item.popular ? '⭐' : '  ';
      console.log(`${popularMark} ${index + 1}. ${item.service}`);
      console.log(`     Price: KES ${item.price.toLocaleString()}`);
      console.log(`     ID: ${item.id}`);
      console.log(`     Created: ${item.createdAt.toISOString().split('T')[0]}`);
      console.log('     ---');
    });
    
    // Total catalogue stats
    const totalCatalogueCount = await prisma.catalogue.count();
    console.log(`\n📊 Total catalogue items (all categories): ${totalCatalogueCount}`);
    
    // Category breakdown
    const categoryStats = await prisma.catalogue.groupBy({
      by: ['category'],
      _count: { category: true },
      orderBy: { _count: { category: 'desc' } }
    });
    
    console.log('\n📋 Category breakdown:');
    categoryStats.forEach(stat => {
      console.log(`   ${stat.category}: ${stat._count.category} items`);
    });
    
  } catch (error) {
    console.error('❌ Verification failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Main function
async function main() {
  console.log('🎯 CATALOGUE SEEDING - Sawaprint Banner Products\n');
  
  try {
    await seedCatalogueBanners();
    await verifyCatalogueSeeding();
    
    console.log('\n✅ Catalogue seeding completed successfully!');
    console.log('🚀 Your catalogue is now ready with banner products from Sawaprint.com');
    console.log('\n💡 Next steps:');
    console.log('   • Add product images via the admin panel');
    console.log('   • Review and update descriptions if needed');
    console.log('   • Set featured/popular products');
    console.log('   • Add more categories (drinkware, office essentials, etc.)');
    
  } catch (error) {
    console.error('❌ Main function failed:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { seedCatalogueBanners, verifyCatalogueSeeding, catalogueBannerProducts }; 