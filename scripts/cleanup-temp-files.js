/**
 * Artwork File Cleanup Script
 * 
 * This script moves artwork files that are stuck in TEMP directories to their proper order directories.
 * 
 * When to use:
 * - When files show in database but not in admin interface
 * - When file relocation fails during order creation
 * - For general cleanup of orphaned TEMP files
 * 
 * Usage: node scripts/cleanup-temp-files.js
 */

const { S3Client, ListObjectsV2Command, CopyObjectCommand, DeleteObjectCommand } = require('@aws-sdk/client-s3');
const { PrismaClient } = require('@prisma/client');

async function getDefaultStorageConfig() {
  const prisma = new PrismaClient();
  
  try {
    const config = await prisma.storageConfig.findFirst({
      where: { isDefault: true }
    });
    
    await prisma.$disconnect();
    return config;
  } catch (error) {
    console.error('Error getting storage config:', error);
    await prisma.$disconnect();
    return null;
  }
}

async function cleanupTempFiles() {
  console.log('🧹 Starting cleanup of artwork files in TEMP directories...\n');
  
  const config = await getDefaultStorageConfig();
  if (!config) {
    console.error('❌ No S3 configuration found');
    return;
  }
  
  const s3Client = new S3Client({
    region: config.region,
    endpoint: config.endpoint,
    credentials: {
      accessKeyId: config.accessKey,
      secretAccessKey: config.secretKey,
    },
    forcePathStyle: true
  });
  
  const prisma = new PrismaClient();
  
  try {
    // Find all files in TEMP directories
    const tempListCommand = new ListObjectsV2Command({
      Bucket: config.bucketName,
      Prefix: 'artwork/TEMP-',
      MaxKeys: 1000
    });
    
    const tempResponse = await s3Client.send(tempListCommand);
    const tempFiles = tempResponse.Contents || [];
    
    console.log(`📋 Found ${tempFiles.length} files in TEMP directories`);
    
    if (tempFiles.length === 0) {
      console.log('✅ No TEMP files to clean up');
      return;
    }
    
    // Get all orders to match against
    const orders = await prisma.order.findMany({
      select: {
        orderNumber: true,
        artworkFiles: true
      }
    });
    
    console.log(`📋 Checking against ${orders.length} orders...\n`);
    
    let movedFiles = 0;
    let errors = 0;
    
    for (const tempFile of tempFiles) {
      if (!tempFile.Key) continue;
      
      const fileName = tempFile.Key.split('/').pop() || '';
      console.log(`🔍 Processing: ${fileName}`);
      
      // Find which order this file belongs to
      const matchingOrder = orders.find(order => 
        order.artworkFiles.some(artworkFile => 
          artworkFile.includes(fileName) || 
          fileName.includes(artworkFile) ||
          // Match by timestamp and random parts
          (() => {
            const artworkParts = artworkFile.split('-');
            const fileParts = fileName.split('-');
            return artworkParts.length >= 2 && fileParts.length >= 2 && 
                   artworkParts[0] === fileParts[0] && artworkParts[1] === fileParts[1];
          })()
        )
      );
      
      if (!matchingOrder) {
        console.log(`❓ No matching order found for: ${fileName}`);
        continue;
      }
      
      console.log(`✅ Found matching order: ${matchingOrder.orderNumber}`);
      
      // Determine the correct filename and destination
      const matchingArtworkFile = matchingOrder.artworkFiles.find(artworkFile => 
        artworkFile.includes(fileName) || fileName.includes(artworkFile) ||
        (() => {
          const artworkParts = artworkFile.split('-');
          const fileParts = fileName.split('-');
          return artworkParts.length >= 2 && fileParts.length >= 2 && 
                 artworkParts[0] === fileParts[0] && artworkParts[1] === fileParts[1];
        })()
      );
      
      const correctFileName = matchingArtworkFile || fileName;
      const destinationKey = `artwork/${matchingOrder.orderNumber}/${correctFileName}`;
      
      console.log(`🔄 Moving: ${tempFile.Key} → ${destinationKey}`);
      
      try {
        // Copy file to correct location
        const copyCommand = new CopyObjectCommand({
          Bucket: config.bucketName,
          CopySource: `${config.bucketName}/${tempFile.Key}`,
          Key: destinationKey,
          ACL: 'public-read'
        });
        
        await s3Client.send(copyCommand);
        
        // Delete original file
        const deleteCommand = new DeleteObjectCommand({
          Bucket: config.bucketName,
          Key: tempFile.Key
        });
        
        await s3Client.send(deleteCommand);
        
        console.log(`✅ Successfully moved: ${fileName}`);
        movedFiles++;
        
      } catch (error) {
        console.error(`❌ Failed to move ${fileName}:`, error.message);
        errors++;
      }
      
      console.log(''); // Empty line for readability
    }
    
    console.log(`\n🎯 Cleanup Summary:`);
    console.log(`   Files moved: ${movedFiles}`);
    console.log(`   Errors: ${errors}`);
    console.log(`   Total processed: ${tempFiles.length}`);
    
  } catch (error) {
    console.error('❌ Error during cleanup:', error);
  } finally {
    await prisma.$disconnect();
  }
}

cleanupTempFiles(); 