#!/bin/bash

# Quick Git Conflict Resolution Script
# This script safely resolves the branch divergence using rebase

echo "🔧 Quick Git Conflict Resolution"
echo "================================"
echo ""

# Check if we're in a git repository
if ! git rev-parse --git-dir >/dev/null 2>&1; then
    echo "❌ Error: Not in a git repository"
    exit 1
fi

# Show current status
echo "📊 Current Git Status:"
git status --short
echo ""

# Create a backup branch with timestamp
BACKUP_BRANCH="backup-$(date +%Y%m%d-%H%M%S)"
echo "💾 Creating backup branch: $BACKUP_BRANCH"
git checkout -b "$BACKUP_BRANCH"
git checkout main
echo "✅ Backup created successfully"
echo ""

# Check if we can fetch from remote
echo "📥 Fetching latest changes from remote..."
if git fetch origin; then
    echo "✅ Fetch successful"
else
    echo "⚠️  Warning: Could not fetch from remote. Continuing with local resolution."
fi
echo ""

# Show what we're dealing with
echo "📋 Your local commits (not on remote):"
git log origin/main..HEAD --oneline --no-merges 2>/dev/null || git log --oneline -6
echo ""

echo "📋 Remote commits (not in local):"
git log HEAD..origin/main --oneline 2>/dev/null || echo "Unable to check remote commits"
echo ""

# Ask user for confirmation
echo "🤔 This will rebase your 6 local commits onto the remote changes."
echo "   Your work is safely backed up in branch: $BACKUP_BRANCH"
echo ""
read -p "Continue with rebase? (y/n): " -n 1 -r
echo ""

if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Rebase cancelled by user"
    echo "💡 Your backup is available at: git checkout $BACKUP_BRANCH"
    exit 0
fi

# Perform the rebase
echo ""
echo "🔄 Starting rebase..."
if git rebase origin/main; then
    echo "✅ Rebase completed successfully!"
    echo ""
    
    # Try to push
    echo "📤 Pushing rebased changes to remote..."
    if git push origin main --force-with-lease; then
        echo "✅ Push successful!"
    else
        echo "⚠️  Warning: Could not push to remote. You may need to:"
        echo "   git push origin main --force-with-lease"
    fi
    
    echo ""
    echo "🎉 Git conflict resolution completed!"
    echo "📊 Final status:"
    git status --short
    
else
    echo "❌ Rebase encountered conflicts!"
    echo ""
    echo "🔧 To resolve conflicts:"
    echo "   1. Edit the conflicted files (look for <<<<<<< ======= >>>>>>>)"
    echo "   2. Stage resolved files: git add <filename>"
    echo "   3. Continue rebase: git rebase --continue"
    echo "   4. Or abort: git rebase --abort"
    echo ""
    echo "🔍 Conflicted files:"
    git status --porcelain | grep "^UU\|^AA\|^DD" || echo "No conflict markers detected"
    echo ""
    echo "💾 Your original work is safe in: $BACKUP_BRANCH"
fi

echo ""
echo "🆘 If you need to recover your original state:"
echo "   git rebase --abort"
echo "   git checkout $BACKUP_BRANCH" 