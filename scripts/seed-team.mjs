/**
 * ES Module version for seeding team members
 * Run with: node scripts/seed-team.mjs
 */

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

const teamMembers = [
  {
    name: '<PERSON>',
    role: 'Creative Director & Founder',
    bio: 'With over 10 years of experience in graphic design and brand development, <PERSON> leads our creative vision and ensures every project meets the highest standards. She specializes in brand identity design and has worked with over 200+ businesses across Kenya.',
    imageKey: 'team/sarah-johnson.jpg',
    order: 1,
    linkedinUrl: 'https://linkedin.com/in/sarah<PERSON><PERSON><PERSON>-design',
    twitterUrl: 'https://twitter.com/sarahj_design',
    githubUrl: null,
    emailAddress: '<EMAIL>'
  },
  {
    name: '<PERSON>',
    role: 'Senior Web Developer',
    bio: '<PERSON> is our lead developer with expertise in modern web technologies including React, Next.js, and Node.js. He has been building scalable web applications for 8 years and leads our technical development team.',
    imageKey: 'team/michael-ochieng.jpg',
    order: 2,
    linkedinUrl: 'https://linkedin.com/in/michaelochieng-dev',
    twitterUrl: 'https://twitter.com/mike_codes',
    githubUrl: 'https://github.com/michaelochieng',
    emailAddress: '<EMAIL>'
  },
  {
    name: 'Grace Wanjiku',
    role: 'UI/UX Designer',
    bio: 'Grace brings fresh perspectives to user experience design with a focus on creating intuitive and beautiful interfaces. She has a keen eye for detail and specializes in mobile-first design approaches.',
    imageKey: 'team/grace-wanjiku.jpg',
    order: 3,
    linkedinUrl: 'https://linkedin.com/in/gracewanjiku-ux',
    twitterUrl: 'https://twitter.com/grace_designs',
    githubUrl: null,
    emailAddress: '<EMAIL>'
  },
  {
    name: 'David Kimani',
    role: 'Digital Marketing Specialist',
    bio: 'David manages our digital marketing campaigns and social media strategies. With 6 years of experience in digital marketing, he helps businesses grow their online presence and reach their target audiences effectively.',
    imageKey: 'team/david-kimani.jpg',
    order: 4,
    linkedinUrl: 'https://linkedin.com/in/davidkimani-marketing',
    twitterUrl: 'https://twitter.com/david_markets',
    githubUrl: null,
    emailAddress: '<EMAIL>'
  },
  {
    name: 'Faith Akinyi',
    role: 'Graphic Designer',
    bio: 'Faith specializes in print design and visual communications. She creates stunning brochures, flyers, and marketing materials that help businesses communicate their message effectively. Her attention to detail is exceptional.',
    imageKey: 'team/faith-akinyi.jpg',
    order: 5,
    linkedinUrl: 'https://linkedin.com/in/faithakinyi-design',
    twitterUrl: null,
    githubUrl: null,
    emailAddress: '<EMAIL>'
  },
  {
    name: 'James Mwangi',
    role: 'Full Stack Developer',
    bio: 'James is our versatile full-stack developer who works on both frontend and backend systems. He has strong experience with JavaScript, Python, and database design, making him invaluable for complex web applications.',
    imageKey: 'team/james-mwangi.jpg',
    order: 6,
    linkedinUrl: 'https://linkedin.com/in/jamesmwangi-fullstack',
    twitterUrl: 'https://twitter.com/james_codes',
    githubUrl: 'https://github.com/jamesmwangi',
    emailAddress: '<EMAIL>'
  },
  {
    name: 'Test User for Deletion',
    role: 'Test Role',
    bio: 'This is a test team member specifically created for testing the deletion functionality. You can safely delete this member to test the delete feature.',
    imageKey: 'team/test-user.jpg',
    order: 999,
    linkedinUrl: 'https://linkedin.com/in/testuser',
    twitterUrl: null,
    githubUrl: null,
    emailAddress: '<EMAIL>'
  }
];

async function main() {
  try {
    console.log('🌱 Starting team members seeding...');

    // Check if team members already exist
    const existingMembers = await prisma.teamMember.findMany();
    console.log(`📊 Found ${existingMembers.length} existing team members`);

    if (existingMembers.length > 0) {
      console.log('⚠️  Clearing existing team members...');
      await prisma.teamMember.deleteMany({});
      console.log('✅ Existing team members cleared');
    }

    console.log('👥 Creating team members...');

    // Create all team members
    const result = await prisma.teamMember.createMany({
      data: teamMembers,
      skipDuplicates: true
    });

    console.log(`🎉 Successfully created ${result.count} team members!`);

    // List created members
    const createdMembers = await prisma.teamMember.findMany({
      orderBy: { order: 'asc' }
    });

    console.log('\n📋 Created team members:');
    createdMembers.forEach((member, index) => {
      console.log(`${index + 1}. ${member.name} - ${member.role} (ID: ${member.id})`);
    });

    console.log('\n🔗 You can now:');
    console.log('   - Visit /admin/team to see the team members');
    console.log('   - Test the deletion functionality with "Test User for Deletion"');
    console.log('   - Update team member details');

  } catch (error) {
    console.error('🚨 Error during team members seeding:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

main()
  .catch((error) => {
    console.error('💥 Fatal error:', error);
    process.exit(1);
  }); 