const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkProducts() {
  try {
    const products = await prisma.catalogue.findMany({
      select: {
        id: true,
        service: true,
        pricingType: true,
        category: true
      }
    });
    
    console.log('Products in database:');
    products.forEach(product => {
      console.log(`- ID: ${product.id}, Service: "${product.service}", Type: ${product.pricingType || 'not set'}, Category: ${product.category || 'not set'}`);
    });
    
    console.log(`\nTotal products: ${products.length}`);
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkProducts(); 