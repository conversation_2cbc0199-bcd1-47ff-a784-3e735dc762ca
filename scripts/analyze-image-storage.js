#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

async function analyzeImageStorage() {
  try {
    console.log('📊 SCRAPED IMAGE STORAGE ANALYSIS');
    console.log('==================================');
    
    // Check if scraped data exists
    const scrapedFile = './scraped-products.json';
    if (!fs.existsSync(scrapedFile)) {
      console.log('❌ No scraped data found. Run the scraper first.');
      return;
    }
    
    const data = JSON.parse(fs.readFileSync(scrapedFile, 'utf8'));
    console.log(`Total products: ${data.length}`);
    
    const withImages = data.filter(p => p.imageUrl && p.imageUrl !== null);
    const withoutImages = data.filter(p => !p.imageUrl || p.imageUrl === null);
    
    console.log(`Products with images: ${withImages.length}`);
    console.log(`Products without images: ${withoutImages.length}`);
    
    if (withImages.length > 0) {
      console.log('\n📸 SAMPLE IMAGE URLS:');
      console.log('======================');
      withImages.slice(0, 5).forEach((p, i) => {
        console.log(`${i+1}. ${p.service}`);
        console.log(`   URL: ${p.imageUrl}`);
        console.log('');
      });
      
      console.log('\n🌐 IMAGE SOURCES:');
      console.log('=================');
      const sources = {};
      withImages.forEach(p => {
        try {
          const url = new URL(p.imageUrl);
          const domain = url.hostname;
          sources[domain] = (sources[domain] || 0) + 1;
        } catch (e) {
          sources['invalid-url'] = (sources['invalid-url'] || 0) + 1;
        }
      });
      
      Object.entries(sources).forEach(([domain, count]) => {
        console.log(`${domain}: ${count} images`);
      });
      
      console.log('\n📂 STORAGE METHOD:');
      console.log('==================');
      console.log('❌ Images are NOT downloaded locally');
      console.log('🔗 Images are stored as external URLs');
      console.log('⚠️  Images depend on source website availability');
      console.log('💡 Consider downloading images for local storage');
      
      console.log('\n📁 LOCAL STORAGE CHECK:');
      console.log('=======================');
      
      // Check common image storage locations
      const imageLocations = [
        './public/images',
        './public/images/scraped',
        './public/images/products',
        './uploads',
        './assets/images'
      ];
      
      imageLocations.forEach(location => {
        if (fs.existsSync(location)) {
          const files = fs.readdirSync(location);
          console.log(`✅ ${location}: ${files.length} files`);
        } else {
          console.log(`❌ ${location}: Does not exist`);
        }
      });
    }
    
  } catch (error) {
    console.error('❌ Error analyzing image storage:', error);
  }
}

analyzeImageStorage(); 