require('dotenv').config();
const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');
const prisma = new PrismaClient();

async function verifyPassword() {
  console.log('🔐 Verifying password...\n');
  
  try {
    // Get password from environment
    const password = process.env.ADMIN_PASSWORD;
    if (!password) {
      console.error('❌ ADMIN_PASSWORD not set in environment');
      return;
    }

    // Get user from database
    const user = await prisma.user.findFirst({
      where: {
        username: 'admin'
      },
      select: {
        username: true,
        passwordHash: true
      }
    });

    if (!user) {
      console.error('❌ Admin user not found');
      return;
    }

    // Verify password
    const isValid = await bcrypt.compare(password, user.passwordHash);
    
    console.log('Password verification results:');
    console.log('- Username:', user.username);
    console.log('- Password from ENV:', password);
    console.log('- Stored Hash:', user.passwordHash);
    console.log('- Password Valid:', isValid ? '✅ YES' : '❌ NO');

    if (!isValid) {
      // Create new hash for comparison
      const newHash = await bcrypt.hash(password, 12);
      console.log('\nDebug Info:');
      console.log('- New hash of ENV password:', newHash);
      console.log('- Hash Length:', newHash.length);
      console.log('- Hash Format Check:', newHash.startsWith('$2b$') ? '✅ Valid bcrypt format' : '❌ Invalid format');
    }
    
  } catch (error) {
    console.error('❌ Error verifying password:', error);
  } finally {
    await prisma.$disconnect();
  }
}

verifyPassword().catch(console.error); 