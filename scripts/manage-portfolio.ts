import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

interface PortfolioStats {
  total: number;
  featured: number;
  categories: { category: string; count: number }[];
}

async function getPortfolioStats(): Promise<PortfolioStats> {
  const [total, featured, categories] = await Promise.all([
    prisma.websitePortfolio.count(),
    prisma.websitePortfolio.count({ where: { featured: true } }),
    prisma.websitePortfolio.groupBy({
      by: ['category'],
      _count: { category: true }
    })
  ]);

  return {
    total,
    featured,
    categories: categories.map(cat => ({
      category: cat.category,
      count: cat._count.category
    }))
  };
}

async function listPortfolio(category?: string, featuredOnly: boolean = false) {
  console.log(`📋 ${featuredOnly ? 'Featured ' : ''}${category ? `${category} ` : ''}Portfolio Items:`);
  
  const where: any = {};
  if (category) where.category = category;
  if (featuredOnly) where.featured = true;

  const portfolioItems = await prisma.websitePortfolio.findMany({
    where,
    orderBy: [{ featured: 'desc' }, { createdAt: 'desc' }]
  });

  if (portfolioItems.length === 0) {
    console.log('   No portfolio items found.');
    return;
  }

  portfolioItems.forEach((item, index) => {
    const featured = item.featured ? '⭐' : '  ';
    console.log(`   ${index + 1}. ${featured} ${item.title}`);
    console.log(`      Category: ${item.category} | URL: ${item.url}`);
    console.log(`      Description: ${item.description}`);
    console.log('');
  });
}

async function showStats() {
  console.log('📊 Portfolio Statistics:');
  
  const stats = await getPortfolioStats();
  
  console.log(`   Total portfolio items: ${stats.total}`);
  console.log(`   Featured items: ${stats.featured}`);
  console.log(`   Regular items: ${stats.total - stats.featured}`);
  
  console.log('\n📂 Categories:');
  stats.categories.forEach(cat => {
    console.log(`   ${cat.category}: ${cat.count} items`);
  });
}

async function toggleFeatured(id: number, featured?: boolean) {
  try {
    const item = await prisma.websitePortfolio.findUnique({ where: { id } });
    
    if (!item) {
      console.log(`❌ Portfolio item with ID ${id} not found.`);
      return;
    }

    const newStatus = featured !== undefined ? featured : !item.featured;
    
    await prisma.websitePortfolio.update({
      where: { id },
      data: { featured: newStatus }
    });

    console.log(`✅ Portfolio item "${item.title}" ${newStatus ? 'featured' : 'unfeatured'}.`);
  } catch (error) {
    console.error('❌ Error toggling featured status:', error);
  }
}

async function deletePortfolioItem(id: number) {
  try {
    const item = await prisma.websitePortfolio.findUnique({ where: { id } });
    
    if (!item) {
      console.log(`❌ Portfolio item with ID ${id} not found.`);
      return;
    }

    await prisma.websitePortfolio.delete({ where: { id } });
    console.log(`✅ Portfolio item "${item.title}" deleted successfully.`);
  } catch (error) {
    console.error('❌ Error deleting portfolio item:', error);
  }
}

async function clearAllPortfolio() {
  try {
    const count = await prisma.websitePortfolio.count();
    
    if (count === 0) {
      console.log('ℹ️  No portfolio items to clear.');
      return;
    }

    await prisma.websitePortfolio.deleteMany();
    console.log(`✅ Cleared all ${count} portfolio items.`);
  } catch (error) {
    console.error('❌ Error clearing portfolio:', error);
  }
}

async function listCategories() {
  try {
    const categories = await prisma.websitePortfolio.groupBy({
      by: ['category'],
      _count: { category: true }
    });

    console.log('📂 Available Categories:');
    categories.forEach(cat => {
      console.log(`   ${cat.category}: ${cat._count.category} items`);
    });
  } catch (error) {
    console.error('❌ Error listing categories:', error);
  }
}

function showHelp() {
  console.log(`
🎯 Portfolio Management Script

Usage: npm run manage:portfolio [command] [options]

Commands:
  list [category] [--featured]  List portfolio items (optionally by category or featured only)
  stats                         Show portfolio statistics
  categories                    List all categories
  feature <id> [true|false]     Toggle featured status
  delete <id>                   Delete a portfolio item by ID
  clear                         Clear all portfolio items
  help                          Show this help message

Examples:
  npm run manage:portfolio list
  npm run manage:portfolio list e-commerce
  npm run manage:portfolio list -- --featured
  npm run manage:portfolio stats
  npm run manage:portfolio categories
  npm run manage:portfolio feature 1
  npm run manage:portfolio feature 1 true
  npm run manage:portfolio delete 1
  npm run manage:portfolio clear
  `);
}

async function main() {
  try {
    const args = process.argv.slice(2);
    const command = args[0];

    switch (command) {
      case 'list':
        const category = args[1] && !args[1].startsWith('--') ? args[1] : undefined;
        const featuredOnly = args.includes('--featured');
        await listPortfolio(category, featuredOnly);
        break;

      case 'stats':
        await showStats();
        break;

      case 'categories':
        await listCategories();
        break;

      case 'feature':
        const featureId = parseInt(args[1]);
        const featuredStatus = args[2] === 'true' ? true : args[2] === 'false' ? false : undefined;
        
        if (isNaN(featureId)) {
          console.log('❌ Please provide a valid portfolio item ID.');
          break;
        }
        
        await toggleFeatured(featureId, featuredStatus);
        break;

      case 'delete':
        const deleteId = parseInt(args[1]);
        
        if (isNaN(deleteId)) {
          console.log('❌ Please provide a valid portfolio item ID.');
          break;
        }
        
        await deletePortfolioItem(deleteId);
        break;

      case 'clear':
        await clearAllPortfolio();
        break;

      case 'help':
      case undefined:
        showHelp();
        break;

      default:
        console.log(`❌ Unknown command: ${command}`);
        showHelp();
        break;
    }
  } catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

main();
