/**
 * Seed script for Team Members
 * Creates realistic test data for the team section
 */

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

// Sample team members data
const teamMembers = [
  {
    name: '<PERSON>',
    role: 'Creative Director & Founder',
    bio: 'With over 10 years of experience in graphic design and brand development, <PERSON> leads our creative vision and ensures every project meets the highest standards. She specializes in brand identity design and has worked with over 200+ businesses across Kenya.',
    imageKey: 'team/sarah-johnson.jpg',
    order: 1,
    linkedinUrl: 'https://linkedin.com/in/sarah<PERSON><PERSON><PERSON>-design',
    twitterUrl: 'https://twitter.com/sarahj_design',
    githubUrl: null,
    emailAddress: '<EMAIL>'
  },
  {
    name: '<PERSON>',
    role: 'Senior Web Developer',
    bio: '<PERSON> is our lead developer with expertise in modern web technologies including React, Next.js, and Node.js. He has been building scalable web applications for 8 years and leads our technical development team.',
    imageKey: 'team/michael-ochieng.jpg',
    order: 2,
    linkedinUrl: 'https://linkedin.com/in/michaelochieng-dev',
    twitterUrl: 'https://twitter.com/mike_codes',
    githubUrl: 'https://github.com/michaelochieng',
    emailAddress: '<EMAIL>'
  },
  {
    name: 'Grace Wanjiku',
    role: 'UI/UX Designer',
    bio: 'Grace brings fresh perspectives to user experience design with a focus on creating intuitive and beautiful interfaces. She has a keen eye for detail and specializes in mobile-first design approaches.',
    imageKey: 'team/grace-wanjiku.jpg',
    order: 3,
    linkedinUrl: 'https://linkedin.com/in/gracewanjiku-ux',
    twitterUrl: 'https://twitter.com/grace_designs',
    githubUrl: null,
    emailAddress: '<EMAIL>'
  },
  {
    name: 'David Kimani',
    role: 'Digital Marketing Specialist',
    bio: 'David manages our digital marketing campaigns and social media strategies. With 6 years of experience in digital marketing, he helps businesses grow their online presence and reach their target audiences effectively.',
    imageKey: 'team/david-kimani.jpg',
    order: 4,
    linkedinUrl: 'https://linkedin.com/in/davidkimani-marketing',
    twitterUrl: 'https://twitter.com/david_markets',
    githubUrl: null,
    emailAddress: '<EMAIL>'
  },
  {
    name: 'Faith Akinyi',
    role: 'Graphic Designer',
    bio: 'Faith specializes in print design and visual communications. She creates stunning brochures, flyers, and marketing materials that help businesses communicate their message effectively. Her attention to detail is exceptional.',
    imageKey: 'team/faith-akinyi.jpg',
    order: 5,
    linkedinUrl: 'https://linkedin.com/in/faithakinyi-design',
    twitterUrl: null,
    githubUrl: null,
    emailAddress: '<EMAIL>'
  },
  {
    name: 'James Mwangi',
    role: 'Full Stack Developer',
    bio: 'James is our versatile full-stack developer who works on both frontend and backend systems. He has strong experience with JavaScript, Python, and database design, making him invaluable for complex web applications.',
    imageKey: 'team/james-mwangi.jpg',
    order: 6,
    linkedinUrl: 'https://linkedin.com/in/jamesmwangi-fullstack',
    twitterUrl: 'https://twitter.com/james_codes',
    githubUrl: 'https://github.com/jamesmwangi',
    emailAddress: '<EMAIL>'
  },
  {
    name: 'Linda Chebet',
    role: 'Brand Strategist',
    bio: 'Linda helps businesses define their brand identity and positioning. With a background in marketing and communications, she develops comprehensive brand strategies that resonate with target audiences.',
    imageKey: 'team/linda-chebet.jpg',
    order: 7,
    linkedinUrl: 'https://linkedin.com/in/lindachebet-brand',
    twitterUrl: 'https://twitter.com/linda_brands',
    githubUrl: null,
    emailAddress: '<EMAIL>'
  },
  {
    name: 'Peter Onyango',
    role: 'Project Manager',
    bio: 'Peter ensures all projects are delivered on time and within budget. With 7 years of project management experience, he coordinates between teams and clients to ensure smooth project execution.',
    imageKey: 'team/peter-onyango.jpg',
    order: 8,
    linkedinUrl: 'https://linkedin.com/in/peteronyango-pm',
    twitterUrl: null,
    githubUrl: null,
    emailAddress: '<EMAIL>'
  },
  {
    name: 'Alice Mutua',
    role: 'Content Creator & Copywriter',
    bio: 'Alice crafts compelling content and copy that engages audiences and drives action. She specializes in web content, blog writing, and social media content that aligns with brand voice and goals.',
    imageKey: 'team/alice-mutua.jpg',
    order: 9,
    linkedinUrl: 'https://linkedin.com/in/alicemutua-content',
    twitterUrl: 'https://twitter.com/alice_writes',
    githubUrl: null,
    emailAddress: '<EMAIL>'
  },
  {
    name: 'Robert Kariuki',
    role: 'SEO Specialist',
    bio: 'Robert optimizes websites for search engines and helps businesses improve their online visibility. His data-driven approach to SEO has helped numerous clients achieve top rankings on Google.',
    imageKey: 'team/robert-kariuki.jpg',
    order: 10,
    linkedinUrl: 'https://linkedin.com/in/robertkariuki-seo',
    twitterUrl: 'https://twitter.com/robert_seo',
    githubUrl: null,
    emailAddress: '<EMAIL>'
  }
];

async function seedTeamMembers() {
  try {
    console.log('🌱 Starting team members seeding...');

    // Check if team members already exist
    const existingMembers = await prisma.teamMember.findMany();
    console.log(`📊 Found ${existingMembers.length} existing team members`);

    if (existingMembers.length > 0) {
      console.log('⚠️  Team members already exist. Options:');
      console.log('1. Delete existing and reseed (run with --force flag)');
      console.log('2. Skip seeding to preserve existing data');
      
      // Check for --force flag
      const forceFlag = process.argv.includes('--force');
      if (forceFlag) {
        console.log('🗑️  Force flag detected. Deleting existing team members...');
        await prisma.teamMember.deleteMany({});
        console.log('✅ Existing team members deleted');
      } else {
        console.log('ℹ️  Skipping seeding to preserve existing data');
        console.log('   Use --force flag to delete existing data and reseed');
        return;
      }
    }

    console.log('👥 Creating team members...');

    // Create team members
    let created = 0;
    for (const memberData of teamMembers) {
      try {
        const teamMember = await prisma.teamMember.create({
          data: memberData
        });
        console.log(`✅ Created: ${teamMember.name} (${teamMember.role})`);
        created++;
      } catch (error) {
        console.error(`❌ Failed to create ${memberData.name}:`, error.message);
      }
    }

    console.log(`\n🎉 Successfully created ${created} team members!`);
    console.log('\n📊 Summary:');
    console.log(`   - Total members created: ${created}`);
    console.log(`   - Failed: ${teamMembers.length - created}`);

    // Verify the data
    const finalCount = await prisma.teamMember.count();
    console.log(`   - Final count in database: ${finalCount}`);

    console.log('\n🔗 You can now:');
    console.log('   - Visit /admin/team to see the team members');
    console.log('   - Test the deletion functionality');
    console.log('   - Update team member details');

  } catch (error) {
    console.error('🚨 Error during team members seeding:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Function to create a specific team member (for testing)
async function createTestMember() {
  try {
    const testMember = await prisma.teamMember.create({
      data: {
        name: 'Test User',
        role: 'Test Role',
        bio: 'This is a test team member created for debugging purposes. This member can be safely deleted.',
        imageKey: 'team/test-user.jpg',
        order: 999,
        linkedinUrl: 'https://linkedin.com/in/testuser',
        twitterUrl: null,
        githubUrl: null,
        emailAddress: '<EMAIL>'
      }
    });

    console.log('🧪 Test team member created:');
    console.log(`   - ID: ${testMember.id}`);
    console.log(`   - Name: ${testMember.name}`);
    console.log(`   - Role: ${testMember.role}`);
    console.log('\n✅ You can use this member to test deletion functionality');
    
    return testMember;
  } catch (error) {
    console.error('❌ Failed to create test member:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Main execution
async function main() {
  const command = process.argv[2];
  
  switch (command) {
    case 'test':
      await createTestMember();
      break;
    case 'seed':
    default:
      await seedTeamMembers();
      break;
  }
}

// Execute if run directly
if (require.main === module) {
  main()
    .catch((error) => {
      console.error('💥 Fatal error:', error);
      process.exit(1);
    });
}

module.exports = {
  seedTeamMembers,
  createTestMember,
  teamMembers
}; 