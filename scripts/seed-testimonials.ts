import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// Testimonials data from AboutTestimonials.tsx
const testimonialsData = [
  {
    name: "<PERSON>",
    location: "Nairobi, Kenya",
    project: "Website Development, 2 weeks",
    testimonial: "These guys always do awesome work - their developers know their stuff. Plus, the customer service is top-notch; they're responsive, helpful, and always ready to go the extra mile to make me happy.",
    rating: 5,
    company: "TechHub Kenya",
    active: true,
    order: 1
  },
  {
    name: "<PERSON>",
    location: "Mombasa, Kenya",
    project: "Branding, 1 month",
    testimonial: "You are the best! Thanks for another great branding project; I submitted it to my board yesterday and expect great feedback, as always. The logo design and brand guidelines are perfect.",
    rating: 5,
    company: "Coastal Ventures",
    active: true,
    order: 2
  },
  {
    name: "<PERSON>",
    location: "Kisumu, Kenya",
    project: "E-commerce Website, 3 weeks",
    testimonial: "For my retail business, I needed an online store that was both detailed and professionally built. Mocky Digital's service was excellent. The developer assigned to my project had a deep understanding of e-commerce and provided a comprehensive solution.",
    rating: 5,
    company: "Lake Region Retail",
    active: true,
    order: 3
  },
  {
    name: "<PERSON>",
    location: "Nakuru, Kenya",
    project: "Digital Marketing, 2 months",
    testimonial: "The digital marketing campaign exceeded our expectations. Their team was responsive and delivered results that perfectly aligned with our goals. The SEO improvements and social media strategy have significantly increased our online visibility.",
    rating: 5,
    company: "Highland Tours",
    active: true,
    order: 4
  },
  {
    name: "James",
    location: "Eldoret, Kenya",
    project: "Logo Design, 1 week",
    testimonial: "I needed a professional logo for my startup and Mocky Digital delivered beyond my expectations. The designer captured the essence of my brand perfectly. The process was smooth and the communication was excellent throughout.",
    rating: 5,
    company: "Upland Innovations",
    active: true,
    order: 5
  },
  {
    name: "Catherine",
    location: "Thika, Kenya",
    project: "UI/UX Design, 3 weeks",
    testimonial: "The user interface design for our app was exactly what we needed. Mocky Digital's attention to detail and understanding of user experience principles resulted in a design that our customers love. Highly recommended for any UI/UX work.",
    rating: 5,
    company: "Central Tech Solutions",
    active: true,
    order: 6
  }
];

async function main() {
  try {
    console.log('🌱 Starting testimonials seeding...');

    // Check if testimonials already exist
    const existingTestimonials = await prisma.testimonial.findMany();
    
    if (existingTestimonials.length > 0) {
      console.log(`⚠️  Found ${existingTestimonials.length} existing testimonials. Skipping seed to avoid duplicates.`);
      console.log('💡 If you want to re-seed, please delete existing testimonials first.');
      return;
    }

    // Create testimonials
    console.log(`📝 Creating ${testimonialsData.length} testimonials...`);
    
    for (const testimonialData of testimonialsData) {
      const testimonial = await prisma.testimonial.create({
        data: testimonialData
      });
      
      console.log(`✅ Created testimonial: ${testimonial.name} (${testimonial.company})`);
    }

    console.log('🎉 Testimonials seeding completed successfully!');
    
    // Display summary
    const totalTestimonials = await prisma.testimonial.count();
    const activeTestimonials = await prisma.testimonial.count({ where: { active: true } });
    
    console.log('\n📊 Summary:');
    console.log(`   Total testimonials: ${totalTestimonials}`);
    console.log(`   Active testimonials: ${activeTestimonials}`);
    console.log(`   Inactive testimonials: ${totalTestimonials - activeTestimonials}`);

  } catch (error) {
    console.error('❌ Error seeding testimonials:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seed function
main()
  .catch((error) => {
    console.error('❌ Seeding failed:', error);
    process.exit(1);
  });
