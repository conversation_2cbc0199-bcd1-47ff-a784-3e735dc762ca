#!/bin/bash

echo "🧹 Clearing browser and application caches to fix logo loading issues..."

# Clear Next.js cache but preserve static assets
echo "1. Clearing Next.js cache (preserving static assets)..."
rm -rf .next/cache 2>/dev/null || echo "No Next.js cache to clear"
# Don't delete .next/static as it breaks the app

# Clear PM2 logs (in case there are any error messages we missed)
echo "2. Clearing PM2 logs..."
pm2 flush

# Reload the application (not restart to avoid breaking static assets)
echo "3. Reloading the application..."
pm2 reload mocky-digital

# Clear browser cache headers by modifying file timestamp (only the standard logo path)
echo "4. Updating logo file timestamp to bust browser cache..."
touch public/images/logo.png
touch public/favicon.ico

# Add cache-busting query parameter to logo
echo "5. Creating cache-busting version..."
current_time=$(date +%s)
echo "Logo cache-buster timestamp: $current_time"

# Wait for reload
echo "6. Waiting for application to reload..."
sleep 8

# Test the endpoints
echo "7. Testing logo accessibility..."
echo "Direct Next.js app logo test:"
curl -I http://localhost:3000/images/logo.png?v=$current_time | head -5

echo -e "\nTesting with cache-busting parameter:"
curl -I http://localhost:3000/images/logo.png?cb=$current_time | head -3

echo -e "\n✅ Cache clearing complete!"
echo "📝 Next steps to fix admin logo:"
echo "   1. Hard refresh your browser (Ctrl+F5 or Cmd+Shift+R)"
echo "   2. Open browser DevTools -> Network tab -> Disable cache"
echo "   3. Clear browser data for mocky.co.ke (cookies, cache, etc.)"
echo "   4. Access the admin dashboard: http://mocky.co.ke/admin/dashboard"
echo "   5. If logo still doesn't load, add cache-busting parameter: ?v=$current_time"
echo ""
echo "🔧 Admin logo troubleshooting:"
echo "   - Check browser console for 404 errors"
echo "   - Verify logo loads directly: http://mocky.co.ke/images/logo.png"
echo "   - Try incognito/private browsing mode"

echo "Clearing Next.js cache..."

# Remove .next directory
rm -rf .next

# Clear npm cache
npm cache clean --force

# Clear node_modules/.cache if it exists
rm -rf node_modules/.cache

echo "Cache cleared successfully!"
echo "Please restart your development server with: npm run dev" 