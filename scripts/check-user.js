require('dotenv').config();
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkUser() {
  console.log('🔍 Checking user details...\n');
  
  try {
    const user = await prisma.user.findFirst({
      select: {
        id: true,
        username: true,
        email: true,
        active: true,
        passwordHash: true,
        role: {
          select: {
            name: true
          }
        }
      }
    });

    if (!user) {
      console.log('❌ No users found in the database');
      return;
    }

    console.log('User found:');
    console.log('- Username:', user.username);
    console.log('- Email:', user.email);
    console.log('- Active:', user.active);
    console.log('- Role:', user.role?.name);
    console.log('- Password Hash Length:', user.passwordHash.length);
    console.log('\nPassword Hash Preview:', user.passwordHash.substring(0, 20) + '...');
    
  } catch (error) {
    console.error('❌ Error checking user:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkUser().catch(console.error); 