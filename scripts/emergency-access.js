#!/usr/bin/env node

/**
 * Emergency Access Script
 * Temporarily disables rate limiting for admin dashboard access
 * Run this script if you're locked out due to rate limiting
 */

const fs = require('fs');
const path = require('path');

// Backup and modify middleware.ts
const middlewarePath = path.join(__dirname, 'src/middleware.ts');
const backupPath = path.join(__dirname, 'src/middleware.ts.backup');

console.log('🚨 EMERGENCY ACCESS MODE - Disabling rate limiting temporarily');

try {
  // Create backup
  const originalContent = fs.readFileSync(middlewarePath, 'utf8');
  fs.writeFileSync(backupPath, originalContent);
  
  // Modify rate limits to be extremely lenient
  const emergencyContent = originalContent.replace(
    /const RATE_LIMITS = \{[\s\S]*?\};/,
    `const RATE_LIMITS = {
  login: {
    max: 10000, // EMERGENCY: Extremely high limit
    window: 15 * 60 * 1000,
  },
  adminApi: {
    max: 10000, // EMERGENCY: Extremely high limit
    window: 5 * 60 * 1000,
  },
  publicApi: {
    max: 10000, // EMERGENCY: Extremely high limit
    window: 60 * 1000,
  },
  generalApi: {
    max: 10000, // EMERGENCY: Extremely high limit
    window: 60 * 1000,
  }
};`
  );
  
  fs.writeFileSync(middlewarePath, emergencyContent);
  
  console.log('✅ Rate limiting temporarily disabled');
  console.log('🔄 Restarting application...');
  
  // Restart the application
  const { exec } = require('child_process');
  exec('pm2 restart mocky-digital', (error, stdout, stderr) => {
    if (error) {
      console.error('❌ Error restarting application:', error);
      return;
    }
    console.log('✅ Application restarted');
    console.log('🌐 You should now be able to access your admin dashboard');
    console.log('');
    console.log('⚠️  IMPORTANT: This is temporary emergency access');
    console.log('⚠️  Run "node restore-security.js" to restore normal rate limiting');
    console.log('⚠️  Or manually restore from: src/middleware.ts.backup');
  });
  
} catch (error) {
  console.error('❌ Emergency access failed:', error);
  console.log('Manual steps:');
  console.log('1. Edit src/middleware.ts');
  console.log('2. Increase all "max" values in RATE_LIMITS to 10000');
  console.log('3. Run: pm2 restart mocky-digital');
} 