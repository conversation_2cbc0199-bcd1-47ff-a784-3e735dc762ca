#!/usr/bin/env node

/**
 * Performance Monitoring Script
 * 
 * This script monitors various performance metrics of the Financial Suite application:
 * - Database connection pool health
 * - Redis cache performance
 * - Image optimization metrics
 * - CDN performance
 * - Memory usage
 * - Response times
 */

const https = require('https');
const http = require('http');

// Configuration
const CONFIG = {
  baseUrl: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
  checkInterval: parseInt(process.env.MONITOR_INTERVAL || '60000'), // 1 minute
  alertThresholds: {
    responseTime: parseInt(process.env.ALERT_RESPONSE_TIME || '2000'), // 2 seconds
    memoryUsage: parseInt(process.env.ALERT_MEMORY_MB || '512'), // 512 MB
    dbConnections: parseInt(process.env.ALERT_DB_CONNECTIONS || '15'), // 15 connections
  },
  logFile: process.env.MONITOR_LOG_FILE || './logs/performance.log',
  enableAlerts: process.env.ENABLE_ALERTS === 'true',
};

// Metrics storage
let metrics = {
  responseTime: [],
  memoryUsage: [],
  dbConnections: [],
  cacheHitRate: [],
  imageOptimization: [],
};

// Utility functions
function log(message, level = 'INFO') {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] [${level}] ${message}`;
  console.log(logMessage);
  
  // Write to log file if specified
  if (CONFIG.logFile) {
    const fs = require('fs');
    const path = require('path');
    
    // Ensure log directory exists
    const logDir = path.dirname(CONFIG.logFile);
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }
    
    fs.appendFileSync(CONFIG.logFile, logMessage + '\n');
  }
}

function makeRequest(url) {
  return new Promise((resolve, reject) => {
    const startTime = Date.now();
    const client = url.startsWith('https') ? https : http;
    
    const req = client.get(url, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        const responseTime = Date.now() - startTime;
        try {
          const jsonData = JSON.parse(data);
          resolve({ data: jsonData, responseTime, statusCode: res.statusCode });
        } catch (error) {
          resolve({ data: null, responseTime, statusCode: res.statusCode, error: error.message });
        }
      });
    });
    
    req.on('error', (error) => {
      reject({ error: error.message, responseTime: Date.now() - startTime });
    });
    
    req.setTimeout(10000, () => {
      req.destroy();
      reject({ error: 'Request timeout', responseTime: Date.now() - startTime });
    });
  });
}

async function checkHealth() {
  try {
    log('Performing health check...');
    
    const healthUrl = `${CONFIG.baseUrl}/api/health`;
    const result = await makeRequest(healthUrl);
    
    if (result.error) {
      log(`Health check failed: ${result.error}`, 'ERROR');
      return null;
    }
    
    if (result.statusCode !== 200) {
      log(`Health check returned status ${result.statusCode}`, 'WARN');
      return null;
    }
    
    return result;
  } catch (error) {
    log(`Health check error: ${error.message}`, 'ERROR');
    return null;
  }
}

async function checkDetailedHealth() {
  try {
    const detailedUrl = `${CONFIG.baseUrl}/api/health?detailed=true`;
    const result = await makeRequest(detailedUrl);
    
    if (result.error || result.statusCode !== 200) {
      return null;
    }
    
    return result;
  } catch (error) {
    log(`Detailed health check error: ${error.message}`, 'ERROR');
    return null;
  }
}

function analyzeMetrics(healthData, responseTime) {
  const analysis = {
    alerts: [],
    recommendations: [],
    status: 'healthy',
  };
  
  // Check response time
  if (responseTime > CONFIG.alertThresholds.responseTime) {
    analysis.alerts.push(`High response time: ${responseTime}ms (threshold: ${CONFIG.alertThresholds.responseTime}ms)`);
    analysis.status = 'degraded';
  }
  
  // Check memory usage
  if (healthData.system && healthData.system.memory) {
    const memoryUsed = parseInt(healthData.system.memory.used.replace('MB', ''));
    if (memoryUsed > CONFIG.alertThresholds.memoryUsage) {
      analysis.alerts.push(`High memory usage: ${memoryUsed}MB (threshold: ${CONFIG.alertThresholds.memoryUsage}MB)`);
      analysis.status = 'degraded';
    }
  }
  
  // Check database connections
  if (healthData.services && healthData.services.database && healthData.services.database.connections) {
    const totalConnections = healthData.services.database.connections.total;
    if (totalConnections > CONFIG.alertThresholds.dbConnections) {
      analysis.alerts.push(`High database connections: ${totalConnections} (threshold: ${CONFIG.alertThresholds.dbConnections})`);
      analysis.status = 'degraded';
    }
  }
  
  // Check service health
  if (healthData.services) {
    Object.entries(healthData.services).forEach(([service, data]) => {
      if (data.status === 'unhealthy') {
        analysis.alerts.push(`Service ${service} is unhealthy: ${data.error || 'Unknown error'}`);
        analysis.status = 'unhealthy';
      }
    });
  }
  
  // Generate recommendations
  if (analysis.alerts.length === 0) {
    analysis.recommendations.push('System is performing well');
  } else {
    if (responseTime > 1000) {
      analysis.recommendations.push('Consider enabling CDN for better response times');
    }
    if (healthData.services && healthData.services.redis && healthData.services.redis.status === 'unhealthy') {
      analysis.recommendations.push('Consider setting up Redis for better caching performance');
    }
  }
  
  return analysis;
}

function storeMetrics(healthData, responseTime) {
  const timestamp = Date.now();
  
  // Store response time
  metrics.responseTime.push({ timestamp, value: responseTime });
  
  // Store memory usage
  if (healthData.system && healthData.system.memory) {
    const memoryUsed = parseInt(healthData.system.memory.used.replace('MB', ''));
    metrics.memoryUsage.push({ timestamp, value: memoryUsed });
  }
  
  // Store database connections
  if (healthData.services && healthData.services.database && healthData.services.database.connections) {
    metrics.dbConnections.push({ timestamp, value: healthData.services.database.connections.total });
  }
  
  // Keep only last 100 entries for each metric
  Object.keys(metrics).forEach(key => {
    if (metrics[key].length > 100) {
      metrics[key] = metrics[key].slice(-100);
    }
  });
}

function generateReport() {
  const report = {
    timestamp: new Date().toISOString(),
    summary: {},
    trends: {},
  };
  
  // Calculate averages for the last 10 measurements
  Object.entries(metrics).forEach(([key, values]) => {
    if (values.length > 0) {
      const recent = values.slice(-10);
      const average = recent.reduce((sum, item) => sum + item.value, 0) / recent.length;
      const min = Math.min(...recent.map(item => item.value));
      const max = Math.max(...recent.map(item => item.value));
      
      report.summary[key] = {
        average: Math.round(average * 100) / 100,
        min,
        max,
        count: recent.length,
      };
      
      // Calculate trend (simple linear regression)
      if (recent.length >= 5) {
        const trend = calculateTrend(recent);
        report.trends[key] = trend;
      }
    }
  });
  
  return report;
}

function calculateTrend(data) {
  const n = data.length;
  const sumX = data.reduce((sum, _, index) => sum + index, 0);
  const sumY = data.reduce((sum, item) => sum + item.value, 0);
  const sumXY = data.reduce((sum, item, index) => sum + index * item.value, 0);
  const sumXX = data.reduce((sum, _, index) => sum + index * index, 0);
  
  const slope = (n * sumXY - sumX * sumY) / (n * sumXX - sumX * sumX);
  
  return {
    direction: slope > 0.1 ? 'increasing' : slope < -0.1 ? 'decreasing' : 'stable',
    slope: Math.round(slope * 100) / 100,
  };
}

async function runMonitoring() {
  log('Starting performance monitoring...');
  
  const healthResult = await checkHealth();
  if (!healthResult) {
    log('Health check failed, skipping this cycle', 'WARN');
    return;
  }
  
  const { data: healthData, responseTime } = healthResult;
  
  // Analyze metrics and generate alerts
  const analysis = analyzeMetrics(healthData, responseTime);
  
  // Store metrics for trending
  storeMetrics(healthData, responseTime);
  
  // Log status
  log(`Health check completed - Status: ${analysis.status}, Response time: ${responseTime}ms`);
  
  // Log alerts
  if (analysis.alerts.length > 0) {
    analysis.alerts.forEach(alert => log(alert, 'ALERT'));
  }
  
  // Log recommendations
  analysis.recommendations.forEach(rec => log(`Recommendation: ${rec}`, 'INFO'));
  
  // Generate and log report every 10 cycles
  if (metrics.responseTime.length % 10 === 0) {
    const report = generateReport();
    log(`Performance Report: ${JSON.stringify(report, null, 2)}`, 'REPORT');
  }
}

// Main execution
async function main() {
  log('Performance Monitor started');
  log(`Configuration: ${JSON.stringify(CONFIG, null, 2)}`);
  
  // Run initial check
  await runMonitoring();
  
  // Set up interval monitoring
  setInterval(runMonitoring, CONFIG.checkInterval);
  
  // Graceful shutdown
  process.on('SIGINT', () => {
    log('Received SIGINT, shutting down gracefully...');
    const finalReport = generateReport();
    log(`Final Performance Report: ${JSON.stringify(finalReport, null, 2)}`, 'REPORT');
    process.exit(0);
  });
}

// Run if called directly
if (require.main === module) {
  main().catch(error => {
    log(`Fatal error: ${error.message}`, 'ERROR');
    process.exit(1);
  });
}

module.exports = {
  runMonitoring,
  generateReport,
  CONFIG,
};
