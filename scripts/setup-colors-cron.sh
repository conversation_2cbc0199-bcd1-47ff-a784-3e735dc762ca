#!/bin/bash

# Color Palette Scraping Automation Setup Script
# This script sets up automated color palette scraping from Coolors.co

set -e

# Configuration
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
SCRIPT_PATH="$PROJECT_ROOT/scripts/scrape-colors.js"
LOG_DIR="$PROJECT_ROOT/logs"
CRON_LOG="$LOG_DIR/colors-cron.log"
CRON_ERROR_LOG="$LOG_DIR/colors-cron-error.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1"
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO:${NC} $1"
}

# Check if running as root (not recommended for cron jobs)
check_user() {
    if [[ $EUID -eq 0 ]]; then
        warn "Running as root. Consider running as a regular user for security."
        read -p "Continue anyway? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
}

# Create necessary directories
setup_directories() {
    log "Setting up directories..."
    
    mkdir -p "$LOG_DIR"
    mkdir -p "$PROJECT_ROOT/data"
    
    # Set appropriate permissions
    chmod 755 "$LOG_DIR"
    chmod 755 "$PROJECT_ROOT/data"
    
    log "Directories created successfully"
}

# Check dependencies
check_dependencies() {
    log "Checking dependencies..."
    
    # Check if Node.js is installed
    if ! command -v node &> /dev/null; then
        error "Node.js is not installed. Please install Node.js first."
        exit 1
    fi
    
    # Check if npm is installed
    if ! command -v npm &> /dev/null; then
        error "npm is not installed. Please install npm first."
        exit 1
    fi
    
    # Check if the scraping script exists
    if [[ ! -f "$SCRIPT_PATH" ]]; then
        error "Scraping script not found at $SCRIPT_PATH"
        exit 1
    fi
    
    # Make the script executable
    chmod +x "$SCRIPT_PATH"
    
    log "Dependencies check passed"
}

# Install Node.js dependencies
install_dependencies() {
    log "Installing Node.js dependencies..."
    
    cd "$PROJECT_ROOT"
    
    # Check if package.json exists
    if [[ ! -f "package.json" ]]; then
        error "package.json not found in project root"
        exit 1
    fi
    
    # Install dependencies
    npm install
    
    log "Dependencies installed successfully"
}

# Test the scraping script
test_script() {
    log "Testing the color scraping script..."
    
    cd "$PROJECT_ROOT"
    
    # Run the script once to test
    if node "$SCRIPT_PATH"; then
        log "Script test successful"
    else
        warn "Script test failed, but continuing with setup"
    fi
}

# Setup cron job
setup_cron() {
    log "Setting up cron job..."
    
    # Create the cron command
    CRON_COMMAND="cd $PROJECT_ROOT && /usr/bin/node $SCRIPT_PATH >> $CRON_LOG 2>> $CRON_ERROR_LOG"
    
    # Cron schedule: Every 6 hours (at 00:00, 06:00, 12:00, 18:00)
    CRON_SCHEDULE="0 */6 * * *"
    
    # Full cron entry
    CRON_ENTRY="$CRON_SCHEDULE $CRON_COMMAND"
    
    # Check if cron job already exists
    if crontab -l 2>/dev/null | grep -q "scrape-colors.js"; then
        warn "Cron job for color scraping already exists"
        read -p "Replace existing cron job? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            # Remove existing cron job
            crontab -l 2>/dev/null | grep -v "scrape-colors.js" | crontab -
            log "Removed existing cron job"
        else
            info "Keeping existing cron job"
            return
        fi
    fi
    
    # Add new cron job
    (crontab -l 2>/dev/null; echo "$CRON_ENTRY") | crontab -
    
    log "Cron job added successfully"
    info "Schedule: Every 6 hours (00:00, 06:00, 12:00, 18:00)"
    info "Command: $CRON_COMMAND"
}

# Create log rotation script
setup_log_rotation() {
    log "Setting up log rotation..."
    
    # Create logrotate configuration
    LOGROTATE_CONFIG="/etc/logrotate.d/colors-scraping"
    
    if [[ -w "/etc/logrotate.d" ]]; then
        cat > "$LOGROTATE_CONFIG" << EOF
$CRON_LOG $CRON_ERROR_LOG {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 $(whoami) $(whoami)
}
EOF
        log "Log rotation configured"
    else
        warn "Cannot write to /etc/logrotate.d (insufficient permissions)"
        info "You may want to manually set up log rotation for:"
        info "  - $CRON_LOG"
        info "  - $CRON_ERROR_LOG"
    fi
}

# Display status and next steps
show_status() {
    log "Color palette scraping automation setup complete!"
    echo
    info "Configuration:"
    echo "  - Script: $SCRIPT_PATH"
    echo "  - Schedule: Every 6 hours"
    echo "  - Logs: $CRON_LOG"
    echo "  - Error logs: $CRON_ERROR_LOG"
    echo
    info "Manual commands:"
    echo "  - Run once: npm run colors:scrape"
    echo "  - View cron jobs: crontab -l"
    echo "  - View logs: tail -f $CRON_LOG"
    echo "  - View error logs: tail -f $CRON_ERROR_LOG"
    echo
    info "The next automatic run will occur at the next 6-hour interval (00:00, 06:00, 12:00, or 18:00)"
}

# Main execution
main() {
    log "Starting color palette scraping automation setup..."
    
    check_user
    setup_directories
    check_dependencies
    install_dependencies
    test_script
    setup_cron
    setup_log_rotation
    show_status
    
    log "Setup completed successfully!"
}

# Handle script interruption
trap 'error "Setup interrupted"; exit 1' INT TERM

# Run main function
main "$@"
