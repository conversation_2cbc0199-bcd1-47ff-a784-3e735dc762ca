#!/usr/bin/env node

/**
 * Script to seed sample blog posts using the blog categories
 * Run with: node scripts/seed-sample-blog-posts.js
 */

const { PrismaClient } = require('@prisma/client');
const slugify = require('slugify');

const prisma = new PrismaClient();

// Sample blog posts related to services
const sampleBlogPosts = [
  {
    title: 'The Ultimate Guide to Modern Web Development in 2024',
    content: `# The Ultimate Guide to Modern Web Development in 2024

Web development has evolved significantly over the past few years. In this comprehensive guide, we'll explore the latest trends, technologies, and best practices that are shaping the web development landscape in 2024.

## Key Technologies to Watch

### Frontend Frameworks
- **React 18+**: With concurrent features and improved performance
- **Next.js 14**: Full-stack React framework with App Router
- **Vue 3**: Composition API and improved TypeScript support
- **Svelte/SvelteKit**: Compile-time optimizations for better performance

### Backend Technologies
- **Node.js**: Still the go-to for JavaScript backends
- **Deno**: Secure runtime for JavaScript and TypeScript
- **Rust**: Growing popularity for high-performance applications
- **Go**: Excellent for microservices and APIs

## Best Practices for 2024

1. **Performance First**: Core Web Vitals are crucial for SEO
2. **Accessibility**: WCAG 2.1 compliance is no longer optional
3. **Security**: Zero-trust architecture and secure coding practices
4. **Mobile-First**: Progressive Web Apps (PWAs) are becoming standard

## Conclusion

The web development landscape continues to evolve rapidly. Staying updated with these trends will help you build better, faster, and more secure web applications.`,
    excerpt: 'Explore the latest trends, technologies, and best practices shaping web development in 2024, from modern frameworks to performance optimization.',
    category: 'Web Development',
    status: 'published',
    author: 'Mocky Digital Team',
    tags: ['web development', 'react', 'next.js', 'javascript', 'frontend', 'backend'],
    readingTime: 8,
    seoTitle: 'Modern Web Development Guide 2024 | Latest Trends & Best Practices',
    seoDescription: 'Complete guide to modern web development in 2024. Learn about the latest frameworks, technologies, and best practices for building modern web applications.',
    seoKeywords: ['web development 2024', 'modern web development', 'react', 'next.js', 'web development trends']
  },
  {
    title: 'Logo Design Trends That Will Dominate 2024',
    content: `# Logo Design Trends That Will Dominate 2024

Logo design continues to evolve with changing consumer preferences and technological advances. Here are the key trends shaping logo design in 2024.

## Top Logo Design Trends

### 1. Simplified Minimalism
Clean, simple designs that work across all platforms and sizes.

### 2. Dynamic and Animated Logos
Logos that adapt and move, especially for digital platforms.

### 3. Retro and Vintage Revival
Nostalgic designs with modern twists are making a comeback.

### 4. Bold Typography
Strong, custom typefaces that make a statement.

### 5. Sustainable and Eco-Friendly Themes
Designs that reflect environmental consciousness.

## Color Trends

- **Digital Gradients**: Vibrant, tech-inspired color combinations
- **Earth Tones**: Natural, sustainable color palettes
- **Monochromatic Schemes**: Single-color variations for versatility

## Technical Considerations

### Scalability
Your logo must work at any size, from business cards to billboards.

### Versatility
Design for multiple applications: print, digital, merchandise, signage.

### Timelessness
While following trends, ensure your logo won't look dated in 5 years.

## Conclusion

The best logos balance current trends with timeless design principles. Focus on creating something that represents your brand authentically while appealing to your target audience.`,
    excerpt: 'Discover the logo design trends that will shape 2024, from minimalism to dynamic animations and sustainable themes.',
    category: 'Graphic Design',
    status: 'published',
    author: 'Mocky Digital Team',
    tags: ['logo design', 'graphic design', 'branding', 'design trends', 'minimalism'],
    readingTime: 5,
    seoTitle: 'Logo Design Trends 2024 | Modern Branding & Visual Identity',
    seoDescription: 'Explore the top logo design trends for 2024. Learn about minimalism, dynamic logos, and sustainable design themes for modern branding.',
    seoKeywords: ['logo design trends 2024', 'logo design', 'branding trends', 'graphic design', 'visual identity']
  },
  {
    title: 'Digital Marketing Strategies for Small Businesses in Kenya',
    content: `# Digital Marketing Strategies for Small Businesses in Kenya

Digital marketing has become essential for small businesses in Kenya to compete and grow. Here's your comprehensive guide to effective digital marketing strategies.

## Understanding the Kenyan Digital Landscape

### Mobile-First Approach
With over 90% internet access via mobile devices, your strategy must be mobile-optimized.

### Social Media Dominance
- **WhatsApp**: Primary communication platform
- **Facebook**: Largest social network
- **Instagram**: Growing among younger demographics
- **TikTok**: Rapidly gaining popularity

## Key Digital Marketing Strategies

### 1. Search Engine Optimization (SEO)
Target local keywords and optimize for "near me" searches.

### 2. Social Media Marketing
- Create engaging content in local languages
- Use local hashtags and trends
- Partner with local influencers

### 3. Content Marketing
- Share valuable, locally relevant content
- Address local challenges and solutions
- Use storytelling to connect with your audience

### 4. Email Marketing
Build relationships through personalized email campaigns.

### 5. Pay-Per-Click (PPC) Advertising
- Google Ads for search visibility
- Facebook Ads for social reach
- Target specific locations and demographics

## Budget-Friendly Tips

1. **Start with organic social media**
2. **Focus on one platform initially**
3. **Create user-generated content**
4. **Leverage local partnerships**
5. **Use free tools like Google My Business**

## Measuring Success

Track these key metrics:
- Website traffic and conversions
- Social media engagement
- Email open and click rates
- Return on ad spend (ROAS)

## Conclusion

Digital marketing success in Kenya requires understanding local preferences, mobile optimization, and consistent engagement with your audience.`,
    excerpt: 'Learn effective digital marketing strategies specifically tailored for small businesses in Kenya, from SEO to social media marketing.',
    category: 'Digital Marketing',
    status: 'published',
    author: 'Mocky Digital Team',
    tags: ['digital marketing', 'kenya', 'small business', 'seo', 'social media', 'local marketing'],
    readingTime: 7,
    seoTitle: 'Digital Marketing for Small Businesses Kenya | Local SEO & Social Media',
    seoDescription: 'Comprehensive digital marketing guide for small businesses in Kenya. Learn SEO, social media marketing, and local advertising strategies.',
    seoKeywords: ['digital marketing kenya', 'small business marketing', 'kenya seo', 'social media marketing kenya', 'local digital marketing']
  }
];

async function seedSampleBlogPosts() {
  try {
    console.log('📝 Starting sample blog posts seeding...');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');

    // Get all blog categories to map category names to IDs
    const blogCategories = await prisma.blogCategory.findMany();
    const categoryMap = {};
    blogCategories.forEach(cat => {
      categoryMap[cat.name] = cat.id;
    });

    console.log(`📂 Found ${blogCategories.length} blog categories`);

    let createdCount = 0;
    let skippedCount = 0;

    for (const postData of sampleBlogPosts) {
      // Generate slug
      const slug = slugify(postData.title, { 
        lower: true, 
        strict: true,
        remove: /[*+~.()'"!:@]/g
      });

      // Check if post already exists
      const existingPost = await prisma.blogPost.findUnique({
        where: { slug }
      });

      if (existingPost) {
        console.log(`⏭️  Skipping "${postData.title}" (already exists)`);
        skippedCount++;
        continue;
      }

      // Find category ID
      const categoryId = categoryMap[postData.category];
      if (!categoryId) {
        console.log(`⚠️  Category "${postData.category}" not found, skipping post`);
        skippedCount++;
        continue;
      }

      // Create the blog post
      const newPost = await prisma.blogPost.create({
        data: {
          title: postData.title,
          slug: slug,
          content: postData.content.trim(),
          excerpt: postData.excerpt,
          category: postData.category,
          status: postData.status,
          author: postData.author,
          tags: postData.tags || [],
          readingTime: postData.readingTime,
          viewCount: Math.floor(Math.random() * 100) + 10, // Random view count for demo
          seoTitle: postData.seoTitle,
          seoDescription: postData.seoDescription,
          seoKeywords: postData.seoKeywords || [],
          publishedAt: new Date()
        }
      });

      console.log(`✅ Created: "${newPost.title}"`);
      console.log(`   Category: ${postData.category}`);
      console.log(`   Slug: ${newPost.slug}`);
      console.log(`   Reading Time: ${postData.readingTime} min`);
      console.log('');
      createdCount++;
    }

    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    console.log('🎉 Sample blog posts seeding completed!');
    console.log(`📊 Summary:`);
    console.log(`   ✅ Created: ${createdCount} posts`);
    console.log(`   ⏭️  Skipped: ${skippedCount} posts`);

    // Get final counts
    const totalPosts = await prisma.blogPost.count();
    const publishedPosts = await prisma.blogPost.count({
      where: { status: 'published' }
    });

    console.log(`   📝 Total posts in database: ${totalPosts}`);
    console.log(`   📰 Published posts: ${publishedPosts}`);

    console.log('\n🚀 Sample blog posts are now ready!');
    console.log('💡 You can view them at: /admin/blog');
    console.log('🌐 Public blog: /blog');

  } catch (error) {
    console.error('❌ Error seeding sample blog posts:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Handle script execution
if (require.main === module) {
  seedSampleBlogPosts()
    .then(() => {
      console.log('\n✨ Script completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Script failed:', error);
      process.exit(1);
    });
}

module.exports = { seedSampleBlogPosts, sampleBlogPosts }; 