# Blog Seeding Scripts Documentation

This directory contains scripts to seed your blog with categories and sample content related to your services.

## 📁 Available Scripts

### 1. `seed-blog-categories.js`
Seeds 20 comprehensive blog categories related to your services.

**Categories included:**
- Web Development
- Mobile App Development  
- Graphic Design
- Digital Marketing
- E-commerce
- SEO & Analytics
- Branding & Identity
- User Experience (UX)
- Business Technology
- Print Design
- Social Media
- Content Strategy
- Freelancing & Business
- Industry Trends
- Case Studies
- Tutorials & How-To
- Tools & Resources
- Client Education
- Portfolio Showcase
- Company News

### 2. `seed-sample-blog-posts.js`
Creates sample blog posts using the seeded categories.

**Sample posts included:**
- Modern Web Development Guide 2024
- Logo Design Trends 2024
- Digital Marketing for Small Businesses in Kenya

### 3. `seed-blog-complete.js`
Runs both category and blog post seeding in sequence.

## 🚀 How to Use

### Option 1: Seed Everything (Recommended)
```bash
node scripts/seed-blog-complete.js
```

### Option 2: Seed Categories Only
```bash
node scripts/seed-blog-categories.js
```

### Option 3: Seed Sample Posts Only
```bash
node scripts/seed-sample-blog-posts.js
```

## 📋 Prerequisites

1. **Database Setup**: Ensure your PostgreSQL database is running
2. **Prisma Migration**: Run the blog categories migration:
   ```bash
   npx prisma migrate dev --name add_blog_categories
   ```
3. **Dependencies**: Ensure you have the required packages:
   ```bash
   npm install slugify
   ```

## 🔧 Script Features

### Smart Duplicate Handling
- Scripts check for existing content before creating
- Skips duplicates automatically
- Provides detailed feedback on what was created vs skipped

### SEO Optimization
- Auto-generates SEO-friendly slugs
- Includes meta titles and descriptions
- Adds relevant keywords for each post

### Content Quality
- Professional, well-structured content
- Relevant to your service offerings
- Includes proper markdown formatting
- Reading time calculations

## 📊 What Gets Created

### Blog Categories
- **20 categories** covering all service areas
- **Unique slugs** for URL-friendly navigation
- **Detailed descriptions** for each category
- **Alphabetically sorted** for easy management

### Sample Blog Posts
- **Full-length articles** (1000+ words each)
- **SEO-optimized** titles and meta descriptions
- **Relevant tags** for better categorization
- **Reading time estimates**
- **Professional content** related to your services

## 🎯 Post-Seeding Steps

1. **Review Content**: Check the admin panel at `/admin/blog/categories`
2. **Customize**: Edit categories and posts to match your brand voice
3. **Add Images**: Upload featured images for blog posts
4. **Create More**: Use the categories to create additional content
5. **SEO Setup**: Configure additional SEO settings as needed

## 🔗 Access Points

After running the scripts, you can access:

- **Admin Blog Management**: `http://localhost:3000/admin/blog`
- **Category Management**: `http://localhost:3000/admin/blog/categories`
- **Public Blog**: `http://localhost:3000/blog`

## 🛠️ Troubleshooting

### Common Issues

1. **Database Connection Error**
   ```bash
   # Check if PostgreSQL is running
   sudo systemctl status postgresql
   
   # Restart if needed
   sudo systemctl restart postgresql
   ```

2. **Prisma Client Error**
   ```bash
   # Regenerate Prisma client
   npx prisma generate
   ```

3. **Migration Issues**
   ```bash
   # Reset and re-run migrations
   npx prisma migrate reset
   npx prisma migrate dev
   ```

### Script Logs
The scripts provide detailed logging:
- ✅ Success indicators
- ⏭️ Skip notifications  
- ❌ Error messages
- 📊 Summary statistics

## 🔄 Re-running Scripts

Scripts are designed to be re-run safely:
- **Categories**: Will skip existing categories by slug
- **Posts**: Will skip existing posts by slug
- **No Data Loss**: Existing content is preserved

## 📝 Customization

### Adding More Categories
Edit `seed-blog-categories.js` and add to the `blogCategories` array:

```javascript
{
  name: 'Your Category Name',
  description: 'Detailed description of the category'
}
```

### Adding More Sample Posts
Edit `seed-sample-blog-posts.js` and add to the `sampleBlogPosts` array:

```javascript
{
  title: 'Your Post Title',
  content: 'Full markdown content...',
  excerpt: 'Brief description...',
  category: 'Existing Category Name',
  // ... other fields
}
```

## 🎉 Success!

Once seeding is complete, you'll have a fully functional blog system with:
- Comprehensive category structure
- Professional sample content
- SEO-optimized setup
- Admin management interface
- Public blog display

Your blog is now ready for content creation and management! 