const { PrismaClient } = require('@prisma/client');
const { Decimal } = require('@prisma/client/runtime/library');

const prisma = new PrismaClient();

async function main() {
  console.log('🚀 Starting Financial Suite seeding...');

  try {
    // Clear existing data
    console.log('🗑️ Clearing existing financial data...');
    await prisma.receiptItem.deleteMany();
    await prisma.receipt.deleteMany();
    await prisma.invoiceItem.deleteMany();
    await prisma.invoice.deleteMany();
    await prisma.quoteItem.deleteMany();
    await prisma.quote.deleteMany();
    await prisma.service.deleteMany();

    // 1. Seed Services
    console.log('📦 Seeding Services...');
    const services = await Promise.all([
      // Design Services
      prisma.service.create({
        data: {
          name: 'Logo Design',
          description: 'Professional logo design with unlimited revisions',
          price: new Decimal('15000'),
          category: 'Design'
        }
      }),
      prisma.service.create({
        data: {
          name: 'Business Card Design',
          description: 'Professional business card design with print-ready files',
          price: new Decimal('3000'),
          category: 'Design'
        }
      }),
      prisma.service.create({
        data: {
          name: 'Flyer Design',
          description: 'Eye-catching flyer design for events and promotions',
          price: new Decimal('5000'),
          category: 'Design'
        }
      }),
      prisma.service.create({
        data: {
          name: 'Letterhead Design',
          description: 'Professional letterhead design with brand consistency',
          price: new Decimal('4000'),
          category: 'Design'
        }
      }),
      prisma.service.create({
        data: {
          name: 'Company Profile Design',
          description: 'Complete company profile design with brochure layout',
          price: new Decimal('25000'),
          category: 'Design'
        }
      }),

      // Web Development Services
      prisma.service.create({
        data: {
          name: 'Website Development',
          description: 'Custom website development with responsive design',
          price: new Decimal('80000'),
          category: 'Web Development'
        }
      }),
      prisma.service.create({
        data: {
          name: 'E-commerce Website',
          description: 'Full e-commerce solution with payment integration',
          price: new Decimal('150000'),
          category: 'Web Development'
        }
      }),
      prisma.service.create({
        data: {
          name: 'Website Maintenance',
          description: 'Monthly website maintenance and updates',
          price: new Decimal('10000'),
          category: 'Web Development'
        }
      }),

      // Digital Marketing Services
      prisma.service.create({
        data: {
          name: 'Social Media Management',
          description: 'Complete social media management for all platforms',
          price: new Decimal('20000'),
          category: 'Digital Marketing'
        }
      }),
      prisma.service.create({
        data: {
          name: 'Google Ads Campaign',
          description: 'Google Ads setup and management for 3 months',
          price: new Decimal('45000'),
          category: 'Digital Marketing'
        }
      }),

      // Consultation Services
      prisma.service.create({
        data: {
          name: 'Brand Strategy Consultation',
          description: 'Complete brand strategy and positioning consultation',
          price: new Decimal('35000'),
          category: 'Consultation'
        }
      }),
      prisma.service.create({
        data: {
          name: 'Digital Marketing Audit',
          description: 'Comprehensive digital marketing audit and recommendations',
          price: new Decimal('25000'),
          category: 'Consultation'
        }
      })
    ]);

    console.log(`✅ Created ${services.length} services`);

    // 2. Create sample customers data
    const customers = [
      {
        name: 'John Mwangi',
        phone: '+254712345678',
        email: '<EMAIL>'
      },
      {
        name: 'Sarah Wanjiku',
        phone: '+254723456789',
        email: '<EMAIL>'
      },
      {
        name: 'Peter Ochieng',
        phone: '+254734567890',
        email: '<EMAIL>'
      },
      {
        name: 'Grace Akinyi',
        phone: '+254745678901',
        email: '<EMAIL>'
      },
      {
        name: 'David Kariuki',
        phone: '+254756789012',
        email: '<EMAIL>'
      },
      {
        name: 'Elizabeth Nyambura',
        phone: '+254767890123',
        email: '<EMAIL>'
      }
    ];

    // 3. Seed Quotes
    console.log('💰 Seeding Quotes...');
    const quotes = [];
    
    for (let i = 0; i < 8; i++) {
      const customer = customers[i % customers.length];
      const randomServices = services.sort(() => 0.5 - Math.random()).slice(0, Math.floor(Math.random() * 3) + 1);
      
      let totalAmount = new Decimal('0');
      
      const quote = await prisma.quote.create({
        data: {
          quoteNumber: `QT-2024-${String(i + 1).padStart(3, '0')}`,
          customerName: customer.name,
          phoneNumber: customer.phone,
          email: customer.email,
          status: ['pending', 'approved', 'rejected', 'expired'][Math.floor(Math.random() * 4)],
          notes: `Quote for ${customer.name} - ${randomServices.map(s => s.name).join(', ')}`,
          validUntil: new Date(Date.now() + (30 * 24 * 60 * 60 * 1000)), // 30 days from now
          totalAmount: new Decimal('0') // Will be updated after items
        }
      });

      // Add quote items
      for (const service of randomServices) {
        const quantity = Math.floor(Math.random() * 3) + 1;
        const unitPrice = service.price;
        const totalPrice = unitPrice.mul(quantity);
        totalAmount = totalAmount.add(totalPrice);

        await prisma.quoteItem.create({
          data: {
            quoteId: quote.id,
            serviceId: service.id,
            quantity,
            unitPrice,
            totalPrice,
            description: `${service.name} - ${service.description}`
          }
        });
      }

      // Update quote total
      await prisma.quote.update({
        where: { id: quote.id },
        data: { totalAmount }
      });

      quotes.push({ ...quote, totalAmount });
    }

    console.log(`✅ Created ${quotes.length} quotes`);

    // 4. Seed Invoices (some from approved quotes, some standalone)
    console.log('📄 Seeding Invoices...');
    const invoices = [];

    // Create invoices from approved quotes
    const approvedQuotes = quotes.filter(q => q.status === 'approved').slice(0, 3);
    
    for (const quote of approvedQuotes) {
      const quoteData = await prisma.quote.findUnique({
        where: { id: quote.id },
        include: { items: { include: { service: true } } }
      });

      const invoice = await prisma.invoice.create({
        data: {
          invoiceNumber: `INV-2024-${String(invoices.length + 1).padStart(3, '0')}`,
          customerName: quoteData.customerName,
          phoneNumber: quoteData.phoneNumber,
          email: quoteData.email,
          totalAmount: quoteData.totalAmount,
          balance: quoteData.totalAmount,
          status: ['pending', 'paid', 'overdue'][Math.floor(Math.random() * 3)],
          notes: `Invoice generated from quote ${quoteData.quoteNumber}`,
          dueDate: new Date(Date.now() + (14 * 24 * 60 * 60 * 1000)), // 14 days from now
          quoteId: quoteData.id
        }
      });

      // Copy quote items to invoice items
      for (const item of quoteData.items) {
        await prisma.invoiceItem.create({
          data: {
            invoiceId: invoice.id,
            serviceId: item.serviceId,
            quantity: item.quantity,
            unitPrice: item.unitPrice,
            totalPrice: item.totalPrice,
            description: item.description
          }
        });
      }

      invoices.push(invoice);
    }

    // Create standalone invoices
    for (let i = 0; i < 5; i++) {
      const customer = customers[i % customers.length];
      const randomServices = services.sort(() => 0.5 - Math.random()).slice(0, Math.floor(Math.random() * 2) + 1);
      
      let totalAmount = new Decimal('0');
      
      const invoice = await prisma.invoice.create({
        data: {
          invoiceNumber: `INV-2024-${String(invoices.length + i + 1).padStart(3, '0')}`,
          customerName: customer.name,
          phoneNumber: customer.phone,
          email: customer.email,
          status: ['pending', 'paid', 'overdue'][Math.floor(Math.random() * 3)],
          notes: `Invoice for ${customer.name}`,
          dueDate: new Date(Date.now() + (14 * 24 * 60 * 60 * 1000)),
          totalAmount: new Decimal('0'), // Will be updated
          balance: new Decimal('0') // Will be updated
        }
      });

      // Add invoice items
      for (const service of randomServices) {
        const quantity = Math.floor(Math.random() * 2) + 1;
        const unitPrice = service.price;
        const totalPrice = unitPrice.mul(quantity);
        totalAmount = totalAmount.add(totalPrice);

        await prisma.invoiceItem.create({
          data: {
            invoiceId: invoice.id,
            serviceId: service.id,
            quantity,
            unitPrice,
            totalPrice,
            description: `${service.name} - ${service.description}`
          }
        });
      }

      // Update invoice total
      const amountPaid = Math.random() > 0.5 ? totalAmount : new Decimal('0');
      await prisma.invoice.update({
        where: { id: invoice.id },
        data: { 
          totalAmount,
          amountPaid,
          balance: totalAmount.sub(amountPaid)
        }
      });

      invoices.push(invoice);
    }

    console.log(`✅ Created ${invoices.length} invoices`);

    // 5. Seed Receipts
    console.log('🧾 Seeding Receipts...');
    const receipts = [];

    // Create receipts for paid invoices
    const paidInvoices = await prisma.invoice.findMany({
      where: { status: 'paid' },
      include: { items: { include: { service: true } } }
    });

    for (const invoice of paidInvoices) {
      // Create receipt
      const receipt = await prisma.receipt.create({
        data: {
          receiptNumber: `RCP-2024-${String(receipts.length + 1).padStart(3, '0')}`,
          customerName: invoice.customerName,
          phoneNumber: invoice.phoneNumber,
          email: invoice.email,
          totalAmount: invoice.totalAmount,
          amountPaid: invoice.totalAmount,
          balance: new Decimal('0'),
          status: 'paid',
          notes: `Payment received for invoice ${invoice.invoiceNumber}`,
          paymentMethod: 'M-Pesa'
        }
      });

      // Copy invoice items to receipt items
      for (const item of invoice.items) {
        await prisma.receiptItem.create({
          data: {
            receiptId: receipt.id,
            serviceId: item.serviceId,
            quantity: item.quantity,
            unitPrice: item.unitPrice,
            totalPrice: item.totalPrice,
            description: item.description
          }
        });
      }

      receipts.push(receipt);
    }

    // Create additional standalone receipts
    for (let i = 0; i < 6; i++) {
      const customer = customers[i % customers.length];
      const randomServices = services.sort(() => 0.5 - Math.random()).slice(0, Math.floor(Math.random() * 2) + 1);
      
      let totalAmount = new Decimal('0');
      
      // Calculate total amount
      for (const service of randomServices) {
        const quantity = Math.floor(Math.random() * 2) + 1;
        totalAmount = totalAmount.add(service.price.mul(quantity));
      }

      // Create receipt
      const receipt = await prisma.receipt.create({
        data: {
          receiptNumber: `RCP-2024-${String(receipts.length + i + 1).padStart(3, '0')}`,
          customerName: customer.name,
          phoneNumber: customer.phone,
          email: customer.email,
          totalAmount,
          amountPaid: totalAmount,
          balance: new Decimal('0'),
          status: 'paid',
          notes: `Direct payment for services`,
          paymentMethod: 'M-Pesa'
        }
      });

      // Add receipt items
      for (const service of randomServices) {
        const quantity = Math.floor(Math.random() * 2) + 1;
        const unitPrice = service.price;
        const totalPrice = unitPrice.mul(quantity);

        await prisma.receiptItem.create({
          data: {
            receiptId: receipt.id,
            serviceId: service.id,
            quantity,
            unitPrice,
            totalPrice,
            description: `${service.name} - Direct payment`
          }
        });
      }

      receipts.push(receipt);
    }

    console.log(`✅ Created ${receipts.length} receipts`);

    // Summary
    console.log('\n🎉 Financial Suite seeding completed successfully!');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    console.log(`📦 Services: ${services.length}`);
    console.log(`💰 Quotes: ${quotes.length}`);
    console.log(`📄 Invoices: ${invoices.length}`);
    console.log(`🧾 Receipts: ${receipts.length}`);
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    
    // Generate summary statistics
    const totalRevenue = receipts.reduce((sum, receipt) => sum.add(receipt.totalAmount), new Decimal('0'));
    const pendingInvoiceAmount = await prisma.invoice.aggregate({
      where: { status: 'pending' },
      _sum: { totalAmount: true }
    });
    const approvedQuoteAmount = await prisma.quote.aggregate({
      where: { status: 'approved' },
      _sum: { totalAmount: true }
    });

    console.log(`💰 Total Revenue (Paid): KES ${totalRevenue.toFixed(2)}`);
    console.log(`⏳ Pending Invoices: KES ${pendingInvoiceAmount._sum.totalAmount?.toFixed(2) || '0.00'}`);
    console.log(`✅ Approved Quotes: KES ${approvedQuoteAmount._sum.totalAmount?.toFixed(2) || '0.00'}`);
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');

  } catch (error) {
    console.error('❌ Error seeding financial suite:', error);
    throw error;
  }
}

main()
  .catch((e) => {
    console.error('❌ Seeding failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  }); 