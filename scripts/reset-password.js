require('dotenv').config();
const { PrismaClient } = require('@prisma/client');
const bcryptjs = require('bcryptjs');
const prisma = new PrismaClient();

async function resetPassword() {
  console.log('🔄 Resetting admin password...\n');
  
  try {
    // Get command line arguments
    const username = process.argv[2];
    const password = process.argv[3];

    if (!username || !password) {
      console.error('❌ Usage: node reset-password.js <username> <password>');
      return;
    }

    // Get user
    const user = await prisma.user.findFirst({
      where: {
        username
      }
    });

    if (!user) {
      console.error('❌ User not found:', username);
      return;
    }

    // Hash new password with bcryptjs
    const salt = await bcryptjs.genSalt(12);
    const passwordHash = await bcryptjs.hash(password, salt);

    // Update user password
    await prisma.user.update({
      where: {
        id: user.id
      },
      data: {
        passwordHash,
        active: true,
        lastLogin: null
      }
    });

    console.log('✅ Password reset successful');
    console.log('Username:', username);
    console.log('Password:', password);
    
  } catch (error) {
    console.error('❌ Error resetting password:', error);
  } finally {
    await prisma.$disconnect();
  }
}

resetPassword().catch(console.error); 