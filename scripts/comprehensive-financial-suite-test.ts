/**
 * Comprehensive Financial Suite Bug Detection & Testing Script
 * Tests all functionalities including forms, APIs, validation, and security
 */

import { promises as fs } from 'fs';
import path from 'path';

// Test Results Interface
interface TestResult {
  testName: string;
  status: 'PASS' | 'FAIL' | 'WARNING';
  message: string;
  details?: any;
  severity: 'HIGH' | 'MEDIUM' | 'LOW';
}

interface TestSuite {
  suiteName: string;
  results: TestResult[];
  summary: {
    total: number;
    passed: number;
    failed: number;
    warnings: number;
  };
}

class FinancialSuiteTestRunner {
  private testSuites: TestSuite[] = [];
  private baseUrl = 'http://localhost:3000';

  constructor() {
    console.log('🧪 Financial Suite Comprehensive Testing Started');
    console.log('=' .repeat(60));
  }

  // Add test result
  private addResult(suiteName: string, result: TestResult) {
    let suite = this.testSuites.find(s => s.suiteName === suiteName);
    if (!suite) {
      suite = {
        suiteName,
        results: [],
        summary: { total: 0, passed: 0, failed: 0, warnings: 0 }
      };
      this.testSuites.push(suite);
    }

    suite.results.push(result);
    suite.summary.total++;

    switch (result.status) {
      case 'PASS': suite.summary.passed++; break;
      case 'FAIL': suite.summary.failed++; break;
      case 'WARNING': suite.summary.warnings++; break;
    }
  }

  // Test 1: API Endpoint Security
  async testApiSecurity() {
    console.log('\n🔒 Testing API Security...');

    const endpoints = [
      { method: 'GET', path: '/api/admin/transactions', requiresAuth: true },
      { method: 'POST', path: '/api/admin/transactions', requiresAuth: true, requiresCSRF: true },
      { method: 'GET', path: '/api/admin/receipts', requiresAuth: true },
      { method: 'POST', path: '/api/admin/receipts', requiresAuth: true, requiresCSRF: true },
      { method: 'GET', path: '/api/admin/invoices', requiresAuth: true },
      { method: 'POST', path: '/api/admin/invoices', requiresAuth: true, requiresCSRF: true },
      { method: 'GET', path: '/api/admin/quotes', requiresAuth: true },
      { method: 'POST', path: '/api/admin/quotes', requiresAuth: true, requiresCSRF: true },
      { method: 'GET', path: '/api/admin/services', requiresAuth: true },
      { method: 'POST', path: '/api/admin/services', requiresAuth: true, requiresCSRF: true },
    ];

    for (const endpoint of endpoints) {
      try {
        // Test without authentication
        const response = await fetch(`${this.baseUrl}${endpoint.path}`, {
          method: endpoint.method,
          headers: { 'Content-Type': 'application/json' }
        });

        if (endpoint.requiresAuth && response.status !== 401 && response.status !== 403) {
          this.addResult('API Security', {
            testName: `${endpoint.method} ${endpoint.path} - Authentication`,
            status: 'FAIL',
            message: `Expected 401 Unauthorized or 403 Forbidden, got ${response.status}`,
            severity: 'HIGH'
          });
        } else if (endpoint.requiresAuth && (response.status === 401 || response.status === 403)) {
          this.addResult('API Security', {
            testName: `${endpoint.method} ${endpoint.path} - Authentication`,
            status: 'PASS',
            message: `Properly requires authentication (${response.status === 401 ? 'Unauthorized' : 'Forbidden'})`,
            severity: 'LOW'
          });
        }
      } catch (error) {
        this.addResult('API Security', {
          testName: `${endpoint.method} ${endpoint.path} - Connection`,
          status: 'FAIL',
          message: `Connection failed: ${error}`,
          severity: 'HIGH'
        });
      }
    }
  }

  // Test 2: File Structure & Route Validation
  async testFileStructure() {
    console.log('\n📁 Testing File Structure...');

    const requiredFiles = [
      'src/app/api/admin/transactions/route.ts',
      'src/app/api/admin/transactions/[id]/route.ts',
      'src/app/api/admin/receipts/route.ts',
      'src/app/api/admin/receipts/[id]/route.ts',
      'src/app/api/admin/invoices/route.ts',
      'src/app/api/admin/invoices/[id]/route.ts',
      'src/app/api/admin/quotes/route.ts',
      'src/app/api/admin/quotes/[id]/route.ts',
      'src/app/api/admin/services/route.ts',
      'src/app/api/admin/services/[id]/route.ts',
      'src/services/transactionService.ts',
      'src/services/receiptService.ts',
      'src/services/invoiceService.ts',
      'src/services/quoteService.ts',
      'src/services/serviceItemService.ts',
    ];

    for (const filePath of requiredFiles) {
      try {
        await fs.access(filePath);
        this.addResult('File Structure', {
          testName: `File exists: ${filePath}`,
          status: 'PASS',
          message: 'File exists',
          severity: 'LOW'
        });
      } catch (error) {
        this.addResult('File Structure', {
          testName: `File exists: ${filePath}`,
          status: 'FAIL',
          message: 'File missing',
          severity: 'HIGH'
        });
      }
    }
  }

  // Test 3: API Route Methods
  async testApiMethods() {
    console.log('\n🔧 Testing API Methods...');

    const apiFiles = [
      'src/app/api/admin/transactions/route.ts',
      'src/app/api/admin/receipts/route.ts',
      'src/app/api/admin/invoices/route.ts',
      'src/app/api/admin/quotes/route.ts',
      'src/app/api/admin/services/route.ts',
    ];

    for (const filePath of apiFiles) {
      try {
        const content = await fs.readFile(filePath, 'utf-8');

        // Check for required HTTP methods
        const hasGET = content.includes('export const GET');
        const hasPOST = content.includes('export const POST');
        const hasPATCH = content.includes('export const PATCH');

        if (!hasGET) {
          this.addResult('API Methods', {
            testName: `${filePath} - GET method`,
            status: 'FAIL',
            message: 'Missing GET export',
            severity: 'MEDIUM'
          });
        } else {
          this.addResult('API Methods', {
            testName: `${filePath} - GET method`,
            status: 'PASS',
            message: 'GET method exists',
            severity: 'LOW'
          });
        }

        if (!hasPOST) {
          this.addResult('API Methods', {
            testName: `${filePath} - POST method`,
            status: 'FAIL',
            message: 'Missing POST export',
            severity: 'MEDIUM'
          });
        } else {
          this.addResult('API Methods', {
            testName: `${filePath} - POST method`,
            status: 'PASS',
            message: 'POST method exists',
            severity: 'LOW'
          });
        }

        // Check for authentication middleware
        const hasAuth = content.includes('withAuthAndCSRF');
        if (!hasAuth) {
          this.addResult('API Methods', {
            testName: `${filePath} - Authentication`,
            status: 'FAIL',
            message: 'Missing authentication middleware',
            severity: 'HIGH'
          });
        } else {
          this.addResult('API Methods', {
            testName: `${filePath} - Authentication`,
            status: 'PASS',
            message: 'Authentication middleware present',
            severity: 'LOW'
          });
        }

      } catch (error) {
        this.addResult('API Methods', {
          testName: `${filePath} - File read`,
          status: 'FAIL',
          message: `Cannot read file: ${error}`,
          severity: 'HIGH'
        });
      }
    }
  }

  // Test 4: DELETE Route Validation
  async testDeleteRoutes() {
    console.log('\n🗑️ Testing DELETE Routes...');

    const deleteRoutes = [
      'src/app/api/admin/transactions/[id]/route.ts',
      'src/app/api/admin/receipts/[id]/route.ts',
      'src/app/api/admin/invoices/[id]/route.ts',
      'src/app/api/admin/quotes/[id]/route.ts',
      'src/app/api/admin/services/[id]/route.ts',
    ];

    for (const filePath of deleteRoutes) {
      try {
        const content = await fs.readFile(filePath, 'utf-8');

        const hasDELETE = content.includes('export const DELETE') || content.includes('export async function DELETE');

        if (!hasDELETE) {
          this.addResult('DELETE Routes', {
            testName: `${filePath} - DELETE method`,
            status: 'FAIL',
            message: 'Missing DELETE method',
            severity: 'MEDIUM'
          });
        } else {
          this.addResult('DELETE Routes', {
            testName: `${filePath} - DELETE method`,
            status: 'PASS',
            message: 'DELETE method exists',
            severity: 'LOW'
          });
        }

      } catch (error) {
        this.addResult('DELETE Routes', {
          testName: `${filePath} - File access`,
          status: 'FAIL',
          message: `Cannot access file: ${error}`,
          severity: 'HIGH'
        });
      }
    }
  }

  // Test 5: Service Function Validation
  async testServiceFunctions() {
    console.log('\n⚙️ Testing Service Functions...');

    const serviceFiles = [
      { file: 'src/services/transactionService.ts', functions: ['getAllTransactions', 'createTransaction', 'deleteTransaction'] },
      { file: 'src/services/receiptService.ts', functions: ['getAllReceipts', 'createReceipt', 'deleteReceipt'] },
      { file: 'src/services/invoiceService.ts', functions: ['getAllInvoices', 'createInvoice', 'deleteInvoice'] },
      { file: 'src/services/quoteService.ts', functions: ['getAllQuotes', 'createQuote', 'deleteQuote'] },
      { file: 'src/services/serviceItemService.ts', functions: ['getAllServices', 'createService', 'deleteService'] },
    ];

    for (const service of serviceFiles) {
      try {
        const content = await fs.readFile(service.file, 'utf-8');

        for (const func of service.functions) {
          const hasFunction = content.includes(`export async function ${func}`) ||
                            content.includes(`export function ${func}`) ||
                            content.includes(`${func}:`);

          if (!hasFunction) {
            this.addResult('Service Functions', {
              testName: `${service.file} - ${func}`,
              status: 'FAIL',
              message: `Missing function: ${func}`,
              severity: 'MEDIUM'
            });
          } else {
            this.addResult('Service Functions', {
              testName: `${service.file} - ${func}`,
              status: 'PASS',
              message: `Function exists: ${func}`,
              severity: 'LOW'
            });
          }
        }

      } catch (error) {
        this.addResult('Service Functions', {
          testName: `${service.file} - File access`,
          status: 'FAIL',
          message: `Cannot access file: ${error}`,
          severity: 'HIGH'
        });
      }
    }
  }

  // Test 6: Frontend Component Validation
  async testFrontendComponents() {
    console.log('\n🎨 Testing Frontend Components...');

    const frontendFiles = [
      'src/app/admin/transactions/page.tsx',
      'src/app/admin/receipts/page.tsx',
      'src/app/admin/invoices/page.tsx',
      'src/app/admin/quotes/page.tsx',
      'src/app/admin/services/page.tsx',
    ];

    for (const filePath of frontendFiles) {
      try {
        const content = await fs.readFile(filePath, 'utf-8');

        // Check for delete functionality
        const hasDeleteButton = content.includes('TrashIcon') || content.includes('delete') || content.includes('Delete');
        const hasDeleteFunction = content.includes('handleDelete') || content.includes('deleteItem') || content.includes('onDelete');

        if (!hasDeleteButton) {
          this.addResult('Frontend Components', {
            testName: `${filePath} - Delete Button`,
            status: 'WARNING',
            message: 'No delete button found',
            severity: 'MEDIUM'
          });
        } else {
          this.addResult('Frontend Components', {
            testName: `${filePath} - Delete Button`,
            status: 'PASS',
            message: 'Delete button present',
            severity: 'LOW'
          });
        }

        // Check for error handling
        const hasErrorHandling = content.includes('try') && content.includes('catch');
        if (!hasErrorHandling) {
          this.addResult('Frontend Components', {
            testName: `${filePath} - Error Handling`,
            status: 'WARNING',
            message: 'No error handling found',
            severity: 'MEDIUM'
          });
        } else {
          this.addResult('Frontend Components', {
            testName: `${filePath} - Error Handling`,
            status: 'PASS',
            message: 'Error handling present',
            severity: 'LOW'
          });
        }

        // Check for loading states
        const hasLoadingState = content.includes('loading') || content.includes('Loading');
        if (!hasLoadingState) {
          this.addResult('Frontend Components', {
            testName: `${filePath} - Loading State`,
            status: 'WARNING',
            message: 'No loading state found',
            severity: 'LOW'
          });
        } else {
          this.addResult('Frontend Components', {
            testName: `${filePath} - Loading State`,
            status: 'PASS',
            message: 'Loading state present',
            severity: 'LOW'
          });
        }

      } catch (error) {
        this.addResult('Frontend Components', {
          testName: `${filePath} - File access`,
          status: 'FAIL',
          message: `Cannot access file: ${error}`,
          severity: 'HIGH'
        });
      }
    }
  }

  // Test 7: Validation Schema Testing
  async testValidationSchemas() {
    console.log('\n✅ Testing Validation Schemas...');

    const validationFiles = [
      'src/utils/validation.ts',
      'src/utils/catalogueValidation.ts',
    ];

    for (const filePath of validationFiles) {
      try {
        const content = await fs.readFile(filePath, 'utf-8');

        // Check for validation functions
        const hasValidation = content.includes('validate') || content.includes('schema');
        if (!hasValidation) {
          this.addResult('Validation Schemas', {
            testName: `${filePath} - Validation Functions`,
            status: 'FAIL',
            message: 'No validation functions found',
            severity: 'MEDIUM'
          });
        } else {
          this.addResult('Validation Schemas', {
            testName: `${filePath} - Validation Functions`,
            status: 'PASS',
            message: 'Validation functions present',
            severity: 'LOW'
          });
        }

        // Check for error handling in validation
        const hasErrorHandling = content.includes('throw') || content.includes('Error');
        if (!hasErrorHandling) {
          this.addResult('Validation Schemas', {
            testName: `${filePath} - Error Handling`,
            status: 'WARNING',
            message: 'Limited error handling in validation',
            severity: 'MEDIUM'
          });
        } else {
          this.addResult('Validation Schemas', {
            testName: `${filePath} - Error Handling`,
            status: 'PASS',
            message: 'Error handling present',
            severity: 'LOW'
          });
        }

      } catch (error) {
        this.addResult('Validation Schemas', {
          testName: `${filePath} - File access`,
          status: 'FAIL',
          message: `Cannot access file: ${error}`,
          severity: 'HIGH'
        });
      }
    }
  }

  // Test 8: Database Schema Validation
  async testDatabaseSchema() {
    console.log('\n🗄️ Testing Database Schema...');

    try {
      const schemaContent = await fs.readFile('prisma/schema.prisma', 'utf-8');

      // Check for required models
      const requiredModels = ['Transaction', 'Receipt', 'Invoice', 'Quote', 'Service'];

      for (const model of requiredModels) {
        const hasModel = schemaContent.includes(`model ${model}`);
        if (!hasModel) {
          this.addResult('Database Schema', {
            testName: `Model: ${model}`,
            status: 'FAIL',
            message: `Missing model: ${model}`,
            severity: 'HIGH'
          });
        } else {
          this.addResult('Database Schema', {
            testName: `Model: ${model}`,
            status: 'PASS',
            message: `Model exists: ${model}`,
            severity: 'LOW'
          });
        }
      }

      // Check for relationships
      const hasRelations = schemaContent.includes('@@relation') || schemaContent.includes('@relation');
      if (!hasRelations) {
        this.addResult('Database Schema', {
          testName: 'Model Relations',
          status: 'WARNING',
          message: 'No explicit relations found',
          severity: 'MEDIUM'
        });
      } else {
        this.addResult('Database Schema', {
          testName: 'Model Relations',
          status: 'PASS',
          message: 'Relations present',
          severity: 'LOW'
        });
      }

    } catch (error) {
      this.addResult('Database Schema', {
        testName: 'Schema File Access',
        status: 'FAIL',
        message: `Cannot access schema file: ${error}`,
        severity: 'HIGH'
      });
    }
  }

  // Run all tests
  async runAllTests() {
    try {
      await this.testFileStructure();
      await this.testApiMethods();
      await this.testDeleteRoutes();
      await this.testServiceFunctions();
      await this.testFrontendComponents();
      await this.testValidationSchemas();
      await this.testDatabaseSchema();
      await this.testApiSecurity();

      this.generateReport();
    } catch (error) {
      console.error('❌ Test execution failed:', error);
    }
  }

  // Generate comprehensive report
  generateReport() {
    console.log('\n' + '='.repeat(60));
    console.log('📊 COMPREHENSIVE TEST REPORT');
    console.log('='.repeat(60));

    let totalTests = 0;
    let totalPassed = 0;
    let totalFailed = 0;
    let totalWarnings = 0;
    let highSeverityIssues = 0;
    let mediumSeverityIssues = 0;

    for (const suite of this.testSuites) {
      console.log(`\n📋 ${suite.suiteName}`);
      console.log('-'.repeat(40));
      console.log(`Total: ${suite.summary.total} | Passed: ${suite.summary.passed} | Failed: ${suite.summary.failed} | Warnings: ${suite.summary.warnings}`);

      totalTests += suite.summary.total;
      totalPassed += suite.summary.passed;
      totalFailed += suite.summary.failed;
      totalWarnings += suite.summary.warnings;

      // Show failed tests
      const failedTests = suite.results.filter(r => r.status === 'FAIL');
      if (failedTests.length > 0) {
        console.log('\n❌ Failed Tests:');
        failedTests.forEach(test => {
          console.log(`  • ${test.testName}: ${test.message}`);
          if (test.severity === 'HIGH') highSeverityIssues++;
          if (test.severity === 'MEDIUM') mediumSeverityIssues++;
        });
      }

      // Show warnings
      const warningTests = suite.results.filter(r => r.status === 'WARNING');
      if (warningTests.length > 0) {
        console.log('\n⚠️ Warnings:');
        warningTests.forEach(test => {
          console.log(`  • ${test.testName}: ${test.message}`);
        });
      }
    }

    console.log('\n' + '='.repeat(60));
    console.log('🎯 OVERALL SUMMARY');
    console.log('='.repeat(60));
    console.log(`Total Tests: ${totalTests}`);
    console.log(`✅ Passed: ${totalPassed} (${((totalPassed/totalTests)*100).toFixed(1)}%)`);
    console.log(`❌ Failed: ${totalFailed} (${((totalFailed/totalTests)*100).toFixed(1)}%)`);
    console.log(`⚠️ Warnings: ${totalWarnings} (${((totalWarnings/totalTests)*100).toFixed(1)}%)`);
    console.log(`\n🚨 High Severity Issues: ${highSeverityIssues}`);
    console.log(`⚠️ Medium Severity Issues: ${mediumSeverityIssues}`);

    if (totalFailed === 0 && highSeverityIssues === 0) {
      console.log('\n🎉 ALL CRITICAL TESTS PASSED! System is robust.');
    } else if (highSeverityIssues > 0) {
      console.log('\n🚨 CRITICAL ISSUES FOUND! Immediate attention required.');
    } else {
      console.log('\n⚠️ Some issues found. Review and fix recommended.');
    }

    console.log('\n' + '='.repeat(60));
  }
}

// Run the tests
async function main() {
  const testRunner = new FinancialSuiteTestRunner();
  await testRunner.runAllTests();
}

if (require.main === module) {
  main().catch(console.error);
}

export { FinancialSuiteTestRunner };
