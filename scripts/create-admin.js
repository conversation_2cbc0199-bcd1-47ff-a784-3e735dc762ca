const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcrypt');
const { v4: uuidv4 } = require('uuid');

async function createAdmin() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🚀 Creating Super User Admin...\n');
    
    // Get credentials from environment - strictly require ADMIN_PASSWORD
    const email = process.env.ADMIN_EMAIL || '<EMAIL>';
    const password = process.env.ADMIN_PASSWORD;
    const username = email.split('@')[0]; // Use first part of email as username
    
    // Strict validation of environment variables
    if (!process.env.ADMIN_PASSWORD) {
      console.error('❌ Error: ADMIN_PASSWORD environment variable is not set!');
      console.log('\n📝 Please ensure your .env file contains:');
      console.log('ADMIN_PASSWORD=your_secure_password');
      console.log('\nCurrent .env values:');
      console.log('ADMIN_EMAIL:', process.env.ADMIN_EMAIL || '(not set, using default)');
      console.log('ADMIN_PASSWORD:', process.env.ADMIN_PASSWORD || '(not set) ❌');
      process.exit(1);
    }
    
    console.log('📋 Using credentials from environment:');
    console.log('👤 Username:', username);
    console.log('📧 Email:', email);
    console.log('🔑 Password: [Using password from ADMIN_PASSWORD env var]');
    
    // Create admin role if it doesn't exist
    const adminRole = await prisma.role.upsert({
      where: { name: 'admin' },
      update: { 
        permissions: ['*'],
        description: 'Administrator with full access'
      },
      create: {
        id: uuidv4(),
        name: 'admin',
        description: 'Administrator with full access',
        permissions: ['*']
      }
    });
    
    console.log('✅ Admin role ready:', adminRole.id);
    
    // Hash password with bcrypt (same as auth.ts)
    const passwordHash = await bcrypt.hash(process.env.ADMIN_PASSWORD, 12);
    
    // Delete existing admin user if exists (to handle unique constraints)
    try {
      await prisma.user.delete({
        where: {
          email: email
        }
      });
      console.log('🗑️  Removed existing admin user');
    } catch (e) {
      // User doesn't exist, which is fine
    }
    
    // Create new admin user
    const admin = await prisma.user.create({
      data: {
        id: uuidv4(),
        username: username,
        email: email,
        name: 'System Administrator',
        passwordHash,
        roleId: adminRole.id,
        active: true
      },
      include: {
        role: true
      }
    });
    
    console.log('✅ Super user created:', admin.username);
    console.log('📧 Email:', admin.email);
    console.log('👤 Name:', admin.name);
    console.log('🔐 Active:', admin.active);
    console.log('🎭 Role:', admin.role.name);
    
    // Test password
    const passwordTest = await bcrypt.compare(process.env.ADMIN_PASSWORD, admin.passwordHash);
    console.log('🧪 Password test:', passwordTest ? '✅ PASS' : '❌ FAIL');
    
    console.log('\n🎉 SUPER USER CREATION COMPLETE!');
    console.log('━'.repeat(50));
    console.log('🌐 Login URL: http://localhost:3000/admin/login');
    console.log('👤 Username:', admin.username);
    console.log('🔑 Password: [Using password from ADMIN_PASSWORD env var]');
    console.log('━'.repeat(50));
    
  } catch (error) {
    console.error('❌ Error creating admin:', error.message);
    if (error.code) {
      console.error('Error code:', error.code);
    }
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

createAdmin().catch(console.error); 