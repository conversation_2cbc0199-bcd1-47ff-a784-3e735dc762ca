const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkQuotes() {
  try {
    console.log('Checking quotes in database...');
    
    const quotes = await prisma.quote.findMany({
      select: {
        id: true,
        quoteNumber: true,
        customerName: true,
        status: true,
        createdAt: true
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: 10
    });
    
    console.log(`Found ${quotes.length} quotes:`);
    quotes.forEach((quote, index) => {
      console.log(`${index + 1}. ID: ${quote.id}`);
      console.log(`   Quote Number: ${quote.quoteNumber}`);
      console.log(`   Customer: ${quote.customerName}`);
      console.log(`   Status: ${quote.status}`);
      console.log(`   Created: ${quote.createdAt}`);
      console.log('');
    });
    
    if (quotes.length > 0) {
      const firstQuote = quotes[0];
      console.log('Testing quote service with first quote...');
      
      // Test the quote service
      const { getQuoteById } = require('./src/services/quoteService');
      const fetchedQuote = await getQuoteById(firstQuote.id);
      
      if (fetchedQuote) {
        console.log('✅ Quote service working correctly');
        console.log('Fetched quote:', {
          id: fetchedQuote.id,
          quoteNumber: fetchedQuote.quoteNumber,
          customerName: fetchedQuote.customerName
        });
      } else {
        console.log('❌ Quote service returned null for existing quote');
      }
    }
    
  } catch (error) {
    console.error('Error checking quotes:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkQuotes();
