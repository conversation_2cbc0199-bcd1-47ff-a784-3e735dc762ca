const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  try {
    console.log('Setting up storage configuration...');

    // Check if a default config already exists
    const existingConfig = await prisma.storageConfig.findFirst({
      where: { isDefault: true }
    });

    if (existingConfig) {
      console.log('Updating existing default storage configuration...');
      
      const updatedConfig = await prisma.storageConfig.update({
        where: { id: existingConfig.id },
        data: {
          provider: 'S3',
          region: process.env.NEXT_PUBLIC_S3_REGION || 'fr-par-1',
          endpoint: process.env.NEXT_PUBLIC_S3_ENDPOINT || 'https://fr-par-1.linodeobjects.com',
          bucketName: process.env.NEXT_PUBLIC_S3_BUCKET || 'mocky2',
          accessKey: process.env.NEXT_PUBLIC_S3_ACCESS_KEY,
          secretKey: process.env.NEXT_PUBLIC_S3_SECRET_KEY,
          isDefault: true
        }
      });

      console.log('Updated storage configuration:', {
        id: updatedConfig.id,
        provider: updatedConfig.provider,
        region: updatedConfig.region,
        endpoint: updatedConfig.endpoint,
        bucketName: updatedConfig.bucketName,
        hasAccessKey: !!updatedConfig.accessKey,
        hasSecretKey: !!updatedConfig.secretKey,
        isDefault: updatedConfig.isDefault
      });
    } else {
      console.log('Creating new default storage configuration...');
      
      const newConfig = await prisma.storageConfig.create({
        data: {
          provider: 'S3',
          region: process.env.NEXT_PUBLIC_S3_REGION || 'fr-par-1',
          endpoint: process.env.NEXT_PUBLIC_S3_ENDPOINT || 'https://fr-par-1.linodeobjects.com',
          bucketName: process.env.NEXT_PUBLIC_S3_BUCKET || 'mocky2',
          accessKey: process.env.NEXT_PUBLIC_S3_ACCESS_KEY,
          secretKey: process.env.NEXT_PUBLIC_S3_SECRET_KEY,
          isDefault: true
        }
      });

      console.log('Created storage configuration:', {
        id: newConfig.id,
        provider: newConfig.provider,
        region: newConfig.region,
        endpoint: newConfig.endpoint,
        bucketName: newConfig.bucketName,
        hasAccessKey: !!newConfig.accessKey,
        hasSecretKey: !!newConfig.secretKey,
        isDefault: newConfig.isDefault
      });
    }

    console.log('Storage configuration setup complete!');
  } catch (error) {
    console.error('Error setting up storage configuration:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

main(); 