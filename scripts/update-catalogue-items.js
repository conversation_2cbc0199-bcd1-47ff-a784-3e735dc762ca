// Script to update catalogue items to match the UI
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

// Define the catalogue items that should match the UI
const catalogueItems = [
  {
    service: 'Business Card Design',
    price: 3000,
    description: 'Professional business card design for your brand',
    features: [
      '3 unique concepts',
      '2 revision rounds',
      'Print-ready files',
      'Digital formats for online use',
      'Double-sided design',
      'Delivery in 2 days'
    ],
    icon: 'id-card',
    popular: false
  },
  {
    service: 'Flyer Design',
    price: 4500,
    description: 'Eye-catching flyer designs for your marketing campaigns',
    features: [
      '2 design concepts',
      '3 revision rounds',
      'Print-ready files (CMYK)',
      'Web formats (RGB)',
      'Custom illustrations',
      'Delivery in 3 days'
    ],
    icon: 'file-image',
    popular: false
  },
  {
    service: 'Brochure Design',
    price: 8000,
    description: 'Professional multi-page brochure design',
    features: [
      'Up to 8-page design',
      '3 revision rounds',
      'Print-ready files',
      'Digital formats',
      'Custom graphics',
      'Layout & typography',
      'Delivery in 5 days'
    ],
    icon: 'newspaper',
    popular: true
  }
];

async function main() {
  console.log('Updating catalogue items to match the UI...');

  try {
    // Get existing catalogue items
    const existingItems = await prisma.catalogue.findMany();
    console.log(`Found ${existingItems.length} existing catalogue items`);

    // Create a map of existing items by service name (case insensitive)
    const existingItemsMap = new Map();
    existingItems.forEach(item => {
      existingItemsMap.set(item.service.toLowerCase(), item);
    });

    // Process each catalogue item
    for (const item of catalogueItems) {
      const existingItem = existingItemsMap.get(item.service.toLowerCase());

      if (existingItem) {
        // Update existing item
        console.log(`Updating existing item: ${item.service}`);
        await prisma.catalogue.update({
          where: { id: existingItem.id },
          data: {
            price: item.price,
            description: item.description,
            features: item.features,
            icon: item.icon,
            popular: item.popular
          }
        });
      } else {
        // Create new item
        console.log(`Creating new item: ${item.service}`);
        await prisma.catalogue.create({
          data: {
            service: item.service,
            price: item.price,
            description: item.description,
            features: item.features,
            icon: item.icon,
            popular: item.popular
          }
        });
      }
    }

    console.log('Catalogue items updated successfully');

    // Verify the updated items
    const updatedItems = await prisma.catalogue.findMany();
    console.log('Updated catalogue items:');
    updatedItems.forEach(item => {
      console.log(`- ${item.service}: KSh ${item.price} (${item.popular ? 'Popular' : 'Standard'})`);
    });
  } catch (error) {
    console.error('Error updating catalogue items:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main()
  .catch(e => {
    console.error('Error:', e);
    process.exit(1);
  });
