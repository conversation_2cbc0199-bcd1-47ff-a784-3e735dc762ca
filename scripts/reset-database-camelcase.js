#!/usr/bin/env node

/**
 * Database Reset Script - CamelCase Migration
 * 
 * This script will:
 * 1. Drop all existing tables
 * 2. Reset Prisma migrations
 * 3. Apply the new camelCase schema
 * 4. Generate fresh Prisma client
 * 5. Seed with initial data
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Starting Database Reset for CamelCase Migration...\n');

function runCommand(command, description) {
  console.log(`📋 ${description}`);
  console.log(`   Command: ${command}`);
  try {
    const result = execSync(command, { 
      stdio: 'inherit', 
      cwd: process.cwd(),
      env: { ...process.env }
    });
    console.log('   ✅ Success\n');
    return result;
  } catch (error) {
    console.error(`   ❌ Failed: ${error.message}\n`);
    throw error;
  }
}

async function resetDatabase() {
  try {
    // Step 1: Reset Prisma migrations directory
    console.log('🗑️  Cleaning up old migrations...');
    const migrationsDir = path.join(__dirname, '../prisma/migrations');
    if (fs.existsSync(migrationsDir)) {
      fs.rmSync(migrationsDir, { recursive: true, force: true });
      console.log('   ✅ Old migrations removed\n');
    }

    // Step 2: Reset database (this will drop all tables)
    runCommand(
      'npx prisma migrate reset --force --skip-seed',
      'Resetting database (dropping all tables)'
    );

    // Step 3: Create initial migration with new schema
    runCommand(
      'npx prisma migrate dev --name "init_camelcase_schema" --create-only',
      'Creating initial migration for camelCase schema'
    );

    // Step 4: Apply the migration
    runCommand(
      'npx prisma migrate deploy',
      'Applying new camelCase schema migration'
    );

    // Step 5: Generate Prisma client
    runCommand(
      'npx prisma generate',
      'Generating new Prisma client with camelCase models'
    );

    // Step 6: Seed with initial data
    console.log('🌱 Seeding database with initial data...');
    await seedInitialData();

    console.log('🎉 Database reset complete!');
    console.log('\n📊 Summary:');
    console.log('   ✅ All old tables dropped');
    console.log('   ✅ New camelCase schema applied');
    console.log('   ✅ Prisma client regenerated');
    console.log('   ✅ Initial data seeded');
    console.log('\n🚀 Your application now uses camelCase models consistently!');

  } catch (error) {
    console.error('\n❌ Database reset failed:', error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('   1. Check your DATABASE_URL in .env');
    console.log('   2. Ensure PostgreSQL is running');
    console.log('   3. Verify database connection permissions');
    process.exit(1);
  }
}

async function seedInitialData() {
  const { PrismaClient } = require('@prisma/client');
  const bcrypt = require('bcryptjs');
  
  const prisma = new PrismaClient();

  try {
    // Create default roles
    const adminRole = await prisma.role.create({
      data: {
        name: 'admin',
        description: 'Full system access',
        permissions: ['*']
      }
    });

    const userRole = await prisma.role.create({
      data: {
        name: 'user',
        description: 'Standard user access',
        permissions: ['read:own', 'write:own']
      }
    });

    // Create default admin user
    const hashedPassword = await bcrypt.hash('admin123', 12);
    await prisma.user.create({
      data: {
        username: 'admin',
        email: '<EMAIL>',
        name: 'System Administrator',
        passwordHash: hashedPassword,
        roleId: adminRole.id,
        active: true
      }
    });

    // Create default site settings
    await prisma.siteSettings.create({
      data: {
        siteName: 'Mocky Digital',
        siteDescription: 'Professional Digital Services',
        contactEmail: '<EMAIL>',
        metaTitle: 'Mocky Digital - Professional Digital Services',
        metaDescription: 'Expert web development, design, and digital marketing services'
      }
    });

    // Create sample categories
    await prisma.category.createMany({
      data: [
        { name: 'Web Development', slug: 'web-development' },
        { name: 'Design', slug: 'design' },
        { name: 'Marketing', slug: 'marketing' },
        { name: 'Consulting', slug: 'consulting' }
      ]
    });

    console.log('   ✅ Initial data seeded successfully');

  } catch (error) {
    console.error('   ❌ Seeding failed:', error.message);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
if (require.main === module) {
  resetDatabase().catch(console.error);
}

module.exports = { resetDatabase }; 