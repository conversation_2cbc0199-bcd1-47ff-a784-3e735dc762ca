#!/bin/bash

# Switch to Main Branch and Pull Script
cd /var/www/mocky

echo "🔄 Switching to main branch and pulling latest changes..."
echo "========================================================="

# Check current status
echo "📍 Current branch:"
git branch --show-current

# Switch to main branch
echo "🔄 Switching to main branch..."
git checkout main

# Fetch remote changes
echo "📥 Fetching remote changes..."
git fetch origin

# Pull latest changes
echo "⬇️  Pulling latest changes..."
git pull origin main

# Show final status
echo "📊 Final status:"
git status

# Show recent commits
echo "📝 Recent commits:"
git log --oneline -5

echo ""
echo "✅ Successfully switched to main branch and pulled latest changes!" 