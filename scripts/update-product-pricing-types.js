const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function updateProductPricingTypes() {
  console.log('🔧 Updating product pricing types...');

  const updates = [
    // Paper print products
    {
      name: 'Business Cards',
      pricingType: 'paper_print',
      unitType: 'piece',
      minQuantity: 100,
      maxQuantity: 10000
    },
    {
      name: 'Flyers',
      pricingType: 'paper_print',
      unitType: 'piece',
      minQuantity: 100,
      maxQuantity: 50000
    },
    {
      name: 'Brochures',
      pricingType: 'paper_print',
      unitType: 'piece',
      minQuantity: 50,
      maxQuantity: 10000
    },
    {
      name: 'Posters',
      pricingType: 'paper_print',
      unitType: 'piece',
      minQuantity: 1,
      maxQuantity: 1000
    },
    {
      name: 'Letterheads',
      pricingType: 'paper_print',
      unitType: 'piece',
      minQuantity: 100,
      maxQuantity: 5000
    },
    
    // Banner meter products
    {
      name: 'Banner Print',
      pricingType: 'banner_meter',
      unitType: 'meter',
      pricePerMeter: 799,
      minMeters: 0.5,
      maxMeters: 100
    },
    {
      name: 'Vinyl <PERSON>',
      pricingType: 'banner_meter',
      unitType: 'meter',
      pricePerMeter: 850,
      minMeters: 0.5,
      maxMeters: 50
    },
    {
      name: 'Outdoor Banner',
      pricingType: 'banner_meter',
      unitType: 'meter',
      pricePerMeter: 900,
      minMeters: 1,
      maxMeters: 100
    },
    
    // Fixed price products
    {
      name: 'Roll-up Banner',
      pricingType: 'fixed',
      unitType: 'piece',
      price: 7500,
      minQuantity: 1,
      maxQuantity: 50
    },
    {
      name: 'Pull-up Banner',
      pricingType: 'fixed',
      unitType: 'piece',
      price: 7500,
      minQuantity: 1,
      maxQuantity: 50
    },
    {
      name: 'X-Banner Stand',
      pricingType: 'fixed',
      unitType: 'piece',
      price: 3500,
      minQuantity: 1,
      maxQuantity: 20
    }
  ];

  for (const update of updates) {
    try {
      // Find products that match the name (case-insensitive)
      const products = await prisma.catalogue.findMany({
        where: {
          service: {
            contains: update.name,
            mode: 'insensitive'
          }
        }
      });

      if (products.length === 0) {
        console.log(`⚠️  No products found matching "${update.name}"`);
        continue;
      }

      // Update all matching products
      for (const product of products) {
        await prisma.catalogue.update({
          where: { id: product.id },
          data: {
            pricingType: update.pricingType,
            unitType: update.unitType,
            minQuantity: update.minQuantity,
            maxQuantity: update.maxQuantity,
            pricePerMeter: update.pricePerMeter,
            minMeters: update.minMeters,
            maxMeters: update.maxMeters,
            price: update.price || product.price, // Keep existing price if not specified
            updatedAt: new Date()
          }
        });

        console.log(`✅ Updated "${product.service}" (ID: ${product.id}) to ${update.pricingType} pricing`);
      }
    } catch (error) {
      console.error(`❌ Error updating ${update.name}:`, error);
    }
  }

  console.log('✅ Product pricing types update completed!');
}

async function main() {
  try {
    await updateProductPricingTypes();
  } catch (error) {
    console.error('❌ Error during update:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

main(); 