#!/bin/bash

# Phase 1: Security & Stability Upgrade Script
# Safe implementation with rollback capabilities

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BACKUP_DIR="./backups/phase1-$(date +%Y%m%d-%H%M%S)"
LOG_FILE="./logs/phase1-upgrade-$(date +%Y%m%d-%H%M%S).log"

# Ensure log directory exists
mkdir -p ./logs
mkdir -p "$BACKUP_DIR"

# Logging function
log() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

# Backup function
backup_files() {
    log "Creating backup of critical files..."
    
    # Backup package.json
    cp package.json "$BACKUP_DIR/package.json.backup"
    
    # Backup package-lock.json
    cp package-lock.json "$BACKUP_DIR/package-lock.json.backup"
    
    # Backup .env if it exists
    if [ -f .env ]; then
        cp .env "$BACKUP_DIR/.env.backup"
    fi
    
    # Backup middleware
    cp src/middleware.ts "$BACKUP_DIR/middleware.ts.backup"
    
    success "Backup completed in $BACKUP_DIR"
}

# Database backup function
backup_database() {
    log "Creating database backup..."
    
    if [ -n "$DATABASE_URL" ]; then
        # Extract database info from URL
        DB_NAME=$(echo "$DATABASE_URL" | sed -n 's/.*\/\([^?]*\).*/\1/p')
        
        # Create database dump
        npm run prisma:migrate -- status > "$BACKUP_DIR/db-migration-status.txt" 2>&1 || true
        
        success "Database backup metadata saved"
    else
        warning "DATABASE_URL not set, skipping database backup"
    fi
}

# Pre-flight checks
preflight_checks() {
    log "Running pre-flight checks..."
    
    # Check if Node.js is installed
    if ! command -v node &> /dev/null; then
        error "Node.js is not installed"
        exit 1
    fi
    
    # Check if npm is installed
    if ! command -v npm &> /dev/null; then
        error "npm is not installed"
        exit 1
    fi
    
    # Check if application is running
    if pgrep -f "node.*server.js" > /dev/null; then
        warning "Application appears to be running. Consider stopping it first."
    fi
    
    # Check disk space (need at least 1GB)
    AVAILABLE_SPACE=$(df . | tail -1 | awk '{print $4}')
    if [ "$AVAILABLE_SPACE" -lt 1048576 ]; then
        error "Insufficient disk space. Need at least 1GB free."
        exit 1
    fi
    
    success "Pre-flight checks passed"
}

# Install new dependencies
install_dependencies() {
    log "Installing new security dependencies..."
    
    # Install new dependencies one by one to avoid conflicts
    npm install helmet@^8.0.0 --save || {
        error "Failed to install helmet"
        return 1
    }
    
    npm install express-rate-limit@^7.4.1 --save || {
        error "Failed to install express-rate-limit"
        return 1
    }
    
    npm install express-slow-down@^2.0.3 --save || {
        error "Failed to install express-slow-down"
        return 1
    }
    
    success "New dependencies installed"
}

# Update existing dependencies (safe updates only)
update_dependencies() {
    log "Updating existing dependencies (safe updates only)..."
    
    # Update node-fetch to version 3 (major change, needs attention)
    npm install node-fetch@^3.3.2 --save || {
        warning "Failed to update node-fetch, keeping current version"
    }
    
    success "Dependencies updated"
}

# Apply database indexes using Prisma migrations
apply_database_indexes() {
    log "Applying database performance indexes using Prisma migrations..."
    
    # Check if we can connect to database
    npm run prisma:migrate -- status > /dev/null 2>&1 || {
        warning "Cannot connect to database, skipping index creation"
        return 0
    }
    
    # Apply the migration
    npm run prisma:migrate -- deploy || {
        warning "Failed to apply database migrations, but continuing..."
        log "You can manually apply the migration later with: npx prisma migrate deploy"
        return 0
    }
    
    success "Database indexes applied via Prisma migration"
}

# Test application
test_application() {
    log "Testing application functionality..."
    
    # Test if the application can start
    timeout 30 npm run build > /dev/null 2>&1 || {
        error "Application build failed"
        return 1
    }
    
    success "Application build successful"
}

# Rollback function
rollback() {
    error "Rolling back changes..."
    
    # Restore package.json
    if [ -f "$BACKUP_DIR/package.json.backup" ]; then
        cp "$BACKUP_DIR/package.json.backup" package.json
    fi
    
    # Restore package-lock.json
    if [ -f "$BACKUP_DIR/package-lock.json.backup" ]; then
        cp "$BACKUP_DIR/package-lock.json.backup" package-lock.json
    fi
    
    # Restore middleware
    if [ -f "$BACKUP_DIR/middleware.ts.backup" ]; then
        cp "$BACKUP_DIR/middleware.ts.backup" src/middleware.ts
    fi
    
    # Reinstall original dependencies
    npm install > /dev/null 2>&1 || true
    
    error "Rollback completed. Please check your application."
}

# Main execution
main() {
    log "Starting Phase 1: Security & Stability Upgrade"
    
    # Trap errors for rollback
    trap rollback ERR
    
    preflight_checks
    backup_files
    backup_database
    
    install_dependencies
    update_dependencies
    apply_database_indexes
    
    test_application
    
    success "Phase 1 upgrade completed successfully!"
    success "Backup location: $BACKUP_DIR"
    success "Log file: $LOG_FILE"
    
    log "Next steps:"
    log "1. Test your application thoroughly"
    log "2. Monitor logs for any issues"
    log "3. If everything works, you can remove the backup after 7 days"
    
    # Show summary
    echo ""
    echo "=== UPGRADE SUMMARY ==="
    echo "✅ Security configuration added"
    echo "✅ Database security wrapper created"
    echo "✅ Dependencies updated (safe updates only)"
    echo "✅ Database indexes applied"
    echo "✅ Environment template created"
    echo ""
    echo "Review the new files:"
    echo "- src/config/security.ts"
    echo "- src/utils/secureDb.ts"
    echo "- env.example"
    echo "- prisma/migrations/[timestamp]_add_performance_indexes/"
}

# Check if running with --dry-run
if [ "$1" = "--dry-run" ]; then
    log "DRY RUN MODE - No changes will be made"
    log "Would perform the following actions:"
    log "1. Create backup in $BACKUP_DIR"
    log "2. Install security dependencies"
    log "3. Update node-fetch to v3"
    log "4. Apply database indexes via Prisma migration"
    log "5. Test application build"
    exit 0
fi

# Ask for confirmation
echo "This script will upgrade your application with Phase 1 security improvements."
echo "A backup will be created in: $BACKUP_DIR"
echo ""
read -p "Do you want to continue? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    log "Upgrade cancelled by user"
    exit 0
fi

# Run main function
main 