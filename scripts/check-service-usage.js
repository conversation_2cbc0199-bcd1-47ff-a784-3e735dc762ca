#!/usr/bin/env node

const { PrismaClient } = require('@prisma/client');

async function checkServiceUsage() {
  const prisma = new PrismaClient();
  
  try {
    console.log('🔍 Checking Service Usage in Invoices\n');
    
    // Get all services
    const services = await prisma.service.findMany({
      select: {
        id: true,
        name: true,
        category: true,
        price: true
      }
    });
    
    console.log(`📊 Total Services: ${services.length}\n`);
    
    // Check usage for each service
    const usageResults = [];
    
    for (const service of services) {
      const invoiceItems = await prisma.invoiceItem.findMany({
        where: { serviceId: service.id },
        include: {
          invoice: {
            select: {
              id: true,
              invoiceNumber: true,
              customerName: true,
              status: true
            }
          }
        }
      });
      
      usageResults.push({
        service,
        invoiceCount: invoiceItems.length,
        invoices: invoiceItems.map(item => ({
          number: item.invoice.invoiceNumber,
          client: item.invoice.customerName,
          status: item.invoice.status
        }))
      });
    }
    
    // Separate deletable and non-deletable services
    const deletableServices = usageResults.filter(result => result.invoiceCount === 0);
    const nonDeletableServices = usageResults.filter(result => result.invoiceCount > 0);
    
    console.log('✅ DELETABLE SERVICES:');
    console.log('======================');
    if (deletableServices.length === 0) {
      console.log('❌ No services can be safely deleted');
    } else {
      deletableServices.forEach((result, index) => {
        console.log(`\n${index + 1}. ${result.service.name}`);
        console.log(`   ID: ${result.service.id}`);
        console.log(`   Category: ${result.service.category}`);
        console.log(`   Price: KES ${result.service.price.toLocaleString()}`);
        console.log(`   ✅ Safe to delete (not used in any invoices)`);
      });
    }
    
    console.log('\n🚫 NON-DELETABLE SERVICES:');
    console.log('==========================');
    if (nonDeletableServices.length === 0) {
      console.log('✅ All services can be safely deleted');
    } else {
      nonDeletableServices.forEach((result, index) => {
        console.log(`\n${index + 1}. ${result.service.name}`);
        console.log(`   ID: ${result.service.id}`);
        console.log(`   Category: ${result.service.category}`);
        console.log(`   Price: KES ${result.service.price.toLocaleString()}`);
        console.log(`   ❌ Used in ${result.invoiceCount} invoice(s):`);
        
        result.invoices.forEach(invoice => {
          console.log(`      • ${invoice.number} (${invoice.client}) - ${invoice.status}`);
        });
      });
    }
    
    // Check the specific problematic service
    const problematicId = 'ec86fa06-909c-4f67-bfbd-2380771db50d';
    const problematicService = usageResults.find(result => result.service.id === problematicId);
    
    console.log('\n🎯 PROBLEMATIC SERVICE ANALYSIS:');
    console.log('===============================');
    console.log(`Service ID: ${problematicId}`);
    
    if (problematicService) {
      console.log(`Service Name: ${problematicService.service.name}`);
      console.log(`Used in ${problematicService.invoiceCount} invoice(s)`);
      
      if (problematicService.invoiceCount > 0) {
        console.log('\n📋 Invoices using this service:');
        problematicService.invoices.forEach(invoice => {
          console.log(`   • ${invoice.number} (${invoice.client}) - ${invoice.status}`);
        });
        console.log('\n💡 To delete this service, you must first:');
        console.log('   1. Edit the invoices listed above');
        console.log('   2. Remove this service from each invoice');
        console.log('   3. Then try deleting the service again');
      } else {
        console.log('✅ This service should be deletable (no invoice references found)');
      }
    } else {
      console.log('❌ Service not found');
    }
    
    console.log('\n📊 SUMMARY:');
    console.log('===========');
    console.log(`✅ Deletable services: ${deletableServices.length}`);
    console.log(`🚫 Non-deletable services: ${nonDeletableServices.length}`);
    console.log(`📋 Total invoices with service references: ${nonDeletableServices.reduce((sum, result) => sum + result.invoiceCount, 0)}`);
    
  } catch (error) {
    console.error('❌ Error checking service usage:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkServiceUsage(); 