#!/usr/bin/env node

/**
 * <PERSON>ript to generate Open Graph images for Mocky Digital
 * This creates branded 1200x630 images for social media sharing
 */

const fs = require('fs');
const path = require('path');

// Define the images we need to create
const ogImages = [
  {
    filename: 'default-og.jpg',
    title: 'Mocky Digital',
    subtitle: 'Professional Digital Agency in Kenya',
    description: 'Web Design • Graphic Design • Digital Marketing',
    color: '#FF5400'
  },
  {
    filename: 'about/team-og.jpg',
    title: 'About Mocky Digital',
    subtitle: 'Meet Our Expert Team',
    description: 'Leading Digital Agency in Kenya',
    color: '#0A2647'
  },
  {
    filename: 'blog-default-og.jpg',
    title: 'Mocky Digital Blog',
    subtitle: 'Insights & Tips',
    description: 'Design • Development • Marketing',
    color: '#FF5400'
  },
  {
    filename: 'social-media-marketing-og.jpg',
    title: 'Social Media Marketing',
    subtitle: 'Mocky Digital Services',
    description: 'Grow Your Brand Online',
    color: '#0A2647'
  },
  {
    filename: 'web-development-og.jpg',
    title: 'Web Development',
    subtitle: 'Mocky Digital Services',
    description: 'Professional Websites That Convert',
    color: '#FF5400'
  },
  {
    filename: 'contact-og.jpg',
    title: 'Contact Mocky Digital',
    subtitle: 'Let\'s Work Together',
    description: 'Get Your Free Quote Today',
    color: '#0A2647'
  }
];

// Create directories if they don't exist
function ensureDirectoryExists(filePath) {
  const dir = path.dirname(filePath);
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
    console.log(`Created directory: ${dir}`);
  }
}

// Generate SVG template for Open Graph images
function generateOGImageSVG(config) {
  return `<svg width="1200" height="630" viewBox="0 0 1200 630" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:${config.color};stop-opacity:0.1" />
      <stop offset="100%" style="stop-color:${config.color};stop-opacity:0.05" />
    </linearGradient>
    <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
      <path d="M 40 0 L 0 0 0 40" fill="none" stroke="${config.color}" stroke-width="0.5" opacity="0.1"/>
    </pattern>
  </defs>
  
  <!-- Background -->
  <rect width="1200" height="630" fill="white"/>
  <rect width="1200" height="630" fill="url(#bgGradient)"/>
  <rect width="1200" height="630" fill="url(#grid)"/>
  
  <!-- Brand accent -->
  <rect x="0" y="0" width="8" height="630" fill="${config.color}"/>
  
  <!-- Logo area -->
  <circle cx="120" cy="120" r="40" fill="${config.color}" opacity="0.1"/>
  <text x="120" y="130" text-anchor="middle" font-family="Arial, sans-serif" font-size="32" font-weight="bold" fill="${config.color}">M</text>
  
  <!-- Main content -->
  <text x="80" y="280" font-family="Arial, sans-serif" font-size="64" font-weight="bold" fill="#1a1a1a">${config.title}</text>
  <text x="80" y="340" font-family="Arial, sans-serif" font-size="36" font-weight="600" fill="${config.color}">${config.subtitle}</text>
  <text x="80" y="400" font-family="Arial, sans-serif" font-size="24" fill="#666666">${config.description}</text>
  
  <!-- Website URL -->
  <text x="80" y="550" font-family="Arial, sans-serif" font-size="20" fill="#999999">mocky.co.ke</text>
  
  <!-- Decorative elements -->
  <circle cx="1000" cy="150" r="80" fill="${config.color}" opacity="0.05"/>
  <circle cx="1100" cy="400" r="60" fill="${config.color}" opacity="0.08"/>
  <rect x="900" y="500" width="200" height="4" fill="${config.color}" opacity="0.3"/>
</svg>`;
}

// Generate instruction file for manual creation
function generateInstructions() {
  const instructions = `# Open Graph Images Generation Instructions

## Required Images (1200x630px):

${ogImages.map(img => `
### ${img.filename}
- **Title**: ${img.title}
- **Subtitle**: ${img.subtitle}
- **Description**: ${img.description}
- **Primary Color**: ${img.color}
- **Background**: White with subtle pattern
- **Brand**: Include Mocky Digital logo/branding
`).join('\n')}

## Design Guidelines:
1. **Dimensions**: Exactly 1200x630 pixels (Facebook/Twitter optimal)
2. **Safe Area**: Keep important text within 1200x600 center area
3. **Brand Colors**: 
   - Primary Orange: #FF5400
   - Primary Blue: #0A2647
   - White: #FFFFFF
   - Gray: #666666
4. **Typography**: Clean, readable fonts (Arial, Helvetica, or similar)
5. **Logo**: Include Mocky Digital logo or "M" monogram
6. **Quality**: High resolution, optimized for web

## Tools Recommended:
- Canva (templates available)
- Figma (design tool)
- Adobe Photoshop
- Online OG image generators

## File Locations:
Save all images in: \`public/images/\`

## Testing:
After creating images, test with:
- Facebook Sharing Debugger: https://developers.facebook.com/tools/debug/
- Twitter Card Validator: https://cards-dev.twitter.com/validator
- LinkedIn Post Inspector: https://www.linkedin.com/post-inspector/
`;

  return instructions;
}

// Main execution
console.log('🎨 Generating Open Graph image templates and instructions...\n');

// Create SVG templates
ogImages.forEach(config => {
  const svgPath = path.join(__dirname, '..', 'public', 'images', config.filename.replace('.jpg', '.svg'));
  ensureDirectoryExists(svgPath);
  
  const svgContent = generateOGImageSVG(config);
  fs.writeFileSync(svgPath, svgContent);
  console.log(`✅ Created SVG template: ${svgPath}`);
});

// Create instructions file
const instructionsPath = path.join(__dirname, 'og-images-instructions.md');
fs.writeFileSync(instructionsPath, generateInstructions());
console.log(`📋 Created instructions: ${instructionsPath}`);

console.log('\n🎯 Next Steps:');
console.log('1. Use the SVG templates as guides');
console.log('2. Create high-quality JPG versions (1200x630px)');
console.log('3. Save them in public/images/ directory');
console.log('4. Test with social media validators');
console.log('\n📖 See og-images-instructions.md for detailed guidelines');
