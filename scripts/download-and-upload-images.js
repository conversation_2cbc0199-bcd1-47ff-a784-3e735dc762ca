#!/usr/bin/env node

const { PrismaClient } = require('@prisma/client');
const https = require('https');
const http = require('http');
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

const prisma = new PrismaClient();

// Configuration
const CONFIG = {
  // Local temporary directory for downloads
  tempDir: './temp-images',
  
  // S3 configuration (will be loaded from database)
  s3Config: null,
  
  // S3 path prefix for catalogue images
  s3Prefix: 'images/catalogue/',
  
  // Supported image formats
  supportedFormats: ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp'],
  
  // Maximum file size (10MB)
  maxFileSize: 10 * 1024 * 1024,
  
  // Download timeout (30 seconds)
  downloadTimeout: 30000
};

class ImageDownloader {
  constructor() {
    this.downloadedCount = 0;
    this.failedCount = 0;
    this.s3Client = null;
  }

  async init() {
    console.log('🚀 Initializing Image Downloader...');
    
    // Create temp directory
    if (!fs.existsSync(CONFIG.tempDir)) {
      fs.mkdirSync(CONFIG.tempDir, { recursive: true });
      console.log(`📁 Created temp directory: ${CONFIG.tempDir}`);
    }
    
    // Load S3 configuration from database
    await this.loadS3Config();
    
    // Initialize S3 client
    await this.initS3Client();
    
    console.log('✅ Image Downloader initialized');
  }

  async loadS3Config() {
    try {
      const s3Config = await prisma.storageConfig.findFirst({
        where: { isDefault: true }
      });
      
      if (!s3Config) {
        throw new Error('No default S3 configuration found in database');
      }
      
      CONFIG.s3Config = s3Config;
      console.log(`📡 Loaded S3 config: ${s3Config.bucketName} (${s3Config.region})`);
      
    } catch (error) {
      console.error('❌ Failed to load S3 configuration:', error);
      throw error;
    }
  }

  async initS3Client() {
    try {
      // Dynamic import of AWS SDK
      const { S3Client, PutObjectCommand } = await import('@aws-sdk/client-s3');
      
      this.s3Client = new S3Client({
        region: CONFIG.s3Config.region,
        endpoint: CONFIG.s3Config.endpoint,
        credentials: {
          accessKeyId: CONFIG.s3Config.accessKeyId,
          secretAccessKey: CONFIG.s3Config.secretAccessKey,
        },
        forcePathStyle: true, // Required for some S3-compatible services
      });
      
      this.PutObjectCommand = PutObjectCommand;
      console.log('✅ S3 client initialized');
      
    } catch (error) {
      console.error('❌ Failed to initialize S3 client:', error);
      console.log('💡 Make sure @aws-sdk/client-s3 is installed: npm install @aws-sdk/client-s3');
      throw error;
    }
  }

  async downloadImage(url, filename) {
    return new Promise((resolve, reject) => {
      const filePath = path.join(CONFIG.tempDir, filename);
      const file = fs.createWriteStream(filePath);
      
      const client = url.startsWith('https:') ? https : http;
      
      const request = client.get(url, { timeout: CONFIG.downloadTimeout }, (response) => {
        // Check if response is successful
        if (response.statusCode !== 200) {
          reject(new Error(`HTTP ${response.statusCode}: ${response.statusMessage}`));
          return;
        }
        
        // Check content length
        const contentLength = parseInt(response.headers['content-length'] || '0');
        if (contentLength > CONFIG.maxFileSize) {
          reject(new Error(`File too large: ${contentLength} bytes`));
          return;
        }
        
        let downloadedBytes = 0;
        
        response.on('data', (chunk) => {
          downloadedBytes += chunk.length;
          if (downloadedBytes > CONFIG.maxFileSize) {
            request.destroy();
            reject(new Error(`File too large: ${downloadedBytes} bytes`));
            return;
          }
        });
        
        response.pipe(file);
        
        file.on('finish', () => {
          file.close();
          resolve(filePath);
        });
        
        file.on('error', (err) => {
          fs.unlink(filePath, () => {}); // Delete the file
          reject(err);
        });
      });
      
      request.on('timeout', () => {
        request.destroy();
        reject(new Error('Download timeout'));
      });
      
      request.on('error', (err) => {
        fs.unlink(filePath, () => {}); // Delete the file
        reject(err);
      });
    });
  }

  async uploadToS3(localPath, s3Key) {
    try {
      const fileContent = fs.readFileSync(localPath);
      const contentType = this.getContentType(localPath);
      
      const uploadParams = {
        Bucket: CONFIG.s3Config.bucketName,
        Key: s3Key,
        Body: fileContent,
        ContentType: contentType,
        ACL: 'public-read', // Make images publicly accessible
      };
      
      const command = new this.PutObjectCommand(uploadParams);
      await this.s3Client.send(command);
      
      // Generate the public URL
      const s3Url = `${CONFIG.s3Config.endpoint}/${CONFIG.s3Config.bucketName}/${s3Key}`;
      
      return s3Url;
      
    } catch (error) {
      console.error(`❌ S3 upload failed for ${s3Key}:`, error);
      throw error;
    }
  }

  getContentType(filePath) {
    const ext = path.extname(filePath).toLowerCase();
    const contentTypes = {
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.gif': 'image/gif',
      '.webp': 'image/webp',
      '.avif': 'image/avif',
      '.bmp': 'image/bmp'
    };
    return contentTypes[ext] || 'image/jpeg';
  }

  generateUniqueFilename(originalUrl, productId) {
    const urlHash = crypto.createHash('md5').update(originalUrl).digest('hex');
    const ext = this.getFileExtension(originalUrl);
    return `product_${productId}_${urlHash}${ext}`;
  }

  getFileExtension(url) {
    try {
      const pathname = new URL(url).pathname;
      const ext = path.extname(pathname).toLowerCase();
      return CONFIG.supportedFormats.includes(ext) ? ext : '.jpg';
    } catch {
      return '.jpg';
    }
  }

  async processProduct(product) {
    console.log(`\n📦 Processing: ${product.service}`);
    
    const imageFields = ['imageUrl', 'imageUrl2', 'imageUrl3'];
    const updatedImageUrls = {};
    
    for (const field of imageFields) {
      const imageUrl = product[field];
      
      if (!imageUrl || imageUrl.startsWith(CONFIG.s3Config.endpoint)) {
        // Skip if no URL or already S3 URL
        continue;
      }
      
      try {
        console.log(`  📥 Downloading ${field}...`);
        
        // Generate unique filename
        const filename = this.generateUniqueFilename(imageUrl, product.id);
        
        // Download image
        const localPath = await this.downloadImage(imageUrl, filename);
        console.log(`  ✅ Downloaded: ${filename}`);
        
        // Upload to S3
        const s3Key = `${CONFIG.s3Prefix}${filename}`;
        const s3Url = await this.uploadToS3(localPath, s3Key);
        console.log(`  ☁️  Uploaded to S3: ${s3Key}`);
        
        // Clean up local file
        fs.unlinkSync(localPath);
        
        // Store new URL
        updatedImageUrls[field] = s3Url;
        this.downloadedCount++;
        
      } catch (error) {
        console.error(`  ❌ Failed to process ${field}:`, error.message);
        this.failedCount++;
        // Continue with other images
      }
    }
    
    // Update database if we have new URLs
    if (Object.keys(updatedImageUrls).length > 0) {
      try {
        await prisma.catalogue.update({
          where: { id: product.id },
          data: updatedImageUrls
        });
        console.log(`  💾 Updated database with ${Object.keys(updatedImageUrls).length} new URLs`);
      } catch (error) {
        console.error(`  ❌ Database update failed:`, error.message);
      }
    }
  }

  async processAllProducts() {
    try {
      // Get all products with external image URLs
      const products = await prisma.catalogue.findMany({
        where: {
          OR: [
            { imageUrl: { not: null, not: { startsWith: CONFIG.s3Config.endpoint } } },
            { imageUrl2: { not: null, not: { startsWith: CONFIG.s3Config.endpoint } } },
            { imageUrl3: { not: null, not: { startsWith: CONFIG.s3Config.endpoint } } }
          ]
        }
      });
      
      console.log(`\n🎯 Found ${products.length} products with external images`);
      
      for (let i = 0; i < products.length; i++) {
        const product = products[i];
        console.log(`\n[${i + 1}/${products.length}] Processing product ID: ${product.id}`);
        
        await this.processProduct(product);
        
        // Add small delay to be respectful
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
      
    } catch (error) {
      console.error('❌ Error processing products:', error);
      throw error;
    }
  }

  async cleanup() {
    try {
      // Remove temp directory
      if (fs.existsSync(CONFIG.tempDir)) {
        fs.rmSync(CONFIG.tempDir, { recursive: true, force: true });
        console.log(`🗑️  Cleaned up temp directory: ${CONFIG.tempDir}`);
      }
      
      // Disconnect Prisma
      await prisma.$disconnect();
      
    } catch (error) {
      console.error('⚠️  Cleanup warning:', error);
    }
  }

  async showSummary() {
    console.log('\n📊 MIGRATION SUMMARY');
    console.log('====================');
    console.log(`✅ Successfully downloaded: ${this.downloadedCount} images`);
    console.log(`❌ Failed downloads: ${this.failedCount} images`);
    console.log(`☁️  All images now stored in S3: ${CONFIG.s3Config.bucketName}`);
    console.log(`🔗 S3 prefix: ${CONFIG.s3Prefix}`);
    
    if (this.failedCount > 0) {
      console.log('\n⚠️  Some images failed to download. Check the logs above for details.');
    }
    
    console.log('\n🎉 Image migration completed!');
  }
}

// Main execution
async function main() {
  const downloader = new ImageDownloader();
  
  try {
    await downloader.init();
    await downloader.processAllProducts();
    await downloader.showSummary();
    
  } catch (error) {
    console.error('❌ Image migration failed:', error);
    process.exit(1);
    
  } finally {
    await downloader.cleanup();
  }
}

// Command line handling
const args = process.argv.slice(2);

if (args.includes('--help') || args.includes('-h')) {
  console.log(`
📥 Image Downloader & S3 Uploader

This script downloads all external images from your catalogue and uploads them to S3.

Usage:
  node scripts/download-and-upload-images.js

Features:
  - Downloads images from external URLs
  - Uploads to your configured S3 bucket
  - Updates database with new S3 URLs
  - Handles duplicates and errors gracefully
  - Cleans up temporary files

Requirements:
  - @aws-sdk/client-s3 package
  - S3 configuration in database
  - Internet connection
  `);
} else {
  main();
}

module.exports = { ImageDownloader, CONFIG }; 