const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function checkAdminUser() {
  try {
    console.log('🔍 Checking Admin User Details...\n');
    
    // Check environment variables
    console.log('📋 Environment Variables:');
    console.log(`ADMIN_USERNAME: ${process.env.ADMIN_USERNAME || 'NOT SET'}`);
    console.log(`ADMIN_EMAIL: ${process.env.ADMIN_EMAIL || 'NOT SET'}`);
    console.log(`ADMIN_PASSWORD: ${process.env.ADMIN_PASSWORD ? '[SET]' : 'NOT SET'}`);
    console.log(`ADMIN_NAME: ${process.env.ADMIN_NAME || 'NOT SET'}\n`);
    
    // Check admin role
    const adminRole = await prisma.role.findFirst({
      where: { name: 'admin' }
    });
    
    if (!adminRole) {
      console.log('❌ Admin role not found!');
      return;
    }
    
    console.log(`✅ Admin role found: ID ${adminRole.id}\n`);
    
    // Check admin users
    const adminUsers = await prisma.user.findMany({
      where: { roleId: adminRole.id },
      select: {
        id: true,
        username: true,
        email: true,
        name: true,
        active: true,
        createdAt: true,
        role: {
          select: {
            name: true,
            permissions: true
          }
        }
      }
    });
    
    if (adminUsers.length === 0) {
      console.log('❌ No admin users found!');
      return;
    }
    
    console.log(`✅ Found ${adminUsers.length} admin user(s):`);
    adminUsers.forEach((user, index) => {
      console.log(`\n${index + 1}. Admin User:`);
      console.log(`   ID: ${user.id}`);
      console.log(`   Username: ${user.username}`);
      console.log(`   Email: ${user.email}`);
      console.log(`   Name: ${user.name}`);
      console.log(`   Active: ${user.active}`);
      console.log(`   Role: ${user.role.name}`);
      console.log(`   Created: ${user.createdAt}`);
    });
    
    // Check which credentials to use for login
    console.log('\n🔑 Login Credentials:');
    const primaryUser = adminUsers[0];
    console.log(`Username/Email for login: ${primaryUser.username}`);
    console.log(`Password: ${process.env.ADMIN_PASSWORD || 'Jack75522r'} (default if env not set)`);
    
    // Test if all users are active
    const activeAdmins = adminUsers.filter(user => user.active);
    console.log(`\n✅ Active admin users: ${activeAdmins.length} of ${adminUsers.length}`);
    
    if (activeAdmins.length === 0) {
      console.log('⚠️  WARNING: No admin users are active! This could prevent login.');
    }
    
  } catch (error) {
    console.error('❌ Error checking admin user:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkAdminUser(); 