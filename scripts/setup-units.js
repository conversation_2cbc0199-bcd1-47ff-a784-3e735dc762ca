const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

const units = [
  {
    name: 'copy',
    displayName: 'Copy',
    plural: 'Copies',
    shortForm: 'pcs',
    category: 'general',
    order: 1
  },
  {
    name: 'meter',
    displayName: 'Meter',
    plural: 'Meters',
    shortForm: 'm',
    category: 'dimension',
    order: 2
  },
  {
    name: 'square_meter',
    displayName: 'Square Meter',
    plural: 'Square Meters',
    shortForm: 'sq m',
    category: 'area',
    order: 3
  },
  {
    name: 'page',
    displayName: 'Page',
    plural: 'Pages',
    shortForm: 'pg',
    category: 'general',
    order: 4
  },
  {
    name: 'hour',
    displayName: 'Hour',
    plural: 'Hours',
    shortForm: 'hr',
    category: 'time',
    order: 5
  },
  {
    name: 'piece',
    displayName: 'Piece',
    plural: 'Pieces',
    shortForm: 'pc',
    category: 'general',
    order: 6
  },
  {
    name: 'set',
    displayName: 'Set',
    plural: 'Sets',
    shortForm: 'set',
    category: 'general',
    order: 7
  },
  {
    name: 'foot',
    displayName: 'Foot',
    plural: 'Feet',
    shortForm: 'ft',
    category: 'dimension',
    order: 8
  },
  {
    name: 'square_foot',
    displayName: 'Square Foot',
    plural: 'Square Feet',
    shortForm: 'sq ft',
    category: 'area',
    order: 9
  },
  {
    name: 'item',
    displayName: 'Item',
    plural: 'Items',
    shortForm: 'item',
    category: 'general',
    order: 10
  }
];

async function setupUnits() {
  try {
    console.log('🚀 Setting up units...');

    // Create units
    for (const unit of units) {
      const existingUnit = await prisma.unit.findUnique({
        where: { name: unit.name }
      });

      if (!existingUnit) {
        await prisma.unit.create({
          data: unit
        });
        console.log(`✅ Created unit: ${unit.displayName}`);
      } else {
        console.log(`⚠️  Unit already exists: ${unit.displayName}`);
      }
    }

    // Update existing catalogue items to use the new unit system
    // Map old unitType values to new unit names
    const unitMappings = {
      'meter': 'meter',
      'meters': 'meter',
      'copy': 'copy',
      'copies': 'copy',
      'page': 'page',
      'pages': 'page',
      'piece': 'piece',
      'pieces': 'piece',
      'item': 'item',
      'items': 'item',
      'set': 'set',
      'sets': 'set'
    };

    console.log('\n🔄 Updating existing catalogue items...');
    
    const catalogueItems = await prisma.catalogue.findMany({
      where: {
        unitType: { not: null },
        unitId: null
      }
    });

    for (const item of catalogueItems) {
      if (item.unitType) {
        const normalizedUnitType = item.unitType.toLowerCase().trim();
        const unitName = unitMappings[normalizedUnitType] || 'copy'; // Default to 'copy'
        
        const unit = await prisma.unit.findUnique({
          where: { name: unitName }
        });

        if (unit) {
          await prisma.catalogue.update({
            where: { id: item.id },
            data: { unitId: unit.id }
          });
          console.log(`✅ Updated ${item.service} to use unit: ${unit.displayName}`);
        }
      }
    }

    console.log('\n✨ Unit setup completed successfully!');

  } catch (error) {
    console.error('❌ Error setting up units:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  setupUnits()
    .then(() => {
      console.log('🎉 Setup completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Setup failed:', error);
      process.exit(1);
    });
}

module.exports = { setupUnits }; 