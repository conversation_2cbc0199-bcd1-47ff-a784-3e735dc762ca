const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');

const prisma = new PrismaClient();

async function resetUsersAndSeed() {
  try {
    console.log('🗑️  RESETTING USER TABLE AND SEEDING FRESH ADMIN\n');
    console.log('═'.repeat(60));
    
    // Step 1: Delete all existing users
    console.log('\n1️⃣ DELETING ALL EXISTING USERS');
    console.log('─'.repeat(40));
    
    // First, delete activity logs that reference users
    const activityLogsDeleted = await prisma.activityLog.deleteMany({});
    console.log(`✅ Deleted ${activityLogsDeleted.count} activity logs`);
    
    // Then delete all users
    const usersDeleted = await prisma.user.deleteMany({});
    console.log(`✅ Deleted ${usersDeleted.count} users`);
    
    // Step 2: Ensure admin role exists
    console.log('\n2️⃣ ENSURING ADMIN ROLE EXISTS');
    console.log('─'.repeat(40));
    
    let adminRole = await prisma.role.findFirst({
      where: { name: 'admin' }
    });
    
    if (!adminRole) {
      console.log('Creating admin role...');
      adminRole = await prisma.role.create({
        data: {
          name: 'admin',
          description: 'Administrator with full access',
          permissions: ['*']
        }
      });
      console.log('✅ Admin role created');
    } else {
      console.log('✅ Admin role already exists');
      
      // Ensure it has the right permissions
      if (!adminRole.permissions.includes('*')) {
        await prisma.role.update({
          where: { id: adminRole.id },
          data: { permissions: ['*'] }
        });
        console.log('✅ Admin role permissions updated');
      }
    }
    
    console.log(`   Role ID: ${adminRole.id}`);
    console.log(`   Permissions: ${JSON.stringify(adminRole.permissions)}`);
    
    // Step 3: Get admin details from environment
    console.log('\n3️⃣ READING ADMIN DETAILS FROM ENVIRONMENT');
    console.log('─'.repeat(40));
    
    const adminUsername = process.env.ADMIN_USERNAME || 'admin';
    const adminPassword = process.env.ADMIN_PASSWORD || 'Jack75522r';
    const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
    const adminName = process.env.ADMIN_NAME || 'System Administrator';
    
    console.log(`Username: ${adminUsername}`);
    console.log(`Email: ${adminEmail}`);
    console.log(`Name: ${adminName}`);
    console.log(`Password: [${adminPassword.length} characters]`);
    
    // Step 4: Create fresh admin user
    console.log('\n4️⃣ CREATING FRESH ADMIN USER');
    console.log('─'.repeat(40));
    
    // Hash the password
    console.log('🔐 Hashing password...');
    const passwordHash = await bcrypt.hash(adminPassword, 12);
    console.log(`✅ Password hashed (${passwordHash.substring(0, 20)}...)`);
    
    // Create the admin user
    const adminUser = await prisma.user.create({
      data: {
        username: adminUsername,
        email: adminEmail,
        name: adminName,
        passwordHash: passwordHash,
        roleId: adminRole.id,
        active: true,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      include: {
        role: true
      }
    });
    
    console.log('✅ Admin user created successfully!');
    console.log(`   ID: ${adminUser.id}`);
    console.log(`   Username: ${adminUser.username}`);
    console.log(`   Email: ${adminUser.email}`);
    console.log(`   Active: ${adminUser.active}`);
    console.log(`   Role: ${adminUser.role.name}`);
    
    // Step 5: Test password verification
    console.log('\n5️⃣ TESTING PASSWORD VERIFICATION');
    console.log('─'.repeat(40));
    
    const passwordTest = await bcrypt.compare(adminPassword, adminUser.passwordHash);
    if (passwordTest) {
      console.log('✅ Password verification: SUCCESS');
    } else {
      console.log('❌ Password verification: FAILED');
      throw new Error('Password verification failed after creation!');
    }
    
    // Step 6: Test the full authentication flow
    console.log('\n6️⃣ TESTING AUTHENTICATION FLOW');
    console.log('─'.repeat(40));
    
    // Simulate findUserByUsernameOrEmail
    const foundUser = await prisma.user.findFirst({
      where: {
        OR: [
          { username: adminUsername },
          { email: adminUsername }
        ],
        active: true
      },
      include: {
        role: true
      }
    });
    
    if (foundUser) {
      console.log('✅ User lookup: SUCCESS');
      
      // Test password again
      const authTest = await bcrypt.compare(adminPassword, foundUser.passwordHash);
      if (authTest) {
        console.log('✅ Authentication test: SUCCESS');
        
        // Create a test activity log
        await prisma.activityLog.create({
          data: {
            userId: foundUser.id,
            action: 'user_reset',
            details: 'User table reset and admin user recreated',
            ipAddress: 'localhost',
            userAgent: 'Reset Script'
          }
        });
        console.log('✅ Activity log test: SUCCESS');
        
      } else {
        console.log('❌ Authentication test: FAILED');
      }
    } else {
      console.log('❌ User lookup: FAILED');
    }
    
    // Step 7: Clear application cache and restart
    console.log('\n7️⃣ CLEARING CACHE AND RESTARTING');
    console.log('─'.repeat(40));
    
    const { execSync } = require('child_process');
    
    try {
      console.log('🗑️  Clearing Next.js cache...');
      execSync('rm -rf .next', { stdio: 'inherit' });
      
      console.log('🔨 Rebuilding application...');
      execSync('npm run build', { stdio: 'inherit' });
      
      console.log('🔄 Restarting PM2...');
      execSync('pm2 restart all', { stdio: 'inherit' });
      
      console.log('✅ Application reset complete');
    } catch (error) {
      console.log('⚠️  Manual restart required');
    }
    
    console.log('\n═'.repeat(60));
    console.log('🎉 USER TABLE RESET COMPLETED SUCCESSFULLY!');
    console.log('');
    console.log('📋 LOGIN CREDENTIALS:');
    console.log(`🌐 URL: https://mocky.co.ke/admin/login`);
    console.log(`👤 Username: ${adminUsername}`);
    console.log(`🔑 Password: ${adminPassword}`);
    console.log('');
    console.log('💡 NEXT STEPS:');
    console.log('1. Clear your browser cache and cookies');
    console.log('2. Try logging in with the credentials above');
    console.log('3. If still failing, check browser console for errors');
    console.log('');
    console.log('✅ The authentication system has been completely reset!');
    
  } catch (error) {
    console.error('❌ Reset failed:', error);
    console.log('\nRolling back changes...');
    
    // If something went wrong, we should still have at least tried to create an admin user
    try {
      const userCount = await prisma.user.count();
      if (userCount === 0) {
        console.log('No users exist, attempting emergency admin creation...');
        
        let adminRole = await prisma.role.findFirst({ where: { name: 'admin' } });
        if (!adminRole) {
          adminRole = await prisma.role.create({
            data: {
              name: 'admin',
              description: 'Administrator with full access',
              permissions: ['*']
            }
          });
        }
        
        const emergencyHash = await bcrypt.hash('Jack75522r', 12);
        await prisma.user.create({
          data: {
            username: 'admin',
            email: '<EMAIL>',
            name: 'Emergency Admin',
            passwordHash: emergencyHash,
            roleId: adminRole.id,
            active: true
          }
        });
        
        console.log('✅ Emergency admin user created');
      }
    } catch (emergencyError) {
      console.error('❌ Emergency admin creation failed:', emergencyError);
    }
  } finally {
    await prisma.$disconnect();
  }
}

resetUsersAndSeed(); 