const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  console.log('🔍 Adding missing services from PrintShop.co.ke...');

  try {
    // Get existing units
    const units = await prisma.unit.findMany();
    const unitsMap = {};
    units.forEach(unit => {
      unitsMap[unit.name] = unit;
    });

    // Missing Services from PrintShop.co.ke
    console.log('📋 Creating missing PrintShop services...');
    const missingServices = [
      // Brochures (distinct from flyers)
      {
        service: 'A4 Brochures',
        description: 'Professional A4 brochures with multiple pages',
        price: 120,
        designFee: 3500,
        category: 'Marketing Materials',
        features: ['Multi-page Layout', 'Professional Binding', 'Quality Paper', 'Custom Design'],
        icon: '📖',
        popular: true,
        maxQuantity: 1000,
        minQuantity: 50,
        paperTypes: ['Art Paper 157gsm', 'Art Paper 200gsm'],
        pricingTiers: {
          '50-99': 140,
          '100-299': 120,
          '300+': 100
        },
        pricingType: 'tiered',
        unitId: unitsMap.piece?.id
      },
      {
        service: 'A5 Brochures',
        description: 'Compact A5 brochures for detailed information',
        price: 95,
        designFee: 3000,
        category: 'Marketing Materials',
        features: ['Compact Size', 'Detailed Info', 'Professional Finish'],
        icon: '📖',
        popular: false,
        maxQuantity: 1000,
        minQuantity: 50,
        paperTypes: ['Art Paper 157gsm', 'Art Paper 200gsm'],
        pricingTiers: {
          '50-99': 110,
          '100-299': 95,
          '300+': 85
        },
        pricingType: 'tiered',
        unitId: unitsMap.piece?.id
      },

      // Door Frame Banners
      {
        service: 'Door Frame Banners',
        description: 'Custom banners designed for door frames and entrances',
        price: 7300,
        designFee: 3000,
        category: 'Banners',
        features: ['Custom Fit', 'High Visibility', 'Durable Material', 'Easy Installation'],
        icon: '🚪',
        popular: false,
        maxQuantity: 20,
        minQuantity: 1,
        pricingTiers: {
          '1-4': 7500,
          '5+': 7300
        },
        pricingType: 'tiered',
        unitId: unitsMap.piece?.id
      },

      // Water Bottles
      {
        service: 'Branded Water Bottles',
        description: 'Custom branded water bottles for corporate gifts',
        price: 450,
        designFee: 2000,
        category: 'Gifts & Merchandise',
        features: ['BPA Free', 'Custom Branding', 'Durable Material', 'Various Sizes'],
        icon: '💧',
        popular: true,
        maxQuantity: 200,
        minQuantity: 10,
        pricingTiers: {
          '10-24': 500,
          '25-49': 450,
          '50+': 400
        },
        pricingType: 'tiered',
        unitId: unitsMap.piece?.id
      },

      // V-neck T-shirts
      {
        service: 'V-neck T-Shirts',
        description: 'Stylish V-neck t-shirts with custom printing',
        price: 620,
        designFee: 2000,
        category: 'Apparel',
        features: ['V-neck Design', 'Comfortable Fit', 'Quality Fabric', 'Custom Print'],
        icon: '👕',
        popular: false,
        maxQuantity: 300,
        minQuantity: 10,
        pricingTiers: {
          '10-24': 680,
          '25-49': 620,
          '50+': 580
        },
        pricingType: 'tiered',
        unitId: unitsMap.piece?.id
      },

      // School Uniforms
      {
        service: 'School Uniforms',
        description: 'Custom school uniforms with embroidered logos',
        price: 850,
        designFee: 3000,
        category: 'Apparel',
        features: ['Durable Fabric', 'Embroidered Logo', 'Standard Sizing', 'Quality Stitching'],
        icon: '🎓',
        popular: false,
        maxQuantity: 500,
        minQuantity: 20,
        pricingTiers: {
          '20-49': 900,
          '50-99': 850,
          '100+': 800
        },
        pricingType: 'tiered',
        unitId: unitsMap.piece?.id
      },

      // Packaging Items
      {
        service: 'Pizza Boxes',
        description: 'Custom printed pizza boxes for restaurants',
        price: 45,
        designFee: 2500,
        category: 'Packaging',
        features: ['Food Safe', 'Custom Branding', 'Various Sizes', 'Stackable Design'],
        icon: '🍕',
        popular: false,
        maxQuantity: 5000,
        minQuantity: 100,
        pricingTiers: {
          '100-499': 50,
          '500-999': 45,
          '1000+': 40
        },
        pricingType: 'tiered',
        unitId: unitsMap.piece?.id
      },
      {
        service: 'Cake Boxes',
        description: 'Professional cake boxes for bakeries and events',
        price: 85,
        designFee: 2000,
        category: 'Packaging',
        features: ['Food Grade', 'Window Option', 'Secure Closure', 'Custom Sizes'],
        icon: '🎂',
        popular: false,
        maxQuantity: 2000,
        minQuantity: 50,
        pricingTiers: {
          '50-199': 95,
          '200-499': 85,
          '500+': 75
        },
        pricingType: 'tiered',
        unitId: unitsMap.piece?.id
      },
      {
        service: 'Product Packaging Boxes',
        description: 'Custom packaging boxes for retail products',
        price: 120,
        designFee: 3500,
        category: 'Packaging',
        features: ['Custom Design', 'Quality Cardboard', 'Professional Finish', 'Various Sizes'],
        icon: '📦',
        popular: true,
        maxQuantity: 3000,
        minQuantity: 100,
        pricingTiers: {
          '100-299': 130,
          '300-999': 120,
          '1000+': 110
        },
        pricingType: 'tiered',
        unitId: unitsMap.piece?.id
      },

      // Specialized Stickers
      {
        service: 'Clear Stickers',
        description: 'Transparent clear stickers for professional labeling',
        price: 30,
        designFee: 1500,
        category: 'Stickers & Labels',
        features: ['Transparent Material', 'Professional Look', 'Weather Resistant', 'Custom Shapes'],
        icon: '🔍',
        popular: false,
        maxQuantity: 5000,
        minQuantity: 100,
        pricingTiers: {
          '100-499': 35,
          '500-999': 30,
          '1000+': 25
        },
        pricingType: 'tiered',
        unitId: unitsMap.piece?.id
      },
      {
        service: 'Round Stickers',
        description: 'Circular stickers perfect for branding and sealing',
        price: 22,
        designFee: 1200,
        category: 'Stickers & Labels',
        features: ['Perfect Circle', 'Strong Adhesive', 'Various Sizes', 'Custom Colors'],
        icon: '⭕',
        popular: true,
        maxQuantity: 10000,
        minQuantity: 200,
        pricingTiers: {
          '200-999': 25,
          '1000-2999': 22,
          '3000+': 18
        },
        pricingType: 'tiered',
        unitId: unitsMap.piece?.id
      },

      // Promotional Items
      {
        service: 'Wrist Bands',
        description: 'Custom silicone wristbands for events and promotions',
        price: 35,
        designFee: 1500,
        category: 'Gifts & Merchandise',
        features: ['Silicone Material', 'Custom Colors', 'Debossed/Embossed Text', 'Comfortable Fit'],
        icon: '🔗',
        popular: true,
        maxQuantity: 5000,
        minQuantity: 100,
        pricingTiers: {
          '100-499': 40,
          '500-999': 35,
          '1000+': 30
        },
        pricingType: 'tiered',
        unitId: unitsMap.piece?.id
      },
      {
        service: 'Branded Pens',
        description: 'Custom branded ballpoint pens for corporate use',
        price: 45,
        designFee: 1200,
        category: 'Gifts & Merchandise',
        features: ['Smooth Writing', 'Custom Logo', 'Professional Look', 'Bulk Friendly'],
        icon: '🖊️',
        popular: true,
        maxQuantity: 2000,
        minQuantity: 50,
        pricingTiers: {
          '50-199': 50,
          '200-499': 45,
          '500+': 40
        },
        pricingType: 'tiered',
        unitId: unitsMap.piece?.id
      },
      {
        service: 'Flash Drives',
        description: 'Custom branded USB flash drives for data storage',
        price: 680,
        designFee: 2000,
        category: 'Gifts & Merchandise',
        features: ['Various Capacities', 'Custom Logo', 'Data Preloading', 'Protective Cap'],
        icon: '💾',
        popular: false,
        maxQuantity: 200,
        minQuantity: 10,
        pricingTiers: {
          '10-24': 750,
          '25-49': 680,
          '50+': 620
        },
        pricingType: 'tiered',
        unitId: unitsMap.piece?.id
      },
      {
        service: 'Staff ID Cards',
        description: 'Professional staff identification cards with photos',
        price: 85,
        designFee: 2500,
        category: 'Corporate Stationery',
        features: ['Photo ID', 'Laminated Finish', 'Badge Holders', 'Security Features'],
        icon: '🆔',
        popular: true,
        maxQuantity: 500,
        minQuantity: 10,
        pricingTiers: {
          '10-49': 95,
          '50-99': 85,
          '100+': 75
        },
        pricingType: 'tiered',
        unitId: unitsMap.piece?.id
      },
      {
        service: 'Student ID Cards',
        description: 'Professional student identification cards for schools',
        price: 75,
        designFee: 2000,
        category: 'Corporate Stationery',
        features: ['Photo ID', 'School Branding', 'Durable Material', 'Security Features'],
        icon: '🎓',
        popular: false,
        maxQuantity: 1000,
        minQuantity: 20,
        pricingTiers: {
          '20-99': 80,
          '100-299': 75,
          '300+': 70
        },
        pricingType: 'tiered',
        unitId: unitsMap.piece?.id
      },
      {
        service: 'Wall Clocks',
        description: 'Custom branded wall clocks for offices',
        price: 1200,
        designFee: 3000,
        category: 'Gifts & Merchandise',
        features: ['Custom Branding', 'Quality Mechanism', 'Various Designs', 'Office Ready'],
        icon: '🕒',
        popular: false,
        maxQuantity: 50,
        minQuantity: 5,
        pricingTiers: {
          '5-19': 1300,
          '20+': 1200
        },
        pricingType: 'tiered',
        unitId: unitsMap.piece?.id
      },
      {
        service: 'ID Tags & Lanyards',
        description: 'Professional ID tags with custom lanyards',
        price: 120,
        designFee: 1800,
        category: 'Corporate Stationery',
        features: ['Custom Lanyards', 'ID Tag Holders', 'Safety Breakaway', 'Corporate Branding'],
        icon: '🏷️',
        popular: true,
        maxQuantity: 500,
        minQuantity: 20,
        pricingTiers: {
          '20-49': 130,
          '50-99': 120,
          '100+': 110
        },
        pricingType: 'tiered',
        unitId: unitsMap.piece?.id
      },
      {
        service: 'Branded Umbrellas',
        description: 'Custom branded umbrellas for corporate gifts',
        price: 950,
        designFee: 2500,
        category: 'Gifts & Merchandise',
        features: ['Weather Resistant', 'Custom Branding', 'Automatic Open', 'Compact Design'],
        icon: '☂️',
        popular: false,
        maxQuantity: 100,
        minQuantity: 10,
        pricingTiers: {
          '10-24': 1000,
          '25+': 950
        },
        pricingType: 'tiered',
        unitId: unitsMap.piece?.id
      },
      {
        service: 'Branded Caps',
        description: 'Custom embroidered caps for promotional use',
        price: 380,
        designFee: 1800,
        category: 'Apparel',
        features: ['Embroidered Logo', 'Adjustable Strap', 'Quality Fabric', 'Various Colors'],
        icon: '🧢',
        popular: true,
        maxQuantity: 200,
        minQuantity: 20,
        pricingTiers: {
          '20-49': 420,
          '50-99': 380,
          '100+': 340
        },
        pricingType: 'tiered',
        unitId: unitsMap.piece?.id
      },
      {
        service: 'Mouse Pads',
        description: 'Custom branded mouse pads for office use',
        price: 180,
        designFee: 1500,
        category: 'Gifts & Merchandise',
        features: ['Non-slip Base', 'Custom Design', 'Smooth Surface', 'Durable Material'],
        icon: '🖱️',
        popular: true,
        maxQuantity: 500,
        minQuantity: 25,
        pricingTiers: {
          '25-99': 200,
          '100-199': 180,
          '200+': 160
        },
        pricingType: 'tiered',
        unitId: unitsMap.piece?.id
      },

      // Safety Gear
      {
        service: 'Safety Helmets',
        description: 'Custom branded safety helmets for construction',
        price: 850,
        designFee: 2000,
        category: 'Safety Gear',
        features: ['Safety Certified', 'Custom Branding', 'Adjustable Fit', 'Durable Material'],
        icon: '⛑️',
        popular: false,
        maxQuantity: 100,
        minQuantity: 10,
        pricingTiers: {
          '10-24': 900,
          '25+': 850
        },
        pricingType: 'tiered',
        unitId: unitsMap.piece?.id
      },
      {
        service: 'Reflective Jackets',
        description: 'High-visibility reflective jackets with custom branding',
        price: 1200,
        designFee: 2500,
        category: 'Safety Gear',
        features: ['High Visibility', 'Reflective Strips', 'Custom Logo', 'Safety Certified'],
        icon: '🦺',
        popular: false,
        maxQuantity: 100,
        minQuantity: 10,
        pricingTiers: {
          '10-24': 1300,
          '25+': 1200
        },
        pricingType: 'tiered',
        unitId: unitsMap.piece?.id
      },

      // Vehicle Accessories
      {
        service: 'Car Headrests',
        description: 'Custom branded car headrest covers',
        price: 450,
        designFee: 2000,
        category: 'Vehicle Accessories',
        features: ['Universal Fit', 'Easy Installation', 'Custom Branding', 'Durable Fabric'],
        icon: '🚗',
        popular: false,
        maxQuantity: 100,
        minQuantity: 10,
        pricingTiers: {
          '10-24': 500,
          '25+': 450
        },
        pricingType: 'tiered',
        unitId: unitsMap.piece?.id
      },

      // Signage
      {
        service: '3D Signages',
        description: 'Professional 3D signage for businesses',
        price: 8500,
        designFee: 5000,
        category: 'Signage',
        features: ['3D Effect', 'LED Options', 'Weather Resistant', 'Professional Installation'],
        icon: '🏢',
        popular: false,
        maxQuantity: 10,
        minQuantity: 1,
        pricingTiers: {
          '1-2': 9000,
          '3+': 8500
        },
        pricingType: 'tiered',
        unitId: unitsMap.piece?.id
      },
      {
        service: 'Directional Signs',
        description: 'Professional directional signage for buildings',
        price: 1200,
        designFee: 2500,
        category: 'Signage',
        features: ['Clear Directions', 'Durable Material', 'Professional Mounting', 'Custom Design'],
        icon: '➡️',
        popular: false,
        maxQuantity: 50,
        minQuantity: 5,
        pricingTiers: {
          '5-19': 1300,
          '20+': 1200
        },
        pricingType: 'tiered',
        unitId: unitsMap.piece?.id
      },

      // Additional Banner Types
      {
        service: 'Pop-up Banners',
        description: 'Portable pop-up banners for quick setup displays',
        price: 4800,
        designFee: 3000,
        category: 'Banners',
        features: ['Quick Setup', 'Portable Design', 'High Quality Print', 'Curved Display'],
        icon: '📊',
        popular: false,
        maxQuantity: 20,
        minQuantity: 1,
        pricingTiers: {
          '1-4': 5000,
          '5+': 4800
        },
        pricingType: 'tiered',
        unitId: unitsMap.piece?.id
      },
      {
        service: 'Telescopic Banners',
        description: 'Adjustable telescopic banners for various heights',
        price: 16500,
        designFee: 4000,
        category: 'Banners',
        features: ['Adjustable Height', 'Stable Base', 'Professional Look', 'Easy Assembly'],
        icon: '📏',
        popular: false,
        maxQuantity: 10,
        minQuantity: 1,
        pricingTiers: {
          '1-2': 17000,
          '3+': 16500
        },
        pricingType: 'tiered',
        unitId: unitsMap.piece?.id
      },

      // Document Services
      {
        service: 'Architectural Blueprints',
        description: 'Professional printing of architectural drawings and blueprints',
        price: 120,
        designFee: 0,
        category: 'Document Printing',
        features: ['Large Format', 'Technical Accuracy', 'Professional Quality', 'Various Sizes'],
        icon: '📐',
        popular: false,
        maxQuantity: 100,
        minQuantity: 1,
        pricingTiers: {
          '1-9': 150,
          '10-24': 120,
          '25+': 100
        },
        pricingType: 'tiered',
        unitId: unitsMap.piece?.id
      },
      {
        service: 'Document Printing',
        description: 'High-quality document printing services',
        price: 8,
        designFee: 0,
        category: 'Document Printing',
        features: ['Black & White', 'Color Options', 'Various Paper Types', 'Binding Options'],
        icon: '📄',
        popular: true,
        maxQuantity: 10000,
        minQuantity: 1,
        pricingTiers: {
          '1-99': 10,
          '100-499': 8,
          '500+': 6
        },
        pricingType: 'tiered',
        unitId: unitsMap.piece?.id
      }
    ];

    // Create missing catalogue items
    let createdCount = 0;
    for (const item of missingServices) {
      try {
        const existingItem = await prisma.catalogue.findFirst({
          where: { service: item.service }
        });
        
        if (!existingItem) {
          await prisma.catalogue.create({ data: item });
          createdCount++;
          console.log(`✅ Created: ${item.service} - KSh ${item.price}`);
        } else {
          console.log(`⚠️ Already exists: ${item.service}`);
        }
      } catch (error) {
        console.log(`❌ Error creating ${item.service}:`, error.message);
      }
    }

    console.log('\n🎉 Missing PrintShop services seeding completed!');
    console.log(`📊 Summary:`);
    console.log(`   - New Services Added: ${createdCount}`);
    console.log(`   - Total Missing Services: ${missingServices.length}`);
    console.log(`   - Categories: Packaging, Safety Gear, Vehicle Accessories, Signage, Document Printing & More`);

  } catch (error) {
    console.error('❌ Error during missing services seeding:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  main()
    .catch((error) => {
      console.error('❌ Missing services seeding failed:', error);
      process.exit(1);
    });
}

module.exports = { main }; 