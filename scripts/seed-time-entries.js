const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function seedTimeEntries() {
  try {
    console.log('Creating sample time entries...');
    
    // Get admin user
    const admin = await prisma.user.findFirst({
      where: { email: '<EMAIL>' }
    });
    
    if (!admin) {
      console.log('Admin user not found');
      return;
    }
    
    console.log('Admin user found:', admin.id);
    
    // Create some projects first
    let project1 = await prisma.project.findFirst({
      where: { name: 'Website Redesign' }
    });
    
    if (!project1) {
      project1 = await prisma.project.create({
        data: {
          name: 'Website Redesign',
          description: 'Complete redesign of company website',
          status: 'in_progress',
          priority: 'high',
          startDate: new Date('2024-01-01'),
          createdBy: admin.id
        }
      });
    }
    
    let project2 = await prisma.project.findFirst({
      where: { name: 'Mobile App Development' }
    });
    
    if (!project2) {
      project2 = await prisma.project.create({
        data: {
          name: 'Mobile App Development',
          description: 'Native mobile app for iOS and Android',
          status: 'in_progress',
          priority: 'medium',
          startDate: new Date('2024-01-15'),
          createdBy: admin.id
        }
      });
    }
    
    console.log('Projects created');
    
    // Create time entries
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    
    const timeEntries = [
      {
        description: 'Homepage design mockups',
        projectId: project1.id,
        userId: admin.id,
        startTime: new Date(today.getTime() + 9 * 60 * 60 * 1000), // 9 AM today
        endTime: new Date(today.getTime() + 12 * 60 * 60 * 1000), // 12 PM today
        duration: 180, // 3 hours
        billable: true,
        hourlyRate: 75
      },
      {
        description: 'API development',
        projectId: project2.id,
        userId: admin.id,
        startTime: new Date(today.getTime() - 24 * 60 * 60 * 1000 + 10 * 60 * 60 * 1000), // 10 AM yesterday
        endTime: new Date(today.getTime() - 24 * 60 * 60 * 1000 + 14 * 60 * 60 * 1000), // 2 PM yesterday
        duration: 240, // 4 hours
        billable: true,
        hourlyRate: 80
      },
      {
        description: 'Code review and testing',
        projectId: project1.id,
        userId: admin.id,
        startTime: new Date(today.getTime() + 14 * 60 * 60 * 1000), // 2 PM today
        endTime: null, // Currently running
        duration: null,
        billable: true,
        hourlyRate: 75
      }
    ];
    
    for (const entry of timeEntries) {
      const created = await prisma.timeEntry.create({ data: entry });
      console.log('Created time entry:', created.id);
    }
    
    console.log('Sample time entries created successfully!');
    
  } catch (error) {
    console.error('Error creating time entries:', error);
  } finally {
    await prisma.$disconnect();
  }
}

seedTimeEntries(); 