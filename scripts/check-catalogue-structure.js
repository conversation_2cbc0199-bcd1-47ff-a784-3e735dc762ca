#!/usr/bin/env node

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkCatalogue() {
  try {
    const catalogueItems = await prisma.catalogue.findMany();
    console.log('📊 CATALOGUE DATABASE STRUCTURE & DATA');
    console.log('=====================================');
    console.log(`Total items: ${catalogueItems.length}`);
    
    if (catalogueItems.length > 0) {
      console.log('\n📋 SAMPLE ITEM STRUCTURE:');
      console.log('==========================');
      const sampleItem = catalogueItems[0];
      console.log(`ID: ${sampleItem.id} (${typeof sampleItem.id})`);
      console.log(`Service: ${sampleItem.service} (${typeof sampleItem.service})`);
      console.log(`Price: ${sampleItem.price} (${typeof sampleItem.price})`);
      console.log(`Description: ${sampleItem.description} (${typeof sampleItem.description})`);
      console.log(`Features: ${JSON.stringify(sampleItem.features)} (${typeof sampleItem.features})`);
      console.log(`Icon: ${sampleItem.icon} (${typeof sampleItem.icon})`);
      console.log(`Popular: ${sampleItem.popular} (${typeof sampleItem.popular})`);
      console.log(`Category: ${sampleItem.category} (${typeof sampleItem.category})`);
      console.log(`ImageUrl: ${sampleItem.imageUrl} (${typeof sampleItem.imageUrl})`);
      console.log(`ImageUrl2: ${sampleItem.imageUrl2} (${typeof sampleItem.imageUrl2})`);
      console.log(`ImageUrl3: ${sampleItem.imageUrl3} (${typeof sampleItem.imageUrl3})`);
      console.log(`CreatedAt: ${sampleItem.createdAt} (${typeof sampleItem.createdAt})`);
      console.log(`UpdatedAt: ${sampleItem.updatedAt} (${typeof sampleItem.updatedAt})`);
      
      console.log('\n📝 ALL CATALOGUE ITEMS:');
      console.log('=======================');
      catalogueItems.forEach(item => {
        console.log(`[${item.id}] ${item.service} - KES ${item.price.toLocaleString()} (${item.category}) ${item.popular ? '⭐' : ''}`);
      });

      console.log('\n📊 CATEGORIES SUMMARY:');
      console.log('======================');
      const categories = [...new Set(catalogueItems.map(item => item.category))];
      categories.forEach(category => {
        const count = catalogueItems.filter(item => item.category === category).length;
        console.log(`${category}: ${count} items`);
      });
    }
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkCatalogue(); 