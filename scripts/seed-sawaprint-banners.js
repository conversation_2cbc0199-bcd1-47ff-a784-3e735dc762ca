#!/usr/bin/env node

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

// Actual banner products from Sawaprint.com with prices in KES - Rebranded for Mocky Digital
const mockyDigitalBanners = [
  {
    service: 'Broad-base Roll-up Banner',
    price: 7500,
    description: 'Professional broad-base roll-up banners designed by Mocky Digital for events and marketing campaigns. Portable and easy to set up for maximum impact.',
    category: 'Banners',
    features: [
      'Broad base for stability',
      'Easy setup and breakdown',
      'Professional appearance',
      'Portable design',
      'High-quality printing by Mocky Digital'
    ],
    icon: 'fas fa-flag',
    popular: true,
    imageUrl: null
  },
  {
    service: 'Telescopic Banners',
    price: 19000,
    description: 'Adjustable telescopic banners crafted by Mocky Digital, perfect for various display heights and versatile marketing needs across different venues.',
    category: 'Banners',
    features: [
      'Adjustable height mechanism',
      'Telescopic design engineering',
      'Versatile display options',
      'Durable construction materials',
      'Professional graphics design'
    ],
    icon: 'fas fa-arrows-alt-v',
    popular: true,
    imageUrl: null
  },
  {
    service: 'Adjustable Backdrop Media Banners (3M × 2M)',
    price: 18500,
    description: 'Large format adjustable backdrop banners measuring 3M × 2M, expertly designed by Mocky Digital for events, exhibitions and professional presentations.',
    category: 'Banners',
    features: [
      '3M × 2M premium size',
      'Adjustable backdrop system',
      'Event-ready design',
      'Large format printing quality',
      'Professional presentation setup'
    ],
    icon: 'fas fa-expand-arrows-alt',
    popular: true,
    imageUrl: null
  },
  {
    service: 'Custom Door Frame Banners',
    price: 7300,
    description: 'Custom-designed door frame banners created by Mocky Digital for entrance marketing and eye-catching promotional displays at your business location.',
    category: 'Banners',
    features: [
      'Custom design service',
      'Perfect door frame fitting',
      'Eye-catching entrance display',
      'Weather resistant materials',
      'Professional installation guide'
    ],
    icon: 'fas fa-door-open',
    popular: false,
    imageUrl: null
  },
  {
    service: 'Broad Base Media Banner (2M × 2M)',
    price: 15600,
    description: 'Square format broad base media banners measuring 2M × 2M, designed by Mocky Digital for impactful displays and brand visibility.',
    category: 'Banners',
    features: [
      '2M × 2M square format',
      'Broad base stability system',
      'Media-ready graphics',
      'Professional setup process',
      'High visibility design'
    ],
    icon: 'fas fa-square',
    popular: false,
    imageUrl: null
  },
  {
    service: 'Collapsible Backdrop Stand & Media Banners',
    price: 17800,
    description: 'Complete backdrop solution with collapsible stand and media banners, professionally designed by Mocky Digital for seamless presentations and events.',
    category: 'Banners',
    features: [
      'Collapsible design system',
      'Complete backdrop solution',
      'Professional presentation quality',
      'Easy transport and storage',
      'Quick setup process'
    ],
    icon: 'fas fa-compress-arrows-alt',
    popular: false,
    imageUrl: null
  },
  {
    service: 'Narrow Base Roll-Up Banners',
    price: 5800,
    description: 'Compact narrow base roll-up banners designed by Mocky Digital, ideal for smaller spaces and indoor displays where space efficiency is key.',
    category: 'Banners',
    features: [
      'Space-saving design',
      'Narrow base footprint',
      'Indoor display optimization',
      'Compact storage solution',
      'Quick deployment system'
    ],
    icon: 'fas fa-compress',
    popular: false,
    imageUrl: null
  },
  {
    service: 'X-Banner Stands',
    price: 5400,
    description: 'Affordable X-banner stands designed by Mocky Digital for budget-friendly promotional displays and effective marketing campaigns.',
    category: 'Banners',
    features: [
      'Budget-friendly pricing',
      'X-frame design structure',
      'Lightweight construction',
      'Easy assembly process',
      'Portable marketing solution'
    ],
    icon: 'fas fa-times',
    popular: false,
    imageUrl: null
  },
  {
    service: 'Teardrop Banner',
    price: 12500,
    description: 'Eye-catching teardrop-shaped banners expertly crafted by Mocky Digital, perfect for outdoor events and high-impact promotional activities.',
    category: 'Banners',
    features: [
      'Distinctive teardrop shape',
      'Wind-resistant design',
      'Outdoor event suitable',
      'Eye-catching visual appeal',
      'Event marketing optimization'
    ],
    icon: 'fas fa-tint',
    popular: false,
    imageUrl: null
  }
];

async function seedMockyDigitalBanners() {
  console.log('🌱 Seeding catalogue with Mocky Digital banner products...');
  
  try {
    // Clear existing banner items (optional)
    console.log('🗑️  Clearing existing banner items...');
    await prisma.catalogue.deleteMany({
      where: { category: 'Banners' }
    });
    
    // Insert Mocky Digital banner products
    console.log(`📝 Inserting ${mockyDigitalBanners.length} banner products...`);
    
    for (const banner of mockyDigitalBanners) {
      await prisma.catalogue.create({
        data: {
          service: banner.service,
          price: banner.price,
          description: banner.description,
          features: banner.features,
          icon: banner.icon,
          popular: banner.popular,
          imageUrl: banner.imageUrl,
          category: banner.category
        }
      });
      
      console.log(`✅ Added: ${banner.service} - KES ${banner.price.toLocaleString()}`);
    }
    
    console.log('🎉 Mocky Digital banner catalogue seeding completed!');
    console.log(`💰 Total products: ${mockyDigitalBanners.length}`);
    console.log(`📊 Price range: KES ${Math.min(...mockyDigitalBanners.map(b => b.price)).toLocaleString()} - KES ${Math.max(...mockyDigitalBanners.map(b => b.price)).toLocaleString()}`);
    
  } catch (error) {
    console.error('❌ Seeding failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seeding
seedMockyDigitalBanners()
  .catch(console.error)
  .finally(() => process.exit(0)); 