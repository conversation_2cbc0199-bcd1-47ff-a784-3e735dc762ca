const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function main() {
  console.log('🚀 Starting basic client seeding...');

  try {
    // Get the admin user
    const adminUser = await prisma.user.findFirst({
      where: { email: '<EMAIL>' }
    });

    if (!adminUser) {
      console.log('❌ Admin user not found. Please run the user creation script first.');
      return;
    }

    // Create basic clients
    const clientsData = [
      {
        firstName: 'David',
        lastName: '<PERSON><PERSON><PERSON>',
        email: '<EMAIL>',
        phone: '+254712345678',
        company: 'TechStart Kenya Ltd',
        position: 'CEO',
        industry: 'Technology',
        companySize: 'MEDIUM',
        status: 'active',
        priority: 'high',
        address: 'Westlands, Nairobi',
        city: 'Nairobi',
        country: 'Kenya',
        website: 'https://techstartkenya.com',
        tags: ['technology', 'startup', 'nairobi'],
        createdBy: adminUser.id,
        updatedBy: adminUser.id
      },
      {
        firstName: 'Mary',
        lastName: 'Njeri',
        email: '<EMAIL>',
        phone: '+254723456789',
        company: 'Green Solutions Enterprises',
        position: 'Founder',
        industry: 'Environmental',
        companySize: 'SMALL',
        status: 'active',
        priority: 'medium',
        address: 'Kilifi Road, Mombasa',
        city: 'Mombasa',
        country: 'Kenya',
        website: 'https://greensolutions.co.ke',
        tags: ['environmental', 'consulting'],
        createdBy: adminUser.id,
        updatedBy: adminUser.id
      },
      {
        firstName: 'Peter',
        lastName: 'Mwangi',
        email: '<EMAIL>',
        phone: '+254734567890',
        company: 'Local Eats Restaurant',
        position: 'Restaurant Manager',
        industry: 'Food & Beverage',
        companySize: 'SMALL',
        status: 'active',
        priority: 'medium',
        address: 'Tom Mboya Street, Kisumu',
        city: 'Kisumu',
        country: 'Kenya',
        tags: ['restaurant', 'food', 'delivery'],
        createdBy: adminUser.id,
        updatedBy: adminUser.id
      }
    ];

    console.log('🧹 Clearing existing clients...');
    await prisma.client.deleteMany();

    console.log('👥 Creating clients...');
    const createdClients = [];
    for (const clientData of clientsData) {
      const client = await prisma.client.create({
        data: clientData
      });
      createdClients.push(client);
      console.log(`✅ Created client: ${client.firstName} ${client.lastName}`);
    }

    console.log('\n🎉 Basic client seeding completed successfully!');
    console.log(`📊 Created ${createdClients.length} clients`);

  } catch (error) {
    console.error('❌ Error seeding clients:', error);
    throw error;
  }
}

main()
  .catch((e) => {
    console.error('❌ Seeding failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  }); 