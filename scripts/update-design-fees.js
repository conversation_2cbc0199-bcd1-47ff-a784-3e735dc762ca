// Script to update existing catalogue items with design fees
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

// Design fee mapping based on product complexity
const getDesignFee = (serviceName) => {
  const service = serviceName.toLowerCase();
  
  if (service.includes('business card') || service.includes('id card')) {
    return 1500;
  } else if (service.includes('letterhead')) {
    return 1200;
  } else if (service.includes('invoice')) {
    return 1200;
  } else if (service.includes('flyer')) {
    return 2000;
  } else if (service.includes('poster')) {
    return 1600;
  } else if (service.includes('brochure')) {
    return 2500;
  } else if (service.includes('banner')) {
    return 1800;
  } else if (service.includes('catalogue') || service.includes('catalog')) {
    return 3000;
  } else if (service.includes('company profile')) {
    return 4000;
  } else if (service.includes('menu')) {
    return 2200;
  } else if (service.includes('logo')) {
    return 3000;
  } else if (service.includes('label') || service.includes('sticker')) {
    return 1500;
  } else if (service.includes('social media')) {
    return 1000;
  } else if (service.includes('packaging')) {
    return 4000;
  } else if (service.includes('cap') || service.includes('caps')) {
    return 1500; // For caps
  } else {
    // Default design fee for other products
    return 2000;
  }
};

async function main() {
  try {
    console.log('Starting design fee update for existing catalogue items...');
    
    // Get all catalogue items
    const catalogueItems = await prisma.catalogue.findMany();
    console.log(`Found ${catalogueItems.length} catalogue items to update`);
    
    let updatedCount = 0;
    
    for (const item of catalogueItems) {
      // Skip items that already have a design fee set
      if (item.designFee && item.designFee > 0) {
        console.log(`Skipping "${item.service}" - already has design fee: KSh ${item.designFee}`);
        continue;
      }
      
      // Calculate design fee for this item
      const designFee = getDesignFee(item.service);
      
      // Update the item
      await prisma.catalogue.update({
        where: { id: item.id },
        data: { designFee: designFee }
      });
      
      console.log(`Updated "${item.service}": Print Price = KSh ${item.price}, Design Fee = KSh ${designFee}`);
      updatedCount++;
    }
    
    console.log(`\n✅ Successfully updated ${updatedCount} catalogue items with design fees`);
    
    // Display summary of all items
    console.log('\n📊 UPDATED CATALOGUE SUMMARY:');
    console.log('=' .repeat(80));
    
    const updatedItems = await prisma.catalogue.findMany({
      orderBy: { service: 'asc' }
    });
    
    updatedItems.forEach(item => {
      console.log(`${item.service.padEnd(30)} | Print: KSh ${item.price.toString().padStart(6)} | Design: KSh ${item.designFee.toString().padStart(6)}`);
    });
    
    console.log('=' .repeat(80));
    console.log(`Total items: ${updatedItems.length}`);
    
  } catch (error) {
    console.error('❌ Error updating design fees:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

main(); 