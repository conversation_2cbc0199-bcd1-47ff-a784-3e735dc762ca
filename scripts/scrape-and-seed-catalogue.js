#!/usr/bin/env node

const puppeteer = require('puppeteer');
const fs = require('fs').promises;
const path = require('path');

// Configuration - Update these with your target website details
const CONFIG = {
  // Target website URL - UPDATE THIS
  baseUrl: 'https://www.sawaprint.com',
  
  // Product page selectors - UPDATE THESE BASED ON TARGET WEBSITE
  selectors: {
    // Main product containers - Updated for Sawaprint structure
    productCards: '.product, .product-item, .item, div[class*="product"], div[style*="display"]',
    
    // Individual product information - More specific for Sawaprint
    title: 'h2, h3, .product-title, .title, strong, b',
    price: '.price, .cost, .amount, [data-price]',
    description: '.description, .desc, .summary, .details, p',
    image: 'img',
    category: '.category, .cat, .type, [data-category]',
    features: 'ul li, .feature, .specs li, .benefits li',
    
    // Pagination or "load more" elements
    nextButton: '.next, .load-more, [data-next]',
    pagination: '.pagination a, .page-link'
  },
  
  // Pages to scrape (add specific category pages)
  pagesToScrape: [
    '/catalogue',
    '/banners', 
    '/drinkware',
    '/diaries-notebooks',
    '/gift-sets',
    '/office-essentials',
    '/awards'
  ],
  
  // Output settings
  outputFile: './scraped-products.json',
  seedScript: './scripts/seed-scraped-data.js'
};

class ProductScraper {
  constructor() {
    this.browser = null;
    this.page = null;
    this.scrapedProducts = [];
  }

  async init() {
    console.log('🚀 Initializing browser...');
    this.browser = await puppeteer.launch({ 
      headless: false, // Set to true for production
      defaultViewport: { width: 1920, height: 1080 }
    });
    this.page = await this.browser.newPage();
    
    // Set user agent to avoid detection
    await this.page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
    
    console.log('✅ Browser initialized');
  }

  async scrapePage(url) {
    try {
      console.log(`📄 Scraping: ${url}`);
      await this.page.goto(url, { waitUntil: 'networkidle2', timeout: 30000 });
      
      // Wait for products to load
      await this.page.waitForSelector(CONFIG.selectors.productCards, { timeout: 10000 }).catch(() => {
        console.log(`⚠️  No product cards found on ${url}`);
      });

      const products = await this.page.evaluate((selectors) => {
        const productElements = document.querySelectorAll(selectors.productCards);
        const extractedProducts = [];

        productElements.forEach((element, index) => {
          try {
            // Helper function to safely extract text
            const getText = (selector, fallback = '') => {
              const el = element.querySelector(selector);
              return el ? el.textContent.trim() : fallback;
            };

            // Helper function to extract price
            const getPrice = (selector) => {
              const el = element.querySelector(selector);
              if (!el) return 0;
              
              const priceText = el.textContent.trim();
              // Extract numbers from price text (handles various currency formats)
              const priceMatch = priceText.match(/[\d,]+(?:\.\d{2})?/);
              return priceMatch ? parseFloat(priceMatch[0].replace(/,/g, '')) : 0;
            };

            // Helper function to extract image URL
            const getImageUrl = (selector) => {
              const img = element.querySelector(selector);
              if (!img) return null;
              
              return img.src || img.getAttribute('data-src') || img.getAttribute('data-lazy') || null;
            };

            // Helper function to extract features/specs
            const getFeatures = (selector) => {
              const featureElements = element.querySelectorAll(selector);
              return Array.from(featureElements).map(el => el.textContent.trim()).filter(text => text.length > 0);
            };

            // Extract product data with all required catalogue fields
            const product = {
              // Required fields
              service: getText(selectors.title) || `Product ${index + 1}`, // Fallback if no title
              price: getPrice(selectors.price) || 1000, // Default price if none found
              
              // Optional fields that map to database
              description: getText(selectors.description) || '',
              category: getText(selectors.category) || 'General Services',
              features: getFeatures(selectors.features).length > 0 ? getFeatures(selectors.features) : [
                'High quality service',
                'Professional delivery',
                'Customer support',
                'Satisfaction guaranteed'
              ],
              icon: 'fas fa-star', // Default icon, can be customized per category
              popular: element.classList.contains('featured') || element.classList.contains('popular') || false,
              
              // Image fields (database supports 3 image URLs)
              imageUrl: getImageUrl(selectors.image) || null,
              imageUrl2: null, // Can be enhanced to extract multiple images
              imageUrl3: null, // Can be enhanced to extract multiple images
              
              // Metadata for tracking (not stored in database)
              scrapedFrom: window.location.href,
              scrapedAt: new Date().toISOString(),
              originalId: `scraped_${Date.now()}_${index}`
            };

            // Only add if we have at least a title
            if (product.service && product.service.length > 0) {
              extractedProducts.push(product);
            }
          } catch (error) {
            console.error('Error extracting product:', error);
          }
        });

        return extractedProducts;
      }, CONFIG.selectors);

      console.log(`✅ Found ${products.length} products on ${url}`);
      return products;

    } catch (error) {
      console.error(`❌ Error scraping ${url}:`, error.message);
      return [];
    }
  }

  async scrapeAllPages() {
    const allProducts = [];
    
    for (const pagePath of CONFIG.pagesToScrape) {
      const fullUrl = CONFIG.baseUrl + pagePath;
      const products = await this.scrapePage(fullUrl);
      allProducts.push(...products);
      
      // Add delay to be respectful to the server
      await new Promise(resolve => setTimeout(resolve, 2000));
    }

    // Remove duplicates based on title
    const uniqueProducts = allProducts.filter((product, index, self) => 
      index === self.findIndex(p => p.service === product.service)
    );

    console.log(`🎉 Total unique products scraped: ${uniqueProducts.length}`);
    return uniqueProducts;
  }

  async saveToFile(products) {
    try {
      const jsonData = JSON.stringify(products, null, 2);
      await fs.writeFile(CONFIG.outputFile, jsonData);
      console.log(`💾 Saved ${products.length} products to ${CONFIG.outputFile}`);
    } catch (error) {
      console.error('❌ Error saving file:', error);
    }
  }

  async close() {
    if (this.browser) {
      await this.browser.close();
      console.log('🔒 Browser closed');
    }
  }

  // Enhanced scraping for specific product pages
  async scrapeProductDetails(productUrl) {
    try {
      await this.page.goto(productUrl, { waitUntil: 'networkidle2' });
      
      const productDetails = await this.page.evaluate(() => {
        // More detailed extraction for individual product pages
        const getDetailedText = (selectors) => {
          for (const selector of selectors) {
            const element = document.querySelector(selector);
            if (element) return element.textContent.trim();
          }
          return '';
        };

        return {
          title: getDetailedText(['h1', '.product-title', '.title']),
          price: getDetailedText(['.price', '.cost', '.amount']),
          description: getDetailedText(['.description', '.details', '.summary']),
          specifications: Array.from(document.querySelectorAll('.specs li, .specifications li, .features li'))
            .map(el => el.textContent.trim()),
          images: Array.from(document.querySelectorAll('.product-images img, .gallery img'))
            .map(img => img.src),
          category: getDetailedText(['.breadcrumb a:last-child', '.category', '.product-category'])
        };
      });

      return productDetails;
    } catch (error) {
      console.error(`Error scraping product details from ${productUrl}:`, error);
      return null;
    }
  }
}

// Helper function to get appropriate icon based on category
function getCategoryIcon(category) {
  if (!category) return 'fas fa-star';
  
  const categoryLower = category.toLowerCase();
  
  // Category to icon mapping
  const iconMap = {
    'banners': 'fas fa-flag',
    'banner': 'fas fa-flag',
    'drinkware': 'fas fa-coffee',
    'drink': 'fas fa-coffee',
    'cups': 'fas fa-coffee',
    'mugs': 'fas fa-coffee',
    'diaries': 'fas fa-book',
    'notebooks': 'fas fa-book',
    'books': 'fas fa-book',
    'stationery': 'fas fa-pen',
    'gift': 'fas fa-gift',
    'gifts': 'fas fa-gift',
    'office': 'fas fa-building',
    'awards': 'fas fa-trophy',
    'trophy': 'fas fa-trophy',
    'trophies': 'fas fa-trophy',
    'printing': 'fas fa-print',
    'print': 'fas fa-print',
    'design': 'fas fa-paint-brush',
    'graphic': 'fas fa-paint-brush',
    'logo': 'fas fa-paint-brush',
    'web': 'fas fa-laptop-code',
    'website': 'fas fa-laptop-code',
    'digital': 'fas fa-digital-tachograph',
    'marketing': 'fas fa-bullhorn',
    'social': 'fas fa-share-alt',
    'business': 'fas fa-briefcase',
    'corporate': 'fas fa-building',
    'branding': 'fas fa-palette',
    'cards': 'fas fa-id-card',
    'flyers': 'fas fa-file-image',
    'brochures': 'fas fa-newspaper',
    'letterheads': 'fas fa-file-text'
  };
  
  // Find matching icon
  for (const [keyword, icon] of Object.entries(iconMap)) {
    if (categoryLower.includes(keyword)) {
      return icon;
    }
  }
  
  return 'fas fa-star'; // Default fallback
}

// Manual product mapping for common categories (as fallback)
const MANUAL_PRODUCT_TEMPLATES = {
  'banners': [
    { service: 'PVC Banner', price: 1500, category: 'Banners', description: 'Durable outdoor PVC banner' },
    { service: 'Canvas Banner', price: 2000, category: 'Banners', description: 'High-quality canvas banner' },
    { service: 'Vinyl Banner', price: 1200, category: 'Banners', description: 'Weather-resistant vinyl banner' }
  ],
  'business-cards': [
    { service: 'Standard Business Cards', price: 800, category: 'Printing Services', description: '100 premium business cards' },
    { service: 'Premium Business Cards', price: 1200, category: 'Printing Services', description: '100 premium matte finish cards' }
  ],
  'digital-services': [
    { service: 'Logo Design', price: 5000, category: 'Digital Services', description: 'Professional logo design' },
    { service: 'Website Design', price: 25000, category: 'Digital Services', description: 'Responsive website design' },
    { service: 'Social Media Graphics', price: 3000, category: 'Digital Services', description: 'Social media post designs' }
  ]
};

// Main execution function
async function main() {
  const scraper = new ProductScraper();
  
  try {
    await scraper.init();
    
    console.log('🔍 Starting product scraping...');
    console.log(`Target website: ${CONFIG.baseUrl}`);
    console.log(`Pages to scrape: ${CONFIG.pagesToScrape.length}`);
    
    const scrapedProducts = await scraper.scrapeAllPages();
    
    // If we didn't get many products, add manual templates
    if (scrapedProducts.length < 10) {
      console.log('⚠️  Limited products found, adding manual templates...');
      const manualProducts = Object.values(MANUAL_PRODUCT_TEMPLATES).flat();
      scrapedProducts.push(...manualProducts);
    }

    // Clean and enhance the scraped data to match database schema
    const cleanedProducts = scrapedProducts.map((product, index) => ({
      // Required database fields
      service: product.service || `Scraped Service ${index + 1}`,
      price: Math.round(product.price) || 1000, // Ensure integer price
      
      // Optional database fields with proper defaults
      description: product.description || `Professional ${product.service || 'service'} provided by Mocky Digital`,
      category: product.category || 'General Services',
      features: Array.isArray(product.features) && product.features.length > 0 
        ? product.features.slice(0, 10) // Max 10 features as per database validation
        : [
            'Professional service',
            'High quality delivery',
            'Customer satisfaction',
            'Timely completion',
            'Expert consultation'
          ],
      icon: getCategoryIcon(product.category) || 'fas fa-star',
      popular: Boolean(product.popular) || index < 3, // Mark first 3 as popular
      
      // Image URLs (database supports 3)
      imageUrl: product.imageUrl || null,
      imageUrl2: product.imageUrl2 || null,
      imageUrl3: product.imageUrl3 || null,
      
      // Metadata (for seeding script, not stored in database)
      scrapedFrom: product.scrapedFrom,
      scrapedAt: product.scrapedAt
    }));

    await scraper.saveToFile(cleanedProducts);
    
    // Generate seeding script
    await generateSeedScript(cleanedProducts);
    
  } catch (error) {
    console.error('❌ Scraping failed:', error);
  } finally {
    await scraper.close();
  }
}

// Generate the database seeding script
async function generateSeedScript(products) {
  const seedScript = `#!/usr/bin/env node

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

const scrapedProducts = ${JSON.stringify(products, null, 2)};

async function seedCatalogue() {
  console.log('🌱 Seeding catalogue with scraped products...');
  
  try {
    // Clear existing catalogue items (optional)
    console.log('🗑️  Clearing existing catalogue...');
    await prisma.catalogue.deleteMany({});
    
    // Insert scraped products
    console.log(\`📝 Inserting \$\{scrapedProducts.length\} products...\`);
    
    for (const product of scrapedProducts) {
      await prisma.catalogue.create({
        data: {
          // Required fields
          service: product.service,
          price: product.price,
          
          // Optional fields that match database schema
          description: product.description,
          features: product.features,
          icon: product.icon,
          popular: product.popular,
          category: product.category,
          
          // Image URLs (database supports up to 3)
          imageUrl: product.imageUrl,
          imageUrl2: product.imageUrl2,
          imageUrl3: product.imageUrl3
          
          // Note: createdAt and updatedAt are auto-generated by Prisma
        }
      });
      
      console.log(\`✅ Added: \$\{product.service\}\`);
    }
    
    console.log('🎉 Catalogue seeding completed!');
    
  } catch (error) {
    console.error('❌ Seeding failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seeding
seedCatalogue()
  .catch(console.error)
  .finally(() => process.exit(0));
`;

  await fs.writeFile(CONFIG.seedScript, seedScript);
  console.log(`📝 Generated seeding script: ${CONFIG.seedScript}`);
}

// Interactive configuration setup
async function setupConfig() {
  const readline = require('readline').createInterface({
    input: process.stdin,
    output: process.stdout
  });

  const question = (prompt) => new Promise(resolve => readline.question(prompt, resolve));

  console.log('🛠️  Configuration Setup');
  console.log('====================================');
  
  const website = await question('Enter the website URL to scrape (e.g., https://printshop.com): ');
  if (website.trim()) {
    CONFIG.baseUrl = website.trim();
  }

  const customPages = await question('Enter specific pages to scrape (comma-separated, press Enter for defaults): ');
  if (customPages.trim()) {
    CONFIG.pagesToScrape = customPages.split(',').map(page => page.trim());
  }

  readline.close();
  
  console.log('✅ Configuration updated!');
  console.log(`Target: ${CONFIG.baseUrl}`);
  console.log(`Pages: ${CONFIG.pagesToScrape.join(', ')}`);
}

// Command line arguments handling
const args = process.argv.slice(2);

if (args.includes('--setup') || args.includes('-s')) {
  setupConfig().then(() => main());
} else if (args.includes('--help') || args.includes('-h')) {
  console.log(`
🕷️  Product Scraper & Catalogue Seeder

Usage:
  node scripts/scrape-and-seed-catalogue.js [options]

Options:
  --setup, -s     Interactive configuration setup
  --help, -h      Show this help message

Examples:
  npm run scrape-catalogue
  node scripts/scrape-and-seed-catalogue.js --setup
  `);
} else {
  main();
}

module.exports = { ProductScraper, CONFIG };