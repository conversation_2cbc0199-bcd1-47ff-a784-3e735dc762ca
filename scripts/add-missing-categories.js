const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function addMissingCategories() {
  console.log('Adding missing categories...');
  
  const categoriesToAdd = [
    {
      name: 'E-commerce',
      slug: 'e-commerce',
      description: 'E-commerce and online store solutions'
    },
    {
      name: 'Mobile Development',
      slug: 'mobile-development',
      description: 'Mobile app development services'
    },
    {
      name: 'Other',
      slug: 'other',
      description: 'Other miscellaneous services'
    }
  ];

  for (const cat of categoriesToAdd) {
    try {
      const existing = await prisma.category.findUnique({
        where: { slug: cat.slug }
      });
      
      if (!existing) {
        await prisma.category.create({
          data: cat
        });
        console.log('✅ Added:', cat.name);
      } else {
        console.log('⏭️  Exists:', cat.name);
      }
    } catch (error) {
      console.error('❌ Error adding', cat.name, ':', error.message);
    }
  }

  // Show all categories
  console.log('\n📋 All categories in database:');
  const allCategories = await prisma.category.findMany({
    orderBy: { name: 'asc' }
  });
  
  allCategories.forEach(cat => {
    console.log(`  - ${cat.name} (${cat.slug})`);
  });

  await prisma.$disconnect();
}

addMissingCategories().catch(console.error); 