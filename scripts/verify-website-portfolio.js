#!/usr/bin/env node

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function verifyWebsitePortfolio() {
  console.log('🔍 Verifying Website Portfolio Database...\n');

  try {
    // Get total count
    const totalCount = await prisma.websitePortfolio.count();
    console.log(`📊 Total Portfolio Items: ${totalCount}`);

    // Get breakdown by category
    const categoryBreakdown = await prisma.websitePortfolio.groupBy({
      by: ['category'],
      _count: { category: true },
      orderBy: { category: 'asc' }
    });

    console.log('\n📂 Category Breakdown:');
    let totalByCategory = 0;
    categoryBreakdown.forEach(item => {
      console.log(`   ${item.category.padEnd(15)} : ${item._count.category} items`);
      totalByCategory += item._count.category;
    });

    // Get featured items
    const featuredCount = await prisma.websitePortfolio.count({
      where: { featured: true }
    });
    console.log(`\n⭐ Featured Items: ${featuredCount}`);

    // Get recent items
    const recentItems = await prisma.websitePortfolio.findMany({
      select: {
        title: true,
        category: true,
        featured: true,
        createdAt: true
      },
      orderBy: { createdAt: 'desc' },
      take: 5
    });

    console.log('\n🕐 Recently Created Items:');
    recentItems.forEach((item, index) => {
      const featuredIcon = item.featured ? '⭐' : '  ';
      const date = new Date(item.createdAt).toLocaleDateString();
      console.log(`   ${index + 1}. ${featuredIcon} ${item.title} (${item.category}) - ${date}`);
    });

    // Check for missing required fields
    const itemsWithMissingData = await prisma.websitePortfolio.findMany({
      where: {
        OR: [
          { title: { equals: '' } },
          { category: { equals: null } },
          { url: { equals: null } }
        ]
      },
      select: { id: true, title: true, category: true, url: true }
    });

    if (itemsWithMissingData.length > 0) {
      console.log(`\n⚠️  Items with missing data: ${itemsWithMissingData.length}`);
      itemsWithMissingData.forEach(item => {
        console.log(`   - ${item.title || 'NO TITLE'} (${item.id})`);
      });
    } else {
      console.log('\n✅ All items have required data fields');
    }

    // Sample API endpoint test
    console.log('\n🌐 API Endpoints Available:');
    console.log('   GET /api/website-portfolio           - All items');
    console.log('   GET /api/website-portfolio?featured  - Featured items only');
    console.log('   GET /api/website-portfolio?category=corporate - By category');

    console.log('\n📋 Management Commands:');
    console.log('   npm run db:seed:website-portfolio   - Re-run seeding');
    console.log('   npx prisma studio                   - Open database browser');
    console.log('   node scripts/verify-website-portfolio.js - Run this verification again');

    if (totalCount >= 20) {
      console.log('\n🎉 Website Portfolio Seeding: SUCCESS!');
      console.log(`   ${totalCount} items created across ${categoryBreakdown.length} categories`);
      console.log(`   ${featuredCount} featured items for homepage display`);
    } else {
      console.log('\n⚠️  Website Portfolio Seeding: INCOMPLETE');
      console.log(`   Expected 20+ items, found ${totalCount}`);
    }

  } catch (error) {
    console.error('\n❌ Verification failed:', error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('   1. Ensure database is running');
    console.log('   2. Check DATABASE_URL in .env file');
    console.log('   3. Run: npm run db:push');
    console.log('   4. Re-run: npm run db:seed:website-portfolio');
    
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run verification
verifyWebsitePortfolio(); 