const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function main() {
  console.log('🔍 Financial Suite Data Verification\n');

  try {
    // Get counts
    const serviceCounts = await prisma.service.groupBy({
      by: ['category'],
      _count: { category: true }
    });

    const quoteStats = await prisma.quote.groupBy({
      by: ['status'],
      _count: { status: true },
      _sum: { totalAmount: true }
    });

    const invoiceStats = await prisma.invoice.groupBy({
      by: ['status'],
      _count: { status: true },
      _sum: { totalAmount: true }
    });

    const transactionStats = await prisma.transaction.groupBy({
      by: ['status'],
      _count: { status: true },
      _sum: { amount: true }
    });

    // Display results
    console.log('📦 SERVICES BY CATEGORY:');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    serviceCounts.forEach(item => {
      console.log(`   ${item.category}: ${item._count.category} services`);
    });

    console.log('\n💰 QUOTES BY STATUS:');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    quoteStats.forEach(item => {
      console.log(`   ${item.status}: ${item._count.status} quotes (KES ${item._sum.totalAmount?.toFixed(2) || '0.00'})`);
    });

    console.log('\n📄 INVOICES BY STATUS:');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    invoiceStats.forEach(item => {
      console.log(`   ${item.status}: ${item._count.status} invoices (KES ${item._sum.totalAmount?.toFixed(2) || '0.00'})`);
    });

    console.log('\n💳 TRANSACTIONS BY STATUS:');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    transactionStats.forEach(item => {
      console.log(`   ${item.status}: ${item._count.status} transactions (KES ${item._sum.amount?.toFixed(2) || '0.00'})`);
    });

    // Get recent data examples
    console.log('\n📋 RECENT EXAMPLES:');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    
    const recentQuotes = await prisma.quote.findMany({
      take: 3,
      orderBy: { createdAt: 'desc' },
      include: { items: { include: { service: true } } }
    });

    console.log('\n💰 Latest Quotes:');
    recentQuotes.forEach(quote => {
      console.log(`   ${quote.quoteNumber} - ${quote.customerName} (${quote.status})`);
      console.log(`     KES ${quote.totalAmount.toFixed(2)} - ${quote.items.length} items`);
    });

    const recentInvoices = await prisma.invoice.findMany({
      take: 3,
      orderBy: { createdAt: 'desc' },
      include: { items: { include: { service: true } } }
    });

    console.log('\n📄 Latest Invoices:');
    recentInvoices.forEach(invoice => {
      console.log(`   ${invoice.invoiceNumber} - ${invoice.customerName} (${invoice.status})`);
      console.log(`     KES ${invoice.totalAmount.toFixed(2)} - Due: ${invoice.dueDate.toDateString()}`);
    });

    const recentReceipts = await prisma.receipt.findMany({
      take: 3,
      orderBy: { createdAt: 'desc' },
      include: { transaction: true }
    });

    console.log('\n🧾 Latest Receipts:');
    recentReceipts.forEach(receipt => {
      console.log(`   ${receipt.receiptNumber} - ${receipt.customerName} (${receipt.status})`);
      console.log(`     KES ${receipt.totalAmount.toFixed(2)} - ${receipt.transaction.transactionId}`);
    });

    console.log('\n✅ Financial Suite verification completed!\n');

  } catch (error) {
    console.error('❌ Error verifying financial data:', error);
  }
}

main()
  .catch((e) => {
    console.error('❌ Verification failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  }); 