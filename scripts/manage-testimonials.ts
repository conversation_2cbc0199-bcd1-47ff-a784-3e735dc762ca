import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

interface TestimonialStats {
  total: number;
  active: number;
  inactive: number;
  averageRating: number;
  companies: string[];
  locations: string[];
}

async function getTestimonialStats(): Promise<TestimonialStats> {
  const [total, active, inactive, avgRating, allTestimonials] = await Promise.all([
    prisma.testimonial.count(),
    prisma.testimonial.count({ where: { active: true } }),
    prisma.testimonial.count({ where: { active: false } }),
    prisma.testimonial.aggregate({ _avg: { rating: true } }),
    prisma.testimonial.findMany({ select: { company: true, location: true } })
  ]);

  const companies = [...new Set(allTestimonials.map(t => t.company).filter(Boolean))] as string[];
  const locations = [...new Set(allTestimonials.map(t => t.location))];

  return {
    total,
    active,
    inactive,
    averageRating: avgRating._avg.rating || 0,
    companies,
    locations
  };
}

async function listTestimonials(activeOnly: boolean = false) {
  console.log(`📋 ${activeOnly ? 'Active ' : ''}Testimonials:`);

  const where = activeOnly ? { active: true } : {};
  const testimonials = await prisma.testimonial.findMany({
    where,
    orderBy: [{ order: 'asc' }, { createdAt: 'desc' }]
  });

  if (testimonials.length === 0) {
    console.log('   No testimonials found.');
    return;
  }

  testimonials.forEach((testimonial, index) => {
    const status = testimonial.active ? '✅' : '❌';
    const stars = '⭐'.repeat(testimonial.rating);
    console.log(`   ${index + 1}. ${status} ${testimonial.name} (${testimonial.company})`);
    console.log(`      ${stars} ${testimonial.rating}/5 | ${testimonial.location}`);
    console.log(`      Project: ${testimonial.project}`);
    console.log(`      "${testimonial.testimonial.substring(0, 100)}${testimonial.testimonial.length > 100 ? '...' : ''}"`);
    console.log('');
  });
}

async function showStats() {
  console.log('📊 Testimonial Statistics:');

  const stats = await getTestimonialStats();

  console.log(`   Total testimonials: ${stats.total}`);
  console.log(`   Active testimonials: ${stats.active}`);
  console.log(`   Inactive testimonials: ${stats.inactive}`);
  console.log(`   Average rating: ${stats.averageRating.toFixed(1)}/5`);
  console.log(`   Unique companies: ${stats.companies.length}`);
  console.log(`   Unique locations: ${stats.locations.length}`);

  if (stats.companies.length > 0) {
    console.log(`   Companies: ${stats.companies.join(', ')}`);
  }

  if (stats.locations.length > 0) {
    console.log(`   Locations: ${stats.locations.join(', ')}`);
  }
}

async function toggleTestimonial(id: number, active?: boolean) {
  try {
    const testimonial = await prisma.testimonial.findUnique({ where: { id } });

    if (!testimonial) {
      console.log(`❌ Testimonial with ID ${id} not found.`);
      return;
    }

    const newStatus = active !== undefined ? active : !testimonial.active;

    await prisma.testimonial.update({
      where: { id },
      data: { active: newStatus }
    });

    console.log(`✅ Testimonial "${testimonial.name}" ${newStatus ? 'activated' : 'deactivated'}.`);
  } catch (error) {
    console.error('❌ Error toggling testimonial:', error);
  }
}

async function deleteTestimonial(id: number) {
  try {
    const testimonial = await prisma.testimonial.findUnique({ where: { id } });

    if (!testimonial) {
      console.log(`❌ Testimonial with ID ${id} not found.`);
      return;
    }

    await prisma.testimonial.delete({ where: { id } });
    console.log(`✅ Testimonial "${testimonial.name}" deleted successfully.`);
  } catch (error) {
    console.error('❌ Error deleting testimonial:', error);
  }
}

async function reorderTestimonials() {
  try {
    const testimonials = await prisma.testimonial.findMany({
      where: { active: true },
      orderBy: { createdAt: 'asc' }
    });

    console.log('🔄 Reordering testimonials...');

    for (let i = 0; i < testimonials.length; i++) {
      await prisma.testimonial.update({
        where: { id: testimonials[i].id },
        data: { order: i + 1 }
      });
    }

    console.log(`✅ Reordered ${testimonials.length} testimonials.`);
  } catch (error) {
    console.error('❌ Error reordering testimonials:', error);
  }
}

async function clearAllTestimonials() {
  try {
    const count = await prisma.testimonial.count();

    if (count === 0) {
      console.log('ℹ️  No testimonials to clear.');
      return;
    }

    await prisma.testimonial.deleteMany();
    console.log(`✅ Cleared all ${count} testimonials.`);
  } catch (error) {
    console.error('❌ Error clearing testimonials:', error);
  }
}

function showHelp() {
  console.log(`
🎯 Testimonial Management Script

Usage: npm run manage:testimonials [command] [options]

Commands:
  list [--active]           List all testimonials (or only active ones)
  stats                     Show testimonial statistics
  toggle <id> [true|false]  Toggle testimonial active status
  delete <id>               Delete a testimonial by ID
  reorder                   Reorder testimonials by creation date
  clear                     Clear all testimonials
  help                      Show this help message

Examples:
  npm run manage:testimonials list
  npm run manage:testimonials list -- --active
  npm run manage:testimonials stats
  npm run manage:testimonials toggle 1
  npm run manage:testimonials toggle 1 false
  npm run manage:testimonials delete 1
  npm run manage:testimonials reorder
  npm run manage:testimonials clear
  `);
}

async function main() {
  try {
    const args = process.argv.slice(2);
    const command = args[0];

    switch (command) {
      case 'list':
        const activeOnly = args.includes('--active');
        await listTestimonials(activeOnly);
        break;

      case 'stats':
        await showStats();
        break;

      case 'toggle':
        const toggleId = parseInt(args[1]);
        const activeStatus = args[2] === 'true' ? true : args[2] === 'false' ? false : undefined;

        if (isNaN(toggleId)) {
          console.log('❌ Please provide a valid testimonial ID.');
          break;
        }

        await toggleTestimonial(toggleId, activeStatus);
        break;

      case 'delete':
        const deleteId = parseInt(args[1]);

        if (isNaN(deleteId)) {
          console.log('❌ Please provide a valid testimonial ID.');
          break;
        }

        await deleteTestimonial(deleteId);
        break;

      case 'reorder':
        await reorderTestimonials();
        break;

      case 'clear':
        await clearAllTestimonials();
        break;

      case 'help':
      case undefined:
        showHelp();
        break;

      default:
        console.log(`❌ Unknown command: ${command}`);
        showHelp();
        break;
    }
  } catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

main();
