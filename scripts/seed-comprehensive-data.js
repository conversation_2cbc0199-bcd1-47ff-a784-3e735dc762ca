const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function main() {
  console.log('🚀 Starting comprehensive database seeding with real business data...');

  try {
    // Clear existing data in the right order (respecting foreign key constraints)
    console.log('🧹 Clearing existing data...');
    await prisma.activityLog.deleteMany();
    await prisma.timeEntry.deleteMany();
    await prisma.task.deleteMany();
    await prisma.communication.deleteMany();
    await prisma.projectTask.deleteMany();
    await prisma.project.deleteMany();
    await prisma.clientContact.deleteMany();
    await prisma.client.deleteMany();
    await prisma.user.deleteMany({ where: { email: { not: '<EMAIL>' } } });
    
    // 1. Create Admin User if doesn't exist
    console.log('👤 Creating Admin User...');
    const adminUser = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        name: 'System Administrator',
        hashedPassword: '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LeKg/KSk4DPvs2N0m', // password: Jack75522r
        role: 'ADMIN',
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    });

    // 2. Create Team Members as Users
    console.log('👥 Creating Team Members as Users...');
    const teamUsers = [
      {
        email: '<EMAIL>',
        name: 'John Kamau',
        role: 'ADMIN',
        department: 'Management',
        position: 'CEO & Founder'
      },
      {
        email: '<EMAIL>',
        name: 'Sarah Wanjiku',
        role: 'USER',
        department: 'Development',
        position: 'Lead Developer'
      },
      {
        email: '<EMAIL>',
        name: 'Michael Ochieng',
        role: 'USER',
        department: 'Design',
        position: 'Creative Director'
      },
      {
        email: '<EMAIL>',
        name: 'Grace Akinyi',
        role: 'USER',
        department: 'Marketing',
        position: 'Digital Marketing Manager'
      }
    ];

    const createdUsers = [];
    for (const userData of teamUsers) {
      const user = await prisma.user.upsert({
        where: { email: userData.email },
        update: userData,
        create: {
          ...userData,
          hashedPassword: '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LeKg/KSk4DPvs2N0m',
          isActive: true
        }
      });
      createdUsers.push(user);
    }
    console.log(`✅ Created ${createdUsers.length} team users`);

    // 3. Create Realistic Clients
    console.log('🏢 Creating Clients...');
    const clientsData = [
      {
        name: 'TechStart Kenya Ltd',
        email: '<EMAIL>',
        phone: '+254712345678',
        industry: 'Technology',
        companySize: 'MEDIUM',
        status: 'ACTIVE',
        address: 'Westlands, Nairobi',
        website: 'https://techstartkenya.com',
        description: 'Leading technology startup focusing on fintech solutions for SMEs in Kenya',
        contactPerson: 'David Kariuki',
        contactEmail: '<EMAIL>',
        contactPhone: '+254712345678'
      },
      {
        name: 'Green Solutions Enterprises',
        email: '<EMAIL>',
        phone: '+254723456789',
        industry: 'Environmental',
        companySize: 'SMALL',
        status: 'ACTIVE',
        address: 'Kilifi Road, Mombasa',
        website: 'https://greensolutions.co.ke',
        description: 'Environmental consulting and renewable energy solutions provider',
        contactPerson: 'Mary Njeri',
        contactEmail: '<EMAIL>',
        contactPhone: '+254723456789'
      },
      {
        name: 'Local Eats Restaurant',
        email: '<EMAIL>',
        phone: '+254734567890',
        industry: 'Food & Beverage',
        companySize: 'SMALL',
        status: 'ACTIVE',
        address: 'Tom Mboya Street, Kisumu',
        description: 'Popular restaurant chain specializing in local cuisine with delivery services',
        contactPerson: 'Peter Mwangi',
        contactEmail: '<EMAIL>',
        contactPhone: '+254734567890'
      },
      {
        name: 'Wellness Center Nakuru',
        email: '<EMAIL>',
        phone: '+254745678901',
        industry: 'Healthcare',
        companySize: 'SMALL',
        status: 'ACTIVE',
        address: 'Kenyatta Avenue, Nakuru',
        website: 'https://wellnessnakuru.com',
        description: 'Comprehensive wellness center offering medical, fitness, and spa services',
        contactPerson: 'Dr. Elizabeth Wambui',
        contactEmail: '<EMAIL>',
        contactPhone: '+254745678901'
      },
      {
        name: 'LogiCorp Limited',
        email: '<EMAIL>',
        phone: '+254756789012',
        industry: 'Logistics',
        companySize: 'LARGE',
        status: 'ACTIVE',
        address: 'Industrial Area, Nairobi',
        website: 'https://logicorp.co.ke',
        description: 'Leading logistics company providing supply chain solutions across East Africa',
        contactPerson: 'James Omondi',
        contactEmail: '<EMAIL>',
        contactPhone: '+254756789012'
      },
      {
        name: 'Beauty Essentials Store',
        email: '<EMAIL>',
        phone: '+254767890123',
        industry: 'Retail',
        companySize: 'SMALL',
        status: 'ACTIVE',
        address: 'Mama Ngina Street, Nairobi',
        website: 'https://beautyessentials.ke',
        description: 'Premium beauty products and cosmetics retailer with online presence',
        contactPerson: 'Lucy Atieno',
        contactEmail: '<EMAIL>',
        contactPhone: '+254767890123'
      },
      {
        name: 'EduTech Solutions',
        email: '<EMAIL>',
        phone: '+254778901234',
        industry: 'Education',
        companySize: 'MEDIUM',
        status: 'POTENTIAL',
        address: 'University Way, Nairobi',
        description: 'Educational technology company developing e-learning platforms',
        contactPerson: 'Dr. Samuel Kiptoo',
        contactEmail: '<EMAIL>',
        contactPhone: '+254778901234'
      }
    ];

    const createdClients = [];
    for (const clientData of clientsData) {
      const client = await prisma.client.create({
        data: {
          ...clientData,
          createdBy: adminUser.id,
          updatedBy: adminUser.id
        }
      });
      createdClients.push(client);
    }
    console.log(`✅ Created ${createdClients.length} clients`);

    // 4. Create Projects
    console.log('📋 Creating Projects...');
    const now = new Date();
    const projectsData = [
      {
        name: 'Corporate Website Redesign',
        description: 'Complete redesign of corporate website with modern UI/UX and mobile responsiveness',
        clientId: createdClients[0].id, // TechStart Kenya
        status: 'IN_PROGRESS',
        priority: 'HIGH',
        startDate: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
        endDate: new Date(now.getTime() + 15 * 24 * 60 * 60 * 1000), // 15 days from now
        estimatedHours: 120,
        actualHours: 80,
        budget: 150000,
        assignedTo: createdUsers[1].id, // Sarah Wanjiku
        progress: 65
      },
      {
        name: 'E-commerce Platform Development',
        description: 'Full e-commerce platform with inventory management, payment gateway integration, and admin dashboard',
        clientId: createdClients[1].id, // Green Solutions
        status: 'IN_PROGRESS',
        priority: 'HIGH',
        startDate: new Date(now.getTime() - 45 * 24 * 60 * 60 * 1000), // 45 days ago
        endDate: new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        estimatedHours: 200,
        actualHours: 150,
        budget: 300000,
        assignedTo: createdUsers[1].id, // Sarah Wanjiku
        progress: 75
      },
      {
        name: 'Restaurant Delivery Mobile App',
        description: 'Mobile application for food ordering and delivery with real-time tracking',
        clientId: createdClients[2].id, // Local Eats
        status: 'COMPLETED',
        priority: 'MEDIUM',
        startDate: new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000), // 90 days ago
        endDate: new Date(now.getTime() - 10 * 24 * 60 * 60 * 1000), // 10 days ago
        estimatedHours: 180,
        actualHours: 195,
        budget: 250000,
        assignedTo: createdUsers[1].id, // Sarah Wanjiku
        progress: 100
      },
      {
        name: 'Brand Identity Package',
        description: 'Complete brand identity including logo, business cards, letterheads, and brand guidelines',
        clientId: createdClients[3].id, // Wellness Center
        status: 'COMPLETED',
        priority: 'MEDIUM',
        startDate: new Date(now.getTime() - 60 * 24 * 60 * 60 * 1000), // 60 days ago
        endDate: new Date(now.getTime() - 20 * 24 * 60 * 60 * 1000), // 20 days ago
        estimatedHours: 60,
        actualHours: 58,
        budget: 80000,
        assignedTo: createdUsers[2].id, // Michael Ochieng
        progress: 100
      },
      {
        name: 'Corporate Website Development',
        description: 'Professional corporate website with service pages, team profiles, and contact forms',
        clientId: createdClients[4].id, // LogiCorp
        status: 'COMPLETED',
        priority: 'MEDIUM',
        startDate: new Date(now.getTime() - 120 * 24 * 60 * 60 * 1000), // 120 days ago
        endDate: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
        estimatedHours: 100,
        actualHours: 105,
        budget: 120000,
        assignedTo: createdUsers[1].id, // Sarah Wanjiku
        progress: 100
      },
      {
        name: 'E-commerce Store & SEO',
        description: 'Online beauty store with product catalog, shopping cart, and comprehensive SEO optimization',
        clientId: createdClients[5].id, // Beauty Essentials
        status: 'IN_PROGRESS',
        priority: 'HIGH',
        startDate: new Date(now.getTime() - 20 * 24 * 60 * 60 * 1000), // 20 days ago
        endDate: new Date(now.getTime() + 40 * 24 * 60 * 60 * 1000), // 40 days from now
        estimatedHours: 150,
        actualHours: 45,
        budget: 200000,
        assignedTo: createdUsers[1].id, // Sarah Wanjiku
        progress: 30
      },
      {
        name: 'Digital Marketing Consultation',
        description: 'Comprehensive digital marketing strategy and implementation plan',
        clientId: createdClients[6].id, // EduTech Solutions
        status: 'PLANNING',
        priority: 'LOW',
        startDate: new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
        endDate: new Date(now.getTime() + 60 * 24 * 60 * 60 * 1000), // 60 days from now
        estimatedHours: 80,
        actualHours: 0,
        budget: 100000,
        assignedTo: createdUsers[3].id, // Grace Akinyi
        progress: 0
      }
    ];

    const createdProjects = [];
    for (const projectData of projectsData) {
      const project = await prisma.project.create({
        data: {
          ...projectData,
          createdBy: adminUser.id,
          updatedBy: adminUser.id
        }
      });
      createdProjects.push(project);
    }
    console.log(`✅ Created ${createdProjects.length} projects`);

    // 5. Create Communications
    console.log('💬 Creating Communications...');
    const communicationsData = [
      {
        clientId: createdClients[0].id,
        projectId: createdProjects[0].id,
        type: 'EMAIL',
        direction: 'OUTBOUND',
        subject: 'Website Redesign - Progress Update',
        content: 'Hi David, I wanted to provide you with an update on your website redesign project. We have completed the wireframes and are now moving into the design phase. Please find attached the wireframes for your review.',
        contactPerson: 'David Kariuki',
        status: 'COMPLETED',
        scheduledAt: new Date(now.getTime() - 5 * 24 * 60 * 60 * 1000),
        completedAt: new Date(now.getTime() - 5 * 24 * 60 * 60 * 1000),
        createdBy: createdUsers[1].id,
        priority: 'MEDIUM'
      },
      {
        clientId: createdClients[1].id,
        projectId: createdProjects[1].id,
        type: 'CALL',
        direction: 'INBOUND',
        subject: 'E-commerce Platform Features Discussion',
        content: 'Client called to discuss additional features for the e-commerce platform including loyalty program and advanced analytics dashboard.',
        contactPerson: 'Mary Njeri',
        status: 'COMPLETED',
        scheduledAt: new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000),
        completedAt: new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000),
        createdBy: createdUsers[1].id,
        priority: 'HIGH',
        duration: 45
      },
      {
        clientId: createdClients[2].id,
        projectId: createdProjects[2].id,
        type: 'MEETING',
        direction: 'OUTBOUND',
        subject: 'App Launch Strategy Meeting',
        content: 'Scheduled meeting to discuss the launch strategy for the restaurant delivery mobile app including marketing approach and user acquisition.',
        contactPerson: 'Peter Mwangi',
        status: 'SCHEDULED',
        scheduledAt: new Date(now.getTime() + 2 * 24 * 60 * 60 * 1000),
        createdBy: createdUsers[3].id,
        priority: 'HIGH',
        location: 'Client Office, Kisumu'
      },
      {
        clientId: createdClients[3].id,
        projectId: createdProjects[3].id,
        type: 'EMAIL',
        direction: 'OUTBOUND',
        subject: 'Brand Guidelines Delivery',
        content: 'Dear Elizabeth, We are pleased to deliver the complete brand guidelines package for Wellness Center Nakuru. The package includes logo variations, color palette, typography guidelines, and usage examples.',
        contactPerson: 'Dr. Elizabeth Wambui',
        status: 'COMPLETED',
        scheduledAt: new Date(now.getTime() - 25 * 24 * 60 * 60 * 1000),
        completedAt: new Date(now.getTime() - 25 * 24 * 60 * 60 * 1000),
        createdBy: createdUsers[2].id,
        priority: 'MEDIUM'
      },
      {
        clientId: createdClients[4].id,
        type: 'CALL',
        direction: 'OUTBOUND',
        subject: 'Follow-up: Website Maintenance',
        content: 'Follow-up call to discuss ongoing website maintenance and potential new projects.',
        contactPerson: 'James Omondi',
        status: 'SCHEDULED',
        scheduledAt: new Date(now.getTime() + 1 * 24 * 60 * 60 * 1000),
        createdBy: createdUsers[1].id,
        priority: 'LOW'
      }
    ];

    const createdCommunications = [];
    for (const commData of communicationsData) {
      const communication = await prisma.communication.create({
        data: commData
      });
      createdCommunications.push(communication);
    }
    console.log(`✅ Created ${createdCommunications.length} communications`);

    // 6. Create Tasks
    console.log('✅ Creating Tasks...');
    const tasksData = [
      {
        title: 'Design Homepage Mockup',
        description: 'Create homepage mockup design for TechStart Kenya website redesign',
        projectId: createdProjects[0].id,
        assignedTo: createdUsers[2].id, // Michael Ochieng
        status: 'COMPLETED',
        priority: 'HIGH',
        dueDate: new Date(now.getTime() - 10 * 24 * 60 * 60 * 1000),
        completedAt: new Date(now.getTime() - 12 * 24 * 60 * 60 * 1000),
        estimatedHours: 16,
        actualHours: 14,
        progress: 100,
        createdBy: createdUsers[1].id
      },
      {
        title: 'Implement Payment Gateway',
        description: 'Integrate M-Pesa and card payment options for Green Solutions e-commerce platform',
        projectId: createdProjects[1].id,
        assignedTo: createdUsers[1].id, // Sarah Wanjiku
        status: 'IN_PROGRESS',
        priority: 'HIGH',
        dueDate: new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000),
        estimatedHours: 24,
        actualHours: 18,
        progress: 75,
        createdBy: createdUsers[1].id
      },
      {
        title: 'Content Creation for Website',
        description: 'Write and optimize content for all website pages including SEO keywords',
        projectId: createdProjects[0].id,
        assignedTo: createdUsers[3].id, // Grace Akinyi
        status: 'IN_PROGRESS',
        priority: 'MEDIUM',
        dueDate: new Date(now.getTime() + 5 * 24 * 60 * 60 * 1000),
        estimatedHours: 20,
        actualHours: 12,
        progress: 60,
        createdBy: createdUsers[1].id
      },
      {
        title: 'Mobile App Testing',
        description: 'Comprehensive testing of Local Eats mobile app on iOS and Android devices',
        projectId: createdProjects[2].id,
        assignedTo: createdUsers[1].id, // Sarah Wanjiku
        status: 'COMPLETED',
        priority: 'CRITICAL',
        dueDate: new Date(now.getTime() - 15 * 24 * 60 * 60 * 1000),
        completedAt: new Date(now.getTime() - 18 * 24 * 60 * 60 * 1000),
        estimatedHours: 32,
        actualHours: 28,
        progress: 100,
        createdBy: createdUsers[1].id
      },
      {
        title: 'SEO Optimization Setup',
        description: 'Implement SEO best practices and set up analytics for Beauty Essentials store',
        projectId: createdProjects[5].id,
        assignedTo: createdUsers[3].id, // Grace Akinyi
        status: 'PENDING',
        priority: 'MEDIUM',
        dueDate: new Date(now.getTime() + 14 * 24 * 60 * 60 * 1000),
        estimatedHours: 16,
        actualHours: 0,
        progress: 0,
        createdBy: createdUsers[1].id
      },
      {
        title: 'Client Feedback Integration',
        description: 'Incorporate client feedback into the current design iteration',
        projectId: createdProjects[0].id,
        assignedTo: createdUsers[2].id, // Michael Ochieng
        status: 'IN_PROGRESS',
        priority: 'HIGH',
        dueDate: new Date(now.getTime() + 3 * 24 * 60 * 60 * 1000),
        estimatedHours: 8,
        actualHours: 4,
        progress: 50,
        createdBy: createdUsers[1].id
      }
    ];

    const createdTasks = [];
    for (const taskData of tasksData) {
      const task = await prisma.task.create({
        data: taskData
      });
      createdTasks.push(task);
    }
    console.log(`✅ Created ${createdTasks.length} tasks`);

    // 7. Create Time Entries
    console.log('⏰ Creating Time Entries...');
    const timeEntriesData = [
      {
        userId: createdUsers[1].id, // Sarah Wanjiku
        projectId: createdProjects[0].id,
        taskId: createdTasks[0].id,
        description: 'Working on homepage wireframe and layout design',
        startTime: new Date(now.getTime() - 2 * 24 * 60 * 60 * 1000 - 4 * 60 * 60 * 1000),
        endTime: new Date(now.getTime() - 2 * 24 * 60 * 60 * 1000),
        duration: 240, // 4 hours in minutes
        hourlyRate: 2500,
        isBillable: true,
        status: 'APPROVED'
      },
      {
        userId: createdUsers[2].id, // Michael Ochieng
        projectId: createdProjects[3].id,
        description: 'Brand identity design and logo creation',
        startTime: new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000 - 6 * 60 * 60 * 1000),
        endTime: new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000),
        duration: 360, // 6 hours in minutes
        hourlyRate: 2000,
        isBillable: true,
        status: 'APPROVED'
      },
      {
        userId: createdUsers[1].id, // Sarah Wanjiku
        projectId: createdProjects[1].id,
        taskId: createdTasks[1].id,
        description: 'Payment gateway integration and testing',
        startTime: new Date(now.getTime() - 1 * 24 * 60 * 60 * 1000 - 5 * 60 * 60 * 1000),
        endTime: new Date(now.getTime() - 1 * 24 * 60 * 60 * 1000),
        duration: 300, // 5 hours in minutes
        hourlyRate: 2500,
        isBillable: true,
        status: 'PENDING'
      },
      {
        userId: createdUsers[3].id, // Grace Akinyi
        projectId: createdProjects[0].id,
        taskId: createdTasks[2].id,
        description: 'Content writing and SEO optimization',
        startTime: new Date(now.getTime() - 4 * 60 * 60 * 1000),
        endTime: new Date(now.getTime() - 1 * 60 * 60 * 1000),
        duration: 180, // 3 hours in minutes
        hourlyRate: 1800,
        isBillable: true,
        status: 'PENDING'
      },
      {
        userId: createdUsers[1].id, // Sarah Wanjiku
        projectId: createdProjects[2].id,
        description: 'Bug fixes and performance optimization',
        startTime: new Date(now.getTime() - 5 * 24 * 60 * 60 * 1000 - 3 * 60 * 60 * 1000),
        endTime: new Date(now.getTime() - 5 * 24 * 60 * 60 * 1000),
        duration: 180, // 3 hours in minutes
        hourlyRate: 2500,
        isBillable: true,
        status: 'APPROVED'
      }
    ];

    const createdTimeEntries = [];
    for (const timeData of timeEntriesData) {
      const timeEntry = await prisma.timeEntry.create({
        data: timeData
      });
      createdTimeEntries.push(timeEntry);
    }
    console.log(`✅ Created ${createdTimeEntries.length} time entries`);

    // 8. Create Activity Logs (for recent activities)
    console.log('📋 Creating Activity Logs...');
    const activitiesData = [
      {
        userId: createdUsers[1].id,
        action: 'CREATE',
        entityType: 'PROJECT',
        entityId: createdProjects[0].id,
        description: 'Created new project: Corporate Website Redesign'
      },
      {
        userId: createdUsers[2].id,
        action: 'UPDATE',
        entityType: 'TASK',
        entityId: createdTasks[0].id,
        description: 'Completed task: Design Homepage Mockup'
      },
      {
        userId: createdUsers[3].id,
        action: 'CREATE',
        entityType: 'COMMUNICATION',
        entityId: createdCommunications[0].id,
        description: 'Sent email to TechStart Kenya regarding project update'
      },
      {
        userId: createdUsers[1].id,
        action: 'UPDATE',
        entityType: 'PROJECT',
        entityId: createdProjects[1].id,
        description: 'Updated project progress: E-commerce Platform Development'
      }
    ];

    for (const activityData of activitiesData) {
      await prisma.activityLog.create({
        data: activityData
      });
    }
    console.log(`✅ Created ${activitiesData.length} activity logs`);

    // Summary
    console.log('\n🎉 Comprehensive business database seeding completed successfully!');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    console.log(`👤 Users: ${createdUsers.length + 1} (including admin)`);
    console.log(`🏢 Clients: ${createdClients.length}`);
    console.log(`📋 Projects: ${createdProjects.length}`);
    console.log(`💬 Communications: ${createdCommunications.length}`);
    console.log(`✅ Tasks: ${createdTasks.length}`);
    console.log(`⏰ Time Entries: ${createdTimeEntries.length}`);
    console.log(`📋 Activity Logs: ${activitiesData.length}`);
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');

    // Get final counts from database
    const finalCounts = await Promise.all([
      prisma.user.count(),
      prisma.client.count(),
      prisma.project.count(),
      prisma.task.count(),
      prisma.communication.count(),
      prisma.timeEntry.count(),
      prisma.activityLog.count()
    ]);

    console.log('\n📊 Final Database Summary:');
    console.log(`   Users: ${finalCounts[0]}`);
    console.log(`   Clients: ${finalCounts[1]}`);
    console.log(`   Projects: ${finalCounts[2]}`);
    console.log(`   Tasks: ${finalCounts[3]}`);
    console.log(`   Communications: ${finalCounts[4]}`);
    console.log(`   Time Entries: ${finalCounts[5]}`);
    console.log(`   Activity Logs: ${finalCounts[6]}`);
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');

  } catch (error) {
    console.error('❌ Error seeding database:', error);
    throw error;
  }
}

main()
  .catch((e) => {
    console.error('❌ Seeding failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  }); 