#!/usr/bin/env node

// Test script to create a backup for restore testing
const { exec } = require('child_process');
const { promisify } = require('util');
const path = require('path');
const fs = require('fs');

const execPromise = promisify(exec);

async function createTestBackup() {
  try {
    console.log('🔄 Creating test backup for restore validation...');

    // Get database URL from environment
    require('dotenv').config();
    const databaseUrl = process.env.DATABASE_URL;
    
    if (!databaseUrl) {
      throw new Error('DATABASE_URL not found in environment variables');
    }

    // Create backup directory if it doesn't exist
    const backupDir = path.join(process.cwd(), 'backups');
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir, { recursive: true });
    }

    // Generate backup filename
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupId = Math.random().toString(36).substring(0, 8);
    const backupFilename = `test-backup-${timestamp}-${backupId}.dump`;
    const backupPath = path.join(backupDir, backupFilename);

    console.log(`📁 Creating backup: ${backupFilename}`);

    // Create backup using pg_dump
    const pgDumpCommand = `pg_dump "${databaseUrl}" -Fc -f "${backupPath}"`;
    
    console.log('🔧 Executing pg_dump...');
    const { stdout, stderr } = await execPromise(pgDumpCommand);

    if (stderr && stderr.includes('ERROR:')) {
      throw new Error(`pg_dump failed: ${stderr}`);
    }

    // Verify backup file
    const stats = fs.statSync(backupPath);
    if (stats.size === 0) {
      throw new Error('Backup file is empty');
    }

    console.log('✅ Backup created successfully!');
    console.log(`📁 File: ${backupFilename}`);
    console.log(`📁 Path: ${backupPath}`);
    console.log(`📁 Size: ${stats.size} bytes`);
    console.log('');

    // Output the path for use in restore testing
    console.log('BACKUP_PATH=' + backupPath);
    console.log('BACKUP_FILENAME=' + backupFilename);

    return {
      path: backupPath,
      filename: backupFilename,
      size: stats.size
    };

  } catch (error) {
    console.error('❌ Backup creation failed:', error.message);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  createTestBackup();
}

module.exports = { createTestBackup }; 