const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function seedProjects() {
  try {
    console.log('🌱 Starting project seeding...');

    // First, get the existing clients
    const clients = await prisma.Client.findMany({
      select: { id: true, firstName: true, lastName: true, company: true }
    });

    if (clients.length === 0) {
      console.log('❌ No clients found. Please run client seeding first.');
      return;
    }

    console.log(`📋 Found ${clients.length} clients to link projects to`);

    // Create projects
    const projects = [
      {
        name: 'Website Redesign',
        description: 'Complete website redesign with modern UI/UX and responsive design. Includes new branding, improved user experience, and mobile optimization.',
        status: 'active',
        priority: 'high',
        progress: 65,
        startDate: new Date('2024-01-15'),
        endDate: new Date('2024-03-15'),
        deadline: new Date('2024-03-10'),
        budget: 850000, // 850,000 KES
        actualCost: 550000, // 550,000 KES spent so far
        clientId: clients[0].id
      },
      {
        name: 'E-commerce Platform',
        description: 'Development of a comprehensive e-commerce platform with payment integration, inventory management, and customer portal.',
        status: 'planning',
        priority: 'medium',
        progress: 15,
        startDate: new Date('2024-02-01'),
        endDate: new Date('2024-06-01'),
        deadline: new Date('2024-05-30'),
        budget: 1200000, // 1.2M KES
        actualCost: 180000, // 180,000 KES spent so far
        clientId: clients[1].id
      },
      {
        name: 'Mobile App Development',
        description: 'Native mobile application for both iOS and Android platforms with real-time features and offline capabilities.',
        status: 'on_hold',
        priority: 'low',
        progress: 30,
        startDate: new Date('2024-01-01'),
        endDate: new Date('2024-08-01'),
        deadline: new Date('2024-07-15'),
        budget: 2500000, // 2.5M KES
        actualCost: 750000, // 750,000 KES spent so far
        clientId: clients[2] ? clients[2].id : clients[0].id
      },
      {
        name: 'Digital Marketing Campaign',
        description: 'Comprehensive digital marketing strategy including SEO optimization, social media management, and PPC advertising.',
        status: 'completed',
        priority: 'medium',
        progress: 100,
        startDate: new Date('2023-10-01'),
        endDate: new Date('2024-01-01'),
        deadline: new Date('2023-12-31'),
        budget: 450000, // 450,000 KES
        actualCost: 425000, // 425,000 KES final cost
        clientId: clients[0].id
      },
      {
        name: 'System Integration',
        description: 'Integration of existing systems with new CRM platform, including data migration and API development.',
        status: 'active',
        priority: 'urgent',
        progress: 80,
        startDate: new Date('2024-02-15'),
        endDate: new Date('2024-04-15'),
        deadline: new Date('2024-04-10'),
        budget: 650000, // 650,000 KES
        actualCost: 520000, // 520,000 KES spent so far
        clientId: clients[1].id
      }
    ];

    console.log('📝 Creating projects...');

    // Create projects one by one to handle any errors
    const createdProjects = [];
    for (const projectData of projects) {
      try {
        const project = await prisma.Project.create({
          data: projectData
        });
        createdProjects.push(project);
        console.log(`✅ Created project: ${project.name} for client ${projectData.clientId}`);
      } catch (error) {
        console.error(`❌ Failed to create project ${projectData.name}:`, error.message);
      }
    }

    console.log(`\n🎉 Successfully created ${createdProjects.length} projects!`);
    
    // Display summary
    console.log('\n📊 Projects Summary:');
    createdProjects.forEach((project, index) => {
      const client = clients.find(c => c.id === project.clientId);
      console.log(`${index + 1}. ${project.name}`);
      console.log(`   Client: ${client?.firstName} ${client?.lastName} (${client?.company || 'Individual'})`);
      console.log(`   Status: ${project.status} | Priority: ${project.priority} | Progress: ${project.progress}%`);
      console.log(`   Budget: KES ${project.budget?.toLocaleString()}`);
      console.log('');
    });

  } catch (error) {
    console.error('❌ Error seeding projects:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seeding
if (require.main === module) {
  seedProjects()
    .then(() => {
      console.log('✅ Project seeding completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Project seeding failed:', error);
      process.exit(1);
    });
}

module.exports = seedProjects; 