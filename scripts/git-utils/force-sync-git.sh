#!/bin/bash

# Force Git Sync Script (Last Resort)
# This will backup local changes and sync with remote

echo "⚠️  FORCE GIT SYNC - LAST RESORT"
echo "================================"
echo "This will backup your local changes and force sync with remote"
echo ""

read -p "Are you sure you want to continue? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Aborted."
    exit 1
fi

cd /var/www/mocky

echo "💾 Creating backup of current state..."
BACKUP_BRANCH="backup-$(date +%Y%m%d-%H%M%S)"
git checkout -b "$BACKUP_BRANCH"
git add .
git commit -m "Backup before force sync - $(date)"

echo "📤 Pushing backup branch..."
git push origin "$BACKUP_BRANCH"

echo "🔄 Switching back to main and force sync..."
git checkout main

echo "🔄 Fetching remote..."
git fetch origin

echo "⚠️  Force resetting to remote main..."
git reset --hard origin/main

echo "📤 Ensuring remote sync..."
git push origin main --force-with-lease

echo ""
echo "✅ Force sync completed!"
echo "🎉 Repository is now synchronized with remote"
echo ""
echo "📋 Your local changes have been backed up to branch: $BACKUP_BRANCH"
echo "💡 To restore your changes later:"
echo "   git checkout $BACKUP_BRANCH"
echo "   git checkout main"
echo "   git cherry-pick <commit-hash-from-backup>"
echo ""
echo "🔍 To see what was backed up:"
echo "   git log $BACKUP_BRANCH --oneline -5" 