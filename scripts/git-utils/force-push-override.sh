#!/bin/bash

# Force Push Override Script
# This will override the remote repository with local changes

echo "🚀 FORCE PUSH OVERRIDE TO GITHUB"
echo "================================"
echo "This will override the remote repository with your local changes"
echo "⚠️  WARNING: This will overwrite remote changes!"
echo ""

read -p "Are you sure you want to override GitHub with local changes? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Aborted."
    exit 1
fi

cd /var/www/mocky

echo "📊 Current Git Status:"
git status

echo ""
echo "💾 Adding all current changes..."
git add .

echo "📝 Committing changes..."
git commit -m "Fix authentication error handling in admin catalogue pages - Override remote conflicts $(date)"

echo "🔍 Checking remote connection..."
if ! git remote -v; then
    echo "❌ No remote configured"
    exit 1
fi

echo ""
echo "🚀 Force pushing to override remote..."
echo "⚠️  This will override any remote changes!"

# Force push with lease is safer than --force
if git push origin main --force-with-lease; then
    echo "✅ Force push successful!"
else
    echo "❌ Force push with lease failed, trying regular force push..."
    if git push origin main --force; then
        echo "✅ Force push successful!"
    else
        echo "❌ Force push failed"
        exit 1
    fi
fi

echo ""
echo "🎉 SUCCESS! Local changes have been pushed to GitHub"
echo "✅ Remote repository has been overridden with local changes"
echo ""
echo "📋 Recent commits:"
git log --oneline -3

echo ""
echo "🔗 Your changes are now live on GitHub!" 