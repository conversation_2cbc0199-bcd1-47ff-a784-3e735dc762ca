#!/bin/bash

# Fix Git Conflicts Script
# This script will help resolve the diverged branches

echo "🔧 Fixing Git Conflicts..."
echo "================================"

# Navigate to the project directory
cd /var/www/mocky

# Check current git status
echo "📊 Current Git Status:"
git status

echo ""
echo "🔍 Checking divergence..."
git log --oneline --graph --decorate -10

echo ""
echo "🌐 Remote vs Local commits:"
git log --oneline origin/main..main
echo "Remote commits not in local:"
git log --oneline main..origin/main

echo ""
echo "🔄 Attempting to resolve..."

# Option 1: Try to merge (recommended)
echo "Trying to merge remote changes..."
if git merge origin/main --no-edit; then
    echo "✅ Merge successful!"
else
    echo "❌ Merge failed - conflicts detected"
    echo "🔍 Checking for conflict markers..."
    
    # Find files with conflicts
    git diff --name-only --diff-filter=U
    
    echo ""
    echo "Manual resolution needed. Files with conflicts:"
    git status --porcelain | grep "^UU"
    
    echo ""
    echo "🛠️  To resolve manually:"
    echo "1. Edit the conflicted files"
    echo "2. Remove conflict markers (<<<<<<, =======, >>>>>>>)"
    echo "3. Run: git add <file>"
    echo "4. Run: git commit"
    
    exit 1
fi

# Push the merged changes
echo "📤 Pushing merged changes..."
git push origin main

echo "✅ Git conflicts resolved successfully!"
echo "🎉 Your repository is now in sync!" 