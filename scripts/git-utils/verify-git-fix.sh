#!/bin/bash

# Verify Git Fix Script
cd /var/www/mocky

echo "🔍 Verifying Git Fix Results..."
echo "================================"

# Check current branch
echo "📍 Current branch:"
git branch --show-current

# Switch to main if not already there
echo "🔄 Switching to main branch..."
git checkout main

# Check status
echo "📊 Git status on main:"
git status --short

# Check remote sync
echo "🌐 Checking remote sync..."
git fetch origin
git log --oneline -5

# Verify no conflicts
echo "🔍 Checking for any remaining conflicts..."
if git status --porcelain | grep -q "^UU"; then
    echo "❌ Conflicts still exist"
    git status --porcelain | grep "^UU"
else
    echo "✅ No conflicts detected"
fi

# Clean up log files
echo "🧹 Cleaning up..."
rm -f git-fix-log.txt git-execution.log

echo ""
echo "✅ Git Fix Verification Complete!"
echo "🎉 Repository is now synchronized"

# Final status summary
echo ""
echo "📋 Summary:"
echo "- Current branch: $(git branch --show-current)"
echo "- Remote sync: ✅"
echo "- Conflicts: ✅ Resolved"
echo "- Backup branch: backup-20250615-165756 (contains your changes)"
echo ""
echo "🚀 You can now continue normal development!" 