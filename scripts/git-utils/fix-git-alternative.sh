#!/bin/bash

# Alternative Git Fix - Force Push Local Changes
# This will override remote with local changes

echo "🚀 GIT FIX - FORCE PUSH LOCAL CHANGES"
echo "====================================="
echo "This will override the remote repository with your local changes"
echo ""

cd /var/www/mocky || exit 1

echo "📊 Current Status:"
git status

echo ""
echo "💾 Adding all changes..."
git add .

echo ""
echo "📝 Committing authentication fixes..."
git commit -m "Fix authentication error handling in admin catalogue pages

- Added 401 status code detection and automatic redirect to login
- Enhanced error handling in catalogue view and list pages  
- Created reusable useAdminAuth hook for consistent auth handling
- Improved user experience during session expiration
- Override remote conflicts to push critical auth fixes"

echo ""
echo "🔍 Checking branches..."
git branch -v

echo ""
echo "🚀 Force pushing to override remote..."
echo "⚠️  This will override any conflicting remote changes!"

# Try force push with lease first (safer)
if git push origin main --force-with-lease 2>/dev/null; then
    echo "✅ Force push with lease successful!"
elif git push origin main --force 2>/dev/null; then
    echo "✅ Force push successful!"
else
    echo "❌ Push failed, trying alternative approach..."
    
    # Alternative: reset and push
    git fetch origin 2>/dev/null || true
    git reset --soft HEAD~1
    git add .
    git commit -m "Authentication fixes - force override $(date)"
    
    if git push origin main --force; then
        echo "✅ Alternative push successful!"
    else
        echo "❌ All push attempts failed"
        echo "Manual intervention may be required"
        exit 1
    fi
fi

echo ""
echo "🎉 SUCCESS! Local changes pushed to GitHub"
echo "✅ Authentication fixes are now live"
echo ""
echo "📋 Recent commits:"
git log --oneline -3 2>/dev/null || echo "Could not display log"

echo ""
echo "🔗 Changes successfully pushed to remote repository!"
echo "🛡️  Authentication error handling is now improved"

echo ""
echo "🔄 If both approaches fail, try force sync:"
echo "   bash force-sync-git.sh" 