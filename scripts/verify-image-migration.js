#!/usr/bin/env node

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function verifyImageMigration() {
  try {
    // Get products with images
    const products = await prisma.catalogue.findMany({
      where: { imageUrl: { not: null } },
      take: 5,
      select: {
        id: true,
        service: true,
        imageUrl: true,
        imageUrl2: true,
        imageUrl3: true
      }
    });
    
    console.log('🖼️  IMAGE URLS AFTER MIGRATION:');
    console.log('==============================');
    
    if (products.length === 0) {
      console.log('❌ No products with images found');
      return;
    }
    
    let s3Count = 0;
    let externalCount = 0;
    
    products.forEach(p => {
      console.log(`\nProduct: ${p.service} (ID: ${p.id})`);
      
      if (p.imageUrl) {
        const url = p.imageUrl;
        const isS3 = url.includes('linodeobjects.com') || url.includes('amazonaws.com');
        console.log(`  Image 1: ${url.substring(0, 80)}...`);
        console.log(`  Source: ${isS3 ? '✅ S3' : '❌ External'}`);
        
        if (isS3) s3Count++;
        else externalCount++;
      }
      
      if (p.imageUrl2) {
        console.log(`  Image 2: ${p.imageUrl2.substring(0, 80)}...`);
      }
      
      if (p.imageUrl3) {
        console.log(`  Image 3: ${p.imageUrl3.substring(0, 80)}...`);
      }
    });
    
    console.log('\n📊 MIGRATION SUMMARY:');
    console.log('=====================');
    console.log(`✅ S3 hosted images: ${s3Count}`);
    console.log(`❌ External images: ${externalCount}`);
    
    if (externalCount === 0) {
      console.log('🎉 All images successfully migrated to S3!');
    } else {
      console.log('⚠️  Some images still hosted externally');
    }
    
  } catch (error) {
    console.error('❌ Error verifying image migration:', error);
  } finally {
    await prisma.$disconnect();
  }
}

verifyImageMigration(); 