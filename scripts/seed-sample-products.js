const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function seedSampleProducts() {
  console.log('Seeding sample products...');

  const products = [
    {
      service: 'Business Cards',
      description: 'Professional business cards with various paper options',
      price: 20, // Base price per unit for paper print
      category: 'Print',
      pricingType: 'paper_print',
      unitType: 'piece',
      minQuantity: 100,
      maxQuantity: 5000,
      features: ['High Quality Print', 'Various Paper Types', 'Single/Double Sided']
    },
    {
      service: 'Flyers',
      description: 'Eye-catching flyers for marketing and promotions',
      price: 15, // Base price per unit for paper print
      category: 'Print',
      pricingType: 'paper_print',
      unitType: 'piece',
      minQuantity: 50,
      maxQuantity: 10000,
      features: ['Full Color', 'Various Sizes', 'Matt/Gloss Finish']
    },
    {
      service: 'Banner Print',
      description: 'Large format banner printing for outdoor advertising',
      price: 100, // Fallback price
      category: 'Large Format',
      pricingType: 'banner_meter',
      unitType: 'meter',
      pricePerMeter: 100,
      minMeters: 0.5,
      maxMeters: 50,
      features: ['Weather Resistant', 'Full Color', 'Various Sizes']
    },
    {
      service: 'Vinyl Banner',
      description: 'Durable vinyl banners for long-term outdoor use',
      price: 150, // Fallback price
      category: 'Large Format',
      pricingType: 'banner_meter',
      unitType: 'meter',
      pricePerMeter: 150,
      minMeters: 1,
      maxMeters: 100,
      features: ['Waterproof', 'UV Resistant', 'High Resolution']
    },
    {
      service: 'Roll-up Banner',
      description: 'Portable roll-up banners for events and exhibitions',
      price: 2500, // Fixed price per unit
      category: 'Display',
      pricingType: 'fixed',
      unitType: 'piece',
      minQuantity: 1,
      maxQuantity: 50,
      features: ['Portable', 'Easy Setup', 'Professional Display']
    },
    {
      service: 'Brochures',
      description: 'Folded brochures for detailed product information',
      price: 25, // Base price per unit for paper print
      category: 'Print',
      pricingType: 'paper_print',
      unitType: 'piece',
      minQuantity: 100,
      maxQuantity: 5000,
      features: ['Tri-fold/Bi-fold', 'High Quality Paper', 'Full Color']
    },
    {
      service: 'Stickers',
      description: 'Custom stickers in various shapes and sizes',
      price: 5, // Base price per unit
      category: 'Print',
      pricingType: 'fixed',
      unitType: 'piece',
      minQuantity: 50,
      maxQuantity: 10000,
      features: ['Waterproof', 'Various Shapes', 'Strong Adhesive']
    },
    {
      service: 'Poster Print',
      description: 'High-quality poster printing for events and promotions',
      price: 30, // Base price per unit for paper print
      category: 'Print',
      pricingType: 'paper_print',
      unitType: 'piece',
      minQuantity: 1,
      maxQuantity: 1000,
      features: ['Various Sizes', 'High Resolution', 'Matt/Gloss Options']
    }
  ];

  for (const product of products) {
    try {
      const result = await prisma.catalogue.create({
        data: {
          service: product.service,
          description: product.description,
          price: product.price,
          category: product.category,
          pricingType: product.pricingType,
          unitType: product.unitType,
          minQuantity: product.minQuantity || null,
          maxQuantity: product.maxQuantity || null,
          pricePerMeter: product.pricePerMeter || null,
          minMeters: product.minMeters || null,
          maxMeters: product.maxMeters || null,
          features: product.features,
          popular: false
        }
      });
      console.log(`✓ Created ${product.service} (${product.pricingType})`);
    } catch (error) {
      console.error(`✗ Failed to create ${product.service}:`, error.message);
    }
  }
}

async function main() {
  try {
    await seedSampleProducts();
    console.log('\n✅ Sample products seeded successfully!');
  } catch (error) {
    console.error('❌ Seeding failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main(); 