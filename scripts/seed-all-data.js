const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function main() {
  console.log('🚀 Starting comprehensive database seeding...');

  try {
    // 1. Seed Categories
    console.log('📦 Seeding Categories...');
    const categories = [
      {
        name: 'Web Development',
        slug: 'web-development',
        description: 'Custom website development and web applications'
      },
      {
        name: 'Graphic Design',
        slug: 'graphic-design',
        description: 'Professional graphic design services including logos, branding, and print materials'
      },
      {
        name: 'Digital Marketing',
        slug: 'digital-marketing',
        description: 'Digital marketing services including SEO, social media, and online advertising'
      },
      {
        name: 'Branding',
        slug: 'branding',
        description: 'Complete branding solutions including logo design and brand identity'
      },
      {
        name: 'E-commerce',
        slug: 'ecommerce',
        description: 'E-commerce solutions and online store development'
      },
      {
        name: 'Mobile Apps',
        slug: 'mobile-apps',
        description: 'Mobile application development for iOS and Android'
      }
    ];

    for (const category of categories) {
      await prisma.category.upsert({
        where: { slug: category.slug },
        update: category,
        create: category
      });
    }
    console.log(`✅ Created ${categories.length} categories`);

    // 2. Seed Team Members
    console.log('👥 Seeding Team Members...');
    const teamMembers = [
      {
        name: '<PERSON> <PERSON>mau',
        role: 'CEO & Founder',
        bio: 'Visionary leader with over 10 years of experience in digital marketing and business development. John founded Mocky Digital with the mission to help businesses succeed online.',
        imageKey: 'team/john-kamau.jpg',
        order: 1,
        linkedinUrl: 'https://linkedin.com/in/johnkamau',
        emailAddress: '<EMAIL>'
      },
      {
        name: 'Sarah Wanjiku',
        role: 'Lead Web Developer',
        bio: 'Full-stack developer specializing in modern web technologies including React, Node.js, and cloud platforms. Sarah leads our development team with expertise in scalable web solutions.',
        imageKey: 'team/sarah-wanjiku.jpg',
        order: 2,
        linkedinUrl: 'https://linkedin.com/in/sarahwanjiku',
        githubUrl: 'https://github.com/sarahwanjiku',
        emailAddress: '<EMAIL>'
      },
      {
        name: 'Michael Ochieng',
        role: 'Creative Director',
        bio: 'Award-winning graphic designer with a passion for creating compelling visual stories. Michael brings creativity and strategic thinking to every design project.',
        imageKey: 'team/michael-ochieng.jpg',
        order: 3,
        linkedinUrl: 'https://linkedin.com/in/michaelochieng',
        emailAddress: '<EMAIL>'
      },
      {
        name: 'Grace Akinyi',
        role: 'Digital Marketing Manager',
        bio: 'Digital marketing expert with proven track record in SEO, social media marketing, and content strategy. Grace helps clients achieve measurable growth online.',
        imageKey: 'team/grace-akinyi.jpg',
        order: 4,
        linkedinUrl: 'https://linkedin.com/in/graceakinyi',
        twitterUrl: 'https://twitter.com/graceakinyi',
        emailAddress: '<EMAIL>'
      }
    ];

    // Clear existing team members first to avoid duplicates
    await prisma.teamMember.deleteMany();
    
    for (const member of teamMembers) {
      await prisma.teamMember.create({
        data: member
      });
    }
    console.log(`✅ Created ${teamMembers.length} team members`);

    // 3. Seed Testimonials
    console.log('💬 Seeding Testimonials...');
    
    // Clear existing testimonials first to avoid duplicates
    await prisma.testimonial.deleteMany();
    
    const testimonials = [
      {
        name: 'David Kariuki',
        location: 'Nairobi, Kenya',
        project: 'Corporate Website Development',
        testimonial: 'Mocky Digital transformed our online presence completely. Their web development team created a stunning website that perfectly represents our brand and has significantly increased our lead generation.',
        rating: 5,
        company: 'TechStart Kenya',
        active: true,
        order: 1
      },
      {
        name: 'Mary Njeri',
        location: 'Mombasa, Kenya',
        project: 'E-commerce Platform & Digital Marketing',
        testimonial: 'Outstanding service! The team at Mocky Digital not only designed our new website but also implemented a comprehensive digital marketing strategy that doubled our online sales in just 3 months.',
        rating: 5,
        company: 'Green Solutions',
        active: true,
        order: 2
      },
      {
        name: 'Peter Mwangi',
        location: 'Kisumu, Kenya',
        project: 'Restaurant Delivery Platform',
        testimonial: 'Professional, creative, and reliable. Mocky Digital helped us launch our restaurant delivery platform. The user experience is fantastic and our customers love the easy ordering process.',
        rating: 5,
        company: 'Local Eats',
        active: true,
        order: 3
      },
      {
        name: 'Elizabeth Wambui',
        location: 'Nakuru, Kenya',
        project: 'Brand Identity & Marketing Materials',
        testimonial: 'The graphic design work for our wellness center was exceptional. From logo design to marketing materials, everything was perfectly crafted and professional. Highly recommended!',
        rating: 5,
        company: 'Wellness Center',
        active: true,
        order: 4
      },
      {
        name: 'James Omondi',
        location: 'Eldoret, Kenya',
        project: 'Corporate Website',
        testimonial: 'Mocky Digital delivered our corporate website on time and within budget. The team was professional throughout the process and provided excellent support after launch.',
        rating: 4,
        company: 'LogiCorp',
        active: true,
        order: 5
      },
      {
        name: 'Lucy Atieno',
        location: 'Nairobi, Kenya',
        project: 'E-commerce Store Development',
        testimonial: 'Amazing e-commerce solution! Our online store built by Mocky Digital has streamlined our sales process and improved customer satisfaction. The admin panel is user-friendly and powerful.',
        rating: 5,
        company: 'Beauty Essentials',
        active: true,
        order: 6
      }
    ];

    for (const testimonial of testimonials) {
      await prisma.testimonial.create({
        data: testimonial
      });
    }
    console.log(`✅ Created ${testimonials.length} testimonials`);

    // 4. Seed Blog Posts
    console.log('📝 Seeding Blog Posts...');
    const blogPosts = [
      {
        title: 'Top 10 Web Development Trends in 2024',
        slug: 'top-10-web-development-trends-2024',
        content: 'Discover the latest web development trends that are shaping the digital landscape in 2024...',
        excerpt: 'Stay ahead of the curve with these essential web development trends for 2024.',
        author: 'Sarah Wanjiku',
        category: 'Web Development',
        status: 'published',
        publishedAt: new Date(),
        tags: ['web development', 'trends', '2024', 'technology']
      },
      {
        title: 'The Ultimate Guide to SEO for Small Businesses',
        slug: 'ultimate-guide-seo-small-businesses',
        content: 'Search Engine Optimization (SEO) is crucial for small businesses looking to increase their online visibility...',
        excerpt: 'Learn how to improve your website\'s search engine rankings with practical SEO strategies.',
        author: 'Grace Akinyi',
        category: 'Digital Marketing',
        status: 'published',
        publishedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 1 week ago
        tags: ['SEO', 'digital marketing', 'small business', 'online visibility']
      },
      {
        title: 'Building a Strong Brand Identity: A Designer\'s Perspective',
        slug: 'building-strong-brand-identity-designers-perspective',
        content: 'Brand identity goes beyond just a logo. It encompasses the entire visual and emotional experience...',
        excerpt: 'Explore the key elements of creating a memorable and effective brand identity.',
        author: 'Michael Ochieng',
        category: 'Branding',
        status: 'published',
        publishedAt: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000), // 2 weeks ago
        tags: ['branding', 'design', 'identity', 'logo']
      },
      {
        title: 'E-commerce Success: Converting Visitors into Customers',
        slug: 'ecommerce-success-converting-visitors-customers',
        content: 'Converting website visitors into paying customers is the ultimate goal of any e-commerce business...',
        excerpt: 'Discover proven strategies to optimize your e-commerce website for maximum conversions.',
        author: 'John Kamau',
        category: 'E-commerce',
        status: 'published',
        publishedAt: new Date(Date.now() - 21 * 24 * 60 * 60 * 1000), // 3 weeks ago
        tags: ['ecommerce', 'conversion', 'online sales', 'user experience']
      }
    ];

    for (const post of blogPosts) {
      await prisma.blogPost.upsert({
        where: { slug: post.slug },
        update: post,
        create: post
      });
    }
    console.log(`✅ Created ${blogPosts.length} blog posts`);

    // 5. Update Site Settings
    console.log('⚙️ Updating Site Settings...');
    
    // Check if site settings exist
    const existingSettings = await prisma.siteSettings.findFirst();
    
    const settingsData = {
      siteName: 'Mocky Digital',
      siteDescription: 'Professional web design, graphic design, and digital marketing services in Kenya',
      contactEmail: '<EMAIL>',
      phoneNumber: '+254712345678',
      address: 'Nairobi, Kenya',
      facebookUrl: 'https://facebook.com/mockydigital',
      twitterUrl: 'https://twitter.com/mockydigital',
      instagramUrl: 'https://instagram.com/mockydigital',
      linkedinUrl: 'https://linkedin.com/company/mockydigital',
      metaTitle: 'Mocky Digital - Web Design & Digital Marketing Agency Kenya',
      metaDescription: 'Professional web design, graphic design, and digital marketing services in Kenya. Transform your business with our expert digital solutions.'
    };

    if (existingSettings) {
      await prisma.siteSettings.update({
        where: { id: existingSettings.id },
        data: settingsData
      });
    } else {
      await prisma.siteSettings.create({
        data: settingsData
      });
    }
    console.log('✅ Updated site settings');

    // 6. Seed Catalogue Items
    console.log('🛍️ Seeding Catalogue Items...');
    
    // Clear existing catalogue items first to avoid duplicates
    await prisma.catalogue.deleteMany();
    
    const catalogueItems = [
      {
        service: 'Logo Design',
        description: 'Professional logo design with unlimited revisions',
        price: 15000,
        category: 'Graphic Design',
        features: ['Unlimited revisions', 'Multiple formats', 'Copyright ownership', 'Brand guidelines'],
        popular: true,
        icon: 'fas fa-paint-brush',
        imageUrl: '/images/services/logo-design.jpg'
      },
      {
        service: 'Business Website',
        description: 'Professional business website with responsive design',
        price: 80000,
        category: 'Web Development',
        features: ['Responsive design', 'SEO optimized', 'Contact forms', 'Google Analytics', '1 year support'],
        popular: true,
        icon: 'fas fa-laptop-code',
        imageUrl: '/images/services/business-website.jpg'
      },
      {
        service: 'E-commerce Store',
        description: 'Complete online store with payment integration',
        price: 150000,
        category: 'E-commerce',
        features: ['Payment gateway', 'Inventory management', 'Order tracking', 'Mobile app', 'SEO ready'],
        popular: false,
        icon: 'fas fa-shopping-cart',
        imageUrl: '/images/services/ecommerce-store.jpg'
      },
      {
        service: 'Social Media Management',
        description: 'Complete social media management for all platforms',
        price: 25000,
        category: 'Digital Marketing',
        features: ['Content creation', 'Daily posting', 'Engagement management', 'Monthly reports', 'Strategy development'],
        popular: true,
        icon: 'fas fa-share-alt',
        imageUrl: '/images/services/social-media.jpg'
      },
      {
        service: 'Business Card Design',
        description: 'Professional business card design with print-ready files',
        price: 5000,
        category: 'Graphic Design',
        features: ['Double-sided design', 'Print-ready files', '3 design concepts', 'Quick turnaround'],
        popular: false,
        icon: 'fas fa-id-card',
        imageUrl: '/images/services/business-card.jpg'
      },
      {
        service: 'SEO Optimization',
        description: 'Complete SEO optimization for better search rankings',
        price: 35000,
        category: 'Digital Marketing',
        features: ['Keyword research', 'On-page optimization', 'Technical SEO', 'Monthly reports', '6 month package'],
        popular: true,
        icon: 'fas fa-search',
        imageUrl: '/images/services/seo.jpg'
      },
      {
        service: 'Company Profile Design',
        description: 'Professional company profile with modern layout',
        price: 25000,
        category: 'Graphic Design',
        features: ['Professional layout', 'High-quality graphics', 'Print and digital versions', 'Unlimited revisions'],
        popular: false,
        icon: 'fas fa-file-alt',
        imageUrl: '/images/services/company-profile.jpg'
      },
      {
        service: 'Mobile App Development',
        description: 'Custom mobile app for iOS and Android',
        price: 300000,
        category: 'Mobile Apps',
        features: ['Cross-platform', 'Cloud integration', 'Push notifications', 'App store submission', '6 month support'],
        popular: false,
        icon: 'fas fa-mobile-alt',
        imageUrl: '/images/services/mobile-app.jpg'
      }
    ];

    for (const item of catalogueItems) {
      await prisma.catalogue.create({
        data: item
      });
    }
    console.log(`✅ Created ${catalogueItems.length} catalogue items`);

    // Summary
    console.log('\n🎉 Comprehensive database seeding completed successfully!');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    console.log(`📦 Categories: ${categories.length}`);
    console.log(`👥 Team Members: ${teamMembers.length}`);
    console.log(`💬 Testimonials: ${testimonials.length}`);
    console.log(`📝 Blog Posts: ${blogPosts.length}`);
    console.log(`⚙️ Site Settings: Updated`);
    console.log(`🛍️ Catalogue Items: ${catalogueItems.length}`);
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');

    // Get final counts from database
    const finalCounts = await Promise.all([
      prisma.category.count(),
      prisma.teamMember.count(),
      prisma.testimonial.count(),
      prisma.blogPost.count(),
      prisma.catalogue.count(),
      prisma.websitePortfolio.count(),
      prisma.service.count(),
      prisma.quote.count(),
      prisma.invoice.count(),
      prisma.receipt.count(),
      prisma.transaction.count()
    ]);

    console.log('\n📊 Final Database Summary:');
    console.log(`   Categories: ${finalCounts[0]}`);
    console.log(`   Team Members: ${finalCounts[1]}`);
    console.log(`   Testimonials: ${finalCounts[2]}`);
    console.log(`   Blog Posts: ${finalCounts[3]}`);
    console.log(`   Catalogue Items: ${finalCounts[4]}`);
    console.log(`   Website Portfolio: ${finalCounts[5]}`);
    console.log(`   Services: ${finalCounts[6]}`);
    console.log(`   Quotes: ${finalCounts[7]}`);
    console.log(`   Invoices: ${finalCounts[8]}`);
    console.log(`   Receipts: ${finalCounts[9]}`);
    console.log(`   Transactions: ${finalCounts[10]}`);
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');

  } catch (error) {
    console.error('❌ Error seeding database:', error);
    throw error;
  }
}

main()
  .catch((e) => {
    console.error('❌ Seeding failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  }); 