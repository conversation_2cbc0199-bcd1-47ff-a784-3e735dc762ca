#!/usr/bin/env node

/**
 * Production Security Fix Script
 * Helps identify and fix critical security issues before deployment
 */

const fs = require('fs');
const path = require('path');

console.log('🔒 Production Security Fix Script');
console.log('==================================\n');

// Check for critical environment variables
function checkEnvironmentVariables() {
  console.log('1. Checking Environment Variables...');
  
  const criticalVars = [
    'DATABASE_URL',
    'NEXTAUTH_SECRET',
    'ADMIN_USERNAME',
    'ADMIN_PASSWORD'
  ];
  
  const missingVars = [];
  
  criticalVars.forEach(varName => {
    if (!process.env[varName]) {
      missingVars.push(varName);
    }
  });
  
  if (missingVars.length > 0) {
    console.log('❌ Missing critical environment variables:');
    missingVars.forEach(varName => {
      console.log(`   - ${varName}`);
    });
  } else {
    console.log('✅ All critical environment variables are set');
  }
  
  // Check for insecure S3 variables
  const insecureS3Vars = [
    'NEXT_PUBLIC_S3_ACCESS_KEY',
    'NEXT_PUBLIC_S3_SECRET_KEY'
  ];
  
  const foundInsecureVars = [];
  insecureS3Vars.forEach(varName => {
    if (process.env[varName]) {
      foundInsecureVars.push(varName);
    }
  });
  
  if (foundInsecureVars.length > 0) {
    console.log('⚠️  SECURITY WARNING: Found exposed S3 credentials:');
    foundInsecureVars.forEach(varName => {
      console.log(`   - ${varName} (should be server-side only)`);
    });
    console.log('   Move these to S3_ACCESS_KEY and S3_SECRET_KEY (without NEXT_PUBLIC_ prefix)');
  }
  
  console.log('');
}

// Check for debug routes
function checkDebugRoutes() {
  console.log('2. Checking for Debug Routes...');
  
  const debugRoutes = [];
  const apiDir = path.join(process.cwd(), 'src', 'app', 'api');
  
  function scanDirectory(dir) {
    try {
      const items = fs.readdirSync(dir);
      
      items.forEach(item => {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        
        if (stat.isDirectory()) {
          if (item.includes('debug')) {
            debugRoutes.push(fullPath.replace(process.cwd(), ''));
          }
          scanDirectory(fullPath);
        } else if (item === 'route.ts' && dir.includes('debug')) {
          debugRoutes.push(dir.replace(process.cwd(), ''));
        }
      });
    } catch (error) {
      // Directory doesn't exist or can't be read
    }
  }
  
  scanDirectory(apiDir);
  
  if (debugRoutes.length > 0) {
    console.log('⚠️  Found debug routes that should be disabled in production:');
    debugRoutes.forEach(route => {
      console.log(`   - ${route}`);
    });
  } else {
    console.log('✅ No debug routes found');
  }
  
  console.log('');
}

// Check for CSRF protection
function checkCSRFProtection() {
  console.log('3. Checking CSRF Protection...');
  
  const routesToCheck = [
    'src/app/api/admin/receipts/check/route.ts',
    'src/app/api/admin/transactions/route.ts',
    'src/app/api/admin/quotes/route.ts',
    'src/app/api/admin/invoices/route.ts'
  ];
  
  const missingCSRF = [];
  
  routesToCheck.forEach(routePath => {
    const fullPath = path.join(process.cwd(), routePath);
    try {
      const content = fs.readFileSync(fullPath, 'utf8');
      if (!content.includes('csrf') && !content.includes('CSRF')) {
        missingCSRF.push(routePath);
      }
    } catch (error) {
      // File doesn't exist
    }
  });
  
  if (missingCSRF.length > 0) {
    console.log('⚠️  Routes missing CSRF protection:');
    missingCSRF.forEach(route => {
      console.log(`   - ${route}`);
    });
  } else {
    console.log('✅ CSRF protection appears to be implemented');
  }
  
  console.log('');
}

// Check TypeScript types
function checkTypeScriptTypes() {
  console.log('4. Checking TypeScript Types...');
  
  // This would require running the TypeScript compiler
  // For now, just remind to run the linter
  console.log('ℹ️  Run `npm run lint` to check for TypeScript issues');
  console.log('');
}

// Generate security report
function generateSecurityReport() {
  console.log('5. Security Report Summary');
  console.log('========================');
  
  const report = {
    timestamp: new Date().toISOString(),
    checks: [
      'Environment Variables',
      'Debug Routes',
      'CSRF Protection',
      'TypeScript Types'
    ],
    recommendations: [
      'Fix S3 credential exposure',
      'Add CSRF protection to remaining endpoints',
      'Disable debug routes in production',
      'Review and fix TypeScript any types'
    ]
  };
  
  const reportPath = path.join(process.cwd(), 'security-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  
  console.log(`📄 Security report saved to: ${reportPath}`);
  console.log('');
}

// Main execution
function main() {
  checkEnvironmentVariables();
  checkDebugRoutes();
  checkCSRFProtection();
  checkTypeScriptTypes();
  generateSecurityReport();
  
  console.log('🎯 Next Steps:');
  console.log('1. Review the PRODUCTION_SECURITY_CHECKLIST.md file');
  console.log('2. Fix all HIGH and MEDIUM priority issues');
  console.log('3. Run `npm run audit:security` for detailed analysis');
  console.log('4. Test all admin functionality before deployment');
  console.log('5. Set up production monitoring and alerts');
  console.log('');
  console.log('✅ Security check complete!');
}

// Run the script
main(); 