// Fix StorageConfig entry in the database
require('dotenv').config();
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  try {
    console.log('Checking StorageConfig entries in the database...');
    
    // Delete all existing StorageConfig entries
    const deleteResult = await prisma.storageConfig.deleteMany({});
    console.log(`Deleted ${deleteResult.count} existing StorageConfig entries`);
    
    // Create a new StorageConfig entry with the correct values
    const newConfig = await prisma.storageConfig.create({
      data: {
        provider: 'S3',
        region: 'fr-par-1',
        endpoint: 'https://fr-par-1.linodeobjects.com',
        bucketName: 'mocky',
        accessKeyId: '73OQS52ORLRPBO3KG6YN',
        secretAccessKey: 'p5UW5FZ7Gog5PMP749MpdHGhPRXUKYnStFJtAaMx',
        isDefault: true
      }
    });
    
    console.log('Created new StorageConfig entry:');
    console.log(`  ID: ${newConfig.id}`);
    console.log(`  Provider: ${newConfig.provider}`);
    console.log(`  Region: ${newConfig.region}`);
    console.log(`  Endpoint: ${newConfig.endpoint}`);
    console.log(`  Bucket: ${newConfig.bucketName}`);
    console.log(`  Access Key: ${newConfig.accessKeyId ? '✓ Set' : '✗ Not set'}`);
    console.log(`  Secret Key: ${newConfig.secretAccessKey ? '✓ Set' : '✗ Not set'}`);
    console.log(`  Is Default: ${newConfig.isDefault ? 'Yes' : 'No'}`);
    
    console.log('StorageConfig fixed successfully!');
  } catch (error) {
    console.error('Error fixing StorageConfig:', error);
  } finally {
    await prisma.$disconnect();
  }
}

main();
