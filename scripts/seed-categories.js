/**
 * Seed Categories Script
 * 
 * This script seeds the database with default catalogue categories.
 * Run with: node scripts/seed-categories.js
 */

const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

const DEFAULT_CATEGORIES = [
  {
    name: 'Web Development',
    slug: 'web-development',
    description: 'Web development services including websites, web applications, and e-commerce solutions'
  },
  {
    name: 'Design',
    slug: 'design',
    description: 'Graphic design services including logos, branding, and visual identity'
  },
  {
    name: 'Marketing',
    slug: 'marketing',
    description: 'Digital marketing services including social media, SEO, and advertising'
  },
  {
    name: 'Consulting',
    slug: 'consulting',
    description: 'Business and technical consulting services'
  },
  {
    name: 'Printing',
    slug: 'printing',
    description: 'Printing services for business cards, banners, and promotional materials'
  },
  {
    name: 'Branding',
    slug: 'branding',
    description: 'Brand identity and corporate branding services'
  },
  {
    name: 'Other',
    slug: 'other',
    description: 'Miscellaneous services and custom solutions'
  }
];

async function seedCategories() {
  console.log('🌱 Starting category seeding...');

  try {
    for (const category of DEFAULT_CATEGORIES) {
      // Check if category already exists
      const existingCategory = await prisma.category.findFirst({
        where: {
          OR: [
            { name: category.name },
            { slug: category.slug }
          ]
        }
      });

      if (existingCategory) {
        console.log(`⏭️  Category "${category.name}" already exists, skipping...`);
        continue;
      }

      // Create the category
      const newCategory = await prisma.category.create({
        data: category
      });

      console.log(`✅ Created category: ${newCategory.name} (ID: ${newCategory.id})`);
    }

    console.log('\n🎉 Category seeding completed successfully!');
    
    // Show summary
    const totalCategories = await prisma.category.count();
    console.log(`📊 Total categories in database: ${totalCategories}`);

  } catch (error) {
    console.error('❌ Error seeding categories:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seeding
if (require.main === module) {
  seedCategories();
}

module.exports = { seedCategories, DEFAULT_CATEGORIES }; 