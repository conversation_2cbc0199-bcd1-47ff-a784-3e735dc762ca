// Script to update blog post dates to the current date
const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function updateBlogPost() {
  try {
    // Get the current date
    const currentDate = new Date();
    console.log(`Updating blog posts to use current date: ${currentDate.toISOString()}`);

    // Update all blog posts
    const updateResult = await prisma.blogPost.updateMany({
      where: {
        status: 'published',
      },
      data: {
        publishedAt: currentDate,
        // Also update createdAt and updatedAt to ensure consistency
        createdAt: currentDate,
        updatedAt: currentDate,
      },
    });

    console.log(`Updated ${updateResult.count} blog posts`);
  } catch (error) {
    console.error('Error updating blog posts:', error);
  } finally {
    await prisma.$disconnect();
  }
}

updateBlogPost()
  .then(() => console.log('Done!'))
  .catch((e) => console.error(e)); 