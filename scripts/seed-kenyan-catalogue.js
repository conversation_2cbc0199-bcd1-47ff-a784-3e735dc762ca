const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  console.log('🇰🇪 Starting Kenyan market catalogue seeding...');

  try {
    // 1. Seed Units first (required by catalogue)
    console.log('📐 Creating units...');
    const units = [
      {
        name: 'piece',
        displayName: 'Piece',
        plural: 'Pieces',
        shortForm: 'pcs',
        category: 'quantity',
        active: true,
        order: 1
      },
      {
        name: 'square_meter',
        displayName: 'Square Meter',
        plural: 'Square Meters',
        shortForm: 'm²',
        category: 'area',
        active: true,
        order: 2
      },
      {
        name: 'meter',
        displayName: 'Meter',
        plural: 'Meters',
        shortForm: 'm',
        category: 'length',
        active: true,
        order: 3
      },
      {
        name: 'box',
        displayName: 'Box',
        plural: 'Boxes',
        shortForm: 'box',
        category: 'packaging',
        active: true,
        order: 4
      },
      {
        name: 'set',
        displayName: 'Set',
        plural: 'Sets',
        shortForm: 'set',
        category: 'group',
        active: true,
        order: 5
      },
      {
        name: 'roll',
        displayName: 'Roll',
        plural: 'Rolls',
        shortForm: 'roll',
        category: 'material',
        active: true,
        order: 6
      }
    ];

    const createdUnits = {};
    for (const unit of units) {
      try {
        const existingUnit = await prisma.unit.findUnique({
          where: { name: unit.name }
        });
        
        if (!existingUnit) {
          const createdUnit = await prisma.unit.create({ data: unit });
          createdUnits[unit.name] = createdUnit;
          console.log(`✅ Created unit: ${unit.displayName}`);
        } else {
          createdUnits[unit.name] = existingUnit;
          console.log(`⚠️ Unit ${unit.displayName} already exists`);
        }
      } catch (error) {
        console.log(`❌ Error creating unit ${unit.name}:`, error.message);
      }
    }

    // 2. Seed Paper Types (common in Kenyan market)
    console.log('📄 Creating paper types...');
    const paperTypes = [
      {
        name: 'Art Paper 128gsm',
        grammage: '128gsm',
        oneSidedPrice: 25.00,
        twoSidedPrice: 35.00,
        category: 'art_paper',
        active: true,
        order: 1
      },
      {
        name: 'Art Paper 157gsm',
        grammage: '157gsm',
        oneSidedPrice: 30.00,
        twoSidedPrice: 40.00,
        category: 'art_paper',
        active: true,
        order: 2
      },
      {
        name: 'Art Paper 200gsm',
        grammage: '200gsm',
        oneSidedPrice: 35.00,
        twoSidedPrice: 50.00,
        category: 'art_paper',
        active: true,
        order: 3
      },
      {
        name: 'Art Paper 250gsm',
        grammage: '250gsm',
        oneSidedPrice: 45.00,
        twoSidedPrice: 65.00,
        category: 'art_paper',
        active: true,
        order: 4
      },
      {
        name: 'Art Paper 300gsm',
        grammage: '300gsm',
        oneSidedPrice: 55.00,
        twoSidedPrice: 75.00,
        category: 'art_paper',
        active: true,
        order: 5
      },
      {
        name: 'Bond Paper 80gsm',
        grammage: '80gsm',
        oneSidedPrice: 8.00,
        twoSidedPrice: 12.00,
        category: 'bond_paper',
        active: true,
        order: 6
      },
      {
        name: 'Bond Paper 100gsm',
        grammage: '100gsm',
        oneSidedPrice: 12.00,
        twoSidedPrice: 18.00,
        category: 'bond_paper',
        active: true,
        order: 7
      },
      {
        name: 'Matte Paper 130gsm',
        grammage: '130gsm',
        oneSidedPrice: 28.00,
        twoSidedPrice: 38.00,
        category: 'matte_paper',
        active: true,
        order: 8
      },
      {
        name: 'Vinyl Sticker',
        grammage: 'Standard',
        oneSidedPrice: 180.00,
        twoSidedPrice: 180.00,
        category: 'vinyl',
        active: true,
        order: 9
      },
      {
        name: 'Canvas Material',
        grammage: 'Heavy Duty',
        oneSidedPrice: 450.00,
        twoSidedPrice: 450.00,
        category: 'canvas',
        active: true,
        order: 10
      }
    ];

    for (const paperType of paperTypes) {
      try {
        const existingPaper = await prisma.paperType.findFirst({
          where: { name: paperType.name }
        });
        
        if (!existingPaper) {
          await prisma.paperType.create({ data: paperType });
          console.log(`✅ Created paper type: ${paperType.name}`);
        } else {
          console.log(`⚠️ Paper type ${paperType.name} already exists`);
        }
      } catch (error) {
        console.log(`❌ Error creating paper type ${paperType.name}:`, error.message);
      }
    }

    // 3. Seed Pricing Rules
    console.log('💰 Creating pricing rules...');
    const pricingRules = [
      {
        name: 'Bulk Discount - 100+ pieces',
        type: 'quantity_discount',
        category: 'Business Cards',
        rules: {
          minQuantity: 100,
          maxQuantity: 499,
          discountType: 'percentage',
          discountValue: 10
        },
        active: true
      },
      {
        name: 'Bulk Discount - 500+ pieces',
        type: 'quantity_discount',
        category: 'Business Cards',
        rules: {
          minQuantity: 500,
          maxQuantity: 999,
          discountType: 'percentage',
          discountValue: 15
        },
        active: true
      },
      {
        name: 'Bulk Discount - 1000+ pieces',
        type: 'quantity_discount',
        category: 'Business Cards',
        rules: {
          minQuantity: 1000,
          discountType: 'percentage',
          discountValue: 20
        },
        active: true
      },
      {
        name: 'Large Format Pricing',
        type: 'area_pricing',
        category: 'Banners',
        rules: {
          basePrice: 450,
          pricePerSqm: 450,
          minArea: 1,
          maxArea: 100
        },
        active: true
      }
    ];

    for (const rule of pricingRules) {
      try {
        const existingRule = await prisma.pricingRule.findFirst({
          where: { name: rule.name }
        });
        
        if (!existingRule) {
          await prisma.pricingRule.create({ data: rule });
          console.log(`✅ Created pricing rule: ${rule.name}`);
        } else {
          console.log(`⚠️ Pricing rule ${rule.name} already exists`);
        }
      } catch (error) {
        console.log(`❌ Error creating pricing rule ${rule.name}:`, error.message);
      }
    }

    // 4. Seed Catalogue Items (Kenyan market focused)
    console.log('🛍️ Creating catalogue items...');
    const catalogueItems = [
      // Business Cards
      {
        service: 'Business Cards',
        description: 'Professional business cards - single or double sided printing',
        price: 8, // per piece for 100+ quantity
        designFee: 1500,
        category: 'Business Cards',
        features: ['Professional Design', 'Multiple Paper Options', 'Fast Delivery', 'Quality Print'],
        icon: '💼',
        popular: true,
        maxQuantity: 10000,
        minQuantity: 50,
        paperTypes: ['Art Paper 300gsm', 'Art Paper 250gsm', 'Matte Paper 130gsm'],
        pricingTiers: {
          '50-99': 12,
          '100-499': 8,
          '500-999': 6,
          '1000+': 5
        },
        pricingType: 'tiered',
        unitId: createdUnits.piece?.id
      },
      // Flyers & Brochures
      {
        service: 'A4 Flyers',
        description: 'Single page A4 flyers for marketing and promotions',
        price: 25,
        designFee: 2000,
        category: 'Marketing Materials',
        features: ['Eye-catching Design', 'High Quality Print', 'Various Paper Options'],
        icon: '📄',
        popular: true,
        maxQuantity: 5000,
        minQuantity: 50,
        paperTypes: ['Art Paper 128gsm', 'Art Paper 157gsm', 'Bond Paper 100gsm'],
        pricingTiers: {
          '50-99': 35,
          '100-499': 25,
          '500-999': 20,
          '1000+': 18
        },
        pricingType: 'tiered',
        unitId: createdUnits.piece?.id
      },
      {
        service: 'A5 Flyers',
        description: 'Compact A5 flyers perfect for handouts and promotions',
        price: 20,
        designFee: 1800,
        category: 'Marketing Materials',
        features: ['Compact Size', 'Cost Effective', 'Professional Design'],
        icon: '📄',
        popular: false,
        maxQuantity: 5000,
        minQuantity: 100,
        paperTypes: ['Art Paper 128gsm', 'Art Paper 157gsm'],
        pricingTiers: {
          '100-499': 20,
          '500-999': 16,
          '1000+': 14
        },
        pricingType: 'tiered',
        unitId: createdUnits.piece?.id
      },
      {
        service: 'Tri-fold Brochures',
        description: 'Professional tri-fold brochures for business marketing',
        price: 85,
        designFee: 3500,
        category: 'Marketing Materials',
        features: ['Professional Layout', 'Multiple Panels', 'Premium Finish'],
        icon: '📑',
        popular: true,
        maxQuantity: 2000,
        minQuantity: 50,
        paperTypes: ['Art Paper 157gsm', 'Art Paper 200gsm', 'Matte Paper 130gsm'],
        pricingTiers: {
          '50-99': 95,
          '100-499': 85,
          '500+': 75
        },
        pricingType: 'tiered',
        unitId: createdUnits.piece?.id
      },
      // Posters
      {
        service: 'A3 Posters',
        description: 'High quality A3 posters for advertising and decoration',
        price: 150,
        designFee: 2500,
        category: 'Posters',
        features: ['High Resolution Print', 'Vibrant Colors', 'Durable Material'],
        icon: '🖼️',
        popular: true,
        maxQuantity: 1000,
        minQuantity: 1,
        paperTypes: ['Art Paper 200gsm', 'Art Paper 250gsm', 'Canvas Material'],
        pricingTiers: {
          '1-9': 180,
          '10-49': 150,
          '50-99': 130,
          '100+': 120
        },
        pricingType: 'tiered',
        unitId: createdUnits.piece?.id
      },
      {
        service: 'A2 Posters',
        description: 'Large format A2 posters for impactful advertising',
        price: 280,
        designFee: 3000,
        category: 'Posters',
        features: ['Large Format', 'Professional Quality', 'Weather Resistant Options'],
        icon: '🖼️',
        popular: false,
        maxQuantity: 500,
        minQuantity: 1,
        paperTypes: ['Art Paper 200gsm', 'Art Paper 250gsm', 'Canvas Material'],
        pricingTiers: {
          '1-9': 320,
          '10-49': 280,
          '50+': 250
        },
        pricingType: 'tiered',
        unitId: createdUnits.piece?.id
      },
      {
        service: 'A1 Posters',
        description: 'Extra large A1 posters for maximum impact',
        price: 450,
        designFee: 3500,
        category: 'Posters',
        features: ['Extra Large Format', 'Museum Quality', 'Premium Materials'],
        icon: '🖼️',
        popular: false,
        maxQuantity: 200,
        minQuantity: 1,
        paperTypes: ['Art Paper 250gsm', 'Canvas Material'],
        pricingTiers: {
          '1-9': 500,
          '10-49': 450,
          '50+': 400
        },
        pricingType: 'tiered',
        unitId: createdUnits.piece?.id
      },
      // Banners & Large Format
      {
        service: 'PVC Banners',
        description: 'Durable outdoor PVC banners - price per square meter',
        price: 450,
        designFee: 4000,
        category: 'Banners',
        features: ['Weather Resistant', 'UV Protected', 'Reinforced Edges', 'Grommets Included'],
        icon: '🏗️',
        popular: true,
        maxMeters: 100.0,
        minMeters: 1.0,
        pricePerMeter: 450.0,
        pricingType: 'per_meter',
        unitId: createdUnits.square_meter?.id
      },
      {
        service: 'Vinyl Banners',
        description: 'Premium vinyl banners for indoor and outdoor use',
        price: 650,
        designFee: 4500,
        category: 'Banners',
        features: ['Premium Quality', 'Fade Resistant', 'Indoor/Outdoor Use', 'Custom Sizes'],
        icon: '🏗️',
        popular: false,
        maxMeters: 50.0,
        minMeters: 1.0,
        pricePerMeter: 650.0,
        pricingType: 'per_meter',
        unitId: createdUnits.square_meter?.id
      },
      {
        service: 'Mesh Banners',
        description: 'Wind-resistant mesh banners perfect for outdoor advertising',
        price: 550,
        designFee: 4000,
        category: 'Banners',
        features: ['Wind Resistant', 'Perforated Material', 'Outdoor Durable', 'Easy Installation'],
        icon: '🌪️',
        popular: false,
        maxMeters: 75.0,
        minMeters: 2.0,
        pricePerMeter: 550.0,
        pricingType: 'per_meter',
        unitId: createdUnits.square_meter?.id
      },
      // Stickers & Labels
      {
        service: 'Vinyl Stickers',
        description: 'Custom vinyl stickers - waterproof and durable',
        price: 25,
        designFee: 1500,
        category: 'Stickers & Labels',
        features: ['Waterproof', 'UV Resistant', 'Custom Shapes', 'Strong Adhesive'],
        icon: '🏷️',
        popular: true,
        maxQuantity: 10000,
        minQuantity: 50,
        pricingTiers: {
          '50-99': 30,
          '100-499': 25,
          '500-999': 20,
          '1000+': 15
        },
        pricingType: 'tiered',
        unitId: createdUnits.piece?.id
      },
      {
        service: 'Paper Stickers',
        description: 'Cost-effective paper stickers for indoor use',
        price: 15,
        designFee: 1200,
        category: 'Stickers & Labels',
        features: ['Cost Effective', 'Custom Shapes', 'Various Sizes', 'Quick Turnaround'],
        icon: '🏷️',
        popular: true,
        maxQuantity: 15000,
        minQuantity: 100,
        pricingTiers: {
          '100-499': 18,
          '500-999': 15,
          '1000-2999': 12,
          '3000+': 10
        },
        pricingType: 'tiered',
        unitId: createdUnits.piece?.id
      },
      // Letterheads & Documents
      {
        service: 'Company Letterheads',
        description: 'Professional letterheads with company branding',
        price: 35,
        designFee: 2500,
        category: 'Corporate Stationery',
        features: ['Professional Design', 'Company Branding', 'High Quality Paper'],
        icon: '📄',
        popular: true,
        maxQuantity: 2000,
        minQuantity: 50,
        paperTypes: ['Bond Paper 100gsm', 'Art Paper 128gsm'],
        pricingTiers: {
          '50-99': 40,
          '100-499': 35,
          '500+': 30
        },
        pricingType: 'tiered',
        unitId: createdUnits.piece?.id
      },
      {
        service: 'Compliment Slips',
        description: 'Professional compliment slips for business correspondence',
        price: 25,
        designFee: 1800,
        category: 'Corporate Stationery',
        features: ['Professional Layout', 'Company Details', 'Quality Paper'],
        icon: '📄',
        popular: false,
        maxQuantity: 2000,
        minQuantity: 100,
        paperTypes: ['Bond Paper 100gsm', 'Art Paper 128gsm'],
        pricingTiers: {
          '100-499': 25,
          '500-999': 22,
          '1000+': 20
        },
        pricingType: 'tiered',
        unitId: createdUnits.piece?.id
      },
      // Certificates & Awards
      {
        service: 'Certificates',
        description: 'Professional certificates for awards and achievements',
        price: 180,
        designFee: 3000,
        category: 'Certificates',
        features: ['Premium Paper', 'Professional Design', 'Customizable Layout'],
        icon: '🏆',
        popular: true,
        maxQuantity: 500,
        minQuantity: 1,
        paperTypes: ['Art Paper 250gsm', 'Art Paper 300gsm'],
        pricingTiers: {
          '1-9': 200,
          '10-49': 180,
          '50+': 160
        },
        pricingType: 'tiered',
        unitId: createdUnits.piece?.id
      },
      // Wedding & Events
      {
        service: 'Wedding Invitations',
        description: 'Elegant wedding invitations with custom design',
        price: 120,
        designFee: 5000,
        category: 'Wedding & Events',
        features: ['Elegant Design', 'Premium Paper', 'Custom Layout', 'Multiple Finishes'],
        icon: '💒',
        popular: true,
        maxQuantity: 1000,
        minQuantity: 50,
        paperTypes: ['Art Paper 250gsm', 'Art Paper 300gsm'],
        pricingTiers: {
          '50-99': 130,
          '100-299': 120,
          '300+': 110
        },
        pricingType: 'tiered',
        unitId: createdUnits.piece?.id
      },
      {
        service: 'Event Programs',
        description: 'Professional event programs and booklets',
        price: 80,
        designFee: 3500,
        category: 'Wedding & Events',
        features: ['Professional Layout', 'Multiple Pages', 'Quality Binding'],
        icon: '📖',
        popular: false,
        maxQuantity: 1000,
        minQuantity: 50,
        paperTypes: ['Art Paper 157gsm', 'Art Paper 200gsm'],
        pricingTiers: {
          '50-99': 90,
          '100-299': 80,
          '300+': 70
        },
        pricingType: 'tiered',
        unitId: createdUnits.piece?.id
      },
      // T-Shirts & Apparel
      {
        service: 'T-Shirt Printing',
        description: 'Custom t-shirt printing with various design options',
        price: 650,
        designFee: 2000,
        category: 'Apparel',
        features: ['High Quality Fabric', 'Durable Print', 'Various Colors', 'Custom Designs'],
        icon: '👕',
        popular: true,
        maxQuantity: 500,
        minQuantity: 5,
        pricingTiers: {
          '5-19': 750,
          '20-49': 650,
          '50-99': 550,
          '100+': 500
        },
        pricingType: 'tiered',
        unitId: createdUnits.piece?.id
      },
      // Mugs & Gifts
      {
        service: 'Custom Mugs',
        description: 'Personalized ceramic mugs with custom designs',
        price: 450,
        designFee: 1500,
        category: 'Gifts & Merchandise',
        features: ['Ceramic Material', 'Dishwasher Safe', 'Custom Design', 'Gift Packaging'],
        icon: '☕',
        popular: true,
        maxQuantity: 200,
        minQuantity: 1,
        pricingTiers: {
          '1-9': 500,
          '10-24': 450,
          '25-49': 400,
          '50+': 350
        },
        pricingType: 'tiered',
        unitId: createdUnits.piece?.id
      }
    ];

    for (const item of catalogueItems) {
      try {
        const existingItem = await prisma.catalogue.findFirst({
          where: { service: item.service }
        });
        
        if (!existingItem) {
          await prisma.catalogue.create({ data: item });
          console.log(`✅ Created catalogue item: ${item.service}`);
        } else {
          console.log(`⚠️ Catalogue item ${item.service} already exists`);
        }
      } catch (error) {
        console.log(`❌ Error creating catalogue item ${item.service}:`, error.message);
      }
    }

    console.log('🎉 Kenyan market catalogue seeding completed successfully!');
    console.log(`📊 Summary:`);
    console.log(`   - Units: ${units.length} items`);
    console.log(`   - Paper Types: ${paperTypes.length} items`);
    console.log(`   - Pricing Rules: ${pricingRules.length} items`);
    console.log(`   - Catalogue Items: ${catalogueItems.length} items`);

  } catch (error) {
    console.error('❌ Error during seeding:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  main()
    .catch((error) => {
      console.error('❌ Seeding failed:', error);
      process.exit(1);
    });
}

module.exports = { main }; 