#!/bin/bash

# Git Conflict Resolution Script for Mocky Digital
# This script helps resolve branch divergence and merge conflicts safely
# Usage: ./scripts/resolve-git-conflicts.sh [strategy]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log() {
    echo -e "${BLUE}[$(date '+%H:%M:%S')]${NC} $1"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show current git status
show_status() {
    log "Current Git Status:"
    git status --porcelain
    echo ""
    git log --oneline -5
    echo ""
}

# Function to backup current changes
backup_changes() {
    local backup_branch="backup-$(date +%Y%m%d-%H%M%S)"
    log "Creating backup branch: $backup_branch"
    git checkout -b "$backup_branch"
    git checkout main
    success "Backup created at branch: $backup_branch"
}

# Function to handle diverged branches
resolve_divergence() {
    log "Detecting branch divergence..."
    
    # Check if remote exists
    if ! git remote get-url origin >/dev/null 2>&1; then
        warning "No remote 'origin' found. This might be a local-only repository."
        return 0
    fi
    
    # Fetch latest from remote
    log "Fetching latest changes from remote..."
    git fetch origin || {
        warning "Could not fetch from remote. Continuing with local resolution."
        return 0
    }
    
    # Check divergence
    local ahead=$(git rev-list --count origin/main..HEAD 2>/dev/null || echo "0")
    local behind=$(git rev-list --count HEAD..origin/main 2>/dev/null || echo "0")
    
    log "Local commits ahead: $ahead"
    log "Remote commits behind: $behind"
    
    if [ "$ahead" -gt 0 ] && [ "$behind" -gt 0 ]; then
        warning "Branch has diverged!"
        echo ""
        echo "You have 3 options:"
        echo "1. Rebase your changes onto remote (recommended for clean history)"
        echo "2. Merge remote changes (creates merge commit)"
        echo "3. Reset to remote and apply changes manually (safe but manual)"
        echo ""
        
        if [ "$1" = "--auto" ]; then
            log "Auto mode: Using rebase strategy"
            resolve_with_rebase
        else
            read -p "Choose option (1-3): " choice
            case $choice in
                1) resolve_with_rebase ;;
                2) resolve_with_merge ;;
                3) resolve_with_reset ;;
                *) error "Invalid choice"; exit 1 ;;
            esac
        fi
    elif [ "$ahead" -gt 0 ]; then
        log "Local branch is ahead. Pushing changes..."
        git push origin main || warning "Could not push to remote"
    elif [ "$behind" -gt 0 ]; then
        log "Local branch is behind. Pulling changes..."
        git pull origin main
    else
        success "Branches are in sync!"
    fi
}

# Resolve using rebase (recommended)
resolve_with_rebase() {
    log "Resolving conflicts using rebase..."
    
    # Create backup first
    backup_changes
    
    # Attempt rebase
    if git rebase origin/main; then
        success "Rebase completed successfully!"
        git push origin main --force-with-lease || warning "Could not push rebased changes"
    else
        warning "Rebase conflicts detected. Manual resolution needed."
        echo ""
        echo "To resolve conflicts:"
        echo "1. Edit conflicted files (shown below)"
        echo "2. Stage resolved files: git add <file>"
        echo "3. Continue rebase: git rebase --continue"
        echo "4. Or abort rebase: git rebase --abort"
        echo ""
        
        # Show conflicted files
        git status --porcelain | grep "^UU\|^AA\|^DD" || echo "No conflict markers found"
        
        # Offer to abort
        read -p "Abort rebase and try merge instead? (y/n): " abort_choice
        if [ "$abort_choice" = "y" ]; then
            git rebase --abort
            resolve_with_merge
        fi
    fi
}

# Resolve using merge
resolve_with_merge() {
    log "Resolving conflicts using merge..."
    
    # Create backup first
    backup_changes
    
    # Attempt merge
    if git merge origin/main; then
        success "Merge completed successfully!"
        git push origin main || warning "Could not push merged changes"
    else
        warning "Merge conflicts detected. Manual resolution needed."
        echo ""
        echo "To resolve conflicts:"
        echo "1. Edit conflicted files (shown below)"
        echo "2. Stage resolved files: git add <file>"
        echo "3. Complete merge: git commit"
        echo "4. Or abort merge: git merge --abort"
        echo ""
        
        # Show conflicted files
        git status --porcelain | grep "^UU\|^AA\|^DD" || echo "No conflict markers found"
    fi
}

# Resolve by resetting to remote
resolve_with_reset() {
    log "Resolving by resetting to remote..."
    
    # Create backup first
    backup_changes
    
    warning "This will reset your local main branch to match remote"
    read -p "Are you sure? Your changes are backed up. (y/n): " confirm
    
    if [ "$confirm" = "y" ]; then
        git reset --hard origin/main
        success "Reset to remote completed!"
        echo ""
        echo "Your previous changes are available in the backup branch."
        echo "To apply specific changes, use: git cherry-pick <commit-hash>"
    else
        log "Reset cancelled"
    fi
}

# Function to resolve file conflicts interactively
resolve_file_conflicts() {
    log "Checking for file conflicts..."
    
    # Look for conflict markers
    local conflict_files=$(git diff --name-only --diff-filter=U 2>/dev/null || echo "")
    
    if [ -n "$conflict_files" ]; then
        warning "Found conflicts in files:"
        echo "$conflict_files"
        echo ""
        
        for file in $conflict_files; do
            echo "Conflict in: $file"
            echo "Options:"
            echo "1. Edit manually"
            echo "2. Take local version"
            echo "3. Take remote version"
            echo "4. Skip this file"
            
            read -p "Choose option (1-4): " file_choice
            case $file_choice in
                1) 
                    echo "Opening $file for manual editing..."
                    echo "Look for conflict markers: <<<<<<< ======= >>>>>>>"
                    ;;
                2) 
                    git checkout --ours "$file"
                    git add "$file"
                    success "Kept local version of $file"
                    ;;
                3) 
                    git checkout --theirs "$file"
                    git add "$file"
                    success "Kept remote version of $file"
                    ;;
                4) 
                    log "Skipping $file"
                    ;;
            esac
        done
    else
        success "No file conflicts found!"
    fi
}

# Function to clean up git state
cleanup_git_state() {
    log "Cleaning up git state..."
    
    # Remove untracked files (with confirmation)
    local untracked=$(git ls-files --others --exclude-standard)
    if [ -n "$untracked" ]; then
        echo "Untracked files found:"
        echo "$untracked"
        read -p "Remove untracked files? (y/n): " clean_choice
        if [ "$clean_choice" = "y" ]; then
            git clean -fd
            success "Untracked files removed"
        fi
    fi
    
    # Reset any staged changes
    if git diff --cached --quiet; then
        log "No staged changes to reset"
    else
        warning "Staged changes found"
        read -p "Reset staged changes? (y/n): " reset_choice
        if [ "$reset_choice" = "y" ]; then
            git reset HEAD
            success "Staged changes reset"
        fi
    fi
}

# Main function
main() {
    log "Git Conflict Resolution Tool"
    echo "=============================="
    echo ""
    
    # Check if we're in a git repository
    if ! git rev-parse --git-dir >/dev/null 2>&1; then
        error "Not in a git repository"
        exit 1
    fi
    
    # Show current status
    show_status
    
    # Handle different scenarios
    if git merge-tree $(git merge-base HEAD origin/main) HEAD origin/main | grep -q "^"; then
        log "Detected branch divergence"
        resolve_divergence "$1"
    elif [ -f .git/MERGE_HEAD ]; then
        log "Detected active merge"
        resolve_file_conflicts
    elif [ -f .git/rebase-merge/head-name ]; then
        log "Detected active rebase"
        resolve_file_conflicts
    else
        log "Checking for general conflicts..."
        resolve_file_conflicts
        cleanup_git_state
    fi
    
    echo ""
    success "Git conflict resolution completed!"
    log "Final status:"
    show_status
}

# Handle script arguments
case "$1" in
    --help|-h)
        echo "Git Conflict Resolution Script"
        echo ""
        echo "Usage: $0 [options]"
        echo ""
        echo "Options:"
        echo "  --auto        Use automatic resolution (rebase strategy)"
        echo "  --help, -h    Show this help message"
        echo ""
        echo "This script helps resolve:"
        echo "  - Branch divergence"
        echo "  - Merge conflicts"
        echo "  - Rebase conflicts"
        echo "  - Git state cleanup"
        exit 0
        ;;
    *)
        main "$1"
        ;;
esac 