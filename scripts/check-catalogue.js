#!/usr/bin/env node

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function checkCatalogue() {
  try {
    console.log('🔍 CATALOGUE TABLE STATUS\n');
    
    // Get all catalogue items
    const allItems = await prisma.catalogue.findMany({
      orderBy: [
        { category: 'asc' },
        { price: 'desc' }
      ]
    });
    
    console.log(`📊 Total catalogue items: ${allItems.length}`);
    
    if (allItems.length > 0) {
      console.log('\n📋 CATALOGUE INVENTORY:');
      console.log('='.repeat(100));
      
      let currentCategory = '';
      allItems.forEach((item, index) => {
        if (item.category !== currentCategory) {
          currentCategory = item.category;
          console.log(`\n🏷️  ${currentCategory.toUpperCase()} (${allItems.filter(i => i.category === currentCategory).length} items)`);
          console.log('-'.repeat(80));
        }
        
        const popularMark = item.popular ? '⭐' : '  ';
        console.log(`${popularMark} ${item.service}`);
        console.log(`     💰 KES ${item.price.toLocaleString()}`);
        console.log(`     📝 ${item.description ? item.description.substring(0, 80) + '...' : 'No description'}`);
        console.log(`     🔧 Features: ${item.features ? item.features.length : 0} items`);
        console.log(`     🆔 ID: ${item.id}`);
        console.log('');
      });
      
      // Statistics
      const categories = [...new Set(allItems.map(item => item.category))];
      const popularCount = allItems.filter(item => item.popular).length;
      const withImages = allItems.filter(item => item.imageUrl).length;
      
      console.log('\n📈 STATISTICS:');
      console.log(`   📦 Categories: ${categories.length} (${categories.join(', ')})`);
      console.log(`   ⭐ Popular items: ${popularCount}`);
      console.log(`   🖼️  Items with images: ${withImages}`);
      console.log(`   💰 Price range: KES ${Math.min(...allItems.map(i => i.price)).toLocaleString()} - KES ${Math.max(...allItems.map(i => i.price)).toLocaleString()}`);
      console.log(`   📊 Average price: KES ${Math.round(allItems.reduce((sum, item) => sum + item.price, 0) / allItems.length).toLocaleString()}`);
    }
    
  } catch (error) {
    console.error('❌ Error checking catalogue:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkCatalogue(); 