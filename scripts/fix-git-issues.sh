#!/bin/bash

echo "🔧 Fixing Git Issues..."
echo "📍 Working in: /var/www/mocky"

cd /var/www/mocky

echo ""
echo "📊 Current git status:"
git status

echo ""
echo "🔍 Current branch info:"
git branch -vv

echo ""
echo "🔧 Applying fix..."

# The main branch reference has been updated to match remote
# Now we need to reset the working tree and index

echo "1️⃣ Resetting working tree to match HEAD..."
git reset --hard HEAD

echo "2️⃣ Cleaning untracked files..."
git clean -fd

echo ""
echo "✅ Git issues fixed!"
echo ""
echo "📊 Final status:"
git status

echo ""
echo "🔗 Final branch info:"
git branch -vv

echo ""
echo "📝 Recent commits:"
git log --oneline -3 