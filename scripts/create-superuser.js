const { PrismaClient } = require('@prisma/client');
const bcrypt = require('bcryptjs');
const { v4: uuidv4 } = require('uuid');

const prisma = new PrismaClient();

async function createSuperuser() {
  try {
    console.log('🚀 MOCKY DIGITAL SUPERUSER CREATION');
    console.log('═'.repeat(50));
    
    // Get credentials from environment or prompts
    const username = process.env.ADMIN_USERNAME || 'admin';
    const email = process.env.ADMIN_EMAIL || '<EMAIL>';
    const password = process.env.ADMIN_PASSWORD || 'Jack75522r'; // Default for development
    const name = process.env.ADMIN_NAME || 'System Administrator';
    
    console.log('\n📋 SUPERUSER DETAILS:');
    console.log(`👤 Username: ${username}`);
    console.log(`📧 Email: ${email}`);
    console.log(`👨‍💼 Name: ${name}`);
    console.log(`🔑 Password: ${'*'.repeat(password.length)} characters`);
    
    // Step 1: Ensure admin role exists
    console.log('\n1️⃣ CREATING/UPDATING ADMIN ROLE');
    console.log('─'.repeat(30));
    
    const adminRole = await prisma.role.upsert({
      where: { name: 'admin' },
      update: {
        description: 'System Administrator with full access',
        permissions: ['*'], // Superuser permissions
      },
      create: {
        id: uuidv4(),
        name: 'admin',
        description: 'System Administrator with full access',
        permissions: ['*'],
      },
    });
    
    console.log(`✅ Admin role ready: ${adminRole.id}`);
    console.log(`🛡️ Permissions: ${JSON.stringify(adminRole.permissions)}`);
    
    // Step 2: Hash password securely
    console.log('\n2️⃣ SECURING PASSWORD');
    console.log('─'.repeat(30));
    
    const passwordHash = await bcrypt.hash(password, 12);
    console.log('✅ Password hashed with bcrypt (12 rounds)');
    
    // Step 3: Create/update superuser
    console.log('\n3️⃣ CREATING/UPDATING SUPERUSER');
    console.log('─'.repeat(30));
    
    // Check if user exists by username or email
    const existingUser = await prisma.user.findFirst({
      where: {
        OR: [
          { username: username },
          { email: email }
        ]
      },
      include: { role: true }
    });
    
    let superuser;
    
    if (existingUser) {
      console.log(`⚠️ User exists: ${existingUser.username} (${existingUser.email})`);
      console.log('🔄 Updating existing user...');
      
      superuser = await prisma.user.update({
        where: { id: existingUser.id },
        data: {
          username: username,
          email: email,
          name: name,
          passwordHash: passwordHash,
          roleId: adminRole.id,
          active: true,
          updatedAt: new Date(),
        },
        include: { role: true }
      });
      
      console.log('✅ Existing user updated to superuser');
    } else {
      console.log('🆕 Creating new superuser...');
      
      superuser = await prisma.user.create({
        data: {
          id: uuidv4(),
          username: username,
          email: email,
          name: name,
          passwordHash: passwordHash,
          roleId: adminRole.id,
          active: true,
        },
        include: { role: true }
      });
      
      console.log('✅ New superuser created');
    }
    
    // Step 4: Verify password
    console.log('\n4️⃣ VERIFYING PASSWORD');
    console.log('─'.repeat(30));
    
    const passwordTest = await bcrypt.compare(password, superuser.passwordHash);
    if (passwordTest) {
      console.log('✅ Password verification: SUCCESS');
    } else {
      console.log('❌ Password verification: FAILED');
      throw new Error('Password verification failed!');
    }
    
    // Step 5: Test database connection
    console.log('\n5️⃣ TESTING DATABASE CONNECTION');
    console.log('─'.repeat(30));
    
    const userCount = await prisma.user.count();
    const roleCount = await prisma.role.count();
    
    console.log(`✅ Database connection: OK`);
    console.log(`👥 Total users: ${userCount}`);
    console.log(`🎭 Total roles: ${roleCount}`);
    
    // Step 6: Create activity log entry
    console.log('\n6️⃣ LOGGING ACTIVITY');
    console.log('─'.repeat(30));
    
    try {
      await prisma.activityLog.create({
        data: {
          id: uuidv4(),
          userId: superuser.id,
          action: 'superuser_created',
          details: 'Superuser account created/updated via script',
          ipAddress: 'localhost',
          userAgent: 'create-superuser-script',
          createdAt: new Date(),
        }
      });
      console.log('✅ Activity logged successfully');
    } catch (logError) {
      console.log('⚠️ Activity logging skipped (non-critical)');
    }
    
    // Success summary
    console.log('\n🎉 SUPERUSER CREATION COMPLETE!');
    console.log('═'.repeat(50));
    console.log('📊 SUMMARY:');
    console.log(`   ID: ${superuser.id}`);
    console.log(`   Username: ${superuser.username}`);
    console.log(`   Email: ${superuser.email}`);
    console.log(`   Name: ${superuser.name}`);
    console.log(`   Role: ${superuser.role.name}`);
    console.log(`   Active: ${superuser.active}`);
    console.log(`   Permissions: ${JSON.stringify(superuser.role.permissions)}`);
    
    console.log('\n🌐 LOGIN INFORMATION:');
    console.log('═'.repeat(50));
    console.log('🔗 URL: https://mocky.co.ke/admin/login');
    console.log(`👤 Username: ${username}`);
    console.log(`🔑 Password: ${password}`);
    console.log('═'.repeat(50));
    
    return superuser;
    
  } catch (error) {
    console.error('\n❌ SUPERUSER CREATION FAILED!');
    console.error('═'.repeat(50));
    console.error('Error:', error.message);
    
    if (error.code) {
      console.error('Code:', error.code);
    }
    
    if (error.meta) {
      console.error('Details:', error.meta);
    }
    
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the script
if (require.main === module) {
  createSuperuser()
    .then(() => {
      console.log('\n✅ Script completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Script failed:', error.message);
      process.exit(1);
    });
}

module.exports = { createSuperuser }; 