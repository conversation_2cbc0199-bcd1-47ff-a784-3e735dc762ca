import { PrismaClient } from '@prisma/client';
import dotenv from 'dotenv';

dotenv.config();

const prisma = new PrismaClient();

// Website portfolio data with real websites from provided search results
const websitePortfolioData = [
  {
    title: 'SGTC Company',
    description: 'Leading provider of facility management, construction, logistics, and waste management services across Somalia. Partners with UN organizations and delivers excellence in infrastructure development.',
    category: 'construction',
    url: 'https://sgtccompany.com/',
    imageKey: 'portfolio/sgtc-company.jpg',
    featured: true,
  },
  {
    title: 'Homestore Kenya',
    description: 'Premium online store for household items, kitchen accessories, home decor, and electronics. Offering up to 70% discounts with countrywide delivery across Kenya.',
    category: 'e-commerce',
    url: 'https://homestore.co.ke/',
    imageKey: 'portfolio/homestore-kenya.jpg',
    featured: true,
  },
  {
    title: 'Knowledge Spring Training Institute',
    description: 'Private institution providing technical, vocational, and entrepreneurial training. Accredited by TVETA with multiple schools including Health Sciences, Business Studies, and Information Technology.',
    category: 'education',
    url: 'https://knowledgespringinstitute.ac.ke/',
    imageKey: 'portfolio/knowledge-spring.jpg',
    featured: true,
  },
  {
    title: 'Excalibur Dealers Limited',
    description: 'Premium electronics retailer specializing in smartphones, laptops, cameras, gaming equipment, and tech accessories. Offering authentic products with nationwide shipping.',
    category: 'e-commerce',
    url: 'https://excaliburdealerslimited.com/',
    imageKey: 'portfolio/excalibur-dealers.jpg',
    featured: true,
  },
  {
    title: 'C.O Wasonga & Co. Advocates',
    description: 'Reputable and dynamic law firm committed to delivering comprehensive legal services to individuals, businesses, and organizations. Specializing in corporate law, employment relations, constitutional litigation, and conveyancing with a focus on integrity and excellence.',
    category: 'legal',
    url: 'https://wasongalaw.co.ke/',
    imageKey: 'portfolio/wasonga-law.jpg',
    featured: true,
  },
  {
    title: 'MRL Motors - Mombasa Rickshaw LTD',
    description: 'Leading supplier of high-quality motorcycles and tuk-tuks in Kenya since 2019. Providing durable, cost-effective mobility solutions with flexible payment plans and expert technical support for entrepreneurs and businesses.',
    category: 'automotive',
    url: 'https://mrlmotors.co.ke/',
    imageKey: 'portfolio/mrl-motors.jpg',
    featured: true,
  },
  {
    title: 'Institute For Food Systems & Climate (i4Food)',
    description: 'IFCA bridges the gap between innovative research and practical solutions, fostering sustainable food systems that adapt to climate change challenges. Focus on integrated research, community-centered solutions, and policy advocacy.',
    category: 'nonprofit',
    url: 'https://i4food.org/',
    imageKey: 'portfolio/i4food.jpg',
    featured: true,
  },
  {
    title: 'Top23Security',
    description: 'Professional security services and solutions provider focusing on comprehensive security management and protection services.',
    category: 'security',
    url: 'https://top23security.com/',
    imageKey: 'portfolio/top23security.jpg',
    featured: false,
  },
  {
    title: 'Pure Gift Organization',
    description: 'Non-profit organization focused on community development and charitable initiatives, working to make a positive impact in local communities.',
    category: 'nonprofit',
    url: 'https://pure-gift.org/',
    imageKey: 'portfolio/pure-gift.jpg',
    featured: false,
  },
  {
    title: 'Reucher Africa Kenya Ltd',
    description: 'Industrial Chemicals & Water Treatment Solutions Provider',
    category: 'corporate',
    url: 'https://reucherafricakenyaltd.co.ke/',
    imageKey: 'portfolio/reucher-africa.jpg',
    featured: false,
  },
  {
    title: 'Wezesha Kenya',
    description: 'Empowerment & Development Organization',
    category: 'nonprofit',
    url: 'https://wezeshakenya.com/',
    imageKey: 'portfolio/wezesha-kenya.jpg',
    featured: false,
  },
];

async function main() {
  try {
    console.log('🚀 Starting Website Portfolio seeding...');
    console.log('📦 Upserting website portfolio data (update existing, create new)...');
    
    let updatedCount = 0;
    let createdCount = 0;
    
    for (const item of websitePortfolioData) {
      try {
        // Check if the website already exists
        const existingWebsite = await prisma.websitePortfolio.findFirst({
          where: { url: item.url }
        });

        if (existingWebsite) {
          // Update existing record
          await prisma.websitePortfolio.update({
            where: { id: existingWebsite.id },
            data: {
              title: item.title,
              description: item.description,
              category: item.category,
              featured: item.featured,
              imageKey: item.imageKey,
              updatedAt: new Date()
            }
          });
          updatedCount++;
          console.log(`✅ Updated: ${item.title} - ${item.category}`);
        } else {
          // Create new record
          await prisma.websitePortfolio.create({ data: item });
          createdCount++;
          console.log(`✅ Created: ${item.title} - ${item.category}`);
        }
      } catch (error: any) {
        console.log(`⚠️ Error processing ${item.title}:`, error.message);
      }
    }
    
    const totalCount = await prisma.websitePortfolio.count();
    console.log('\n🎉 Website Portfolio seeding completed successfully!');
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    console.log(`📦 Total items in database: ${totalCount}`);
    console.log(`📝 Records updated: ${updatedCount}`);
    console.log(`🆕 Records created: ${createdCount}`);
    
    // Count by category
    const categories = await prisma.websitePortfolio.groupBy({
      by: ['category'],
      _count: {
        category: true
      },
      orderBy: {
        category: 'asc'
      }
    });
    
    console.log('\n📊 Items by category:');
    categories.forEach(cat => {
      console.log(`   ${cat.category}: ${cat._count.category} items`);
    });
    
    const featuredCount = await prisma.websitePortfolio.count({
      where: { featured: true }
    });
    console.log(`\n⭐ Featured items: ${featuredCount}`);
    console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    
  } catch (error) {
    console.error('❌ Error seeding website portfolio:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

main();
