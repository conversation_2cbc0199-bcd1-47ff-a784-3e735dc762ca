const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function main() {
  console.log('🇰🇪 Starting Enhanced Kenyan market catalogue seeding (PrintShop.co.ke data)...');

  try {
    // Get existing units
    const units = await prisma.unit.findMany();
    const unitsMap = {};
    units.forEach(unit => {
      unitsMap[unit.name] = unit;
    });

    // Enhanced Catalogue Items based on PrintShop Kenya pricing
    console.log('🛍️ Creating enhanced catalogue items with real market pricing...');
    const enhancedCatalogueItems = [
      // Business Cards (Updated pricing from PrintShop)
      {
        service: 'Premium Business Cards',
        description: 'High-quality business cards with professional finishing',
        price: 14, // From PrintShop: KSh 14 per card
        designFee: 1500,
        category: 'Business Cards',
        features: ['Premium Quality', 'UV Coating', 'Multiple Paper Options', 'Fast Delivery'],
        icon: '💼',
        popular: true,
        maxQuantity: 10000,
        minQuantity: 50,
        paperTypes: ['Art Paper 300gsm', 'Art Paper 250gsm'],
        pricingTiers: {
          '50-99': 18,
          '100-499': 14,
          '500-999': 12,
          '1000+': 10
        },
        pricingType: 'tiered',
        unitId: unitsMap.piece?.id
      },
      {
        service: 'Folded Business Cards',
        description: 'Creative folded business cards for unique branding',
        price: 20, // From PrintShop: KSh 20 per piece
        designFee: 2000,
        category: 'Business Cards',
        features: ['Unique Design', 'More Space', 'Premium Finish'],
        icon: '💼',
        popular: false,
        maxQuantity: 5000,
        minQuantity: 50,
        paperTypes: ['Art Paper 250gsm', 'Art Paper 300gsm'],
        pricingTiers: {
          '50-99': 25,
          '100-499': 20,
          '500+': 18
        },
        pricingType: 'tiered',
        unitId: unitsMap.piece?.id
      },

      // Flyers (Updated pricing)
      {
        service: 'A5 Flyers Premium',
        description: 'Professional A5 flyers with vibrant colors',
        price: 15, // From PrintShop: KSh 15 per flyer
        designFee: 1800,
        category: 'Marketing Materials',
        features: ['Vibrant Colors', 'Quality Paper', 'Fast Turnaround'],
        icon: '📄',
        popular: true,
        maxQuantity: 5000,
        minQuantity: 100,
        paperTypes: ['Art Paper 128gsm', 'Art Paper 157gsm'],
        pricingTiers: {
          '100-499': 15,
          '500-999': 12,
          '1000+': 10
        },
        pricingType: 'tiered',
        unitId: unitsMap.piece?.id
      },
      {
        service: 'A6 Flyers',
        description: 'Compact A6 flyers perfect for pocket-sized promotions',
        price: 12,
        designFee: 1500,
        category: 'Marketing Materials',
        features: ['Pocket Size', 'Cost Effective', 'Eye-catching'],
        icon: '📄',
        popular: false,
        maxQuantity: 10000,
        minQuantity: 200,
        paperTypes: ['Art Paper 128gsm', 'Bond Paper 100gsm'],
        pricingTiers: {
          '200-499': 12,
          '500-999': 10,
          '1000+': 8
        },
        pricingType: 'tiered',
        unitId: unitsMap.piece?.id
      },

      // Calendars
      {
        service: '2025 Wall Calendars',
        description: 'Professional wall calendars for the year 2025',
        price: 90, // From PrintShop: KSh 90 per piece
        designFee: 3000,
        category: 'Calendars',
        features: ['Full Year Display', 'High Quality Print', 'Durable Paper'],
        icon: '📅',
        popular: true,
        maxQuantity: 1000,
        minQuantity: 10,
        paperTypes: ['Art Paper 200gsm', 'Art Paper 250gsm'],
        pricingTiers: {
          '10-49': 100,
          '50-99': 90,
          '100+': 80
        },
        pricingType: 'tiered',
        unitId: unitsMap.piece?.id
      },
      {
        service: 'Desk Calendars',
        description: 'Compact desk calendars for office use',
        price: 120,
        designFee: 2500,
        category: 'Calendars',
        features: ['Desk Size', 'Professional Look', 'Monthly View'],
        icon: '📅',
        popular: false,
        maxQuantity: 500,
        minQuantity: 20,
        paperTypes: ['Art Paper 200gsm'],
        pricingTiers: {
          '20-49': 130,
          '50-99': 120,
          '100+': 110
        },
        pricingType: 'tiered',
        unitId: unitsMap.piece?.id
      },

      // Banners & Display Items
      {
        service: 'Roll-up Banners',
        description: 'Professional retractable roll-up banners for events',
        price: 6000, // From PrintShop: KSh 6,000 per piece
        designFee: 4000,
        category: 'Banners',
        features: ['Retractable', 'Portable', 'High Resolution Print', 'Includes Stand'],
        icon: '🎯',
        popular: true,
        maxQuantity: 50,
        minQuantity: 1,
        pricingTiers: {
          '1-4': 6500,
          '5-9': 6000,
          '10+': 5500
        },
        pricingType: 'tiered',
        unitId: unitsMap.piece?.id
      },
      {
        service: 'X-Banner Stands',
        description: 'Portable X-banner stands for quick setup displays',
        price: 5800, // From PrintShop: KSh 5800 per piece
        designFee: 3500,
        category: 'Banners',
        features: ['Quick Setup', 'Lightweight', 'Portable', 'Durable Frame'],
        icon: '🎯',
        popular: false,
        maxQuantity: 20,
        minQuantity: 1,
        pricingTiers: {
          '1-4': 6000,
          '5-9': 5800,
          '10+': 5500
        },
        pricingType: 'tiered',
        unitId: unitsMap.piece?.id
      },
      {
        service: 'Tear Drop Banners',
        description: 'Eye-catching teardrop banners for outdoor events',
        price: 4500,
        designFee: 3000,
        category: 'Banners',
        features: ['Wind Resistant', 'Unique Shape', 'Outdoor Use', 'Ground Spike Included'],
        icon: '💧',
        popular: true,
        maxQuantity: 30,
        minQuantity: 1,
        pricingTiers: {
          '1-4': 4800,
          '5-9': 4500,
          '10+': 4200
        },
        pricingType: 'tiered',
        unitId: unitsMap.piece?.id
      },
      {
        service: 'Backdrop Banners',
        description: 'Large backdrop banners for events and photo sessions',
        price: 3500,
        designFee: 4500,
        category: 'Banners',
        features: ['Large Format', 'Professional Finish', 'Event Ready', 'Custom Sizes'],
        icon: '🎭',
        popular: false,
        maxQuantity: 20,
        minQuantity: 1,
        pricingTiers: {
          '1-4': 3800,
          '5+': 3500
        },
        pricingType: 'tiered',
        unitId: unitsMap.piece?.id
      },

      // Stationery & Corporate Items
      {
        service: 'Receipt Books',
        description: 'Professional receipt books for business transactions',
        price: 500, // From PrintShop: KSh 500 per book
        designFee: 2000,
        category: 'Corporate Stationery',
        features: ['Carbonless Paper', 'Sequential Numbering', 'Professional Layout'],
        icon: '📋',
        popular: true,
        maxQuantity: 100,
        minQuantity: 5,
        pricingTiers: {
          '5-19': 550,
          '20-49': 500,
          '50+': 450
        },
        pricingType: 'tiered',
        unitId: unitsMap.piece?.id
      },
      {
        service: 'Envelopes',
        description: 'Professional envelopes with company branding',
        price: 40, // From PrintShop: KSh 40 per piece
        designFee: 2000,
        category: 'Corporate Stationery',
        features: ['Professional Branding', 'Multiple Sizes', 'Quality Paper'],
        icon: '✉️',
        popular: true,
        maxQuantity: 5000,
        minQuantity: 100,
        paperTypes: ['Bond Paper 100gsm', 'Art Paper 128gsm'],
        pricingTiers: {
          '100-499': 45,
          '500-999': 40,
          '1000+': 35
        },
        pricingType: 'tiered',
        unitId: unitsMap.piece?.id
      },
      {
        service: 'Presentation Folders',
        description: 'Professional presentation folders for business use',
        price: 180,
        designFee: 3000,
        category: 'Corporate Stationery',
        features: ['Professional Look', 'Document Pockets', 'Business Card Slots'],
        icon: '📁',
        popular: false,
        maxQuantity: 1000,
        minQuantity: 50,
        paperTypes: ['Art Paper 250gsm', 'Art Paper 300gsm'],
        pricingTiers: {
          '50-99': 200,
          '100-299': 180,
          '300+': 160
        },
        pricingType: 'tiered',
        unitId: unitsMap.piece?.id
      },

      // Specialty Items
      {
        service: 'Bookmarks',
        description: 'Custom printed bookmarks for promotional use',
        price: 25, // From PrintShop: Starting at KSh 25
        designFee: 1500,
        category: 'Marketing Materials',
        features: ['Unique Shape', 'Durable Material', 'Custom Design'],
        icon: '🔖',
        popular: false,
        maxQuantity: 5000,
        minQuantity: 100,
        paperTypes: ['Art Paper 250gsm', 'Art Paper 300gsm'],
        pricingTiers: {
          '100-499': 25,
          '500-999': 20,
          '1000+': 18
        },
        pricingType: 'tiered',
        unitId: unitsMap.piece?.id
      },
      {
        service: 'Postcards',
        description: 'High-quality postcards for marketing and events',
        price: 25, // From PrintShop: KSh 25 per card
        designFee: 1800,
        category: 'Marketing Materials',
        features: ['Standard Size', 'Quality Print', 'Marketing Ready'],
        icon: '📮',
        popular: true,
        maxQuantity: 3000,
        minQuantity: 100,
        paperTypes: ['Art Paper 200gsm', 'Art Paper 250gsm'],
        pricingTiers: {
          '100-499': 30,
          '500-999': 25,
          '1000+': 22
        },
        pricingType: 'tiered',
        unitId: unitsMap.piece?.id
      },
      {
        service: 'New Baby Cards',
        description: 'Beautiful announcement cards for new arrivals',
        price: 25, // From PrintShop: KSh 25 per piece
        designFee: 2500,
        category: 'Personal Cards',
        features: ['Beautiful Design', 'Premium Paper', 'Personalized Text'],
        icon: '👶',
        popular: false,
        maxQuantity: 500,
        minQuantity: 50,
        paperTypes: ['Art Paper 250gsm', 'Art Paper 300gsm'],
        pricingTiers: {
          '50-99': 30,
          '100-199': 25,
          '200+': 22
        },
        pricingType: 'tiered',
        unitId: unitsMap.piece?.id
      },

      // Apparel (Updated pricing)
      {
        service: 'Premium T-Shirt Printing',
        description: 'High-quality t-shirt printing with various options',
        price: 580, // From PrintShop: KSh 580
        designFee: 2000,
        category: 'Apparel',
        features: ['100% Cotton', 'Locally Made', 'Sizes S to 2XL', 'Durable Print'],
        icon: '👕',
        popular: true,
        maxQuantity: 500,
        minQuantity: 5,
        pricingTiers: {
          '5-19': 650,
          '20-49': 580,
          '50-99': 520,
          '100+': 480
        },
        pricingType: 'tiered',
        unitId: unitsMap.piece?.id
      },
      {
        service: 'Custom Hoodies',
        description: 'Premium hoodies with custom printing',
        price: 1200,
        designFee: 2500,
        category: 'Apparel',
        features: ['Premium Quality', 'Warm Material', 'Custom Design', 'Multiple Colors'],
        icon: '🧥',
        popular: true,
        maxQuantity: 200,
        minQuantity: 5,
        pricingTiers: {
          '5-19': 1300,
          '20-49': 1200,
          '50+': 1100
        },
        pricingType: 'tiered',
        unitId: unitsMap.piece?.id
      },
      {
        service: 'Polo T-Shirts',
        description: 'Professional polo t-shirts for corporate wear',
        price: 780,
        designFee: 2000,
        category: 'Apparel',
        features: ['Professional Look', 'Collar Design', 'Business Appropriate'],
        icon: '👔',
        popular: false,
        maxQuantity: 300,
        minQuantity: 10,
        pricingTiers: {
          '10-24': 820,
          '25-49': 780,
          '50+': 720
        },
        pricingType: 'tiered',
        unitId: unitsMap.piece?.id
      },

      // Merchandise & Gifts
      {
        service: 'Branded Mugs Premium',
        description: 'High-quality ceramic mugs with professional branding',
        price: 400,
        designFee: 1500,
        category: 'Gifts & Merchandise',
        features: ['Ceramic Material', 'Dishwasher Safe', 'Professional Print', 'Gift Ready'],
        icon: '☕',
        popular: true,
        maxQuantity: 200,
        minQuantity: 1,
        pricingTiers: {
          '1-9': 450,
          '10-24': 400,
          '25-49': 350,
          '50+': 320
        },
        pricingType: 'tiered',
        unitId: unitsMap.piece?.id
      },
      {
        service: 'Magic Mugs',
        description: 'Color-changing magic mugs with custom design',
        price: 650,
        designFee: 2000,
        category: 'Gifts & Merchandise',
        features: ['Color Changing', 'Unique Effect', 'Gift Quality', 'Ceramic Material'],
        icon: '🎩',
        popular: true,
        maxQuantity: 100,
        minQuantity: 1,
        pricingTiers: {
          '1-9': 700,
          '10-24': 650,
          '25+': 600
        },
        pricingType: 'tiered',
        unitId: unitsMap.piece?.id
      },
      {
        service: 'Travel Mugs',
        description: 'Insulated travel mugs for on-the-go branding',
        price: 850,
        designFee: 2000,
        category: 'Gifts & Merchandise',
        features: ['Insulated', 'Leak Proof', 'Travel Friendly', 'Durable'],
        icon: '🚗',
        popular: false,
        maxQuantity: 100,
        minQuantity: 5,
        pricingTiers: {
          '5-19': 900,
          '20-49': 850,
          '50+': 800
        },
        pricingType: 'tiered',
        unitId: unitsMap.piece?.id
      },
      {
        service: 'Branded Tote Bags',
        description: 'Custom tote bags made from cotton or canvas',
        price: 380,
        designFee: 2000,
        category: 'Gifts & Merchandise',
        features: ['Cotton/Canvas Material', 'Eco-Friendly', 'Reusable', 'Strong Handles'],
        icon: '👜',
        popular: true,
        maxQuantity: 500,
        minQuantity: 10,
        pricingTiers: {
          '10-24': 420,
          '25-49': 380,
          '50-99': 340,
          '100+': 300
        },
        pricingType: 'tiered',
        unitId: unitsMap.piece?.id
      },
      {
        service: 'Custom Notebooks',
        description: 'Professional notebooks and journals with custom covers',
        price: 250,
        designFee: 2500,
        category: 'Gifts & Merchandise',
        features: ['Quality Paper', 'Custom Cover', 'Spiral/Perfect Bound', 'Business Use'],
        icon: '📔',
        popular: true,
        maxQuantity: 500,
        minQuantity: 20,
        pricingTiers: {
          '20-49': 280,
          '50-99': 250,
          '100+': 220
        },
        pricingType: 'tiered',
        unitId: unitsMap.piece?.id
      },
      {
        service: 'Custom Diaries',
        description: 'Professional diaries with company branding',
        price: 320,
        designFee: 3000,
        category: 'Gifts & Merchandise',
        features: ['Annual Calendar', 'Quality Binding', 'Professional Design', 'Corporate Gift'],
        icon: '📅',
        popular: false,
        maxQuantity: 300,
        minQuantity: 25,
        pricingTiers: {
          '25-49': 350,
          '50-99': 320,
          '100+': 280
        },
        pricingType: 'tiered',
        unitId: unitsMap.piece?.id
      },

      // Photo Services
      {
        service: 'Mounted Photos',
        description: 'Professional photo mounting on foam board',
        price: 700, // From PrintShop: from KSh 700
        designFee: 0,
        category: 'Photo Services',
        features: ['Foam Board Mount', 'Professional Finish', 'Various Sizes', 'Exhibition Ready'],
        icon: '🖼️',
        popular: false,
        maxQuantity: 100,
        minQuantity: 1,
        pricingTiers: {
          '1-9': 750,
          '10-24': 700,
          '25+': 650
        },
        pricingType: 'tiered',
        unitId: unitsMap.piece?.id
      },
      {
        service: 'Framed Photos',
        description: 'Professional photo framing service',
        price: 1200,
        designFee: 0,
        category: 'Photo Services',
        features: ['Quality Frame', 'Glass Protection', 'Professional Finish', 'Ready to Hang'],
        icon: '🖼️',
        popular: false,
        maxQuantity: 50,
        minQuantity: 1,
        pricingTiers: {
          '1-9': 1300,
          '10+': 1200
        },
        pricingType: 'tiered',
        unitId: unitsMap.piece?.id
      },

      // Specialized Stickers
      {
        service: 'Reflective Stickers',
        description: 'High-visibility reflective stickers for safety',
        price: 45,
        designFee: 1800,
        category: 'Stickers & Labels',
        features: ['High Visibility', 'Reflective Material', 'Safety Use', 'Weather Resistant'],
        icon: '⚡',
        popular: false,
        maxQuantity: 2000,
        minQuantity: 50,
        pricingTiers: {
          '50-199': 50,
          '200-499': 45,
          '500+': 40
        },
        pricingType: 'tiered',
        unitId: unitsMap.piece?.id
      },
      {
        service: 'Adhesive Label Stickers',
        description: 'Professional adhesive labels for products',
        price: 18,
        designFee: 1500,
        category: 'Stickers & Labels',
        features: ['Strong Adhesive', 'Custom Shapes', 'Product Labels', 'Various Finishes'],
        icon: '🏷️',
        popular: true,
        maxQuantity: 10000,
        minQuantity: 100,
        pricingTiers: {
          '100-499': 20,
          '500-999': 18,
          '1000-2999': 15,
          '3000+': 12
        },
        pricingType: 'tiered',
        unitId: unitsMap.piece?.id
      },

      // Specialty Books & Booklets
      {
        service: 'Spiral Bound Books',
        description: 'Professional spiral bound books and manuals',
        price: 180,
        designFee: 3500,
        category: 'Books & Booklets',
        features: ['Spiral Binding', 'Lay Flat Design', 'Professional Finish', 'Custom Pages'],
        icon: '📚',
        popular: false,
        maxQuantity: 500,
        minQuantity: 10,
        pricingTiers: {
          '10-49': 200,
          '50-99': 180,
          '100+': 160
        },
        pricingType: 'tiered',
        unitId: unitsMap.piece?.id
      },
      {
        service: 'Eulogy Booklets',
        description: 'Memorial booklets for funeral services',
        price: 120,
        designFee: 4000,
        category: 'Books & Booklets',
        features: ['Respectful Design', 'Quality Paper', 'Professional Layout', 'Memorial Service'],
        icon: '🕊️',
        popular: false,
        maxQuantity: 200,
        minQuantity: 20,
        pricingTiers: {
          '20-49': 130,
          '50-99': 120,
          '100+': 110
        },
        pricingType: 'tiered',
        unitId: unitsMap.piece?.id
      },

      // Custom Flags
      {
        service: 'Custom Flags',
        description: 'Custom printed flags for events and businesses',
        price: 2800,
        designFee: 3000,
        category: 'Banners',
        features: ['Outdoor Durable', 'Vibrant Colors', 'Custom Sizes', 'Reinforced Edges'],
        icon: '🏁',
        popular: false,
        maxQuantity: 50,
        minQuantity: 1,
        pricingTiers: {
          '1-4': 3000,
          '5-9': 2800,
          '10+': 2500
        },
        pricingType: 'tiered',
        unitId: unitsMap.piece?.id
      }
    ];

    // Create enhanced catalogue items
    let createdCount = 0;
    for (const item of enhancedCatalogueItems) {
      try {
        const existingItem = await prisma.catalogue.findFirst({
          where: { service: item.service }
        });
        
        if (!existingItem) {
          await prisma.catalogue.create({ data: item });
          createdCount++;
          console.log(`✅ Created: ${item.service} - KSh ${item.price}`);
        } else {
          console.log(`⚠️ Already exists: ${item.service}`);
        }
      } catch (error) {
        console.log(`❌ Error creating ${item.service}:`, error.message);
      }
    }

    // Create additional paper types based on PrintShop services
    console.log('\n📄 Adding specialized paper types...');
    const additionalPaperTypes = [
      {
        name: 'Perfumed Paper',
        grammage: '200gsm',
        oneSidedPrice: 85.00,
        twoSidedPrice: 120.00,
        category: 'specialty',
        active: true,
        order: 11
      },
      {
        name: 'Embossed Paper',
        grammage: '250gsm',
        oneSidedPrice: 95.00,
        twoSidedPrice: 135.00,
        category: 'specialty',
        active: true,
        order: 12
      },
      {
        name: 'Carbonless Paper',
        grammage: '55gsm',
        oneSidedPrice: 15.00,
        twoSidedPrice: 15.00,
        category: 'forms',
        active: true,
        order: 13
      }
    ];

    for (const paperType of additionalPaperTypes) {
      try {
        const existingPaper = await prisma.paperType.findFirst({
          where: { name: paperType.name }
        });
        
        if (!existingPaper) {
          await prisma.paperType.create({ data: paperType });
          console.log(`✅ Added paper type: ${paperType.name}`);
        } else {
          console.log(`⚠️ Paper type exists: ${paperType.name}`);
        }
      } catch (error) {
        console.log(`❌ Error creating paper type ${paperType.name}:`, error.message);
      }
    }

    console.log('\n🎉 Enhanced Kenyan market catalogue seeding completed!');
    console.log(`📊 Summary:`);
    console.log(`   - New Catalogue Items Added: ${createdCount}`);
    console.log(`   - Total Enhanced Items: ${enhancedCatalogueItems.length}`);
    console.log(`   - Price Range: KSh 12 - KSh 6,500`);
    console.log(`   - Categories: Business Cards, Marketing, Banners, Apparel, Merchandise & More`);
    console.log(`   - Data Source: PrintShop.co.ke market research`);

  } catch (error) {
    console.error('❌ Error during enhanced seeding:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  main()
    .catch((error) => {
      console.error('❌ Enhanced seeding failed:', error);
      process.exit(1);
    });
}

module.exports = { main }; 