#!/usr/bin/env ts-node

/**
 * Security audit script for the receipting system
 * Checks for common security vulnerabilities and best practices
 */

import fs from 'fs';
import path from 'path';

interface SecurityIssue {
  severity: 'HIGH' | 'MEDIUM' | 'LOW' | 'INFO';
  category: string;
  description: string;
  file?: string;
  line?: number;
  recommendation: string;
}

const issues: SecurityIssue[] = [];

function addIssue(issue: SecurityIssue): void {
  issues.push(issue);
}

function checkFile(filePath: string): void {
  if (!fs.existsSync(filePath)) {
    return;
  }

  const content = fs.readFileSync(filePath, 'utf-8');
  const lines = content.split('\n');

  lines.forEach((line, index) => {
    const lineNumber = index + 1;

    // Check for SQL injection vulnerabilities
    if (line.includes('prisma.') && line.includes('${') && !line.includes('where:')) {
      addIssue({
        severity: 'HIGH',
        category: 'SQL Injection',
        description: 'Potential SQL injection vulnerability with string interpolation',
        file: filePath,
        line: lineNumber,
        recommendation: 'Use parameterized queries and avoid string interpolation in database queries'
      });
    }

    // Check for hardcoded secrets
    if (line.match(/(password|secret|key|token)\s*[:=]\s*["'][^"']+["']/i)) {
      addIssue({
        severity: 'HIGH',
        category: 'Hardcoded Secrets',
        description: 'Potential hardcoded secret or password',
        file: filePath,
        line: lineNumber,
        recommendation: 'Move secrets to environment variables or secure configuration'
      });
    }

    // Check for console.log with sensitive data
    if (line.includes('console.log') && (line.includes('password') || line.includes('secret') || line.includes('token'))) {
      addIssue({
        severity: 'MEDIUM',
        category: 'Information Disclosure',
        description: 'Potential logging of sensitive information',
        file: filePath,
        line: lineNumber,
        recommendation: 'Avoid logging sensitive data or sanitize before logging'
      });
    }

    // Check for eval() usage
    if (line.includes('eval(')) {
      addIssue({
        severity: 'HIGH',
        category: 'Code Injection',
        description: 'Use of eval() function detected',
        file: filePath,
        line: lineNumber,
        recommendation: 'Avoid using eval() as it can lead to code injection vulnerabilities'
      });
    }

    // Check for insufficient input validation
    if (line.includes('req.body') && !line.includes('validate')) {
      addIssue({
        severity: 'MEDIUM',
        category: 'Input Validation',
        description: 'Direct use of request body without validation',
        file: filePath,
        line: lineNumber,
        recommendation: 'Always validate and sanitize user input before processing'
      });
    }

    // Check for missing error handling
    if (line.includes('await ') && !content.includes('try') && !content.includes('catch')) {
      addIssue({
        severity: 'MEDIUM',
        category: 'Error Handling',
        description: 'Async operation without proper error handling',
        file: filePath,
        line: lineNumber,
        recommendation: 'Wrap async operations in try-catch blocks'
      });
    }

    // Check for weak random number generation
    if (line.includes('Math.random()')) {
      addIssue({
        severity: 'MEDIUM',
        category: 'Weak Randomness',
        description: 'Use of Math.random() for potentially security-sensitive operations',
        file: filePath,
        line: lineNumber,
        recommendation: 'Use crypto.randomBytes() or crypto.randomUUID() for security-sensitive random values'
      });
    }

    // Check for missing rate limiting
    if (line.includes('export async function POST') || line.includes('export async function GET')) {
      if (!content.includes('rateLimit') && !content.includes('throttle')) {
        addIssue({
          severity: 'MEDIUM',
          category: 'Rate Limiting',
          description: 'API endpoint without rate limiting',
          file: filePath,
          line: lineNumber,
          recommendation: 'Implement rate limiting to prevent abuse'
        });
      }
    }

    // Check for missing authentication
    if (line.includes('/api/admin/') && !content.includes('auth') && !content.includes('session')) {
      addIssue({
        severity: 'HIGH',
        category: 'Authentication',
        description: 'Admin API endpoint without authentication check',
        file: filePath,
        line: lineNumber,
        recommendation: 'Implement proper authentication and authorization checks'
      });
    }

    // Check for missing CSRF protection
    if (line.includes('POST') && !content.includes('csrf') && !content.includes('token')) {
      addIssue({
        severity: 'MEDIUM',
        category: 'CSRF Protection',
        description: 'POST endpoint without CSRF protection',
        file: filePath,
        line: lineNumber,
        recommendation: 'Implement CSRF token validation for state-changing operations'
      });
    }
  });
}

function checkDatabaseSchema(): void {
  const schemaPath = path.join(process.cwd(), 'prisma', 'schema.prisma');
  
  if (!fs.existsSync(schemaPath)) {
    addIssue({
      severity: 'HIGH',
      category: 'Database Security',
      description: 'Prisma schema file not found',
      recommendation: 'Ensure database schema is properly configured'
    });
    return;
  }

  const schema = fs.readFileSync(schemaPath, 'utf-8');

  // Check for missing indexes on frequently queried fields
  if (!schema.includes('@@index') && schema.includes('transactionId')) {
    addIssue({
      severity: 'MEDIUM',
      category: 'Database Performance',
      description: 'Missing database indexes on frequently queried fields',
      recommendation: 'Add indexes on transactionId, receiptNumber, and other frequently queried fields'
    });
  }

  // Check for missing unique constraints
  if (!schema.includes('@unique') && schema.includes('receiptNumber')) {
    addIssue({
      severity: 'HIGH',
      category: 'Data Integrity',
      description: 'Missing unique constraints on critical fields',
      recommendation: 'Add unique constraints on receiptNumber and transactionId'
    });
  }

  // Check for missing cascade deletes
  if (schema.includes('onDelete') && !schema.includes('Cascade')) {
    addIssue({
      severity: 'MEDIUM',
      category: 'Data Integrity',
      description: 'Missing cascade delete constraints',
      recommendation: 'Configure proper cascade delete behavior for related records'
    });
  }
}

function checkEnvironmentVariables(): void {
  const envPath = path.join(process.cwd(), '.env');
  
  if (!fs.existsSync(envPath)) {
    addIssue({
      severity: 'HIGH',
      category: 'Configuration Security',
      description: 'Environment file not found',
      recommendation: 'Create .env file with proper security configurations'
    });
    return;
  }

  const envContent = fs.readFileSync(envPath, 'utf-8');

  // Check for weak database passwords
  if (envContent.includes('DATABASE_URL') && envContent.includes('password=123')) {
    addIssue({
      severity: 'HIGH',
      category: 'Weak Credentials',
      description: 'Weak database password detected',
      recommendation: 'Use strong, randomly generated passwords for database connections'
    });
  }

  // Check for missing JWT secret
  if (!envContent.includes('JWT_SECRET') && !envContent.includes('NEXTAUTH_SECRET')) {
    addIssue({
      severity: 'HIGH',
      category: 'Authentication Security',
      description: 'Missing JWT or NextAuth secret',
      recommendation: 'Configure strong secrets for authentication tokens'
    });
  }

  // Check for development settings in production
  if (envContent.includes('NODE_ENV=development') && envContent.includes('production')) {
    addIssue({
      severity: 'MEDIUM',
      category: 'Configuration Security',
      description: 'Development settings detected in production environment',
      recommendation: 'Ensure proper environment-specific configurations'
    });
  }
}

function generateReport(): void {
  console.log('🔒 Security Audit Report for Receipting System\n');

  const severityCounts = {
    HIGH: issues.filter(i => i.severity === 'HIGH').length,
    MEDIUM: issues.filter(i => i.severity === 'MEDIUM').length,
    LOW: issues.filter(i => i.severity === 'LOW').length,
    INFO: issues.filter(i => i.severity === 'INFO').length
  };

  console.log('📊 Summary:');
  console.log(`High Severity Issues: ${severityCounts.HIGH} 🔴`);
  console.log(`Medium Severity Issues: ${severityCounts.MEDIUM} 🟡`);
  console.log(`Low Severity Issues: ${severityCounts.LOW} 🟢`);
  console.log(`Informational: ${severityCounts.INFO} ℹ️`);
  console.log(`Total Issues: ${issues.length}\n`);

  if (issues.length === 0) {
    console.log('🎉 No security issues found! The receipting system appears to be secure.');
    return;
  }

  // Group issues by severity
  ['HIGH', 'MEDIUM', 'LOW', 'INFO'].forEach(severity => {
    const severityIssues = issues.filter(i => i.severity === severity);
    if (severityIssues.length > 0) {
      console.log(`\n${severity} SEVERITY ISSUES:`);
      console.log('='.repeat(50));
      
      severityIssues.forEach((issue, index) => {
        console.log(`\n${index + 1}. ${issue.category}`);
        console.log(`   Description: ${issue.description}`);
        if (issue.file) {
          console.log(`   File: ${issue.file}${issue.line ? `:${issue.line}` : ''}`);
        }
        console.log(`   Recommendation: ${issue.recommendation}`);
      });
    }
  });

  console.log('\n🔧 Recommended Actions:');
  console.log('1. Address all HIGH severity issues immediately');
  console.log('2. Plan to fix MEDIUM severity issues in the next release');
  console.log('3. Consider LOW severity issues for future improvements');
  console.log('4. Review and implement security best practices');
  console.log('5. Conduct regular security audits');
}

// Main execution
console.log('🔍 Starting security audit...\n');

// Check key files
const filesToCheck = [
  'src/services/receiptService.ts',
  'src/services/transactionService.ts',
  'src/utils/transactionParser.ts',
  'src/utils/validation.ts',
  'src/app/api/admin/receipts/route.ts',
  'src/app/api/admin/receipts/check/route.ts',
  'src/app/api/admin/transactions/route.ts'
];

filesToCheck.forEach(checkFile);

// Check database schema
checkDatabaseSchema();

// Check environment variables
checkEnvironmentVariables();

// Generate and display report
generateReport();

// Exit with appropriate code
if (issues.filter(i => i.severity === 'HIGH').length > 0) {
  console.log('\n❌ Security audit failed due to HIGH severity issues.');
  process.exit(1);
} else {
  console.log('\n✅ Security audit completed successfully.');
  process.exit(0);
}
