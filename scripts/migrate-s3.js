/**
 * S3 Migration Script
 *
 * This script migrates all objects from one S3 bucket to another.
 * It automatically detects S3 configurations from the .env file.
 *
 * Usage: node scripts/migrate-s3.js
 *
 * The script will automatically detect both configurations from your .env file.
 * It will use the two configurations found in the .env file:
 * - Configuration 1: First set of S3 credentials (source)
 * - Configuration 2: Second set of S3 credentials (destination)
 */

// Import required modules
const { S3Client, ListObjectsV2Command, GetObjectCommand, PutObjectCommand, HeadBucketCommand } = require('@aws-sdk/client-s3');
const { NodeHttpHandler } = require('@smithy/node-http-handler');
const fs = require('fs');
const path = require('path');
const dotenv = require('dotenv');
const { Readable } = require('stream');
const { finished } = require('stream/promises');

// Load environment variables
dotenv.config();

// Parse .env file directly to find all S3 configurations
const envContent = fs.readFileSync(path.join(__dirname, '../.env'), 'utf8');
const s3Configs = [];

// Extract S3 configurations from .env file
// Try to find any S3 configuration blocks directly
const regions = envContent.match(/NEXT_PUBLIC_S3_REGION="([^"]+)"/g) || [];
const endpoints = envContent.match(/NEXT_PUBLIC_S3_ENDPOINT="([^"]+)"/g) || [];
const buckets = envContent.match(/NEXT_PUBLIC_S3_BUCKET="([^"]+)"/g) || [];
const accessKeys = envContent.match(/NEXT_PUBLIC_S3_ACCESS_KEY="([^"]+)"/g) || [];
const secretKeys = envContent.match(/NEXT_PUBLIC_S3_SECRET_KEY="([^"]+)"/g) || [];

// If we found at least one complete set of configuration
if (regions.length > 0 && endpoints.length > 0 && buckets.length > 0 &&
    accessKeys.length > 0 && secretKeys.length > 0) {

  console.log('Found S3 configurations in .env file.');

  // Extract the first configuration
  const region1 = regions[0].match(/"([^"]+)"/)[1];
  const endpoint1 = endpoints[0].match(/"([^"]+)"/)[1];
  const bucket1 = buckets[0].match(/"([^"]+)"/)[1];
  const accessKey1 = accessKeys[0].match(/"([^"]+)"/)[1];
  const secretKey1 = secretKeys[0].match(/"([^"]+)"/)[1];

  s3Configs.push({
    configNumber: '1',
    region: region1,
    endpoint: endpoint1,
    bucketName: bucket1,
    accessKeyId: accessKey1,
    secretAccessKey: secretKey1,
    useS3: true
  });

  // If we have a second configuration
  if (regions.length > 1 && endpoints.length > 1 && buckets.length > 1 &&
      accessKeys.length > 1 && secretKeys.length > 1) {

    const region2 = regions[1].match(/"([^"]+)"/)[1];
    const endpoint2 = endpoints[1].match(/"([^"]+)"/)[1];
    const bucket2 = buckets[1].match(/"([^"]+)"/)[1];
    const accessKey2 = accessKeys[1].match(/"([^"]+)"/)[1];
    const secretKey2 = secretKeys[1].match(/"([^"]+)"/)[1];

    s3Configs.push({
      configNumber: '2',
      region: region2,
      endpoint: endpoint2,
      bucketName: bucket2,
      accessKeyId: accessKey2,
      secretAccessKey: secretKey2,
      useS3: true
    });
  }
}

// If no configurations found using the regex pattern, fall back to environment variables
if (s3Configs.length === 0) {
  console.log('No S3 configurations found in .env file using pattern. Falling back to environment variables.');

  // Add the first configuration from environment variables
  if (process.env.NEXT_PUBLIC_S3_REGION && process.env.NEXT_PUBLIC_S3_ENDPOINT) {
    s3Configs.push({
      configNumber: '1',
      region: process.env.NEXT_PUBLIC_S3_REGION || 'fr-par-1',
      endpoint: process.env.NEXT_PUBLIC_S3_ENDPOINT || 'https://fr-par-1.linodeobjects.com',
      bucketName: process.env.NEXT_PUBLIC_S3_BUCKET || 'mocky',
      accessKeyId: process.env.NEXT_PUBLIC_S3_ACCESS_KEY || '',
      secretAccessKey: process.env.NEXT_PUBLIC_S3_SECRET_KEY || '',
      useS3: process.env.NEXT_PUBLIC_USE_S3 === 'true'
    });
  }

  // Add the second configuration with the destination bucket "mocky2"
  s3Configs.push({
    configNumber: '2',
    region: process.env.NEXT_PUBLIC_S3_REGION || 'fr-par-1',
    endpoint: process.env.NEXT_PUBLIC_S3_ENDPOINT || 'https://fr-par-1.linodeobjects.com',
    bucketName: 'mocky2', // Hardcoded destination bucket
    accessKeyId: process.env.NEXT_PUBLIC_S3_ACCESS_KEY || '',
    secretAccessKey: process.env.NEXT_PUBLIC_S3_SECRET_KEY || '',
    useS3: process.env.NEXT_PUBLIC_USE_S3 === 'true'
  });
}

// If we still don't have at least two configurations, create a destination config
if (s3Configs.length < 2) {
  console.log('Found only one S3 configuration. Creating destination configuration with bucket "mocky2".');
  // Create a destination configuration with bucket "mocky2"
  if (s3Configs.length === 1) {
    const firstConfig = s3Configs[0];
    s3Configs.push({
      ...firstConfig,
      configNumber: '2',
      bucketName: 'mocky2' // Use mocky2 as the destination bucket
    });
  } else {
    console.error('No S3 configurations found in .env file. Please check your .env file.');
    process.exit(1);
  }
}

// Use the first two configurations as source and destination
const sourceConfig = s3Configs[0];
const destConfig = s3Configs[1];

console.log('Found the following S3 configurations:');
console.log(`Source (Config ${sourceConfig.configNumber}): ${sourceConfig.endpoint}/${sourceConfig.bucketName}`);
console.log(`Destination (Config ${destConfig.configNumber}): ${destConfig.endpoint}/${destConfig.bucketName}`);

// Create S3 clients
const sourceS3Client = new S3Client({
  region: sourceConfig.region,
  endpoint: sourceConfig.endpoint,
  credentials: {
    accessKeyId: sourceConfig.accessKeyId,
    secretAccessKey: sourceConfig.secretAccessKey,
  },
  forcePathStyle: true,
  maxAttempts: 5, // Increase retry attempts
  retryMode: 'adaptive', // Use adaptive retry mode for better handling
  requestHandler: new NodeHttpHandler({
    connectionTimeout: 15000, // 15 seconds connection timeout
    socketTimeout: 120000,    // 2 minutes socket timeout
  })
});

const destS3Client = new S3Client({
  region: destConfig.region,
  endpoint: destConfig.endpoint,
  credentials: {
    accessKeyId: destConfig.accessKeyId,
    secretAccessKey: destConfig.secretAccessKey,
  },
  forcePathStyle: true,
  maxAttempts: 5, // Increase retry attempts
  retryMode: 'adaptive', // Use adaptive retry mode for better handling
  requestHandler: new NodeHttpHandler({
    connectionTimeout: 15000, // 15 seconds connection timeout
    socketTimeout: 120000,    // 2 minutes socket timeout
  })
});

// Create a log file for the migration
const logDir = path.join(__dirname, '../logs');
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir, { recursive: true });
}
const logFile = path.join(logDir, `s3-migration-${new Date().toISOString().replace(/:/g, '-')}.log`);
const logStream = fs.createWriteStream(logFile, { flags: 'a' });

// Helper function to log messages to console and file
function log(message) {
  const timestamp = new Date().toISOString();
  const logMessage = `[${timestamp}] ${message}`;
  console.log(logMessage);
  logStream.write(logMessage + '\n');
}

// Helper function to check if bucket exists
async function checkBucketExists(s3Client, bucketName) {
  try {
    await s3Client.send(new HeadBucketCommand({ Bucket: bucketName }));
    return true;
  } catch (error) {
    if (error.name === 'NotFound' || error.$metadata?.httpStatusCode === 404) {
      return false;
    }
    throw error;
  }
}

// Helper function to list all objects in a bucket with pagination
async function listAllObjects(s3Client, bucketName, prefix = '') {
  let allObjects = [];
  let continuationToken = undefined;

  do {
    const command = new ListObjectsV2Command({
      Bucket: bucketName,
      Prefix: prefix,
      ContinuationToken: continuationToken,
      MaxKeys: 1000,
    });

    const response = await s3Client.send(command);

    if (response.Contents && response.Contents.length > 0) {
      allObjects = [...allObjects, ...response.Contents];
    }

    continuationToken = response.NextContinuationToken;
  } while (continuationToken);

  return allObjects;
}

// Helper function to copy an object from source to destination
async function copyObject(sourceClient, destClient, sourceBucket, destBucket, key) {
  const tempDir = path.join(__dirname, '../temp');
  const tempFilePath = path.join(tempDir, `temp-${Date.now()}-${path.basename(key).replace(/[^a-zA-Z0-9.-]/g, '_')}`);

  // Create temp directory if it doesn't exist
  if (!fs.existsSync(tempDir)) {
    fs.mkdirSync(tempDir, { recursive: true });
  }

  try {
    // Get the object from source
    const getCommand = new GetObjectCommand({
      Bucket: sourceBucket,
      Key: key,
    });

    // Add timeout for the get operation
    const getTimeout = setTimeout(() => {
      throw new Error('Get operation timed out after 60 seconds');
    }, 60000);

    const response = await sourceClient.send(getCommand);
    clearTimeout(getTimeout);

    const { Body, ContentType, ContentLength, Metadata } = response;

    if (!Body) {
      throw new Error('Received empty body from source');
    }

    // Save to temp file
    const writeStream = fs.createWriteStream(tempFilePath);

    // Convert Body to a Node.js readable stream
    const bodyStream = Body;

    // Pipe the stream to the file with timeout
    await new Promise((resolve, reject) => {
      const streamTimeout = setTimeout(() => {
        writeStream.close();
        reject(new Error('Stream operation timed out after 120 seconds'));
      }, 120000);

      bodyStream.pipe(writeStream)
        .on('error', (err) => {
          clearTimeout(streamTimeout);
          reject(err);
        })
        .on('finish', () => {
          clearTimeout(streamTimeout);
          resolve();
        });
    });

    // Verify the file was created and has content
    if (!fs.existsSync(tempFilePath)) {
      throw new Error('Temp file was not created');
    }

    const stats = fs.statSync(tempFilePath);
    if (stats.size === 0) {
      throw new Error('Temp file is empty');
    }

    // Read the file back
    const fileContent = fs.readFileSync(tempFilePath);

    // Upload to destination
    const putCommand = new PutObjectCommand({
      Bucket: destBucket,
      Key: key,
      Body: fileContent,
      ContentType: ContentType || 'application/octet-stream',
      Metadata: Metadata || {},
      ACL: 'public-read', // Default to public-read
    });

    // Add timeout for the put operation
    const putTimeout = setTimeout(() => {
      throw new Error('Put operation timed out after 60 seconds');
    }, 60000);

    await destClient.send(putCommand);
    clearTimeout(putTimeout);

    // Clean up temp file
    if (fs.existsSync(tempFilePath)) {
      fs.unlinkSync(tempFilePath);
    }

    return {
      success: true,
      key,
      size: ContentLength || fileContent.length || 0,
    };
  } catch (error) {
    // Clean up temp file if it exists
    if (fs.existsSync(tempFilePath)) {
      try {
        fs.unlinkSync(tempFilePath);
      } catch (cleanupError) {
        console.error(`Error cleaning up temp file: ${cleanupError.message}`);
      }
    }

    return {
      success: false,
      key,
      error: error.message,
    };
  }
}

// Main migration function
async function migrateS3() {
  log('Starting S3 migration...');
  log(`Source: ${sourceConfig.endpoint}/${sourceConfig.bucketName}`);
  log(`Destination: ${destConfig.endpoint}/${destConfig.bucketName}`);

  try {
    // Check if source bucket exists
    log('Checking source bucket...');
    const sourceExists = await checkBucketExists(sourceS3Client, sourceConfig.bucketName);
    if (!sourceExists) {
      throw new Error(`Source bucket '${sourceConfig.bucketName}' does not exist`);
    }

    // Check if destination bucket exists
    log('Checking destination bucket...');
    const destExists = await checkBucketExists(destS3Client, destConfig.bucketName);
    if (!destExists) {
      throw new Error(`Destination bucket '${destConfig.bucketName}' does not exist`);
    }

    // List all objects in source bucket
    log('Listing objects in source bucket...');
    const objects = await listAllObjects(sourceS3Client, sourceConfig.bucketName);
    log(`Found ${objects.length} objects in source bucket`);

    if (objects.length === 0) {
      log('No objects to migrate. Exiting...');
      return;
    }

    // Migrate objects
    log('Starting migration of objects...');
    let successCount = 0;
    let failureCount = 0;
    let totalSize = 0;

    // Process in batches to avoid overwhelming the system
    const batchSize = 3; // Process 3 objects at a time to reduce load

    for (let i = 0; i < objects.length; i += batchSize) {
      const batch = objects.slice(i, i + batchSize);
      const batchPromises = batch.map(async (object, batchIndex) => {
        const globalIndex = i + batchIndex;
        const progress = ((globalIndex + 1) / objects.length * 100).toFixed(2);
        log(`[${progress}%] Migrating object ${globalIndex + 1}/${objects.length}: ${object.Key}`);

        try {
          const result = await copyObject(
            sourceS3Client,
            destS3Client,
            sourceConfig.bucketName,
            destConfig.bucketName,
            object.Key
          );

          if (result.success) {
            successCount++;
            totalSize += result.size;
            log(`✅ Successfully migrated: ${object.Key} (${formatBytes(result.size)})`);
            return { success: true, key: object.Key, size: result.size };
          } else {
            failureCount++;
            log(`❌ Failed to migrate: ${object.Key} - ${result.error}`);
            return { success: false, key: object.Key, error: result.error };
          }
        } catch (error) {
          failureCount++;
          log(`❌ Failed to migrate: ${object.Key} - ${error.message}`);
          return { success: false, key: object.Key, error: error.message };
        }
      });

      // Wait for the current batch to complete before moving to the next
      await Promise.all(batchPromises);

      // Add a delay between batches to avoid overwhelming the system
      if (i + batchSize < objects.length) {
        // Longer delay (3 seconds) to give the system more time to recover
        await new Promise(resolve => setTimeout(resolve, 3000));
      }
    }

    // Log summary
    log('\nMigration Summary:');
    log(`Total objects: ${objects.length}`);
    log(`Successfully migrated: ${successCount}`);
    log(`Failed to migrate: ${failureCount}`);
    log(`Total size migrated: ${formatBytes(totalSize)}`);

    if (failureCount > 0) {
      log('\nWarning: Some objects failed to migrate. Check the log file for details.');
    } else {
      log('\nAll objects were successfully migrated!');
    }

  } catch (error) {
    console.error(`Error during migration: ${error.message}`);
    console.error(error.stack);

    try {
      // Try to log to file if stream is still writable
      if (!logStream.closed && !logStream.destroyed) {
        logStream.write(`[${new Date().toISOString()}] Error during migration: ${error.message}\n`);
        logStream.write(`[${new Date().toISOString()}] ${error.stack}\n`);
      }
    } catch (logError) {
      console.error('Error writing to log file:', logError);
    }
  } finally {
    try {
      // Close the log stream if it's still open
      if (!logStream.closed && !logStream.destroyed) {
        logStream.end();
        console.log(`Migration log saved to: ${logFile}`);
      }
    } catch (closeError) {
      console.error('Error closing log stream:', closeError);
    }
  }
}

// Helper function to format bytes
function formatBytes(bytes, decimals = 2) {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

// Run the migration
migrateS3().catch(error => {
  console.error('Unhandled error:', error);
  process.exit(1);
});
