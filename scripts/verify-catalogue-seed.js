const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function verifySeededData() {
  console.log('🔍 Verifying seeded catalogue data...\n');

  try {
    // Check Units
    const units = await prisma.unit.findMany({
      orderBy: { order: 'asc' }
    });
    console.log(`📐 Units (${units.length}):`);
    units.forEach(unit => {
      console.log(`   ✅ ${unit.displayName} (${unit.shortForm}) - ${unit.category}`);
    });

    // Check Paper Types
    const paperTypes = await prisma.paperType.findMany({
      where: { active: true },
      orderBy: { order: 'asc' }
    });
    console.log(`\n📄 Paper Types (${paperTypes.length}):`);
    paperTypes.forEach(paper => {
      console.log(`   ✅ ${paper.name} - One: KES ${paper.oneSidedPrice}, Two: KES ${paper.twoSidedPrice}`);
    });

    // Check Pricing Rules
    const pricingRules = await prisma.pricingRule.findMany({
      where: { active: true }
    });
    console.log(`\n💰 Pricing Rules (${pricingRules.length}):`);
    pricingRules.forEach(rule => {
      console.log(`   ✅ ${rule.name} (${rule.type}) - ${rule.category || 'General'}`);
    });

    // Check Catalogue Items by Category
    const catalogueItems = await prisma.catalogue.findMany({
      include: {
        unit: true
      },
      orderBy: [
        { category: 'asc' },
        { service: 'asc' }
      ]
    });

    console.log(`\n🛍️ Catalogue Items (${catalogueItems.length}):`);
    
    // Group by category
    const grouped = catalogueItems.reduce((acc, item) => {
      const category = item.category || 'Other';
      if (!acc[category]) acc[category] = [];
      acc[category].push(item);
      return acc;
    }, {});

    Object.entries(grouped).forEach(([category, items]) => {
      console.log(`\n   📂 ${category} (${items.length} items):`);
      items.forEach(item => {
        const unit = item.unit ? item.unit.shortForm : 'unit';
        const pricing = item.pricingType === 'tiered' ? 'Tiered' : 
                       item.pricingType === 'per_meter' ? 'Per m²' : 'Fixed';
        console.log(`     ✅ ${item.service} - KES ${item.price}/${unit} (${pricing}) + Design: KES ${item.designFee}`);
      });
    });

    // Popular items
    const popularItems = await prisma.catalogue.findMany({
      where: { popular: true },
      select: { service: true, category: true, price: true }
    });
    console.log(`\n⭐ Popular Items (${popularItems.length}):`);
    popularItems.forEach(item => {
      console.log(`   🌟 ${item.service} (${item.category}) - KES ${item.price}`);
    });

    // Price ranges
    const priceStats = await prisma.catalogue.aggregate({
      _min: { price: true, designFee: true },
      _max: { price: true, designFee: true },
      _avg: { price: true, designFee: true }
    });

    console.log(`\n💵 Price Statistics:`);
    console.log(`   Print Prices: KES ${priceStats._min.price} - KES ${priceStats._max.price} (Avg: KES ${Math.round(priceStats._avg.price)})`);
    console.log(`   Design Fees: KES ${priceStats._min.designFee} - KES ${priceStats._max.designFee} (Avg: KES ${Math.round(priceStats._avg.designFee)})`);

    console.log('\n🎉 Catalogue verification completed!');

  } catch (error) {
    console.error('❌ Error verifying data:', error);
  } finally {
    await prisma.$disconnect();
  }
}

verifySeededData(); 