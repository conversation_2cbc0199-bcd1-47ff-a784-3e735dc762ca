// Pre-configured selectors for popular website platforms
module.exports = {
  // WordPress/WooCommerce sites
  wordpress: {
    productCards: '.product, .woocommerce-loop-product__link, .product-item',
    title: '.woocommerce-loop-product__title, .product-title, h2.woocommerce-loop-product__title',
    price: '.price, .woocommerce-Price-amount, .amount',
    description: '.product-summary, .woocommerce-product-details__short-description',
    image: '.wp-post-image, .product-image img',
    category: '.product-category, .posted_in a',
    features: '.product-features li, .woocommerce-product-attributes-item'
  },

  // Shopify sites
  shopify: {
    productCards: '.product-card, .grid-view-item, .product-item',
    title: '.product-card__title, .grid-view-item__title, h3',
    price: '.product-card__price, .price, .money',
    description: '.product-card__description, .grid-view-item__meta',
    image: '.product-card__image img, .grid-view-item__image img',
    category: '.product-type, .collection-title',
    features: '.product-form__buttons, .product-single__description li'
  },

  // Custom HTML/CSS sites
  generic: {
    productCards: '.product, .item, .service, .card, [class*="product"], [class*="service"]',
    title: 'h1, h2, h3, h4, .title, .name, [class*="title"], [class*="name"]',
    price: '.price, .cost, .amount, [class*="price"], [class*="cost"]',
    description: '.description, .desc, .summary, .details, p',
    image: 'img',
    category: '.category, .type, .cat, [class*="category"]',
    features: 'li, .feature, .spec, [class*="feature"]'
  },

  // Magento sites
  magento: {
    productCards: '.product-item, .product-item-info',
    title: '.product-item-name, .product-item-link',
    price: '.price-box .price, .regular-price .price',
    description: '.product-item-description',
    image: '.product-image-photo',
    category: '.category-title, .breadcrumbs li',
    features: '.product-features li, .additional-attributes tr'
  },

  // PrestaShop sites  
  prestashop: {
    productCards: '.product-miniature, .js-product-miniature',
    title: '.product-title, h3 a',
    price: '.product-price-and-shipping .price, .current-price',
    description: '.product-description-short',
    image: '.thumbnail-container img',
    category: '.category-title, .breadcrumb li',
    features: '.product-features, .data-sheet-row'
  }
};

// Auto-detect website platform
function detectPlatform(html) {
  const htmlLower = html.toLowerCase();
  
  if (htmlLower.includes('woocommerce') || htmlLower.includes('wp-content')) {
    return 'wordpress';
  }
  if (htmlLower.includes('shopify') || htmlLower.includes('cdn.shopify')) {
    return 'shopify';
  }
  if (htmlLower.includes('magento') || htmlLower.includes('mage/js')) {
    return 'magento';
  }
  if (htmlLower.includes('prestashop') || htmlLower.includes('ps_')) {
    return 'prestashop';
  }
  
  return 'generic';
} 