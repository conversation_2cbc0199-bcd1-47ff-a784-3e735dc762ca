# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/database_name

# NextAuth Configuration
NEXTAUTH_SECRET=your_nextauth_secret_here_minimum_32_characters
NEXTAUTH_URL=http://localhost:3000

# Admin Credentials (Change these immediately)
ADMIN_USERNAME=admin
ADMIN_PASSWORD=secure_password_here
ADMIN_EMAIL=<EMAIL>

# S3/Storage Configuration
S3_PROVIDER=linode
S3_REGION=fr-par-1
S3_ENDPOINT=https://fr-par-1.linodeobjects.com
S3_BUCKET_NAME=your_bucket_name
S3_ACCESS_KEY_ID=your_access_key
S3_SECRET_ACCESS_KEY=your_secret_key

# Redis Configuration
REDIS_URL=redis://localhost:6379

# Security Settings
SECURITY_AUDIT_LOG=true
RATE_LIMIT_ENABLED=true

# App Configuration
NEXT_PUBLIC_APP_URL=https://mocky.co.ke
NODE_ENV=production
HOSTNAME=0.0.0.0
PORT=3000

# Optional: Email Configuration
SMTP_HOST=
SMTP_PORT=587
SMTP_USER=
SMTP_PASS=
SMTP_FROM=<EMAIL>

# Optional: Analytics
GOOGLE_ANALYTICS_ID=
META_PIXEL_ID= 