/**
 * Debug <PERSON>ript for Team Member Deletion
 * Run this in your browser console on the /admin/team page
 */

// Test function to debug team member deletion
async function debugTeamMemberDeletion(teamMemberId) {
  console.log('🔍 Starting team member deletion debug...');
  console.log('Team Member ID:', teamMemberId);
  console.log('ID Type:', typeof teamMemberId);
  console.log('ID Length:', teamMemberId?.length);
  
  try {
    // Test the API endpoint
    console.log('📡 Making DELETE request...');
    const response = await fetch(`/api/admin/team/${teamMemberId}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    console.log('📊 Response Status:', response.status);
    console.log('📊 Response OK:', response.ok);
    
    // Try to parse response
    let responseData;
    try {
      responseData = await response.json();
      console.log('📋 Response Data:', responseData);
    } catch (parseError) {
      console.log('⚠️ Could not parse response as JSON');
      const textResponse = await response.text();
      console.log('📄 Raw Response:', textResponse);
    }
    
    if (response.ok) {
      console.log('✅ Deletion successful!');
      return { success: true, data: responseData };
    } else {
      console.log('❌ Deletion failed!');
      return { success: false, error: responseData };
    }
    
  } catch (error) {
    console.error('🚨 Network or other error:', error);
    return { success: false, error: error.message };
  }
}

// Test authentication endpoint
async function testAuth() {
  try {
    const response = await fetch('/api/auth/me');
    const data = await response.json();
    console.log('🔐 Auth Status:', data);
    return data;
  } catch (error) {
    console.error('🚨 Auth check failed:', error);
    return null;
  }
}

// Test team member list
async function testTeamList() {
  try {
    const response = await fetch('/api/admin/team');
    const data = await response.json();
    console.log('👥 Team Members:', data);
    return data;
  } catch (error) {
    console.error('🚨 Team list failed:', error);
    return null;
  }
}

// Main debug function
async function runFullDebug() {
  console.log('🚀 Running full team deletion debug...');
  
  // Check auth
  console.log('\n1️⃣ Checking authentication...');
  await testAuth();
  
  // Get team list
  console.log('\n2️⃣ Fetching team members...');
  const teamMembers = await testTeamList();
  
  if (teamMembers && teamMembers.length > 0) {
    const firstMember = teamMembers[0];
    console.log('\n3️⃣ Testing deletion with first team member...');
    console.log('Target member:', firstMember);
    
    // Test deletion (uncomment the line below to actually test deletion)
    // await debugTeamMemberDeletion(firstMember.id);
    console.log('⚠️ Deletion test commented out for safety');
    console.log('To test deletion, uncomment the line above and run again');
  } else {
    console.log('ℹ️ No team members found to test with');
  }
}

// Console helpers
console.log('🛠️ Team Deletion Debug Tools Loaded!');
console.log('Available functions:');
console.log('- runFullDebug() - Complete debugging workflow');
console.log('- debugTeamMemberDeletion(id) - Test deletion of specific member');
console.log('- testAuth() - Check authentication status');
console.log('- testTeamList() - Fetch team members');

// Export for use
window.debugTeamTools = {
  debugTeamMemberDeletion,
  testAuth,
  testTeamList,
  runFullDebug
}; 