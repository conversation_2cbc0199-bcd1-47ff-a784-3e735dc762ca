import { PrismaClient } from '@prisma/client';
import { v4 as uuidv4 } from 'uuid';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seeding...');

  // 1. Create Admin Role and User first
  console.log('👤 Creating admin role and user...');
  const adminRoleId = uuidv4();
  const adminUserId = uuidv4();
  
  try {
    // Create admin role first
    let adminRole = await prisma.role.findUnique({
      where: { name: 'admin' }
    });

    if (!adminRole) {
      adminRole = await prisma.role.create({
        data: {
          id: adminRoleId,
          name: 'admin',
          description: 'System Administrator Role',
          permissions: ['*'], // Full permissions
        },
      });
      console.log(`✅ Created admin role: ${adminRole.name}`);
    } else {
      console.log(`⚠️ Admin role already exists: ${adminRole.name}`);
    }

    // Create admin user
    const existingUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });

    if (!existingUser) {
      const adminUser = await prisma.user.create({
        data: {
          id: adminUserId,
          email: '<EMAIL>',
          username: 'admin',
          name: 'System Administrator',
          passwordHash: await bcrypt.hash('admin123', 12),
          active: true,
          roleId: adminRole.id,
        },
      });
      console.log(`✅ Created admin user: ${adminUser.email}`);
    } else {
      console.log(`⚠️ Admin user already exists: ${existingUser.email}`);
    }
  } catch (error) {
    console.log(`⚠️ Admin user/role creation issue:`, error);
  }

  // 2. Create Services
  console.log('🛠️ Creating services...');
  const services = [
    {
      id: uuidv4(),
      name: 'Custom Website Development',
      description: 'Professional custom website development with modern technologies',
      price: 85000.00,
      category: 'Web Development',
    },
    {
      id: uuidv4(),
      name: 'E-commerce Store Setup',
      description: 'Complete e-commerce solution with payment integration',
      price: 120000.00,
      category: 'E-commerce',
    },
    {
      id: uuidv4(),
      name: 'Mobile App Development',
      description: 'Native iOS and Android mobile application development',
      price: 150000.00,
      category: 'Mobile Apps',
    },
    {
      id: uuidv4(),
      name: 'Digital Marketing Setup',
      description: 'Comprehensive digital marketing strategy and implementation',
      price: 45000.00,
      category: 'Business',
    },
    {
      id: uuidv4(),
      name: 'AI Chatbot Integration',
      description: 'Custom AI chatbot for customer service automation',
      price: 75000.00,
      category: 'AI & Machine Learning',
    },
  ];

  const createdServices = [];
  for (const service of services) {
    try {
      const srv = await prisma.service.create({
        data: service,
      });
      createdServices.push(srv);
      console.log(`✅ Created service: ${srv.name}`);
    } catch (error) {
      console.log(`⚠️ Service ${service.name} may already exist, skipping...`);
    }
  }

  // 3. Create Quotes (only if we have services)
  console.log('📄 Creating quotes...');
  if (createdServices.length >= 2) {
    const quotes = [
      {
        id: uuidv4(),
        quoteNumber: 'QUO-250529-001',
        customerName: 'TechStart Kenya Ltd',
        phoneNumber: '************',
        email: '<EMAIL>',
        totalAmount: 205000.00,
        status: 'pending',
        notes: 'Complete business website with e-commerce functionality',
        issuedAt: new Date(),
        validUntil: new Date('2025-06-29'),
      },
      {
        id: uuidv4(),
        quoteNumber: 'QUO-250528-001',
        customerName: 'Digital Solutions Inc',
        phoneNumber: '************',
        email: '<EMAIL>',
        totalAmount: 195000.00,
        status: 'accepted',
        notes: 'Mobile app development with AI integration',
        issuedAt: new Date(),
        validUntil: new Date('2025-06-28'),
      },
    ];

    for (const quote of quotes) {
      try {
        const qt = await prisma.quote.create({
          data: quote,
        });
        console.log(`✅ Created quote: ${qt.quoteNumber}`);

        // Add quote items
        if (qt.quoteNumber === 'QUO-250529-001' && createdServices.length >= 2) {
          await prisma.quoteItem.create({
            data: {
              id: uuidv4(),
              quoteId: qt.id,
              serviceId: createdServices[0]?.id,
              quantity: 1,
              unitPrice: 85000.00,
              totalPrice: 85000.00,
              description: 'Custom business website with CMS',
            },
          });
          await prisma.quoteItem.create({
            data: {
              id: uuidv4(),
              quoteId: qt.id,
              serviceId: createdServices[1]?.id,
              quantity: 1,
              unitPrice: 120000.00,
              totalPrice: 120000.00,
              description: 'E-commerce functionality with payment gateway',
            },
          });
        }
      } catch (error) {
        console.log(`⚠️ Quote ${quote.quoteNumber} may already exist, skipping...`);
      }
    }
  }

  // 4. Create Testimonials
  console.log('⭐ Creating testimonials...');
  const testimonials = [
    {
      id: 1,
      name: 'Sarah Kimani',
      location: 'Nairobi, Kenya',
      project: 'Custom Website Development',
      testimonial: 'Mocky Digital exceeded our expectations with their professional website development. The team delivered a modern, responsive site that perfectly captures our brand identity. Our online presence has significantly improved!',
      rating: 5,
      company: 'Kimani Enterprises',
      active: true,
      order: 1,
    },
    {
      id: 2,
      name: 'David Mwangi',
      location: 'Mombasa, Kenya',
      project: 'Digital Marketing Setup',
      testimonial: 'The digital marketing strategy implemented by Mocky Digital has transformed our business. Our online visibility increased by 300% within the first month. Highly professional and results-driven team!',
      rating: 5,
      company: 'Coastal Solutions Ltd',
      active: true,
      order: 2,
    },
    {
      id: 3,
      name: 'Grace Wanjiku',
      location: 'Nakuru, Kenya',
      project: 'E-commerce Store Setup',
      testimonial: 'Outstanding e-commerce development! Mocky Digital created a seamless online store with M-Pesa integration that has revolutionized how we sell our products. Customer satisfaction has never been higher.',
      rating: 5,
      company: 'Rift Valley Crafts',
      active: true,
      order: 3,
    },
  ];

  for (const testimonial of testimonials) {
    try {
      await prisma.testimonial.upsert({
        where: { id: testimonial.id },
        update: testimonial,
        create: testimonial,
      });
      console.log(`✅ Created/updated testimonial: ${testimonial.name} - ${testimonial.project}`);
    } catch (error) {
      console.log(`⚠️ Testimonial from ${testimonial.name} may already exist, skipping...`);
    }
  }

  // 5. Create Team Members
  console.log('👥 Creating team members...');
  const teamMembers = [
    {
      id: uuidv4(),
      name: 'John Doe',
      role: 'CEO & Founder',
      bio: 'Visionary leader with 15+ years of experience in technology and business development.',
      imageKey: 'team/john-doe.jpg',
      order: 1,
      linkedinUrl: 'https://linkedin.com/in/johndoe',
      twitterUrl: 'https://twitter.com/johndoe',
      githubUrl: 'https://github.com/johndoe',
      emailAddress: '<EMAIL>'
    },
    {
      id: uuidv4(),
      name: 'Jane Smith',
      role: 'CTO',
      bio: 'Tech innovator specializing in cloud architecture and AI solutions.',
      imageKey: 'team/jane-smith.jpg',
      order: 2,
      linkedinUrl: 'https://linkedin.com/in/janesmith',
      twitterUrl: 'https://twitter.com/janesmith',
      githubUrl: 'https://github.com/janesmith',
      emailAddress: '<EMAIL>'
    },
  ];

  for (const member of teamMembers) {
    try {
      await prisma.teamMember.upsert({
        where: { id: member.id },
        update: member,
        create: member,
      });
      console.log(`✅ Created/updated team member: ${member.name}`);
    } catch (error) {
      console.log(`⚠️ Team member ${member.name} may already exist, skipping...`);
    }
  }

  console.log('✅ Database seeding completed successfully!');
  console.log('');
  console.log('📊 Summary of created data:');
  console.log(`- Admin role and user`);
  console.log(`- ${services.length} Services`);
  console.log('- 2 Quotes with items');
  console.log(`- ${testimonials.length} Testimonials`);
  console.log(`- ${teamMembers.length} Team Members`);
  console.log('');
  console.log('🚀 Ready for production!');
}

main()
  .catch((e) => {
    console.error('❌ Seeding failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
