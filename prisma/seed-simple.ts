import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting simple database seeding...');

  // 1. Create basic roles
  console.log('🔐 Creating roles...');
  
  const adminRole = await prisma.role.upsert({
    where: { name: 'admin' },
    update: {},
    create: {
      name: 'admin',
      description: 'System Administrator',
      permissions: [
        'admin:all',
        'users:read', 'users:write', 'users:delete',
        'orders:read', 'orders:write', 'orders:delete',
        'design-requests:read', 'design-requests:write', 'design-requests:delete',
        'invoices:read', 'invoices:write', 'invoices:delete',
        'quotes:read', 'quotes:write', 'quotes:delete',
        'receipts:read', 'receipts:write', 'receipts:delete',
        'services:read', 'services:write', 'services:delete',
        'catalogue:read', 'catalogue:write', 'catalogue:delete',
        'analytics:read',
        'settings:read', 'settings:write'
      ]
    }
  });

  const userRole = await prisma.role.upsert({
    where: { name: 'user' },
    update: {},
    create: {
      name: 'user',
      description: 'Regular User',
      permissions: [
        'orders:read',
        'design-requests:read', 'design-requests:write',
        'invoices:read',
        'quotes:read'
      ]
    }
  });

  console.log('✅ Created roles');

  // 2. Create admin user
  console.log('👤 Creating admin user...');
  
  const adminUser = await prisma.user.upsert({
    where: { username: 'admin' },
    update: {},
    create: {
      username: 'admin',
      email: '<EMAIL>',
      name: 'System Administrator',
      passwordHash: await bcrypt.hash('password123', 12),
      roleId: adminRole.id,
      active: true
    }
  });

  console.log('✅ Created admin user');

  // 3. Create basic services
  console.log('🛠️ Creating services...');
  
  const services = [
    {
      name: 'Website Development',
      description: 'Custom website development services',
      category: 'Web Development',
      price: 50000.00
    },
    {
      name: 'Logo Design',
      description: 'Professional logo design services', 
      category: 'Graphic Design',
      price: 15000.00
    }
  ];

  for (const service of services) {
    await prisma.service.create({
      data: service
    });
  }

  console.log('✅ Created services');

  console.log('✅ Simple database seeding completed successfully!');
  console.log('');
  console.log('📊 Created:');
  console.log('- Admin and User roles');
  console.log('- Admin user (username: admin, password: password123)');
  console.log('- Basic services');
  console.log('');
  console.log('🚀 Database is ready!');
}

main()
  .catch((e) => {
    console.error('❌ Seeding failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  }); 