import { PrismaClient } from '@prisma/client';
import { v4 as uuidv4 } from 'uuid';

const prisma = new PrismaClient();

async function main() {
  console.log('🌐 Starting website portfolio seeding...');

  // Sample website portfolio data with realistic entries for Mocky Digital
  const websitePortfolioData = [
    // E-commerce websites
    {
      id: uuidv4(),
      title: 'TechHub Kenya - Electronics Store',
      description: 'A modern e-commerce platform for electronics and gadgets with integrated M-Pesa payment gateway, inventory management, and user reviews. Features include product search, wishlists, and real-time order tracking.',
      category: 'e-commerce',
      tags: ['E-commerce', 'M-Pesa', 'Electronics', 'Inventory Management', 'Next.js', 'Stripe'],
      featured: true,
      order: 1,
      url: 'https://techhub.co.ke',
      imageUrl: '/images/portfolio/websites/techhub-ecommerce.jpg',
      imageKey: 'portfolio/websites/techhub-ecommerce.jpg',
      createdBy: 'admin',
    },
    {
      id: uuidv4(),
      title: 'FreshMart Online Grocery',
      description: 'Complete online grocery platform with delivery scheduling, fresh produce categories, and subscription-based orders. Integrated with local payment systems and delivery tracking.',
      category: 'e-commerce',
      tags: ['Grocery', 'Delivery', 'Subscriptions', 'React', 'PostgreSQL', 'Payment Gateway'],
      featured: false,
      order: 2,
      url: 'https://freshmart.co.ke',
      imageUrl: '/images/portfolio/websites/freshmart-grocery.jpg',
      imageKey: 'portfolio/websites/freshmart-grocery.jpg',
      createdBy: 'admin',
    },
    {
      id: uuidv4(),
      title: 'Artisan Crafts Marketplace',
      description: 'Multi-vendor marketplace for local artisans to showcase and sell handmade crafts. Features vendor dashboards, commission management, and integrated escrow payments.',
      category: 'e-commerce',
      tags: ['Marketplace', 'Multi-vendor', 'Artisan', 'Commission System', 'Laravel', 'Vue.js'],
      featured: false,
      order: 3,
      url: 'https://artisancrafts.co.ke',
      imageUrl: '/images/portfolio/websites/artisan-marketplace.jpg',
      imageKey: 'portfolio/websites/artisan-marketplace.jpg',
      createdBy: 'admin',
    },

    // Corporate websites
    {
      id: uuidv4(),
      title: 'Safaricom Foundation',
      description: 'Corporate website for Kenya\'s leading telecommunications company foundation. Features include program showcases, impact reports, donation portal, and volunteer registration system.',
      category: 'corporate',
      tags: ['Corporate', 'Foundation', 'Impact Reports', 'Donations', 'WordPress', 'Custom Theme'],
      featured: true,
      order: 4,
      url: 'https://safaricomfoundation.org',
      imageUrl: '/images/portfolio/websites/safaricom-foundation.jpg',
      imageKey: 'portfolio/websites/safaricom-foundation.jpg',
      createdBy: 'admin',
    },
    {
      id: uuidv4(),
      title: 'Kenya Commercial Bank Corporate',
      description: 'Modern corporate website with comprehensive banking services information, branch locator, loan calculators, and secure customer portal integration.',
      category: 'corporate',
      tags: ['Banking', 'Financial Services', 'Branch Locator', 'Loan Calculator', 'Security', 'React'],
      featured: true,
      order: 5,
      url: 'https://kcb.co.ke',
      imageUrl: '/images/portfolio/websites/kcb-corporate.jpg',
      imageKey: 'portfolio/websites/kcb-corporate.jpg',
      createdBy: 'admin',
    },
    {
      id: uuidv4(),
      title: 'Equity Bank Business Portal',
      description: 'Enterprise-grade business banking portal with account management, transaction history, bulk payments, and comprehensive reporting dashboard.',
      category: 'corporate',
      tags: ['Business Banking', 'Enterprise Portal', 'Reporting', 'Angular', 'Security', 'API Integration'],
      featured: false,
      order: 6,
      url: 'https://business.equitybank.co.ke',
      imageUrl: '/images/portfolio/websites/equity-business.jpg',
      imageKey: 'portfolio/websites/equity-business.jpg',
      createdBy: 'admin',
    },

    // Non-profit websites
    {
      id: uuidv4(),
      title: 'Water.org Kenya Initiative',
      description: 'Non-profit website focused on water access projects across Kenya. Features project tracking, donation management, impact visualization, and volunteer coordination.',
      category: 'nonprofit',
      tags: ['Non-profit', 'Water Access', 'Donations', 'Impact Tracking', 'Drupal', 'Mapping'],
      featured: true,
      order: 7,
      url: 'https://water.org/kenya',
      imageUrl: '/images/portfolio/websites/water-org-kenya.jpg',
      imageKey: 'portfolio/websites/water-org-kenya.jpg',
      createdBy: 'admin',
    },
    {
      id: uuidv4(),
      title: 'Kenya Education Fund',
      description: 'Educational non-profit platform connecting sponsors with students. Includes student profiles, sponsorship tracking, progress reports, and impact stories.',
      category: 'nonprofit',
      tags: ['Education', 'Sponsorship', 'Student Profiles', 'Impact Stories', 'Django', 'PostgreSQL'],
      featured: false,
      order: 8,
      url: 'https://kenyaeducationfund.org',
      imageUrl: '/images/portfolio/websites/education-fund.jpg',
      imageKey: 'portfolio/websites/education-fund.jpg',
      createdBy: 'admin',
    },

    // Portfolio websites
    {
      id: uuidv4(),
      title: 'David Kimani - Professional Photographer',
      description: 'Stunning photography portfolio showcasing wedding, corporate, and landscape photography. Features image galleries, client testimonials, and booking system.',
      category: 'portfolio',
      tags: ['Photography', 'Portfolio', 'Image Gallery', 'Booking System', 'Lightbox', 'WordPress'],
      featured: true,
      order: 9,
      url: 'https://davidkimani.photography',
      imageUrl: '/images/portfolio/websites/photographer-portfolio.jpg',
      imageKey: 'portfolio/websites/photographer-portfolio.jpg',
      createdBy: 'admin',
    },
    {
      id: uuidv4(),
      title: 'Sarah Wanjiku - Graphic Designer',
      description: 'Creative portfolio showcasing graphic design, branding, and digital art projects. Interactive project galleries with case studies and client testimonials.',
      category: 'portfolio',
      tags: ['Graphic Design', 'Creative Portfolio', 'Case Studies', 'Interactive Gallery', 'Next.js', 'Framer Motion'],
      featured: false,
      order: 10,
      url: 'https://sarahwanjiku.design',
      imageUrl: '/images/portfolio/websites/designer-portfolio.jpg',
      imageKey: 'portfolio/websites/designer-portfolio.jpg',
      createdBy: 'admin',
    },
    {
      id: uuidv4(),
      title: 'Michael Ochieng - Software Developer',
      description: 'Modern developer portfolio showcasing full-stack projects, open source contributions, and technical blog. Features code examples and project demos.',
      category: 'portfolio',
      tags: ['Developer Portfolio', 'Full-stack', 'Open Source', 'Technical Blog', 'React', 'TypeScript'],
      featured: false,
      order: 11,
      url: 'https://michaelochieng.dev',
      imageUrl: '/images/portfolio/websites/developer-portfolio.jpg',
      imageKey: 'portfolio/websites/developer-portfolio.jpg',
      createdBy: 'admin',
    },

    // Blog websites
    {
      id: uuidv4(),
      title: 'Tech Trends Kenya',
      description: 'Technology blog covering latest trends in Kenya\'s tech ecosystem. Features include author profiles, newsletter subscription, and social media integration.',
      category: 'blog',
      tags: ['Technology Blog', 'Kenya Tech', 'Newsletter', 'Social Media', 'Ghost CMS', 'SEO'],
      featured: true,
      order: 12,
      url: 'https://techtrendskenya.com',
      imageUrl: '/images/portfolio/websites/tech-blog.jpg',
      imageKey: 'portfolio/websites/tech-blog.jpg',
      createdBy: 'admin',
    },
    {
      id: uuidv4(),
      title: 'Nairobi Food Scene',
      description: 'Food and restaurant blog featuring reviews, recipes, and culinary events in Nairobi. Includes restaurant directory and food photography gallery.',
      category: 'blog',
      tags: ['Food Blog', 'Restaurant Reviews', 'Recipes', 'Photography', 'Jekyll', 'Instagram API'],
      featured: false,
      order: 13,
      url: 'https://nairobifoodscene.com',
      imageUrl: '/images/portfolio/websites/food-blog.jpg',
      imageKey: 'portfolio/websites/food-blog.jpg',
      createdBy: 'admin',
    },

    // Landing pages
    {
      id: uuidv4(),
      title: 'KenTech Conference 2024',
      description: 'High-converting landing page for Kenya\'s premier technology conference. Features speaker lineup, agenda, ticket sales, and sponsor showcases.',
      category: 'landing-page',
      tags: ['Conference', 'Landing Page', 'Ticket Sales', 'Speaker Lineup', 'Conversion Optimization', 'Tailwind CSS'],
      featured: true,
      order: 14,
      url: 'https://kentechconf.com',
      imageUrl: '/images/portfolio/websites/conference-landing.jpg',
      imageKey: 'portfolio/websites/conference-landing.jpg',
      createdBy: 'admin',
    },
    {
      id: uuidv4(),
      title: 'EcoGreen Solar Solutions',
      description: 'Solar energy company landing page with cost calculators, installation process visualization, and lead generation forms. High conversion rate optimization.',
      category: 'landing-page',
      tags: ['Solar Energy', 'Lead Generation', 'Cost Calculator', 'CRO', 'Bootstrap', 'Analytics'],
      featured: false,
      order: 15,
      url: 'https://ecogreensolar.co.ke',
      imageUrl: '/images/portfolio/websites/solar-landing.jpg',
      imageKey: 'portfolio/websites/solar-landing.jpg',
      createdBy: 'admin',
    },
    {
      id: uuidv4(),
      title: 'FintechPay Kenya Launch',
      description: 'Product launch landing page for new fintech payment solution. Features product demo, beta signup, investor information, and media kit.',
      category: 'landing-page',
      tags: ['Fintech', 'Product Launch', 'Beta Signup', 'Payment Solution', 'Vue.js', 'A/B Testing'],
      featured: false,
      order: 16,
      url: 'https://fintechpay.co.ke',
      imageUrl: '/images/portfolio/websites/fintech-launch.jpg',
      imageKey: 'portfolio/websites/fintech-launch.jpg',
      createdBy: 'admin',
    },

    // Other category websites
    {
      id: uuidv4(),
      title: 'Kenya Tourism Virtual Experience',
      description: 'Interactive virtual tourism platform showcasing Kenya\'s attractions through 360° tours, AR experiences, and virtual safari bookings.',
      category: 'other',
      tags: ['Virtual Tourism', '360° Tours', 'AR Experience', 'Virtual Safari', 'Three.js', 'WebXR'],
      featured: true,
      order: 17,
      url: 'https://virtualkenya.com',
      imageUrl: '/images/portfolio/websites/virtual-tourism.jpg',
      imageKey: 'portfolio/websites/virtual-tourism.jpg',
      createdBy: 'admin',
    },
    {
      id: uuidv4(),
      title: 'AgriConnect Farmer Network',
      description: 'Agricultural platform connecting farmers with buyers, weather information, crop advice, and market prices. Includes SMS integration for rural farmers.',
      category: 'other',
      tags: ['Agriculture', 'Farmer Network', 'Market Prices', 'Weather API', 'SMS Integration', 'PWA'],
      featured: false,
      order: 18,
      url: 'https://agriconnect.co.ke',
      imageUrl: '/images/portfolio/websites/agri-network.jpg',
      imageKey: 'portfolio/websites/agri-network.jpg',
      createdBy: 'admin',
    },
    {
      id: uuidv4(),
      title: 'MediCare Kenya Telemedicine',
      description: 'Telemedicine platform enabling remote consultations, prescription management, and health record tracking. HIPAA-compliant with video calling integration.',
      category: 'other',
      tags: ['Telemedicine', 'Healthcare', 'Video Calling', 'HIPAA Compliant', 'WebRTC', 'Medical Records'],
      featured: false,
      order: 19,
      url: 'https://medicare.co.ke',
      imageUrl: '/images/portfolio/websites/telemedicine.jpg',
      imageKey: 'portfolio/websites/telemedicine.jpg',
      createdBy: 'admin',
    },

    // Additional showcase entries
    {
      id: uuidv4(),
      title: 'Mocky Digital Company Website',
      description: 'Our own company website featuring our services, portfolio, team information, and client testimonials. Built with Next.js 15 and modern web technologies.',
      category: 'corporate',
      tags: ['Company Website', 'Next.js 15', 'Modern Design', 'Portfolio Showcase', 'TypeScript', 'Tailwind CSS'],
      featured: true,
      order: 20,
      url: 'https://mocky.co.ke',
      imageUrl: '/images/portfolio/websites/mocky-digital.jpg',
      imageKey: 'portfolio/websites/mocky-digital.jpg',
      createdBy: 'admin',
    }
  ];

  console.log(`📝 Creating ${websitePortfolioData.length} website portfolio entries...`);

  let created = 0;
  let skipped = 0;

  for (const website of websitePortfolioData) {
    try {
      // Check if website already exists by title
      const existing = await prisma.websitePortfolio.findFirst({
        where: { title: website.title }
      });

      if (existing) {
        console.log(`⚠️ Website portfolio item "${website.title}" already exists, skipping...`);
        skipped++;
        continue;
      }

      // Create new website portfolio entry
      const portfolioItem = await prisma.websitePortfolio.create({
        data: website
      });

      console.log(`✅ Created: ${portfolioItem.title} (${portfolioItem.category})`);
      created++;
    } catch (error) {
      console.error(`❌ Error creating website portfolio item "${website.title}":`, error);
    }
  }

  // Summary
  console.log('\n📊 Website Portfolio Seeding Summary:');
  console.log(`✅ Created: ${created} items`);
  console.log(`⚠️ Skipped: ${skipped} items (already exist)`);
  console.log(`📝 Total attempted: ${websitePortfolioData.length} items`);

  // Show breakdown by category
  const categoryBreakdown = websitePortfolioData.reduce((acc, item) => {
    acc[item.category] = (acc[item.category] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  console.log('\n📂 Portfolio breakdown by category:');
  Object.entries(categoryBreakdown).forEach(([category, count]) => {
    console.log(`  ${category}: ${count} items`);
  });

  console.log('\n🎉 Website portfolio seeding completed!');
}

main()
  .catch((e) => {
    console.error('❌ Seeding failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  }); 