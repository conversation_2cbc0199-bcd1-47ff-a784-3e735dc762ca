import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function seedLogoPackages() {
  console.log('🎨 Seeding logo packages...');

  // Create logo packages based on the current hardcoded data
  const logoPackages = [
    {
      name: 'Basic Package',
      price: 5000,
      description: 'Perfect for small businesses and startups',
      features: [
        '3 Unique Concepts',
        'Professional files (AI, EPS, SVG)',
        '4 Rounds Of Revision',
        'Business Card Design',
        'Letterhead Design'
      ],
      isActive: true,
      isPopular: false,
      sortOrder: 1,
      whatsappMessage: "Hello Mocky Digital! 👋 I'm interested in the Basic Package (KSH 5,000) that includes:\n• 3 Unique Concepts\n• Professional files (AI, EPS, SVG)\n• 4 Rounds Of Revision\n• Business Card Design\n• Letterhead Design\n\nCan you help me with my logo design?"
    },
    {
      name: 'Standard Package',
      price: 15000,
      description: 'Logo design with comprehensive brand guidelines and stationery',
      features: [
        '3 Unique Concepts',
        'Professional files (AI, EPS, SVG)',
        '5 Rounds Of Revision',
        'Basic Brand Guidelines',
        'Full Stationery Kit (Business Cards, Letterhead, Envelope, Invoice)'
      ],
      isActive: true,
      isPopular: true,
      sortOrder: 2,
      whatsappMessage: "Hello Mocky Digital! 👋 I'm interested in the Standard Package (KSH 15,000) that includes:\n• 3 Unique Concepts\n• Professional files (AI, EPS, SVG)\n• 5 Rounds Of Revision\n• Basic Brand Guidelines\n• Full Stationery Kit (Business Cards, Letterhead, Envelope, Invoice)\n\nCan you help me with my professional logo design?"
    },
    {
      name: 'Premium Package',
      price: 35000,
      description: 'Complete logo design with comprehensive brand guidelines and premium stationery',
      features: [
        '3 Unique Concepts',
        'Professional files (AI, EPS, SVG)',
        '6 Rounds Of Revision',
        'Comprehensive Brand Guidelines',
        'Full Stationery Kit (Business Cards, Letterhead, Envelope, Invoice)',
        'Social Media Kit',
        'Brand Color Palette',
        'Logo Usage Guidelines'
      ],
      isActive: true,
      isPopular: false,
      sortOrder: 3,
      whatsappMessage: "Hello Mocky Digital! 👋 I'm interested in the Premium Package (KSH 35,000) that includes:\n• 3 Unique Concepts\n• Professional files (AI, EPS, SVG)\n• 6 Rounds Of Revision\n• Comprehensive Brand Guidelines\n• Full Stationery Kit (Business Cards, Letterhead, Envelope, Invoice)\n• Social Media Kit\n• Brand Color Palette\n• Logo Usage Guidelines\n\nCan you help me with my premium logo design?"
    }
  ];

  // Insert logo packages
  for (const packageData of logoPackages) {
    const existingPackage = await prisma.logoPackage.findFirst({
      where: { name: packageData.name }
    });

    if (!existingPackage) {
      await prisma.logoPackage.create({
        data: packageData
      });
      console.log(`✅ Created logo package: ${packageData.name}`);
    } else {
      console.log(`⚠️  Logo package already exists: ${packageData.name}`);
    }
  }

  console.log('🎨 Logo packages seeded successfully!');
}

async function main() {
  try {
    await seedLogoPackages();
  } catch (error) {
    console.error('❌ Error seeding logo packages:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  main()
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
} 