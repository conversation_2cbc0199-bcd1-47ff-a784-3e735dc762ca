generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id             String           @id @default(cuid())
  username       String           @unique @db.VarChar(50)
  email          String           @unique @db.Var<PERSON>har(100)
  name           String?          @db.VarChar(100)
  passwordHash   String           @db.VarChar(255)
  active         Boolean          @default(true)
  roleId         String
  lastLogin      DateTime?
  createdAt      DateTime         @default(now())
  updatedAt      DateTime         @updatedAt
  activityLogs   ActivityLog[]
  designRequests DesignRequest[]  @relation("DesignRequests")
  invoices       Invoice[]
  quotes         Quote[]
  receipts       Receipt[]
  role           Role             @relation(fields: [roleId], references: [id])

  @@map("users")
}

model Role {
  id          String   @id @default(cuid())
  name        String   @unique @db.VarChar(50)
  description String?
  permissions String[] @default([])
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  users       User[]

  @@map("roles")
}

model ActivityLog {
  id           String   @id @default(cuid())
  userId       String
  action       String   @db.VarChar(100)
  details      String?
  ipAddress    String?  @db.VarChar(50)
  userAgent    String?
  resourceType String?  @db.VarChar(50)
  resourceId   String?
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([action])
  @@index([createdAt])
  @@index([resourceType])
  @@map("activity_logs")
}

model SiteSettings {
  id                String   @id @default(cuid())
  siteName          String   @default("Mocky Digital")
  siteDescription   String?  @db.VarChar(500)
  contactEmail      String?  @db.VarChar(100)
  phoneNumber       String?  @db.VarChar(50)
  address           String?  @db.VarChar(255)
  facebookUrl       String?  @db.VarChar(255)
  twitterUrl        String?  @db.VarChar(255)
  instagramUrl      String?  @db.VarChar(255)
  linkedinUrl       String?  @db.VarChar(255)
  tiktokUrl         String?  @db.VarChar(255)
  metaTitle         String?  @db.VarChar(255)
  metaDescription   String?  @db.VarChar(500)
  googleAnalyticsId String?  @db.VarChar(50)
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  @@map("site_settings")
}

model SiteScript {
  id          String   @id @default(cuid())
  name        String   @db.VarChar(100)
  description String?  @db.VarChar(500)
  scriptType  String   @db.VarChar(20)
  content     String
  isActive    Boolean  @default(true)
  position    Int      @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([scriptType])
  @@index([isActive])
  @@index([position])
  @@map("site_scripts")
}

model StorageConfig {
  id         String   @id @default(cuid())
  provider   String   @db.VarChar(50)
  region     String?  @db.VarChar(100)
  endpoint   String?  @db.VarChar(255)
  bucketName String?  @db.VarChar(100)
  accessKey  String?  @db.VarChar(255)
  secretKey  String?  @db.VarChar(255)
  publicUrl  String?  @db.VarChar(255)
  isActive   Boolean  @default(false)
  isDefault  Boolean  @default(false)
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  @@index([provider])
  @@index([isActive])
  @@index([isDefault])
  @@map("storage_configs")
}

model ScheduledBlogPost {
  id             String    @id @default(cuid())
  title          String    @db.VarChar(255)
  content        String
  excerpt        String?
  author         String?   @db.VarChar(100)
  category       String?   @db.VarChar(100)
  tags           String[]  @default([])
  scheduledAt    DateTime
  scheduledDate  DateTime?
  status         String    @default("scheduled") @db.VarChar(20)
  tone           String?   @db.VarChar(50)
  length         String?   @db.VarChar(20)
  targetAudience String?   @db.VarChar(100)
  blogPostId     String?   @db.VarChar(100)
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt

  @@index([scheduledAt])
  @@index([status])
  @@map("scheduled_blog_posts")
}

model BlogPost {
  id             Int           @id @default(autoincrement())
  title          String        @db.VarChar(255)
  slug           String        @unique @db.VarChar(255)
  content        String
  excerpt        String?
  author         String?       @db.VarChar(100)
  category       String?       @db.VarChar(100)
  tags           String[]      @default([])
  status         String        @default("draft") @db.VarChar(20)
  featuredImage  String?       @db.VarChar(500)
  seoTitle       String?       @db.VarChar(255)
  seoDescription String?       @db.VarChar(500)
  seoKeywords    String[]      @default([])
  readingTime    Int?
  viewCount      Int           @default(0)
  publishedAt    DateTime?
  scheduledAt    DateTime?
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt
  categoryId     String?
  blogCategory   BlogCategory? @relation("BlogPostCategory", fields: [categoryId], references: [id])

  @@index([status])
  @@index([category])
  @@index([categoryId])
  @@index([publishedAt])
  @@index([createdAt])
  @@map("blog_posts")
}

model Category {
  id          Int      @id @default(autoincrement())
  name        String   @db.VarChar(100)
  slug        String   @unique @db.VarChar(100)
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("categories")
}

model BlogCategory {
  id          String         @id @default(cuid())
  name        String         @db.VarChar(100)
  slug        String         @unique @db.VarChar(100)
  description String?
  parentId    String?
  order       Int            @default(0)
  active      Boolean        @default(true)
  createdAt   DateTime       @default(now())
  updatedAt   DateTime       @updatedAt
  parent      BlogCategory?  @relation("BlogCategoryHierarchy", fields: [parentId], references: [id])
  children    BlogCategory[] @relation("BlogCategoryHierarchy")
  posts       BlogPost[]     @relation("BlogPostCategory")

  @@index([slug])
  @@index([parentId])
  @@index([active])
  @@map("blog_categories")
}

model WebsitePortfolio {
  id          String    @id @default(cuid())
  title       String    @db.VarChar(255)
  description String?
  imageUrl    String?   @db.VarChar(500)
  projectUrl  String?   @db.VarChar(500)
  category    String?   @db.VarChar(100)
  tags        String[]  @default([])
  featured    Boolean   @default(false)
  order       Int       @default(0)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  createdBy   String?   @db.VarChar(100)
  deletedAt   DateTime?
  imageKey    String?   @db.VarChar(500)
  updatedBy   String?   @db.VarChar(100)
  url         String?   @db.VarChar(500)

  @@index([category])
  @@index([featured])
  @@map("website_portfolio")
}

model TeamMember {
  id           String   @id @default(cuid())
  name         String   @db.VarChar(100)
  role         String   @db.VarChar(100)
  bio          String
  imageKey     String
  linkedinUrl  String?  @db.VarChar(255)
  twitterUrl   String?  @db.VarChar(255)
  githubUrl    String?  @db.VarChar(255)
  emailAddress String?  @db.VarChar(100)
  order        Int      @default(0)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  @@map("team_members")
}

model Testimonial {
  id          Int      @id @default(autoincrement())
  name        String   @db.VarChar(100)
  company     String?  @db.VarChar(100)
  location    String   @db.VarChar(100)
  project     String   @db.VarChar(255)
  testimonial String
  rating      Int      @default(5)
  active      Boolean  @default(true)
  order       Int      @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("testimonials")
}

model Service {
  id           String        @id @default(cuid())
  name         String        @db.VarChar(100)
  description  String?
  price        Decimal       @db.Decimal(10, 2)
  category     String        @db.VarChar(50)
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt
  invoiceItems InvoiceItem[]
  quoteItems   QuoteItem[]
  receiptItems ReceiptItem[]

  @@map("services")
}

model Unit {
  id          String      @id @default(cuid())
  name        String      @unique @db.VarChar(50)
  displayName String      @db.VarChar(50)
  plural      String      @db.VarChar(50)
  shortForm   String?     @db.VarChar(10)
  category    String      @default("general") @db.VarChar(50)
  active      Boolean     @default(true)
  order       Int         @default(0)
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  catalogue   Catalogue[]

  @@index([active])
  @@index([category])
  @@map("units")
}

model Catalogue {
  id            Int      @id @default(autoincrement())
  service       String   @db.VarChar(255)
  description   String?
  price         Int      // Print/production price
  designFee     Int      @default(0) // Design service fee
  category      String?  @default("Other") @db.VarChar(100)
  features      String[] @default([])
  imageUrl      String?
  imageUrl2     String?
  imageUrl3     String?
  popular       Boolean? @default(false)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  maxMeters     Decimal? @db.Decimal(8, 2)
  maxQuantity   Int?
  minMeters     Decimal? @db.Decimal(5, 2)
  minQuantity   Int?     @default(1)
  paperTypes    Json?
  pricePerMeter Decimal? @db.Decimal(8, 2)
  pricingTiers  Json?
  pricingType   String   @default("fixed") @db.VarChar(50)
  unitType      String?  @db.VarChar(50) // Deprecated: use unitId instead
  unitId        String?  // Reference to Unit model
  orders        Order[]
  unit          Unit?    @relation(fields: [unitId], references: [id])

  @@index([category])
  @@index([popular])
  @@index([pricingType])
  @@index([unitId])
  @@map("catalogue")
}

model PaperType {
  id            String   @id @default(cuid())
  name          String   @db.VarChar(100)
  grammage      String?  @db.VarChar(50)
  oneSidedPrice Decimal  @db.Decimal(8, 2)
  twoSidedPrice Decimal  @db.Decimal(8, 2)
  category      String   @default("paper") @db.VarChar(50)
  active        Boolean  @default(true)
  order         Int      @default(0)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  @@index([category])
  @@index([active])
  @@map("paper_types")
}

model PricingRule {
  id        String   @id @default(cuid())
  name      String   @db.VarChar(100)
  type      String   @db.VarChar(50)
  category  String?  @db.VarChar(100)
  rules     Json
  active    Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([type])
  @@index([category])
  @@index([active])
  @@map("pricing_rules")
}

model DesignRequest {
  id             String    @id @default(cuid())
  customerName   String    @db.VarChar(255)
  email          String    @db.VarChar(100)
  phone          String    @db.VarChar(50)
  productType    String    @db.VarChar(100)
  quantity       Int
  specifications String?
  urgency        String    @default("standard") @db.VarChar(50)
  budget         Decimal?  @db.Decimal(8, 2)
  designType     String    @db.VarChar(100)
  status         String    @default("pending") @db.VarChar(50)
  designFee      Decimal   @default(0) @db.Decimal(8, 2)
  printCost      Decimal   @default(0) @db.Decimal(8, 2)
  totalCost      Decimal   @default(0) @db.Decimal(8, 2)
  orderId        String?
  assignedTo     String?
  referenceFiles String[]  @default([])
  designFiles    String[]  @default([])
  requestedAt    DateTime  @default(now())
  completedAt    DateTime?
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt
  designer       User?     @relation("DesignRequests", fields: [assignedTo], references: [id])
  order          Order?    @relation(fields: [orderId], references: [id])

  @@index([status])
  @@index([designType])
  @@index([requestedAt])
  @@map("design_requests")
}

model Order {
  id               String          @id @default(cuid())
  orderNumber      String          @unique @db.VarChar(50)
  customerName     String          @db.VarChar(255)
  email            String          @db.VarChar(100)
  phone            String          @db.VarChar(50)
  productId        Int
  productName      String          @db.VarChar(255)
  quantity         Int
  customQuantity   Boolean         @default(false)
  unitPrice        Decimal         @db.Decimal(8, 2)
  designFee        Decimal         @default(0) @db.Decimal(8, 2)
  subtotal         Decimal         @db.Decimal(8, 2)
  totalAmount      Decimal         @db.Decimal(8, 2)
  paperType        String?         @db.VarChar(100)
  printingSide     String?         @db.VarChar(50)
  meters           Decimal?        @db.Decimal(5, 2)
  specifications   Json?
  needsDesign      Boolean         @default(false)
  designBrief      String?
  artworkFiles     String[]        @default([])
  status           String          @default("pending") @db.VarChar(50)
  paymentStatus    String          @default("pending") @db.VarChar(50)
  deliveryMethod   String?         @db.VarChar(50)
  deliveryAddress  String?
  notes            String?
  createdAt        DateTime        @default(now())
  updatedAt        DateTime        @updatedAt
  confirmedAt      DateTime?
  completedAt      DateTime?
  artworkFileCount Int             @default(0)
  designOnly       Boolean         @default(false)
  designRequests   DesignRequest[]
  product          Catalogue       @relation(fields: [productId], references: [id])

  @@index([status])
  @@index([createdAt])
  @@index([customerName])
  @@map("orders")
}

model Invoice {
  id             String        @id @default(cuid())
  invoiceNumber  String        @unique @db.VarChar(50)
  userId         String?
  totalAmount    Decimal       @db.Decimal(10, 2)
  taxAmount      Decimal       @default(0) @db.Decimal(10, 2)
  discountAmount Decimal       @default(0) @db.Decimal(10, 2)
  status         String        @default("draft") @db.VarChar(20)
  notes          String?
  dueDate        DateTime
  issuedAt       DateTime      @default(now())
  paidAt         DateTime?
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt
  amountPaid     Decimal       @default(0) @db.Decimal(10, 2)
  balance        Decimal       @default(0) @db.Decimal(10, 2)
  customerName   String?       @db.VarChar(255)
  email          String?       @db.VarChar(100)
  pdfUrl         String?       @db.VarChar(500)
  phoneNumber    String?       @db.VarChar(50)
  quoteId        String?       @db.VarChar(100)
  items          InvoiceItem[]
  user           User?         @relation(fields: [userId], references: [id])

  @@index([status])
  @@index([dueDate])
  @@map("invoices")
}

model InvoiceItem {
  id          String   @id @default(cuid())
  invoiceId   String
  serviceId   String?
  description String   @db.VarChar(255)
  quantity    Int      @default(1)
  unitPrice   Decimal  @db.Decimal(10, 2)
  totalPrice  Decimal  @db.Decimal(10, 2)
  invoice     Invoice  @relation(fields: [invoiceId], references: [id], onDelete: Cascade)
  service     Service? @relation(fields: [serviceId], references: [id])

  @@map("invoice_items")
}

model Quote {
  id             String      @id @default(cuid())
  quoteNumber    String      @unique @db.VarChar(50)
  userId         String?
  totalAmount    Decimal     @db.Decimal(10, 2)
  taxAmount      Decimal     @default(0) @db.Decimal(10, 2)
  discountAmount Decimal     @default(0) @db.Decimal(10, 2)
  status         String      @default("draft") @db.VarChar(20)
  notes          String?
  validUntil     DateTime
  createdAt      DateTime    @default(now())
  updatedAt      DateTime    @updatedAt
  customerName   String?     @db.VarChar(255)
  discountType   String?     @db.VarChar(20)
  discountValue  Decimal?    @db.Decimal(10, 4)
  email          String?     @db.VarChar(100)
  issuedAt       DateTime    @default(now())
  pdfUrl         String?     @db.VarChar(500)
  phoneNumber    String?     @db.VarChar(50)
  subtotalAmount Decimal     @default(0) @db.Decimal(10, 2)
  items          QuoteItem[]
  user           User?       @relation(fields: [userId], references: [id])

  @@index([status])
  @@map("quotes")
}

model QuoteItem {
  id          String   @id @default(cuid())
  quoteId     String
  serviceId   String?
  description String   @db.VarChar(255)
  quantity    Int      @default(1)
  unitPrice   Decimal  @db.Decimal(10, 2)
  totalPrice  Decimal  @db.Decimal(10, 2)
  itemName    String?  @db.VarChar(255)
  itemType    String   @default("service") @db.VarChar(50)
  quote       Quote    @relation(fields: [quoteId], references: [id], onDelete: Cascade)
  service     Service? @relation(fields: [serviceId], references: [id])

  @@map("quote_items")
}

model Receipt {
  id             String        @id @default(cuid())
  receiptNumber  String        @unique @db.VarChar(50)
  userId         String?
  totalAmount    Decimal       @db.Decimal(10, 2)
  taxAmount      Decimal       @default(0) @db.Decimal(10, 2)
  discountAmount Decimal       @default(0) @db.Decimal(10, 2)
  paymentMethod  String?       @db.VarChar(50)
  notes          String?
  issuedAt       DateTime      @default(now())
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt
  amountPaid     Decimal?      @db.Decimal(10, 2)
  balance        Decimal?      @db.Decimal(10, 2)
  customerName   String?       @db.VarChar(255)
  email          String?       @db.VarChar(100)
  paidAt         DateTime?
  pdfUrl         String?       @db.VarChar(500)
  phoneNumber    String?       @db.VarChar(50)
  status         String?       @default("issued") @db.VarChar(20)
  transactionId  String?       @db.VarChar(100)
  items          ReceiptItem[]
  user           User?         @relation(fields: [userId], references: [id])

  @@index([issuedAt])
  @@map("receipts")
}

model ReceiptItem {
  id              String   @id @default(cuid())
  receiptId       String
  serviceId       String?
  description     String   @db.VarChar(255)
  quantity        Int      @default(1)
  unitPrice       Decimal  @db.Decimal(10, 2)
  totalPrice      Decimal  @db.Decimal(10, 2)
  discountRate    Decimal  @default(0) @db.Decimal(5, 2)
  discount        Decimal  @default(0) @db.Decimal(10, 2)
  discountType    String   @default("fixed") @db.VarChar(20)
  isCustomService Boolean  @default(false)
  receipt         Receipt  @relation(fields: [receiptId], references: [id], onDelete: Cascade)
  service         Service? @relation(fields: [serviceId], references: [id])

  @@map("receipt_items")
}

model Pricing {
  id          String   @id @default(cuid())
  name        String   @db.VarChar(255)
  description String?
  basePrice   Decimal  @db.Decimal(10, 2)
  category    String?  @db.VarChar(100)
  unit        String?  @db.VarChar(50)
  active      Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([category])
  @@index([active])
  @@map("pricing")
}

model DatabaseBackup {
  id        String   @id @default(cuid())
  filename  String   @db.VarChar(255)
  size      Int
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("database_backups")
}

// Logo Package Management Models
model LogoPackage {
  id                String      @id @default(cuid())
  name              String      @db.VarChar(100)
  price             Decimal     @db.Decimal(10, 2)
  description       String?     @db.VarChar(500)
  features          String[]    @default([])
  isActive          Boolean     @default(true)
  isPopular         Boolean     @default(false)
  sortOrder         Int         @default(0)
  whatsappMessage   String?     @db.Text
  createdAt         DateTime    @default(now())
  updatedAt         DateTime    @updatedAt
  logoOrders        LogoOrder[]

  @@index([isActive])
  @@index([sortOrder])
  @@map("logo_packages")
}

model LogoOrder {
  id                String      @id @default(cuid())
  orderNumber       String      @unique @db.VarChar(50)
  packageId         String
  customerName      String      @db.VarChar(255)
  email             String      @db.VarChar(100)
  phone             String?     @db.VarChar(50)
  businessName      String      @db.VarChar(255)
  industry          String?     @db.VarChar(100)
  logoType          String?     @db.VarChar(50)
  slogan            String?     @db.VarChar(255)
  additionalInfo    String?     @db.Text
  totalAmount       Decimal     @db.Decimal(10, 2)
  status            String      @default("pending") @db.VarChar(50)
  paymentStatus     String      @default("pending") @db.VarChar(50)
  deliveryMethod    String      @default("email") @db.VarChar(50)
  whatsappSent      Boolean     @default(false)
  adminNotified     Boolean     @default(false)
  designFiles       String[]    @default([])
  revisionCount     Int         @default(0)
  notes             String?     @db.Text
  createdAt         DateTime    @default(now())
  updatedAt         DateTime    @updatedAt
  completedAt       DateTime?
  package           LogoPackage @relation(fields: [packageId], references: [id])

  @@index([status])
  @@index([paymentStatus])
  @@index([createdAt])
  @@index([customerName])
  @@index([packageId])
  @@map("logo_orders")
}
