-- CreateTable
CREATE TABLE "database_backups" (
    "id" TEXT NOT NULL,
    "filename" VARCHAR(255) NOT NULL,
    "size" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "database_backups_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "logo_packages" (
    "id" TEXT NOT NULL,
    "name" VARCHAR(100) NOT NULL,
    "price" DECIMAL(10,2) NOT NULL,
    "description" VARCHAR(500),
    "features" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "isPopular" BOOLEAN NOT NULL DEFAULT false,
    "sortOrder" INTEGER NOT NULL DEFAULT 0,
    "whatsappMessage" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "logo_packages_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "logo_orders" (
    "id" TEXT NOT NULL,
    "orderNumber" VARCHAR(50) NOT NULL,
    "packageId" TEXT NOT NULL,
    "customerName" VARCHAR(255) NOT NULL,
    "email" VARCHAR(100) NOT NULL,
    "phone" VARCHAR(50),
    "businessName" VARCHAR(255) NOT NULL,
    "industry" VARCHAR(100),
    "logoType" VARCHAR(50),
    "slogan" VARCHAR(255),
    "additionalInfo" TEXT,
    "totalAmount" DECIMAL(10,2) NOT NULL,
    "status" VARCHAR(50) NOT NULL DEFAULT 'pending',
    "paymentStatus" VARCHAR(50) NOT NULL DEFAULT 'pending',
    "deliveryMethod" VARCHAR(50) NOT NULL DEFAULT 'email',
    "whatsappSent" BOOLEAN NOT NULL DEFAULT false,
    "adminNotified" BOOLEAN NOT NULL DEFAULT false,
    "designFiles" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "revisionCount" INTEGER NOT NULL DEFAULT 0,
    "notes" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "completedAt" TIMESTAMP(3),

    CONSTRAINT "logo_orders_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "logo_packages_isActive_idx" ON "logo_packages"("isActive");

-- CreateIndex
CREATE INDEX "logo_packages_sortOrder_idx" ON "logo_packages"("sortOrder");

-- CreateIndex
CREATE UNIQUE INDEX "logo_orders_orderNumber_key" ON "logo_orders"("orderNumber");

-- CreateIndex
CREATE INDEX "logo_orders_status_idx" ON "logo_orders"("status");

-- CreateIndex
CREATE INDEX "logo_orders_paymentStatus_idx" ON "logo_orders"("paymentStatus");

-- CreateIndex
CREATE INDEX "logo_orders_createdAt_idx" ON "logo_orders"("createdAt");

-- CreateIndex
CREATE INDEX "logo_orders_customerName_idx" ON "logo_orders"("customerName");

-- CreateIndex
CREATE INDEX "logo_orders_packageId_idx" ON "logo_orders"("packageId");

-- AddForeignKey
ALTER TABLE "logo_orders" ADD CONSTRAINT "logo_orders_packageId_fkey" FOREIGN KEY ("packageId") REFERENCES "logo_packages"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
