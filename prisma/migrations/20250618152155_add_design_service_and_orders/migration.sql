-- CreateTable
CREATE TABLE "design_requests" (
    "id" TEXT NOT NULL,
    "customerName" VARCHAR(255) NOT NULL,
    "email" VARCHAR(100) NOT NULL,
    "phone" VARCHAR(50) NOT NULL,
    "productType" VARCHAR(100) NOT NULL,
    "quantity" INTEGER NOT NULL,
    "specifications" TEXT,
    "urgency" VARCHAR(50) NOT NULL DEFAULT 'standard',
    "budget" DECIMAL(8,2),
    "designType" VARCHAR(100) NOT NULL,
    "status" VARCHAR(50) NOT NULL DEFAULT 'pending',
    "designFee" DECIMAL(8,2) NOT NULL DEFAULT 0,
    "printCost" DECIMAL(8,2) NOT NULL DEFAULT 0,
    "totalCost" DECIMAL(8,2) NOT NULL DEFAULT 0,
    "orderId" TEXT,
    "assignedTo" TEXT,
    "referenceFiles" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "designFiles" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "requestedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "completedAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "design_requests_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "orders" (
    "id" TEXT NOT NULL,
    "orderNumber" VARCHAR(50) NOT NULL,
    "customerName" VARCHAR(255) NOT NULL,
    "email" VARCHAR(100) NOT NULL,
    "phone" VARCHAR(50) NOT NULL,
    "productId" INTEGER NOT NULL,
    "productName" VARCHAR(255) NOT NULL,
    "quantity" INTEGER NOT NULL,
    "customQuantity" BOOLEAN NOT NULL DEFAULT false,
    "unitPrice" DECIMAL(8,2) NOT NULL,
    "designFee" DECIMAL(8,2) NOT NULL DEFAULT 0,
    "subtotal" DECIMAL(8,2) NOT NULL,
    "totalAmount" DECIMAL(8,2) NOT NULL,
    "paperType" VARCHAR(100),
    "printingSide" VARCHAR(50),
    "meters" DECIMAL(5,2),
    "specifications" JSONB,
    "needsDesign" BOOLEAN NOT NULL DEFAULT false,
    "designBrief" TEXT,
    "artworkFiles" TEXT[] DEFAULT ARRAY[]::TEXT[],
    "status" VARCHAR(50) NOT NULL DEFAULT 'pending',
    "paymentStatus" VARCHAR(50) NOT NULL DEFAULT 'pending',
    "deliveryMethod" VARCHAR(50),
    "deliveryAddress" TEXT,
    "notes" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "confirmedAt" TIMESTAMP(3),
    "completedAt" TIMESTAMP(3),

    CONSTRAINT "orders_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "design_requests_status_idx" ON "design_requests"("status");

-- CreateIndex
CREATE INDEX "design_requests_designType_idx" ON "design_requests"("designType");

-- CreateIndex
CREATE INDEX "design_requests_requestedAt_idx" ON "design_requests"("requestedAt");

-- CreateIndex
CREATE UNIQUE INDEX "orders_orderNumber_key" ON "orders"("orderNumber");

-- CreateIndex
CREATE INDEX "orders_status_idx" ON "orders"("status");

-- CreateIndex
CREATE INDEX "orders_createdAt_idx" ON "orders"("createdAt");

-- CreateIndex
CREATE INDEX "orders_customerName_idx" ON "orders"("customerName");

-- AddForeignKey
ALTER TABLE "design_requests" ADD CONSTRAINT "design_requests_assignedTo_fkey" FOREIGN KEY ("assignedTo") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "design_requests" ADD CONSTRAINT "design_requests_orderId_fkey" FOREIGN KEY ("orderId") REFERENCES "orders"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "orders" ADD CONSTRAINT "orders_productId_fkey" FOREIGN KEY ("productId") REFERENCES "catalogue"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
