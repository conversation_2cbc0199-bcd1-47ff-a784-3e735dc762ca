/*
  Warnings:

  - You are about to drop the column `clientId` on the `invoices` table. All the data in the column will be lost.
  - You are about to drop the column `clientId` on the `quotes` table. All the data in the column will be lost.
  - You are about to drop the column `clientId` on the `receipts` table. All the data in the column will be lost.
  - You are about to drop the `client_communications` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `client_documents` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `client_feedback` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `client_notifications` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `clients` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `communication_attachments` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `project_documents` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `project_members` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `project_milestones` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `projects` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `tasks` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `time_entries` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "client_communications" DROP CONSTRAINT "client_communications_clientId_fkey";

-- DropForeignKey
ALTER TABLE "client_communications" DROP CONSTRAINT "client_communications_createdBy_fkey";

-- DropForeignKey
ALTER TABLE "client_communications" DROP CONSTRAINT "client_communications_projectId_fkey";

-- DropForeignKey
ALTER TABLE "client_documents" DROP CONSTRAINT "client_documents_clientId_fkey";

-- DropForeignKey
ALTER TABLE "client_documents" DROP CONSTRAINT "client_documents_uploadedBy_fkey";

-- DropForeignKey
ALTER TABLE "client_feedback" DROP CONSTRAINT "client_feedback_clientId_fkey";

-- DropForeignKey
ALTER TABLE "client_feedback" DROP CONSTRAINT "client_feedback_projectId_fkey";

-- DropForeignKey
ALTER TABLE "client_notifications" DROP CONSTRAINT "client_notifications_clientId_fkey";

-- DropForeignKey
ALTER TABLE "communication_attachments" DROP CONSTRAINT "communication_attachments_communicationId_fkey";

-- DropForeignKey
ALTER TABLE "invoices" DROP CONSTRAINT "invoices_clientId_fkey";

-- DropForeignKey
ALTER TABLE "project_documents" DROP CONSTRAINT "project_documents_projectId_fkey";

-- DropForeignKey
ALTER TABLE "project_documents" DROP CONSTRAINT "project_documents_uploadedBy_fkey";

-- DropForeignKey
ALTER TABLE "project_members" DROP CONSTRAINT "project_members_projectId_fkey";

-- DropForeignKey
ALTER TABLE "project_members" DROP CONSTRAINT "project_members_userId_fkey";

-- DropForeignKey
ALTER TABLE "project_milestones" DROP CONSTRAINT "project_milestones_projectId_fkey";

-- DropForeignKey
ALTER TABLE "projects" DROP CONSTRAINT "projects_clientId_fkey";

-- DropForeignKey
ALTER TABLE "quotes" DROP CONSTRAINT "quotes_clientId_fkey";

-- DropForeignKey
ALTER TABLE "receipts" DROP CONSTRAINT "receipts_clientId_fkey";

-- DropForeignKey
ALTER TABLE "tasks" DROP CONSTRAINT "tasks_assignedTo_fkey";

-- DropForeignKey
ALTER TABLE "tasks" DROP CONSTRAINT "tasks_projectId_fkey";

-- DropForeignKey
ALTER TABLE "time_entries" DROP CONSTRAINT "time_entries_projectId_fkey";

-- DropForeignKey
ALTER TABLE "time_entries" DROP CONSTRAINT "time_entries_taskId_fkey";

-- DropForeignKey
ALTER TABLE "time_entries" DROP CONSTRAINT "time_entries_userId_fkey";

-- DropIndex
DROP INDEX "invoices_clientId_idx";

-- DropIndex
DROP INDEX "quotes_clientId_idx";

-- DropIndex
DROP INDEX "receipts_clientId_idx";

-- AlterTable
ALTER TABLE "invoices" DROP COLUMN "clientId";

-- AlterTable
ALTER TABLE "quotes" DROP COLUMN "clientId";

-- AlterTable
ALTER TABLE "receipts" DROP COLUMN "clientId";

-- DropTable
DROP TABLE "client_communications";

-- DropTable
DROP TABLE "client_documents";

-- DropTable
DROP TABLE "client_feedback";

-- DropTable
DROP TABLE "client_notifications";

-- DropTable
DROP TABLE "clients";

-- DropTable
DROP TABLE "communication_attachments";

-- DropTable
DROP TABLE "project_documents";

-- DropTable
DROP TABLE "project_members";

-- DropTable
DROP TABLE "project_milestones";

-- DropTable
DROP TABLE "projects";

-- DropTable
DROP TABLE "tasks";

-- DropTable
DROP TABLE "time_entries";
