-- DropFore<PERSON><PERSON><PERSON>
ALTER TABLE "invoices" DROP CONSTRAINT "invoices_clientId_fkey";

-- DropForeignKey
ALTER TABLE "invoices" DROP CONSTRAINT "invoices_userId_fkey";

-- DropForeignKey
ALTER TABLE "quotes" DROP CONSTRAINT "quotes_clientId_fkey";

-- DropForeignKey
ALTER TABLE "quotes" DROP CONSTRAINT "quotes_userId_fkey";

-- DropForeignKey
ALTER TABLE "receipts" DROP CONSTRAINT "receipts_clientId_fkey";

-- DropForeignKey
ALTER TABLE "receipts" DROP CONSTRAINT "receipts_userId_fkey";

-- AlterTable
ALTER TABLE "catalogue" ADD COLUMN     "maxMeters" DECIMAL(8,2),
ADD COLUMN     "maxQuantity" INTEGER,
ADD COLUMN     "minMeters" DECIMAL(5,2),
ADD COLUMN     "minQuantity" INTEGER DEFAULT 1,
ADD COLUMN     "paperTypes" JSONB,
ADD COLUMN     "pricePerMeter" DECIMAL(8,2),
ADD COLUMN     "pricingTiers" JSONB,
ADD COLUMN     "pricingType" VARCHAR(50) NOT NULL DEFAULT 'fixed',
ADD COLUMN     "unitType" VARCHAR(50);

-- AlterTable
ALTER TABLE "invoices" ADD COLUMN     "amountPaid" DECIMAL(10,2) NOT NULL DEFAULT 0,
ADD COLUMN     "balance" DECIMAL(10,2) NOT NULL DEFAULT 0,
ADD COLUMN     "customerName" VARCHAR(255),
ADD COLUMN     "email" VARCHAR(100),
ADD COLUMN     "pdfUrl" VARCHAR(500),
ADD COLUMN     "phoneNumber" VARCHAR(50),
ADD COLUMN     "quoteId" VARCHAR(100),
ALTER COLUMN "clientId" DROP NOT NULL,
ALTER COLUMN "userId" DROP NOT NULL;

-- AlterTable
ALTER TABLE "quote_items" ADD COLUMN     "itemName" VARCHAR(255),
ADD COLUMN     "itemType" VARCHAR(50) NOT NULL DEFAULT 'service';

-- AlterTable
ALTER TABLE "quotes" ADD COLUMN     "customerName" VARCHAR(255),
ADD COLUMN     "discountType" VARCHAR(20),
ADD COLUMN     "discountValue" DECIMAL(10,4),
ADD COLUMN     "email" VARCHAR(100),
ADD COLUMN     "issuedAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "pdfUrl" VARCHAR(500),
ADD COLUMN     "phoneNumber" VARCHAR(50),
ADD COLUMN     "subtotalAmount" DECIMAL(10,2) NOT NULL DEFAULT 0,
ALTER COLUMN "clientId" DROP NOT NULL,
ALTER COLUMN "userId" DROP NOT NULL;

-- AlterTable
ALTER TABLE "receipt_items" ADD COLUMN     "discount" DECIMAL(10,2) NOT NULL DEFAULT 0,
ADD COLUMN     "discountType" VARCHAR(20) NOT NULL DEFAULT 'fixed',
ADD COLUMN     "isCustomService" BOOLEAN NOT NULL DEFAULT false;

-- AlterTable
ALTER TABLE "receipts" ADD COLUMN     "amountPaid" DECIMAL(10,2),
ADD COLUMN     "balance" DECIMAL(10,2),
ADD COLUMN     "customerName" VARCHAR(255),
ADD COLUMN     "email" VARCHAR(100),
ADD COLUMN     "paidAt" TIMESTAMP(3),
ADD COLUMN     "pdfUrl" VARCHAR(500),
ADD COLUMN     "phoneNumber" VARCHAR(50),
ADD COLUMN     "status" VARCHAR(20) DEFAULT 'issued',
ADD COLUMN     "transactionId" VARCHAR(100),
ALTER COLUMN "clientId" DROP NOT NULL,
ALTER COLUMN "userId" DROP NOT NULL,
ALTER COLUMN "paymentMethod" DROP NOT NULL;

-- CreateTable
CREATE TABLE "paper_types" (
    "id" TEXT NOT NULL,
    "name" VARCHAR(100) NOT NULL,
    "grammage" VARCHAR(50),
    "oneSidedPrice" DECIMAL(8,2) NOT NULL,
    "twoSidedPrice" DECIMAL(8,2) NOT NULL,
    "category" VARCHAR(50) NOT NULL DEFAULT 'paper',
    "active" BOOLEAN NOT NULL DEFAULT true,
    "order" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "paper_types_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "pricing_rules" (
    "id" TEXT NOT NULL,
    "name" VARCHAR(100) NOT NULL,
    "type" VARCHAR(50) NOT NULL,
    "category" VARCHAR(100),
    "rules" JSONB NOT NULL,
    "active" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "pricing_rules_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "paper_types_category_idx" ON "paper_types"("category");

-- CreateIndex
CREATE INDEX "paper_types_active_idx" ON "paper_types"("active");

-- CreateIndex
CREATE INDEX "pricing_rules_type_idx" ON "pricing_rules"("type");

-- CreateIndex
CREATE INDEX "pricing_rules_category_idx" ON "pricing_rules"("category");

-- CreateIndex
CREATE INDEX "pricing_rules_active_idx" ON "pricing_rules"("active");

-- CreateIndex
CREATE INDEX "catalogue_pricingType_idx" ON "catalogue"("pricingType");

-- AddForeignKey
ALTER TABLE "invoices" ADD CONSTRAINT "invoices_clientId_fkey" FOREIGN KEY ("clientId") REFERENCES "clients"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "invoices" ADD CONSTRAINT "invoices_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "quotes" ADD CONSTRAINT "quotes_clientId_fkey" FOREIGN KEY ("clientId") REFERENCES "clients"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "quotes" ADD CONSTRAINT "quotes_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "receipts" ADD CONSTRAINT "receipts_clientId_fkey" FOREIGN KEY ("clientId") REFERENCES "clients"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "receipts" ADD CONSTRAINT "receipts_userId_fkey" FOREIGN KEY ("userId") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;
