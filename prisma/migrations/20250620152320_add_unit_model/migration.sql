-- AlterTable
ALTER TABLE "catalogue" ADD COLUMN     "unitId" TEXT;

-- CreateTable
CREATE TABLE "units" (
    "id" TEXT NOT NULL,
    "name" VARCHAR(50) NOT NULL,
    "displayName" VARCHAR(50) NOT NULL,
    "plural" VARCHAR(50) NOT NULL,
    "shortForm" VARCHAR(10),
    "category" VARCHAR(50) NOT NULL DEFAULT 'general',
    "active" BOOLEAN NOT NULL DEFAULT true,
    "order" INTEGER NOT NULL DEFAULT 0,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "units_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "units_name_key" ON "units"("name");

-- CreateIndex
CREATE INDEX "units_active_idx" ON "units"("active");

-- CreateIndex
CREATE INDEX "units_category_idx" ON "units"("category");

-- CreateIndex
CREATE INDEX "catalogue_unitId_idx" ON "catalogue"("unitId");

-- AddForeignKey
ALTER TABLE "catalogue" ADD CONSTRAINT "catalogue_unitId_fkey" FOREIGN KEY ("unitId") REFERENCES "units"("id") ON DELETE SET NULL ON UPDATE CASCADE;
