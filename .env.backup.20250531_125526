# ====================================
# DATABASE CONFIGURATION
# ====================================
# DATABASE_URL="postgresql://postgres:postgres@localhost:5432/mocky"

# ====================================
# APPLICATION CONFIGURATION
# ====================================
NODE_ENV=development
PORT=3000

# ====================================
# AUTHENTICATION CONFIGURATION
# ====================================
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=NZD0KjnLPSA6w/Drd5mMq3d7L8o+eT5a3G3Aaxz/ZjY=  # ⚠️ Keep this secret

# ====================================
# SITE CONFIGURATION
# ====================================
SITE_URL=localhost:3000


# ====================================
# ADMIN CONFIGURATION
# ====================================
ADMIN_USERNAME=admin
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=Jack75522r  # ⚠️ Change before production or use ENV-based user creation

# ====================================
# S3 CONFIGURATION (LINODE OBJECT STORAGE)
# ====================================
NEXT_PUBLIC_S3_REGION="fr-par-1"
NEXT_PUBLIC_S3_ENDPOINT="https://fr-par-1.linodeobjects.com"
NEXT_PUBLIC_S3_BUCKET="mocky2"
NEXT_PUBLIC_S3_ACCESS_KEY="RFUCVVCBIB34H47YSOAP"
NEXT_PUBLIC_S3_SECRET_KEY="XK969rMKHSN37EZugmDFSyjjPp487wHEjMgzhLNa"
NEXT_PUBLIC_S3_PUBLIC_URL="https://fr-par-1.linodeobjects.com/mocky2"
NEXT_PUBLIC_USE_S3="true"



# Additional S3 variables for module compatibility
S3_ACCESS_KEY_ID="RFUCVVCBIB34H47YSOAP"
S3_SECRET_ACCESS_KEY="XK969rMKHSN37EZugmDFSyjjPp487wHEjMgzhLNa"

# ====================================
# EMAIL CONFIGURATION (OPTIONAL)
# ====================================
# Email Configuration (Gmail with App Password)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_SECURE="false"
SMTP_USER="<EMAIL>"
SMTP_PASS="sncr fuit apix bitk"
SMTP_FROM="Web Scraper <<EMAIL>>"

# Gmail App Password (backup)
GMAIL_USER="<EMAIL>"
GMAIL_PASS="sncr fuit apix bitk"

# ====================================
# SECURITY
# ====================================
JWT_SECRET=NZD0KjnLPSA6w/Drd5mMq3d7L8o+eT5a3G3Aaxz/ZjY=  # ⚠️ Same as NEXTAUTH_SECRET
ENCRYPTION_KEY=your-encryption-key-here  # ⚠️ Use a 32-character secure key

DATABASE_URL="postgresql://omondi:Jack75522rmoi@localhost:5432/mocky"
DB_NAME="mocky"
DB_HOST="localhost"
DB_PORT="5432"
DB_USER="omondi"
DB_PASSWORD="Jack75522rmoi"
DB_SSL_MODE="disable"
DB_CONNECTION_LIMIT="20"

OPENAI_API_KEY=sk-your-placeholder-key
# Disable Google Analytics to prevent metadata lookup warnings
DISABLE_GOOGLE_ANALYTICS=true
# CRITICAL: Remove these exposed credentials:
# NEXT_PUBLIC_S3_ACCESS_KEY="..." ❌ DELETE THIS
# NEXT_PUBLIC_S3_SECRET_KEY="..." ❌ DELETE THIS

# Update these for production:
NODE_ENV=production
NEXTAUTH_URL=https://mocky.co.ke
NEXT_PUBLIC_APP_URL=https://mocky.co.ke

# Use these secure S3 credentials (already in your .env):
S3_ACCESS_KEY="RFUCVVCBIB34H47YSOAP"          ✅ Keep
S3_SECRET_KEY="XK969rMKHSN37EZugmDFSyjjPp487wHEjMgzhLNa"  ✅ Keep