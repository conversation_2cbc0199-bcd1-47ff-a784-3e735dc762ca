.dump# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage
/tests/coverage/
*.test.js.snap
junit.xml

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env
.env*.local
.env.development
.env.test
.env.production

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# IDE
.idea/
.vscode/

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage

# nyc test coverage
.nyc_output

# Grunt intermediate storage (https://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons (https://nodejs.org/api/addons.html)
build/Release

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variable files
.env
.env.development.local
.env.test.local
.env.production.local
.env.local

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next
out

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# Prisma
prisma/*.db
prisma/migrations/dev/
*.sqlite
*.sqlite-journal

# PM2
ecosystem.config.js

# PWA / Service workers
**/public/sw.js
**/public/workbox-*.js
**/public/worker-*.js
**/public/sw.js.map
**/public/workbox-*.js.map
**/public/worker-*.js.map

# Temporary files
*.tmp
*.temp
.cache

# System Files
.DS_Store
Thumbs.db

# Optional stylelint cache
.stylelintcache

# Track all image files
!public/
!public/images/
!public/images/**
!public/uploads/
!public/uploads/**

# Track specific portfolio directories
!public/images/portfolio/
!public/images/portfolio/**
!public/images/portfolio/fliers/
!public/images/portfolio/logos/
!public/images/portfolio/cards/
!public/images/portfolio/letterheads/
!public/images/portfolio/websites/
!public/images/portfolio/branding/

# Database Backup Files - DO NOT COMMIT TO GIT
# All backup file patterns
backup-*.dump*
backup-*.sql*
backup-*.gz*
backup-*.enc*
*.dump
*.dump.gz
*.dump.enc
*.dump.gz.enc
backups/
backup_*.dump
test-*.txt
test-*.js

# Temporary test files for S3 and backup debugging
test-s3-*.js
test-backup-*.js
test-simple-*.js
test-final-*.js
test-bigint-*.js

# Legacy tests (deprecated)
/src/__tests__/
/src/tests/
