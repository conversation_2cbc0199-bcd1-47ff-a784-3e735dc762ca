/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  // Development doesn't need standalone output
  // output: 'standalone',
  images: {
    remotePatterns: [
      {
        hostname: 'mocky.co.ke',
      },
      {
        hostname: 'localhost',
      },
      {
        hostname: 'fr-par-1.linodeobjects.com',
        protocol: 'https',
      },
    ],
    dangerouslyAllowSVG: true,
    contentDispositionType: 'attachment',
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
  },
  typescript: {
    // Keep type checking in development
    ignoreBuildErrors: false,
  },
  eslint: {
    // Keep eslint in development
    ignoreDuringBuilds: false,
  },
  // Development specific tracing excludes
  outputFileTracingExcludes: {
    '*': [
      'node_modules/@swc/core-linux-x64-gnu',
      'node_modules/@swc/core-linux-x64-musl',
      'node_modules/@esbuild/linux-x64',
    ],
  },
  experimental: {
    // Add any experimental features for development here
  },
  // Keep headers consistent
  async headers() {
    return [
      {
        source: '/:path*',
        headers: [
          {
            key: 'Cache-Control',
            // No caching in development
            value: 'no-cache, no-store, must-revalidate',
          },
        ],
      },
      {
        source: '/api/:path*',
        headers: [
          { key: 'Access-Control-Allow-Origin', value: '*' },
          { key: 'Access-Control-Allow-Methods', value: 'GET,POST,PUT,DELETE' },
        ],
      },
    ];
  },
  // Development settings for on-demand entries
  onDemandEntries: {
    // Short cache for faster development experience
    maxInactiveAge: 10 * 1000,
    pagesBufferLength: 3,
  },
  sassOptions: {
    includePaths: ['./src'],
  },
  // Keep redirects consistent
  async redirects() {
    return [
      {
        source: '/images/logos/:path*',
        destination: '/images/portfolio/logos/:path*',
        permanent: true,
      },
    ]
  },
  
  // Development optimization settings
  compress: true,
  poweredByHeader: true, // Show for development debugging
  generateEtags: true,
  productionBrowserSourceMaps: true, // Enable source maps in development
};

module.exports = nextConfig; 