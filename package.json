{"name": "mocky-digital", "version": "0.1.0", "private": true, "scripts": {"dev": "node server.js", "dev:config": "NODE_ENV=development next dev -c next.config.dev.js", "dev:debug": "DEBUG=* next dev", "dev:nextjs": "DEBUG=next:* next dev", "dev:trace": "NODE_OPTIONS='--trace-deprecation' next dev", "dev:rendering": "DEBUG=next:rendering* next dev", "dev:router": "DEBUG=next:router* next dev", "dev:build": "DEBUG=next:build* next dev", "dev:full": "DEBUG=next:*,react:* next dev", "build": "next build", "build:dev": "NODE_ENV=development next build -c next.config.dev.js", "start": "NODE_ENV=production node server.js", "lint": "next lint", "optimize-images": "node scripts/optimize-images.js", "clean": "rm -rf .next node_modules/.cache", "reset": "rm -rf .next node_modules package-lock.json && npm install", "dev:clean": "npm run clean && npm run dev", "build:prod": "NODE_ENV=production next build", "dev:fast": "NEXT_MINIMAL=1 NODE_OPTIONS='--max-old-space-size=4096' next dev", "dev:memory": "NODE_OPTIONS='--max-old-space-size=8192' next dev", "prisma:generate": "prisma generate", "prisma:push": "prisma db push", "prisma:seed": "ts-node prisma/seed.ts", "prisma:migrate": "prisma migrate dev", "prisma:seed:website": "ts-node scripts/seed-website-portfolio-prisma.ts", "seed:portfolio:enhanced": "ts-node scripts/seed-website-portfolio-enhanced.ts", "manage:portfolio": "ts-node scripts/manage-portfolio.ts", "seed:testimonials": "ts-node scripts/seed-testimonials.ts", "seed:testimonials:enhanced": "ts-node scripts/seed-testimonials-enhanced.ts", "manage:testimonials": "ts-node scripts/manage-testimonials.ts", "db:setup": "npm run prisma:generate && npm run prisma:push && npm run prisma:seed", "db:migrate-seed-website": "bash scripts/migrate-and-seed.sh", "seed:storage": "node prisma/seed-storage.js", "verify:storage": "node scripts/verify-storage-config.js", "test:system": "node scripts/run-system-test.js", "test:security": "node scripts/test-week1-security-fixes.js", "test:security:prod": "NODE_ENV=production node scripts/test-week1-security-fixes.js", "test:encryption": "node scripts/test-encryption.js", "blog:generate": "ts-node scripts/scheduled-blog-post.ts --run-once", "blog:setup-cron": "bash scripts/setup-blog-automation.sh", "blog:setup-pm2": "node scripts/setup-blog-automation-pm2.js", "seo:scan": "node scripts/seo-scan.js", "seo:scan-all": "node scripts/seo-scan.js --all", "seo:setup-cron": "bash scripts/setup-seo-cron.sh", "test:receipting": "ts-node scripts/test-receipting-system.ts", "audit:security": "ts-node scripts/security-audit-receipting.ts", "test:admin-fixes": "ts-node src/scripts/test-admin-fixes.ts", "test:cache": "ts-node src/scripts/test-cache-invalidation.ts", "test:all": "npm run test:receipting && npm run audit:security && npm run test:admin-fixes && npm run test:cache && npm run test:security", "monitor:performance": "node scripts/performance-monitor.js", "monitor:start": "pm2 start scripts/performance-monitor.js --name performance-monitor", "monitor:stop": "pm2 stop performance-monitor", "monitor:logs": "pm2 logs performance-monitor", "setup:performance": "node scripts/setup-performance.js", "colors:scrape": "node scripts/scrape-colors.js", "colors:setup-cron": "bash scripts/setup-colors-cron.sh", "scrape-catalogue": "node scripts/scrape-and-seed-catalogue.js", "scrape-setup": "node scripts/scrape-and-seed-catalogue.js --setup", "seed-catalogue": "node scripts/seed-scraped-data.js", "seed-sawaprint-banners": "node scripts/seed-sawaprint-banners.js", "download-images": "node scripts/download-and-upload-images.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:component": "jest --testPathPattern=components", "test:api": "jest --testPathPattern=api", "test:hooks": "jest --testPathPattern=hooks", "test:integration": "jest --testPathPattern=integration", "type-check": "tsc --noEmit", "lint:fix": "next lint --fix", "format": "prettier --write .", "format:check": "prettier --check .", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:seed": "tsx prisma/seed.ts", "db:seed:website-portfolio": "tsx prisma/seed-website-portfolio.ts", "db:verify:website-portfolio": "node scripts/verify-website-portfolio.js", "db:reset": "prisma migrate reset --force", "db:studio": "prisma studio", "db:validate": "node scripts/validate-prisma-consistency.js", "db:check": "npx prisma format && npx prisma validate", "db:sync": "npx prisma db push", "db:reset-dev": "npx prisma migrate reset --force && npx prisma db seed", "db:backup": "pg_dump $DATABASE_URL > backup-$(date +%Y%m%d-%H%M%S).sql", "precommit": "npm run db:validate && npm run db:check && npm run type-check", "test:prisma": "jest tests/prisma-consistency.test.js", "test:models": "node -e \"require('./scripts/validate-prisma-consistency.js')\"", "postinstall": "npx prisma generate"}, "dependencies": {"@auth/prisma-adapter": "^2.9.1", "@aws-sdk/client-s3": "^3.832.0", "@aws-sdk/s3-request-presigner": "^3.832.0", "@aws-sdk/types": "^3.734.0", "@emailjs/browser": "^4.4.1", "@headlessui/react": "^2.2.0", "@hello-pangea/dnd": "^18.0.1", "@heroicons/react": "^2.2.0", "@prisma/client": "^6.8.2", "@prisma/engines": "^6.10.0", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.4", "@smithy/node-http-handler": "^4.0.6", "@tanstack/react-query": "^5.80.7", "@tanstack/react-query-devtools": "^5.80.7", "@tanstack/react-virtual": "^3.13.4", "@tiptap/extension-code-block-lowlight": "^2.25.1", "@tiptap/extension-heading": "^2.11.5", "@tiptap/extension-highlight": "^2.25.1", "@tiptap/extension-image": "^2.11.5", "@tiptap/extension-link": "^2.11.5", "@tiptap/extension-placeholder": "^2.11.5", "@tiptap/extension-text-align": "^2.25.1", "@tiptap/extension-typography": "^2.25.1", "@tiptap/react": "^2.11.5", "@tiptap/starter-kit": "^2.11.5", "@types/jsdom": "^21.1.7", "@types/node-cron": "^3.0.11", "@types/nodemailer": "^6.4.17", "@types/pg": "^8.15.0", "aos": "^2.3.4", "bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "browser-image-compression": "^2.0.2", "chart.js": "^4.4.8", "cheerio": "^1.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cookie": "^1.0.2", "csv-writer": "^1.6.0", "date-fns": "^4.1.0", "decimal.js": "^10.5.0", "dompurify": "^3.2.5", "dotenv": "^16.5.0", "express-rate-limit": "^7.4.1", "express-slow-down": "^2.0.3", "framer-motion": "^10.18.0", "helmet": "^8.0.0", "ioredis": "^5.4.1", "iron-session": "^8.0.4", "isomorphic-dompurify": "^2.26.0", "jose": "^6.0.11", "js-cookie": "^3.0.5", "jsdom": "^26.1.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "lowlight": "^3.3.0", "lru-cache": "^11.1.0", "lucide-react": "^0.483.0", "mime-types": "^2.1.35", "next": "^15.2.4", "next-auth": "^5.0.0-beta.28", "node-cron": "^4.1.0", "node-fetch": "^2.7.0", "nodemailer": "^6.10.1", "p-queue": "^8.1.0", "pdfkit": "^0.17.1", "pdfkit-table": "^0.1.99", "pg": "^8.15.6", "plaiceholder": "^3.0.0", "puppeteer": "^24.9.0", "qrcode": "^1.5.4", "react": "^18.3.1", "react-chartjs-2": "^5.3.0", "react-countup": "^6.5.3", "react-dom": "^18.3.1", "react-dropzone": "^14.3.8", "react-error-boundary": "^5.0.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-intersection-observer": "^9.16.0", "react-type-animation": "^3.2.0", "react-window": "^1.8.11", "recharts": "^2.15.3", "redis": "^4.7.0", "slugify": "^1.6.6", "speakeasy": "^2.0.0", "swiper": "^11.2.5", "tailwind-merge": "^3.2.0", "validator": "^13.15.15", "zod": "^3.25.65", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@playwright/test": "^1.49.1", "@tailwindcss/forms": "^0.5.10", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^14.5.2", "@types/bcrypt": "^5.0.2", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^9.0.10", "@types/lru-cache": "^7.10.9", "@types/mime-types": "^3.0.1", "@types/pdfkit": "^0.14.0", "@types/react": "19.1.8", "@types/uuid": "^10.0.0", "@types/validator": "^13.15.2", "assert": "^2.1.0", "autoprefixer": "^10.4.21", "axios": "^1.10.0", "critters": "^0.0.23", "cross-env": "^7.0.3", "eslint": "^9.17.0", "eslint-plugin-jest-dom": "^5.4.0", "eslint-plugin-testing-library": "^6.4.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-mock-extended": "^4.0.0", "msw": "^2.6.6", "playwright": "^1.53.2", "postcss": "^8.5.6", "prettier": "^3.3.3", "prisma": "^6.9.0", "rimraf": "^6.0.1", "tailwindcss": "^3.4.17", "ts-node": "^10.9.2", "tsx": "^4.19.2"}, "prisma": {"seed": "ts-node prisma/seed.ts"}}