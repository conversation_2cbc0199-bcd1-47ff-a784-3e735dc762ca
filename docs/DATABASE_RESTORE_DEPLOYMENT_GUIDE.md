# Database Restore Production Deployment Guide

## 🎯 Overview

This guide covers the deployment of the fixed database restore functionality that was previously failing silently. All critical issues have been identified and resolved.

## ✅ Issues Fixed

### 1. **CRITICAL: Simulation Instead of Execution**
- **Issue**: Main restore API was returning simulations
- **Fix**: Replaced with actual `restoreDatabaseFromBackup()` calls
- **File**: `src/app/api/admin/database/restore/route.ts`

### 2. **CRITICAL: Missing Schema Dependencies**  
- **Issue**: Code referenced non-existent `backupRestoreLog` table
- **Fix**: Removed problematic dependencies, added proper error handling
- **File**: `src/services/databaseBackupService.ts`

### 3. **CRITICAL: Inadequate Error Handling**
- **Issue**: Too many errors were being ignored
- **Fix**: Comprehensive error detection with proper filtering
- **Files**: `src/services/databaseBackupService.ts`, `src/services/s3BackupService.ts`

### 4. **CRITICAL: Wrong Restore Methods**
- **Issue**: Using `psql` for custom format backups
- **Fix**: Intelligent format detection and proper command selection
- **File**: `src/services/s3BackupService.ts`

### 5. **CRITICAL: Missing Command Flags**
- **Issue**: Lack of atomic operation flags
- **Fix**: Added `--single-transaction`, `--exit-on-error`
- **Files**: All restore services

## 🧪 Test Results

### Validation Script Results
```
✅ Database connection working
✅ pg_dump, pg_restore, and psql available  
✅ Temp and backup directories accessible
✅ Backup creation tested successfully
✅ Backup integrity validation working
✅ Restore command syntax validated
✅ Node.js dependencies available
✅ Prisma client accessible
```

### Manual Restore Test Results
```
✅ Backup file validation: PASSED
✅ Database record creation: PASSED  
✅ pg_restore command execution: PASSED
✅ Database functionality verification: PASSED
```

### API Endpoint Test Results
```
✅ Endpoint responding correctly
✅ Authentication working (returns Unauthorized without token)
✅ Request parsing functional
```

## 🚀 Production Deployment Steps

### Pre-Deployment Checklist

#### 1. **Server Prerequisites**
```bash
# Verify PostgreSQL client tools
which pg_dump pg_restore psql

# Check Node.js version
node --version  # Should be >= 18

# Verify NPM dependencies
npm ci
```

#### 2. **Environment Validation**
```bash
# Run validation script
./scripts/test-restore.sh

# Create test backup  
node scripts/create-test-backup.js

# Test manual restore
node scripts/test-restore-manual.js
```

#### 3. **Database Schema Check**
```bash
# Ensure Prisma client is generated
npx prisma generate

# Verify database connectivity
npx prisma db seed --preview-feature || echo "Seed not available"
```

### Deployment Process

#### 1. **Backup Current Production**
```bash
# Create full production backup before deployment
npm run backup:create -- --description "Pre-deployment backup $(date)"

# Verify backup creation
ls -la backups/
```

#### 2. **Deploy Code Changes**
```bash
# Pull latest changes
git pull origin main

# Install dependencies
npm ci --production=false

# Generate Prisma client
npx prisma generate

# Build application
npm run build
```

#### 3. **Restart Services**
```bash
# If using PM2
pm2 restart mocky-digital

# If using systemd
sudo systemctl restart mocky-digital

# If using Docker
docker-compose restart mocky-digital
```

#### 4. **Post-Deployment Verification**
```bash
# Verify API is responding
curl -I http://localhost:3000/api/health

# Test restore endpoint (should return Unauthorized)
curl -X POST http://localhost:3000/api/admin/database/restore \
  -H "Content-Type: application/json" \
  -d '{"backupId":"test","confirmRestore":true}'
```

## 🔐 Security Considerations

### Authentication Requirements
- Admin authentication required for all restore operations
- Restore confirmation flag prevents accidental execution
- Audit logging for all restore attempts

### File Permissions
```bash
# Backup directory permissions
chmod 750 backups/
chown app:app backups/

# Temp directory access
chmod 755 /tmp
```

### Database Security
- Restore operations use application database credentials
- No elevation of privileges required
- Pre-restore backups created automatically

## 📊 Monitoring & Alerting

### Key Metrics to Monitor
- `restore_duration_seconds`
- `restore_success_rate` 
- `backup_integrity_failures`
- `file_access_errors`

### Log Patterns to Alert On
```
ERROR: pg_restore failed
ERROR: Backup file not found
ERROR: Database connection failed
CRITICAL: Restore operation failed
```

### Recommended Alerts
```yaml
# Example Prometheus alerting rules
- alert: DatabaseRestoreFailure
  expr: restore_success_rate < 0.9
  for: 5m
  labels:
    severity: critical
  annotations:
    summary: "Database restore success rate is below 90%"

- alert: BackupIntegrityFailure  
  expr: backup_integrity_failures > 0
  for: 1m
  labels:
    severity: warning
  annotations:
    summary: "Backup integrity check failed"
```

## 🆘 Troubleshooting

### Common Issues

#### "Backup file not found"
```bash
# Check file exists
ls -la /path/to/backup/file

# Check permissions
stat /path/to/backup/file

# Verify S3 connectivity if cloud backup
aws s3 ls s3://your-bucket/database-backups/
```

#### "pg_restore command failed"
```bash
# Test restore manually
pg_restore --list /path/to/backup/file

# Check database connectivity
psql "$DATABASE_URL" -c "SELECT 1"

# Verify disk space
df -h /tmp
```

#### "Database verification failed"
```bash
# Check Prisma client
npx prisma db seed --preview-feature

# Verify schema
npx prisma db pull
```

## 📋 Recovery Procedures

### If Restore Fails
1. **Stop Application**: Prevent further damage
2. **Check Logs**: Identify specific failure cause  
3. **Restore from Pre-Restore Backup**: Use automatic backup created before restore
4. **Verify Database State**: Run integrity checks
5. **Restart Application**: Resume normal operations

### Emergency Rollback
```bash
# If restore corrupted database, use pre-restore backup
LATEST_BACKUP=$(ls -t backups/pre-restore-*.dump | head -1)
pg_restore -d "$DATABASE_URL" --clean --if-exists "$LATEST_BACKUP"
```

## 🔄 Regular Maintenance

### Weekly Tasks
- Test restore functionality with recent backups
- Verify backup integrity of all stored backups
- Check disk space in backup directories
- Review restore operation logs

### Monthly Tasks  
- Performance test restore operations
- Update backup retention policies
- Review and update documentation
- Test emergency recovery procedures

## 📞 Support Contacts

- **Database Issues**: DevOps Team
- **Application Issues**: Backend Development Team  
- **Security Concerns**: Security Team
- **Emergency**: On-call Engineer

---

## 🎉 Deployment Complete

After following this guide, your database restore functionality will be:
- ✅ Actually working (no more simulations)
- ✅ Properly secured with authentication
- ✅ Comprehensively logged and monitored
- ✅ Resilient with proper error handling
- ✅ Protected with automatic pre-restore backups

The silent failures have been eliminated and replaced with robust, production-ready restore functionality. 