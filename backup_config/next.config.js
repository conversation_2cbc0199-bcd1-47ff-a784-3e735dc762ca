/** @type {import('next').NextConfig} */
const nextConfig = {
  // Production optimizations
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production' ? {
      exclude: ['error', 'warn']
    } : false,
    styledComponents: true,
  },
  
  // Enable React strict mode for better development experience
  reactStrictMode: true,
  
  // Experimental features for performance
  experimental: {
    optimizePackageImports: [
      '@tanstack/react-query',
      'zustand',
      'lucide-react',
      'framer-motion',
      '@heroicons/react',
      'react-icons',
      'axios',
      'date-fns',
      'lodash'
    ],
    webVitalsAttribution: ['CLS', 'LCP'],
    optimizeCss: true,
    scrollRestoration: true,
  },
  
  // Enhanced image optimization
  images: {
    formats: ['image/avif', 'image/webp'],
    minimumCacheTTL: 60 * 60 * 24 * 30, // 30 days
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    dangerouslyAllowSVG: true,
    contentDispositionType: 'attachment',
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
    unoptimized: process.env.NODE_ENV === 'development',
    remotePatterns: [
      {
        hostname: 'mocky.co.ke',
        protocol: 'https',
        pathname: '/**',
      },
      {
        hostname: 'mocky.co.ke',
        protocol: 'http',
        pathname: '/**',
      },
      {
        hostname: 'localhost',
        protocol: 'http',
        pathname: '/**',
      },
      {
        hostname: 'fr-par-1.linodeobjects.com',
        protocol: 'https',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: '**.linodeobjects.com',
        pathname: '/**',
      },
      {
        protocol: 'http',
        hostname: '**.linodeobjects.com',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: '**.amazonaws.com',
        pathname: '/**',
      },
    ],
  },
  
  // Build optimizations
  typescript: {
    ignoreBuildErrors: process.env.NODE_ENV === 'production',
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  
  // File tracing exclusions for smaller bundles
  outputFileTracingExcludes: {
    '*': [
      'node_modules/@swc/core-linux-x64-gnu',
      'node_modules/@swc/core-linux-x64-musl',
      'node_modules/@esbuild/linux-x64',
      'node_modules/puppeteer/.local-chromium/**/*',
      'node_modules/sharp/vendor/**/*',
    ],
  },
  
  // Enhanced caching headers
  async headers() {
    return [
      // Static assets - very long cache
      {
        source: '/:all*(svg|jpg|jpeg|png|webp|avif|ico|woff|woff2|ttf|otf)',
        locale: false,
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
          {
            key: 'Vary',
            value: 'Accept-Encoding',
          },
        ],
      },
      // Next.js static files
      {
        source: '/_next/static/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
      // Image optimization
      {
        source: '/_next/image',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=3600, s-maxage=31536000, stale-while-revalidate=86400',
          },
        ],
      },
      // Images directory
      {
        source: '/images/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=86400, s-maxage=31536000, stale-while-revalidate=86400',
          },
        ],
      },
      // Pages - shorter cache with stale-while-revalidate
      {
        source: '/((?!api|_next/static|_next/image|favicon.ico).*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=0, s-maxage=86400, stale-while-revalidate=43200',
          },
        ],
      },
      // API routes - no cache but allow CORS
      {
        source: '/api/:path*',
        headers: [
          { key: 'Access-Control-Allow-Origin', value: '*' },
          { key: 'Access-Control-Allow-Methods', value: 'GET,POST,PUT,DELETE,OPTIONS' },
          { key: 'Access-Control-Allow-Headers', value: 'Content-Type, Authorization' },
          { key: 'Cache-Control', value: 'no-cache, no-store, must-revalidate' },
        ],
      },
    ];
  },
  
  // Output configuration
  output: 'standalone',
  
  // Disable powered by header
  poweredByHeader: false,
  
  // Enable compression
  compress: true,
  
  // Generate etags for caching
  generateEtags: true,
  
  // No trailing slash
  trailingSlash: false,
  
  // Redirects for SEO
  async redirects() {
    return [
      {
        source: '/images/logos/:path*',
        destination: '/images/portfolio/logos/:path*',
        permanent: true,
      },
      {
        source: '/pricing',
        destination: '/catalogue',
        permanent: true,
      },
    ];
  },
  
  // Development optimizations
  onDemandEntries: {
    maxInactiveAge: 25 * 1000,
    pagesBufferLength: 5,
  },
};

module.exports = nextConfig;