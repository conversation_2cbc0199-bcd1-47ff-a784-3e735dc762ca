#!/bin/bash

# PM2 Management Script for Mocky Digital
# Usage: ./pm2-manage.sh [command]

APP_NAME="mocky-digital"
PROJECT_DIR="/var/www/mocky"

case $1 in
  "start")
    echo "🚀 Starting Mocky Digital..."
    cd $PROJECT_DIR
    pm2 start ecosystem.config.js --env production
    ;;
  
  "stop")
    echo "🛑 Stopping Mocky Digital..."
    pm2 stop $APP_NAME
    ;;
  
  "restart")
    echo "🔄 Restarting Mocky Digital..."
    pm2 restart $APP_NAME --update-env
    ;;
  
  "reload")
    echo "♻️ Reloading Mocky Digital (zero-downtime)..."
    pm2 reload $APP_NAME
    ;;
  
  "status")
    echo "📊 Checking status..."
    pm2 status
    ;;
  
  "logs")
    echo "📝 Showing logs..."
    pm2 logs $APP_NAME --lines 50
    ;;
  
  "monitor")
    echo "📈 Opening PM2 monitor..."
    pm2 monit
    ;;
  
  "deploy")
    echo "🚀 Deploying latest version..."
    cd $PROJECT_DIR
    git pull origin main
    npm install
    npm run build
    pm2 restart $APP_NAME --update-env
    echo "✅ Deployment complete!"
    ;;
  
  "backup")
    echo "💾 Creating backup..."
    cd $PROJECT_DIR
    tar -czf "backup-$(date +%Y%m%d-%H%M%S).tar.gz" \
      --exclude=node_modules \
      --exclude=.next \
      --exclude=logs \
      .
    echo "✅ Backup created!"
    ;;
  
  "health")
    echo "🏥 Health check..."
    echo "PM2 Status:"
    pm2 status
    echo ""
    echo "Application Response:"
    curl -I http://localhost:3000
    echo ""
    echo "Memory Usage:"
    pm2 show $APP_NAME | grep -E "(memory|cpu)"
    ;;
  
  "save")
    echo "💾 Saving PM2 configuration..."
    pm2 save
    echo "✅ Configuration saved!"
    ;;
  
  *)
    echo "🛠️ PM2 Management Script for Mocky Digital"
    echo ""
    echo "Available commands:"
    echo "  start     - Start the application"
    echo "  stop      - Stop the application"
    echo "  restart   - Restart the application"
    echo "  reload    - Zero-downtime reload"
    echo "  status    - Show application status"
    echo "  logs      - Show application logs"
    echo "  monitor   - Open PM2 monitoring interface"
    echo "  deploy    - Deploy latest version (git pull + build + restart)"
    echo "  backup    - Create backup of project"
    echo "  health    - Comprehensive health check"
    echo "  save      - Save PM2 configuration"
    echo ""
    echo "Usage: ./pm2-manage.sh [command]"
    ;;
esac 