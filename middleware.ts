import { auth } from "./auth"
import { NextResponse } from "next/server"
import { getToken } from "next-auth/jwt"

// Inactivity timeout in milliseconds (30 minutes)
const INACTIVITY_TIMEOUT = 30 * 60 * 1000

export default auth(async (req) => {
  const { nextUrl } = req
  
  // Get the token directly to check lastActivity
  const token = await getToken({ req })
  const isLoggedIn = !!token
  
  // Define protected and public routes
  const isAdminRoute = nextUrl.pathname.startsWith('/admin')
  const isApiAdminRoute = nextUrl.pathname.startsWith('/api/admin')
  const isLoginPage = nextUrl.pathname === '/admin/login'
  const isLogoutPage = nextUrl.pathname === '/admin/logout'
  
  // Check for inactivity timeout if user is logged in
  if (isLoggedIn && !isLoginPage && !isLogoutPage) {
    const lastActivity = token.lastActivity as number || 0
    const timeSinceLastActivity = Date.now() - lastActivity
    
    if (timeSinceLastActivity > INACTIVITY_TIMEOUT) {
      // User has been inactive, redirect to logout
      return NextResponse.redirect(new URL('/admin/logout', nextUrl))
    }
  }
  
  // Allow login page for unauthenticated users
  if (isLoginPage && !isLoggedIn) {
    return NextResponse.next()
  }
  
  // Redirect logged in users away from login page
  if (isLoginPage && isLoggedIn) {
    return NextResponse.redirect(new URL('/admin/dashboard', nextUrl))
  }
  
  // Protect admin routes
  if ((isAdminRoute || isApiAdminRoute) && !isLoggedIn) {
    if (isApiAdminRoute) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
    return NextResponse.redirect(new URL('/admin/login', nextUrl))
  }
  
  // Check admin role for admin routes
  if ((isAdminRoute || isApiAdminRoute) && isLoggedIn) {
    const userRole = token.role
    
    if (userRole !== 'admin' && userRole !== 'Admin') {
      if (isApiAdminRoute) {
        return NextResponse.json({ error: 'Forbidden' }, { status: 403 })
      }
      return NextResponse.redirect(new URL('/unauthorized', nextUrl))
    }
  }
  
  return NextResponse.next()
})

export const config = {
  matcher: [
    '/admin/:path*',
    '/api/admin/:path*',
    '/admin/login'
  ]
} 