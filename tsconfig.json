{"compilerOptions": {"target": "es2020", "lib": ["dom", "dom.iterable", "esnext"], "types": ["node"], "typeRoots": ["./node_modules/@types"], "allowJs": true, "skipLibCheck": true, "skipDefaultLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "commonjs", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "jsxImportSource": "react", "incremental": true, "noUncheckedIndexedAccess": true, "exactOptionalPropertyTypes": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./src/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}