import <PERSON>A<PERSON> from "next-auth"
import C<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from "next-auth/providers/credentials"
import { PrismaAdapter } from "@auth/prisma-adapter"
import { prisma } from "@/lib/prisma"
import bcryptjs from "bcryptjs"
import { User } from "@prisma/client"

interface UserWithRole extends User {
  role: {
    name: string;
  } | null;
}

export const { handlers, auth, signIn, signOut } = NextAuth({
  adapter: PrismaAdapter(prisma),
  providers: [
    CredentialsProvider({
      name: "credentials",
      credentials: {
        username: { label: "Username", type: "text" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials, req) {
        const isDev = process.env.NODE_ENV === 'development';
        
        if (isDev) {
          console.log("authorize called with credentials:", {
            username: credentials?.username,
            hasPassword: !!credentials?.password
          });
        }

        if (!credentials?.username || !credentials?.password) {
          if (isDev) console.log("Missing credentials");
          return null;
        }

        try {
          if (isDev) console.log("Looking up user:", credentials.username);
          
          // Find user by username
          const user = await prisma.user.findUnique({
            where: {
              username: credentials.username
            },
            include: {
              role: true
            }
          }) as UserWithRole | null;

          if (!user) {
            if (isDev) console.log("User not found:", credentials.username);
            return null;
          }

          if (!user.active) {
            if (isDev) console.log("User is inactive:", credentials.username);
            return null;
          }

          if (isDev) {
            console.log("Found user:", {
              username: user.username,
              active: user.active,
              roleName: user.role?.name,
              // REMOVED: Sensitive hash information
            });
          }

          // Verify password using bcryptjs
          if (isDev) console.log("Attempting password verification...");
          const isValid = await bcryptjs.compare(credentials.password, user.passwordHash);
          
          if (isDev) {
            console.log("Password verification completed:", {
              isValid
              // REMOVED: Sensitive password/hash information
            });
          }

          if (!isValid) {
            if (isDev) console.log("Invalid password for user:", credentials.username);
            return null;
          }

          // Update last login
          await prisma.user.update({
            where: { id: user.id },
            data: { lastLogin: new Date() }
          });

          if (isDev) console.log("Login successful for user:", credentials.username);
          
          const userResult = {
            id: user.id,
            username: user.username,
            email: user.email,
            name: user.name,
            role: user.role?.name || "user",
            active: user.active
          };

          if (isDev) console.log("Returning user data:", userResult);
          return userResult;
        } catch (error) {
          console.error("Authentication error:", error);
          return null;
        }
      }
    })
  ],
  pages: {
    signIn: '/admin/login',
    error: '/admin/login',
  },
  callbacks: {
    async jwt({ token, user }) {
      const isDev = process.env.NODE_ENV === 'development';
      
      if (isDev) {
        console.log("JWT callback called with:", {
          hasUser: !!user,
          tokenSub: token.sub,
          tokenUsername: token.username
        });
      }

      if (user) {
        // Initial sign in - set user data and lastActivity
        token.role = user.role;
        token.active = user.active;
        token.username = user.username;
        token.lastActivity = Date.now();
        
        if (isDev) {
          console.log("JWT callback - new token:", { username: token.username, role: token.role });
        }
      } else {
        // Subsequent requests - update lastActivity only if needed
        const lastActivity = token.lastActivity as number || 0;
        if (Date.now() - lastActivity > 5 * 60 * 1000) {
          token.lastActivity = Date.now();
        }
      }
      return token;
    },
    async session({ session, token }) {
      const isDev = process.env.NODE_ENV === 'development';
      
      if (isDev) {
        console.log("Session callback called with token:", {
          sub: token.sub,
          username: token.username,
          role: token.role
        });
      }

      if (token) {
        session.user.id = token.sub!;
        session.user.role = token.role as string;
        session.user.active = token.active as boolean;
        session.user.username = token.username as string;
        session.user.lastActivity = token.lastActivity as number;
        
        if (isDev) {
          console.log("Session callback - updated session:", { username: session.user.username, role: session.user.role });
        }
      }
      return session;
    },
    async redirect({ url, baseUrl }) {
      const isDev = process.env.NODE_ENV === 'development';
      
      // Use SITE_URL in production, fallback to baseUrl for development
      const siteUrl = (process.env.SITE_URL || baseUrl) as string;
      
      if (isDev) {
        console.log("Redirect callback called with:", { url, baseUrl, siteUrl });
      }
      
      // Always redirect to dashboard after successful login
      if (url.includes('/api/auth/callback/credentials')) {
        if (isDev) console.log("Redirecting to dashboard");
        return `${siteUrl}/admin/dashboard`;
      }

      // Handle other redirects
      if (url.startsWith("/")) {
        const finalUrl = `${siteUrl}${url}`;
        if (isDev) console.log("Redirecting to:", finalUrl);
        return finalUrl;
      }
      
      // Allow same-origin URLs
      try {
        const urlOrigin = new URL(url).origin;
        if (urlOrigin === siteUrl) {
          if (isDev) console.log("Redirecting to same-origin URL:", url);
          return url;
        }
      } catch (error) {
        console.error("Invalid URL:", url);
      }

      if (isDev) console.log("Fallback redirect to:", siteUrl);
      return siteUrl;
    }
  },
  session: {
    strategy: "jwt",
    maxAge: 60 * 60 * 24 * 7, // 7 days
  },
  cookies: {
    sessionToken: {
      name: 'next-auth.session-token',
      options: {
        httpOnly: true,
        sameSite: 'lax',
        path: '/',
        secure: process.env.NODE_ENV === 'production'
      }
    },
    callbackUrl: {
      name: 'next-auth.callback-url',
      options: {
        sameSite: 'lax',
        path: '/',
        secure: process.env.NODE_ENV === 'production'
      }
    },
    csrfToken: {
      name: 'next-auth.csrf-token',
      options: {
        httpOnly: true,
        sameSite: 'lax',
        path: '/',
        secure: process.env.NODE_ENV === 'production'
      }
    }
  },
  secret: process.env.AUTH_SECRET || 'fallback-secret-do-not-use-in-production'
}); 