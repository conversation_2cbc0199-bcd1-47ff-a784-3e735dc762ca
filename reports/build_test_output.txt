
> mocky-digital@0.1.0 build
> next build

   ▲ Next.js 15.3.3
   - Environments: .env.local, .env
   - Experiments (use with caution):
     ✓ optimizeCss
     ✓ scrollRestoration

   Creating an optimized production build ...
 ✓ Compiled successfully in 28.0s
   Skipping validation of types
   Skipping linting
   Collecting page data ...
   Generating static pages (0/212) ...
   Generating static pages (53/212) 
   Generating static pages (106/212) 
   Generating static pages (159/212) 
 ✓ Generating static pages (212/212)
   Finalizing page optimization ...
   Collecting build traces ...

Route (app)                                                 Size  First Load JS  Revalidate  Expire
┌ ○ /                                                    6.37 kB         536 kB
├ ○ /_not-found                                            214 B         451 kB
├ ○ /about                                               7.12 kB         501 kB
├ ○ /admin                                                 708 B         452 kB
├ ○ /admin/activity-logs                                 2.68 kB         489 kB
├ ○ /admin/analytics                                      2.9 kB         489 kB
├ ○ /admin/blog                                          2.83 kB         489 kB
├ ƒ /admin/blog/[id]/edit                                3.41 kB         520 kB
├ ○ /admin/blog/categories                               3.49 kB         490 kB
├ ○ /admin/blog/new                                      2.94 kB         519 kB
├ ○ /admin/calendar                                      9.86 kB         496 kB
├ ○ /admin/catalogue                                     4.04 kB         490 kB
├ ƒ /admin/catalogue/[id]                                2.87 kB         489 kB
├ ƒ /admin/catalogue/[id]/edit                           5.01 kB         491 kB
├ ○ /admin/catalogue/new                                 5.72 kB         492 kB
├ ○ /admin/categories                                    1.85 kB         488 kB
├ ƒ /admin/categories/[id]/edit                          1.75 kB         488 kB
├ ○ /admin/categories/new                                1.49 kB         488 kB
├ ○ /admin/clients                                          4 kB         490 kB
├ ƒ /admin/clients/[id]                                  6.38 kB         493 kB
├ ○ /admin/communications                                4.18 kB         490 kB
├ ○ /admin/comprehensive-receipts                        4.59 kB         491 kB
├ ƒ /admin/comprehensive-receipts/[id]                   6.55 kB         499 kB
├ ○ /admin/comprehensive-receipts/list                   3.08 kB         496 kB
├ ○ /admin/dashboard                                      5.5 kB         492 kB
├ ○ /admin/database                                      5.69 kB         492 kB
├ ○ /admin/design-requests                               7.82 kB         494 kB
├ ○ /admin/invoices                                      3.01 kB         496 kB
├ ƒ /admin/invoices/[id]                                  3.8 kB         497 kB
├ ○ /admin/invoices/new                                  3.97 kB         497 kB
├ ○ /admin/login                                         2.39 kB         489 kB
├ ○ /admin/orders                                         6.1 kB         499 kB
├ ○ /admin/portfolio                                     7.62 kB         494 kB
├ ƒ /admin/portfolio/[id]/edit                           3.81 kB         490 kB
├ ○ /admin/portfolio/new                                 3.67 kB         490 kB
├ ○ /admin/posts                                         2.89 kB         489 kB
├ ○ /admin/pricing                                       3.06 kB         489 kB
├ ○ /admin/projects                                       7.3 kB         494 kB
├ ƒ /admin/projects/[id]                                 7.16 kB         493 kB
├ ƒ /admin/projects/[id]/edit                            2.77 kB         489 kB
├ ○ /admin/quotes                                        3.13 kB         496 kB
├ ƒ /admin/quotes/[id]                                   3.67 kB         496 kB
├ ○ /admin/quotes/new                                    3.79 kB         497 kB
├ ○ /admin/roles                                         4.26 kB         491 kB
├ ƒ /admin/roles/edit/[id]                               2.23 kB         495 kB
├ ○ /admin/roles/new                                     1.66 kB         488 kB
├ ○ /admin/scheduled-blog-posts                          2.43 kB         495 kB
├ ○ /admin/scheduled-blog-posts/new                      2.25 kB         489 kB
├ ○ /admin/services                                      4.72 kB         491 kB
├ ○ /admin/settings                                      3.24 kB         490 kB
├ ○ /admin/settings/cache                                5.91 kB         492 kB
├ ○ /admin/settings/scripts                              3.79 kB         490 kB
├ ○ /admin/settings/storage                              4.84 kB         491 kB
├ ○ /admin/settings/storage/debug                        2.19 kB         488 kB
├ ○ /admin/settings/storage/test-endpoint                1.35 kB         452 kB
├ ○ /admin/tasks                                         4.49 kB         491 kB
├ ○ /admin/team                                          2.35 kB         489 kB
├ ƒ /admin/team/[id]/edit                                3.08 kB         489 kB
├ ○ /admin/team/new                                      2.23 kB         489 kB
├ ○ /admin/testimonials                                  2.88 kB         489 kB
├ ƒ /admin/testimonials/[id]/edit                        2.02 kB         488 kB
├ ○ /admin/testimonials/new                              1.85 kB         488 kB
├ ○ /admin/tests                                         2.02 kB         488 kB
├ ○ /admin/time-tracking                                 4.34 kB         491 kB
├ ○ /admin/users                                         4.63 kB         491 kB
├ ƒ /admin/users/edit/[id]                               6.56 kB         499 kB
├ ○ /admin/users/new                                     2.25 kB         489 kB
├ ○ /admin/website-portfolio                             3.43 kB         490 kB
├ ƒ /admin/website-portfolio/[id]/edit                   3.43 kB         490 kB
├ ○ /admin/website-portfolio/new                         3.16 kB         489 kB
├ ƒ /api/admin/activity-logs                               214 B         451 kB
├ ƒ /api/admin/analytics                                   214 B         451 kB
├ ƒ /api/admin/blog                                        214 B         451 kB
├ ƒ /api/admin/blog/[id]                                   214 B         451 kB
├ ƒ /api/admin/blog/categories                             214 B         451 kB
├ ƒ /api/admin/blog/categories/[id]                        214 B         451 kB
├ ƒ /api/admin/cache                                       214 B         451 kB
├ ƒ /api/admin/cache/redis                                 214 B         451 kB
├ ƒ /api/admin/cache/warm                                  214 B         451 kB
├ ƒ /api/admin/cache/warmup                                214 B         451 kB
├ ƒ /api/admin/calendar                                    214 B         451 kB
├ ƒ /api/admin/calendar/events                             214 B         451 kB
├ ƒ /api/admin/calendar/events/[id]                        214 B         451 kB
├ ƒ /api/admin/catalogue                                   214 B         451 kB
├ ƒ /api/admin/catalogue-v3                                214 B         451 kB
├ ƒ /api/admin/catalogue-v3/[id]                           214 B         451 kB
├ ƒ /api/admin/catalogue/categories                        214 B         451 kB
├ ƒ /api/admin/categories                                  214 B         451 kB
├ ƒ /api/admin/categories/[id]                             214 B         451 kB
├ ƒ /api/admin/clients                                     214 B         451 kB
├ ƒ /api/admin/clients/[id]                                214 B         451 kB
├ ƒ /api/admin/clients/documents                           214 B         451 kB
├ ƒ /api/admin/clients/reminders                           214 B         451 kB
├ ƒ /api/admin/clients/sync                                214 B         451 kB
├ ƒ /api/admin/communications                              214 B         451 kB
├ ƒ /api/admin/comprehensive-receipts                      214 B         451 kB
├ ƒ /api/admin/comprehensive-receipts/[id]                 214 B         451 kB
├ ƒ /api/admin/comprehensive-receipts/[id]/add-payment     215 B         451 kB
├ ƒ /api/admin/comprehensive-receipts/check                214 B         451 kB
├ ƒ /api/admin/database/backup                             214 B         451 kB
├ ƒ /api/admin/database/backup/[id]                        214 B         451 kB
├ ƒ /api/admin/database/backup/[id]/download               214 B         451 kB
├ ƒ /api/admin/database/backups                            214 B         451 kB
├ ƒ /api/admin/database/enterprise-backup                  214 B         451 kB
├ ƒ /api/admin/database/restore                            214 B         451 kB
├ ƒ /api/admin/database/s3-backups                         214 B         451 kB
├ ƒ /api/admin/design-requests                             214 B         451 kB
├ ƒ /api/admin/design-requests/[id]                        214 B         451 kB
├ ƒ /api/admin/diagnostic/form-upload                      214 B         451 kB
├ ƒ /api/admin/health                                      214 B         451 kB
├ ƒ /api/admin/health/redis                                214 B         451 kB
├ ƒ /api/admin/images                                      214 B         451 kB
├ ƒ /api/admin/images/[key]                                214 B         451 kB
├ ƒ /api/admin/invoices                                    215 B         451 kB
├ ƒ /api/admin/invoices/[id]                               214 B         451 kB
├ ƒ /api/admin/invoices/by-quote/[quoteId]                 214 B         451 kB
├ ƒ /api/admin/orders                                      214 B         451 kB
├ ƒ /api/admin/paper-types                                 214 B         451 kB
├ ƒ /api/admin/portfolio                                   214 B         451 kB
├ ƒ /api/admin/portfolio/[id]                              214 B         451 kB
├ ƒ /api/admin/projects                                    214 B         451 kB
├ ƒ /api/admin/projects/[id]                               214 B         451 kB
├ ƒ /api/admin/projects/[id]/documents                     214 B         451 kB
├ ƒ /api/admin/projects/[id]/members                       214 B         451 kB
├ ƒ /api/admin/quotes                                      214 B         451 kB
├ ƒ /api/admin/quotes/[id]                                 214 B         451 kB
├ ƒ /api/admin/quotes/convert                              215 B         451 kB
├ ƒ /api/admin/redis/init                                  214 B         451 kB
├ ƒ /api/admin/revalidate                                  214 B         451 kB
├ ƒ /api/admin/roles                                       215 B         451 kB
├ ƒ /api/admin/roles/[id]                                  214 B         451 kB
├ ƒ /api/admin/s3-catalogue-images                         214 B         451 kB
├ ƒ /api/admin/s3-images                                   214 B         451 kB
├ ƒ /api/admin/scheduled-blog-posts                        214 B         451 kB
├ ƒ /api/admin/scheduled-blog-posts/[id]                   214 B         451 kB
├ ƒ /api/admin/scheduled-blog-posts/run-now                214 B         451 kB
├ ƒ /api/admin/scripts                                     214 B         451 kB
├ ƒ /api/admin/scripts/[id]                                214 B         451 kB
├ ƒ /api/admin/send-email                                  214 B         451 kB
├ ƒ /api/admin/services                                    214 B         451 kB
├ ƒ /api/admin/services/[id]                               214 B         451 kB
├ ƒ /api/admin/settings                                    214 B         451 kB
├ ƒ /api/admin/settings/storage                            214 B         451 kB
├ ƒ /api/admin/settings/storage/[id]                       214 B         451 kB
├ ƒ /api/admin/settings/storage/[id]/default               215 B         451 kB
├ ƒ /api/admin/settings/storage/debug                      214 B         451 kB
├ ƒ /api/admin/settings/storage/init-from-env              214 B         451 kB
├ ƒ /api/admin/stats                                       214 B         451 kB
├ ƒ /api/admin/stats/categories                            214 B         451 kB
├ ƒ /api/admin/stats/dashboard                             214 B         451 kB
├ ƒ /api/admin/stats/dashboard-test                        214 B         451 kB
├ ƒ /api/admin/stats/images                                214 B         451 kB
├ ƒ /api/admin/stats/server                                214 B         451 kB
├ ƒ /api/admin/stats/storage                               214 B         451 kB
├ ƒ /api/admin/storage/config                              214 B         451 kB
├ ƒ /api/admin/tags                                        214 B         451 kB
├ ƒ /api/admin/tasks                                       214 B         451 kB
├ ƒ /api/admin/tasks/[id]                                  213 B         451 kB
├ ƒ /api/admin/team                                        214 B         451 kB
├ ƒ /api/admin/team/[id]                                   214 B         451 kB
├ ƒ /api/admin/testimonials                                215 B         451 kB
├ ƒ /api/admin/testimonials/[id]                           214 B         451 kB
├ ƒ /api/admin/testimonials/[id]/toggle                    214 B         451 kB
├ ƒ /api/admin/tests                                       214 B         451 kB
├ ƒ /api/admin/time-tracking                               214 B         451 kB
├ ƒ /api/admin/time-tracking/[id]                          215 B         451 kB
├ ƒ /api/admin/upload                                      214 B         451 kB
├ ƒ /api/admin/uploadImage                                 214 B         451 kB
├ ƒ /api/admin/users                                       214 B         451 kB
├ ƒ /api/admin/users/[id]                                  214 B         451 kB
├ ƒ /api/admin/website-portfolio                           214 B         451 kB
├ ƒ /api/admin/website-portfolio/[id]                      214 B         451 kB
├ ƒ /api/auth/csrf                                         214 B         451 kB
├ ƒ /api/auth/login                                        214 B         451 kB
├ ƒ /api/auth/logout                                       214 B         451 kB
├ ƒ /api/auth/me                                           214 B         451 kB
├ ƒ /api/blog                                              214 B         451 kB
├ ƒ /api/blog/[slug]                                       214 B         451 kB
├ ƒ /api/catalogue                                         214 B         451 kB
├ ƒ /api/catalogue/[id]                                    214 B         451 kB
├ ƒ /api/categories                                        214 B         451 kB
├ ƒ /api/client-portal/activity                            214 B         451 kB
├ ƒ /api/client-portal/auth/2fa/setup                      214 B         451 kB
├ ƒ /api/client-portal/auth/change-password                214 B         451 kB
├ ƒ /api/client-portal/auth/forgot-password                214 B         451 kB
├ ƒ /api/client-portal/auth/login                          214 B         451 kB
├ ƒ /api/client-portal/auth/logout                         214 B         451 kB
├ ƒ /api/client-portal/auth/reset-password                 214 B         451 kB
├ ƒ /api/client-portal/auth/signup                         214 B         451 kB
├ ƒ /api/client-portal/auth/verify                         214 B         451 kB
├ ƒ /api/client-portal/dashboard                           214 B         451 kB
├ ƒ /api/client-portal/documents                           214 B         451 kB
├ ƒ /api/client-portal/documents/[documentId]/delete       214 B         451 kB
├ ƒ /api/client-portal/documents/[documentId]/download     214 B         451 kB
├ ƒ /api/client-portal/documents/stats                     214 B         451 kB
├ ƒ /api/client-portal/documents/upload                    214 B         451 kB
├ ƒ /api/client-portal/export                              214 B         451 kB
├ ƒ /api/client-portal/feedback                            214 B         451 kB
├ ƒ /api/client-portal/invoices                            214 B         451 kB
├ ƒ /api/client-portal/milestones/[milestoneId]/approve    213 B         451 kB
├ ƒ /api/client-portal/notifications                       214 B         451 kB
├ ƒ /api/client-portal/notifications/create-sample         214 B         451 kB
├ ƒ /api/client-portal/profile                             214 B         451 kB
├ ƒ /api/client-portal/projects                            214 B         451 kB
├ ƒ /api/client-portal/projects/create-sample              214 B         451 kB
├ ƒ /api/client-portal/search                              214 B         451 kB
├ ƒ /api/client-portal/settings                            214 B         451 kB
├ ƒ /api/client-portal/support                             214 B         451 kB
├ ƒ /api/colors/trending                                   214 B         451 kB
├ ƒ /api/contact                                           214 B         451 kB
├ ƒ /api/debug/catalogue-upload-test                       214 B         451 kB
├ ƒ /api/debug/quotes/[id]                                 214 B         451 kB
├ ƒ /api/delete-local-images                               214 B         451 kB
├ ƒ /api/family-savings/dashboard                          214 B         451 kB
├ ƒ /api/family-savings/transactions                       214 B         451 kB
├ ƒ /api/health                                            214 B         451 kB
├ ƒ /api/images                                            214 B         451 kB
├ ƒ /api/images/homepage                                   214 B         451 kB
├ ƒ /api/invoices/[id]                                     214 B         451 kB
├ ƒ /api/logos                                             214 B         451 kB
├ ƒ /api/meta-pixel                                        214 B         451 kB
├ ƒ /api/optimize-image                                    214 B         451 kB
├ ƒ /api/orders                                            214 B         451 kB
├ ƒ /api/orders/[id]                                       214 B         451 kB
├ ƒ /api/portfolio                                         214 B         451 kB
├ ƒ /api/portfolio/categories                              214 B         451 kB
├ ƒ /api/portfolio/random                                  214 B         451 kB
├ ƒ /api/pricing/calculate                                 213 B         451 kB
├ ƒ /api/quotes/[id]                                       214 B         451 kB
├ ƒ /api/receipts/[id]                                     214 B         451 kB
├ ƒ /api/scripts                                           214 B         451 kB
├ ƒ /api/services                                          214 B         451 kB
├ ƒ /api/settings                                          214 B         451 kB
├ ƒ /api/team                                              214 B         451 kB
├ ƒ /api/test                                              214 B         451 kB
├ ƒ /api/test-params/[id]                                  214 B         451 kB
├ ƒ /api/testimonials                                      214 B         451 kB
├ ƒ /api/upload                                            214 B         451 kB
├ ƒ /api/upload/artwork                                    214 B         451 kB
├ ƒ /api/upload/artwork/download                           214 B         451 kB
├ ƒ /api/upload/chunk                                      214 B         451 kB
├ ƒ /api/upload/finalize                                   214 B         451 kB
├ ƒ /api/upload/init                                       214 B         451 kB
├ ƒ /api/website-portfolio                                 214 B         451 kB
├ ƒ /blog                                                1.18 kB         487 kB
├ ƒ /blog/[slug]                                          3.9 kB         490 kB
├ ○ /blog/sitemap.xml                                      214 B         451 kB
├ ○ /brand-colors                                        1.79 kB         496 kB
├ ○ /catalogue                                           4.26 kB         491 kB
├ ○ /client-portal                                       14.4 kB         501 kB
├ ○ /colors                                              3.16 kB         533 kB
├ ○ /contact                                               214 B         451 kB
├ ○ /debug/quotes                                         1.4 kB         452 kB
├ ○ /debug/upload-test                                   2.72 kB         454 kB
├ ○ /family-savings                                      2.59 kB         495 kB
├ ○ /freelance                                            6.3 kB         500 kB
├ ○ /graphics                                            6.38 kB         501 kB
├ ○ /logos                                               10.7 kB         540 kB          5m      1y
├ ○ /portfolio                                            3.8 kB         455 kB          5m      1y
├ ○ /privacy                                               214 B         451 kB
├ ○ /process                                             1.09 kB         452 kB
├ ƒ /product/[id]                                        12.2 kB         498 kB
├ ○ /request                                             3.96 kB         455 kB
├ ○ /robots.txt                                            214 B         451 kB
├ ○ /services                                            2.36 kB         489 kB
├ ○ /sitemap.xml                                           214 B         451 kB
├ ○ /social-media                                        3.39 kB         454 kB
├ ○ /terms                                               3.78 kB         498 kB
├ ○ /test-currency                                        1.5 kB         488 kB
├ ○ /test-enhanced-upload                                4.18 kB         497 kB
├ ○ /test-images                                         1.44 kB         452 kB
├ ○ /test-s3-images                                      1.82 kB         453 kB
├ ○ /test-settings                                         718 B         452 kB
├ ○ /training                                            4.11 kB         498 kB
├ ○ /unauthorized                                          214 B         451 kB
└ ○ /web-development                                      9.2 kB         503 kB
+ First Load JS shared by all                             451 kB
  ├ chunks/common-f3956634-0e4895392cee9a13.js           18.1 kB
  ├ chunks/vendor-2898f16f-df214e55ec708a29.js           19.1 kB
  ├ chunks/vendor-351e52ed-3cb776c49a8ecb7e.js           19.6 kB
  ├ chunks/vendor-362d063c-7808b1dfcc7160cf.js           13.4 kB
  ├ chunks/vendor-377fed06-3e2585210c6fe493.js           16.9 kB
  ├ chunks/vendor-48b8d778-91daf08cae271d8b.js           14.8 kB
  ├ chunks/vendor-4a7382ad-fd0e56c5b963a84b.js           11.7 kB
  ├ chunks/vendor-51a8d53d-f89581907fa4b69c.js           19.2 kB
  ├ chunks/vendor-6185be05-52d6caee2c822821.js           11.6 kB
  ├ chunks/vendor-7e004f95-1de96cdaa66709c1.js           12.5 kB
  ├ chunks/vendor-83113d1a-18a52daee1fad3c9.js           20.7 kB
  ├ chunks/vendor-8af8abc7-6caa9d486c0e3e63.js           12.6 kB
  ├ chunks/vendor-8cbd2506-5c660e7c276eb393.js           17.9 kB
  └ chunks/vendor-a80e7ea4-014b4f0892778619.js           18.7 kB
  ├ chunks/vendor-aacc2dbb-880460777eec8943.js           36.3 kB
  ├ chunks/vendor-b9f70e77-c76dbf072ca35819.js           24.6 kB
  ├ chunks/vendor-fa70753b-d237cccf8e0d57b3.js           33.3 kB
  ├ chunks/vendor-ff30e0d3-60fd7ff45f8b2761.js           53.1 kB
  └ other shared chunks (total)                            77 kB

Route (pages)                                               Size  First Load JS
┌ ○ /404 (547 ms)                                          769 B         496 kB
└ ○ /500 (544 ms)                                          681 B         496 kB
+ First Load JS shared by all                             495 kB
  ├ chunks/common-f3956634-0e4895392cee9a13.js           18.1 kB
  ├ chunks/react-759409ed0e4950f8.js                     43.7 kB
  ├ chunks/vendor-2898f16f-df214e55ec708a29.js           19.1 kB
  ├ chunks/vendor-351e52ed-3cb776c49a8ecb7e.js           19.6 kB
  ├ chunks/vendor-362d063c-7808b1dfcc7160cf.js           13.4 kB
  ├ chunks/vendor-377fed06-3e2585210c6fe493.js           16.9 kB
  ├ chunks/vendor-48b8d778-91daf08cae271d8b.js           14.8 kB
  ├ chunks/vendor-4a7382ad-fd0e56c5b963a84b.js           11.7 kB
  ├ chunks/vendor-51a8d53d-f89581907fa4b69c.js           19.2 kB
  ├ chunks/vendor-6185be05-52d6caee2c822821.js           11.6 kB
  ├ chunks/vendor-7e004f95-1de96cdaa66709c1.js           12.5 kB
  ├ chunks/vendor-83113d1a-18a52daee1fad3c9.js           20.7 kB
  ├ chunks/vendor-8af8abc7-6caa9d486c0e3e63.js           12.6 kB
  ├ chunks/vendor-8cbd2506-5c660e7c276eb393.js           17.9 kB
  ├ chunks/vendor-a80e7ea4-014b4f0892778619.js           18.7 kB
  ├ chunks/vendor-aacc2dbb-880460777eec8943.js           36.3 kB
  ├ chunks/vendor-b9f70e77-c76dbf072ca35819.js           24.6 kB
  ├ chunks/vendor-fa70753b-d237cccf8e0d57b3.js           33.3 kB
  ├ chunks/vendor-ff30e0d3-60fd7ff45f8b2761.js           53.1 kB
  └ other shared chunks (total)                          77.2 kB

ƒ Middleware                                             45.1 kB

○  (Static)   prerendered as static content
ƒ  (Dynamic)  server-rendered on demand

