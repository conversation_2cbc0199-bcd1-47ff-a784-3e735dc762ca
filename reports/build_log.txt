
> mocky-digital@0.1.0 build
> next build

   ▲ Next.js 15.2.4
   - Environments: .env.local, .env

   Creating an optimized production build ...
 ✓ Compiled successfully
   Skipping validation of types
   Skipping linting
   Collecting page data ...
   Generating static pages (0/190) ...
   Generating static pages (47/190) 
   Generating static pages (94/190) 
   Generating static pages (142/190) 
 ✓ Generating static pages (190/190)
   Finalizing page optimization ...
   Collecting build traces ...

Route (app)                                                 Size  First Load JS  Revalidate  Expire
┌ ○ /                                                    8.88 kB         152 kB
├ ○ /_not-found                                            535 B         101 kB
├ ○ /about                                               6.99 kB         153 kB
├ ○ /admin/activity-logs                                  4.5 kB         128 kB
├ ○ /admin/analytics                                      4.4 kB         115 kB
├ ○ /admin/blog                                           5.1 kB         125 kB
├ ƒ /admin/blog/[id]/edit                                 3.1 kB         244 kB
├ ○ /admin/blog/categories                               5.43 kB         136 kB
├ ○ /admin/blog/new                                      2.68 kB         244 kB
├ ○ /admin/calendar                                      11.4 kB         126 kB
├ ○ /admin/catalogue                                     3.87 kB         108 kB
├ ƒ /admin/catalogue/[id]/edit                           6.99 kB         120 kB
├ ○ /admin/catalogue/new                                 4.37 kB         117 kB
├ ○ /admin/categories                                    2.46 kB         116 kB
├ ƒ /admin/categories/[id]/edit                          2.86 kB         114 kB
├ ○ /admin/categories/new                                2.66 kB         113 kB
├ ○ /admin/clients                                       6.85 kB         120 kB
├ ƒ /admin/clients/[id]                                    10 kB         127 kB
├ ○ /admin/communications                                7.49 kB         123 kB
├ ○ /admin/dashboard                                      7.9 kB         117 kB
├ ○ /admin/database                                      7.08 kB         113 kB
├ ○ /admin/invoices                                      4.49 kB         131 kB
├ ƒ /admin/invoices/[id]                                 5.28 kB         131 kB
├ ○ /admin/invoices/new                                  5.21 kB         131 kB
├ ○ /admin/login                                         3.47 kB         129 kB
├ ○ /admin/portfolio                                     10.3 kB         131 kB
├ ƒ /admin/portfolio/[id]/edit                           4.58 kB         124 kB
├ ○ /admin/portfolio/new                                 4.44 kB         124 kB
├ ○ /admin/posts                                         4.87 kB         125 kB
├ ○ /admin/projects                                      5.24 kB         123 kB
├ ƒ /admin/projects/[id]                                 11.3 kB         128 kB
├ ƒ /admin/projects/[id]/edit                            3.49 kB         114 kB
├ ○ /admin/quotes                                        4.52 kB         131 kB
├ ƒ /admin/quotes/[id]                                   5.47 kB         132 kB
├ ○ /admin/quotes/new                                    5.31 kB         131 kB
├ ○ /admin/receipts                                      4.31 kB         121 kB
├ ƒ /admin/receipts/[id]                                  5.1 kB         122 kB
├ ○ /admin/receipts/new                                  4.98 kB         125 kB
├ ○ /admin/receipts/quick                                17.7 kB         138 kB
├ ○ /admin/roles                                         4.51 kB         131 kB
├ ƒ /admin/roles/edit/[id]                               2.14 kB         131 kB
├ ○ /admin/roles/new                                      1.6 kB         125 kB
├ ○ /admin/scheduled-blog-posts                          3.01 kB         151 kB
├ ○ /admin/scheduled-blog-posts/new                      2.47 kB         144 kB
├ ○ /admin/services                                      6.87 kB         124 kB
├ ○ /admin/settings                                      4.17 kB         108 kB
├ ○ /admin/settings/cache                                 8.4 kB         117 kB
├ ○ /admin/settings/scripts                               5.1 kB         114 kB
├ ○ /admin/settings/storage                              6.52 kB         110 kB
├ ○ /admin/settings/storage/debug                        2.48 kB         106 kB
├ ○ /admin/settings/storage/test-endpoint                1.24 kB         105 kB
├ ○ /admin/tasks                                         6.94 kB         122 kB
├ ○ /admin/team                                          3.67 kB         127 kB
├ ƒ /admin/team/[id]/edit                                3.63 kB         131 kB
├ ○ /admin/team/new                                      2.81 kB         117 kB
├ ○ /admin/testimonials                                  5.38 kB         131 kB
├ ƒ /admin/testimonials/[id]/edit                        3.68 kB         114 kB
├ ○ /admin/testimonials/new                              3.57 kB         114 kB
├ ○ /admin/tests                                         22.3 kB         130 kB
├ ○ /admin/time-tracking                                 8.62 kB         119 kB
├ ○ /admin/transactions                                  4.56 kB         131 kB
├ ○ /admin/transactions/upload                           3.63 kB         114 kB
├ ○ /admin/users                                         4.53 kB         131 kB
├ ƒ /admin/users/edit/[id]                               4.23 kB         130 kB
├ ○ /admin/users/new                                     3.45 kB         114 kB
├ ○ /admin/website-portfolio                             6.04 kB         122 kB
├ ƒ /admin/website-portfolio/[id]/edit                   4.66 kB         124 kB
├ ○ /admin/website-portfolio/new                          4.4 kB         124 kB
├ ƒ /api/admin/activity-logs                               535 B         101 kB
├ ƒ /api/admin/analytics                                   535 B         101 kB
├ ƒ /api/admin/blog                                        535 B         101 kB
├ ƒ /api/admin/blog/[id]                                   535 B         101 kB
├ ƒ /api/admin/blog/categories                             535 B         101 kB
├ ƒ /api/admin/blog/categories/[id]                        535 B         101 kB
├ ƒ /api/admin/cache                                       535 B         101 kB
├ ƒ /api/admin/cache/redis                                 535 B         101 kB
├ ƒ /api/admin/cache/warm                                  535 B         101 kB
├ ƒ /api/admin/cache/warmup                                535 B         101 kB
├ ƒ /api/admin/calendar                                    535 B         101 kB
├ ƒ /api/admin/calendar/events                             535 B         101 kB
├ ƒ /api/admin/calendar/events/[id]                        535 B         101 kB
├ ƒ /api/admin/catalogue                                   535 B         101 kB
├ ƒ /api/admin/catalogue-v3                                535 B         101 kB
├ ƒ /api/admin/catalogue-v3/[id]                           535 B         101 kB
├ ƒ /api/admin/catalogue/[id]                              535 B         101 kB
├ ƒ /api/admin/catalogue/bulk-delete                       535 B         101 kB
├ ƒ /api/admin/categories                                  535 B         101 kB
├ ƒ /api/admin/categories/[id]                             535 B         101 kB
├ ƒ /api/admin/clients                                     535 B         101 kB
├ ƒ /api/admin/clients/[id]                                535 B         101 kB
├ ƒ /api/admin/clients/documents                           535 B         101 kB
├ ƒ /api/admin/clients/reminders                           535 B         101 kB
├ ƒ /api/admin/communications                              535 B         101 kB
├ ƒ /api/admin/database/backup                             535 B         101 kB
├ ƒ /api/admin/database/backup/[id]                        535 B         101 kB
├ ƒ /api/admin/database/backup/[id]/download               535 B         101 kB
├ ƒ /api/admin/database/backups                            535 B         101 kB
├ ƒ /api/admin/database/enterprise-backup                  535 B         101 kB
├ ƒ /api/admin/database/restore                            535 B         101 kB
├ ƒ /api/admin/database/s3-backups                         535 B         101 kB
├ ƒ /api/admin/diagnostic/form-upload                      535 B         101 kB
├ ƒ /api/admin/health                                      535 B         101 kB
├ ƒ /api/admin/health/redis                                535 B         101 kB
├ ƒ /api/admin/images                                      535 B         101 kB
├ ƒ /api/admin/images/[key]                                535 B         101 kB
├ ƒ /api/admin/invoices                                    535 B         101 kB
├ ƒ /api/admin/invoices/[id]                               535 B         101 kB
├ ƒ /api/admin/invoices/by-quote/[quoteId]                 535 B         101 kB
├ ƒ /api/admin/portfolio                                   535 B         101 kB
├ ƒ /api/admin/portfolio/[id]                              535 B         101 kB
├ ƒ /api/admin/projects                                    535 B         101 kB
├ ƒ /api/admin/projects/[id]                               535 B         101 kB
├ ƒ /api/admin/projects/[id]/documents                     535 B         101 kB
├ ƒ /api/admin/projects/[id]/members                       535 B         101 kB
├ ƒ /api/admin/quotes                                      535 B         101 kB
├ ƒ /api/admin/quotes/[id]                                 535 B         101 kB
├ ƒ /api/admin/quotes/convert                              535 B         101 kB
├ ƒ /api/admin/receipts                                    535 B         101 kB
├ ƒ /api/admin/receipts/[id]                               535 B         101 kB
├ ƒ /api/admin/receipts/check                              535 B         101 kB
├ ƒ /api/admin/redis/init                                  535 B         101 kB
├ ƒ /api/admin/revalidate                                  535 B         101 kB
├ ƒ /api/admin/roles                                       535 B         101 kB
├ ƒ /api/admin/roles/[id]                                  535 B         101 kB
├ ƒ /api/admin/s3-catalogue-images                         535 B         101 kB
├ ƒ /api/admin/s3-images                                   535 B         101 kB
├ ƒ /api/admin/scheduled-blog-posts                        535 B         101 kB
├ ƒ /api/admin/scheduled-blog-posts/[id]                   535 B         101 kB
├ ƒ /api/admin/scheduled-blog-posts/run-now                535 B         101 kB
├ ƒ /api/admin/scripts                                     535 B         101 kB
├ ƒ /api/admin/scripts/[id]                                535 B         101 kB
├ ƒ /api/admin/services                                    535 B         101 kB
├ ƒ /api/admin/services/[id]                               535 B         101 kB
├ ƒ /api/admin/settings                                    535 B         101 kB
├ ƒ /api/admin/settings/storage                            535 B         101 kB
├ ƒ /api/admin/settings/storage/[id]                       535 B         101 kB
├ ƒ /api/admin/settings/storage/[id]/default               535 B         101 kB
├ ƒ /api/admin/settings/storage/debug                      535 B         101 kB
├ ƒ /api/admin/settings/storage/init-from-env              535 B         101 kB
├ ƒ /api/admin/stats/categories                            535 B         101 kB
├ ƒ /api/admin/stats/dashboard                             535 B         101 kB
├ ƒ /api/admin/stats/dashboard-test                        535 B         101 kB
├ ƒ /api/admin/stats/images                                535 B         101 kB
├ ƒ /api/admin/stats/server                                535 B         101 kB
├ ƒ /api/admin/stats/storage                               535 B         101 kB
├ ƒ /api/admin/storage/config                              535 B         101 kB
├ ƒ /api/admin/tags                                        535 B         101 kB
├ ƒ /api/admin/tasks                                       535 B         101 kB
├ ƒ /api/admin/tasks/[id]                                  535 B         101 kB
├ ƒ /api/admin/team                                        535 B         101 kB
├ ƒ /api/admin/team/[id]                                   535 B         101 kB
├ ƒ /api/admin/testimonials                                535 B         101 kB
├ ƒ /api/admin/testimonials/[id]                           535 B         101 kB
├ ƒ /api/admin/testimonials/[id]/toggle                    535 B         101 kB
├ ƒ /api/admin/tests                                       535 B         101 kB
├ ƒ /api/admin/time-tracking                               535 B         101 kB
├ ƒ /api/admin/time-tracking/[id]                          535 B         101 kB
├ ƒ /api/admin/transactions                                535 B         101 kB
├ ƒ /api/admin/transactions/[id]                           535 B         101 kB
├ ƒ /api/admin/transactions/batch                          535 B         101 kB
├ ƒ /api/admin/upload                                      535 B         101 kB
├ ƒ /api/admin/uploadImage                                 535 B         101 kB
├ ƒ /api/admin/users                                       535 B         101 kB
├ ƒ /api/admin/users/[id]                                  535 B         101 kB
├ ƒ /api/admin/website-portfolio                           535 B         101 kB
├ ƒ /api/admin/website-portfolio/[id]                      535 B         101 kB
├ ƒ /api/auth/[...nextauth]                                535 B         101 kB
├ ƒ /api/auth/test                                         535 B         101 kB
├ ƒ /api/blog                                              535 B         101 kB
├ ƒ /api/blog/[slug]                                       535 B         101 kB
├ ƒ /api/catalogue                                         535 B         101 kB
├ ƒ /api/catalogue-v2                                      535 B         101 kB
├ ƒ /api/catalogue/[id]                                    535 B         101 kB
├ ƒ /api/categories                                        535 B         101 kB
├ ƒ /api/client-portal/activity                            535 B         101 kB
├ ƒ /api/client-portal/auth/2fa/setup                      535 B         101 kB
├ ƒ /api/client-portal/auth/change-password                535 B         101 kB
├ ƒ /api/client-portal/auth/forgot-password                535 B         101 kB
├ ƒ /api/client-portal/auth/login                          535 B         101 kB
├ ƒ /api/client-portal/auth/logout                         535 B         101 kB
├ ƒ /api/client-portal/auth/reset-password                 535 B         101 kB
├ ƒ /api/client-portal/auth/signup                         535 B         101 kB
├ ƒ /api/client-portal/auth/verify                         535 B         101 kB
├ ƒ /api/client-portal/dashboard                           535 B         101 kB
├ ƒ /api/client-portal/documents                           535 B         101 kB
├ ƒ /api/client-portal/documents/[documentId]/delete       535 B         101 kB
├ ƒ /api/client-portal/documents/[documentId]/download     535 B         101 kB
├ ƒ /api/client-portal/documents/stats                     535 B         101 kB
├ ƒ /api/client-portal/documents/upload                    535 B         101 kB
├ ƒ /api/client-portal/export                              535 B         101 kB
├ ƒ /api/client-portal/feedback                            535 B         101 kB
├ ƒ /api/client-portal/invoices                            535 B         101 kB
├ ƒ /api/client-portal/milestones/[milestoneId]/approve    535 B         101 kB
├ ƒ /api/client-portal/notifications                       535 B         101 kB
├ ƒ /api/client-portal/notifications/create-sample         535 B         101 kB
├ ƒ /api/client-portal/profile                             535 B         101 kB
├ ƒ /api/client-portal/projects                            535 B         101 kB
├ ƒ /api/client-portal/projects/create-sample              535 B         101 kB
├ ƒ /api/client-portal/search                              535 B         101 kB
├ ƒ /api/client-portal/settings                            535 B         101 kB
├ ƒ /api/client-portal/support                             535 B         101 kB
├ ƒ /api/colors/trending                                   535 B         101 kB
├ ƒ /api/contact                                           535 B         101 kB
├ ƒ /api/debug/quotes/[id]                                 535 B         101 kB
├ ƒ /api/delete-local-images                               535 B         101 kB
├ ƒ /api/health                                            535 B         101 kB
├ ƒ /api/images                                            535 B         101 kB
├ ƒ /api/images/homepage                                   535 B         101 kB
├ ƒ /api/invoices/[id]                                     535 B         101 kB
├ ƒ /api/logos                                             535 B         101 kB
├ ƒ /api/meta-pixel                                        535 B         101 kB
├ ƒ /api/optimize-image                                    535 B         101 kB
├ ƒ /api/portfolio                                         535 B         101 kB
├ ƒ /api/portfolio/categories                              535 B         101 kB
├ ƒ /api/portfolio/random                                  535 B         101 kB
├ ƒ /api/quotes/[id]                                       535 B         101 kB
├ ƒ /api/receipts/[id]                                     535 B         101 kB
├ ƒ /api/scripts                                           535 B         101 kB
├ ƒ /api/services                                          535 B         101 kB
├ ƒ /api/settings                                          535 B         101 kB
├ ƒ /api/team                                              535 B         101 kB
├ ƒ /api/test                                              535 B         101 kB
├ ƒ /api/test-params/[id]                                  535 B         101 kB
├ ƒ /api/testimonials                                      535 B         101 kB
├ ƒ /api/upload                                            535 B         101 kB
├ ƒ /api/website-portfolio                                 535 B         101 kB
├ ƒ /blog                                                1.46 kB         111 kB
├ ƒ /blog/[slug]                                           180 B         104 kB
├ ○ /brand-colors                                        2.81 kB         137 kB
├ ○ /catalogue                                           4.69 kB         109 kB
├ ○ /client-portal                                       17.5 kB         118 kB
├ ○ /colors                                              4.28 kB         144 kB
├ ○ /contact                                               535 B         101 kB
├ ○ /debug/quotes                                        1.29 kB         102 kB
├ ○ /freelance                                           6.19 kB         152 kB
├ ○ /graphics                                             6.3 kB         154 kB
├ ○ /logos                                                 12 kB         158 kB          5m      1y
├ ○ /portfolio                                              4 kB         113 kB          5m      1y
├ ○ /privacy                                               535 B         101 kB
├ ○ /process                                               984 B         102 kB
├ ƒ /product/[id]                                           3 kB         107 kB
├ ○ /request                                             4.03 kB         105 kB
├ ○ /robots.txt                                            535 B         101 kB
├ ○ /services                                            2.69 kB         104 kB
├ ○ /sitemap.xml                                           535 B         101 kB
├ ○ /social-media                                        3.44 kB         104 kB
├ ○ /terms                                               3.66 kB         138 kB
├ ○ /test-currency                                       1.39 kB         106 kB
├ ○ /test-images                                         1.53 kB         102 kB
├ ○ /test-s3-images                                      2.58 kB         109 kB
├ ○ /test-settings                                         613 B         102 kB
├ ○ /training                                            4.16 kB         139 kB
└ ○ /web-development                                     10.9 kB         154 kB
+ First Load JS shared by all                             101 kB
  ├ chunks/1684-c6ec21b351127dab.js                      45.7 kB
  ├ chunks/4bd1b696-82efa6df2294e1e1.js                  53.3 kB
  └ other shared chunks (total)                          2.01 kB

Route (pages)                                               Size  First Load JS
┌ ○ /404                                                   683 B        83.7 kB
└ ○ /500                                                   577 B        83.6 kB
+ First Load JS shared by all                            80.9 kB
  ├ chunks/framework-c89ce63e64c69287.js                 44.8 kB
  ├ chunks/main-9ff9047afcf23d9c.js                      34.1 kB
  └ other shared chunks (total)                          1.99 kB

ƒ Middleware                                             57.6 kB

○  (Static)   prerendered as static content
ƒ  (Dynamic)  server-rendered on demand

