server {
    listen 80;
    server_name www.mocky.co.ke;
    return 301 https://mocky.co.ke$request_uri;
}

server {
    listen 80;
    server_name mocky.co.ke;
    return 301 https://mocky.co.ke$request_uri;
}

server {
    listen 443 ssl;
    server_name www.mocky.co.ke;
    ssl_certificate /etc/letsencrypt/live/mocky.co.ke/fullchain.pem; # managed by Certbot
    ssl_certificate_key /etc/letsencrypt/live/mocky.co.ke/privkey.pem; # managed by Certbot
    include /etc/letsencrypt/options-ssl-nginx.conf; # managed by Certbot
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem; # managed by Certbot
    return 301 https://mocky.co.ke$request_uri;
}

server {
    listen 443 ssl;
    server_name mocky.co.ke;
    # Production settings
    error_log /var/log/nginx/mocky-error.log;
    access_log /var/log/nginx/mocky-access.log;

    # Root directory for static files
    root /var/www/mocky.co.ke;

    # Settings for images in uploads directory
    location /uploads/ {
        root /var/www;
        access_log off;
        add_header Access-Control-Allow-Origin "https://mocky.co.ke";

        # Disable directory listings for production
        autoindex off;

        # Add caching
        expires 7d;
        add_header Cache-Control "public, max-age=604800";

        # MIME type handling for images
        include /etc/nginx/mime.types;
    }

    # Static files
    location /images/ {
        root /var/www/mocky.co.ke/public;
        add_header Access-Control-Allow-Origin "https://mocky.co.ke";

        # Production settings
        try_files $uri =404;
        autoindex off;
        access_log off;

        # Add caching
        expires 7d;
        add_header Cache-Control "public, max-age=604800";

        # Enable compression
        gzip on;
        gzip_types image/svg+xml;

        # Add proper MIME types
        types {
            image/jpeg jpg jpeg;
            image/png png;
            image/gif gif;
            image/webp webp;
            image/svg+xml svg svgz;
        }
    }

    # Next.js static files - optimized for React
    location /_next/static {
        alias /var/www/mocky/.next/static;
        access_log off;

        # Improved caching for static assets
        expires 30d;
        add_header Cache-Control "public, max-age=2592000, immutable";

        # Production CORS headers
        add_header Access-Control-Allow-Origin "*";

        # Compression for better performance
        gzip on;
        gzip_vary on;
        gzip_proxied any;
        gzip_comp_level 6;
        gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    }

    # React chunk files need special handling
    location /_next/data/ {
        add_header Cache-Control "public, max-age=31536000, immutable";
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }

    # API routes with no caching
    location /api/ {
        # Standard timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;

        # Production server
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Disable caching for API routes
        add_header Cache-Control "no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0";
        expires -1;

        # CORS headers for API
        add_header Access-Control-Allow-Origin "https://mocky.co.ke";
        add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
        add_header Access-Control-Allow-Headers "X-Requested-With, Content-Type, Accept, Authorization";

        # Handle OPTIONS requests for CORS preflight
        if ($request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' 'https://mocky.co.ke';
            add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, DELETE, OPTIONS';
            add_header 'Access-Control-Allow-Headers' 'X-Requested-With, Content-Type, Accept, Authorization';
            add_header 'Access-Control-Max-Age' 1728000;
            add_header 'Content-Type' 'text/plain charset=UTF-8';
            add_header 'Content-Length' 0;
            return 204;
        }
    }

    # Proxy to production server
    location / {
        # Standard timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;

        # Production server
        proxy_pass http://127.0.0.1:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Production CORS headers
        add_header Access-Control-Allow-Origin "https://mocky.co.ke";
        add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
        add_header Access-Control-Allow-Headers "X-Requested-With, Content-Type, Accept, Authorization";
    }

    ssl_certificate /etc/letsencrypt/live/mocky.co.ke/fullchain.pem; # managed by Certbot
    ssl_certificate_key /etc/letsencrypt/live/mocky.co.ke/privkey.pem; # managed by Certbot
    include /etc/letsencrypt/options-ssl-nginx.conf; # managed by Certbot
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem; # managed by Certbot
}
