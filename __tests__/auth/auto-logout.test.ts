import { getToken } from 'next-auth/jwt'
import { NextRequest, NextResponse } from 'next/server'
import { auth } from '../../auth'
import middleware from '../../middleware'

// Mock next-auth/jwt and auth
jest.mock('next-auth/jwt')
jest.mock('../../auth', () => ({
  auth: jest.fn().mockImplementation((handler) => {
    return async (req: NextRequest) => {
      return handler({ req, auth: await getToken({ req }) })
    }
  })
}))

describe('Auto Logout Functionality', () => {
  const mockRequest = (pathname: string) => {
    return new NextRequest(new URL(`http://localhost:3000${pathname}`), {
      method: 'GET',
    })
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should allow active users to access protected routes', async () => {
    // Mock an active user's token
    ;(getToken as jest.Mock).mockResolvedValue({
      lastActivity: Date.now(), // Current timestamp
      role: 'admin',
    })

    const req = mockRequest('/admin/dashboard')
    const response = await middleware(req)

    expect(response).toBeInstanceOf(NextResponse)
    expect((response as NextResponse).status).not.toBe(302) // Should not redirect
  })

  it('should redirect inactive users to logout', async () => {
    // Mock an inactive user's token (last activity > 30 minutes ago)
    const thirtyOneMinutesAgo = Date.now() - 31 * 60 * 1000
    ;(getToken as jest.Mock).mockResolvedValue({
      lastActivity: thirtyOneMinutesAgo,
      role: 'admin',
    })

    const req = mockRequest('/admin/dashboard')
    const response = await middleware(req)

    expect(response).toBeInstanceOf(NextResponse)
    expect((response as NextResponse).status).toBe(302) // Should redirect
    expect((response as NextResponse).headers.get('location')).toContain('/admin/logout')
  })

  it('should allow unauthenticated users to access login page', async () => {
    // Mock no token for unauthenticated user
    ;(getToken as jest.Mock).mockResolvedValue(null)

    const req = mockRequest('/admin/login')
    const response = await middleware(req)

    expect(response).toBeInstanceOf(NextResponse)
    expect((response as NextResponse).status).not.toBe(302) // Should not redirect
  })

  it('should not check inactivity for login and logout pages', async () => {
    // Mock an inactive user's token
    const thirtyOneMinutesAgo = Date.now() - 31 * 60 * 1000
    ;(getToken as jest.Mock).mockResolvedValue({
      lastActivity: thirtyOneMinutesAgo,
      role: 'admin',
    })

    const loginReq = mockRequest('/admin/login')
    const logoutReq = mockRequest('/admin/logout')

    const loginResponse = await middleware(loginReq)
    const logoutResponse = await middleware(logoutReq)

    // Should redirect login to dashboard (standard behavior)
    expect((loginResponse as NextResponse).headers.get('location')).toContain('/admin/dashboard')
    
    // Should allow access to logout page despite inactivity
    expect((logoutResponse as NextResponse).status).not.toBe(302)
  })

  it('should handle missing lastActivity in token', async () => {
    // Mock token without lastActivity field
    ;(getToken as jest.Mock).mockResolvedValue({
      role: 'admin',
    })

    const req = mockRequest('/admin/dashboard')
    const response = await middleware(req)

    expect(response).toBeInstanceOf(NextResponse)
    expect((response as NextResponse).status).toBe(302) // Should redirect to logout
    expect((response as NextResponse).headers.get('location')).toContain('/admin/logout')
  })
}) 