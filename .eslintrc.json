{"extends": ["next/core-web-vitals", "next/typescript"], "plugins": ["@typescript-eslint", "react-hooks"], "rules": {"@typescript-eslint/no-unused-vars": "error", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/prefer-const": "error", "react-hooks/rules-of-hooks": "error", "react-hooks/exhaustive-deps": "warn", "prefer-const": "error", "no-var": "error", "no-console": "warn", "eqeqeq": ["error", "always"], "curly": ["error", "all"], "no-unused-expressions": "error", "no-duplicate-imports": "error"}, "env": {"browser": true, "node": true, "es6": true}, "parserOptions": {"ecmaVersion": 2020, "sourceType": "module", "ecmaFeatures": {"jsx": true}}}