import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

const updatedDescriptions = {
  'Company Profile': 'Professional 15-page company profile document showcasing your brand story, services, team, and achievements. Includes executive summary, mission/vision, and high-quality design layout.',
  'Event Programs': 'Elegant event programs and booklets with customizable layouts, high-quality paper options, and professional finishing. Perfect for weddings, conferences, and special occasions.',
  'T-Shirt Printing': 'Premium quality custom t-shirt printing service with various fabric options, printing techniques (DTG, screen printing, vinyl), and sizes. Minimum order of 5 pieces.',
  'Company Letterheads': 'Custom letterhead design and printing on premium paper stock. Includes your company logo, contact information, and professional layout for official communications.',
  'Certificates': 'Premium quality certificates printed on thick paper stock with optional metallic foiling, embossing, and custom designs. Ideal for awards, achievements, and recognition.',
  'Business Cards': 'Professional business cards printed on 350gsm art card with options for spot UV, embossing, and metallic foiling. Available in single or double-sided printing.',
  'Desk Calendars': 'Custom desk calendars with 12 monthly pages, sturdy stand, and high-quality paper. Perfect for corporate gifts and office organization.',
  'Tear Drop Banners': 'Durable outdoor teardrop banners with aluminum pole system and water base. Full-color printing on 115gsm knitted polyester fabric. Height: 3.5m',
  'Envelopes': 'Custom printed envelopes in various sizes (DL, C4, C5) with your company branding. Available in white or manila paper with window or plain options.',
  'Postcards': 'High-quality postcards printed on 300gsm art card with UV coating. Perfect for marketing campaigns, invitations, and promotional materials.',
  'Backdrop Banners': 'Large format backdrop banners for events and photo sessions. Printed on 440gsm PVC with reinforced edges and eyelets. Available up to 3m × 6m.',
  'X-Banner Stands': 'Portable X-banner display stand with lightweight aluminum frame. Includes carrying bag and high-quality banner print. Banner size: 60cm × 160cm.',
  'Branded Mugs Premium': 'Premium ceramic mugs with dishwasher-safe full-color printing. 11oz capacity with glossy or matte finish. Minimum order: 12 pieces.',
  'Premium Business Cards': 'Luxury business cards with special finishes including metallic foil, spot UV, soft-touch lamination, and colored edges. 400gsm premium paper.',
  'Custom Hoodies': 'High-quality hooded sweatshirts with custom printing or embroidery. Available in various colors and sizes. 280gsm cotton blend fabric.',
  'Polo T-Shirts': 'Premium polo shirts with embroidered logo. 220gsm cotton pique fabric available in multiple colors. Perfect for corporate uniforms.',
  'Receipt Books': 'Custom printed receipt books with duplicate or triplicate pages. Includes sequential numbering and perforation. Available in A5 or A6 size.',
  'Reflective Stickers': 'High-grade reflective vinyl stickers for enhanced visibility. Weather-resistant with strong adhesive. Perfect for safety signage and vehicle marking.',
  'Spiral Bound Books': 'Professional spiral binding for documents up to 300 pages. Options include clear front cover, back card, and custom printed covers.',
  'Branded Water Bottles': 'Premium stainless steel water bottles with laser engraving or full-color printing. 500ml capacity with leak-proof design.',
  'Branded Tote Bags': 'Eco-friendly cotton canvas tote bags with custom printing. 10oz fabric weight with reinforced handles. Minimum order: 25 pieces.',
  'Custom Diaries': 'Professional hardcover diaries with debossed logo. Includes monthly planner, note pages, and bookmark. A5 size with premium paper.',
  'Round Stickers': 'Custom circular stickers printed on vinyl or paper. Available in various sizes with glossy or matte lamination. Minimum order: 50 pieces.',
  'Student ID Cards': 'Durable PVC student ID cards with full-color printing. Includes photo, barcode/QR code, and optional smart chip. Standard CR80 size.',
  'ID Tags & Lanyards': 'Professional ID badge holders with custom printed lanyards. Available in horizontal or vertical orientation with various attachment options.',
  'Reflective Jackets': 'High-visibility safety jackets with reflective strips and custom branding. Available in multiple sizes with zip front closure.',
  'Directional Signs': 'Custom directional signage printed on durable materials. Available in various sizes with optional mounting hardware.',
  'Pop-up Banners': 'Portable pop-up display banners with aluminum frame. Quick setup design with carrying case. Standard size: 3m × 3m.',
  'Staff ID Cards': 'Professional PVC staff ID cards with security features. Includes photo, QR code, and optional access control chip. Standard CR80 size.',
  'Telescopic Banners': 'Height-adjustable banner stands with telescopic pole (1.6m-3m). Includes carrying bag and high-quality banner print.',
  'Branded Caps': 'Premium 6-panel caps with structured fit and custom embroidery. Available in various colors with adjustable closure.',
  'Branded Pens': 'High-quality ballpoint pens with custom logo printing. Available in multiple colors with smooth writing system.',
  'Wrist Bands': 'Silicone wristbands with debossed or printed design. Available in multiple colors and sizes. Minimum order: 100 pieces.',
  'Flash Drives': 'Custom branded USB flash drives with laser engraving. Available in various storage capacities with optional gift packaging.',
  'A4 Flyers': 'Full-color A4 flyers printed on 150gsm art paper. Available in single or double-sided with optional UV coating.',
  'A4 Brochures': 'Professional multi-page brochures on 150gsm art paper. Available with saddle stitching or perfect binding.',
  'Narrow Base Roll-Up Banners': 'Compact roll-up banner (85cm × 200cm) ideal for limited spaces. Includes aluminum base and carrying bag.',
  'Broad-base Roll-up Banner': 'Premium roll-up banner (120cm × 200cm) with stable luxury base. Perfect for high-traffic areas and events.',
  'Adjustable Backdrop Media Banners (3M × 2M)': 'Large format adjustable backdrop with telescopic frame. Includes high-quality fabric print and carrying bag.',
  'Broad Base Media Banner (2M × 2M)': 'Square format media wall with stable broad base. Perfect for step-and-repeat backdrops and photo opportunities.',
  '2025 Wall Calendars': 'Custom wall calendars for 2025 with 12 monthly pages. Printed on 200gsm art paper with wire-o binding.',
  'Branded Umbrellas': 'Premium automatic umbrellas with custom logo print. 190T pongee fabric with fiberglass frame for durability.',
  'Flyer Design (A5)': 'Professional A5 flyer design service with unlimited revisions. Includes print-ready files and source files.',
  'Poster Design (A3)': 'Creative A3 poster design service with attention to brand guidelines. Includes print-ready files and source files.',
  'Roll-up Banner Design': 'Custom roll-up banner design optimized for large format printing. Includes print-ready files and mockups.',
  'Premium Brand Identity Suite': 'Comprehensive brand identity package including logo, business cards, letterhead, and brand guidelines.',
  'Social Media Cover Design': 'Custom cover image design for multiple social platforms. Includes optimal sizes for each platform.',
  'Social Media Template Package': '20 custom social media post templates in your brand style. Includes both feed and story formats.',
  'PowerPoint Template Design': 'Custom PowerPoint template with master layouts, custom graphics, and brand colors. Includes training.',
  'Event Ticket Design': 'Professional event ticket design with security features. Includes both digital and print-ready formats.',
  'T-Shirt Graphic Design': 'Custom t-shirt graphic design optimized for various printing methods. Includes source files and mockups.'
};

async function updateCatalogueDescriptions() {
  try {
    console.log('Updating catalogue descriptions...\n');
    
    for (const [service, description] of Object.entries(updatedDescriptions)) {
      const result = await prisma.catalogue.updateMany({
        where: {
          service: {
            contains: service,
            mode: 'insensitive'
          }
        },
        data: {
          description
        }
      });
      
      if (result.count > 0) {
        console.log(`✅ Updated "${service}"`);
      } else {
        console.log(`⚠️  No match found for "${service}"`);
      }
    }
    
    console.log('\nUpdate completed!');
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

updateCatalogueDescriptions(); 