/**
 * Modern Authentication Hook
 * React hook for client-side authentication management
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { useSession, signIn, signOut } from 'next-auth/react';
import { AuthContext, LoginCredentials, Session } from '@/lib/auth/types';

interface LoginResponse {
  success: boolean;
  message: string;
  user?: {
    id: string;
    username: string;
    email: string;
    name: string | null;
    role: string;
    permissions: string[];
  };
  error?: string;
  code?: string;
}

interface LogoutResponse {
  success: boolean;
  message: string;
  error?: string;
}

export function useAuth(): AuthContext & {
  login: (credentials: LoginCredentials) => Promise<{ success: boolean; error?: string }>;
  logout: () => Promise<void>;
  checkSession: () => Promise<void>;
  hasPermission: (permission: string) => boolean;
} {
  const { data: session, status } = useSession();
  const [user, setUser] = useState<AuthContext['user']>(null);
  const [sessionData, setSessionData] = useState<Session | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [permissions, setPermissions] = useState<string[]>([]);
  
  const checkingSession = useRef(false);
  const mounted = useRef(true);
  const router = useRouter();

  // Sync NextAuth session with useAuth state
  useEffect(() => {
    if (status === 'loading') {
      setIsLoading(true);
      return;
    }

    if (status === 'authenticated' && session?.user) {
      setUser({
        id: session.user.id,
        username: session.user.username,
        email: session.user.email,
        name: session.user.name,
        active: session.user.active,
        roleId: session.user.roleId || '',
        lastLogin: session.user.lastLogin ? new Date(session.user.lastLogin) : null,
        createdAt: session.user.createdAt ? new Date(session.user.createdAt) : new Date(),
        updatedAt: session.user.updatedAt ? new Date(session.user.updatedAt) : new Date(),
        role: {
          id: session.user.roleId || '',
          name: session.user.role || 'user',
          permissions: session.user.permissions || [],
        },
      });
      setPermissions(session.user.permissions || []);
    } else {
      setUser(null);
      setPermissions([]);
    }
    
    setIsLoading(false);
  }, [session, status]);

  /**
   * Check if user has specific permission
   */
  const hasPermission = useCallback((permission: string): boolean => {
    if (!permissions.length) return false;
    return permissions.includes('*') || permissions.includes(permission);
  }, [permissions]);

  /**
   * Check current session status - using NextAuth session directly
   */
  const checkSession = useCallback(async (): Promise<void> => {
    // This is now handled by useSession hook from NextAuth
    // We'll sync the useAuth state with NextAuth session
  }, []);

  /**
   * Login with credentials using NextAuth
   */
  const login = useCallback(async (credentials: LoginCredentials): Promise<{ success: boolean; error?: string }> => {
    setIsLoading(true);

    try {
      const result = await signIn('credentials', {
        username: credentials.username,
        password: credentials.password,
        redirect: false,
      });

      if (result?.error) {
        setUser(null);
        setSessionData(null);
        setPermissions([]);
        setIsLoading(false);
        
        return { 
          success: false, 
          error: 'Invalid credentials' 
        };
      } else {
        // Check session to get user data after successful login
        await checkSession();
        
        return { success: true };
      }
    } catch (error) {
      console.error('Login error:', error);
      setUser(null);
      setSessionData(null);
      setPermissions([]);
      setIsLoading(false);
      
      return { 
        success: false, 
        error: 'Network error or server unavailable' 
      };
    }
  }, [checkSession]);

  /**
   * Logout user - Using NextAuth signOut
   */
  const logout = useCallback(async (): Promise<void> => {
    try {
      await signOut({ redirect: false });
      
      // Clear local state
      if (mounted.current) {
        setUser(null);
        setSessionData(null);
        setPermissions([]);
        setIsLoading(false);
      }
      
      // Navigate to login page
      router.push('/admin/login');
    } catch (error) {
      console.error('Logout error:', error);
      // Redirect anyway in case of error
      router.push('/admin/login');
    }
  }, [router]);

  // Check session on mount
  useEffect(() => {
    mounted.current = true;
    checkSession();

    return () => {
      mounted.current = false;
    };
  }, [checkSession]);

  // Session refresh interval (every 5 minutes)
  useEffect(() => {
    if (!user) return;

    const interval = setInterval(() => {
      if (mounted.current && !checkingSession.current) {
        checkSession();
      }
    }, 5 * 60 * 1000);

    return () => clearInterval(interval);
  }, [user, checkSession]);

  return {
    user,
    session: sessionData,
    isAuthenticated: !!user,
    isLoading,
    permissions,
    hasPermission,
    login,
    logout,
    checkSession,
  };
}

/**
 * Hook for checking specific permissions
 */
export function usePermission(permission: string) {
  const { permissions, isLoading } = useAuth();
  
  const hasPermission = permissions.includes('*') || permissions.includes(permission);
  
  return {
    hasPermission,
    isLoading,
  };
}

/**
 * Hook for admin-only access
 */
export function useAdminAuth() {
  const auth = useAuth();
  
  const isAdmin = auth.user?.role.name === 'admin' || 
                  auth.permissions.includes('*') ||
                  auth.permissions.includes('admin');
  
  return {
    ...auth,
    isAdmin,
  };
} 