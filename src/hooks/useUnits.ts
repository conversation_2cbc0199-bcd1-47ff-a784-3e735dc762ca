import { useState, useEffect } from 'react';

interface Unit {
  id: string;
  name: string;
  displayName: string;
  plural: string;
  shortForm?: string;
  category: string;
  active: boolean;
  order: number;
}

export function useUnits() {
  const [units, setUnits] = useState<Unit[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchUnits = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch('/api/admin/units');
      
      if (!response.ok) {
        throw new Error(`Failed to fetch units: ${response.statusText}`);
      }
      
      const data = await response.json();
      setUnits(data);
    } catch (err) {
      console.error('Error fetching units:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch units');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUnits();
  }, []);

  const refetch = () => {
    fetchUnits();
  };

  return {
    units,
    loading,
    error,
    refetch
  };
} 