import { useEffect, useRef, useState, useCallback } from 'react';

// Enhanced intersection observer options
export interface IntersectionObserverOptions {
  threshold?: number | number[];
  rootMargin?: string;
  triggerOnce?: boolean;
  skip?: boolean;
  initialInView?: boolean;
  delay?: number;
}

// Enhanced intersection observer hook
export function useIntersectionObserver(options: IntersectionObserverOptions = {}) {
  const {
    threshold = 0.1,
    rootMargin = '50px',
    triggerOnce = false,
    skip = false,
    initialInView = false,
    delay = 0,
  } = options;

  const elementRef = useRef<HTMLElement | null>(null);
  const [isVisible, setIsVisible] = useState(initialInView);
  const [hasTriggered, setHasTriggered] = useState(false);
  const observerRef = useRef<IntersectionObserver | null>(null);

  const setRef = useCallback((node: HTMLElement | null) => {
    if (elementRef.current && observerRef.current) {
      observerRef.current.unobserve(elementRef.current);
    }

    elementRef.current = node;

    if (node && observerRef.current) {
      observerRef.current.observe(node);
    }
  }, []);

  useEffect(() => {
    if (skip || (triggerOnce && hasTriggered)) {
      return;
    }

    const handleIntersection = (entries: IntersectionObserverEntry[]) => {
      const [entry] = entries;

      if (delay > 0) {
        setTimeout(() => {
          setIsVisible(entry.isIntersecting);
          if (entry.isIntersecting && triggerOnce) {
            setHasTriggered(true);
          }
        }, delay);
      } else {
        setIsVisible(entry.isIntersecting);
        if (entry.isIntersecting && triggerOnce) {
          setHasTriggered(true);
        }
      }
    };

    observerRef.current = new IntersectionObserver(handleIntersection, {
      threshold,
      rootMargin,
    });

    if (elementRef.current) {
      observerRef.current.observe(elementRef.current);
    }

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [threshold, rootMargin, triggerOnce, skip, hasTriggered, delay]);

  return [setRef, isVisible, hasTriggered] as const;
}

// Lazy loading hook specifically for images
export function useLazyImage(src: string, options: IntersectionObserverOptions = {}) {
  const [ref, isVisible] = useIntersectionObserver({
    triggerOnce: true,
    threshold: 0.1,
    rootMargin: '100px',
    ...options,
  });

  const [imageSrc, setImageSrc] = useState<string | undefined>();
  const [isLoaded, setIsLoaded] = useState(false);
  const [isError, setIsError] = useState(false);

  useEffect(() => {
    if (isVisible && src && !imageSrc) {
      setImageSrc(src);
    }
  }, [isVisible, src, imageSrc]);

  const handleLoad = useCallback(() => {
    setIsLoaded(true);
    setIsError(false);
  }, []);

  const handleError = useCallback(() => {
    setIsError(true);
    setIsLoaded(false);
  }, []);

  return {
    ref,
    src: imageSrc,
    isLoaded,
    isError,
    isVisible,
    handleLoad,
    handleError,
  };
}

// Hook for lazy loading content with fade-in animation
export function useLazyContent(options: IntersectionObserverOptions = {}) {
  const [ref, isVisible] = useIntersectionObserver({
    triggerOnce: true,
    threshold: 0.1,
    rootMargin: '50px',
    ...options,
  });

  const [shouldRender, setShouldRender] = useState(false);

  useEffect(() => {
    if (isVisible) {
      // Small delay for smoother animation
      const timer = setTimeout(() => setShouldRender(true), 50);
      return () => clearTimeout(timer);
    }
  }, [isVisible]);

  return {
    ref,
    isVisible,
    shouldRender,
    className: `transition-opacity duration-500 ${
      shouldRender ? 'opacity-100' : 'opacity-0'
    }`,
  };
}

// Hook for infinite scrolling
export function useInfiniteScroll(
  callback: () => void,
  options: IntersectionObserverOptions = {}
) {
  const [ref, isVisible] = useIntersectionObserver({
    threshold: 1.0,
    rootMargin: '100px',
    ...options,
  });

  useEffect(() => {
    if (isVisible) {
      callback();
    }
  }, [isVisible, callback]);

  return ref;
}

// Legacy export for backward compatibility
export default useIntersectionObserver;