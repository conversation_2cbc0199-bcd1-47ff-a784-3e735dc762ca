'use client';

import { useState, useEffect, useCallback } from 'react';
import { 
  getUserCurrency, 
  getUserLocation, 
  convertAndFormatPrice, 
  convertPrice,
  formatPrice,
  SUPPORTED_CURRENCIES,
  type LocationInfo 
} from '@/lib/currency';

interface UseCurrencyReturn {
  currency: string;
  location: LocationInfo | null;
  isLoading: boolean;
  error: string | null;
  convertAndFormat: (kesAmount: number, targetCurrency?: string) => Promise<string>;
  convert: (kesAmount: number, targetCurrency?: string) => Promise<number>;
  format: (amount: number, currencyCode?: string) => string;
  setCurrency: (currency: string) => void;
  supportedCurrencies: typeof SUPPORTED_CURRENCIES;
  refresh: () => Promise<void>;
}

export function useCurrency(): UseCurrencyReturn {
  const [currency, setCurrencyState] = useState<string>('KES');
  const [location, setLocation] = useState<LocationInfo | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Initialize currency and location
  const initializeCurrency = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Get user location and currency in parallel
      const [userLocation, userCurrency] = await Promise.all([
        getUserLocation(),
        getUserCurrency()
      ]);

      setLocation(userLocation);
      setCurrencyState(userCurrency);
    } catch (err) {
      console.error('Failed to initialize currency:', err);
      setError('Failed to detect location and currency');
      // Fallback to default values
      setLocation({
        country: 'Kenya',
        countryCode: 'KE',
        currency: 'KES',
        timezone: 'Africa/Nairobi'
      });
      setCurrencyState('KES');
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Initialize on mount
  useEffect(() => {
    initializeCurrency();
  }, [initializeCurrency]);

  // Convert and format price
  const convertAndFormat = useCallback(async (kesAmount: number, targetCurrency?: string): Promise<string> => {
    try {
      return await convertAndFormatPrice(kesAmount, targetCurrency || currency);
    } catch (err) {
      console.error('Failed to convert and format price:', err);
      return formatPrice(kesAmount, 'KES');
    }
  }, [currency]);

  // Convert price only
  const convert = useCallback(async (kesAmount: number, targetCurrency?: string): Promise<number> => {
    try {
      return await convertPrice(kesAmount, targetCurrency || currency);
    } catch (err) {
      console.error('Failed to convert price:', err);
      return kesAmount;
    }
  }, [currency]);

  // Format price only
  const format = useCallback((amount: number, currencyCode?: string): string => {
    return formatPrice(amount, currencyCode || currency);
  }, [currency]);

  // Set currency manually
  const setCurrency = useCallback((newCurrency: string) => {
    console.log(`Setting currency to: ${newCurrency}`); // Debug log
    if (SUPPORTED_CURRENCIES[newCurrency]) {
      setCurrencyState(newCurrency);
      // Store in localStorage for persistence
      if (typeof window !== 'undefined') {
        localStorage.setItem('preferred-currency', newCurrency);
      }
      console.log(`Currency set successfully to: ${newCurrency}`); // Debug log
    } else {
      console.warn(`Unsupported currency: ${newCurrency}`); // Debug log
    }
  }, []);

  // Refresh currency and location data
  const refresh = useCallback(async () => {
    await initializeCurrency();
  }, [initializeCurrency]);

  // Load preferred currency from localStorage on mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedCurrency = localStorage.getItem('preferred-currency');
      if (savedCurrency && SUPPORTED_CURRENCIES[savedCurrency]) {
        setCurrencyState(savedCurrency);
      }
    }
  }, []);

  return {
    currency,
    location,
    isLoading,
    error,
    convertAndFormat,
    convert,
    format,
    setCurrency,
    supportedCurrencies: SUPPORTED_CURRENCIES,
    refresh
  };
} 