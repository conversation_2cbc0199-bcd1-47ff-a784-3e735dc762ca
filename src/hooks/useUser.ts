import { useState, useEffect, useCallback, useRef } from 'react';
import { User, Role, ActivityLog } from '@/types/user';
import { useNotification } from '@/contexts/NotificationContext';

interface UseUserOptions {
  userId?: string;
  includeActivityLogs?: boolean;
  onError?: (error: string) => void;
}

interface UseUserReturn {
  user: User | null;
  roles: Role[];
  activityLogs: ActivityLog[];
  isLoading: boolean;
  error: string | null;
  refetchUser: () => Promise<void>;
  refetchRoles: () => Promise<void>;
  deleteUser: () => Promise<boolean>;
}

export function useUser({
  userId,
  includeActivityLogs = false,
  onError,
}: UseUserOptions = {}): UseUserReturn {
  const { showNotification } = useNotification();

  // State
  const [user, setUser] = useState<User | null>(null);
  const [roles, setRoles] = useState<Role[]>([]);
  const [activityLogs, setActivityLogs] = useState<ActivityLog[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Ref to track if data is already being fetched
  const fetchingRef = useRef(false);
  const lastFetchRef = useRef<string | null>(null);

  // Fetch user data
  const fetchUser = useCallback(async () => {
    if (!userId) return;

    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch(`/api/admin/users/${userId}`, {
        credentials: 'include',
      });

      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('User not found');
        }
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch user');
      }

      const userData = await response.json();
      setUser(userData);
    } catch (error: any) {
      const errorMessage = error.message || 'Failed to load user data';
      setError(errorMessage);
      
      if (onError) {
        onError(errorMessage);
      } else {
        showNotification('error', errorMessage);
      }
    } finally {
      setIsLoading(false);
    }
  }, [userId]);

  // Fetch roles
  const fetchRoles = useCallback(async () => {
    try {
      const response = await fetch('/api/admin/roles', {
        credentials: 'include',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch roles');
      }

      const rolesData = await response.json();
      setRoles(rolesData);
    } catch (error: any) {
      const errorMessage = error.message || 'Failed to load roles';
      console.error('Error fetching roles:', error);
      // Don't show notification for roles fetch error as it's not critical
    }
  }, []);

  // Fetch activity logs
  const fetchActivityLogs = useCallback(async () => {
    if (!userId || !includeActivityLogs) return;

    try {
      const response = await fetch(`/api/admin/activity-logs?userId=${userId}&limit=10`, {
        credentials: 'include',
      });

      if (response.ok) {
        const activityData = await response.json();
        setActivityLogs(activityData.logs || []);
      }
    } catch (error) {
      console.error('Error fetching activity logs:', error);
      // Activity logs are optional, so don't throw error
    }
  }, [userId, includeActivityLogs]);

  // Delete user
  const deleteUser = useCallback(async (): Promise<boolean> => {
    if (!userId) return false;

    try {
      setIsLoading(true);
      
      const response = await fetch(`/api/admin/users/${userId}`, {
        method: 'DELETE',
        credentials: 'include',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete user');
      }

      showNotification('success', 'User deleted successfully');
      return true;
    } catch (error: any) {
      const errorMessage = error.message || 'Failed to delete user';
      showNotification('error', errorMessage);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [userId]);

  // Initial data fetch
  useEffect(() => {
    if (!userId) return;
    
    // Prevent duplicate fetches
    const fetchKey = `${userId}-${includeActivityLogs}`;
    if (fetchingRef.current || lastFetchRef.current === fetchKey) return;
    
    fetchingRef.current = true;
    lastFetchRef.current = fetchKey;

    const fetchAllData = async () => {
      setIsLoading(true);
      
      try {
        // Fetch all data in parallel
        const promises = [
          fetch(`/api/admin/users/${userId}`, { credentials: 'include' }),
          fetch('/api/admin/roles', { credentials: 'include' }),
        ];

        if (includeActivityLogs) {
          promises.push(
            fetch(`/api/admin/activity-logs?userId=${userId}&limit=10`, { credentials: 'include' })
          );
        }

        const responses = await Promise.all(promises);
        
        // Process user data
        if (responses[0].ok) {
          const userData = await responses[0].json();
          setUser(userData);
        } else {
          throw new Error('Failed to fetch user');
        }

        // Process roles data
        if (responses[1].ok) {
          const rolesData = await responses[1].json();
          setRoles(rolesData);
        }

        // Process activity logs if included
        if (includeActivityLogs && responses[2] && responses[2].ok) {
          const activityData = await responses[2].json();
          setActivityLogs(activityData.logs || []);
        }

      } catch (error: any) {
        const errorMessage = error.message || 'Failed to load user data';
        setError(errorMessage);
        
        if (onError) {
          onError(errorMessage);
        } else {
          showNotification('error', errorMessage);
        }
      } finally {
        setIsLoading(false);
        fetchingRef.current = false;
      }
    };

    fetchAllData();
  }, [userId, includeActivityLogs]);

  return {
    user,
    roles,
    activityLogs,
    isLoading,
    error,
    refetchUser: fetchUser,
    refetchRoles: fetchRoles,
    deleteUser,
  };
} 