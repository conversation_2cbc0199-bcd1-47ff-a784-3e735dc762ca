import { useState, useEffect, useCallback } from 'react';

interface CatalogueCategory {
  name: string;
  count?: number;
}

interface UseCatalogueCategoriesReturn {
  categories: string[];
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export function useCatalogueCategories(): UseCatalogueCategoriesReturn {
  const [categories, setCategories] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchCategories = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/admin/catalogue/categories', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        cache: 'no-store'
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      
      if (result.success) {
        // Use the categories from the API (which includes database + catalogue categories + defaults)
        const fetchedCategories = result.categories || [];
        setCategories(fetchedCategories);
      } else {
        throw new Error(result.error || 'Failed to fetch categories');
      }
    } catch (err) {
      console.error('Error fetching catalogue categories:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch categories');
      
      // Minimal fallback - just the essential ones
      setCategories(['Web Development', 'Design', 'Marketing', 'Other']);
    } finally {
      setLoading(false);
    }
  }, []);

  const refetch = useCallback(async () => {
    await fetchCategories();
  }, [fetchCategories]);

  useEffect(() => {
    fetchCategories();
  }, [fetchCategories]);

  return {
    categories,
    loading,
    error,
    refetch
  };
} 