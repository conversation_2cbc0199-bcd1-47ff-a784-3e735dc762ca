import { useState, useCallback, useEffect } from 'react';
import { ImageItem } from '@/utils/getImages';

interface PortfolioCache {
  [category: string]: ImageItem[];
}

interface CacheMetadata {
  lastFetched: number;
  version: string;
}

const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
const STORAGE_KEY = 'portfolio-cache';
const METADATA_KEY = 'portfolio-cache-metadata';

export function usePortfolioCache() {
  const [cache, setCache] = useState<PortfolioCache>({});
  const [metadata, setMetadata] = useState<CacheMetadata>({
    lastFetched: 0,
    version: 'v1'
  });
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load cache from localStorage on mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        const cachedData = localStorage.getItem(STORAGE_KEY);
        const cachedMetadata = localStorage.getItem(METADATA_KEY);
        
        if (cachedData) {
          setCache(JSON.parse(cachedData));
        }
        
        if (cachedMetadata) {
          setMetadata(JSON.parse(cachedMetadata));
        }
      } catch (error) {
        console.warn('Failed to load portfolio cache from localStorage:', error);
      }
    }
  }, []);

  // Save cache to localStorage
  const saveCache = useCallback((newCache: PortfolioCache, newMetadata: CacheMetadata) => {
    if (typeof window !== 'undefined') {
      try {
        localStorage.setItem(STORAGE_KEY, JSON.stringify(newCache));
        localStorage.setItem(METADATA_KEY, JSON.stringify(newMetadata));
      } catch (error) {
        console.warn('Failed to save portfolio cache to localStorage:', error);
      }
    }
  }, []);

  // Check if cache is stale
  const isCacheStale = useCallback((category?: string) => {
    const now = Date.now();
    const isExpired = now - metadata.lastFetched > CACHE_DURATION;
    
    if (category && !cache[category]) {
      return true; // Category not in cache
    }
    
    return isExpired;
  }, [cache, metadata]);

  // Fetch portfolio data for a specific category
  const fetchCategory = useCallback(async (category: string): Promise<ImageItem[]> => {
    try {
      // Use the public API route that doesn't require auth
      const response = await fetch(`/api/portfolio/${category}`, {
        next: { revalidate: 300 }
      });
      
      if (!response.ok) {
        throw new Error(`Failed to fetch ${category}: ${response.statusText}`);
      }
      
      const data = await response.json();
      return Array.isArray(data) ? data : [];
    } catch (error) {
      console.error(`Error fetching ${category}:`, error);
      throw error;
    }
  }, []);

  // Get portfolio data for a category (with caching)
  const getCategory = useCallback(async (category: string, forceRefresh = false): Promise<ImageItem[]> => {
    // Return cached data if available and not stale
    if (!forceRefresh && !isCacheStale(category) && cache[category]) {
      return cache[category];
    }

    try {
      setError(null);
      const data = await fetchCategory(category);
      
      const newCache = { ...cache, [category]: data };
      const newMetadata = { 
        lastFetched: Date.now(), 
        version: metadata.version 
      };
      
      setCache(newCache);
      setMetadata(newMetadata);
      saveCache(newCache, newMetadata);
      
      return data;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      setError(errorMessage);
      
      // Return cached data if available, even if stale
      if (cache[category]) {
        return cache[category] as ImageItem[];
      }
      
      throw error;
    }
  }, [cache, metadata, isCacheStale, fetchCategory, saveCache]);

  // Refresh all cached categories
  const refreshAll = useCallback(async (): Promise<void> => {
    if (isRefreshing) return;
    
    setIsRefreshing(true);
    setError(null);
    
    try {
      // Call the revalidation API
      const revalidateResponse = await fetch('/api/portfolio/revalidate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ type: 'all' })
      });
      
      if (revalidateResponse.ok) {
        console.log('Portfolio cache revalidated successfully');
      }
      
      // Refresh all cached categories
      const categories = Object.keys(cache);
             const refreshPromises = categories.map(category => 
         fetchCategory(category).catch(error => {
           console.warn(`Failed to refresh ${category}:`, error);
           return (cache[category] || []) as ImageItem[]; // Fallback to cached data
         })
       );
      
      const results = await Promise.all(refreshPromises);
      
      const newCache: PortfolioCache = {};
      categories.forEach((category, index) => {
        newCache[category] = results[index];
      });
      
      const newMetadata = { 
        lastFetched: Date.now(), 
        version: metadata.version 
      };
      
      setCache(newCache);
      setMetadata(newMetadata);
      saveCache(newCache, newMetadata);
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to refresh portfolio data';
      setError(errorMessage);
      console.error('Error refreshing portfolio cache:', error);
    } finally {
      setIsRefreshing(false);
    }
  }, [isRefreshing, cache, metadata, fetchCategory, saveCache]);

  // Invalidate cache and clear storage
  const invalidateCache = useCallback(() => {
    setCache({});
    setMetadata({ lastFetched: 0, version: 'v1' });
    
    if (typeof window !== 'undefined') {
      localStorage.removeItem(STORAGE_KEY);
      localStorage.removeItem(METADATA_KEY);
    }
  }, []);

  // Add optimistic update for new uploads
  const addOptimisticItem = useCallback((category: string, item: ImageItem) => {
    setCache(prevCache => ({
      ...prevCache,
      [category]: [item, ...(prevCache[category] || [])]
    }));
  }, []);

  // Remove optimistic item (if upload failed)
  const removeOptimisticItem = useCallback((category: string, itemId: string | number) => {
    setCache(prevCache => ({
      ...prevCache,
      [category]: (prevCache[category] || []).filter(item => item.id !== itemId)
    }));
  }, []);

  return {
    // Data
    cache,
    metadata,
    
    // State
    isRefreshing,
    error,
    
    // Methods
    getCategory,
    refreshAll,
    invalidateCache,
    isCacheStale,
    addOptimisticItem,
    removeOptimisticItem,
    
    // Cache info
    lastFetched: new Date(metadata.lastFetched),
    cacheAge: Date.now() - metadata.lastFetched,
    isStale: isCacheStale()
  };
} 