'use client';

import { useState, useCallback, useRef, useEffect } from 'react';
import { 
  ClientRateLimiter, 
  debounce, 
  throttle,
  adminApiLimiter,
  uploadLimiter,
  searchLimiter
} from '@/utils/rateLimiting';

interface UseRateLimitOptions {
  windowMs?: number;
  maxRequests?: number;
  onLimitReached?: (retryAfter: number) => void;
  autoRetry?: boolean;
}

interface UseRateLimitReturn {
  execute: <T>(key: string, requestFn: () => Promise<T>) => Promise<T>;
  isAllowed: (key: string) => boolean;
  getRemainingRequests: (key: string) => number;
  getRetryAfter: (key: string) => number;
  clear: (key?: string) => void;
  getCurrentCount: (key: string) => number;
}

/**
 * Custom hook for client-side rate limiting
 */
export function useRateLimit(options: UseRateLimitOptions = {}): UseRateLimitReturn {
  const {
    windowMs = 60000, // 1 minute
    maxRequests = 30,
    onLimitReached,
    autoRetry = false
  } = options;

  const limiterRef = useRef(new ClientRateLimiter(windowMs, maxRequests));
  
  const execute = useCallback(async <T>(
    key: string, 
    requestFn: () => Promise<T>
  ): Promise<T> => {
    try {
      return await limiterRef.current.execute(key, requestFn, {
        throwOnLimit: !autoRetry
      });
    } catch (error) {
      if (error instanceof Error && error.message.includes('Rate limit exceeded')) {
        const retryAfter = limiterRef.current.getRetryAfter(key);
        onLimitReached?.(retryAfter);
      }
      throw error;
    }
  }, [autoRetry, onLimitReached]);

  const isAllowed = useCallback((key: string) => {
    // Note: This will consume a request slot, use carefully
    const currentCount = limiterRef.current.getCurrentCount(key);
    const remainingRequests = limiterRef.current.getRemainingRequests(key);
    return remainingRequests > 0;
  }, []);
  
  const getRemainingRequests = useCallback((key: string) => 
    limiterRef.current.getRemainingRequests(key), []);
  
  const getRetryAfter = useCallback((key: string) => 
    limiterRef.current.getRetryAfter(key), []);
  
  const clear = useCallback((key?: string) => 
    limiterRef.current.clear(key), []);
  
  const getCurrentCount = useCallback((key: string) => 
    limiterRef.current.getCurrentCount(key), []);

  return {
    execute,
    isAllowed,
    getRemainingRequests,
    getRetryAfter,
    clear,
    getCurrentCount
  };
}

/**
 * Hook for API requests with built-in rate limiting
 */
export function useRateLimitedApi() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const executeRequest = useCallback(async <T>(
    url: string,
    options: RequestInit = {},
    rateLimiter: ClientRateLimiter = adminApiLimiter
  ): Promise<T> => {
    const key = `api:${url}:${options.method || 'GET'}`;
    setLoading(true);
    setError(null);

    try {
      const response = await rateLimiter.execute(
        key,
        () => fetch(url, {
          headers: {
            'Content-Type': 'application/json',
            ...options.headers,
          },
          ...options,
        }),
        {
          deduplicate: options.method === 'GET',
          throwOnLimit: false
        }
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}`);
      }

      const data = await response.json();
      return data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Request failed';
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    executeRequest,
    loading,
    error,
    clearError: () => setError(null)
  };
}

/**
 * Hook for search functionality with debouncing and rate limiting
 */
export function useRateLimitedSearch<T>(
  searchFn: (query: string) => Promise<T[]>,
  debounceMs: number = 300
) {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<T[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const debouncedSearch = useCallback(
    debounce(async (searchQuery: string) => {
      if (!searchQuery.trim()) {
        setResults([]);
        return;
      }

      const key = `search:${searchQuery}`;
      setLoading(true);
      setError(null);

      try {
        const searchResults = await searchLimiter.execute(
          key,
          () => searchFn(searchQuery),
          { throwOnLimit: false }
        );
        setResults(searchResults);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Search failed';
        setError(errorMessage);
        setResults([]);
      } finally {
        setLoading(false);
      }
    }, debounceMs),
    [searchFn, debounceMs]
  );

  const handleSearch = useCallback((newQuery: string) => {
    setQuery(newQuery);
    debouncedSearch(newQuery);
  }, [debouncedSearch]);

  const clearSearch = useCallback(() => {
    setQuery('');
    setResults([]);
    setError(null);
  }, []);

  return {
    query,
    results,
    loading,
    error,
    handleSearch,
    clearSearch,
    setQuery: handleSearch
  };
}

/**
 * Hook for file uploads with rate limiting and progress tracking
 */
export function useRateLimitedUpload() {
  const [uploading, setUploading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);

  const uploadFile = useCallback(async (
    url: string,
    file: File,
    options: {
      onProgress?: (progress: number) => void;
      additionalData?: Record<string, any>;
    } = {}
  ) => {
    const key = `upload:${file.name}:${file.size}`;
    setUploading(true);
    setProgress(0);
    setError(null);

    try {
      return await uploadLimiter.execute(
        key,
        () => new Promise<any>((resolve, reject) => {
          const formData = new FormData();
          formData.append('file', file);
          
          if (options.additionalData) {
            Object.entries(options.additionalData).forEach(([key, value]) => {
              formData.append(key, value);
            });
          }

          const xhr = new XMLHttpRequest();

          // Track upload progress
          xhr.upload.addEventListener('progress', (event) => {
            if (event.lengthComputable) {
              const progressPercent = Math.round((event.loaded / event.total) * 100);
              setProgress(progressPercent);
              options.onProgress?.(progressPercent);
            }
          });

          xhr.addEventListener('load', () => {
            if (xhr.status >= 200 && xhr.status < 300) {
              try {
                const response = JSON.parse(xhr.responseText);
                resolve(response);
              } catch {
                resolve(xhr.responseText);
              }
            } else {
              reject(new Error(`Upload failed: ${xhr.status}`));
            }
          });

          xhr.addEventListener('error', () => {
            reject(new Error('Upload failed: Network error'));
          });

          xhr.open('POST', url);
          xhr.send(formData);
        }),
        { throwOnLimit: false }
      );
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Upload failed';
      setError(errorMessage);
      throw err;
    } finally {
      setUploading(false);
      setProgress(0);
    }
  }, []);

  return {
    uploadFile,
    uploading,
    progress,
    error,
    clearError: () => setError(null)
  };
}

/**
 * Hook for throttled functions (e.g., resize handlers, scroll events)
 */
export function useThrottle<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): T {
  const throttledFn = useRef<T>();

  useEffect(() => {
    throttledFn.current = throttle(func, delay) as T;
  }, [func, delay]);

  return throttledFn.current || func;
}

/**
 * Hook for debounced functions (e.g., search inputs, auto-save)
 */
export function useDebounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): T {
  const debouncedFn = useRef<T>();

  useEffect(() => {
    debouncedFn.current = debounce(func, delay) as T;
  }, [func, delay]);

  return debouncedFn.current || func;
}

/**
 * Hook for rate limit status monitoring
 */
export function useRateLimitStatus(
  rateLimiter: ClientRateLimiter,
  key: string
) {
  const [status, setStatus] = useState({
    currentCount: 0,
    remainingRequests: 0,
    retryAfter: 0,
    isAllowed: true
  });

  const updateStatus = useCallback(() => {
    const currentCount = rateLimiter.getCurrentCount(key);
    const remainingRequests = rateLimiter.getRemainingRequests(key);
    const retryAfter = rateLimiter.getRetryAfter(key);
    
    setStatus({
      currentCount,
      remainingRequests,
      retryAfter,
      isAllowed: remainingRequests > 0
    });
  }, [rateLimiter, key]);

  useEffect(() => {
    updateStatus();
    const interval = setInterval(updateStatus, 1000); // Update every second
    return () => clearInterval(interval);
  }, [updateStatus]);

  return status;
} 