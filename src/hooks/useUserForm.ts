import { useState, useCallback, useMemo } from 'react';
import { UserFormData, UserFormSchema, User } from '@/types/user';
import { useNotification } from '@/contexts/NotificationContext';

interface UseUserFormOptions {
  initialData?: Partial<UserFormData>;
  user?: User;
  onSuccess?: (user: User) => void;
  onError?: (error: string) => void;
}

interface ValidationErrors {
  [key: string]: string;
}

export function useUserForm({
  initialData = {},
  user,
  onSuccess,
  onError,
}: UseUserFormOptions = {}) {
  const { showNotification } = useNotification();

  // Form data state
  const [formData, setFormData] = useState<UserFormData>({
    username: initialData.username || user?.username || '',
    email: initialData.email || user?.email || '',
    name: initialData.name || user?.name || '',
    password: initialData.password || '',
    confirmPassword: initialData.confirmPassword || '',
    roleId: initialData.roleId || user?.roleId || '',
    active: initialData.active ?? user?.active ?? true,
  });

  // Validation errors state
  const [errors, setErrors] = useState<ValidationErrors>({});
  
  // Loading state
  const [isLoading, setIsLoading] = useState(false);

  // Track if form has unsaved changes
  const hasChanges = useMemo(() => {
    if (!user) return Object.values(formData).some(value => value !== '');
    
    return (
      formData.username !== user.username ||
      formData.email !== user.email ||
      formData.name !== (user.name || '') ||
      formData.roleId !== user.roleId ||
      formData.active !== user.active ||
      formData.password !== ''
    );
  }, [formData, user]);

  // Update form field
  const updateField = useCallback((field: keyof UserFormData, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value,
    }));

    // Clear error for this field when user starts typing
    if (errors[field as string]) {
      setErrors(prev => ({
        ...prev,
        [field as string]: '',
      }));
    }
  }, [errors]);

  // Validate form
  const validateForm = useCallback(() => {
    try {
      const validationData = {
        ...formData,
        // Only validate password if it's provided (for edit mode)
        ...(formData.password ? { password: formData.password, confirmPassword: formData.confirmPassword } : {}),
      };

      UserFormSchema.parse(validationData);
      setErrors({});
      return true;
    } catch (error: any) {
      const newErrors: ValidationErrors = {};
      
      if (error.errors) {
        error.errors.forEach((err: any) => {
          newErrors[err.path[0]] = err.message;
        });
      }
      
      setErrors(newErrors);
      return false;
    }
  }, [formData]);

  // Reset form
  const resetForm = useCallback(() => {
    setFormData({
      username: user?.username || '',
      email: user?.email || '',
      name: user?.name || '',
      password: '',
      confirmPassword: '',
      roleId: user?.roleId || '',
      active: user?.active ?? true,
    });
    setErrors({});
  }, [user]);

  // Submit form (create or update)
  const submitForm = useCallback(async (endpoint: string, method: 'POST' | 'PUT' = 'PUT') => {
    if (!validateForm()) {
      showNotification('error', 'Please fix validation errors before submitting');
      return false;
    }

    setIsLoading(true);

    try {
      // Prepare data for submission
      const submitData: any = {
        username: formData.username,
        email: formData.email,
        name: formData.name || null,
        roleId: formData.roleId,
        active: formData.active,
      };

      // Only include password if it's provided
      if (formData.password) {
        submitData.password = formData.password;
      }

      const response = await fetch(endpoint, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(submitData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to ${method === 'POST' ? 'create' : 'update'} user`);
      }

      const result = await response.json();
      
      showNotification('success', `User ${method === 'POST' ? 'created' : 'updated'} successfully`);
      
      // Clear password fields after successful submission
      setFormData(prev => ({
        ...prev,
        password: '',
        confirmPassword: '',
      }));

      if (onSuccess) {
        onSuccess(result);
      }

      return true;
    } catch (error: any) {
      const errorMessage = error.message || `Failed to ${method === 'POST' ? 'create' : 'update'} user`;
      showNotification('error', errorMessage);
      
      if (onError) {
        onError(errorMessage);
      }
      
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [formData, validateForm, showNotification, onSuccess, onError]);

  return {
    formData,
    errors,
    isLoading,
    hasChanges,
    updateField,
    validateForm,
    resetForm,
    submitForm,
    setFormData,
    setErrors,
  };
} 