import { sendEmail } from './email';

// Set up environment variables for testing
process.env.SMTP_HOST = "smtp.gmail.com";
process.env.SMTP_PORT = "587";
process.env.SMTP_SECURE = "false";
process.env.SMTP_USER = "<EMAIL>";
process.env.SMTP_PASS = "sncr fuit apix bitk";
process.env.SMTP_FROM = "Web Scraper <<EMAIL>>";

async function testEmailSending() {
  console.log('Testing email functionality...');
  console.log('Using SMTP configuration:');
  console.log('- Host:', process.env.SMTP_HOST);
  console.log('- Port:', process.env.SMTP_PORT);
  console.log('- Secure:', process.env.SMTP_SECURE);
  console.log('- From:', process.env.SMTP_FROM);
  
  try {
    const result = await sendEmail({
      subject: 'Test Email from Mocky Digital',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #2563eb;">Test Email</h2>
          <p style="font-size: 16px; color: #374151;">
            This is a test email to verify the email configuration is working correctly.
          </p>
          <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <p><strong>Time Sent:</strong> ${new Date().toLocaleString()}</p>
            <p><strong>Environment:</strong> ${process.env.NODE_ENV || 'test'}</p>
          </div>
        </div>
      `
    });

    if (result.success) {
      console.log('✅ Email sent successfully!');
      console.log('Message ID:', result.messageId);
    } else {
      console.error('❌ Failed to send email:', result.error);
    }
  } catch (error) {
    console.error('❌ Error during email test:', error);
  }
}

// Run the test
testEmailSending(); 