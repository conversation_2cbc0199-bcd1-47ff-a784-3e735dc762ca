import { CalendarEvent } from '@/types/calendar';

/**
 * Format date for display
 */
export function formatDate(dateString: string): string {
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric'
    });
  } catch {
    return 'Invalid Date';
  }
}

/**
 * Format time for display
 */
export function formatTime(dateString: string): string {
  try {
    const date = new Date(dateString);
    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  } catch {
    return 'Invalid Time';
  }
}

/**
 * Format date and time for display
 */
export function formatDateTime(dateString: string): string {
  try {
    const date = new Date(dateString);
    return date.toLocaleString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });
  } catch {
    return 'Invalid DateTime';
  }
}

/**
 * Format date and time for HTML input fields
 */
export function formatDateTimeLocal(date: Date): string {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  return `${year}-${month}-${day}T${hours}:${minutes}`;
}

/**
 * Calculate event duration in minutes
 */
export function getEventDuration(startTime: string, endTime: string): number {
  try {
    const start = new Date(startTime);
    const end = new Date(endTime);
    return Math.round((end.getTime() - start.getTime()) / (1000 * 60));
  } catch {
    return 0;
  }
}

/**
 * Get client display name
 */
export function getClientName(client: CalendarEvent['client']): string {
  if (!client) return 'N/A';
  const fullName = `${client.firstName} ${client.lastName}`.trim();
  return client.company ? `${fullName} (${client.company})` : fullName;
}

/**
 * Get start date for calendar view
 */
export function getViewStartDate(currentDate: Date, viewMode: 'month' | 'week' | 'day'): Date {
  const start = new Date(currentDate);
  
  switch (viewMode) {
    case 'month':
      start.setDate(1);
      start.setDate(start.getDate() - start.getDay());
      break;
    case 'week':
      start.setDate(start.getDate() - start.getDay());
      break;
    case 'day':
      // Already the correct date
      break;
  }
  
  start.setHours(0, 0, 0, 0);
  return start;
}

/**
 * Get end date for calendar view
 */
export function getViewEndDate(currentDate: Date, viewMode: 'month' | 'week' | 'day'): Date {
  const end = new Date(currentDate);
  
  switch (viewMode) {
    case 'month':
      end.setMonth(end.getMonth() + 1, 0); // Last day of month
      end.setDate(end.getDate() + (6 - end.getDay())); // End of week
      break;
    case 'week':
      end.setDate(end.getDate() + (6 - end.getDay()));
      break;
    case 'day':
      // Same day
      break;
  }
  
  end.setHours(23, 59, 59, 999);
  return end;
}

/**
 * Get calendar dates for month view (6 weeks)
 */
export function getCalendarDates(currentDate: Date): Date[] {
  const dates: Date[] = [];
  const startDate = getViewStartDate(currentDate, 'month');
  
  for (let i = 0; i < 42; i++) { // 6 weeks * 7 days
    const date = new Date(startDate);
    date.setDate(startDate.getDate() + i);
    dates.push(date);
  }
  
  return dates;
}

/**
 * Get events for a specific date
 */
export function getEventsForDate(events: CalendarEvent[], date: Date): CalendarEvent[] {
  return events.filter(event => {
    const eventDate = new Date(event.startTime);
    return eventDate.toDateString() === date.toDateString();
  });
}

/**
 * Get week dates (7 days starting from Sunday)
 */
export function getWeekDates(currentDate: Date): Date[] {
  const dates: Date[] = [];
  const startDate = getViewStartDate(currentDate, 'week');
  
  for (let i = 0; i < 7; i++) {
    const date = new Date(startDate);
    date.setDate(startDate.getDate() + i);
    dates.push(date);
  }
  
  return dates;
}

/**
 * Get hour slots for day/week view
 */
export function getHourSlots(): Array<{ hour: number; label: string }> {
  const slots = [];
  for (let hour = 0; hour < 24; hour++) {
    const is12Hour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
    const period = hour < 12 ? 'AM' : 'PM';
    slots.push({
      hour,
      label: `${is12Hour}:00 ${period}`
    });
  }
  return slots;
}

/**
 * Navigate to next/previous period
 */
export function navigateDate(currentDate: Date, direction: 'prev' | 'next', viewMode: 'month' | 'week' | 'day'): Date {
  const newDate = new Date(currentDate);
  
  switch (viewMode) {
    case 'month':
      newDate.setMonth(newDate.getMonth() + (direction === 'next' ? 1 : -1));
      break;
    case 'week':
      newDate.setDate(newDate.getDate() + (direction === 'next' ? 7 : -7));
      break;
    case 'day':
      newDate.setDate(newDate.getDate() + (direction === 'next' ? 1 : -1));
      break;
  }
  
  return newDate;
}

/**
 * Get view title based on current date and view mode
 */
export function getViewTitle(currentDate: Date, viewMode: 'month' | 'week' | 'day'): string {
  switch (viewMode) {
    case 'month':
      return currentDate.toLocaleDateString('en-US', {
        month: 'long',
        year: 'numeric'
      });
    case 'week': {
      const weekStart = getViewStartDate(currentDate, 'week');
      const weekEnd = getViewEndDate(currentDate, 'week');
      return `${weekStart.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} - ${weekEnd.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })}`;
    }
    case 'day':
      return currentDate.toLocaleDateString('en-US', {
        weekday: 'long',
        month: 'long',
        day: 'numeric',
        year: 'numeric'
      });
    default:
      return '';
  }
}

/**
 * Check if a date is today
 */
export function isToday(date: Date): boolean {
  const today = new Date();
  return date.toDateString() === today.toDateString();
}

/**
 * Check if a date is in the current month
 */
export function isCurrentMonth(date: Date, currentDate: Date): boolean {
  return date.getMonth() === currentDate.getMonth() && date.getFullYear() === currentDate.getFullYear();
}

/**
 * Copy text to clipboard
 */
export async function copyToClipboard(text: string): Promise<boolean> {
  try {
    await navigator.clipboard.writeText(text);
    return true;
  } catch (error) {
    console.error('Failed to copy to clipboard:', error);
    // Fallback for older browsers
    try {
      const textArea = document.createElement('textarea');
      textArea.value = text;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      return true;
    } catch (fallbackError) {
      console.error('Fallback copy failed:', fallbackError);
      return false;
    }
  }
}

/**
 * Filter events based on search term, type, and status
 */
export function filterEvents(
  events: CalendarEvent[],
  searchTerm: string,
  typeFilter: string,
  statusFilter: string
): CalendarEvent[] {
  return events.filter(event => {
    const matchesSearch = !searchTerm || 
      event.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      event.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      event.location?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesType = !typeFilter || event.type === typeFilter;
    const matchesStatus = !statusFilter || event.status === statusFilter;
    
    return matchesSearch && matchesType && matchesStatus;
  });
} 