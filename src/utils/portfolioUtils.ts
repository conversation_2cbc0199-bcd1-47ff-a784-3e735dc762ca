/**
 * Utility functions for portfolio management
 */

/**
 * Extract S3 key from various image URL formats
 * @param imageUrl The image URL to extract the key from
 * @param bucketName The S3 bucket name
 * @returns The extracted S3 key or null if not found
 */
export function extractS3KeyFromUrl(imageUrl: string, bucketName?: string): string | null {
  if (!imageUrl) return null;

  try {
    // Handle different URL formats
    
    // 1. Direct S3 key format (images/portfolio/...)
    if (imageUrl.startsWith('images/')) {
      return imageUrl;
    }

    // 2. Full Linode Object Storage URL
    if (imageUrl.includes('linodeobjects.com')) {
      const url = new URL(imageUrl);
      const pathParts = url.pathname.split('/').filter(part => part.length > 0);
      
      // Find bucket name in path and extract everything after it
      if (bucketName) {
        const bucketIndex = pathParts.findIndex(part => part === bucketName);
        if (bucketIndex !== -1 && bucketIndex < pathParts.length - 1) {
          return pathParts.slice(bucketIndex + 1).join('/');
        }
      }
      
      // Fallback: assume first part is bucket name
      if (pathParts.length > 1) {
        return pathParts.slice(1).join('/');
      }
    }

    // 3. URL with /images/ path
    if (imageUrl.includes('/images/')) {
      const imagePath = imageUrl.substring(imageUrl.indexOf('/images/') + 1);
      return imagePath;
    }

    // 4. Try to parse as URL and extract pathname
    try {
      const url = new URL(imageUrl);
      const pathname = url.pathname;
      
      // Remove leading slash and check if it starts with images/
      const cleanPath = pathname.startsWith('/') ? pathname.substring(1) : pathname;
      if (cleanPath.startsWith('images/')) {
        return cleanPath;
      }
    } catch (urlError) {
      // Not a valid URL, continue with other methods
    }

    // 5. Last resort: check if the URL itself looks like a key
    if (imageUrl.includes('portfolio/') || imageUrl.includes('logos/') || imageUrl.includes('branding/')) {
      return imageUrl;
    }

    console.warn(`Could not extract S3 key from URL: ${imageUrl}`);
    return null;
  } catch (error) {
    console.error('Error extracting S3 key from URL:', error);
    return null;
  }
}

/**
 * Validate regular portfolio item data
 * @param item The portfolio item to validate
 * @returns Object with validation result and errors
 */
export function validatePortfolioItem(item: any): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!item) {
    errors.push('Portfolio item is required');
    return { isValid: false, errors };
  }

  if (!item.id || typeof item.id !== 'string') {
    errors.push('Valid ID is required');
  }

  if (!item.title || typeof item.title !== 'string' || item.title.trim().length === 0) {
    errors.push('Title is required');
  }

  if (!item.category || typeof item.category !== 'string') {
    errors.push('Category is required');
  }

  if (!item.imageSrc || typeof item.imageSrc !== 'string') {
    errors.push('Image source is required');
  }

  if (!item.createdAt) {
    errors.push('Created date is required');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Validate website portfolio item data
 * @param item The website portfolio item to validate
 * @returns Object with validation result and errors
 */
export function validateWebsitePortfolioItem(item: any): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!item) {
    errors.push('Portfolio item is required');
    return { isValid: false, errors };
  }

  if (!item.id || typeof item.id !== 'string') {
    errors.push('Valid ID is required');
  }

  if (!item.title || typeof item.title !== 'string' || item.title.trim().length === 0) {
    errors.push('Title is required');
  }

  if (!item.category || typeof item.category !== 'string') {
    errors.push('Category is required');
  }

  if (!item.imageKey || typeof item.imageKey !== 'string') {
    errors.push('Image key is required');
  }

  if (!item.url || typeof item.url !== 'string' || item.url.trim().length === 0) {
    errors.push('Website URL is required');
  }

  // Validate URL format
  if (item.url) {
    try {
      new URL(item.url);
    } catch (urlError) {
      errors.push('Invalid URL format');
    }
  }

  if (!item.createdAt) {
    errors.push('Created date is required');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
}

/**
 * Sanitize regular portfolio item data
 * @param item The portfolio item to sanitize
 * @returns Sanitized portfolio item
 */
export function sanitizePortfolioItem(item: any): any {
  return {
    id: String(item.id || '').trim(),
    title: String(item.title || '').trim(),
    description: String(item.description || '').trim(),
    category: String(item.category || '').toLowerCase().trim(),
    imageSrc: String(item.imageSrc || '').trim(),
    alt: String(item.alt || item.title || '').trim(),
    featured: Boolean(item.featured),
    createdAt: item.createdAt || new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };
}

/**
 * Sanitize website portfolio item data
 * @param item The website portfolio item to sanitize
 * @returns Sanitized website portfolio item
 */
export function sanitizeWebsitePortfolioItem(item: any): any {
  return {
    id: String(item.id || '').trim(),
    title: String(item.title || '').trim(),
    description: String(item.description || '').trim(),
    category: String(item.category || '').toLowerCase().trim(),
    imageKey: String(item.imageKey || '').trim(),
    url: String(item.url || '').trim(),
    featured: Boolean(item.featured),
    createdAt: item.createdAt || new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };
}

/**
 * Generate a safe filename for portfolio images
 * @param originalName The original filename
 * @param category The portfolio category
 * @returns A safe filename
 */
export function generateSafeFilename(originalName: string, category: string): string {
  // Extract file extension
  const ext = originalName.split('.').pop()?.toLowerCase() || 'jpg';
  
  // Create a safe base name
  const baseName = originalName
    .replace(/\.[^/.]+$/, '') // Remove extension
    .toLowerCase()
    .replace(/[^a-z0-9]/g, '-') // Replace non-alphanumeric with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .replace(/^-|-$/g, ''); // Remove leading/trailing hyphens
  
  // Add timestamp for uniqueness
  const timestamp = Date.now();
  
  // Combine with category
  const safeCategory = category.toLowerCase().replace(/[^a-z0-9]/g, '');
  
  return `${safeCategory}-${baseName}-${timestamp}.${ext}`;
}

/**
 * Check if an image URL is accessible
 * @param imageUrl The image URL to check
 * @returns Promise that resolves to true if accessible, false otherwise
 */
export async function isImageAccessible(imageUrl: string): Promise<boolean> {
  try {
    const response = await fetch(imageUrl, { method: 'HEAD' });
    return response.ok;
  } catch (error) {
    console.error('Error checking image accessibility:', error);
    return false;
  }
}

/**
 * Format file size in human readable format
 * @param bytes The file size in bytes
 * @returns Formatted file size string
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * Get website category display name
 * @param category The category value
 * @returns Display name for the category
 */
export function getCategoryDisplayName(category: string): string {
  const categoryMap: Record<string, string> = {
    'e-commerce': 'E-commerce',
    'corporate': 'Corporate',
    'nonprofit': 'Non-profit',
    'portfolio': 'Portfolio',
    'blog': 'Blog',
    'landing-page': 'Landing Page',
    'other': 'Other'
  };

  return categoryMap[category] || category.charAt(0).toUpperCase() + category.slice(1);
}

/**
 * Filter portfolio items by category
 * @param items Array of portfolio items
 * @param category Category to filter by (or 'all' for no filter)
 * @returns Filtered portfolio items
 */
export function filterByCategory(items: any[], category: string): any[] {
  if (!category || category === 'all') {
    return items;
  }
  
  return items.filter(item => item.category === category);
}

/**
 * Sort portfolio items
 * @param items Array of portfolio items
 * @param sortBy Field to sort by
 * @param sortOrder Sort order (asc or desc)
 * @returns Sorted portfolio items
 */
export function sortPortfolioItems(
  items: any[],
  sortBy: 'title' | 'category' | 'createdAt' | 'featured' = 'createdAt',
  sortOrder: 'asc' | 'desc' = 'desc'
): any[] {
  return [...items].sort((a, b) => {
    let valueA: any = a[sortBy];
    let valueB: any = b[sortBy];

    // Handle different data types
    if (sortBy === 'createdAt') {
      valueA = new Date(valueA).getTime();
      valueB = new Date(valueB).getTime();
    } else if (typeof valueA === 'string') {
      valueA = valueA.toLowerCase();
      valueB = valueB.toLowerCase();
    }

    // Handle featured items (always show featured first if sorting by featured)
    if (sortBy === 'featured') {
      if (valueA === valueB) {
        // If both have same featured status, sort by creation date
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      }
      return valueB ? 1 : -1; // Featured items first
    }

    // Normal sorting
    if (valueA < valueB) {
      return sortOrder === 'asc' ? -1 : 1;
    }
    if (valueA > valueB) {
      return sortOrder === 'asc' ? 1 : -1;
    }
    return 0;
  });
}
