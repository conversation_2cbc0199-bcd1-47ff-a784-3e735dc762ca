interface OrderFormData {
  // Product details
  productId: string;
  productName: string;
  
  // Configuration
  selectedPageType: string;
  selectedPrintingOption: string;
  quantity: number;
  customQuantity: string;
  useCustomQuantity: boolean;
  meters: number;
  orderType: 'design' | 'print' | 'both'; // User's choice toggle
  
  // Artwork/Design
  needsDesign: boolean;
  designBrief: string;
  designOnly: boolean;
  referenceFiles: any[];
  selectedDesignService: any;
  artworkFiles: any[];
  uploadedArtwork: any[];
  

  
  // Customer info (optional - may be pre-filled or empty)
  customerInfo: {
    name: string;
    phone: string;
    email: string;
    notes: string;
  };
}

const ORDER_FORM_STORAGE_KEY = 'mocky_order_form_data';

export const saveOrderFormData = (data: Partial<OrderFormData>) => {
  try {
    const existingData = getOrderFormData();
    const mergedData = { ...existingData, ...data };
    localStorage.setItem(ORDER_FORM_STORAGE_KEY, JSON.stringify(mergedData));
    console.log('Order form data saved:', mergedData);
  } catch (error) {
    console.error('Failed to save order form data:', error);
  }
};

export const getOrderFormData = (): Partial<OrderFormData> => {
  try {
    const data = localStorage.getItem(ORDER_FORM_STORAGE_KEY);
    return data ? JSON.parse(data) : {};
  } catch (error) {
    console.error('Failed to get order form data:', error);
    return {};
  }
};

export const clearOrderFormData = () => {
  try {
    localStorage.removeItem(ORDER_FORM_STORAGE_KEY);
    console.log('Order form data cleared');
  } catch (error) {
    console.error('Failed to clear order form data:', error);
  }
};

export const getMissingRequiredFields = (data: Partial<OrderFormData>): string[] => {
  const missing: string[] = [];
  
  // Always required: customer information
  if (!data.customerInfo?.name) missing.push('Full Name');
  if (!data.customerInfo?.phone) missing.push('Phone Number');
  if (!data.customerInfo?.email) missing.push('Email Address');
  
  // Design brief required if user selected design service
  if (data.needsDesign && !data.designBrief?.trim()) {
    missing.push('Design Brief');
  }
  
  // Artwork/reference files logic:
  // - If design-only order: reference files are optional (already handled in main form)
  // - If design + print: design brief is required (handled above)
  // - If print-only: artwork files are required
  const hasDesignService = data.needsDesign || data.designOnly;
  const hasArtworkFiles = (data.artworkFiles && data.artworkFiles.length > 0) || (data.uploadedArtwork && data.uploadedArtwork.length > 0);
  const hasReferenceFiles = data.referenceFiles && data.referenceFiles.length > 0;
  
  // Only require artwork files if:
  // - User is NOT using design service
  // - User is NOT doing design-only order
  // - User hasn't uploaded artwork files yet (either artworkFiles or uploadedArtwork)
  if (!hasDesignService && !hasArtworkFiles && !hasReferenceFiles) {
    missing.push('Artwork Files');
  }
  
  return missing;
};

export const generateWhatsAppMessage = (orderData: any, artworkUrls: string[] = []): string => {
  const artworkLinks = artworkUrls.length > 0
    ? `\n📎 *ARTWORK FILES:*\n${artworkUrls.map((url, index) => `${index + 1}. ${url}`).join('\n')}\n`
    : '';

  return `🛒 *NEW ORDER REQUEST* - ${orderData.orderNumber}

📦 Product: ${orderData.productName}
📊 Quantity: ${orderData.quantity.toLocaleString()}${orderData.useCustomQuantity ? ' (Custom)' : ''}
${orderData.paperType ? `📄 Paper: ${orderData.paperType}` : ''}
${orderData.printingSide ? `🖨️ Printing: ${orderData.printingSide}` : ''}
${orderData.meters && orderData.meters > 0 ? `📏 Length: ${orderData.meters}m` : ''}

${orderData.designOnly ? `🎨 DESIGN-ONLY SERVICE
💡 Service: ${orderData.designServiceName || 'Design Service'}
💡 Brief: ${orderData.designBrief}
💰 Design Fee: KSh ${orderData.designFee.toLocaleString()}
📦 Delivery: Digital files only${orderData.referenceFiles?.length > 0 ? `
📎 Reference Files: ${orderData.referenceFiles.length} file(s) uploaded` : ''}` : 
  orderData.needsDesign ? `🎨 DESIGN SERVICE + PRINTING
💡 Service: ${orderData.designServiceName || 'Design Service'}
💡 Brief: ${orderData.designBrief}  
💰 Design Fee: KSh ${orderData.designFee.toLocaleString()}` : 
  `📁 Artwork: ${orderData.artworkFiles?.length || 0} file(s) uploaded${artworkLinks}`}

👤 CUSTOMER INFO:
Name: ${orderData.customerName}
📧 Email: ${orderData.email}
📱 Phone: ${orderData.phone}
${orderData.notes ? `📝 Notes: ${orderData.notes}` : ''}

💰 PRICING BREAKDOWN:
${orderData.designOnly ? '' : `Print Cost: KSh ${(orderData.subtotal).toLocaleString()}`}
${(orderData.needsDesign || orderData.designOnly) ? `Design Fee: KSh ${orderData.designFee.toLocaleString()}
` : ''}Total: KSh ${orderData.totalAmount.toLocaleString()}

Order ID: ${orderData.orderNumber}

I will make the deposit to your M-Pesa paybill to confirm this order.`;
};

export const isOrderFormComplete = (data: Partial<OrderFormData>): boolean => {
  return getMissingRequiredFields(data).length === 0;
}; 