// Enhanced image upload utilities with comprehensive error handling

export interface UploadResult {
  success: boolean;
  url?: string;
  key?: string;
  filename?: string;
  error?: string;
  warning?: string;
  fallbackUrl?: string;
}

export interface UploadOptions {
  category?: string;
  maxRetries?: number;
  timeoutMs?: number;
  isOptional?: boolean;
  onProgress?: (progress: number) => void;
  onStatusUpdate?: (message: string, type: 'info' | 'warning' | 'error') => void;
}

export class ImageUploadError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode: number = 500,
    public isRetryable: boolean = false
  ) {
    super(message);
    this.name = 'ImageUploadError';
  }
}

export class ImageUploadService {
  private static readonly MAX_FILE_SIZE = 100 * 1024 * 1024; // 100MB (increased from 50MB)
  private static readonly VALID_TYPES = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/avif'];
  private static readonly FALLBACK_URL = '/images/placeholder.jpg';

  /**
   * Validate file before upload
   */
  static validateFile(file: File): { isValid: boolean; error?: string } {
    if (!file) {
      return { isValid: false, error: 'No file provided' };
    }

    if (!this.VALID_TYPES.includes(file.type)) {
      return { 
        isValid: false, 
        error: `Invalid file type: ${file.type}. Allowed types: ${this.VALID_TYPES.join(', ')}` 
      };
    }

    if (file.size > this.MAX_FILE_SIZE) {
      return { 
        isValid: false, 
        error: `File too large: ${Math.round(file.size / (1024 * 1024))}MB. Maximum size: ${this.MAX_FILE_SIZE / (1024 * 1024)}MB` 
      };
    }

    return { isValid: true };
  }

  /**
   * Upload a single image
   */
  static async uploadImage(
    file: File,
    options: UploadOptions = {}
  ): Promise<UploadResult> {
    const {
      category = 'catalogue',
      maxRetries = 5,
      timeoutMs = 600000, // 10 minutes default timeout (increased from 5 minutes)
      isOptional = false,
      onProgress,
      onStatusUpdate
    } = options;

    // Validate file first
    const validation = this.validateFile(file);
    if (!validation.isValid) {
      throw new ImageUploadError(
        validation.error || 'File validation failed',
        'VALIDATION_ERROR',
        400,
        false
      );
    }

    onStatusUpdate?.('Starting upload...', 'info');
    onProgress?.(0);

    let lastError: Error | null = null;
    const uploadId = Math.random().toString(36).substring(2, 15);
    
    console.log(`[ImageUpload] ${uploadId} - Starting upload for ${file.name} (${Math.round(file.size/1024)}KB)`);

    // Retry logic
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        onStatusUpdate?.(`Upload attempt ${attempt}...`, 'info');
        console.log(`[ImageUpload] ${uploadId} - Attempt ${attempt}/${maxRetries}`);
        
        const result = await this.performUpload(
          file,
          category,
          timeoutMs,
          (progress) => {
            // Scale progress based on attempt (reserve some % for retries)
            const scaledProgress = ((attempt - 1) / maxRetries) * 100 + (progress / maxRetries);
            const finalProgress = Math.min(scaledProgress, 95);
            console.log(`[ImageUpload] ${uploadId} - Progress: ${finalProgress.toFixed(1)}%`);
            onProgress?.(finalProgress);
          }
        );

        console.log(`[ImageUpload] ${uploadId} - Upload successful: ${result.url}`);
        onProgress?.(100);
        onStatusUpdate?.('Upload completed successfully!', 'info');
        return result;

      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        console.error(`[ImageUpload] ${uploadId} - Attempt ${attempt} failed:`, {
          error: lastError.message,
          code: error instanceof ImageUploadError ? error.code : 'UNKNOWN',
          retryable: error instanceof ImageUploadError ? error.isRetryable : true,
          stack: lastError.stack
        });

        if (error instanceof ImageUploadError && !error.isRetryable) {
          // Don't retry non-retryable errors
          console.log(`[ImageUpload] ${uploadId} - Non-retryable error, stopping attempts`);
          break;
        }

        if (attempt === maxRetries) {
          // Last attempt failed
          console.log(`[ImageUpload] ${uploadId} - All ${maxRetries} attempts failed`);
          break;
        }

        // Wait before retry (exponential backoff)
        const backoffMs = Math.min(1000 * Math.pow(2, attempt - 1), 5000);
        console.log(`[ImageUpload] ${uploadId} - Waiting ${backoffMs}ms before retry`);
        onStatusUpdate?.(`Attempt ${attempt} failed, retrying in ${backoffMs/1000}s...`, 'warning');
        
        await new Promise(resolve => setTimeout(resolve, backoffMs));
      }
    }

    // All attempts failed
    const errorMessage = lastError?.message || 'Upload failed after multiple attempts';
    console.error(`[ImageUpload] ${uploadId} - Final failure: ${errorMessage}`);
    
    if (isOptional) {
      console.log(`[ImageUpload] ${uploadId} - Using fallback for optional image`);
      onStatusUpdate?.('Optional image upload failed, using fallback', 'warning');
      return {
        success: false,
        url: this.FALLBACK_URL,
        key: 'fallback',
        filename: 'placeholder.jpg',
        warning: `Upload failed: ${errorMessage}. Using placeholder image.`,
        fallbackUrl: this.FALLBACK_URL
      };
    }

    throw new ImageUploadError(
      errorMessage,
      'UPLOAD_FAILED',
      500,
      true
    );
  }

  /**
   * Perform the actual upload
   */
  private static async performUpload(
    file: File,
    category: string,
    timeoutMs: number,
    onProgress?: (progress: number) => void
  ): Promise<UploadResult> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('category', category);

    // Create timeout promise with more detailed error message
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => {
        reject(new ImageUploadError(
          `Upload timeout after ${timeoutMs / 1000} seconds. This may be due to large file size, slow network, or server issues.`,
          'TIMEOUT_ERROR',
          408,
          true
        ));
      }, timeoutMs);
    });

    // Create upload promise
    const uploadPromise = fetch('/api/upload', {
      method: 'POST',
      body: formData,
    });

    onProgress?.(25);

    try {
      // Race upload against timeout
      const response = await Promise.race([uploadPromise, timeoutPromise]);
      onProgress?.(50);

      if (!response.ok) {
        // Handle non-JSON error responses (like 500 errors)
        let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
        let errorCode = 'HTTP_ERROR';
        
        try {
          const data = await response.json();
          errorMessage = data.error || data.message || errorMessage;
          errorCode = data.code || errorCode;
        } catch (parseError) {
          // If we can't parse JSON, use the status text
          console.warn('Failed to parse error response as JSON:', parseError);
        }
        
        throw new ImageUploadError(
          errorMessage,
          errorCode,
          response.status,
          response.status >= 500 // Retry server errors
        );
      }

      let data: any;
      try {
        data = await response.json();
        onProgress?.(75);
      } catch (parseError) {
        throw new ImageUploadError(
          'Failed to parse server response',
          'PARSE_ERROR',
          500,
          true
        );
      }

      // Progress to 80% after successful JSON parsing
      onProgress?.(80);

      // Handle the server API response format
      if (!data.success) {
        throw new ImageUploadError(
          data.error || data.message || 'Upload failed',
          data.code || 'UPLOAD_FAILED',
          500,
          true
        );
      }

      // Progress to 85% after success validation
      onProgress?.(85);

      // The server API returns different formats based on single vs multiple files
      // For single file uploads, it should return the URL directly
      let resultUrl = data.url;
      let resultFilename = data.filename;
      let resultKey = data.key;

      // If the server returned results array (batch upload format), get the first result
      if (data.results && Array.isArray(data.results) && data.results.length > 0) {
        const firstResult = data.results[0];
        resultUrl = firstResult.url;
        resultFilename = firstResult.filename;
        resultKey = firstResult.s3Key;
      }

      // Progress to 90% after data extraction
      onProgress?.(90);

      // Validate that we have a URL
      if (!resultUrl) {
        throw new ImageUploadError(
          'Invalid server response: missing URL',
          'INVALID_RESPONSE',
          500,
          true
        );
      }

      // Validate URL format (basic check)
      try {
        new URL(resultUrl);
      } catch (urlError) {
        console.warn('Received invalid URL format:', resultUrl);
        // Don't throw an error, just log it as this might still work
      }

      // Progress to 95% after URL validation
      onProgress?.(95);

      // Final validation and result construction
      const result: UploadResult = {
        success: true,
        url: resultUrl,
        key: resultKey,
        filename: resultFilename,
        warning: data.warning
      };

      // Final progress to 100%
      onProgress?.(100);

      return result;

    } catch (error) {
      if (error instanceof ImageUploadError) {
        throw error;
      }

      // Handle network errors
      if (error instanceof TypeError && error.message.includes('fetch')) {
        throw new ImageUploadError(
          'Network error: Please check your internet connection and try again',
          'NETWORK_ERROR',
          0,
          true
        );
      }

      // Handle other errors
      const errorMessage = error instanceof Error ? error.message : 'Unknown upload error';
      throw new ImageUploadError(
        `Upload failed: ${errorMessage}`,
        'UNKNOWN_ERROR',
        500,
        true
      );
    }
  }

  /**
   * Upload multiple images with progress tracking
   */
  static async uploadMultipleImages(
    files: (File | null)[],
    options: UploadOptions = {}
  ): Promise<(UploadResult | null)[]> {
    const results: (UploadResult | null)[] = [];
    const totalFiles = files.filter(f => f !== null).length;
    let completedFiles = 0;

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      
      if (!file) {
        results.push(null);
        continue;
      }

      try {
        const fileOptions = {
          ...options,
          isOptional: i > 0, // First image is required, others are optional
          onProgress: (progress: number) => {
            const overallProgress = ((completedFiles + progress / 100) / totalFiles) * 100;
            options.onProgress?.(overallProgress);
          },
          onStatusUpdate: (message: string, type: 'info' | 'warning' | 'error') => {
            options.onStatusUpdate?.(`Image ${i + 1}: ${message}`, type);
          }
        };

        const result = await this.uploadImage(file, fileOptions);
        results.push(result);
        completedFiles++;

      } catch (error) {
        console.error(`Failed to upload image ${i + 1}:`, error);
        
        if (i === 0) {
          // First image is critical
          throw error;
        } else {
          // Optional images can fail
          results.push({
            success: false,
            error: error instanceof Error ? error.message : 'Upload failed',
            fallbackUrl: this.FALLBACK_URL
          });
          completedFiles++;
        }
      }
    }

    return results;
  }

  /**
   * Get progress message for UI
   */
  static getProgressMessage(progress: number, filename: string): string {
    if (progress === 0) return `Preparing ${filename}...`;
    if (progress < 25) return `Starting upload of ${filename}...`;
    if (progress < 50) return `Uploading ${filename}...`;
    if (progress < 75) return `Processing ${filename}...`;
    if (progress < 100) return `Finalizing ${filename}...`;
    return `${filename} uploaded successfully!`;
  }

  /**
   * Clean error message for display
   */
  static cleanErrorMessage(error: string): string {
    // Remove technical details and make user-friendly
    return error
      .replace(/ImageUploadError: /, '')
      .replace(/Error: /, '')
      .replace(/HTTP \d+: /, '')
      .trim();
  }
}
