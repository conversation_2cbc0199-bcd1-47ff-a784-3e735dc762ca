/**
 * Unit tests for safe array utilities
 * Tests prevention of runtime crashes from undefined/null arrays
 */

import {
  safeMap,
  safeFilter,
  safeReduce,
  safeLength,
  safeGet,
  hasItems
} from '../arrayUtils';

describe('Safe Array Utilities', () => {
  describe('safeMap', () => {
    it('should map over valid arrays', () => {
      const input = [1, 2, 3];
      const result = safeMap(input, x => x * 2);
      expect(result).toEqual([2, 4, 6]);
    });

    it('should handle null array', () => {
      const result = safeMap(null, x => x * 2);
      expect(result).toEqual([]);
    });

    it('should handle undefined array', () => {
      const result = safeMap(undefined, x => x * 2);
      expect(result).toEqual([]);
    });

    it('should handle empty array', () => {
      const result = safeMap([], x => x * 2);
      expect(result).toEqual([]);
    });

    it('should preserve map function behavior', () => {
      const input = ['a', 'b', 'c'];
      const result = safeMap(input, (item, index) => `${item}${index}`);
      expect(result).toEqual(['a0', 'b1', 'c2']);
    });
  });

  describe('safeFilter', () => {
    it('should filter valid arrays', () => {
      const input = [1, 2, 3, 4, 5];
      const result = safeFilter(input, x => x > 3);
      expect(result).toEqual([4, 5]);
    });

    it('should handle null array', () => {
      const result = safeFilter(null, x => x > 3);
      expect(result).toEqual([]);
    });

    it('should handle undefined array', () => {
      const result = safeFilter(undefined, x => x > 3);
      expect(result).toEqual([]);
    });

    it('should handle empty array', () => {
      const result = safeFilter([], x => x > 3);
      expect(result).toEqual([]);
    });

    it('should preserve filter function behavior', () => {
      const input = ['apple', 'banana', 'cherry'];
      const result = safeFilter(input, (item, index) => index % 2 === 0);
      expect(result).toEqual(['apple', 'cherry']);
    });
  });

  describe('safeReduce', () => {
    it('should reduce valid arrays', () => {
      const input = [1, 2, 3, 4];
      const result = safeReduce(input, (acc, curr) => acc + curr, 0);
      expect(result).toBe(10);
    });

    it('should handle null array', () => {
      const result = safeReduce(null, (acc, curr) => acc + curr, 5);
      expect(result).toBe(5);
    });

    it('should handle undefined array', () => {
      const result = safeReduce(undefined, (acc, curr) => acc + curr, 10);
      expect(result).toBe(10);
    });

    it('should handle empty array', () => {
      const result = safeReduce([], (acc, curr) => acc + curr, 7);
      expect(result).toBe(7);
    });

    it('should preserve reduce function behavior', () => {
      const input = ['a', 'b', 'c'];
      const result = safeReduce(input, (acc, curr, index) => `${acc}${curr}${index}`, '');
      expect(result).toBe('a0b1c2');
    });
  });

  describe('safeLength', () => {
    it('should return length of valid arrays', () => {
      expect(safeLength([1, 2, 3])).toBe(3);
      expect(safeLength(['a', 'b'])).toBe(2);
      expect(safeLength([])).toBe(0);
    });

    it('should return 0 for null', () => {
      expect(safeLength(null)).toBe(0);
    });

    it('should return 0 for undefined', () => {
      expect(safeLength(undefined)).toBe(0);
    });
  });

  describe('safeGet', () => {
    it('should get items from valid arrays', () => {
      const input = ['a', 'b', 'c'];
      expect(safeGet(input, 0)).toBe('a');
      expect(safeGet(input, 1)).toBe('b');
      expect(safeGet(input, 2)).toBe('c');
    });

    it('should return undefined for out of bounds', () => {
      const input = ['a', 'b'];
      expect(safeGet(input, 5)).toBeUndefined();
      expect(safeGet(input, -1)).toBeUndefined();
    });

    it('should handle null array', () => {
      expect(safeGet(null, 0)).toBeUndefined();
    });

    it('should handle undefined array', () => {
      expect(safeGet(undefined, 0)).toBeUndefined();
    });

    it('should handle empty array', () => {
      expect(safeGet([], 0)).toBeUndefined();
    });
  });

  describe('hasItems', () => {
    it('should return true for arrays with items', () => {
      expect(hasItems([1])).toBe(true);
      expect(hasItems(['a', 'b'])).toBe(true);
      expect(hasItems([null, undefined])).toBe(true);
    });

    it('should return false for empty arrays', () => {
      expect(hasItems([])).toBe(false);
    });

    it('should return false for null', () => {
      expect(hasItems(null)).toBe(false);
    });

    it('should return false for undefined', () => {
      expect(hasItems(undefined)).toBe(false);
    });

    it('should return false for non-arrays', () => {
      expect(hasItems('string' as any)).toBe(false);
      expect(hasItems(123 as any)).toBe(false);
      expect(hasItems({} as any)).toBe(false);
    });
  });
});

describe('Real-world Scenarios', () => {
  describe('API Response Handling', () => {
    it('should safely handle undefined API responses', () => {
      const apiResponse = undefined;
      const userNames = safeMap(apiResponse, (user: any) => user.name);
      expect(userNames).toEqual([]);
    });

    it('should safely handle null API responses', () => {
      const apiResponse = null;
      const userIds = safeMap(apiResponse, (user: any) => user.id);
      expect(userIds).toEqual([]);
    });

    it('should safely filter invalid data', () => {
      const apiResponse: any = undefined;
      const validUsers = safeFilter(apiResponse, (user: any) => user && user.active);
      expect(validUsers).toEqual([]);
    });
  });

  describe('Component Props Handling', () => {
    it('should safely render empty state when items are undefined', () => {
      const items = undefined;
      const shouldShowEmptyState = !hasItems(items);
      expect(shouldShowEmptyState).toBe(true);
    });

    it('should safely get first item for display', () => {
      const items = null;
      const firstItem = safeGet(items, 0);
      expect(firstItem).toBeUndefined();
    });

    it('should safely count items for display', () => {
      const items = undefined;
      const count = safeLength(items);
      expect(count).toBe(0);
    });
  });

  describe('Pagination Scenarios', () => {
    it('should handle undefined page data', () => {
      const pageData = undefined;
      const totalItems = safeLength(pageData);
      const hasData = hasItems(pageData);
      
      expect(totalItems).toBe(0);
      expect(hasData).toBe(false);
    });

    it('should safely slice undefined arrays', () => {
      const allItems = null;
      const pageItems = safeFilter(allItems, (_, index) => index < 10);
      expect(pageItems).toEqual([]);
    });
  });

  describe('Search/Filter Scenarios', () => {
    it('should handle search on undefined data', () => {
      const searchResults = undefined;
      const filteredResults = safeFilter(searchResults, (item: any) => 
        item.title.toLowerCase().includes('search')
      );
      expect(filteredResults).toEqual([]);
    });

    it('should safely transform search results', () => {
      const searchData = null;
      const displayData = safeMap(searchData, (item: any) => ({
        id: item.id,
        title: item.title
      }));
      expect(displayData).toEqual([]);
    });
  });
});

describe('Performance and Memory Safety', () => {
  it('should not throw errors with large null/undefined arrays', () => {
    expect(() => {
      safeMap(null, x => x);
      safeFilter(undefined, x => true);
      safeReduce(null, (acc, curr) => acc + curr, 0);
    }).not.toThrow();
  });

  it('should handle deeply nested operations safely', () => {
    const nestedData = null;
    const result = safeMap(nestedData, item => 
      safeMap(item?.children, child => 
        safeFilter(child?.tags, tag => tag.active)
      )
    );
    expect(result).toEqual([]);
  });

  it('should handle chained operations safely', () => {
    const data = undefined;
    const result = safeFilter(
      safeMap(data, item => item?.value),
      value => value > 0
    );
    expect(result).toEqual([]);
  });
}); 