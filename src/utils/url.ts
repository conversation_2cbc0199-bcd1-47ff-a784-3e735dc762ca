/**
 * Get the base URL for the application
 */
export function getBaseUrl(): string {
  // In production, use the environment variable or default domain
  if (process.env.NODE_ENV === 'production') {
    return process.env.NEXT_PUBLIC_BASE_URL || 'https://mocky.co.ke';
  }
  
  // In development, use localhost
  return process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
}

/**
 * Generate a canonical URL for a given path
 */
export function getCanonicalUrl(path: string): string {
  const baseUrl = getBaseUrl();
  const cleanPath = path.startsWith('/') ? path : `/${path}`;
  return `${baseUrl}${cleanPath}`;
}

/**
 * Generate a blog post URL
 */
export function getBlogPostUrl(slug: string): string {
  return getCanonicalUrl(`/blog/${slug}`);
}

/**
 * Generate a blog category URL
 */
export function getBlogCategoryUrl(category: string): string {
  return getCanonicalUrl(`/blog?category=${encodeURIComponent(category)}`);
}

/**
 * Generate a blog tag URL
 */
export function getBlogTagUrl(tag: string): string {
  return getCanonicalUrl(`/blog?tag=${encodeURIComponent(tag)}`);
}

/**
 * Create a shareable URL with UTM parameters for tracking
 */
export function createShareableUrl(
  url: string, 
  source: string, 
  medium: string = 'social',
  campaign: string = 'blog_share'
): string {
  const urlObj = new URL(url);
  urlObj.searchParams.set('utm_source', source);
  urlObj.searchParams.set('utm_medium', medium);
  urlObj.searchParams.set('utm_campaign', campaign);
  return urlObj.toString();
}

/**
 * Validate and clean URL
 */
export function cleanUrl(url: string): string {
  try {
    const urlObj = new URL(url);
    return urlObj.toString();
  } catch {
    // If URL is invalid, return as-is
    return url;
  }
} 