/**
 * Portfolio Diagnostic Tool
 *
 * This script helps diagnose portfolio data issues and provides solutions.
 * Run this in the browser console to get detailed information about the portfolio state.
 */

export async function runPortfolioDiagnostic() {
  console.log('🔍 Starting Portfolio Diagnostic (S3-Only Mode)...\n');

  const results = {
    s3Data: null as any,
    s3Error: null as string | null,
    recommendations: [] as string[]
  };

  // Test S3 data fetch
  console.log('📡 Testing S3 data fetch...');
  try {
    const s3Response = await fetch('/api/admin/portfolio?t=' + Date.now(), {
      headers: { 'Cache-Control': 'no-cache' }
    });

    if (s3Response.ok) {
      results.s3Data = await s3Response.json();
      console.log(`✅ S3 fetch successful: ${results.s3Data.length} items found`);
    } else {
      results.s3Error = `HTTP ${s3Response.status}: ${s3Response.statusText}`;
      console.log(`❌ S3 fetch failed: ${results.s3Error}`);
    }
  } catch (error) {
    results.s3Error = error instanceof Error ? error.message : 'Unknown error';
    console.log(`❌ S3 fetch error: ${results.s3Error}`);
  }

  // Analyze results and provide recommendations
  console.log('\n📊 Analysis and Recommendations:');

  const s3Count = results.s3Data?.length || 0;

  if (results.s3Error) {
    console.log('❌ CRITICAL: S3 data source is failing');
    results.recommendations.push('Check API endpoints and authentication');
    results.recommendations.push('Verify S3 configuration and credentials');
    results.recommendations.push('Check S3 bucket access and permissions');
  } else if (s3Count === 0) {
    console.log('ℹ️ INFO: No portfolio items found in S3');
    results.recommendations.push('Add some portfolio items to get started');
    results.recommendations.push('Use the "Add Portfolio Item" button');
    results.recommendations.push('Upload images to S3 storage');
  } else {
    console.log('✅ SUCCESS: S3 data source is working correctly');
    console.log(`   - Found ${s3Count} portfolio items`);
    results.recommendations.push('Portfolio data is healthy and accessible');
  }

  // Display sample data
  if (results.s3Data?.length > 0) {
    console.log('\n📋 Sample portfolio item:');
    console.log(results.s3Data[0]);

    // Check for common issues
    const sampleItem = results.s3Data[0];
    if (!sampleItem.imageSrc || !sampleItem.imageSrc.includes('http')) {
      console.log('⚠️ WARNING: Image URLs may not be properly formatted');
      results.recommendations.push('Check image URL formatting');
    }

    if (!sampleItem.category) {
      console.log('⚠️ WARNING: Some items may be missing category information');
      results.recommendations.push('Ensure all items have proper categories');
    }
  }

  // Quick fix suggestions
  console.log('\n🔧 Available Commands:');
  console.log('// Refresh portfolio data:');
  console.log('window.location.reload()');
  console.log('');
  console.log('// Run diagnostic again:');
  console.log('await portfolioDiagnostic.run()');

  console.log('\n📝 Summary:');
  results.recommendations.forEach((rec, index) => {
    console.log(`${index + 1}. ${rec}`);
  });

  return results;
}

// Refresh portfolio data
export async function refreshPortfolioData() {
  console.log('🔄 Refreshing portfolio data...\n');

  try {
    // Clear any cached data by adding timestamp
    const response = await fetch('/api/admin/portfolio?t=' + Date.now(), {
      headers: { 'Cache-Control': 'no-cache' }
    });

    if (response.ok) {
      const data = await response.json();
      console.log(`✅ Refresh successful: ${data.length} items loaded`);

      // Refresh the page to show updated data
      window.location.reload();
    } else {
      console.log('❌ Refresh failed: Could not fetch updated data');
    }
  } catch (error) {
    console.error('❌ Refresh error:', error);
  }
}

// Export for browser console use
if (typeof window !== 'undefined') {
  (window as any).portfolioDiagnostic = {
    run: runPortfolioDiagnostic,
    refresh: refreshPortfolioData
  };

  console.log('Portfolio Diagnostic Tools loaded (S3-Only Mode)!');
  console.log('Run: portfolioDiagnostic.run() to diagnose issues');
  console.log('Run: portfolioDiagnostic.refresh() to refresh data');
}
