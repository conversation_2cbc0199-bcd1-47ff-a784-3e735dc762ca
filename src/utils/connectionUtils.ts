/**
 * Connection speed detection and adaptive timeout utilities
 */

export type ConnectionSpeed = 'fast' | 'slow' | 'very-slow';

export interface ConnectionInfo {
  speed: ConnectionSpeed;
  effectiveType?: string;
  downlink?: number;
  rtt?: number;
  saveData?: boolean;
}

/**
 * Detect user's connection speed
 */
export async function detectConnectionSpeed(): Promise<ConnectionInfo> {
  // Try to use Network Information API first
  const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection;
  
  if (connection) {
    const effectiveType = connection.effectiveType;
    const downlink = connection.downlink; // Mbps
    const rtt = connection.rtt; // ms
    const saveData = connection.saveData;
    
    let speed: ConnectionSpeed;
    
    // Determine speed based on effective type and downlink
    if (effectiveType === '4g' && downlink > 10) {
      speed = 'fast';
    } else if (effectiveType === '4g' || (effectiveType === '3g' && downlink > 1.5)) {
      speed = 'slow';
    } else {
      speed = 'very-slow';
    }
    
    // Override to very-slow if save-data is enabled
    if (saveData) {
      speed = 'very-slow';
    }
    
    console.log(`[Connection] Detected: ${effectiveType}, ${downlink}Mbps, ${rtt}ms RTT, Speed: ${speed}`);
    
    return {
      speed,
      effectiveType,
      downlink,
      rtt,
      saveData
    };
  }
  
  // Fallback: Speed test with a small resource
  try {
    const startTime = performance.now();
    const response = await fetch('/api/health', { 
      method: 'HEAD',
      cache: 'no-cache'
    });
    const duration = performance.now() - startTime;
    
    let speed: ConnectionSpeed;
    if (duration < 100) {
      speed = 'fast';
    } else if (duration < 500) {
      speed = 'slow';
    } else {
      speed = 'very-slow';
    }
    
    console.log(`[Connection] Speed test: ${duration}ms, Speed: ${speed}`);
    
    return { speed };
  } catch (error) {
    console.warn('[Connection] Speed detection failed, assuming very-slow');
    return { speed: 'very-slow' };
  }
}

/**
 * Calculate dynamic timeout based on file size and connection speed
 */
export function calculateDynamicTimeout(
  fileSize: number, 
  connectionSpeed: ConnectionSpeed,
  baseTimeoutMs: number = 60000
): number {
  const speedMultipliers = {
    'fast': 1,
    'slow': 3,
    'very-slow': 6
  };
  
  // Calculate size-based multiplier (10MB baseline)
  const sizeMultiplier = Math.max(1, fileSize / (10 * 1024 * 1024));
  
  // Apply both multipliers
  const timeout = baseTimeoutMs * speedMultipliers[connectionSpeed] * sizeMultiplier;
  
  // Cap at reasonable limits
  const minTimeout = 60000; // 1 minute minimum
  const maxTimeout = 1800000; // 30 minutes maximum
  
  const finalTimeout = Math.max(minTimeout, Math.min(maxTimeout, timeout));
  
  console.log(`[Connection] Timeout calculated: ${finalTimeout/1000}s for ${Math.round(fileSize/1024/1024)}MB on ${connectionSpeed} connection`);
  
  return finalTimeout;
}

/**
 * Get recommended chunk size based on connection speed
 */
export function getOptimalChunkSize(connectionSpeed: ConnectionSpeed): number {
  const chunkSizes = {
    'fast': 10 * 1024 * 1024,      // 10MB for fast connections
    'slow': 5 * 1024 * 1024,       // 5MB for slow connections  
    'very-slow': 2 * 1024 * 1024   // 2MB for very slow connections
  };
  
  return chunkSizes[connectionSpeed];
}

/**
 * Check if user is on a metered connection
 */
export function isMeteredConnection(): boolean {
  const connection = (navigator as any).connection;
  return connection?.saveData === true || connection?.effectiveType === '2g';
}

/**
 * Get upload strategy recommendations based on connection
 */
export function getUploadStrategy(fileSize: number, connectionInfo: ConnectionInfo) {
  const isSmallFile = fileSize < 5 * 1024 * 1024; // 5MB
  const isLargeFile = fileSize > 10 * 1024 * 1024; // 10MB
  const isVeryLargeFile = fileSize > 50 * 1024 * 1024; // 50MB
  
  return {
    shouldCompress: isLargeFile || connectionInfo.speed !== 'fast',
    // Use chunked upload for files larger than 5MB OR if connection is very slow
    shouldChunk: !isSmallFile || connectionInfo.speed === 'very-slow',
    chunkSize: getOptimalChunkSize(connectionInfo.speed),
    timeout: calculateDynamicTimeout(fileSize, connectionInfo.speed),
    maxRetries: connectionInfo.speed === 'very-slow' ? 5 : 3,
    compressionQuality: connectionInfo.speed === 'very-slow' ? 0.7 : 0.85,
    showDetailedProgress: connectionInfo.speed !== 'fast',
    // Use direct upload for small files on good connections
    useDirectUpload: isSmallFile && connectionInfo.speed !== 'very-slow'
  };
} 