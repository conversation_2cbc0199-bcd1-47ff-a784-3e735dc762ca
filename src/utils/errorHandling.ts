import { useNotification } from '@/contexts/NotificationContext';
import { useState, useCallback } from 'react';

// Standard error types
export interface AppError {
  message: string;
  code?: string;
  details?: any;
  statusCode?: number;
}

// Standard API response format
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  details?: any;
}

// Error handling configuration
export interface ErrorHandlingOptions {
  showToast?: boolean;
  fallbackMessage?: string;
  onError?: (error: AppError) => void;
  onSuccess?: (data: any) => void;
}

// Parse different error formats into standard AppError
export function parseError(error: unknown): AppError {
  if (error instanceof Error) {
    return {
      message: error.message,
      details: error.stack,
    };
  }

  if (typeof error === 'string') {
    return { message: error };
  }

  if (error && typeof error === 'object') {
    const errorObj = error as any;
    return {
      message: errorObj.message || errorObj.error || 'An unexpected error occurred',
      code: errorObj.code,
      statusCode: errorObj.statusCode || errorObj.status,
      details: errorObj.details,
    };
  }

  return { message: 'An unknown error occurred' };
}

// Standard API error handler
export async function handleApiResponse<T>(
  response: Response,
  fallbackMessage = 'An error occurred'
): Promise<T> {
  if (!response.ok) {
    let errorData: any;
    try {
      const text = await response.text();
      errorData = text ? JSON.parse(text) : {};
    } catch {
      errorData = {};
    }

    throw new Error(
      errorData.error || 
      errorData.message || 
      `${fallbackMessage} (${response.status})`
    );
  }

  const data = await response.json();
  
  // Handle API response format
  if ('success' in data) {
    if (!data.success) {
      throw new Error(data.error || data.message || fallbackMessage);
    }
    return data.data || data;
  }

  return data;
}

// Custom hook for standardized async operations
export function useAsyncOperation() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<AppError | null>(null);
  const { showNotification } = useNotification();

  const execute = useCallback(
    async <T>(
      operation: () => Promise<T>,
      options: ErrorHandlingOptions = {}
    ): Promise<T | null> => {
      const {
        showToast = true,
        fallbackMessage = 'Operation failed',
        onError,
        onSuccess,
      } = options;

      try {
        setLoading(true);
        setError(null);

        const result = await operation();

        if (onSuccess) {
          onSuccess(result);
        }

        return result;
      } catch (err) {
        const appError = parseError(err);
        setError(appError);

        if (showToast) {
          showNotification('error', 'Error', appError.message || fallbackMessage);
        }

        if (onError) {
          onError(appError);
        }

        console.error('Async operation failed:', appError);
        return null;
      } finally {
        setLoading(false);
      }
    },
    [showNotification]
  );

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    loading,
    error,
    execute,
    clearError,
  };
}

// Wrapper for fetch operations with standard error handling
export async function apiRequest<T>(
  url: string,
  options: RequestInit = {},
  errorMessage = 'Request failed'
): Promise<T> {
  const response = await fetch(url, {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    credentials: 'include',
    ...options,
  });

  return handleApiResponse<T>(response, errorMessage);
}

// Higher-order component for error handling
export function withErrorHandling<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  fallbackMessage = 'Operation failed',
  options: ErrorHandlingOptions = {}
): T {
  return (async (...args: any[]) => {
    try {
      const result = await fn(...args);
      if (options.onSuccess) {
        options.onSuccess(result);
      }
      return result;
    } catch (error) {
      const appError = parseError(error);
      
      if (options.onError) {
        options.onError(appError);
      }

      console.error('Operation failed:', appError);
      throw appError;
    }
  }) as T;
}

// Standard error UI component data
export function getErrorDisplayData(error: AppError | null) {
  if (!error) return null;

  return {
    title: 'Error',
    message: error.message,
    details: error.details,
    statusCode: error.statusCode,
    retryable: !error.statusCode || (error.statusCode >= 500 && error.statusCode < 600),
  };
}

// Loading states manager
export function useLoadingStates() {
  const [loadingStates, setLoadingStates] = useState<Record<string, boolean>>({});

  const setLoading = useCallback((key: string, loading: boolean) => {
    setLoadingStates(prev => ({
      ...prev,
      [key]: loading,
    }));
  }, []);

  const isLoading = useCallback((key: string) => {
    return loadingStates[key] || false;
  }, [loadingStates]);

  const isAnyLoading = useCallback(() => {
    return Object.values(loadingStates).some(Boolean);
  }, [loadingStates]);

  return { setLoading, isLoading, isAnyLoading };
}
