/**
 * Server-Side Test Utilities
 * 
 * This module contains Node.js specific test functionality that must run on the server.
 */

import fs from 'fs';
import path from 'path';
import { PrismaClient } from '@prisma/client';
import { S3Client, ListObjectsV2Command, HeadObjectCommand, PutObjectCommand, GetObjectCommand, DeleteObjectCommand } from '@aws-sdk/client-s3';
import { TestResult, TestType, S3Config } from './testUtils';
import { getDefaultStorageConfig } from '@/lib/storageConfig';

// Initialize Prisma client
const prisma = new PrismaClient();

/**
 * Get S3 configuration from environment variables (server-side)
 */
export function getS3ConfigFromEnv(): S3Config {
  return {
    region: process.env.NEXT_PUBLIC_S3_REGION || process.env.S3_REGION || '',
    endpoint: process.env.NEXT_PUBLIC_S3_ENDPOINT || process.env.S3_ENDPOINT || '',
    bucketName: process.env.NEXT_PUBLIC_S3_BUCKET || process.env.S3_BUCKET || '',
    accessKeyId: process.env.NEXT_PUBLIC_S3_ACCESS_KEY || process.env.S3_ACCESS_KEY || '',
    secretAccessKey: process.env.NEXT_PUBLIC_S3_SECRET_KEY || process.env.S3_SECRET_KEY || '',
  };
}

/**
 * Create an S3 client with the given configuration
 */
export function createS3Client(config: S3Config): S3Client {
  return new S3Client({
    region: config.region,
    endpoint: config.endpoint,
    credentials: {
      accessKeyId: config.accessKey,
      secretAccessKey: config.secretKey,
    },
    forcePathStyle: true,
  });
}

/**
 * Test S3 connection
 */
export async function testS3Connection(config?: S3Config): Promise<TestResult> {
  const startTime = Date.now();

  try {
    let s3Config: S3Config;
    
    if (config) {
      // Use provided config
      s3Config = config;
    } else {
      // Get from database storage configuration (updated approach)
      const dbConfig = await getDefaultStorageConfig();
      if (dbConfig) {
        s3Config = {
          region: dbConfig.region,
          endpoint: dbConfig.endpoint,
          bucketName: dbConfig.bucketName,
          accessKeyId: dbConfig.accessKey,
          secretAccessKey: dbConfig.secretKey,
        };
      } else {
        // Fallback to environment variables
        s3Config = getS3ConfigFromEnv();
      }
    }

    // Validate configuration
    if (!s3Config.region || !s3Config.endpoint || !s3Config.bucketName ||
        !s3Config.accessKeyId || !s3Config.secretAccessKey) {
      return {
        success: false,
        message: 'S3 configuration is incomplete',
        details: {
          missingFields: Object.entries(s3Config)
            .filter(([_, value]) => !value)
            .map(([key]) => key)
        },
        duration: Date.now() - startTime
      };
    }

    // Create S3 client
    const s3Client = createS3Client(s3Config);

    // List objects in the bucket
    const command = new ListObjectsV2Command({
      Bucket: s3Config.bucketName,
      MaxKeys: 5,
    });

    const response = await s3Client.send(command);

    return {
      success: true,
      message: `Successfully connected to S3 bucket: ${s3Config.bucketName}`,
      details: {
        objectCount: response.KeyCount || 0,
        objects: response.Contents?.slice(0, 5).map(item => ({
          key: item.Key,
          size: item.Size,
          lastModified: item.LastModified
        }))
      },
      duration: Date.now() - startTime
    };
  } catch (error: any) {
    console.error('S3 Connection Test Error:', error);
    
    // Enhanced error details for better debugging
    const errorDetails: any = {
      errorType: error.constructor.name,
      errorMessage: error.message,
      errorCode: error.Code || error.code,
      errorStatus: error.$metadata?.httpStatusCode,
      requestId: error.$metadata?.requestId || error.RequestId,
      errorStack: error.stack
    };

    // Check for specific AWS SDK errors
    if (error.name === 'CredentialsProviderError') {
      errorDetails.hint = 'Check S3 credentials configuration';
    } else if (error.name === 'SignatureDoesNotMatch') {
      errorDetails.hint = 'S3 credentials may be invalid or expired';
    }

    return {
      success: false,
      message: `Failed to connect to S3: ${error.message}`,
      details: errorDetails,
      duration: Date.now() - startTime
    };
  }
}

/**
 * Test S3 upload
 */
export async function testS3Upload(config?: S3Config): Promise<TestResult> {
  const startTime = Date.now();

  try {
    let s3Config: S3Config;
    
    if (config) {
      // Use provided config
      s3Config = config;
    } else {
      // Get from database storage configuration (updated approach)
      const dbConfig = await getDefaultStorageConfig();
      if (dbConfig) {
        s3Config = {
          region: dbConfig.region,
          endpoint: dbConfig.endpoint,
          bucketName: dbConfig.bucketName,
          accessKeyId: dbConfig.accessKey,
          secretAccessKey: dbConfig.secretKey,
        };
      } else {
        // Fallback to environment variables
        s3Config = getS3ConfigFromEnv();
      }
    }

    // Validate configuration
    if (!s3Config.region || !s3Config.endpoint || !s3Config.bucketName ||
        !s3Config.accessKeyId || !s3Config.secretAccessKey) {
      return {
        success: false,
        message: 'S3 configuration is incomplete',
        details: {
          missingFields: Object.entries(s3Config)
            .filter(([_, value]) => !value)
            .map(([key]) => key)
        },
        duration: Date.now() - startTime
      };
    }

    // Create S3 client
    const s3Client = createS3Client(s3Config);

    // Generate a unique test key
    const testKey = `test/test-upload-${Date.now()}.txt`;

    // Create a test file content
    const testContent = `Test file created at ${new Date().toISOString()}`;

    // Upload the test file
    const uploadCommand = new PutObjectCommand({
      Bucket: s3Config.bucketName,
      Key: testKey,
      Body: testContent,
      ContentType: 'text/plain',
      ACL: 'public-read',
    });

    await s3Client.send(uploadCommand);

    // Verify the upload
    const getCommand = new GetObjectCommand({
      Bucket: s3Config.bucketName,
      Key: testKey,
    });

    await s3Client.send(getCommand);

    // Generate URL
    const imageUrl = `${s3Config.endpoint}/${s3Config.bucketName}/${testKey}`;

    // Clean up
    const deleteCommand = new DeleteObjectCommand({
      Bucket: s3Config.bucketName,
      Key: testKey,
    });

    await s3Client.send(deleteCommand);

    return {
      success: true,
      message: 'Successfully uploaded, verified, and cleaned up test file',
      details: {
        testKey,
        url: imageUrl,
        uploadTime: Date.now() - startTime
      },
      duration: Date.now() - startTime
    };
  } catch (error: any) {
    console.error('S3 Upload Test Error:', error);
    
    // Enhanced error details for better debugging
    const errorDetails: any = {
      errorType: error.constructor.name,
      errorMessage: error.message,
      errorCode: error.Code || error.code,
      errorStatus: error.$metadata?.httpStatusCode,
      requestId: error.$metadata?.requestId || error.RequestId,
      errorStack: error.stack
    };

    // Check for specific AWS SDK errors
    if (error.name === 'CredentialsProviderError') {
      errorDetails.hint = 'Check S3 credentials configuration';
    } else if (error.name === 'SignatureDoesNotMatch') {
      errorDetails.hint = 'S3 credentials may be invalid or expired';
    } else if (error.message?.includes('aborted')) {
      errorDetails.hint = 'Upload was aborted - check timeouts and network connectivity';
    } else if (error.message?.includes('Deserialization')) {
      errorDetails.hint = 'Response parsing error - check S3 endpoint and region settings';
    }

    return {
      success: false,
      message: `Failed to test S3 upload: ${error.message}`,
      details: errorDetails,
      duration: Date.now() - startTime
    };
  }
}

/**
 * Test database connection
 */
export async function testDatabaseConnection(): Promise<TestResult> {
  const startTime = Date.now();

  try {
    // Test connection by querying the database
    const result = await prisma.$queryRaw`SELECT 1 as connected`;

    return {
      success: true,
      message: 'Successfully connected to database',
      details: { result },
      duration: Date.now() - startTime
    };
  } catch (error: any) {
    return {
      success: false,
      message: `Failed to connect to database: ${error.message}`,
      details: { error: error.toString() },
      duration: Date.now() - startTime
    };
  }
} 