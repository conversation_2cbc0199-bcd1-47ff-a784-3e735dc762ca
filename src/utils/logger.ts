/**
 * Enhanced logging utility for better debugging
 * 
 * This module provides structured logging with context information,
 * log levels, and optional persistence to help with debugging issues
 * in both development and production environments.
 */

import { PerformanceMetrics, ErrorMetrics } from '@/types/api';

/**
 * Log levels for different types of events
 */
export enum LogLevel {
  DEBUG = 'DEBUG',
  INFO = 'INFO',
  WARN = 'WARN',
  ERROR = 'ERROR',
  FATAL = 'FATAL'
}

/**
 * Base log context interface
 */
export interface BaseLogContext {
  requestId: string;
  userId?: string;
  action: string;
  resource?: string;
  endpoint?: string;
  method?: string;
  ipAddress?: string;
  userAgent?: string;
  duration?: number;
  metadata?: Record<string, any>;
}

/**
 * Structured log entry
 */
interface LogEntry {
  level: LogLevel;
  message: string;
  timestamp: string;
  context: BaseLogContext;
  error?: {
    name: string;
    message: string;
    stack?: string;
    code?: string;
  };
}

/**
 * Enterprise Logger class with structured logging
 */
export class Logger {
  private static instance: Logger;
  private environment: string;
  private serviceName: string;
  private version: string;

  constructor() {
    this.environment = process.env.NODE_ENV || 'development';
    this.serviceName = process.env.SERVICE_NAME || 'mocky-admin';
    this.version = process.env.API_VERSION || '1.0.0';
  }

  /**
   * Get logger instance (singleton)
   */
  static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger();
    }
    return Logger.instance;
  }

  /**
   * Create base log entry
   */
  private createLogEntry(
    level: LogLevel,
    message: string,
    context: BaseLogContext,
    error?: Error
  ): LogEntry {
    return {
      level,
      message,
      timestamp: new Date().toISOString(),
             context: {
         ...context,
         metadata: {
           ...context.metadata,
           environment: this.environment,
           service: this.serviceName,
           version: this.version
         }
       },
      error: error ? {
        name: error.name,
        message: error.message,
        stack: error.stack,
        code: (error as any).code
      } : undefined
    };
  }

  /**
   * Output log entry to console (structured JSON in production)
   */
  private output(entry: LogEntry): void {
    if (this.environment === 'development') {
      // Human-readable format for development
      const timestamp = entry.timestamp;
      const level = entry.level.padEnd(5);
      const requestId = entry.context.requestId.slice(0, 8);
      const action = entry.context.action;
      const duration = entry.context.duration ? `${entry.context.duration}ms` : '';
      
      console.log(`[${timestamp}] ${level} [${requestId}] ${action} ${entry.message} ${duration}`);
      
      if (entry.error) {
        console.error(`  Error: ${entry.error.name}: ${entry.error.message}`);
        if (entry.error.stack) {
          console.error(`  Stack: ${entry.error.stack}`);
        }
      }
      
      if (entry.context.metadata && Object.keys(entry.context.metadata).length > 0) {
        console.log(`  Metadata:`, entry.context.metadata);
      }
    } else {
      // Structured JSON for production
      console.log(JSON.stringify(entry));
    }
  }

  /**
   * Debug level logging
   */
  debug(message: string, context: BaseLogContext): void {
    if (this.environment !== 'production') {
      const entry = this.createLogEntry(LogLevel.DEBUG, message, context);
      this.output(entry);
    }
  }

  /**
   * Info level logging
   */
  info(message: string, context: BaseLogContext): void {
    const entry = this.createLogEntry(LogLevel.INFO, message, context);
    this.output(entry);
  }

  /**
   * Warning level logging
   */
  warn(message: string, context: BaseLogContext): void {
    const entry = this.createLogEntry(LogLevel.WARN, message, context);
    this.output(entry);
  }

  /**
   * Error level logging
   */
  error(message: string, error: Error, context: BaseLogContext): void {
    const entry = this.createLogEntry(LogLevel.ERROR, message, context, error);
    this.output(entry);
  }

  /**
   * Fatal level logging
   */
  fatal(message: string, error: Error, context: BaseLogContext): void {
    const entry = this.createLogEntry(LogLevel.FATAL, message, context, error);
    this.output(entry);
  }

  /**
   * Log API request/response
   */
  apiRequest(
    method: string,
    endpoint: string,
    statusCode: number,
    duration: number,
    context: Omit<BaseLogContext, 'action' | 'method' | 'endpoint' | 'duration'>
  ): void {
    const message = `${method} ${endpoint} ${statusCode} ${duration}ms`;
    this.info(message, {
      ...context,
      action: 'api_request',
      method,
      endpoint,
      duration,
      metadata: {
        statusCode,
        ...context.metadata
      }
    });
  }

  /**
   * Log API error
   */
  apiError(
    method: string,
    endpoint: string,
    error: Error,
    context: Omit<BaseLogContext, 'action' | 'method' | 'endpoint'>
  ): void {
    const message = `API Error: ${method} ${endpoint}`;
    this.error(message, error, {
      ...context,
      action: 'api_error',
      method,
      endpoint
    });
  }

  /**
   * Log authentication events
   */
  auth(
    event: 'login' | 'logout' | 'session_created' | 'session_expired' | 'access_denied',
    context: Omit<BaseLogContext, 'action'>
  ): void {
    const message = `Authentication: ${event}`;
    this.info(message, {
      ...context,
      action: `auth_${event}`
    });
  }

  /**
   * Log database operations
   */
  database(
    operation: 'create' | 'read' | 'update' | 'delete' | 'query',
    table: string,
    duration: number,
    context: Omit<BaseLogContext, 'action' | 'duration'>
  ): void {
    const message = `Database: ${operation} ${table} (${duration}ms)`;
    this.debug(message, {
      ...context,
      action: `db_${operation}`,
      duration,
      resource: table
    });
  }

  /**
   * Log business events
   */
  business(
    event: string,
    context: Omit<BaseLogContext, 'action'>
  ): void {
    const message = `Business Event: ${event}`;
    this.info(message, {
      ...context,
      action: `business_${event}`
    });
  }

  /**
   * Log security events
   */
  security(
    event: 'rate_limit_exceeded' | 'suspicious_activity' | 'permission_denied' | 'invalid_token',
    context: Omit<BaseLogContext, 'action'>
  ): void {
    const message = `Security Event: ${event}`;
    this.warn(message, {
      ...context,
      action: `security_${event}`
    });
  }

  /**
   * Log performance metrics
   */
  performance(metrics: PerformanceMetrics): void {
    const message = `Performance: ${metrics.method} ${metrics.endpoint} ${metrics.duration}ms`;
    this.info(message, {
      requestId: metrics.requestId,
      userId: metrics.userId,
      action: 'performance_metric',
      endpoint: metrics.endpoint,
      method: metrics.method,
      duration: metrics.duration,
      metadata: {
        statusCode: metrics.statusCode
      }
    });
  }

  /**
   * Log error metrics
   */
  errorMetric(metrics: ErrorMetrics): void {
    const message = `Error Metric: ${metrics.method} ${metrics.endpoint} ${metrics.errorCode}`;
    this.error(message, new Error(metrics.errorMessage), {
      requestId: metrics.requestId,
      userId: metrics.userId,
      action: 'error_metric',
      endpoint: metrics.endpoint,
      method: metrics.method,
      metadata: {
        errorCode: metrics.errorCode
      }
    });
  }
}

/**
 * Default logger instance
 */
export const logger = Logger.getInstance();

/**
 * Convenience functions using the default logger
 */
export const log = {
  debug: (message: string, context: BaseLogContext) => logger.debug(message, context),
  info: (message: string, context: BaseLogContext) => logger.info(message, context),
  warn: (message: string, context: BaseLogContext) => logger.warn(message, context),
  error: (message: string, error: Error, context: BaseLogContext) => logger.error(message, error, context),
  fatal: (message: string, error: Error, context: BaseLogContext) => logger.fatal(message, error, context),
  
  apiRequest: (method: string, endpoint: string, statusCode: number, duration: number, context: Omit<BaseLogContext, 'action' | 'method' | 'endpoint' | 'duration'>) => 
    logger.apiRequest(method, endpoint, statusCode, duration, context),
  
  apiError: (method: string, endpoint: string, error: Error, context: Omit<BaseLogContext, 'action' | 'method' | 'endpoint'>) => 
    logger.apiError(method, endpoint, error, context),
  
  auth: (event: 'login' | 'logout' | 'session_created' | 'session_expired' | 'access_denied', context: Omit<BaseLogContext, 'action'>) => 
    logger.auth(event, context),
  
  database: (operation: 'create' | 'read' | 'update' | 'delete' | 'query', table: string, duration: number, context: Omit<BaseLogContext, 'action' | 'duration'>) => 
    logger.database(operation, table, duration, context),
  
  business: (event: string, context: Omit<BaseLogContext, 'action'>) => 
    logger.business(event, context),
  
  security: (event: 'rate_limit_exceeded' | 'suspicious_activity' | 'permission_denied' | 'invalid_token', context: Omit<BaseLogContext, 'action'>) => 
    logger.security(event, context),
  
  performance: (metrics: PerformanceMetrics) => logger.performance(metrics),
  
  errorMetric: (metrics: ErrorMetrics) => logger.errorMetric(metrics)
};
