/**
 * Unified Test Utilities
 *
 * This module provides a centralized set of test utilities for the application.
 * It combines functionality from various test scripts and provides a consistent
 * interface for running tests from the admin interface.
 */

// Test types
export enum TestType {
  S3_CONNECTION = 's3_connection',
  S3_UPLOAD = 's3_upload',
  DATABASE = 'database',
}

// Test result interface
export interface TestResult {
  success: boolean;
  message: string;
  details?: any;
  duration?: number;
}

// S3 configuration interface
export interface S3Config {
  region: string;
  endpoint: string;
  bucketName: string;
  accessKeyId: string;
  secretAccessKey: string;
}

/**
 * Get S3 configuration from environment variables (client-safe)
 */
export function getS3ConfigFromEnv(): S3Config {
  return {
    region: process.env.NEXT_PUBLIC_S3_REGION || '',
    endpoint: process.env.NEXT_PUBLIC_S3_ENDPOINT || '',
    bucketName: process.env.NEXT_PUBLIC_S3_BUCKET || '',
    accessKeyId: process.env.NEXT_PUBLIC_S3_ACCESS_KEY || '',
    secretAccessKey: process.env.NEXT_PUBLIC_S3_SECRET_KEY || '',
  };
}

/**
 * Test descriptions for UI
 */
export const testDescriptions: Record<TestType, string> = {
  [TestType.S3_CONNECTION]: 'Test connection to S3 storage service',
  [TestType.S3_UPLOAD]: 'Test file upload functionality to S3',
  [TestType.DATABASE]: 'Test database connection and basic operations',
};

/**
 * Get available test types
 */
export function getAvailableTestTypes(): TestType[] {
  return Object.values(TestType);
}