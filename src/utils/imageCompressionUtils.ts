/**
 * Enhanced image compression utilities for better upload performance
 */

import imageCompression from 'browser-image-compression';
import { ConnectionSpeed } from './connectionUtils';

export interface CompressionOptions {
  maxSizeMB?: number;
  maxWidthOrHeight?: number;
  useWebWorker?: boolean;
  quality?: number;
  preserveExif?: boolean;
  onProgress?: (progress: number) => void;
}

export interface CompressionResult {
  compressedFile: File;
  originalSize: number;
  compressedSize: number;
  compressionRatio: number;
  timeTaken: number;
}

/**
 * Get compression settings based on connection speed and file characteristics
 */
export function getCompressionSettings(
  file: File,
  connectionSpeed: ConnectionSpeed
): CompressionOptions {
  const fileSizeMB = file.size / (1024 * 1024);
  
  // Base settings by connection speed
  const speedSettings = {
    'fast': {
      maxSizeMB: 5,
      maxWidthOrHeight: 2400,
      quality: 0.9,
      preserveExif: true
    },
    'slow': {
      maxSizeMB: 3,
      maxWidthOrHeight: 1920,
      quality: 0.8,
      preserveExif: false
    },
    'very-slow': {
      maxSizeMB: 1.5,
      maxWidthOrHeight: 1440,
      quality: 0.7,
      preserveExif: false
    }
  };
  
  const baseSettings = speedSettings[connectionSpeed];
  
  // Adjust based on file size
  if (fileSizeMB > 20) {
    // Very large files need aggressive compression
    baseSettings.maxSizeMB *= 0.5;
    baseSettings.maxWidthOrHeight = Math.min(baseSettings.maxWidthOrHeight, 1920);
    baseSettings.quality *= 0.9;
  } else if (fileSizeMB > 10) {
    // Large files need moderate compression
    baseSettings.maxSizeMB *= 0.7;
    baseSettings.quality *= 0.95;
  }
  
  // PNG files can be compressed more aggressively
  if (file.type === 'image/png') {
    baseSettings.maxSizeMB *= 0.8;
    baseSettings.quality *= 0.9;
  }
  
  return {
    ...baseSettings,
    useWebWorker: true, // Always use web worker for better performance
  };
}

/**
 * Compress image with adaptive settings
 */
export async function compressImageAdaptive(
  file: File,
  connectionSpeed: ConnectionSpeed,
  onProgress?: (progress: number) => void
): Promise<CompressionResult> {
  const startTime = performance.now();
  const originalSize = file.size;
  
  console.log(`[Compression] Starting compression for ${file.name} (${Math.round(originalSize/1024/1024)}MB) on ${connectionSpeed} connection`);
  
  // Get adaptive compression settings
  const settings = getCompressionSettings(file, connectionSpeed);
  
  try {
    const compressedFile = await imageCompression(file, {
      ...settings,
      onProgress: (progress: number) => {
        console.log(`[Compression] Progress: ${Math.round(progress)}%`);
        onProgress?.(progress);
      }
    });
    
    const timeTaken = performance.now() - startTime;
    const compressedSize = compressedFile.size;
    const compressionRatio = ((originalSize - compressedSize) / originalSize) * 100;
    
    console.log(`[Compression] Completed in ${Math.round(timeTaken)}ms`);
    console.log(`[Compression] Size: ${Math.round(originalSize/1024/1024)}MB → ${Math.round(compressedSize/1024/1024)}MB (${Math.round(compressionRatio)}% reduction)`);
    
    return {
      compressedFile,
      originalSize,
      compressedSize,
      compressionRatio,
      timeTaken
    };
  } catch (error) {
    console.error('[Compression] Failed:', error);
    // Return original file if compression fails
    return {
      compressedFile: file,
      originalSize,
      compressedSize: originalSize,
      compressionRatio: 0,
      timeTaken: performance.now() - startTime
    };
  }
}

/**
 * Determine if a file should be compressed
 */
export function shouldCompressFile(
  file: File,
  connectionSpeed: ConnectionSpeed,
  threshold: number = 2 * 1024 * 1024 // 2MB default threshold
): boolean {
  // Always compress for very slow connections
  if (connectionSpeed === 'very-slow') {
    return file.size > 500 * 1024; // 500KB threshold for very slow
  }
  
  // Compress large files for slow connections
  if (connectionSpeed === 'slow') {
    return file.size > 1024 * 1024; // 1MB threshold for slow
  }
  
  // Only compress very large files for fast connections
  return file.size > threshold;
}

/**
 * Smart compression that chooses the best approach
 */
export async function smartCompress(
  file: File,
  connectionSpeed: ConnectionSpeed,
  onProgress?: (stage: string, progress: number) => void
): Promise<File> {
  if (!shouldCompressFile(file, connectionSpeed)) {
    console.log(`[Compression] Skipping compression for ${file.name} (${Math.round(file.size/1024)}KB) on ${connectionSpeed} connection`);
    return file;
  }
  
  onProgress?.('Preparing compression...', 0);
  
  try {
    const result = await compressImageAdaptive(file, connectionSpeed, (progress) => {
      onProgress?.('Compressing image...', progress);
    });
    
    // Only use compressed version if it's significantly smaller
    const minReduction = connectionSpeed === 'very-slow' ? 10 : 20; // Minimum % reduction
    
    if (result.compressionRatio >= minReduction) {
      onProgress?.('Compression complete!', 100);
      return result.compressedFile;
    } else {
      console.log(`[Compression] Compression ratio too low (${Math.round(result.compressionRatio)}%), using original file`);
      onProgress?.('Using original file', 100);
      return file;
    }
  } catch (error) {
    console.error('[Compression] Smart compression failed:', error);
    onProgress?.('Compression failed, using original', 100);
    return file;
  }
}

/**
 * Batch compress multiple files
 */
export async function compressMultipleFiles(
  files: File[],
  connectionSpeed: ConnectionSpeed,
  onProgress?: (fileIndex: number, fileName: string, progress: number) => void
): Promise<File[]> {
  const compressedFiles: File[] = [];
  
  for (let i = 0; i < files.length; i++) {
    const file = files[i];
    
    try {
      const compressedFile = await smartCompress(
        file,
        connectionSpeed,
        (stage, progress) => {
          onProgress?.(i, file.name, progress);
        }
      );
      
      compressedFiles.push(compressedFile);
    } catch (error) {
      console.error(`[Compression] Failed to compress ${file.name}:`, error);
      compressedFiles.push(file); // Use original if compression fails
    }
  }
  
  return compressedFiles;
}

/**
 * Get compression statistics for reporting
 */
export function getCompressionStats(results: CompressionResult[]): {
  totalOriginalSize: number;
  totalCompressedSize: number;
  averageCompressionRatio: number;
  totalTimeSaved: number;
  filesCompressed: number;
} {
  const totalOriginalSize = results.reduce((sum, r) => sum + r.originalSize, 0);
  const totalCompressedSize = results.reduce((sum, r) => sum + r.compressedSize, 0);
  const averageCompressionRatio = results.length > 0 
    ? results.reduce((sum, r) => sum + r.compressionRatio, 0) / results.length 
    : 0;
  const filesCompressed = results.filter(r => r.compressionRatio > 0).length;
  
  // Estimate time saved based on size reduction (rough estimate: 1MB = 10s on slow connection)
  const sizeSavedMB = (totalOriginalSize - totalCompressedSize) / (1024 * 1024);
  const totalTimeSaved = sizeSavedMB * 10; // seconds
  
  return {
    totalOriginalSize,
    totalCompressedSize,
    averageCompressionRatio,
    totalTimeSaved,
    filesCompressed
  };
} 