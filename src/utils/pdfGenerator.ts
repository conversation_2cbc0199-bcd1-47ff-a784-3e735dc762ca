/**
 * Main PDF generator facade - now using modular generators
 * Maintains backward compatibility while using the new modular architecture
 */

// Import modular generators
import {
  generateReceiptPDF as generateReceiptPDFModular,
  generateInvoicePDF as generateInvoicePDFModular,
  generateQuotePDF as generateQuotePDFModular
} from './pdf/generators';

// Import and re-export types for backward compatibility
import {
  type PaperSize,
  type ItemData,
  type ReceiptData,
  type InvoiceData,
  type QuoteData,
  type ReceiptItemData,
  PAPER_DIMENSIONS
} from './pdf/types';

// Re-export types for backward compatibility
export type {
  PaperSize,
  ItemData,
  ReceiptData,
  InvoiceData,
  QuoteData,
  ReceiptItemData
};

// Re-export paper dimensions for backward compatibility
export { PAPER_DIMENSIONS };

/**
 * Generate a PDF receipt
 * @param receiptData The receipt data
 * @returns The path to the generated PDF file
 */
export async function generateReceiptPDF(receiptData: ReceiptData): Promise<string> {
  console.log('Using modular receipt PDF generator');
  return generateReceiptPDFModular(receiptData);
}

/**
 * Generate a PDF invoice
 * @param invoiceData The invoice data
 * @returns The path to the generated PDF file
 */
export async function generateInvoicePDF(invoiceData: InvoiceData): Promise<string> {
  console.log('Using modular invoice PDF generator');
  return generateInvoicePDFModular(invoiceData);
}

/**
 * Generate a PDF quote
 * @param quoteData The quote data
 * @returns The path to the generated PDF file
 */
export async function generateQuotePDF(quoteData: QuoteData): Promise<string> {
  console.log('Using modular quote PDF generator');
  return generateQuotePDFModular(quoteData);
}
