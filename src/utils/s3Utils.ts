import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import { NodeHttpHandler } from '@smithy/node-http-handler';
import { getDefaultStorageConfig } from '@/lib/storageConfig';
import { normalizeImageUrl } from '@/utils/imageUtils';
import { logger } from '@/utils/logger';

// Get S3 client with the current configuration
async function getS3Client() {
  try {
    const config = await getDefaultStorageConfig();

    if (!config) {
      console.error('No storage configuration found, checking environment variables directly');

      // Direct fallback to environment variables
      const envBucket = process.env.NEXT_PUBLIC_S3_BUCKET;
      const envAccessKey = process.env.NEXT_PUBLIC_S3_ACCESS_KEY;
      const envSecretKey = process.env.NEXT_PUBLIC_S3_SECRET_KEY;
      const envEndpoint = process.env.NEXT_PUBLIC_S3_ENDPOINT;

      if (!envBucket || !envAccessKey || !envSecretKey || !envEndpoint) {
        const missingVars = [];
        if (!envBucket) missingVars.push('NEXT_PUBLIC_S3_BUCKET');
        if (!envAccessKey) missingVars.push('NEXT_PUBLIC_S3_ACCESS_KEY');
        if (!envSecretKey) missingVars.push('NEXT_PUBLIC_S3_SECRET_KEY');
        if (!envEndpoint) missingVars.push('NEXT_PUBLIC_S3_ENDPOINT');

        throw new Error(`Missing required environment variables: ${missingVars.join(', ')}`);
      }

      console.log('Using environment variables for S3 configuration');
      return new S3Client({
        region: process.env.NEXT_PUBLIC_S3_REGION || 'fr-par-1',
        endpoint: envEndpoint,
        credentials: {
          accessKeyId: envAccessKey,
          secretAccessKey: envSecretKey,
        },
        forcePathStyle: true, // Required for some S3-compatible storage
        maxAttempts: 3, // Retry failed requests up to 3 times
        retryMode: 'standard',
        requestHandler: new (require('@smithy/node-http-handler').NodeHttpHandler)({
          connectionTimeout: 10000, // 10 seconds connection timeout
          socketTimeout: 60000,    // 60 seconds socket timeout
        })
      });
    }

    console.log(`Creating S3 client with region: ${config.region}, endpoint: ${config.endpoint}`);

    // Validate config values
    if (!config.accessKey || !config.secretKey || !config.endpoint) {
      throw new Error('Storage configuration is incomplete - missing required fields');
    }

    // Add more detailed configuration with better error handling
    return new S3Client({
      region: config.region,
      endpoint: config.endpoint,
      credentials: {
        accessKeyId: config.accessKey,
        secretAccessKey: config.secretKey,
      },
      forcePathStyle: true, // Required for some S3-compatible storage
      maxAttempts: 3, // Retry failed requests up to 3 times
      retryMode: 'standard',
      requestHandler: new (require('@smithy/node-http-handler').NodeHttpHandler)({
        connectionTimeout: 10000, // 10 seconds connection timeout
        socketTimeout: 60000,    // 60 seconds socket timeout
      })
    });
  } catch (error) {
    console.error('Error creating S3 client:', error);
    throw new Error('Failed to initialize S3 client: ' + (error instanceof Error ? error.message : 'Unknown error'));
  }
}

// Get the current bucket name
async function getBucketName(): Promise<string> {
  const config = await getDefaultStorageConfig();
  return config?.bucketName || process.env.NEXT_PUBLIC_S3_BUCKET || 'mocky';
}

/**
 * Upload an image to S3 with enhanced error handling and retry logic
 * @param file The file to upload
 * @param key The S3 key to use
 * @param options Optional configuration for the upload
 * @returns Object with success status, URL, and optional error message
 */
export async function uploadImageToS3(
  file: File,
  key: string,
  options: {
    maxRetries?: number;
    timeoutMs?: number;
    logPrefix?: string;
  } = {}
): Promise<{
  success: boolean;
  url: string;
  error?: string;
  warnings?: string[];
}> {
  // Default options
  const {
    maxRetries = 7, // Increased from 5 to 7 for better reliability
    timeoutMs = 600000, // 600 seconds (10 minutes) - increased from 300s
    logPrefix = '[S3Upload]'
  } = options;

  // Adjust timeout based on file size (larger files get more time)
  const adjustedTimeoutMs = file.size > 20 * 1024 * 1024
    ? timeoutMs * 3  // Triple timeout for files > 20MB
    : file.size > 10 * 1024 * 1024
      ? timeoutMs * 2 // Double timeout for files > 10MB
      : file.size > 5 * 1024 * 1024
        ? timeoutMs * 1.5 // 1.5x timeout for files > 5MB
        : timeoutMs;

  // Initialize warnings array to collect non-fatal issues
  const warnings: string[] = [];

  // Create correlation ID for logging
  const correlationId = `s3-upload-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  const category = key.split('/')[0] || 'unknown';

  // Helper function for logging that also collects warnings
  const log = {
    info: (message: string) => {
      console.log(`${logPrefix} ${message}`);
      logger.info(`${logPrefix} ${message}`, {
        requestId: correlationId,
        action: 's3_upload',
        resource: category,
        metadata: { uploadKey: key }
      });
    },
    warn: (message: string) => {
      console.warn(`${logPrefix} ${message}`);
      logger.warn(`${logPrefix} ${message}`, {
        requestId: correlationId,
        action: 's3_upload_warning',
        resource: category,
        metadata: { uploadKey: key }
      });
      warnings.push(message);
    },
    error: (message: string, error?: any) => {
      console.error(`${logPrefix} ${message}`, error);
      logger.error(`${logPrefix} ${message}`, error instanceof Error ? error : new Error(String(error)), {
        requestId: correlationId,
        action: 's3_upload_error',
        resource: category,
        metadata: { uploadKey: key }
      });
    }
  };

  // Track if this is an AI-generated image (based on filename or path)
  const isAIImage = key.includes('ai-') || key.includes('/ai/') || file.name.includes('ai-');

  try {
    log.info(`Attempting to upload image to S3: ${key}`);
    log.info(`Image details: name=${file.name}, type=${file.type}, size=${file.size} bytes`);

    // Get S3 configuration
    const config = await getDefaultStorageConfig();
    if (!config) {
      console.error('No storage configuration found');
      return {
        success: false,
        url: '',
        error: 'Storage configuration not available. Please check your S3 settings.'
      };
    }

    // Validate configuration
    if (!config.bucketName || !config.accessKey || !config.secretKey || !config.endpoint) {
      log.error('Storage configuration is incomplete');
      return {
        success: false,
        url: '',
        error: 'Storage configuration is incomplete. Please check your S3 settings.'
      };
    }

    console.log(`Using S3 config: region=${config.region}, endpoint=${config.endpoint}, bucket=${config.bucketName}`);

    const s3Client = new S3Client({
      region: config.region,
      endpoint: config.endpoint,
      credentials: {
        accessKeyId: config.accessKey,
        secretAccessKey: config.secretKey,
      },
      forcePathStyle: true, // Required for some S3-compatible storage
      maxAttempts: 5, // Increased from 3 to 5
      retryMode: 'standard',
      requestHandler: new (require('@smithy/node-http-handler').NodeHttpHandler)({
        connectionTimeout: 60000, // 60 seconds connection timeout (increased from 30s)
        socketTimeout: 720000,    // 12 minutes socket timeout (increased from 6 minutes)
      })
    });

    const bucketName = config.bucketName;

    log.info(`Using S3 bucket: ${bucketName}, endpoint: ${config.endpoint}, region: ${config.region}`);

    // Convert File to Buffer
    let buffer: Buffer;
    try {
      const arrayBuffer = await file.arrayBuffer();
      buffer = Buffer.from(arrayBuffer);
      log.info(`Converted file to buffer of size: ${buffer.length} bytes`);
    } catch (bufferError) {
      log.error('Failed to convert file to buffer', bufferError);
      return {
        success: false,
        url: '',
        error: 'Failed to process image file'
      };
    }

    // Determine content type
    const contentType = file.type || 'application/octet-stream';

    // Validate file size
    const MAX_FILE_SIZE = 100 * 1024 * 1024; // 100MB (increased from 50MB)
    if (buffer.length > MAX_FILE_SIZE) {
      log.error(`File size exceeds maximum allowed (${buffer.length} > ${MAX_FILE_SIZE} bytes)`);
      return {
        success: false,
        url: '',
        error: `Image file is too large (${Math.round(buffer.length / 1024 / 1024)}MB). Maximum size is 100MB.`
      };
    }

    // Log warning for large files that might cause timeouts
    if (buffer.length > 5 * 1024 * 1024) {
      log.warn(`Large file detected (${Math.round(buffer.length / 1024 / 1024)}MB). Upload may take longer than usual.`);
    }

    // Validate file type
    const ALLOWED_TYPES = ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/avif', 'image/svg+xml'];
    if (!ALLOWED_TYPES.includes(contentType)) {
      log.warn(`File type ${contentType} is not in the allowed list. Proceeding anyway but may cause issues.`);
    }

    // Create upload command
    const createUploadCommand = (uploadKey: string) => new PutObjectCommand({
      Bucket: bucketName,
      Key: uploadKey,
      Body: buffer,
      ContentType: contentType,
      ACL: 'public-read',
      Metadata: {
        originalname: file.name,
        uploadDate: new Date().toISOString(),
      },
    });

    // Sanitize filename for S3 key
    const sanitizeFilename = (name: string) => {
      // Remove any path components
      const baseName = name.split(/[\\/]/).pop() || 'unnamed';
      // Replace any non-alphanumeric chars except for safe ones
      return baseName
        .replace(/[^a-zA-Z0-9.-]/g, '_')
        .replace(/_{2,}/g, '_') // Replace multiple underscores with single
        .replace(/^[.-_]+|[.-_]+$/g, ''); // Trim special chars from start/end
    };

    // Implement retry logic
    let lastError: any = null;

    // Sanitize the initial key if it contains the filename
    let uploadKey = key;
    if (key.includes(file.name)) {
      const sanitizedName = sanitizeFilename(file.name);
      uploadKey = key.replace(file.name, sanitizedName);
      if (uploadKey !== key) {
        log.info(`Sanitized original key from ${key} to ${uploadKey}`);
      }
    }

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        log.info(`Upload attempt ${attempt}/${maxRetries} for key: ${uploadKey}`);

        // Log the timeout value being used
        log.info(`Using timeout of ${adjustedTimeoutMs/1000} seconds for this upload attempt`);

        // Create a timeout promise with adjusted timeout based on file size
        const uploadPromise = s3Client.send(createUploadCommand(uploadKey));
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error(`S3 upload timeout after ${adjustedTimeoutMs/1000} seconds`)), adjustedTimeoutMs);
        });

        // Race the upload against the timeout
        await Promise.race([uploadPromise, timeoutPromise]);

        log.info(`Upload successful on attempt ${attempt}`);

        // Generate public URL
        // Ensure the endpoint has the correct protocol
        let endpoint = config.endpoint;
        if (!endpoint.startsWith('http')) {
          endpoint = `https://${endpoint}`;
        }

        // Remove any trailing slashes from endpoint
        endpoint = endpoint.replace(/\/+$/, '');

        // Construct the URL with proper formatting
        const url = `${endpoint}/${bucketName}/${uploadKey}`;
        log.info(`Image URL: ${url}`);

        return {
          success: true,
          url: normalizeImageUrl(url),
          warnings: warnings.length > 0 ? warnings : undefined
        };
      } catch (uploadError: any) {
        lastError = uploadError;
        log.error(`Attempt ${attempt} failed:`, uploadError);

        // If this is the last attempt, don't modify the key
        if (attempt === maxRetries) break;

        // For subsequent attempts, try with a simplified key
        const sanitizedName = sanitizeFilename(file.name);

        // Extract the category from the original key (e.g., "images/pricing/filename.jpg" -> "pricing")
        const pathParts = key.split('/');
        const category = pathParts.length > 1 ? pathParts[1] : 'pricing';

        // For pricing products, ensure we're using the correct path
        if (attempt === 1 && uploadError.message.includes('timeout')) {
          // Keep the same category but use a simplified filename with timestamp
          uploadKey = `images/${category}/${Date.now()}_${sanitizedName}`;
          log.info(`Retrying with simplified key: ${uploadKey}`);
        } else {
          // Add timestamp to make the key unique for each retry
          uploadKey = `images/${category}/${Date.now()}_retry${attempt}_${sanitizedName}`;
          log.info(`Retrying with unique key: ${uploadKey}`);
        }

        // Log the category we're using for debugging
        log.info(`Using category '${category}' for retry attempt ${attempt}`);

        // Wait before retrying (exponential backoff)
        const backoffMs = Math.min(1000 * Math.pow(2, attempt - 1), 10000);
        log.info(`Waiting ${backoffMs}ms before next attempt`);
        await new Promise(resolve => setTimeout(resolve, backoffMs));
      }
    }

    // All attempts failed
    log.error(`All ${maxRetries} upload attempts failed`);

    // Construct a clear error message
    let errorMessage = 'Failed to upload image after multiple attempts.';

    // Add more specific error details if available
    if (lastError instanceof Error) {
      // Clean up any AI-specific error messages that might be confusing
      const errorMsg = lastError.message
        .replace('AI image uploads failed', 'Image upload failed')
        .replace('Please check your commission privacy again', 'Please try again');

      errorMessage = `${errorMessage} Error: ${errorMsg}`;
    }

    // Return error for all environments
    return {
      success: false,
      url: '',
      error: errorMessage,
      warnings: warnings.length > 0 ? warnings : undefined
    };
  } catch (error) {
    log.error('Unexpected error during upload process:', error);

    // Construct a clear error message
    let errorMessage = 'An unexpected error occurred during image upload.';

    if (error instanceof Error) {
      // Clean up any AI-specific error messages that might be confusing
      const errorMsg = error.message
        .replace('AI image uploads failed', 'Image upload failed')
        .replace('Please check your commission privacy again', 'Please try again');

      errorMessage = `${errorMessage} Error: ${errorMsg}`;
    }

    // Return error for all environments
    return {
      success: false,
      url: '',
      error: errorMessage,
      warnings: warnings.length > 0 ? warnings : undefined
    };
  }
}
