/**
 * Safe Array Utilities
 * Prevents runtime errors from undefined/null arrays
 */

/**
 * Safely map over an array, handling null/undefined cases
 */
export function safeMap<T, R>(
  array: T[] | undefined | null,
  fn: (item: T, index: number, array: T[]) => R
): R[] {
  return (array || []).map(fn);
}

/**
 * Safely filter an array, handling null/undefined cases
 */
export function safeFilter<T>(
  array: T[] | undefined | null,
  fn: (item: T, index: number, array: T[]) => boolean
): T[] {
  return (array || []).filter(fn);
}

/**
 * Safely reduce an array, handling null/undefined cases
 */
export function safeReduce<T, R>(
  array: T[] | undefined | null,
  fn: (acc: R, item: T, index: number, array: T[]) => R,
  initialValue: R
): R {
  return (array || []).reduce(fn, initialValue);
}

/**
 * Safely check array length
 */
export function safeLength(array: any[] | undefined | null): number {
  return (array || []).length;
}

/**
 * Safely get array item by index
 */
export function safeGet<T>(array: T[] | undefined | null, index: number): T | undefined {
  return (array || [])[index];
}

/**
 * Check if array exists and has items
 */
export function hasItems(array: any[] | undefined | null): boolean {
  return Array.isArray(array) && array.length > 0;
} 