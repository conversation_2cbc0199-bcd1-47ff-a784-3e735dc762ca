/**
 * Activity Logger Utility
 * Simple logging utility for enterprise testing
 */

export interface ActivityLogData {
  userId: string
  action: string
  details: any
  ipAddress?: string
  userAgent?: string
  resourceType?: string
  resourceId?: string
}

export const logActivity = async (
  userId: string,
  action: string,
  details: any,
  ipAddress?: string,
  userAgent?: string,
  resourceType?: string,
  resourceId?: string
): Promise<void> => {
  // Simple implementation for testing purposes
  console.log('Activity logged:', {
    userId,
    action,
    details,
    ipAddress,
    userAgent,
    resourceType,
    resourceId,
    timestamp: new Date().toISOString(),
  })
} 