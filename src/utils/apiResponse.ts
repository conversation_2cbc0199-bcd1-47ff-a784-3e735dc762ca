import { NextResponse } from 'next/server';
import { APIResponse, PaginationMeta, APIErrorCode, API_ERROR_CODES } from '@/types/api';
import { APIError, ErrorFactory } from '@/errors/ApiErrors';
import crypto from 'crypto';

/**
 * Generate a unique request ID
 */
export function generateRequestId(): string {
  return crypto.randomUUID();
}

/**
 * Get the current API version
 */
function getAPIVersion(): string {
  return process.env.API_VERSION || '1.0.0';
}

/**
 * Create a successful API response
 */
export function successResponse<T>(
  data: T,
  meta: Partial<APIResponse['meta']> = {},
  statusCode: number = 200
): NextResponse {
  const response: APIResponse<T> = {
    success: true,
    data,
    meta: {
      timestamp: new Date().toISOString(),
      requestId: generateRequestId(),
      version: getAPIVersion(),
      ...meta
    }
  };

  return NextResponse.json(response, { 
    status: statusCode,
    headers: {
      'X-Request-ID': response.meta.requestId,
      'X-API-Version': response.meta.version!
    }
  });
}

/**
 * Create a paginated success response
 */
export function paginatedResponse<T>(
  data: T[],
  pagination: PaginationMeta,
  meta: Partial<APIResponse['meta']> = {},
  statusCode: number = 200
): NextResponse {
  const response: APIResponse<T[]> = {
    success: true,
    data,
    meta: {
      timestamp: new Date().toISOString(),
      requestId: generateRequestId(),
      version: getAPIVersion(),
      pagination,
      ...meta
    }
  };

  return NextResponse.json(response, { 
    status: statusCode,
    headers: {
      'X-Request-ID': response.meta.requestId,
      'X-API-Version': response.meta.version!,
      'X-Total-Count': pagination.total.toString(),
      'X-Page': pagination.page.toString(),
      'X-Per-Page': pagination.limit.toString()
    }
  });
}

/**
 * Create an error API response
 */
export function errorResponse(
  code: APIErrorCode,
  message: string,
  statusCode: number = 500,
  field?: string,
  details?: any,
  requestId?: string
): NextResponse {
  const response: APIResponse = {
    success: false,
    error: {
      code,
      message,
      field,
      details
    },
    meta: {
      timestamp: new Date().toISOString(),
      requestId: requestId || generateRequestId(),
      version: getAPIVersion()
    }
  };

  return NextResponse.json(response, { 
    status: statusCode,
    headers: {
      'X-Request-ID': response.meta.requestId,
      'X-API-Version': response.meta.version!
    }
  });
}

/**
 * Create an error response from an APIError instance
 */
export function errorResponseFromAPIError(error: APIError): NextResponse {
  const response: APIResponse = {
    success: false,
    error: {
      code: error.code,
      message: error.message,
      field: error.field,
      details: error.details
    },
    meta: {
      timestamp: new Date().toISOString(),
      requestId: error.requestId || generateRequestId(),
      version: getAPIVersion()
    }
  };

  return NextResponse.json(response, { 
    status: error.statusCode,
    headers: {
      'X-Request-ID': response.meta.requestId,
      'X-API-Version': response.meta.version!
    }
  });
}

/**
 * Handle any error and return appropriate response
 */
export function handleAPIError(error: unknown, requestId?: string): NextResponse {
  // Log the error for monitoring
  console.error('API Error:', {
    error,
    requestId,
    timestamp: new Date().toISOString(),
    stack: error instanceof Error ? error.stack : undefined
  });

  // Convert to APIError if not already
  const apiError = error instanceof APIError 
    ? error 
    : ErrorFactory.fromUnknownError(error, requestId);

  return errorResponseFromAPIError(apiError);
}

/**
 * Common error responses for quick use
 */
export const CommonResponses = {
  unauthorized: (requestId?: string) => 
    errorResponse(API_ERROR_CODES.UNAUTHORIZED, 'Authentication required', 401, undefined, undefined, requestId),
  
  forbidden: (requestId?: string) => 
    errorResponse(API_ERROR_CODES.FORBIDDEN, 'Insufficient permissions', 403, undefined, undefined, requestId),
  
  notFound: (resource: string, requestId?: string) => 
    errorResponse(API_ERROR_CODES.NOT_FOUND, `${resource} not found`, 404, undefined, undefined, requestId),
  
  validationError: (message: string, field?: string, requestId?: string) => 
    errorResponse(API_ERROR_CODES.VALIDATION_ERROR, message, 400, field, undefined, requestId),
  
  rateLimitExceeded: (retryAfter?: number, requestId?: string) => 
    errorResponse(
      API_ERROR_CODES.RATE_LIMIT_EXCEEDED, 
      'Too many requests', 
      429, 
      undefined, 
      { retryAfter }, 
      requestId
    ),
  
  internalError: (requestId?: string) => 
    errorResponse(API_ERROR_CODES.INTERNAL_ERROR, 'An unexpected error occurred', 500, undefined, undefined, requestId),
  
  serviceUnavailable: (service?: string, requestId?: string) => 
    errorResponse(
      API_ERROR_CODES.SERVICE_UNAVAILABLE, 
      service ? `Service '${service}' is unavailable` : 'Service is unavailable', 
      503, 
      undefined, 
      { service }, 
      requestId
    )
};

/**
 * Response headers for different types of responses
 */
export const ResponseHeaders = {
  json: {
    'Content-Type': 'application/json',
  },
  
  noCache: {
    'Cache-Control': 'no-cache, no-store, must-revalidate',
    'Pragma': 'no-cache',
    'Expires': '0'
  },
  
  cache: (maxAge: number) => ({
    'Cache-Control': `public, max-age=${maxAge}`,
  }),
  
  cors: {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-CSRF-Token',
  }
};

/**
 * Utility to calculate pagination metadata
 */
export function calculatePagination(
  totalItems: number,
  page: number = 1,
  limit: number = 20
): PaginationMeta {
  const totalPages = Math.ceil(totalItems / limit);
  
  return {
    page: Math.max(1, page),
    limit: Math.max(1, limit),
    total: totalItems,
    totalPages,
    hasNextPage: page < totalPages,
    hasPreviousPage: page > 1
  };
}

/**
 * Validate pagination parameters
 */
export function validatePaginationParams(
  page?: string | number,
  limit?: string | number
): { page: number; limit: number } {
  const parsedPage = typeof page === 'string' ? parseInt(page, 10) : (page || 1);
  const parsedLimit = typeof limit === 'string' ? parseInt(limit, 10) : (limit || 20);
  
  return {
    page: Math.max(1, isNaN(parsedPage) ? 1 : parsedPage),
    limit: Math.max(1, Math.min(100, isNaN(parsedLimit) ? 20 : parsedLimit)) // Max 100 items per page
  };
}

/**
 * Extract common query parameters from request
 */
export function extractCommonParams(url: URL) {
  const searchParams = url.searchParams;
  
  return {
    search: searchParams.get('search') || '',
    sortBy: searchParams.get('sortBy') || 'createdAt',
    sortOrder: (searchParams.get('sortOrder') as 'asc' | 'desc') || 'desc',
    ...validatePaginationParams(
      searchParams.get('page') || undefined,
      searchParams.get('limit') || undefined
    )
  };
} 