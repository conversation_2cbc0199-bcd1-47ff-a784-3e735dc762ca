import { Decimal } from 'decimal.js';

/**
 * Financial Calculator for precise decimal arithmetic
 * Prevents floating-point precision errors in financial calculations
 */
export class FinancialCalculator {
  /**
   * Calculate total amount from array of items
   * @param items Array of items with unitPrice and quantity
   * @returns Precise total amount
   */
  static calculateTotal(items: Array<{unitPrice: number, quantity: number}>): number {
    return items.reduce((total, item) => {
      const itemTotal = new Decimal(item.unitPrice).mul(item.quantity);
      return new Decimal(total).add(itemTotal).toNumber();
    }, 0);
  }

  /**
   * Calculate item total (unitPrice * quantity)
   * @param unitPrice Unit price
   * @param quantity Quantity
   * @returns Precise item total
   */
  static calculateItemTotal(unitPrice: number, quantity: number): number {
    return new Decimal(unitPrice).mul(quantity).toNumber();
  }

  /**
   * Calculate balance (totalAmount - amountPaid)
   * @param totalAmount Total amount
   * @param amountPaid Amount paid
   * @returns Precise balance
   */
  static calculateBalance(totalAmount: number, amountPaid: number): number {
    return new Decimal(totalAmount).sub(amountPaid).toNumber();
  }

  /**
   * Add two amounts with precision
   * @param amount1 First amount
   * @param amount2 Second amount
   * @returns Precise sum
   */
  static add(amount1: number, amount2: number): number {
    return new Decimal(amount1).add(amount2).toNumber();
  }

  /**
   * Subtract two amounts with precision
   * @param amount1 First amount
   * @param amount2 Second amount
   * @returns Precise difference
   */
  static subtract(amount1: number, amount2: number): number {
    return new Decimal(amount1).sub(amount2).toNumber();
  }

  /**
   * Multiply two amounts with precision
   * @param amount1 First amount
   * @param amount2 Second amount
   * @returns Precise product
   */
  static multiply(amount1: number, amount2: number): number {
    return new Decimal(amount1).mul(amount2).toNumber();
  }

  /**
   * Divide two amounts with precision
   * @param amount1 Dividend
   * @param amount2 Divisor
   * @returns Precise quotient
   */
  static divide(amount1: number, amount2: number): number {
    if (amount2 === 0) {
      throw new Error('Division by zero is not allowed');
    }
    return new Decimal(amount1).div(amount2).toNumber();
  }

  /**
   * Round currency to 2 decimal places
   * @param amount Amount to round
   * @returns Rounded amount
   */
  static roundCurrency(amount: number): number {
    return new Decimal(amount).toDecimalPlaces(2).toNumber();
  }

  /**
   * Format currency for display
   * @param amount Amount to format
   * @param currency Currency symbol (default: KES)
   * @returns Formatted currency string
   */
  static formatCurrency(amount: number, currency: string = 'KES'): string {
    const rounded = this.roundCurrency(amount);
    return `${currency} ${rounded.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
  }

  /**
   * Calculate percentage
   * @param amount Base amount
   * @param percentage Percentage (e.g., 10 for 10%)
   * @returns Calculated percentage amount
   */
  static calculatePercentage(amount: number, percentage: number): number {
    return new Decimal(amount).mul(percentage).div(100).toNumber();
  }

  /**
   * Calculate discount
   * @param amount Original amount
   * @param discountPercentage Discount percentage
   * @returns Amount after discount
   */
  static applyDiscount(amount: number, discountPercentage: number): number {
    const discountAmount = this.calculatePercentage(amount, discountPercentage);
    return this.subtract(amount, discountAmount);
  }

  /**
   * Calculate tax
   * @param amount Base amount
   * @param taxPercentage Tax percentage
   * @returns Amount including tax
   */
  static applyTax(amount: number, taxPercentage: number): number {
    const taxAmount = this.calculatePercentage(amount, taxPercentage);
    return this.add(amount, taxAmount);
  }

  /**
   * Validate amount is positive
   * @param amount Amount to validate
   * @param fieldName Field name for error messages
   * @throws Error if amount is not positive
   */
  static validatePositiveAmount(amount: number, fieldName: string = 'Amount'): void {
    if (isNaN(amount) || amount < 0) {
      throw new Error(`${fieldName} must be a positive number`);
    }
  }

  /**
   * Validate amount is not zero
   * @param amount Amount to validate
   * @param fieldName Field name for error messages
   * @throws Error if amount is zero
   */
  static validateNonZeroAmount(amount: number, fieldName: string = 'Amount'): void {
    this.validatePositiveAmount(amount, fieldName);
    if (amount === 0) {
      throw new Error(`${fieldName} must be greater than zero`);
    }
  }

  /**
   * Compare two amounts for equality (with precision tolerance)
   * @param amount1 First amount
   * @param amount2 Second amount
   * @param tolerance Tolerance for comparison (default: 0.01)
   * @returns True if amounts are equal within tolerance
   */
  static areEqual(amount1: number, amount2: number, tolerance: number = 0.01): boolean {
    return Math.abs(amount1 - amount2) < tolerance;
  }

  /**
   * Check if amount is paid in full
   * @param totalAmount Total amount due
   * @param amountPaid Amount paid
   * @param tolerance Tolerance for comparison (default: 0.01)
   * @returns True if paid in full
   */
  static isPaidInFull(totalAmount: number, amountPaid: number, tolerance: number = 0.01): boolean {
    return this.areEqual(totalAmount, amountPaid, tolerance) || amountPaid >= totalAmount;
  }

  /**
   * Calculate payment status
   * @param totalAmount Total amount
   * @param amountPaid Amount paid
   * @returns Payment status string
   */
  static getPaymentStatus(totalAmount: number, amountPaid: number): string {
    if (this.isPaidInFull(totalAmount, amountPaid)) {
      return 'paid';
    } else if (amountPaid > 0) {
      return 'partial';
    } else {
      return 'pending';
    }
  }
} 