import { NextRequest } from 'next/server';

/**
 * Extract client IP address from Next.js request
 * Handles various proxy headers and edge cases
 */
export function getClientIP(request: NextRequest): string {
  // Try different headers in order of preference
  const headers = [
    'x-forwarded-for',
    'x-real-ip',
    'x-client-ip',
    'cf-connecting-ip', // Cloudflare
    'x-forwarded',
    'forwarded-for',
    'forwarded'
  ];

  for (const header of headers) {
    const value = request.headers.get(header);
    if (value) {
      // Handle comma-separated IPs (x-forwarded-for can contain multiple IPs)
      const ips = value.split(',').map(ip => ip.trim());
      
      // Return the first valid IP
      for (const ip of ips) {
        if (isValidIP(ip)) {
          return ip;
        }
      }
    }
  }

  // Fallback to unknown if no valid IP found
  return 'unknown';
}

/**
 * Basic IP validation
 */
function isValidIP(ip: string): boolean {
  if (!ip || ip === 'unknown') {
    return false;
  }

  // Remove any port numbers
  const cleanIP = ip.split(':')[0];

  // Basic IPv4 regex
  const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
  
  // Basic IPv6 regex (simplified)
  const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;

  return ipv4Regex.test(cleanIP) || ipv6Regex.test(cleanIP);
} 