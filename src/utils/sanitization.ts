/**
 * Input Sanitization Utilities
 * Prevents XSS, SQL injection, and other injection attacks
 */

/**
 * Sanitize text input to prevent XSS
 */
export function sanitizeText(input: string | null | undefined): string {
  if (!input) return '';
  
  return input
    .trim()
    .replace(/[<>]/g, '') // Remove angle brackets
    .replace(/javascript:/gi, '') // Remove javascript: protocols
    .replace(/on\w+=/gi, '') // Remove event handlers
    .slice(0, 1000); // Limit length
}

/**
 * Sanitize email input
 */
export function sanitizeEmail(input: string | null | undefined): string {
  if (!input) return '';
  
  const email = input.trim().toLowerCase();
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  
  if (!emailRegex.test(email)) {
    throw new Error('Invalid email format');
  }
  
  return email.slice(0, 254); // RFC 5321 limit
}

/**
 * Sanitize phone number
 */
export function sanitizePhone(input: string | null | undefined): string {
  if (!input) return '';
  
  // Remove all non-digit characters except + and spaces
  return input
    .replace(/[^\d\+\s\-\(\)]/g, '')
    .trim()
    .slice(0, 20);
}

/**
 * Sanitize general text with HTML encoding
 */
export function sanitizeHTML(input: string | null | undefined): string {
  if (!input) return '';
  
  return input
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/\//g, '&#x2F;')
    .trim()
    .slice(0, 5000);
}

/**
 * Validate and sanitize URL
 */
export function sanitizeURL(input: string | null | undefined): string {
  if (!input) return '';
  
  try {
    const url = new URL(input);
    // Only allow http and https protocols
    if (!['http:', 'https:'].includes(url.protocol)) {
      throw new Error('Invalid URL protocol');
    }
    return url.toString();
  } catch {
    throw new Error('Invalid URL format');
  }
}

/**
 * Validate required fields
 */
export function validateRequired(fields: Record<string, any>): void {
  for (const [field, value] of Object.entries(fields)) {
    if (!value || (typeof value === 'string' && !value.trim())) {
      throw new Error(`${field} is required`);
    }
  }
}

/**
 * Sanitize object with multiple fields
 */
export function sanitizeContactForm(data: {
  name?: string;
  email?: string;
  phone?: string;
  message?: string;
  company?: string;
  subject?: string;
  service?: string;
}) {
  return {
    name: sanitizeText(data.name),
    email: sanitizeEmail(data.email),
    phone: sanitizePhone(data.phone),
    message: sanitizeHTML(data.message),
    company: sanitizeText(data.company),
    subject: sanitizeText(data.subject),
    service: sanitizeText(data.service),
  };
} 