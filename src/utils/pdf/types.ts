/**
 * PDF generation types and interfaces
 * Extracted from pdfGenerator.ts for better maintainability
 */

// Define supported paper sizes
export type PaperSize = 'A4' | 'A5' | 'LETTER' | 'LEGAL' | 'TABLOID' | 'EXECUTIVE' | 'AUTO';

// Define paper dimensions in points (72 points = 1 inch)
export const PAPER_DIMENSIONS = {
  A4: { width: 595.28, height: 841.89 },
  A5: { width: 419.53, height: 595.28 },
  LETTER: { width: 612, height: 792 },
  LEGAL: { width: 612, height: 1008 },
  TABLOID: { width: 792, height: 1224 },
  EXECUTIVE: { width: 521.86, height: 756 },
  AUTO: { width: 0, height: 0 } // Will be determined dynamically
};

// Define common item data interface
export interface ItemData {
  description: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  serviceName?: string;
}

// Define the receipt data interface
export interface ReceiptData {
  receiptNumber: string;
  customerName: string;
  phoneNumber: string;
  email?: string;
  transactionId: string;
  transactionDate: Date;
  items: ItemData[];
  totalAmount: number;
  amountPaid: number;
  balance: number;
  notes?: string;
  issuedAt: Date;
  paperSize?: PaperSize; // Optional paper size, defaults to A4
  autoScale?: boolean; // Whether to automatically scale content to fit the page
}

// Define the invoice data interface
export interface InvoiceData {
  invoiceNumber: string;
  customerName: string;
  phoneNumber: string;
  email?: string;
  items: ItemData[];
  totalAmount: number;
  amountPaid: number;
  balance: number;
  notes?: string;
  issuedAt: string;
  dueDate: string;
  status: string;
  paperSize?: PaperSize; // Optional paper size, defaults to A4
  autoScale?: boolean; // Whether to automatically scale content to fit the page
}

// Define the quote data interface
export interface QuoteData {
  quoteNumber: string;
  customerName: string;
  phoneNumber: string;
  email?: string;
  items: ItemData[];
  totalAmount: number;
  notes?: string;
  issuedAt: string;
  validUntil: string;
  status: string;
  paperSize?: PaperSize; // Optional paper size, defaults to A4
  autoScale?: boolean; // Whether to automatically scale content to fit the page
}

// For backward compatibility
export type ReceiptItemData = ItemData; 