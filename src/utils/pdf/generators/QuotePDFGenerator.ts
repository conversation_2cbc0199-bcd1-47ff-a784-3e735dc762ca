import path from 'path';
import { BasePDFGenerator, type PDFGenerationOptions } from './BasePDFGenerator';
import type { QuoteData } from '../types';

/**
 * Specific PDF generator for quotes
 * Extends BasePDFGenerator with quote-specific content generation
 */
export class QuotePDFGenerator extends BasePDFGenerator<QuoteData> {
  
  /**
   * Generate quote content
   */
  protected async generateContent(quoteData: QuoteData): Promise<void> {
    try {
      // Add company header
      await this.addCompanyHeader();
      
      // Add title
      this.addTitle('QUOTE');
      
      // Add quote details
      this.addQuoteDetails(quoteData);
      
      // Add customer information
      this.addCustomerInfo(quoteData);
      
      // Add items
      this.addItems(quoteData);
      
      // Add totals
      this.addTotals(quoteData);
      
      // Add quote validity
      this.addQuoteValidity(quoteData);
      
      // Add notes if any
      if (quoteData.notes) {
        this.addNotes(quoteData.notes);
      }
      
      // Add footer
      this.addFooter();
      
      console.log('Quote PDF content added successfully');
    } catch (error) {
      console.error('Error adding quote content:', error);
      this.addFallbackContent(quoteData);
    }
  }

  /**
   * Add quote-specific details (quote number, date, valid until)
   */
  private addQuoteDetails(quoteData: QuoteData): void {
    const detailsFontSize = 12 * this.scaleFactor;
    this.doc.fontSize(detailsFontSize);
    
    this.doc.text(
      `Quote Number: ${quoteData.quoteNumber}`,
      this.leftMargin,
      this.doc.y,
      { width: this.contentWidth, align: 'center' }
    );
    
    this.doc.text(
      `Date: ${quoteData.issuedAt}`,
      this.leftMargin,
      this.doc.y,
      { width: this.contentWidth, align: 'center' }
    );
    
    this.doc.text(
      `Valid Until: ${quoteData.validUntil}`,
      this.leftMargin,
      this.doc.y,
      { width: this.contentWidth, align: 'center' }
    );
    
    this.doc.moveDown();
  }

  /**
   * Add customer information section
   */
  private addCustomerInfo(quoteData: QuoteData): void {
    const sectionFontSize = 12 * this.scaleFactor;
    this.doc.fontSize(sectionFontSize);
    
    this.doc.text(
      'QUOTE FOR:',
      this.leftMargin,
      this.doc.y,
      { width: this.contentWidth, align: 'left' }
    );
    
    this.doc.text(
      `Name: ${quoteData.customerName}`,
      this.leftMargin,
      this.doc.y,
      { width: this.contentWidth, align: 'left' }
    );
    
    this.doc.text(
      `Phone: ${quoteData.phoneNumber}`,
      this.leftMargin,
      this.doc.y,
      { width: this.contentWidth, align: 'left' }
    );

    if (quoteData.email) {
      this.doc.text(
        `Email: ${quoteData.email}`,
        this.leftMargin,
        this.doc.y,
        { width: this.contentWidth, align: 'left' }
      );
    }

    this.doc.moveDown();
  }

  /**
   * Add items section with table-like formatting
   */
  private addItems(quoteData: QuoteData): void {
    const sectionFontSize = 12 * this.scaleFactor;
    this.doc.fontSize(sectionFontSize);
    
    this.doc.text(
      'QUOTED ITEMS & SERVICES',
      this.leftMargin,
      this.doc.y,
      { width: this.contentWidth, align: 'left' }
    );
    
    this.doc.moveDown(0.5);

    // Table header
    const tableY = this.doc.y;
    const descCol = this.leftMargin;
    const qtyCol = this.leftMargin + (this.contentWidth * 0.6);
    const priceCol = this.leftMargin + (this.contentWidth * 0.75);
    const totalCol = this.leftMargin + (this.contentWidth * 0.9);

    this.doc.fontSize(10 * this.scaleFactor);
    this.doc.text('Description', descCol, tableY);
    this.doc.text('Qty', qtyCol, tableY);
    this.doc.text('Price', priceCol, tableY);
    this.doc.text('Total', totalCol, tableY);
    
    this.doc.moveDown(0.3);

    // Add items
    for (const item of quoteData.items) {
      const itemY = this.doc.y;
      this.doc.text(item.description, descCol, itemY, { width: this.contentWidth * 0.55 });
      this.doc.text(item.quantity.toString(), qtyCol, itemY);
      this.doc.text(`KES ${item.unitPrice}`, priceCol, itemY);
      this.doc.text(`KES ${item.totalPrice}`, totalCol, itemY);
      this.doc.moveDown(0.3);
    }

    this.doc.moveDown();
  }

  /**
   * Add totals section
   */
  private addTotals(quoteData: QuoteData): void {
    const totalsFontSize = 14 * this.scaleFactor;
    this.doc.fontSize(totalsFontSize);
    
    // Total
    this.doc.text(
      `TOTAL QUOTED AMOUNT: KES ${quoteData.totalAmount}`,
      this.leftMargin,
      this.doc.y,
      { width: this.contentWidth, align: 'right' }
    );
  }

  /**
   * Add quote validity and status section
   */
  private addQuoteValidity(quoteData: QuoteData): void {
    this.doc.moveDown();
    const validityFontSize = 11 * this.scaleFactor;
    this.doc.fontSize(validityFontSize);
    
    this.doc.text(
      `Quote Status: ${quoteData.status}`,
      this.leftMargin,
      this.doc.y,
      { width: this.contentWidth, align: 'left' }
    );
    
    this.doc.text(
      `This quote is valid until: ${quoteData.validUntil}`,
      this.leftMargin,
      this.doc.y,
      { width: this.contentWidth, align: 'left' }
    );
    
    this.doc.moveDown();
    this.doc.text(
      'Terms & Conditions:',
      this.leftMargin,
      this.doc.y,
      { width: this.contentWidth, align: 'left' }
    );
    
    this.doc.fontSize(9 * this.scaleFactor);
    this.doc.text(
      '• This quote is valid for the period specified above',
      this.leftMargin,
      this.doc.y,
      { width: this.contentWidth, align: 'left' }
    );
    this.doc.text(
      '• Prices may change after the validity period',
      this.leftMargin,
      this.doc.y,
      { width: this.contentWidth, align: 'left' }
    );
    this.doc.text(
      '• 50% deposit required to commence work',
      this.leftMargin,
      this.doc.y,
      { width: this.contentWidth, align: 'left' }
    );
  }

  /**
   * Add notes section
   */
  private addNotes(notes: string): void {
    this.doc.moveDown();
    this.doc.fontSize(10 * this.scaleFactor);
    this.doc.text(
      'Additional Notes:',
      this.leftMargin,
      this.doc.y,
      { width: this.contentWidth, align: 'left' }
    );
    this.doc.text(
      notes,
      this.leftMargin,
      this.doc.y,
      { width: this.contentWidth, align: 'left' }
    );
  }

  /**
   * Add fallback content in case of errors
   */
  private addFallbackContent(quoteData: QuoteData): void {
    this.doc.fontSize(16).text(
      'QUOTE',
      this.leftMargin,
      this.doc.y,
      { width: this.contentWidth, align: 'center' }
    );
    
    this.doc.moveDown();
    
    this.doc.fontSize(12).text(
      `Quote Number: ${quoteData.quoteNumber}`,
      this.leftMargin,
      this.doc.y,
      { width: this.contentWidth, align: 'center' }
    );
    
    this.doc.text(
      'Mocky Digital',
      this.leftMargin,
      this.doc.y,
      { width: this.contentWidth, align: 'center' }
    );
    
    this.doc.moveDown();
    
    this.doc.text(
      'Error generating complete quote. Please contact support.',
      this.leftMargin,
      this.doc.y,
      { width: this.contentWidth, align: 'center' }
    );
  }
}

/**
 * Factory function to generate quote PDF
 */
export async function generateQuotePDF(quoteData: QuoteData): Promise<string> {
  // Validate quote data
  if (!quoteData || !quoteData.quoteNumber) {
    console.error('Invalid quote data provided to PDF generator');
    throw new Error('Invalid quote data');
  }

  const generator = new QuotePDFGenerator();
  const filename = `quote-${quoteData.quoteNumber}.pdf`;
  const outputDir = path.join(process.cwd(), 'public', 'uploads', 'quotes');

  const options: PDFGenerationOptions = {
    outputDir,
    filename,
    title: `Quote ${quoteData.quoteNumber}`,
    subject: 'Quote'
  };

  return generator.generatePDF(quoteData, options);
} 