import path from 'path';
import { BasePDFGenerator, type BaseDocumentData, type PDFGenerationOptions } from './BasePDFGenerator';
import type { ReceiptData } from '../types';

/**
 * Specific PDF generator for receipts
 * Extends BasePDFGenerator with receipt-specific content generation
 */
export class ReceiptPDFGenerator extends BasePDFGenerator<ReceiptData> {
  
  /**
   * Generate receipt content
   */
  protected async generateContent(receiptData: ReceiptData): Promise<void> {
    try {
      // Add company header
      await this.addCompanyHeader();
      
      // Add title
      this.addTitle('RECEIPT');
      
      // Add receipt details
      this.addReceiptDetails(receiptData);
      
      // Add customer information
      this.addCustomerInfo(receiptData);
      
      // Add items
      this.addItems(receiptData);
      
      // Add totals
      this.addTotals(receiptData);
      
      // Add notes if any
      if (receiptData.notes) {
        this.addNotes(receiptData.notes);
      }
      
      // Add footer
      this.addFooter();
      
      console.log('Receipt PDF content added successfully');
    } catch (error) {
      console.error('Error adding receipt content:', error);
      this.addFallbackContent(receiptData);
    }
  }

  /**
   * Add receipt-specific details (receipt number, date)
   */
  private addReceiptDetails(receiptData: ReceiptData): void {
    const detailsFontSize = 12 * this.scaleFactor;
    this.doc.fontSize(detailsFontSize);
    
    this.doc.text(
      `Transaction ID: ${receiptData.receiptNumber}`,
      this.leftMargin,
      this.doc.y,
      { width: this.contentWidth, align: 'center' }
    );
    
    this.doc.text(
      `Date: ${receiptData.issuedAt.toLocaleDateString()}`,
      this.leftMargin,
      this.doc.y,
      { width: this.contentWidth, align: 'center' }
    );
    
    this.doc.moveDown();
  }

  /**
   * Add customer information section
   */
  private addCustomerInfo(receiptData: ReceiptData): void {
    const sectionFontSize = 12 * this.scaleFactor;
    this.doc.fontSize(sectionFontSize);
    
    this.doc.text(
      'CUSTOMER INFORMATION',
      this.leftMargin,
      this.doc.y,
      { width: this.contentWidth, align: 'left' }
    );
    
    this.doc.text(
      `Name: ${receiptData.customerName}`,
      this.leftMargin,
      this.doc.y,
      { width: this.contentWidth, align: 'left' }
    );
    
    this.doc.text(
      `Phone: ${receiptData.phoneNumber}`,
      this.leftMargin,
      this.doc.y,
      { width: this.contentWidth, align: 'left' }
    );

    if (receiptData.email) {
      this.doc.text(
        `Email: ${receiptData.email}`,
        this.leftMargin,
        this.doc.y,
        { width: this.contentWidth, align: 'left' }
      );
    }

    this.doc.moveDown();
  }

  /**
   * Add items section
   */
  private addItems(receiptData: ReceiptData): void {
    const sectionFontSize = 12 * this.scaleFactor;
    this.doc.fontSize(sectionFontSize);
    
    this.doc.text(
      'ITEMS',
      this.leftMargin,
      this.doc.y,
      { width: this.contentWidth, align: 'left' }
    );
    
    this.doc.moveDown(0.5);

    // Add items with simplified formatting
    for (const item of receiptData.items) {
      this.doc.text(
        `${item.description} - ${item.quantity} x KES ${item.unitPrice} = KES ${item.totalPrice}`,
        this.leftMargin,
        this.doc.y,
        { width: this.contentWidth, align: 'left' }
      );
    }

    this.doc.moveDown();
  }

  /**
   * Add totals section
   */
  private addTotals(receiptData: ReceiptData): void {
    const totalsFontSize = 12 * this.scaleFactor;
    this.doc.fontSize(totalsFontSize);
    
    this.doc.text(
      `Total: KES ${receiptData.totalAmount}`,
      this.leftMargin,
      this.doc.y,
      { width: this.contentWidth, align: 'right' }
    );
    
    this.doc.text(
      `Amount Paid: KES ${receiptData.amountPaid}`,
      this.leftMargin,
      this.doc.y,
      { width: this.contentWidth, align: 'right' }
    );
    
    this.doc.text(
      `Balance: KES ${receiptData.balance}`,
      this.leftMargin,
      this.doc.y,
      { width: this.contentWidth, align: 'right' }
    );
  }

  /**
   * Add notes section
   */
  private addNotes(notes: string): void {
    this.doc.moveDown();
    this.doc.text(
      'Notes:',
      this.leftMargin,
      this.doc.y,
      { width: this.contentWidth, align: 'left' }
    );
    this.doc.text(
      notes,
      this.leftMargin,
      this.doc.y,
      { width: this.contentWidth, align: 'left' }
    );
  }

  /**
   * Add fallback content in case of errors
   */
  private addFallbackContent(receiptData: ReceiptData): void {
    this.doc.fontSize(16).text(
      'RECEIPT',
      this.leftMargin,
      this.doc.y,
      { width: this.contentWidth, align: 'center' }
    );
    
    this.doc.moveDown();
    
    this.doc.fontSize(12).text(
      `Transaction ID: ${receiptData.receiptNumber}`,
      this.leftMargin,
      this.doc.y,
      { width: this.contentWidth, align: 'center' }
    );
    
    this.doc.text(
      'Mocky Digital',
      this.leftMargin,
      this.doc.y,
      { width: this.contentWidth, align: 'center' }
    );
    
    this.doc.moveDown();
    
    this.doc.text(
      'Error generating complete receipt. Please contact support.',
      this.leftMargin,
      this.doc.y,
      { width: this.contentWidth, align: 'center' }
    );
  }
}

/**
 * Factory function to generate receipt PDF
 */
export async function generateReceiptPDF(receiptData: ReceiptData): Promise<string> {
  // Validate receipt data
  if (!receiptData || !receiptData.receiptNumber) {
    console.error('Invalid receipt data provided to PDF generator');
    throw new Error('Invalid receipt data');
  }

  const generator = new ReceiptPDFGenerator();
  const filename = `${receiptData.receiptNumber}.pdf`;
  const outputDir = path.join(process.cwd(), 'public', 'uploads', 'receipts');

  const options: PDFGenerationOptions = {
    outputDir,
    filename,
    title: `Receipt ${receiptData.receiptNumber}`,
    subject: 'Payment Receipt'
  };

  return generator.generatePDF(receiptData, options);
} 