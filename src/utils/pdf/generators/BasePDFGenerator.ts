import PDFDocument from 'pdfkit';
import { promises as fs } from 'fs';
import path from 'path';
import fs_sync from 'fs';
import { PAPER_DIMENSIONS, type PaperSize } from '../types';

export interface BaseDocumentData {
  paperSize?: PaperSize;
  autoScale?: boolean;
}

export interface PDFGenerationOptions {
  outputDir: string;
  filename: string;
  title: string;
  subject: string;
}

/**
 * Base class for PDF generation with common functionality
 * Extracted from monolithic pdfGenerator.ts for better modularity
 */
export abstract class BasePDFGenerator<TData extends BaseDocumentData> {
  protected doc!: typeof PDFDocument.prototype;
  protected outputPath!: string;
  protected scaleFactor: number = 1;
  protected pageWidth!: number;
  protected pageHeight!: number;
  protected leftMargin: number = 30;
  protected rightMargin: number = 30;
  protected contentWidth!: number;

  constructor() {}

  /**
   * Generate PDF document
   */
  public async generatePDF(data: TData, options: PDFGenerationOptions): Promise<string> {
    try {
      console.log(`Starting PDF generation: ${options.filename}`);
      
      // Setup output directory and path
      await this.setupOutputDirectory(options.outputDir);
      this.outputPath = path.join(options.outputDir, options.filename);
      
      // Initialize PDF document
      this.initializeDocument(data, options);
      
      // Generate document content (implemented by subclasses)
      await this.generateContent(data);
      
      // Finalize and save the document
      await this.finalizeDocument();
      
      // Return relative path
      return this.getRelativePath(options.outputDir, options.filename);
      
    } catch (error) {
      console.error('Error generating PDF:', error);
      throw error;
    }
  }

  /**
   * Setup output directory
   */
  protected async setupOutputDirectory(outputDir: string): Promise<void> {
    console.log(`Ensuring directory exists: ${outputDir}`);
    
    try {
      if (!fs_sync.existsSync(outputDir)) {
        fs_sync.mkdirSync(outputDir, { recursive: true });
        console.log(`Directory created: ${outputDir}`);
      }
    } catch (error) {
      console.error(`Error creating directory ${outputDir}:`, error);
      throw error;
    }
  }

  /**
   * Initialize PDF document with common settings
   */
  protected initializeDocument(data: TData, options: PDFGenerationOptions): void {
    const paperSize = data.paperSize || 'A4';
    const autoScale = data.autoScale !== undefined ? data.autoScale : true;
    
    console.log(`Using paper size: ${paperSize}, Auto-scale: ${autoScale}`);
    
    const pageDimensions = PAPER_DIMENSIONS[paperSize];
    
    this.doc = new PDFDocument({
      size: paperSize === 'AUTO' ? [pageDimensions.width, pageDimensions.height] : paperSize,
      margins: {
        top: 40,
        bottom: 40,
        left: this.leftMargin,
        right: this.rightMargin
      },
      autoFirstPage: true,
      layout: 'portrait',
      font: 'Helvetica',
      info: {
        Title: options.title,
        Author: 'Mocky Digital',
        Subject: options.subject,
        CreationDate: new Date(),
      },
    });

    // Setup page dimensions and scaling
    this.pageWidth = this.doc.page.width;
    this.pageHeight = this.doc.page.height;
    this.contentWidth = this.pageWidth - this.leftMargin - this.rightMargin;
    this.scaleFactor = autoScale ? Math.min(1, this.contentWidth / 500) : 1;
    
    console.log(`Page dimensions: ${this.pageWidth}x${this.pageHeight}, Content width: ${this.contentWidth}, Scale factor: ${this.scaleFactor}`);
  }

  /**
   * Add company header with logo and info
   */
  protected async addCompanyHeader(): Promise<void> {
    const logoPath = path.join(process.cwd(), 'public', 'images', 'logo.png');
    
    try {
      const startY = this.doc.y;
      const logoWidth = 120 * this.scaleFactor;
      const logoHeight = 80 * this.scaleFactor;

      // Add logo on the left
      this.doc.image(logoPath, this.leftMargin, startY, {
        fit: [logoWidth, logoHeight],
      });

      // Add company info on the right
      const companyInfoX = this.pageWidth - this.rightMargin - (150 * this.scaleFactor);
      const companyInfoFontSize = 12 * this.scaleFactor;

      this.doc.fontSize(companyInfoFontSize);
      this.doc.text('Mocky Digital', companyInfoX, startY, { 
        width: 150 * this.scaleFactor, 
        align: 'right' 
      });
      this.doc.text('Nairobi, Kenya', companyInfoX, this.doc.y, { 
        width: 150 * this.scaleFactor, 
        align: 'right' 
      });
      this.doc.text('Phone: +*********** 670', companyInfoX, this.doc.y, { 
        width: 150 * this.scaleFactor, 
        align: 'right' 
      });
      this.doc.text('Email: <EMAIL>', companyInfoX, this.doc.y, { 
        width: 150 * this.scaleFactor, 
        align: 'right' 
      });
      this.doc.text('Tax PIN: P052373324V', companyInfoX, this.doc.y, { 
        width: 150 * this.scaleFactor, 
        align: 'right' 
      });

      console.log('Company header added successfully');
    } catch (error) {
      console.error('Error adding company header:', error);
      // Continue without logo if error occurs
    }

    this.doc.y += 30 * this.scaleFactor;
  }

  /**
   * Add document title
   */
  protected addTitle(title: string): void {
    const titleFontSize = 18 * this.scaleFactor;
    this.doc.fontSize(titleFontSize);
    this.doc.text(title, this.leftMargin, this.doc.y, { 
      width: this.contentWidth, 
      align: 'center' 
    });
    this.doc.moveDown(0.5);
  }

  /**
   * Add footer with standard text
   */
  protected addFooter(): void {
    const footerY = Math.min(
      this.doc.y + (50 * this.scaleFactor), 
      this.pageHeight - (100 * this.scaleFactor)
    );
    this.doc.y = footerY;

    this.doc.moveDown();
    this.doc.text('Thank you for your business!', this.leftMargin, this.doc.y, { 
      width: this.contentWidth, 
      align: 'center' 
    });
    this.doc.text(
      'This is a computer-generated document and does not require a signature.',
      this.leftMargin, 
      this.doc.y, 
      { width: this.contentWidth, align: 'center' }
    );
  }

  /**
   * Finalize document and save to file
   */
  protected async finalizeDocument(): Promise<void> {
    const stream = require('fs').createWriteStream(this.outputPath);
    this.doc.pipe(stream);
    
    console.log('Finalizing PDF document...');
    this.doc.end();

    await new Promise<void>((resolve, reject) => {
      const timeout = setTimeout(() => {
        console.warn('PDF generation is taking longer than expected...');
      }, 5000);

      stream.on('finish', async () => {
        clearTimeout(timeout);
        console.log(`PDF file created successfully: ${path.basename(this.outputPath)}`);

        try {
          const stats = await fs.stat(this.outputPath);
          if (stats.size > 0) {
            console.log(`PDF file size: ${stats.size} bytes`);
            resolve();
          } else {
            console.error('PDF file was created but is empty');
            reject(new Error('Generated PDF file is empty'));
          }
                 } catch (err: any) {
           console.error(`Error verifying PDF file: ${err}`);
           reject(err);
         }
      });

             stream.on('error', (err: any) => {
         clearTimeout(timeout);
         console.error(`Error writing PDF file: ${err.message}`);
         reject(err);
       });
    });
  }

  /**
   * Get relative path for web access
   */
  protected getRelativePath(outputDir: string, filename: string): string {
    const uploadsIndex = outputDir.indexOf('uploads');
    if (uploadsIndex !== -1) {
      const relativePath = outputDir.substring(uploadsIndex);
      return `/${relativePath}/${filename}`;
    }
    return `/${filename}`;
  }

  /**
   * Abstract method to be implemented by subclasses
   */
  protected abstract generateContent(data: TData): Promise<void>;
} 