import path from 'path';
import { BasePDFGenerator, type PDFGenerationOptions } from './BasePDFGenerator';
import type { InvoiceData } from '../types';

/**
 * Specific PDF generator for invoices
 * Extends BasePDFGenerator with invoice-specific content generation
 */
export class InvoicePDFGenerator extends BasePDFGenerator<InvoiceData> {
  
  /**
   * Generate invoice content
   */
  protected async generateContent(invoiceData: InvoiceData): Promise<void> {
    try {
      // Add company header
      await this.addCompanyHeader();
      
      // Add title
      this.addTitle('INVOICE');
      
      // Add invoice details
      this.addInvoiceDetails(invoiceData);
      
      // Add customer information
      this.addCustomerInfo(invoiceData);
      
      // Add items
      this.addItems(invoiceData);
      
      // Add totals
      this.addTotals(invoiceData);
      
      // Add payment status
      this.addPaymentStatus(invoiceData);
      
      // Add notes if any
      if (invoiceData.notes) {
        this.addNotes(invoiceData.notes);
      }
      
      // Add footer
      this.addFooter();
      
      console.log('Invoice PDF content added successfully');
    } catch (error) {
      console.error('Error adding invoice content:', error);
      this.addFallbackContent(invoiceData);
    }
  }

  /**
   * Add invoice-specific details (invoice number, date, due date)
   */
  private addInvoiceDetails(invoiceData: InvoiceData): void {
    const detailsFontSize = 12 * this.scaleFactor;
    this.doc.fontSize(detailsFontSize);
    
    this.doc.text(
      `Invoice Number: ${invoiceData.invoiceNumber}`,
      this.leftMargin,
      this.doc.y,
      { width: this.contentWidth, align: 'center' }
    );
    
    this.doc.text(
      `Date: ${invoiceData.issuedAt}`,
      this.leftMargin,
      this.doc.y,
      { width: this.contentWidth, align: 'center' }
    );
    
    this.doc.text(
      `Due Date: ${invoiceData.dueDate}`,
      this.leftMargin,
      this.doc.y,
      { width: this.contentWidth, align: 'center' }
    );
    
    this.doc.moveDown();
  }

  /**
   * Add customer information section
   */
  private addCustomerInfo(invoiceData: InvoiceData): void {
    const sectionFontSize = 12 * this.scaleFactor;
    this.doc.fontSize(sectionFontSize);
    
    this.doc.text(
      'BILL TO:',
      this.leftMargin,
      this.doc.y,
      { width: this.contentWidth, align: 'left' }
    );
    
    this.doc.text(
      `Name: ${invoiceData.customerName}`,
      this.leftMargin,
      this.doc.y,
      { width: this.contentWidth, align: 'left' }
    );
    
    this.doc.text(
      `Phone: ${invoiceData.phoneNumber}`,
      this.leftMargin,
      this.doc.y,
      { width: this.contentWidth, align: 'left' }
    );

    if (invoiceData.email) {
      this.doc.text(
        `Email: ${invoiceData.email}`,
        this.leftMargin,
        this.doc.y,
        { width: this.contentWidth, align: 'left' }
      );
    }

    this.doc.moveDown();
  }

  /**
   * Add items section with table-like formatting
   */
  private addItems(invoiceData: InvoiceData): void {
    const sectionFontSize = 12 * this.scaleFactor;
    this.doc.fontSize(sectionFontSize);
    
    this.doc.text(
      'ITEMS & SERVICES',
      this.leftMargin,
      this.doc.y,
      { width: this.contentWidth, align: 'left' }
    );
    
    this.doc.moveDown(0.5);

    // Table header
    const tableY = this.doc.y;
    const descCol = this.leftMargin;
    const qtyCol = this.leftMargin + (this.contentWidth * 0.6);
    const priceCol = this.leftMargin + (this.contentWidth * 0.75);
    const totalCol = this.leftMargin + (this.contentWidth * 0.9);

    this.doc.fontSize(10 * this.scaleFactor);
    this.doc.text('Description', descCol, tableY);
    this.doc.text('Qty', qtyCol, tableY);
    this.doc.text('Price', priceCol, tableY);
    this.doc.text('Total', totalCol, tableY);
    
    this.doc.moveDown(0.3);

    // Add items
    for (const item of invoiceData.items) {
      const itemY = this.doc.y;
      this.doc.text(item.description, descCol, itemY, { width: this.contentWidth * 0.55 });
      this.doc.text(item.quantity.toString(), qtyCol, itemY);
      this.doc.text(`KES ${item.unitPrice}`, priceCol, itemY);
      this.doc.text(`KES ${item.totalPrice}`, totalCol, itemY);
      this.doc.moveDown(0.3);
    }

    this.doc.moveDown();
  }

  /**
   * Add totals section
   */
  private addTotals(invoiceData: InvoiceData): void {
    const totalsFontSize = 12 * this.scaleFactor;
    this.doc.fontSize(totalsFontSize);
    
    // Total
    this.doc.fontSize((totalsFontSize + 2) * this.scaleFactor);
    this.doc.text(
      `TOTAL: KES ${invoiceData.totalAmount}`,
      this.leftMargin,
      this.doc.y,
      { width: this.contentWidth, align: 'right' }
    );
  }

  /**
   * Add payment status section
   */
  private addPaymentStatus(invoiceData: InvoiceData): void {
    this.doc.moveDown();
    const paymentFontSize = 12 * this.scaleFactor;
    this.doc.fontSize(paymentFontSize);
    
    this.doc.text(
      `Amount Paid: KES ${invoiceData.amountPaid}`,
      this.leftMargin,
      this.doc.y,
      { width: this.contentWidth, align: 'right' }
    );
    
    this.doc.text(
      `Balance: KES ${invoiceData.balance}`,
      this.leftMargin,
      this.doc.y,
      { width: this.contentWidth, align: 'right' }
    );
    
    this.doc.text(
      `Status: ${invoiceData.status}`,
      this.leftMargin,
      this.doc.y,
      { width: this.contentWidth, align: 'right' }
    );
  }

  /**
   * Add notes section
   */
  private addNotes(notes: string): void {
    this.doc.moveDown();
    this.doc.fontSize(10 * this.scaleFactor);
    this.doc.text(
      'Notes:',
      this.leftMargin,
      this.doc.y,
      { width: this.contentWidth, align: 'left' }
    );
    this.doc.text(
      notes,
      this.leftMargin,
      this.doc.y,
      { width: this.contentWidth, align: 'left' }
    );
  }

  /**
   * Add fallback content in case of errors
   */
  private addFallbackContent(invoiceData: InvoiceData): void {
    this.doc.fontSize(16).text(
      'INVOICE',
      this.leftMargin,
      this.doc.y,
      { width: this.contentWidth, align: 'center' }
    );
    
    this.doc.moveDown();
    
    this.doc.fontSize(12).text(
      `Invoice Number: ${invoiceData.invoiceNumber}`,
      this.leftMargin,
      this.doc.y,
      { width: this.contentWidth, align: 'center' }
    );
    
    this.doc.text(
      'Mocky Digital',
      this.leftMargin,
      this.doc.y,
      { width: this.contentWidth, align: 'center' }
    );
    
    this.doc.moveDown();
    
    this.doc.text(
      'Error generating complete invoice. Please contact support.',
      this.leftMargin,
      this.doc.y,
      { width: this.contentWidth, align: 'center' }
    );
  }
}

/**
 * Factory function to generate invoice PDF
 */
export async function generateInvoicePDF(invoiceData: InvoiceData): Promise<string> {
  // Validate invoice data
  if (!invoiceData || !invoiceData.invoiceNumber) {
    console.error('Invalid invoice data provided to PDF generator');
    throw new Error('Invalid invoice data');
  }

  const generator = new InvoicePDFGenerator();
  const filename = `invoice-${invoiceData.invoiceNumber}.pdf`;
  const outputDir = path.join(process.cwd(), 'public', 'uploads', 'invoices');

  const options: PDFGenerationOptions = {
    outputDir,
    filename,
    title: `Invoice ${invoiceData.invoiceNumber}`,
    subject: 'Invoice'
  };

  return generator.generatePDF(invoiceData, options);
} 