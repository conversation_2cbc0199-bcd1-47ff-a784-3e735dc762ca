import nodemailer from 'nodemailer';

// Create a transporter using Gmail credentials
const transporter = nodemailer.createTransport({
  service: 'gmail',
  auth: {
    user: '<EMAIL>',
    pass: 'sncr fuit apix bitk'
  }
});

interface EmailOptions {
  subject: string;
  html: string;
}

export async function sendEmail(options: EmailOptions) {
  try {
    const mailOptions = {
      from: 'New Order <<EMAIL>>',
      to: '<EMAIL>',
      subject: options.subject,
      html: options.html,
    };

    const info = await transporter.sendMail(mailOptions);
    console.log('Email sent successfully:', info.messageId);
    return { success: true, messageId: info.messageId };
  } catch (error) {
    console.error('Error sending email:', error);
    return { success: false, error };
  }
}

export function generateOrderNotificationEmail(order: any) {
  const {
    orderNumber,
    customerName,
    email,
    phone,
    businessName,
    industry,
    logoType,
    slogan,
    additionalInfo,
    totalAmount,
    package: pkg,
  } = order;

  return {
    subject: `🎨 New Logo Order - ${orderNumber}`,
    html: `
      <div style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; max-width: 600px; margin: 0 auto; background: #ffffff; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 20px rgba(0,0,0,0.1);">
        <!-- Header with Logo and Brand Colors -->
        <div style="background: linear-gradient(135deg, #0A2647 0%, #205295 100%); padding: 40px 30px; text-align: center;">
          <img src="${process.env.NEXTAUTH_URL || 'https://mocky.co.ke'}/images/logo.png" alt="Mocky Digital Logo" style="width: 60px; height: 60px; margin-bottom: 20px; border-radius: 8px;">
          <h1 style="color: white; margin: 0; font-size: 28px; font-weight: 700; letter-spacing: -0.5px;">🎨 New Logo Order Received</h1>
          <p style="color: rgba(255,255,255,0.9); margin: 10px 0 0 0; font-size: 16px;">Professional logo design request</p>
        </div>

        <!-- Content Body -->
        <div style="background: #ffffff; padding: 40px 30px;">
          <p style="color: #4a5568; font-size: 16px; line-height: 1.6; margin-bottom: 30px;">
            A new logo design order has been received with the following details:
          </p>

          <!-- Order Details Card -->
          <div style="background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%); border-left: 4px solid #FF5400; padding: 25px; border-radius: 8px; margin: 25px 0;">
            <h3 style="color: #0A2647; margin: 0 0 15px 0; font-size: 20px; font-weight: 600;">📋 Order Details</h3>
            <div style="display: grid; gap: 10px;">
              <p style="margin: 0; color: #2d3748;"><strong style="color: #0A2647;">Order Number:</strong> <span style="color: #FF5400; font-weight: 600;">${orderNumber}</span></p>
              <p style="margin: 0; color: #2d3748;"><strong style="color: #0A2647;">Package:</strong> ${pkg?.name || 'N/A'}</p>
              <p style="margin: 0; color: #2d3748;"><strong style="color: #0A2647;">Amount:</strong> <span style="color: #FF5400; font-weight: 600;">KSh ${totalAmount?.toLocaleString()}</span></p>
            </div>
          </div>

          <!-- Customer Information Card -->
          <div style="background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%); border-left: 4px solid #205295; padding: 25px; border-radius: 8px; margin: 25px 0;">
            <h3 style="color: #0A2647; margin: 0 0 15px 0; font-size: 20px; font-weight: 600;">👤 Customer Information</h3>
            <div style="display: grid; gap: 10px;">
              <p style="margin: 0; color: #2d3748;"><strong style="color: #0A2647;">Name:</strong> ${customerName}</p>
              <p style="margin: 0; color: #2d3748;"><strong style="color: #0A2647;">Email:</strong> <a href="mailto:${email}" style="color: #205295;">${email}</a></p>
              ${phone ? `<p style="margin: 0; color: #2d3748;"><strong style="color: #0A2647;">Phone:</strong> <a href="tel:${phone}" style="color: #205295;">${phone}</a></p>` : ''}
              <p style="margin: 0; color: #2d3748;"><strong style="color: #0A2647;">Business Name:</strong> ${businessName}</p>
              ${industry ? `<p style="margin: 0; color: #2d3748;"><strong style="color: #0A2647;">Industry:</strong> ${industry}</p>` : ''}
            </div>
          </div>

          <!-- Design Requirements Card -->
          <div style="background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%); border-left: 4px solid #FF5400; padding: 25px; border-radius: 8px; margin: 25px 0;">
            <h3 style="color: #0A2647; margin: 0 0 15px 0; font-size: 20px; font-weight: 600;">🎨 Design Requirements</h3>
            <div style="display: grid; gap: 10px;">
              ${logoType ? `<p style="margin: 0; color: #2d3748;"><strong style="color: #0A2647;">Logo Type:</strong> ${logoType}</p>` : ''}
              ${slogan ? `<p style="margin: 0; color: #2d3748;"><strong style="color: #0A2647;">Slogan:</strong> "${slogan}"</p>` : ''}
              ${additionalInfo ? `<p style="margin: 0; color: #2d3748;"><strong style="color: #0A2647;">Additional Info:</strong> ${additionalInfo}</p>` : ''}
            </div>
          </div>
        </div>

        <!-- Footer -->
        <div style="background: #f7fafc; padding: 25px 30px; text-align: center; border-top: 1px solid #e2e8f0;">
          <p style="color: #718096; font-size: 14px; margin: 0 0 10px 0;">
            <strong style="color: #0A2647;">Mocky Digital Team</strong>
          </p>
          <p style="color: #a0aec0; font-size: 12px; margin: 0;">
            📧 <EMAIL> | 📱 +254 741 590 670<br>
            Professional Design & Digital Services
          </p>
        </div>
      </div>
    `
  };
}

export function generateCatalogueOrderNotificationEmail(order: any) {
  const {
    orderNumber,
    customerName,
    email,
    phone,
    productName,
    quantity,
    customQuantity,
    unitPrice,
    designFee,
    subtotal,
    totalAmount,
    paperType,
    printingSide,
    meters,
    needsDesign,
    designOnly,
    designBrief,
    artworkFiles,
    artworkFileCount,
    artworkUrls,
    deliveryMethod,
    deliveryAddress,
    notes
  } = order;

  // Format artwork links if they exist
  const artworkLinksHtml = artworkUrls && artworkUrls.length > 0 
    ? `
      <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
        <h3 style="color: #1f2937; margin-top: 0;">Artwork Files</h3>
        <ul style="margin: 0; padding-left: 20px;">
          ${artworkUrls.map((url: string, index: number) => `
            <li style="margin-bottom: 8px;">
              <a href="${url}" style="color: #2563eb; text-decoration: underline;" target="_blank">
                Artwork File ${index + 1}
              </a>
            </li>
          `).join('')}
        </ul>
      </div>
    `
    : '';

  return {
    subject: `🛒 New Catalogue Order - ${orderNumber}`,
    html: `
      <div style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; max-width: 600px; margin: 0 auto; background: #ffffff; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 20px rgba(0,0,0,0.1);">
        <!-- Header with Logo and Brand Colors -->
        <div style="background: linear-gradient(135deg, #0A2647 0%, #205295 100%); padding: 40px 30px; text-align: center;">
          <img src="${process.env.NEXTAUTH_URL || 'https://mocky.co.ke'}/images/logo.png" alt="Mocky Digital Logo" style="width: 60px; height: 60px; margin-bottom: 20px; border-radius: 8px;">
          <h1 style="color: white; margin: 0; font-size: 28px; font-weight: 700; letter-spacing: -0.5px;">🛒 New Catalogue Order Received</h1>
          <p style="color: rgba(255,255,255,0.9); margin: 10px 0 0 0; font-size: 16px;">Professional printing & design services</p>
        </div>

        <!-- Content Body -->
        <div style="background: #ffffff; padding: 40px 30px;">
          <p style="color: #4a5568; font-size: 16px; line-height: 1.6; margin-bottom: 30px;">
            A new catalogue order has been received with the following details:
          </p>
        
        <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="color: #1f2937; margin-top: 0;">Order Details</h3>
          <p><strong>Order Number:</strong> ${orderNumber}</p>
          <p><strong>Product:</strong> ${productName}</p>
          <p><strong>Quantity:</strong> ${quantity}${customQuantity ? ' (Custom)' : ''}</p>
          ${meters ? `<p><strong>Meters:</strong> ${meters}</p>` : ''}
          ${paperType ? `<p><strong>Paper Type:</strong> ${paperType}</p>` : ''}
          ${printingSide ? `<p><strong>Printing Side:</strong> ${printingSide}</p>` : ''}
          <p><strong>Unit Price:</strong> KSH${unitPrice}</p>
          ${designFee > 0 ? `<p><strong>Design Fee:</strong> KSH${designFee}</p>` : ''}
          <p><strong>Subtotal:</strong> KSH${subtotal}</p>
          <p><strong>Total Amount:</strong> KSH${totalAmount}</p>
        </div>

        <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="color: #1f2937; margin-top: 0;">Customer Information</h3>
          <p><strong>Name:</strong> ${customerName}</p>
          <p><strong>Email:</strong> ${email}</p>
          ${phone ? `<p><strong>Phone:</strong> ${phone}</p>` : ''}
        </div>

        ${needsDesign || designOnly ? `
          <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #1f2937; margin-top: 0;">Design Requirements</h3>
            <p><strong>Service Type:</strong> ${designOnly ? 'Design Only' : 'Design & Print'}</p>
            ${designBrief ? `<p><strong>Design Brief:</strong> ${designBrief}</p>` : ''}
          </div>
        ` : ''}

        ${artworkLinksHtml}

        <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="color: #1f2937; margin-top: 0;">Delivery Information</h3>
          <p><strong>Method:</strong> ${deliveryMethod || 'Not specified'}</p>
          ${deliveryAddress ? `<p><strong>Address:</strong> ${deliveryAddress}</p>` : ''}
        </div>

        ${notes ? `
          <div style="background-color: #f3f4f6; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #1f2937; margin-top: 0;">Additional Notes</h3>
            <p>${notes}</p>
          </div>
        ` : ''}
      </div>
    `
  };
} 