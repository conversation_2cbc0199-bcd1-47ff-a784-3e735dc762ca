// Upload diagnostics utility for production debugging

export interface DiagnosticResult {
  timestamp: string;
  testName: string;
  success: boolean;
  duration: number;
  error?: string;
  details?: any;
}

export class UploadDiagnostics {
  private static async runTest(
    testName: string,
    testFn: () => Promise<any>
  ): Promise<DiagnosticResult> {
    const startTime = Date.now();
    const timestamp = new Date().toISOString();
    
    try {
      const result = await testFn();
      return {
        timestamp,
        testName,
        success: true,
        duration: Date.now() - startTime,
        details: result
      };
    } catch (error) {
      return {
        timestamp,
        testName,
        success: false,
        duration: Date.now() - startTime,
        error: error instanceof Error ? error.message : String(error),
        details: error instanceof Error ? { stack: error.stack } : undefined
      };
    }
  }

  /**
   * Test API endpoint connectivity
   */
  static async testApiEndpoint(): Promise<DiagnosticResult> {
    return this.runTest('API Endpoint Connectivity', async () => {
      const response = await fetch('/api/health', { method: 'GET' });
      if (!response.ok) {
        throw new Error(`API endpoint returned ${response.status}: ${response.statusText}`);
      }
      return { status: response.status, statusText: response.statusText };
    });
  }

  /**
   * Test upload API with a small test file
   */
  static async testUploadEndpoint(): Promise<DiagnosticResult> {
    return this.runTest('Upload Endpoint Test', async () => {
      // Create a small test image (1x1 pixel PNG)
      const testImageData = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
      const testBlob = new Blob([Uint8Array.from(atob(testImageData), c => c.charCodeAt(0))], { type: 'image/png' });
      const testFile = new File([testBlob], 'test.png', { type: 'image/png' });

      const formData = new FormData();
      formData.append('file', testFile);
      formData.append('category', 'test');

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(`Upload test failed: ${response.status} - ${errorData.error || response.statusText}`);
      }

      const result = await response.json();
      return result;
    });
  }

  /**
   * Test network connectivity to S3 endpoint
   */
  static async testS3Connectivity(): Promise<DiagnosticResult> {
    return this.runTest('S3 Connectivity Test', async () => {
      // Test if we can reach the S3 endpoint
      const s3Endpoint = process.env.NEXT_PUBLIC_S3_ENDPOINT;
      if (!s3Endpoint) {
        throw new Error('S3 endpoint not configured');
      }

      const response = await fetch(s3Endpoint, { method: 'HEAD' });
      return {
        endpoint: s3Endpoint,
        status: response.status,
        headers: Object.fromEntries(response.headers.entries())
      };
    });
  }

  /**
   * Test browser capabilities
   */
  static async testBrowserCapabilities(): Promise<DiagnosticResult> {
    return this.runTest('Browser Capabilities', async () => {
      const capabilities = {
        fetch: typeof fetch !== 'undefined',
        formData: typeof FormData !== 'undefined',
        file: typeof File !== 'undefined',
        blob: typeof Blob !== 'undefined',
        arrayBuffer: typeof ArrayBuffer !== 'undefined',
        url: typeof URL !== 'undefined',
        userAgent: navigator.userAgent,
        online: navigator.onLine,
        connection: (navigator as any).connection ? {
          effectiveType: (navigator as any).connection.effectiveType,
          downlink: (navigator as any).connection.downlink,
          rtt: (navigator as any).connection.rtt
        } : undefined
      };

      return capabilities;
    });
  }

  /**
   * Run all diagnostic tests
   */
  static async runAllTests(): Promise<DiagnosticResult[]> {
    console.log('🔍 Running upload diagnostics...');
    
    const tests = [
      this.testBrowserCapabilities(),
      this.testApiEndpoint(),
      this.testS3Connectivity(),
      this.testUploadEndpoint()
    ];

    const results = await Promise.allSettled(tests);
    
    return results.map((result, index) => {
      if (result.status === 'fulfilled') {
        return result.value;
      } else {
        return {
          timestamp: new Date().toISOString(),
          testName: `Test ${index + 1}`,
          success: false,
          duration: 0,
          error: result.reason?.message || String(result.reason)
        };
      }
    });
  }

  /**
   * Log diagnostic results to console
   */
  static logResults(results: DiagnosticResult[]): void {
    console.log('\n📊 Upload Diagnostic Results:');
    console.log('='.repeat(50));
    
    results.forEach((result, index) => {
      const status = result.success ? '✅' : '❌';
      console.log(`${status} ${result.testName} (${result.duration}ms)`);
      
      if (!result.success && result.error) {
        console.log(`   Error: ${result.error}`);
      }
      
      if (result.details && typeof result.details === 'object') {
        console.log(`   Details:`, result.details);
      }
      
      console.log('');
    });
    
    const successCount = results.filter(r => r.success).length;
    console.log(`Summary: ${successCount}/${results.length} tests passed`);
    console.log('='.repeat(50));
  }

  /**
   * Generate a diagnostic report
   */
  static async generateReport(): Promise<string> {
    const results = await this.runAllTests();
    this.logResults(results);
    
    const report = {
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      tests: results,
      summary: {
        total: results.length,
        passed: results.filter(r => r.success).length,
        failed: results.filter(r => !r.success).length
      }
    };
    
    return JSON.stringify(report, null, 2);
  }
} 