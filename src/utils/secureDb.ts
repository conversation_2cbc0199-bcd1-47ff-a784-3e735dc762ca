/**
 * Secure Database Operations
 * Wrapper around Prisma operations with additional security validations
 */

import { PrismaClient } from '@prisma/client';
import { containsDangerousPatterns, sanitizeInput } from '@/config/security';

// Create a type-safe wrapper for database operations
type SecureDbOptions = {
  sanitizeStrings?: boolean;
  validateInput?: boolean;
  logOperations?: boolean;
};

/**
 * Secure database operation wrapper
 * Adds input validation and sanitization to prevent injection attacks
 */
export class SecureDb {
  private prisma: PrismaClient;
  private options: SecureDbOptions;

  constructor(prisma: PrismaClient, options: SecureDbOptions = {}) {
    this.prisma = prisma;
    this.options = {
      sanitizeStrings: true,
      validateInput: true,
      logOperations: process.env.NODE_ENV === 'development',
      ...options
    };
  }

  /**
   * Validate and sanitize input data
   */
  private validateAndSanitize(data: any): any {
    if (!this.options.validateInput) return data;

    if (typeof data === 'string') {
      // Check for dangerous patterns
      if (containsDangerousPatterns(data)) {
        throw new Error('Input contains potentially dangerous patterns');
      }
      
      // Sanitize if enabled
      return this.options.sanitizeStrings ? sanitizeInput(data) : data;
    }

    if (Array.isArray(data)) {
      return data.map(item => this.validateAndSanitize(item));
    }

    if (data && typeof data === 'object') {
      const sanitized: any = {};
      for (const [key, value] of Object.entries(data)) {
        sanitized[key] = this.validateAndSanitize(value);
      }
      return sanitized;
    }

    return data;
  }

  /**
   * Log database operation (if enabled)
   */
  private logOperation(operation: string, model: string, data?: any) {
    if (this.options.logOperations) {
      console.log(`[SecureDb] ${operation} on ${model}`, data ? { dataKeys: Object.keys(data) } : '');
    }
  }

  /**
   * Secure findMany operation
   */
  async findMany<T>(model: keyof PrismaClient, args: any): Promise<T[]> {
    this.logOperation('findMany', String(model));
    
    // Validate where clause
    if (args.where) {
      args.where = this.validateAndSanitize(args.where);
    }

    // Execute operation
    const modelClient = this.prisma[model] as any;
    return await modelClient.findMany(args);
  }

  /**
   * Secure findFirst operation
   */
  async findFirst<T>(model: keyof PrismaClient, args: any): Promise<T | null> {
    this.logOperation('findFirst', String(model));
    
    if (args.where) {
      args.where = this.validateAndSanitize(args.where);
    }

    const modelClient = this.prisma[model] as any;
    return await modelClient.findFirst(args);
  }

  /**
   * Secure findUnique operation
   */
  async findUnique<T>(model: keyof PrismaClient, args: any): Promise<T | null> {
    this.logOperation('findUnique', String(model));
    
    if (args.where) {
      args.where = this.validateAndSanitize(args.where);
    }

    const modelClient = this.prisma[model] as any;
    return await modelClient.findUnique(args);
  }

  /**
   * Secure create operation
   */
  async create<T>(model: keyof PrismaClient, args: any): Promise<T> {
    this.logOperation('create', String(model), args.data);
    
    if (args.data) {
      args.data = this.validateAndSanitize(args.data);
    }

    const modelClient = this.prisma[model] as any;
    return await modelClient.create(args);
  }

  /**
   * Secure update operation
   */
  async update<T>(model: keyof PrismaClient, args: any): Promise<T> {
    this.logOperation('update', String(model), args.data);
    
    if (args.where) {
      args.where = this.validateAndSanitize(args.where);
    }
    
    if (args.data) {
      args.data = this.validateAndSanitize(args.data);
    }

    const modelClient = this.prisma[model] as any;
    return await modelClient.update(args);
  }

  /**
   * Secure updateMany operation
   */
  async updateMany(model: keyof PrismaClient, args: any): Promise<{ count: number }> {
    this.logOperation('updateMany', String(model), args.data);
    
    if (args.where) {
      args.where = this.validateAndSanitize(args.where);
    }
    
    if (args.data) {
      args.data = this.validateAndSanitize(args.data);
    }

    const modelClient = this.prisma[model] as any;
    return await modelClient.updateMany(args);
  }

  /**
   * Secure delete operation
   */
  async delete<T>(model: keyof PrismaClient, args: any): Promise<T> {
    this.logOperation('delete', String(model));
    
    if (args.where) {
      args.where = this.validateAndSanitize(args.where);
    }

    const modelClient = this.prisma[model] as any;
    return await modelClient.delete(args);
  }

  /**
   * Secure deleteMany operation
   */
  async deleteMany(model: keyof PrismaClient, args: any): Promise<{ count: number }> {
    this.logOperation('deleteMany', String(model));
    
    if (args.where) {
      args.where = this.validateAndSanitize(args.where);
    }

    const modelClient = this.prisma[model] as any;
    return await modelClient.deleteMany(args);
  }

  /**
   * Secure count operation
   */
  async count(model: keyof PrismaClient, args: any = {}): Promise<number> {
    this.logOperation('count', String(model));
    
    if (args.where) {
      args.where = this.validateAndSanitize(args.where);
    }

    const modelClient = this.prisma[model] as any;
    return await modelClient.count(args);
  }

  /**
   * Execute raw query with validation (use sparingly)
   */
  async executeRaw(query: string, ...params: any[]): Promise<any> {
    // Validate query for dangerous patterns
    if (containsDangerousPatterns(query)) {
      throw new Error('Query contains potentially dangerous patterns');
    }

    // Log raw query in development
    if (this.options.logOperations) {
      console.warn('[SecureDb] Executing raw query:', query);
    }

    return await this.prisma.$executeRaw`${query}`;
  }

  /**
   * Get the underlying Prisma client (use with caution)
   */
  getPrismaClient(): PrismaClient {
    console.warn('[SecureDb] Direct Prisma client access - bypassing security layer');
    return this.prisma;
  }
}

/**
 * Create a secure database instance
 */
export function createSecureDb(prisma: PrismaClient, options?: SecureDbOptions): SecureDb {
  return new SecureDb(prisma, options);
} 