import DOMPurify from 'isomorphic-dompurify';
import validator from 'validator';

// HTML sanitization options
const SANITIZE_CONFIG = {
  ALLOWED_TAGS: ['p', 'br', 'strong', 'em', 'u', 'a', 'ul', 'ol', 'li', 'blockquote', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'],
  ALLOWED_ATTR: ['href', 'target', 'rel'],
  ALLOWED_URI_REGEXP: /^(?:(?:https?|mailto):|[^a-z]|[a-z+.-]+(?:[^a-z+.-:]|$))/i,
  FORBID_TAGS: ['script', 'object', 'embed', 'form', 'input', 'textarea', 'select'],
  FORBID_ATTR: ['onerror', 'onload', 'onclick', 'onmouseover', 'style'],
};

// Rich text sanitization (more permissive for blog content)
const RICH_TEXT_CONFIG = {
  ALLOWED_TAGS: [
    'p', 'br', 'strong', 'em', 'u', 'a', 'ul', 'ol', 'li', 'blockquote',
    'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'img', 'table', 'thead', 'tbody',
    'tr', 'td', 'th', 'code', 'pre', 'span', 'div'
  ],
  ALLOWED_ATTR: ['href', 'target', 'rel', 'src', 'alt', 'title', 'class'],
  ALLOWED_URI_REGEXP: /^(?:(?:https?|mailto):|[^a-z]|[a-z+.-]+(?:[^a-z+.-:]|$))/i,
  FORBID_TAGS: ['script', 'object', 'embed', 'form', 'input', 'textarea', 'select'],
  FORBID_ATTR: ['onerror', 'onload', 'onclick', 'onmouseover', 'onfocus', 'onblur'],
};

/**
 * Sanitize plain text input
 * Removes HTML tags and normalizes whitespace
 */
export function sanitizeText(input: unknown): string {
  if (input === null || input === undefined) return '';
  
  const text = String(input);
  
  // Remove HTML tags and decode entities
  const withoutHtml = text.replace(/<[^>]*>/g, '');
  const decoded = validator.unescape(withoutHtml);
  
  // Normalize whitespace
  const normalized = decoded.replace(/\s+/g, ' ').trim();
  
  // Limit length to prevent DoS
  return normalized.substring(0, 10000);
}

/**
 * Sanitize HTML content while preserving safe formatting
 */
export function sanitizeHtml(input: unknown, allowRichText = false): string {
  if (input === null || input === undefined) return '';
  
  const html = String(input);
  
  // Use appropriate config based on context
  const config = allowRichText ? RICH_TEXT_CONFIG : SANITIZE_CONFIG;
  
  // Sanitize using DOMPurify
  const sanitized = DOMPurify.sanitize(html, config);
  
  // Additional cleanup
  return sanitized
    .replace(/javascript:/gi, '') // Remove javascript: protocols
    .replace(/data:(?!image\/(?:png|jpe?g|gif|webp|svg\+xml))/gi, '') // Only allow safe data URIs
    .trim();
}

/**
 * Sanitize email addresses
 */
export function sanitizeEmail(input: unknown): string {
  if (input === null || input === undefined) return '';
  
  const email = sanitizeText(input).toLowerCase();
  
  // Validate email format
  if (!validator.isEmail(email)) {
    throw new Error('Invalid email format');
  }
  
  return email;
}

/**
 * Sanitize URLs
 */
export function sanitizeUrl(input: unknown, allowedProtocols = ['http', 'https']): string {
  if (input === null || input === undefined) return '';
  
  const url = sanitizeText(input);
  
  if (!url) return '';
  
  // Check if URL is valid
  if (!validator.isURL(url, {
    protocols: allowedProtocols,
    require_protocol: true,
    require_valid_protocol: true,
  })) {
    throw new Error('Invalid URL format');
  }
  
  return url;
}

/**
 * Sanitize phone numbers
 */
export function sanitizePhone(input: unknown): string {
  if (input === null || input === undefined) return '';
  
  const phone = sanitizeText(input);
  
  // Remove all non-digit characters except + at start
  const cleaned = phone.replace(/[^\d+]/g, '');
  
  // Validate basic phone format
  if (cleaned.length < 10 || cleaned.length > 15) {
    throw new Error('Invalid phone number length');
  }
  
  return cleaned;
}

/**
 * Sanitize numeric input
 */
export function sanitizeNumber(input: unknown, min?: number, max?: number): number {
  if (input === null || input === undefined) {
    throw new Error('Number is required');
  }
  
  const num = Number(input);
  
  if (isNaN(num) || !isFinite(num)) {
    throw new Error('Invalid number format');
  }
  
  if (min !== undefined && num < min) {
    throw new Error(`Number must be at least ${min}`);
  }
  
  if (max !== undefined && num > max) {
    throw new Error(`Number must be at most ${max}`);
  }
  
  return num;
}

/**
 * Sanitize file names
 */
export function sanitizeFileName(input: unknown): string {
  if (input === null || input === undefined) return '';
  
  const fileName = sanitizeText(input);
  
  // Remove dangerous characters
  const safe = fileName
    .replace(/[<>:"/\\|?*\x00-\x1f]/g, '')
    .replace(/^\.+/, '') // Remove leading dots
    .replace(/\.+$/, '') // Remove trailing dots
    .replace(/\s+/g, '_') // Replace spaces with underscores
    .substring(0, 255); // Limit length
  
  if (!safe) {
    throw new Error('Invalid file name');
  }
  
  return safe;
}

/**
 * Sanitize JSON input
 */
export function sanitizeJson<T = unknown>(input: unknown): T {
  if (input === null || input === undefined) {
    throw new Error('JSON input is required');
  }
  
  let parsed: T;
  
  if (typeof input === 'string') {
    try {
      parsed = JSON.parse(input);
    } catch {
      throw new Error('Invalid JSON format');
    }
  } else {
    parsed = input as T;
  }
  
  // Additional validation for object depth to prevent DoS
  const jsonString = JSON.stringify(parsed);
  if (jsonString.length > 100000) { // 100KB limit
    throw new Error('JSON input too large');
  }
  
  return parsed;
}

/**
 * Sanitize array of strings
 */
export function sanitizeStringArray(input: unknown, maxItems = 100): string[] {
  if (input === null || input === undefined) return [];
  
  if (!Array.isArray(input)) {
    throw new Error('Input must be an array');
  }
  
  if (input.length > maxItems) {
    throw new Error(`Array cannot have more than ${maxItems} items`);
  }
  
  return input.map(item => sanitizeText(item)).filter(Boolean);
}

/**
 * Sanitize slug (URL-safe identifier)
 */
export function sanitizeSlug(input: unknown): string {
  if (input === null || input === undefined) return '';
  
  const text = sanitizeText(input);
  
  const slug = text
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Remove consecutive hyphens
    .replace(/^-|-$/g, '') // Remove leading/trailing hyphens
    .substring(0, 100); // Limit length
  
  if (!slug) {
    throw new Error('Cannot generate valid slug from input');
  }
  
  return slug;
}

/**
 * Validate and sanitize password
 */
export function validatePassword(password: unknown): string {
  if (typeof password !== 'string') {
    throw new Error('Password must be a string');
  }
  
  if (password.length < 8) {
    throw new Error('Password must be at least 8 characters long');
  }
  
  if (password.length > 128) {
    throw new Error('Password is too long');
  }
  
  // Check for at least one lowercase, uppercase, number, and special character
  const hasLower = /[a-z]/.test(password);
  const hasUpper = /[A-Z]/.test(password);
  const hasNumber = /\d/.test(password);
  const hasSpecial = /[!@#$%^&*(),.?":{}|<>]/.test(password);
  
  if (!hasLower || !hasUpper || !hasNumber || !hasSpecial) {
    throw new Error('Password must contain at least one lowercase letter, uppercase letter, number, and special character');
  }
  
  return password;
}

/**
 * Comprehensive form data sanitizer
 */
export function sanitizeFormData(data: Record<string, unknown>, schema: Record<string, string>): Record<string, unknown> {
  const sanitized: Record<string, unknown> = {};
  
  for (const [key, type] of Object.entries(schema)) {
    const value = data[key];
    
    try {
      switch (type) {
        case 'text':
          sanitized[key] = sanitizeText(value);
          break;
        case 'html':
          sanitized[key] = sanitizeHtml(value);
          break;
        case 'richtext':
          sanitized[key] = sanitizeHtml(value, true);
          break;
        case 'email':
          sanitized[key] = sanitizeEmail(value);
          break;
        case 'url':
          sanitized[key] = sanitizeUrl(value);
          break;
        case 'phone':
          sanitized[key] = sanitizePhone(value);
          break;
        case 'number':
          sanitized[key] = sanitizeNumber(value);
          break;
        case 'slug':
          sanitized[key] = sanitizeSlug(value);
          break;
        case 'array':
          sanitized[key] = sanitizeStringArray(value);
          break;
        case 'json':
          sanitized[key] = sanitizeJson(value);
          break;
        case 'filename':
          sanitized[key] = sanitizeFileName(value);
          break;
        default:
          sanitized[key] = sanitizeText(value);
      }
    } catch (error) {
      throw new Error(`Validation failed for field '${key}': ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
  
  return sanitized;
} 