// Mark as server component
'use server';

import { readFile } from 'node:fs/promises';
import { join } from 'node:path';
import { S3Client, ListObjectsV2Command } from '@aws-sdk/client-s3';
import type { ImageItem } from './getImages';
import { PrismaClient } from '@prisma/client';

// Singleton pattern for Prisma client to prevent connection pool exhaustion
const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined;
};

const prisma = globalForPrisma.prisma ?? new PrismaClient({
  log: process.env.NODE_ENV === 'development' ? ['error', 'warn'] : ['error'],
  datasources: {
    db: {
      url: process.env.DATABASE_URL,
    },
  },
});

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma;

// Cache for S3 client configuration to avoid repeated database calls
let s3ConfigCache: {
  client: S3Client;
  bucketName: string;
  endpoint: string;
} | null = null;
let configCacheTime = 0;
const CONFIG_CACHE_DURATION = 1000 * 60 * 5; // 5 minutes

// In-memory cache for server-side image fetching
let imageCache: Map<string, { data: ImageItem[]; timestamp: number }> = new Map();
const IMAGE_CACHE_TTL = 5 * 60 * 1000; // 5 minutes cache for server-side

// Function to reset server-side image cache (for admin cache invalidation)
export async function resetServerImageCache(category?: string) {
  if (category) {
    imageCache.delete(category);
    console.log(`Server image cache cleared for category: ${category}`);
  } else {
    imageCache.clear();
    console.log('All server image cache cleared');
  }
}

// Function to reset S3 config cache
export async function resetS3ConfigCache() {
  s3ConfigCache = null;
  configCacheTime = 0;
  console.log('S3 config cache cleared');
}

// Function to get S3 client with configuration from database
async function getS3Client() {
  // Return cached config if still valid
  if (s3ConfigCache && Date.now() - configCacheTime < CONFIG_CACHE_DURATION) {
    return s3ConfigCache;
  }

  let config = null;

  try {
    // Try to get configuration from database with timeout
    const configPromise = prisma.storageConfig.findFirst({
      where: { isDefault: true },
    });

    // Add timeout to prevent hanging
    config = await Promise.race([
      configPromise,
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Database query timeout')), 3000)
      )
    ]) as any;

  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      console.warn('Error fetching storage config from database, using environment variables:', (error as Error).message);
    }
    // Continue with environment variables fallback
  }

  // Import NodeHttpHandler dynamically to avoid server component issues
  let NodeHttpHandler;
  try {
    NodeHttpHandler = require('@smithy/node-http-handler').NodeHttpHandler;
  } catch (err) {
    // NodeHttpHandler is optional, continue without it
  }

  const s3Config = {
    client: new S3Client({
      region: config?.region || process.env.NEXT_PUBLIC_S3_REGION || 'fr-par-1',
      endpoint: config?.endpoint || process.env.NEXT_PUBLIC_S3_ENDPOINT || 'https://fr-par-1.linodeobjects.com',
      credentials: {
        accessKeyId: config?.accessKeyId || process.env.NEXT_PUBLIC_S3_ACCESS_KEY || '',
        secretAccessKey: config?.secretAccessKey || process.env.NEXT_PUBLIC_S3_SECRET_KEY || '',
      },
      forcePathStyle: true,
      maxAttempts: 3,
      retryMode: 'standard',
      requestHandler: NodeHttpHandler ? new NodeHttpHandler({
        connectionTimeout: 10000, // 10 seconds connection timeout
        socketTimeout: 60000,    // 60 seconds socket timeout
      }) : undefined
    }),
    bucketName: config?.bucketName || process.env.NEXT_PUBLIC_S3_BUCKET || 'mocky',
    endpoint: config?.endpoint || process.env.NEXT_PUBLIC_S3_ENDPOINT || 'https://fr-par-1.linodeobjects.com'
  };

  // Cache the configuration
  s3ConfigCache = s3Config;
  configCacheTime = Date.now();

  return s3Config;
};

// Function to fetch portfolio data by category from the portfolio metadata API
export async function getPortfolioByCategory(category: string): Promise<ImageItem[]> {
  try {
    // Check cache first (but only in production to avoid development issues)
    if (process.env.NODE_ENV === 'production') {
      const cached = imageCache.get(`portfolio-${category}`);
      if (cached && Date.now() - cached.timestamp < IMAGE_CACHE_TTL) {
        console.log(`Using cached portfolio images for category: ${category}`);
        return cached.data;
      }
    }

    // Construct the API URL
    const baseUrl = process.env.NEXT_PUBLIC_API_URL || process.env.VERCEL_URL 
      ? `https://${process.env.VERCEL_URL}` 
      : 'http://localhost:3000';
    
    const response = await fetch(`${baseUrl}/api/admin/portfolio?category=${category}`, {
      cache: 'no-store',
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache'
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch ${category} from portfolio: ${response.statusText}`);
    }

    const data = await response.json();
    
    if (!Array.isArray(data)) {
      console.error('Invalid response format: expected array');
      return [];
    }

    // Convert portfolio items to ImageItem format
    const images: ImageItem[] = data.map((item, index) => ({
      id: Number(item.id) || index + 1,
      url: item.imageSrc || item.src || '',
      src: item.imageSrc || item.src || '',
      alt: item.alt || item.title || `${category} image ${index + 1}`,
      title: item.title || item.alt || `${category} image ${index + 1}`,
      category: item.category || category,
      createdAt: item.createdAt || new Date().toISOString(),
      updatedAt: item.updatedAt || item.createdAt || new Date().toISOString()
    }));

    // Cache the results (only in production)
    if (process.env.NODE_ENV === 'production' && images.length > 0) {
      imageCache.set(`portfolio-${category}`, {
        data: images,
        timestamp: Date.now()
      });
      console.log(`Cached ${images.length} portfolio images for category: ${category}`);
    }

    return images;
  } catch (error) {
    console.error(`Error fetching ${category} from portfolio:`, error);
    return [];
  }
}

// Get images from S3 based on category (legacy function - kept for backward compatibility)
export async function getImagesFromServer(dirPath: string): Promise<ImageItem[]> {
  try {
    // For portfolio categories, use the new portfolio API
    const portfolioCategories = ['logos', 'cards', 'fliers', 'letterheads', 'profiles', 'branding'];
    const category = dirPath.split('/').pop() || '';
    
    if (portfolioCategories.includes(category)) {
      console.log(`Using portfolio API for category: ${category}`);
      return await getPortfolioByCategory(category);
    }

    // For non-portfolio categories, continue with the original S3 scanning logic
    // Check cache first (but only in production to avoid development issues)
    if (process.env.NODE_ENV === 'production') {
      const cached = imageCache.get(category);
      if (cached && Date.now() - cached.timestamp < IMAGE_CACHE_TTL) {
        console.log(`Using cached images for category: ${category}`);
        return cached.data;
      }
    }

    // Get S3 client and configuration
    const { client, bucketName, endpoint } = await getS3Client();

    if (!bucketName) {
      if (process.env.NODE_ENV === 'development') {
        console.warn('S3 bucket not configured');
      }
      return [];
    }

    if (process.env.NODE_ENV === 'development') {
      console.log('Fetching fresh images for category:', category);
    }

    // Construct the prefix to include images/portfolio/ directory
    const prefix = `images/portfolio/${category}/`;
    if (process.env.NODE_ENV === 'development') {
      console.log('Using S3 prefix:', prefix);
    }

    const command = new ListObjectsV2Command({
      Bucket: bucketName,
      Prefix: prefix,
      MaxKeys: 1000, // Increase max keys to ensure we get all images
    });

    const response = await client.send(command);
    if (process.env.NODE_ENV === 'development') {
      console.log(`Found ${response.Contents?.length || 0} items in S3 for category:`, category);
    }

    if (!response.Contents || response.Contents.length === 0) {
      if (process.env.NODE_ENV === 'development') {
        console.warn(`No images found in S3 for category: ${category}.`);
      }
      return [];
    }

    const images: ImageItem[] = [];

    if (response.Contents) {
      for (const item of response.Contents) {
        if (!item.Key) {
          continue;
        }

        // Skip if not an image or if it's just the folder itself
        if (!/\.(jpg|jpeg|png|webp|gif)$/i.test(item.Key) || item.Key === prefix) {
          continue;
        }

        // Extract filename from the key
        const filename = item.Key.split('/').pop() || '';

        // Construct the URL properly
        const imageUrl = `${endpoint}/${bucketName}/${item.Key}`;

        images.push({
          id: images.length + 1,
          src: imageUrl,
          url: imageUrl, // Include url property as well for compatibility
          alt: filename.replace(/\.(jpg|jpeg|png|webp|gif)$/i, '').replace(/-/g, ' '),
          size: item.Size || 0,
          createdAt: item.LastModified?.toISOString() || new Date().toISOString(),
          category: category
        });
      }
    }

    if (process.env.NODE_ENV === 'development') {
      console.log(`Processed ${images.length} valid images for category:`, category);
      if (images.length === 0) {
        console.warn(`No valid images found for category: ${category}. Check S3 path and configuration.`);
      }
    }

    // Cache the results (only in production)
    if (process.env.NODE_ENV === 'production' && images.length > 0) {
      imageCache.set(category, {
        data: images,
        timestamp: Date.now()
      });
      console.log(`Cached ${images.length} images for category: ${category}`);
    }

    return images;
  } catch (error) {
    console.error('Error fetching images from S3:', error);
    return [];
  }
}

// Add getTestimonialsData function for server-side testimonial fetching
export async function getTestimonialsData() {
  try {
    const dataPath = join(process.cwd(), 'src', 'data', 'testimonials.json');
    const fileContents = await readFile(dataPath, 'utf8');
    return JSON.parse(fileContents);
  } catch (error) {
    console.error('Error reading testimonials data:', error);
    return [];
  }
}