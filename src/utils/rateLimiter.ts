import { LRUCache } from 'lru-cache';
import { NextRequest } from 'next/server';
import { logger } from './logger';

// Rate limiting configuration
export interface RateLimitConfig {
  windowMs: number;
  maxRequests: number;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
  keyGenerator?: (request: NextRequest) => string;
  onLimitReached?: (key: string, request: NextRequest) => void;
}

// Rate limiting tiers for different endpoint types
export const RATE_LIMIT_CONFIGS: Record<string, RateLimitConfig> = {
  // Public endpoints - more restrictive
  public: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 100,
    skipSuccessfulRequests: false,
    skipFailedRequests: false,
  },
  
  // Admin content operations - moderate limits
  'admin-content': {
    windowMs: 5 * 60 * 1000, // 5 minutes
    maxRequests: 200,
    skipSuccessfulRequests: true,
    skipFailedRequests: false,
  },
  
  // Admin business operations - higher limits
  'admin-business': {
    windowMs: 5 * 60 * 1000, // 5 minutes
    maxRequests: 300,
    skipSuccessfulRequests: true,
    skipFailedRequests: false,
  },
  
  // Admin security operations - strict limits
  'admin-security': {
    windowMs: 10 * 60 * 1000, // 10 minutes
    maxRequests: 50,
    skipSuccessfulRequests: false,
    skipFailedRequests: false,
  },
  
  // Admin statistics - frequent access allowed
  'admin-stats': {
    windowMs: 1 * 60 * 1000, // 1 minute
    maxRequests: 60,
    skipSuccessfulRequests: true,
    skipFailedRequests: true,
  },
  
  // File uploads - very restrictive
  'file-upload': {
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 20,
    skipSuccessfulRequests: false,
    skipFailedRequests: false,
  },
  
  // Authentication attempts - very strict
  'auth': {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 10,
    skipSuccessfulRequests: true,
    skipFailedRequests: false,
  },
};

// Cache for tracking rate limits
const rateLimitCache = new LRUCache<string, { count: number; resetTime: number }>({
  max: 10000,
  ttl: 60 * 60 * 1000, // 1 hour TTL
});

// Get client identifier for rate limiting
export function getClientId(request: NextRequest): string {
  // Priority order: authenticated user > IP address > user agent hash
  const userId = request.headers.get('x-user-id');
  if (userId) {
    return `user:${userId}`;
  }
  
  const forwardedFor = request.headers.get('x-forwarded-for');
  const realIp = request.headers.get('x-real-ip');
  const ip = forwardedFor?.split(',')[0] || realIp || 'unknown';
  
  return `ip:${ip}`;
}

// Advanced rate limiting with sliding window
export class RateLimiter {
  private config: RateLimitConfig;
  private type: string;
  
  constructor(type: string, config?: Partial<RateLimitConfig>) {
    this.type = type;
    this.config = {
      ...RATE_LIMIT_CONFIGS[type] || RATE_LIMIT_CONFIGS.public,
      ...config
    };
  }
  
  async checkLimit(request: NextRequest, correlationId?: string): Promise<{
    allowed: boolean;
    limit: number;
    remaining: number;
    resetTime: number;
    retryAfter?: number;
  }> {
    const clientId = this.config.keyGenerator?.(request) || getClientId(request);
    const key = `${this.type}:${clientId}`;
    const now = Date.now();
    const windowStart = now - this.config.windowMs;
    
    // Get current window data
    let windowData = rateLimitCache.get(key);
    
    // Reset if window has expired
    if (!windowData || windowData.resetTime <= now) {
      windowData = {
        count: 0,
        resetTime: now + this.config.windowMs
      };
    }
    
    // Check if limit exceeded
    const allowed = windowData.count < this.config.maxRequests;
    
    if (allowed) {
      // Increment counter
      windowData.count++;
      rateLimitCache.set(key, windowData);
      
      logger.info('Rate limit check passed', {
        requestId: correlationId || 'unknown',
        action: 'rate_limit_check',
        metadata: {
          type: this.type,
          clientId,
          count: windowData.count,
          limit: this.config.maxRequests,
          allowed: true
        }
      });
    } else {
      // Log rate limit exceeded
      logger.security('rate_limit_exceeded', {
        requestId: correlationId || 'unknown',
        metadata: {
          type: this.type,
          clientId,
          count: windowData.count,
          limit: this.config.maxRequests,
          windowMs: this.config.windowMs
        },
        userAgent: request.headers.get('user-agent') || undefined,
        ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || undefined
      });
      
      // Call custom handler if provided
      this.config.onLimitReached?.(key, request);
    }
    
    return {
      allowed,
      limit: this.config.maxRequests,
      remaining: Math.max(0, this.config.maxRequests - windowData.count),
      resetTime: windowData.resetTime,
      retryAfter: allowed ? undefined : Math.ceil((windowData.resetTime - now) / 1000)
    };
  }
  
  // Get current status without incrementing
  async getStatus(request: NextRequest): Promise<{
    limit: number;
    remaining: number;
    resetTime: number;
  }> {
    const clientId = this.config.keyGenerator?.(request) || getClientId(request);
    const key = `${this.type}:${clientId}`;
    const now = Date.now();
    
    const windowData = rateLimitCache.get(key);
    
    if (!windowData || windowData.resetTime <= now) {
      return {
        limit: this.config.maxRequests,
        remaining: this.config.maxRequests,
        resetTime: now + this.config.windowMs
      };
    }
    
    return {
      limit: this.config.maxRequests,
      remaining: Math.max(0, this.config.maxRequests - windowData.count),
      resetTime: windowData.resetTime
    };
  }
  
  // Clear rate limit for a specific client (admin override)
  async clearLimit(request: NextRequest): Promise<void> {
    const clientId = this.config.keyGenerator?.(request) || getClientId(request);
    const key = `${this.type}:${clientId}`;
    rateLimitCache.delete(key);
    
    logger.security('suspicious_activity', {
      requestId: 'system',
      metadata: {
        type: this.type,
        clientId,
        key,
        action: 'rate_limit_cleared'
      }
    });
  }
}

// Convenience functions for common rate limiting scenarios
export const rateLimiters = {
  public: new RateLimiter('public'),
  adminContent: new RateLimiter('admin-content'),
  adminBusiness: new RateLimiter('admin-business'),
  adminSecurity: new RateLimiter('admin-security'),
  adminStats: new RateLimiter('admin-stats'),
  fileUpload: new RateLimiter('file-upload'),
  auth: new RateLimiter('auth'),
};

// Middleware helper for rate limiting
export async function applyRateLimit(
  request: NextRequest,
  type: string,
  correlationId?: string
): Promise<Response | null> {
  const limiter = rateLimiters[type as keyof typeof rateLimiters] || rateLimiters.public;
  const result = await limiter.checkLimit(request, correlationId);
  
  if (!result.allowed) {
    const response = new Response(
      JSON.stringify({
        success: false,
        error: 'Rate limit exceeded',
        code: 'RATE_LIMIT_EXCEEDED',
        message: `Too many requests. Try again in ${result.retryAfter} seconds.`,
        details: {
          limit: result.limit,
          remaining: result.remaining,
          resetTime: result.resetTime,
          retryAfter: result.retryAfter
        }
      }),
      {
        status: 429,
        headers: {
          'Content-Type': 'application/json',
          'X-RateLimit-Limit': result.limit.toString(),
          'X-RateLimit-Remaining': result.remaining.toString(),
          'X-RateLimit-Reset': result.resetTime.toString(),
          'Retry-After': result.retryAfter?.toString() || '60',
        },
      }
    );
    
    return response;
  }
  
  return null; // No rate limiting applied
}

// Rate limiting decorator for API routes
export function withRateLimit(type: string) {
  return function<T extends Function>(target: T): T {
    return (async function(request: NextRequest, ...args: any[]) {
      const correlationId = request.headers.get('x-correlation-id') || 'unknown';
      const rateLimitResponse = await applyRateLimit(request, type, correlationId);
      
      if (rateLimitResponse) {
        return rateLimitResponse;
      }
      
      return await (target as any)(request, ...args);
    }) as any;
  };
}

// Get rate limit statistics for monitoring
export function getRateLimitStats(): {
  totalKeys: number;
  cacheSize: number;
  hitRate: number;
  configs: typeof RATE_LIMIT_CONFIGS;
} {
  return {
    totalKeys: rateLimitCache.size,
    cacheSize: rateLimitCache.calculatedSize,
    hitRate: rateLimitCache.calculatedSize > 0 ? 
      (rateLimitCache.size / rateLimitCache.calculatedSize) * 100 : 0,
    configs: RATE_LIMIT_CONFIGS
  };
} 