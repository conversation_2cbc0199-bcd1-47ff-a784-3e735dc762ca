import { S3Client, PutObjectCommand, DeleteObjectCommand, GetObjectCommand, ListObjectsV2Command, HeadObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { promises as fs } from 'fs';
import path from 'path';
import { getDefaultStorageConfig } from '@/lib/storageConfig';
import { normalizeImageUrl } from '@/utils/imageUtils';

// Valid image extensions
const IMAGE_EXTENSIONS = ['.jpg', '.jpeg', '.png', '.webp', '.svg', '.gif'];

// Get S3 client with the current configuration
async function getS3Client() {
  const config = await getDefaultStorageConfig();

  if (!config) {
    throw new Error('No storage configuration found');
  }

  return new S3Client({
    region: config.region,
    endpoint: config.endpoint,
    credentials: {
      accessKeyId: config.accessKey,
      secretAccessKey: config.secretKey,
    },
    forcePathStyle: true // Required for some S3-compatible storage
  });
}

// Get the current bucket name
async function getBucketName(): Promise<string> {
  const config = await getDefaultStorageConfig();
  return config?.bucketName || process.env.NEXT_PUBLIC_S3_BUCKET || 'mocky';
}

/**
 * Gets a list of images from the specified S3 path
 */
export async function getImagesFromS3(path: string) {
  // Remove leading slash if exists
  const normalizedPath = path.startsWith('/') ? path.slice(1) : path;

  // Ensure the path ends with a trailing slash for S3 directory traversal
  const dirPath = normalizedPath.endsWith('/') ? normalizedPath : `${normalizedPath}/`;

  try {
    const s3Client = await getS3Client();
    const bucketName = await getBucketName();
    const config = await getDefaultStorageConfig();

    const response = await s3Client.send(new ListObjectsV2Command({
      Bucket: bucketName,
      Prefix: dirPath,
    }));

    if (!response.Contents) {
      return [];
    }

    // Filter out directories and process image files
    const files = response.Contents
      .filter(item => !item.Key?.endsWith('/')) // Filter out directory objects
      .filter(item => {
        const key = item.Key || '';
        const ext = key.split('.').pop()?.toLowerCase();
        return ['jpg', 'jpeg', 'png', 'webp', 'gif', 'svg'].includes(ext || '');
      })
      .map((item, index) => {
        const key = item.Key || '';
        const filename = key.split('/').pop() || '';
        const displayName = filename
          .replace(/\.(jpg|jpeg|png|webp|gif|svg)$/i, '')
          .replace(/[-_]/g, ' ')
          .replace(/\b\w/g, (char) => char.toUpperCase());

        let category = 'uncategorized';
        if (key.includes('logos')) category = 'logo';
        else if (key.includes('branding')) category = 'graphic';
        else if (key.includes('fliers')) category = 'flier';
        else if (key.includes('website')) category = 'website';
        else if (key.includes('profile')) category = 'profile';
        else if (key.includes('card')) category = 'card';
        else if (key.includes('letterhead')) category = 'letterhead';

        // Generate the full S3 URL and normalize it
        const src = normalizeImageUrl(`${config?.endpoint}/${bucketName}/${key}`);

        return {
          id: index + 1,
          src,
          url: src, // Add url property for compatibility with different components
          alt: displayName,
          title: displayName,
          category,
          size: item.Size || 0,
          createdAt: item.LastModified?.toISOString() || new Date().toISOString(),
          updatedAt: item.LastModified?.toISOString() || new Date().toISOString(),
        };
      });

    return files;
  } catch (error) {
    console.error(`Error fetching images from S3 path ${dirPath}:`, error);
    return [];
  }
}

/**
 * Deletes local images after confirming they exist in S3
 */
export async function deleteLocalImages(imagePath: string): Promise<{
  total: number;
  existInS3: number;
  deleted: number;
  missing: number;
}> {
  // Remove leading slash if exists
  const normalizedPath = imagePath.startsWith('/') ? imagePath.slice(1) : imagePath;

  // Build the local path
  const publicDir = path.join(process.cwd(), 'public');
  const localPath = path.join(publicDir, normalizedPath);

  try {
    // Check if local directory exists
    const stats = await fs.stat(localPath);
    if (!stats.isDirectory()) {
      throw new Error(`${localPath} is not a directory`);
    }

    // List files in local directory
    const files = await fs.readdir(localPath);

    // Filter for image files
    const imageFiles = files.filter(file => {
      const ext = path.extname(file).toLowerCase();
      return IMAGE_EXTENSIONS.includes(ext);
    });

    console.log(`Found ${imageFiles.length} image files in ${localPath}`);

    // Check which images exist in S3 and delete them
    const s3Client = await getS3Client();
    const bucketName = await getBucketName();

    let existInS3 = 0;
    let missingInS3 = 0;
    let deletedCount = 0;

    for (const file of imageFiles) {
      const s3Key = `${normalizedPath}/${file}`;

      try {
        // Check if file exists in S3
        await s3Client.send(new HeadObjectCommand({
          Bucket: bucketName,
          Key: s3Key
        }));

        console.log(`File ${file} exists in S3`);
        existInS3++;

        // Delete local file
        const localFilePath = path.join(localPath, file);
        await fs.unlink(localFilePath);
        console.log(`Deleted local file: ${localFilePath}`);
        deletedCount++;
      } catch (error) {
        console.log(`File ${file} does not exist in S3`);
        missingInS3++;
      }
    }

    return {
      total: imageFiles.length,
      existInS3,
      deleted: deletedCount,
      missing: missingInS3
    };
  } catch (error) {
    console.error(`Error deleting local images from ${localPath}:`, error);
    throw error;
  }
}

/**
 * Checks if an S3 object exists
 */
export async function checkS3ObjectExists(key: string): Promise<boolean> {
  try {
    // Remove leading slash if it exists
    const normalizedKey = key.startsWith('/') ? key.slice(1) : key;

    const s3Client = await getS3Client();
    const bucketName = await getBucketName();

    await s3Client.send(new HeadObjectCommand({
      Bucket: bucketName,
      Key: normalizedKey,
    }));

    return true;
  } catch (error) {
    return false;
  }
}

/**
 * Generates a URL for an S3 object
 */
export async function getS3Url(key: string): Promise<string> {
  // Remove leading slash if it exists
  const normalizedKey = key.startsWith('/') ? key.slice(1) : key;

  const config = await getDefaultStorageConfig();
  const bucketName = await getBucketName();

  // Create the URL and normalize it to ensure it's properly formatted
  const url = `${config?.endpoint}/${bucketName}/${normalizedKey}`;
  return normalizeImageUrl(url);
}

export async function uploadToS3(file: Buffer, key: string, contentType: string) {
  const s3Client = await getS3Client();
  const bucketName = await getBucketName();
  const config = await getDefaultStorageConfig();

  const command = new PutObjectCommand({
    Bucket: bucketName,
    Key: key,
    Body: file,
    ContentType: contentType,
    ACL: 'public-read',
  });

  await s3Client.send(command);
  const url = `${config?.endpoint}/${bucketName}/${key}`;
  return normalizeImageUrl(url);
}

export async function deleteFromS3(key: string) {
  const s3Client = await getS3Client();
  const bucketName = await getBucketName();

  const command = new DeleteObjectCommand({
    Bucket: bucketName,
    Key: key,
  });

  await s3Client.send(command);
}

export async function getSignedS3Url(key: string, expiresIn = 3600) {
  const s3Client = await getS3Client();
  const bucketName = await getBucketName();

  const command = new GetObjectCommand({
    Bucket: bucketName,
    Key: key,
  });

  return getSignedUrl(s3Client, command, { expiresIn });
}