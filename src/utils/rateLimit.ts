import { NextRequest } from 'next/server';
import { RateLimiter } from './rateLimiter';

// Simple rate limiting wrapper for specific use cases
export async function rateLimit(
  request: NextRequest, 
  options: {
    uniqueTokenPerInterval: number;
    interval: number;
  }
): Promise<{
  success: boolean;
  retryAfter?: number;
}> {
  try {
    const rateLimiter = new RateLimiter('file-upload', {
      windowMs: options.interval,
      maxRequests: Math.floor(options.uniqueTokenPerInterval / 10), // More restrictive for uploads
    });

    const result = await rateLimiter.checkLimit(request);
    
    return {
      success: result.allowed,
      retryAfter: result.retryAfter ? result.retryAfter * 1000 : undefined // Convert to milliseconds
    };
  } catch (error) {
    console.error('Rate limiting error:', error);
    // Fail open - allow the request if rate limiting fails
    return { success: true };
  }
} 