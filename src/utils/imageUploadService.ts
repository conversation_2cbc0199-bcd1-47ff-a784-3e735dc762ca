// Enhanced image upload service with comprehensive error handling and CSRF protection

export interface UploadProgress {
  loaded: number;
  total: number;
  percentage: number;
  stage: 'preparing' | 'uploading' | 'processing' | 'complete';
  message: string;
}

export interface UploadResult {
  success: boolean;
  url?: string;
  filename?: string;
  size?: number;
  type?: string;
  warnings?: string[];
  error?: string;
  uploadTime?: number;
}

export interface MultiUploadResult {
  success: boolean;
  results: UploadResult[];
  successful: UploadResult[];
  failed: UploadResult[];
  stats: {
    totalFiles: number;
    successfulFiles: number;
    failedFiles: number;
    totalTime: number;
    totalSize: number;
  };
}

export interface UploadOptions {
  category?: string;
  maxRetries?: number;
  timeout?: number;
  onProgress?: (progress: UploadProgress) => void;
  onFileProgress?: (fileIndex: number, progress: UploadProgress) => void;
  csrfToken?: string | null;
}

export class ImageUploadService {
  private static readonly MAX_FILE_SIZE = 100 * 1024 * 1024; // 100MB
  private static readonly MAX_TOTAL_SIZE = 250 * 1024 * 1024; // 250MB
  private static readonly VALID_TYPES = [
    'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 
    'image/webp', 'image/avif', 'image/svg+xml'
  ];
  
  private static csrfToken: string | null = null;
  private static csrfTokenExpiry: number = 0;

  /**
   * Get or refresh CSRF token
   */
  private static async getCsrfToken(): Promise<string> {
    const now = Date.now();
    
    // Return cached token if still valid (with 5-minute buffer)
    if (this.csrfToken && this.csrfTokenExpiry > now + 300000) {
      return this.csrfToken;
    }

    try {
      const response = await fetch('/api/auth/csrf', {
        method: 'GET',
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error(`Failed to get CSRF token: ${response.status}`);
      }

      const data = await response.json();
      if (!data.csrfToken) {
        throw new Error('No CSRF token received');
      }

      this.csrfToken = data.csrfToken;
      this.csrfTokenExpiry = now + 3600000; // 1 hour
      
      return this.csrfToken;
    } catch (error) {
      console.error('Failed to fetch CSRF token:', error);
      throw new Error('Unable to secure the upload request. Please refresh the page and try again.');
    }
  }

  /**
   * Validate files before upload
   */
  private static validateFiles(files: File[]): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (files.length === 0) {
      errors.push('No files selected');
      return { isValid: false, errors };
    }

    if (files.length > 3) {
      errors.push('Maximum 3 files allowed');
    }

    let totalSize = 0;
    files.forEach((file, index) => {
      // Check individual file size
      if (file.size > this.MAX_FILE_SIZE) {
        errors.push(`File ${index + 1} (${file.name}) is too large (${Math.round(file.size / 1024 / 1024)}MB). Maximum size is ${this.MAX_FILE_SIZE / 1024 / 1024}MB`);
      }

      // Check file type
      if (!this.VALID_TYPES.includes(file.type)) {
        errors.push(`File ${index + 1} (${file.name}) has an invalid format. Allowed formats: JPG, PNG, GIF, WebP, AVIF, SVG`);
      }

      // Check for empty files
      if (file.size === 0) {
        errors.push(`File ${index + 1} (${file.name}) is empty`);
      }

      totalSize += file.size;
    });

    // Check total size
    if (totalSize > this.MAX_TOTAL_SIZE) {
      errors.push(`Total file size (${Math.round(totalSize / 1024 / 1024)}MB) exceeds maximum allowed (${this.MAX_TOTAL_SIZE / 1024 / 1024}MB)`);
    }

    return { isValid: errors.length === 0, errors };
  }

  /**
   * Upload single file
   */
  static async uploadSingle(
    file: File, 
    options: UploadOptions = {}
  ): Promise<UploadResult> {
    const startTime = Date.now();
    const { 
      category = 'catalogue', 
      maxRetries = 3, 
      timeout = 600000,
      onProgress,
      csrfToken
    } = options;

    // Validate file
    const validation = this.validateFiles([file]);
    if (!validation.isValid) {
      return {
        success: false,
        error: validation.errors.join(', ')
      };
    }

    // Get CSRF token
    let token: string;
    if (csrfToken && typeof csrfToken === 'string') {
      token = csrfToken;
    } else {
      try {
        token = await this.getCsrfToken();
      } catch (error) {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Failed to get security token'
        };
      }
    }

    // Retry logic
    let lastError: Error | null = null;
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        if (attempt > 1) {
          onProgress?.({
            loaded: 0,
            total: file.size,
            percentage: 0,
            stage: 'preparing',
            message: `Retry attempt ${attempt}/${maxRetries}...`
          });
          
          // Exponential backoff
          await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt - 1) * 1000));
        }

        const result = await this.performUpload(file, category, token, timeout, onProgress);
        
        return {
          ...result,
          uploadTime: Date.now() - startTime
        };

      } catch (error) {
        lastError = error as Error;
        console.error(`Upload attempt ${attempt} failed:`, error);
        
        if (attempt === maxRetries) {
          break;
        }
      }
    }

    return {
      success: false,
      error: lastError?.message || 'Upload failed after multiple attempts',
      uploadTime: Date.now() - startTime
    };
  }

  /**
   * Upload multiple files
   */
  static async uploadMultiple(
    files: File[], 
    options: UploadOptions = {}
  ): Promise<MultiUploadResult> {
    const startTime = Date.now();
    const { onProgress, onFileProgress } = options;

    // Validate all files
    const validation = this.validateFiles(files);
    if (!validation.isValid) {
      return {
        success: false,
        results: files.map(file => ({
          success: false,
          error: validation.errors.join(', '),
          filename: file.name,
          size: file.size,
          type: file.type
        })),
        successful: [],
        failed: files.map(file => ({
          success: false,
          error: validation.errors.join(', '),
          filename: file.name,
          size: file.size,
          type: file.type
        })),
        stats: {
          totalFiles: files.length,
          successfulFiles: 0,
          failedFiles: files.length,
          totalTime: Date.now() - startTime,
          totalSize: files.reduce((sum, file) => sum + file.size, 0)
        }
      };
    }

    // Get CSRF token once for all uploads
    let csrfToken: string;
    try {
      csrfToken = await this.getCsrfToken();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to get security token';
      return {
        success: false,
        results: files.map(file => ({
          success: false,
          error: errorMessage,
          filename: file.name,
          size: file.size,
          type: file.type
        })),
        successful: [],
        failed: files.map(file => ({
          success: false,
          error: errorMessage,
          filename: file.name,
          size: file.size,
          type: file.type
        })),
        stats: {
          totalFiles: files.length,
          successfulFiles: 0,
          failedFiles: files.length,
          totalTime: Date.now() - startTime,
          totalSize: files.reduce((sum, file) => sum + file.size, 0)
        }
      };
    }

    // Upload files sequentially to avoid overwhelming the server
    const results: UploadResult[] = [];
    let completedFiles = 0;

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      
      // Update overall progress
      onProgress?.({
        loaded: completedFiles,
        total: files.length,
        percentage: (completedFiles / files.length) * 100,
        stage: 'uploading',
        message: `Uploading file ${i + 1} of ${files.length}: ${file.name}`
      });

      // Upload individual file
      const result = await this.uploadSingle(file, {
        ...options,
        csrfToken,
        onProgress: (progress) => {
          onFileProgress?.(i, progress);
          
          // Update overall progress
          const overallProgress = (completedFiles + progress.percentage / 100) / files.length * 100;
          onProgress?.({
            loaded: completedFiles + progress.loaded,
            total: files.reduce((sum, f) => sum + f.size, 0),
            percentage: overallProgress,
            stage: progress.stage,
            message: `File ${i + 1}/${files.length}: ${progress.message}`
          });
        }
      });

      results.push(result);
      completedFiles++;
    }

    // Separate successful and failed uploads
    const successful = results.filter(r => r.success);
    const failed = results.filter(r => !r.success);

    const totalTime = Date.now() - startTime;
    const totalSize = files.reduce((sum, file) => sum + file.size, 0);

    return {
      success: successful.length > 0,
      results,
      successful,
      failed,
      stats: {
        totalFiles: files.length,
        successfulFiles: successful.length,
        failedFiles: failed.length,
        totalTime,
        totalSize
      }
    };
  }

  /**
   * Perform the actual upload
   */
  private static async performUpload(
    file: File,
    category: string,
    csrfToken: string,
    timeout: number,
    onProgress?: (progress: UploadProgress) => void
  ): Promise<UploadResult> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('category', category);

    onProgress?.({
      loaded: 0,
      total: file.size,
      percentage: 0,
      stage: 'preparing',
      message: 'Preparing upload...'
    });

    // Create timeout promise
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => {
        reject(new Error(`Upload timeout after ${timeout / 1000} seconds`));
      }, timeout);
    });

    // Create upload promise with progress tracking
    const uploadPromise = new Promise<UploadResult>((resolve, reject) => {
      const xhr = new XMLHttpRequest();

      // Track upload progress
      xhr.upload.addEventListener('progress', (event) => {
        if (event.lengthComputable) {
          const percentage = (event.loaded / event.total) * 100;
          onProgress?.({
            loaded: event.loaded,
            total: event.total,
            percentage,
            stage: 'uploading',
            message: `Uploading... ${Math.round(percentage)}%`
          });
        }
      });

      xhr.addEventListener('load', () => {
        onProgress?.({
          loaded: file.size,
          total: file.size,
          percentage: 100,
          stage: 'processing',
          message: 'Processing upload...'
        });

        try {
          if (xhr.status >= 200 && xhr.status < 300) {
            const response = JSON.parse(xhr.responseText);
            
            if (response.success !== false && response.url) {
              onProgress?.({
                loaded: file.size,
                total: file.size,
                percentage: 100,
                stage: 'complete',
                message: 'Upload complete!'
              });
              
              resolve({
                success: true,
                url: response.url,
                filename: response.filename || file.name,
                size: file.size,
                type: file.type,
                warnings: response.warnings
              });
            } else {
              reject(new Error(response.error || 'Upload failed'));
            }
          } else {
            reject(new Error(`HTTP ${xhr.status}: ${xhr.statusText}`));
          }
        } catch (error) {
          reject(new Error('Failed to parse server response'));
        }
      });

      xhr.addEventListener('error', () => {
        reject(new Error('Network error during upload'));
      });

      xhr.addEventListener('abort', () => {
        reject(new Error('Upload was aborted'));
      });

      // Start upload
      xhr.open('POST', '/api/upload');
      xhr.setRequestHeader('X-CSRF-Token', csrfToken);
      xhr.send(formData);
    });

    try {
      // Race upload against timeout
      return await Promise.race([uploadPromise, timeoutPromise]);
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Unknown upload error');
    }
  }

  /**
   * Get upload progress message for UI display
   */
  static getProgressMessage(progress: UploadProgress): string {
    switch (progress.stage) {
      case 'preparing':
        return `Preparing ${progress.message || 'upload'}...`;
      case 'uploading':
        return `Uploading... ${Math.round(progress.percentage)}%`;
      case 'processing':
        return 'Processing upload...';
      case 'complete':
        return 'Upload complete!';
      default:
        return progress.message || 'Uploading...';
    }
  }

  /**
   * Clean error messages for user display
   */
  static cleanErrorMessage(error: string): string {
    return error
      .replace('AI image uploads failed', 'Image upload failed')
      .replace('Please check your commission privacy again', 'Please try again')
      .replace(/key is not defined/g, 'Server configuration error')
      .replace(/undefined/g, 'unknown')
      .replace(/\[object Object\]/g, 'server error');
  }
} 