/**
 * Utility functions for handling image URLs and paths
 */

/**
 * Ensures a consistent format for S3 image URLs
 * This helps prevent issues with Next.js Image component and S3 URLs
 *
 * @param url The original image URL
 * @returns A properly formatted URL
 */
export function normalizeImageUrl(url: string): string {
  if (!url) return '/images/placeholder.jpg';

  try {
    // If it's already a relative URL, return as is
    if (url.startsWith('/')) {
      return url;
    }

    // Handle Linode Object Storage URLs without protocol
    if (url.includes('linodeobjects.com') && !url.startsWith('http')) {
      // Add https protocol if missing
      const normalizedUrl = `https://${url}`;
      return normalizedUrl;
    }

    // Convert HTTP to HTTPS for Linode URLs
    if (url.startsWith('http://') && url.includes('linodeobjects.com')) {
      const secureUrl = url.replace('http://', 'https://');
      return secureUrl;
    }

    // Handle URLs with double slashes (except after protocol)
    if (url.includes('//')) {
      // Fix double slashes in the path portion only (not in protocol)
      const fixedUrl = url.replace(/([^:])\/\/+/g, '$1/');

      // If it's a Linode URL, also ensure it has a protocol
      if (fixedUrl.includes('linodeobjects.com') && !fixedUrl.startsWith('http')) {
        return `https://${fixedUrl}`;
      }

      return fixedUrl;
    }

    // Try to parse the URL to validate and normalize it
    try {
      const parsedUrl = new URL(url);

      // Check if it's an S3/Linode URL
      if (parsedUrl.hostname.includes('linodeobjects.com')) {
        // Fix any double slashes in the path
        const fixedUrl = url.replace(/([^:]\/)\/+/g, '$1');
        return fixedUrl;
      }

      // Return the original URL for other valid URLs
      return url;
    } catch (parseError) {
      // URL parsing failed, but we can still try to fix common issues
      if (process.env.NODE_ENV === 'development') {
        console.warn('URL parsing failed, attempting alternative fixes:', parseError);
      }

      // If it looks like a domain without protocol, add https
      if (url.includes('.com') || url.includes('.net') || url.includes('.org')) {
        const withProtocol = `https://${url}`;
        return withProtocol;
      }

      // If it starts with a domain segment, assume it's a URL missing protocol
      if (/^[a-zA-Z0-9-]+\.[a-zA-Z0-9-]+/.test(url)) {
        const withProtocol = `https://${url}`;
        return withProtocol;
      }

      // If it looks like a path, return as is
      if (url.includes('/')) {
        return url;
      }
    }

    // Return the original URL if we couldn't normalize it
    return url;
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      console.error('Error normalizing image URL:', error);
      console.error('Problematic URL:', url);
    }

    // If URL parsing fails, check if it looks like a path
    if (typeof url === 'string') {
      // If it's a Linode URL without protocol, add https
      if (url.includes('linodeobjects.com') && !url.startsWith('http')) {
        return `https://${url}`;
      }

      // Otherwise return as is if it looks like a valid path or URL
      if (url.startsWith('/') || url.startsWith('http')) {
        return url;
      }
    }

    // Return a fallback for invalid URLs
    return '/images/placeholder.jpg';
  }
}

/**
 * Checks if an image URL is from S3 storage
 *
 * @param url The image URL to check
 * @returns Boolean indicating if the URL is from S3
 */
export function isS3ImageUrl(url: string): boolean {
  if (!url) return false;

  // Direct string check for Linode URLs without protocol
  if (url.includes('linodeobjects.com')) {
    return true;
  }

  try {
    const parsedUrl = new URL(url);
    return parsedUrl.hostname.includes('linodeobjects.com');
  } catch {
    return false;
  }
}

/**
 * Gets the appropriate image loader based on the URL
 * This can be used to optimize image loading for different sources
 *
 * @param url The image URL
 * @returns The appropriate loader configuration
 */
export function getImageLoader(url: string) {
  if (isS3ImageUrl(url)) {
    return {
      loader: 'custom',
      loaderUrl: `/_next/image?url=${encodeURIComponent(url)}&w=3840&q=75`,
    };
  }

  return {
    loader: 'default',
  };
}

export function getImageKeyFromUrl(url: string): string {
  try {
    const urlObj = new URL(url);
    const pathParts = urlObj.pathname.split('/');
    return pathParts.slice(2).join('/'); // Remove bucket name and get the rest of the path
  } catch (error) {
    console.error('Error parsing image URL:', error);
    return '';
  }
}

export function extractS3KeyFromUrl(url: string): string | null {
  if (!url) return null;
  
  // If it's already a key (starts with images/), return as is
  if (url.startsWith('images/')) {
    return url;
  }
  
  // If it's a full S3 URL, extract the key
  if (url.includes('linodeobjects.com') || url.includes('amazonaws.com')) {
    const urlParts = url.split('/');
    const imagesIndex = urlParts.findIndex((part: string) => part === 'images');
    if (imagesIndex !== -1) {
      return urlParts.slice(imagesIndex).join('/');
    }
  }
  
  // If it starts with portfolio/, convert to new format
  if (url.startsWith('portfolio/')) {
    return `images/${url}`;
  }
  
  return null;
}

export function getS3ImageUrl(imageKey: string): string {
  if (!imageKey) {
    return '/images/placeholder.svg';
  }

  // Get S3 configuration with fallbacks
  const endpoint = process.env.NEXT_PUBLIC_S3_ENDPOINT || 'https://fr-par-1.linodeobjects.com';
  const bucketName = process.env.NEXT_PUBLIC_S3_BUCKET || 'mocky2';
  
  // Debug logging for development
  if (process.env.NODE_ENV === 'development') {
    console.log('S3 Config:', { endpoint, bucketName, imageKey });
  }
  
  // Clean the imageKey to ensure no double slashes
  const cleanImageKey = imageKey.startsWith('/') ? imageKey.substring(1) : imageKey;
  
  // Construct the full URL
  const fullUrl = `${endpoint}/${bucketName}/${cleanImageKey}`;
  
  // Debug logging
  if (process.env.NODE_ENV === 'development') {
    console.log('Generated S3 URL:', fullUrl);
  }
  
  // Normalize the URL to fix any potential issues
  return normalizeImageUrl(fullUrl);
} 