import { z } from 'zod';
import DOMPurify from 'isomorphic-dompurify';
import { NextRequest } from 'next/server';
import { logger } from './logger';
import { ValidationError } from '@/errors/ApiErrors';

// Common validation patterns
export const VALIDATION_PATTERNS = {
  // Basic patterns
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PHONE: /^\+?[\d\s\-\(\)]+$/,
  URL: /^https?:\/\/.+/,
  UUID: /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i,
  
  // Security patterns
  SQL_INJECTION: /('|(\')|;|--|\/\*|\*\/|xp_|sp_|exec|execute|union|select|insert|update|delete|drop|create|alter|declare|cast|char|varchar|nchar|nvarchar|binary|varbinary)/i,
  XSS_SCRIPT: /<script[^>]*>.*?<\/script>/gi,
  XSS_ATTRIBUTES: /on\w+\s*=|javascript:|data:text\/html/gi,
  
  // Content patterns
  HTML_TAGS: /<[^>]+>/g,
  WHITESPACE: /^\s+|\s+$/g,
  MULTIPLE_WHITESPACE: /\s+/g,
  
  // File patterns
  IMAGE_FILE: /\.(jpg|jpeg|png|gif|webp|svg)$/i,
  DOCUMENT_FILE: /\.(pdf|doc|docx|txt|rtf)$/i,
  SAFE_FILENAME: /^[a-zA-Z0-9._-]+$/,
};

// Dangerous patterns that should be blocked
const DANGEROUS_PATTERNS = [
  // SQL injection attempts
  /union\s+select/i,
  /insert\s+into/i,
  /delete\s+from/i,
  /drop\s+table/i,
  /exec\s*\(/i,
  
  // XSS attempts
  /<script/i,
  /javascript:/i,
  /on\w+\s*=/i,
  /data:text\/html/i,
  
  // Directory traversal
  /\.\.\//g,
  /\.\.\\/g,
  
  // Command injection
  /;\s*(rm|del|format|shutdown)/i,
  /\|\s*(rm|del|format|shutdown)/i,
  
  // LDAP injection
  /\*\)\(\|/g,
  /\)\(\&/g,
];

// Content type validation
export const CONTENT_TYPES = {
  JSON: 'application/json',
  FORM: 'application/x-www-form-urlencoded',
  MULTIPART: 'multipart/form-data',
  TEXT: 'text/plain',
  XML: 'application/xml',
} as const;

// File size limits (in bytes)
export const FILE_SIZE_LIMITS = {
  IMAGE: 5 * 1024 * 1024,    // 5MB
  DOCUMENT: 10 * 1024 * 1024, // 10MB
  AVATAR: 2 * 1024 * 1024,    // 2MB
  BACKUP: 100 * 1024 * 1024,  // 100MB
} as const;

/**
 * Sanitize string input by removing dangerous patterns
 */
export function sanitizeString(input: string): string {
  if (typeof input !== 'string') {
    return String(input);
  }
  
  let sanitized = input;
  
  // Remove dangerous patterns
  DANGEROUS_PATTERNS.forEach(pattern => {
    sanitized = sanitized.replace(pattern, '');
  });
  
  // Trim whitespace
  sanitized = sanitized.replace(VALIDATION_PATTERNS.WHITESPACE, '');
  
  // Normalize multiple whitespace
  sanitized = sanitized.replace(VALIDATION_PATTERNS.MULTIPLE_WHITESPACE, ' ');
  
  return sanitized;
}

/**
 * Sanitize HTML content while preserving safe formatting
 */
export function sanitizeHTML(input: string, allowedTags?: string[]): string {
  if (typeof input !== 'string') {
    return '';
  }
  
  const defaultAllowedTags = ['p', 'br', 'strong', 'em', 'u', 'a', 'ul', 'ol', 'li', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'];
  const tags = allowedTags || defaultAllowedTags;
  
  // Configure DOMPurify for safe HTML
  const config = {
    ALLOWED_TAGS: tags,
    ALLOWED_ATTR: ['href', 'title', 'alt', 'target'],
    ALLOWED_URI_REGEXP: /^(?:(?:(?:f|ht)tps?|mailto|tel|sms):|[^a-z]|[a-z+.-]+(?:[^a-z+.-:]|$))/i,
    RETURN_DOM: false,
    RETURN_DOM_FRAGMENT: false,
    RETURN_TRUSTED_TYPE: false
  };
  
  return DOMPurify.sanitize(input, config);
}

/**
 * Validate and sanitize file upload
 */
export function validateFile(
  file: File,
  options: {
    maxSize?: number;
    allowedTypes?: string[];
    allowedExtensions?: string[];
    requireExtension?: boolean;
  } = {}
): { valid: boolean; error?: string; sanitizedName?: string } {
  const {
    maxSize = FILE_SIZE_LIMITS.DOCUMENT,
    allowedTypes = [],
    allowedExtensions = [],
    requireExtension = true
  } = options;
  
  // Check file size
  if (file.size > maxSize) {
    return {
      valid: false,
      error: `File size exceeds limit of ${Math.round(maxSize / 1024 / 1024)}MB`
    };
  }
  
  // Check file type
  if (allowedTypes.length > 0 && !allowedTypes.includes(file.type)) {
    return {
      valid: false,
      error: `File type ${file.type} is not allowed`
    };
  }
  
  // Check file extension
  const extension = file.name.toLowerCase().split('.').pop();
  if (requireExtension && !extension) {
    return {
      valid: false,
      error: 'File must have an extension'
    };
  }
  
  if (allowedExtensions.length > 0 && (!extension || !allowedExtensions.includes(extension))) {
    return {
      valid: false,
      error: `File extension ${extension} is not allowed`
    };
  }
  
  // Sanitize filename
  const sanitizedName = file.name
    .replace(/[^a-zA-Z0-9._-]/g, '_')
    .replace(/_{2,}/g, '_')
    .substring(0, 255);
  
  return {
    valid: true,
    sanitizedName
  };
}

/**
 * Check for SQL injection patterns
 */
export function detectSQLInjection(input: string): boolean {
  if (typeof input !== 'string') {
    return false;
  }
  
  const sqlPatterns = [
    /union\s+select/i,
    /insert\s+into/i,
    /delete\s+from/i,
    /drop\s+table/i,
    /exec\s*\(/i,
    /xp_cmdshell/i,
    /sp_executesql/i,
    /'.*?--/,
    /'.*?\/\*/,
    /;.*?(drop|delete|insert|update|create|alter)/i
  ];
  
  return sqlPatterns.some(pattern => pattern.test(input));
}

/**
 * Check for XSS patterns
 */
export function detectXSS(input: string): boolean {
  if (typeof input !== 'string') {
    return false;
  }
  
  const xssPatterns = [
    /<script[^>]*>.*?<\/script>/gi,
    /javascript:/i,
    /on\w+\s*=/i,
    /data:text\/html/i,
    /<iframe/i,
    /<object/i,
    /<embed/i,
    /<link/i,
    /<meta/i,
    /expression\s*\(/i,
    /vbscript:/i,
    /javascript:/i
  ];
  
  return xssPatterns.some(pattern => pattern.test(input));
}

/**
 * Comprehensive input validation function
 */
export function validateInput(
  input: any,
  options: {
    type: 'string' | 'number' | 'email' | 'url' | 'uuid' | 'html' | 'json';
    required?: boolean;
    minLength?: number;
    maxLength?: number;
    min?: number;
    max?: number;
    pattern?: RegExp;
    sanitize?: boolean;
    allowedTags?: string[];
  }
): { valid: boolean; value?: any; error?: string } {
  const {
    type,
    required = false,
    minLength,
    maxLength,
    min,
    max,
    pattern,
    sanitize = true,
    allowedTags
  } = options;
  
  // Handle null/undefined
  if (input === null || input === undefined || input === '') {
    if (required) {
      return { valid: false, error: 'Field is required' };
    }
    return { valid: true, value: input };
  }
  
  let value = input;
  
  // Type-specific validation
  switch (type) {
    case 'string':
      if (typeof value !== 'string') {
        value = String(value);
      }
      
      // Security checks
      if (detectSQLInjection(value)) {
        return { valid: false, error: 'Input contains potentially dangerous SQL patterns' };
      }
      
      if (detectXSS(value)) {
        return { valid: false, error: 'Input contains potentially dangerous XSS patterns' };
      }
      
      // Length validation
      if (minLength && value.length < minLength) {
        return { valid: false, error: `Minimum length is ${minLength}` };
      }
      
      if (maxLength && value.length > maxLength) {
        return { valid: false, error: `Maximum length is ${maxLength}` };
      }
      
      // Pattern validation
      if (pattern && !pattern.test(value)) {
        return { valid: false, error: 'Invalid format' };
      }
      
      // Sanitization
      if (sanitize) {
        value = sanitizeString(value);
      }
      
      break;
      
    case 'html':
      if (typeof value !== 'string') {
        value = String(value);
      }
      
      // Length validation
      if (minLength && value.length < minLength) {
        return { valid: false, error: `Minimum length is ${minLength}` };
      }
      
      if (maxLength && value.length > maxLength) {
        return { valid: false, error: `Maximum length is ${maxLength}` };
      }
      
      // Sanitize HTML
      if (sanitize) {
        value = sanitizeHTML(value, allowedTags);
      }
      
      break;
      
    case 'number':
      const numValue = Number(value);
      if (isNaN(numValue)) {
        return { valid: false, error: 'Must be a valid number' };
      }
      
      if (min !== undefined && numValue < min) {
        return { valid: false, error: `Minimum value is ${min}` };
      }
      
      if (max !== undefined && numValue > max) {
        return { valid: false, error: `Maximum value is ${max}` };
      }
      
      value = numValue;
      break;
      
    case 'email':
      if (typeof value !== 'string') {
        value = String(value);
      }
      
      if (!VALIDATION_PATTERNS.EMAIL.test(value)) {
        return { valid: false, error: 'Must be a valid email address' };
      }
      
      if (sanitize) {
        value = sanitizeString(value.toLowerCase());
      }
      
      break;
      
    case 'url':
      if (typeof value !== 'string') {
        value = String(value);
      }
      
      if (!VALIDATION_PATTERNS.URL.test(value)) {
        return { valid: false, error: 'Must be a valid URL' };
      }
      
      try {
        new URL(value); // Additional URL validation
      } catch {
        return { valid: false, error: 'Must be a valid URL' };
      }
      
      break;
      
    case 'uuid':
      if (typeof value !== 'string') {
        value = String(value);
      }
      
      if (!VALIDATION_PATTERNS.UUID.test(value)) {
        return { valid: false, error: 'Must be a valid UUID' };
      }
      
      break;
      
    case 'json':
      if (typeof value === 'string') {
        try {
          value = JSON.parse(value);
        } catch {
          return { valid: false, error: 'Must be valid JSON' };
        }
      }
      break;
      
    default:
      return { valid: false, error: 'Unknown validation type' };
  }
  
  return { valid: true, value };
}

/**
 * Validate request body with comprehensive security checks
 */
export async function validateRequestBody<T>(
  request: NextRequest,
  schema: z.ZodSchema<T>,
  options: {
    maxSize?: number;
    allowedContentTypes?: string[];
    sanitize?: boolean;
  } = {}
): Promise<{ success: true; data: T } | { success: false; error: ValidationError }> {
  const correlationId = request.headers.get('x-correlation-id') || 'unknown';
  const {
    maxSize = 10 * 1024 * 1024, // 10MB default
    allowedContentTypes = [CONTENT_TYPES.JSON],
    sanitize = true
  } = options;
  
  try {
    // Check content type
    const contentType = request.headers.get('content-type') || '';
    const isValidContentType = allowedContentTypes.some(type => 
      contentType.toLowerCase().includes(type.toLowerCase())
    );
    
    if (!isValidContentType) {
      logger.security('suspicious_activity', {
        requestId: correlationId,
        metadata: {
          reason: 'invalid_content_type',
          contentType,
          allowedTypes: allowedContentTypes
        },
        ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || undefined,
        userAgent: request.headers.get('user-agent') || undefined
      });
      
      throw new ValidationError(
        `Invalid content type. Expected: ${allowedContentTypes.join(', ')}`,
        'INVALID_CONTENT_TYPE'
      );
    }
    
    // Check content length
    const contentLength = request.headers.get('content-length');
    if (contentLength && parseInt(contentLength) > maxSize) {
      throw new ValidationError(
        `Request body too large. Maximum size: ${Math.round(maxSize / 1024 / 1024)}MB`,
        'PAYLOAD_TOO_LARGE'
      );
    }
    
    // Parse request body
    let rawBody: string;
    try {
      rawBody = await request.text();
    } catch (error) {
      throw new ValidationError('Failed to read request body', 'BODY_READ_ERROR');
    }
    
    // Check raw body size
    if (Buffer.byteLength(rawBody, 'utf8') > maxSize) {
      throw new ValidationError(
        `Request body too large. Maximum size: ${Math.round(maxSize / 1024 / 1024)}MB`,
        'PAYLOAD_TOO_LARGE'
      );
    }
    
    // Security scan of raw body
    if (detectSQLInjection(rawBody)) {
      logger.security('suspicious_activity', {
        requestId: correlationId,
        metadata: {
          reason: 'sql_injection_attempt',
          endpoint: request.url,
          bodyPreview: rawBody.substring(0, 100) + '...'
        },
        ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || undefined,
        userAgent: request.headers.get('user-agent') || undefined
      });
      
      throw new ValidationError(
        'Request contains potentially dangerous SQL patterns',
        'SECURITY_VIOLATION'
      );
    }
    
    if (detectXSS(rawBody)) {
      logger.security('suspicious_activity', {
        requestId: correlationId,
        metadata: {
          reason: 'xss_attempt',
          endpoint: request.url,
          bodyPreview: rawBody.substring(0, 100) + '...'
        },
        ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || undefined,
        userAgent: request.headers.get('user-agent') || undefined
      });
      
      throw new ValidationError(
        'Request contains potentially dangerous XSS patterns',
        'SECURITY_VIOLATION'
      );
    }
    
    // Parse JSON
    let parsedBody: any;
    try {
      parsedBody = JSON.parse(rawBody);
    } catch (error) {
      throw new ValidationError('Invalid JSON format', 'INVALID_JSON');
    }
    
    // Sanitize if requested
    if (sanitize) {
      parsedBody = sanitizeObject(parsedBody);
    }
    
    // Validate with schema
    const result = schema.safeParse(parsedBody);
    if (!result.success) {
      const errors = result.error.errors.map(err => 
        `${err.path.join('.')}: ${err.message}`
      ).join(', ');
      
      throw new ValidationError(
        `Validation failed: ${errors}`,
        'VALIDATION_FAILED',
        { details: result.error.errors }
      );
    }
    
    logger.info('Request body validated successfully', {
      requestId: correlationId,
      action: 'body_validation',
      metadata: {
        contentType,
        bodySize: Buffer.byteLength(rawBody, 'utf8'),
        fieldCount: Object.keys(parsedBody).length
      }
    });
    
    return { success: true, data: result.data };
    
  } catch (error) {
    if (error instanceof ValidationError) {
      return { success: false, error };
    }
    
    logger.error('Request body validation failed', error as Error, {
      requestId: correlationId,
      action: 'body_validation_error',
      endpoint: request.url
    });
    
    return {
      success: false,
      error: new ValidationError(
        'Request validation failed',
        'VALIDATION_ERROR',
        { originalError: error instanceof Error ? error.message : 'Unknown error' }
      )
    };
  }
}

/**
 * Recursively sanitize an object
 */
function sanitizeObject(obj: any): any {
  if (obj === null || obj === undefined) {
    return obj;
  }
  
  if (typeof obj === 'string') {
    return sanitizeString(obj);
  }
  
  if (typeof obj === 'number' || typeof obj === 'boolean') {
    return obj;
  }
  
  if (Array.isArray(obj)) {
    return obj.map(sanitizeObject);
  }
  
  if (typeof obj === 'object') {
    const sanitized: any = {};
    for (const [key, value] of Object.entries(obj)) {
      const sanitizedKey = sanitizeString(key);
      sanitized[sanitizedKey] = sanitizeObject(value);
    }
    return sanitized;
  }
  
  return obj;
}

/**
 * Common validation schemas for admin operations
 */
export const AdminSchemas = {
  // User management
  createUser: z.object({
    username: z.string().min(3).max(50),
    email: z.string().email(),
    password: z.string().min(8),
    role: z.enum(['user', 'admin', 'moderator']).optional(),
    profile: z.object({
      firstName: z.string().min(1).max(100),
      lastName: z.string().min(1).max(100),
      bio: z.string().max(500).optional()
    }).optional()
  }),
  
  updateUser: z.object({
    username: z.string().min(3).max(50).optional(),
    email: z.string().email().optional(),
    role: z.enum(['user', 'admin', 'moderator']).optional(),
    profile: z.object({
      firstName: z.string().min(1).max(100).optional(),
      lastName: z.string().min(1).max(100).optional(),
      bio: z.string().max(500).optional()
    }).optional()
  }),
  
  // Content management
  createBlogPost: z.object({
    title: z.string().min(1).max(200),
    content: z.string().min(1),
    excerpt: z.string().max(500).optional(),
    slug: z.string().min(1).max(200).optional(),
    categoryId: z.string().uuid().optional(),
    tags: z.array(z.string().max(50)).optional(),
    status: z.enum(['draft', 'published', 'archived']).optional(),
    scheduledAt: z.string().datetime().optional(),
    seoTitle: z.string().max(60).optional(),
    seoDescription: z.string().max(160).optional()
  }),
  
  // System configuration
  updateSettings: z.object({
    siteName: z.string().min(1).max(100).optional(),
    siteDescription: z.string().max(500).optional(),
    contactEmail: z.string().email().optional(),
    maintenanceMode: z.boolean().optional(),
    allowRegistration: z.boolean().optional(),
    analyticsId: z.string().max(50).optional()
  }),
  
  // Pagination and filtering
  paginationQuery: z.object({
    page: z.coerce.number().min(1).default(1),
    limit: z.coerce.number().min(1).max(100).default(10),
    sortBy: z.string().max(50).optional(),
    sortOrder: z.enum(['asc', 'desc']).default('desc'),
    search: z.string().max(100).optional(),
    filter: z.record(z.string()).optional()
  }),
  
  // Database operations
  backupRequest: z.object({
    name: z.string().min(1).max(100),
    type: z.enum(['full', 'incremental', 'schema']).optional(),
    compress: z.boolean().optional(),
    encryption: z.boolean().optional()
  })
}; 