/**
 * Utility for parsing M-PESA transaction messages
 * Enhanced with comprehensive validation and error handling
 */

import {
  validateTransactionId,
  validateAmount,
  validateCustomerName,
  validatePhoneNumber,
  validateTransactionDate,
  TeamMemberValidationError
} from './validation';

interface TransactionData {
  transactionId: string;
  amount: number;
  customerName: string;
  phoneNumber: string;
  transactionDate: Date;
  rawMessage: string;
}

// Enhanced regex patterns for better parsing
const TRANSACTION_ID_PATTERNS = [
  /([A-Z]{2,3}\d[A-Z0-9]{6,7})/,  // Standard M-Pesa format
  /MPESA\s+confirmation\.?\s+([A-Z]{2,3}\d[A-Z0-9]{6,7})/i,  // With MPESA prefix
  /([A-Z]{2,3}\d[A-Z0-9]{6,7})\s+confirmed/i  // With confirmed suffix
];

const AMOUNT_PATTERNS = [
  /KES\s+([0-9,]+(?:\.[0-9]{2})?)/i,
  /Ksh\.?\s*([0-9,]+(?:\.[0-9]{2})?)/i,
  /([0-9,]+(?:\.[0-9]{2})?)\s+shillings/i,
  /received\s+([0-9,]+(?:\.[0-9]{2})?)/i,
  
  // Additional patterns for different M-Pesa formats
  /amount\s+([0-9,]+(?:\.[0-9]{2})?)/i,
  /of\s+([0-9,]+(?:\.[0-9]{2})?)/i,
  /([0-9,]+(?:\.[0-9]{2})?)\s+from/i,
  /sent\s+([0-9,]+(?:\.[0-9]{2})?)/i,
  
  // Fallback patterns - just numbers with currency indicators
  /([0-9,]+(?:\.[0-9]{2})?)\s*(?:KES|Ksh)/i,
  /(?:KES|Ksh)\s*([0-9,]+(?:\.[0-9]{2})?)/i
];

const CUSTOMER_NAME_PATTERNS = [
  // Standard "from NAME" patterns
  /from\s+([A-Z][A-Z\s]+?)\s+\d/i,
  /from\s+([A-Z][A-Z\s]+?)(?:\s+for|\s+on|\s*$)/i,
  /received\s+from\s+([A-Z][A-Z\s]+)/i,
  
  // More flexible patterns for different M-Pesa formats
  /from\s+([A-Z][A-Za-z\s]+?)\s+(?:on|for|\d)/i,
  /from\s+([A-Z][A-Za-z\s]+?)\s*$/i,
  /from\s+([A-Z][A-Za-z\s]+?)\s+KES/i,
  /from\s+([A-Z][A-Za-z\s]+?)\s+\(/i,
  
  // Alternative formats
  /sent\s+by\s+([A-Z][A-Za-z\s]+)/i,
  /payment\s+from\s+([A-Z][A-Za-z\s]+)/i,
  /by\s+([A-Z][A-Za-z\s]+?)\s+on/i,
  
  // Fallback patterns - more permissive
  /([A-Z][A-Za-z\s]{2,30}?)\s+has\s+sent/i,
  /([A-Z][A-Za-z\s]{2,30}?)\s+sent\s+you/i
];

const PHONE_NUMBER_PATTERNS = [
  /(\d{12})/,  // 12 digits with country code
  /(\d{10})/,  // 10 digits without country code
  /(\d{3}[\s-]\d{3}[\s-]\d{3}[\s-]\d{3})/,  // With separators
  /254(\d{9})/,  // Explicit Kenya country code
  /\+254\s*(\d{9})/  // With plus sign
];

const DATE_PATTERNS = [
  // Standard M-Pesa formats
  /on\s+(\d{1,2}\/\d{1,2}\/\d{4})\s+at\s+(\d{1,2}:\d{2}\s*(?:AM|PM))/i,
  /(\d{1,2}\/\d{1,2}\/\d{4})\s+at\s+(\d{1,2}:\d{2}\s*(?:AM|PM))/i,
  /on\s+(\d{1,2}-\d{1,2}-\d{4})\s+at\s+(\d{1,2}:\d{2}\s*(?:AM|PM))/i,
  /(\d{1,2}-\d{1,2}-\d{4})\s+at\s+(\d{1,2}:\d{2}\s*(?:AM|PM))/i,
  
  // Alternative formats
  /(\d{1,2}\/\d{1,2}\/\d{4})\s+(\d{1,2}:\d{2}\s*(?:AM|PM))/i,
  /(\d{1,2}-\d{1,2}-\d{4})\s+(\d{1,2}:\d{2}\s*(?:AM|PM))/i,
  
  // Date without time (will use current time)
  /on\s+(\d{1,2}\/\d{1,2}\/\d{4})/i,
  /(\d{1,2}\/\d{1,2}\/\d{4})/i,
  /(\d{1,2}-\d{1,2}-\d{4})/i,
  
  // ISO-like formats
  /(\d{4}-\d{1,2}-\d{1,2})\s+(\d{1,2}:\d{2}:\d{2})/i,
  /(\d{4}\/\d{1,2}\/\d{1,2})\s+(\d{1,2}:\d{2})/i
];

/**
 * Parse an M-PESA transaction message to extract relevant information
 * Enhanced with comprehensive validation and error handling
 * @param message The raw M-PESA transaction message
 * @returns Extracted transaction data or null if parsing fails
 */
export function parseTransactionMessage(message: string): TransactionData | null {
  const startTime = Date.now();

  try {
    console.log(`[Transaction Parser] Starting to parse message: ${message.substring(0, 100)}...`);

    // Step 1: Basic validation and cleanup
    if (!message || typeof message !== 'string') {
      console.error('[Transaction Parser] Invalid input: message must be a non-empty string');
      return null;
    }

    const cleanMessage = message.trim();
    if (!cleanMessage) {
      console.error('[Transaction Parser] Empty message after trimming');
      return null;
    }

    // Step 2: Extract and validate transaction ID
    let transactionId = '';
    for (const pattern of TRANSACTION_ID_PATTERNS) {
      const match = cleanMessage.match(pattern);
      if (match) {
        transactionId = match[1];
        break;
      }
    }

    if (!transactionId) {
      console.error('[Transaction Parser] Failed to extract transaction ID from message');
      return null;
    }

    // Validate transaction ID format
    try {
      transactionId = validateTransactionId(transactionId);
      console.log(`[Transaction Parser] Transaction ID extracted: ${transactionId}`);
    } catch (error) {
      console.error(`[Transaction Parser] Invalid transaction ID format: ${transactionId}`, error);
      return null;
    }

    // Step 3: Extract and validate amount
    let amountStr = '';
    for (const pattern of AMOUNT_PATTERNS) {
      const match = cleanMessage.match(pattern);
      if (match) {
        amountStr = match[1].replace(/,/g, ''); // Remove commas
        break;
      }
    }

    if (!amountStr) {
      console.error('[Transaction Parser] Failed to extract amount from message');
      return null;
    }

    let amount: number;
    try {
      amount = validateAmount(amountStr);
      console.log(`[Transaction Parser] Amount extracted: KES ${amount}`);
    } catch (error) {
      console.error(`[Transaction Parser] Invalid amount: ${amountStr}`, error);
      return null;
    }

    // Step 4: Extract and validate customer name
    let customerNameStr = '';
    let patternUsed = '';
    
    for (let i = 0; i < CUSTOMER_NAME_PATTERNS.length; i++) {
      const pattern = CUSTOMER_NAME_PATTERNS[i];
      const match = cleanMessage.match(pattern);
      if (match && match[1]) {
        customerNameStr = match[1].trim();
        patternUsed = `Pattern ${i + 1}`;
        console.log(`[Transaction Parser] Customer name found using ${patternUsed}: "${customerNameStr}"`);
        break;
      }
    }

    // If no customer name found, try a more aggressive approach
    if (!customerNameStr) {
      console.warn('[Transaction Parser] Standard patterns failed, trying fallback extraction...');
      
      // Try to find any capitalized words that could be a name
      const fallbackMatches = cleanMessage.match(/\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b/g);
      if (fallbackMatches && fallbackMatches.length > 0) {
        // Filter out common M-Pesa keywords
        const excludeWords = ['MPESA', 'KES', 'CONFIRMED', 'RECEIVED', 'FROM', 'ON', 'AT', 'FOR', 'SENT', 'BY', 'PAYMENT', 'TRANSACTION'];
        const potentialNames = fallbackMatches.filter(name => 
          !excludeWords.includes(name.toUpperCase()) && 
          name.length > 2 && 
          name.length < 50
        );
        
        if (potentialNames.length > 0) {
          customerNameStr = potentialNames[0];
          console.log(`[Transaction Parser] Fallback name extraction found: "${customerNameStr}"`);
        }
      }
    }

    // Final fallback - use a placeholder if still no name found
    if (!customerNameStr) {
      console.warn('[Transaction Parser] Could not extract customer name, using placeholder');
      customerNameStr = 'Unknown Customer';
    }

    let customerName: string;
    try {
      customerName = validateCustomerName(customerNameStr);
      console.log(`[Transaction Parser] Customer name validated: ${customerName}`);
    } catch (error) {
      console.warn(`[Transaction Parser] Customer name validation failed for "${customerNameStr}", using as-is:`, error);
      // Use the name as-is if validation fails, but clean it up
      customerName = customerNameStr.replace(/[^\w\s]/g, '').trim();
      if (!customerName) {
        customerName = 'Unknown Customer';
      }
    }

    // Step 5: Extract and validate phone number
    let phoneNumberStr = '';
    for (const pattern of PHONE_NUMBER_PATTERNS) {
      const match = cleanMessage.match(pattern);
      if (match) {
        phoneNumberStr = match[1] || match[0];
        // Remove any non-digit characters
        phoneNumberStr = phoneNumberStr.replace(/\D/g, '');
        break;
      }
    }

    // If no phone number found, try to extract any sequence of digits that looks like a phone number
    if (!phoneNumberStr) {
      console.warn('[Transaction Parser] Standard phone patterns failed, trying fallback extraction...');
      const digitSequences = cleanMessage.match(/\d{9,12}/g);
      if (digitSequences && digitSequences.length > 0) {
        phoneNumberStr = digitSequences[0];
        console.log(`[Transaction Parser] Fallback phone extraction found: ${phoneNumberStr}`);
      }
    }

    // Final fallback - use a placeholder if still no phone found
    if (!phoneNumberStr) {
      console.warn('[Transaction Parser] Could not extract phone number, using placeholder');
      phoneNumberStr = '0700000000'; // Placeholder Kenyan number
    }

    let phoneNumber: string;
    try {
      phoneNumber = validatePhoneNumber(phoneNumberStr);
      console.log(`[Transaction Parser] Phone number validated: ${phoneNumber}`);
    } catch (error) {
      console.warn(`[Transaction Parser] Phone number validation failed for "${phoneNumberStr}", using cleaned version:`, error);
      // Clean and format the phone number manually
      let cleanPhone = phoneNumberStr.replace(/\D/g, '');
      
      // Add Kenya country code if missing
      if (cleanPhone.length === 9 && cleanPhone.startsWith('7')) {
        cleanPhone = '254' + cleanPhone;
      } else if (cleanPhone.length === 10 && cleanPhone.startsWith('07')) {
        cleanPhone = '254' + cleanPhone.substring(1);
      }
      
      phoneNumber = cleanPhone || '254700000000';
    }

    // Step 6: Extract transaction date using enhanced patterns
    let transactionDate: Date = new Date(); // Initialize with current date as fallback
    let dateFound = false;

    // Try each date pattern
    for (const pattern of DATE_PATTERNS) {
      const dateMatch = cleanMessage.match(pattern);
      
      if (dateMatch) {
        console.log(`[Transaction Parser] Date pattern matched: ${pattern}`);
        
        let dateStr = dateMatch[1];
        let timeStr = dateMatch[2] || '';
        
        try {
          // Handle different date formats
          let day: number, month: number, year: number;
          
          if (dateStr.includes('/')) {
            const parts = dateStr.split('/').map(Number);
            [day, month, year] = parts;
          } else if (dateStr.includes('-')) {
            const parts = dateStr.split('-').map(Number);
            // Check if it's ISO format (YYYY-MM-DD) or standard format (DD-MM-YYYY)
            if (parts[0] > 31) {
              [year, month, day] = parts;
            } else {
              [day, month, year] = parts;
            }
          } else {
            continue; // Skip this pattern if we can't parse the date
          }
          
          // Parse time if available
          let hours = 0, minutes = 0;
          
          if (timeStr) {
            const timeParts = timeStr.match(/(\d{1,2}):(\d{2})(?::(\d{2}))?\s*([AP]M)?/i);
            
            if (timeParts) {
              hours = parseInt(timeParts[1]);
              minutes = parseInt(timeParts[2]);
              const isPM = timeParts[4] && timeParts[4].toUpperCase() === 'PM';
              
              // Convert to 24-hour format if AM/PM is specified
              if (timeParts[4]) { // AM/PM specified
                if (isPM && hours < 12) {
                  hours += 12;
                } else if (!isPM && hours === 12) {
                  hours = 0;
                }
              }
            }
          } else {
            // If no time specified, use current time
            const now = new Date();
            hours = now.getHours();
            minutes = now.getMinutes();
          }
          
          // Create date object
          transactionDate = new Date(year, month - 1, day, hours, minutes);
          
          // Validate date
          const now = new Date();
          if (!isNaN(transactionDate.getTime()) && transactionDate <= now) {
            console.log(`[Transaction Parser] Transaction date extracted: ${transactionDate.toLocaleString()}`);
            dateFound = true;
            break;
          } else {
            console.warn(`[Transaction Parser] Invalid or future date detected: ${transactionDate}, continuing to next pattern`);
          }
        } catch (error) {
          console.warn(`[Transaction Parser] Error parsing date with pattern ${pattern}:`, error);
          continue;
        }
      }
    }
    
    // Fallback to current date if no valid date found
    if (!dateFound) {
      console.warn('[Transaction Parser] No valid date found in message, using current date and time');
      transactionDate = new Date();
    }

    return {
      transactionId,
      amount,
      customerName,
      phoneNumber,
      transactionDate,
      rawMessage: cleanMessage
    };
  } catch (error) {
    console.error('Error parsing transaction message:', error);
    return null;
  }
}

/**
 * Batch parse multiple M-PESA transaction messages
 * @param messages Array of raw M-PESA transaction messages
 * @returns Array of successfully parsed transaction data
 */
export function parseTransactionMessages(messages: string[]): TransactionData[] {
  const results: TransactionData[] = [];

  for (const message of messages) {
    const parsedData = parseTransactionMessage(message);
    if (parsedData) {
      results.push(parsedData);
    }
  }

  return results;
}

/**
 * Debug function to test parsing patterns
 * @param message Test message
 * @returns Debug information about what was found
 */
export function debugParseMessage(message: string) {
  console.log('=== TRANSACTION PARSER DEBUG ===');
  console.log('Message:', message);
  
  // Test transaction ID patterns
  console.log('\n--- Transaction ID Patterns ---');
  TRANSACTION_ID_PATTERNS.forEach((pattern, index) => {
    const match = message.match(pattern);
    console.log(`Pattern ${index + 1}:`, pattern, match ? `Found: ${match[1]}` : 'No match');
  });
  
  // Test amount patterns
  console.log('\n--- Amount Patterns ---');
  AMOUNT_PATTERNS.forEach((pattern, index) => {
    const match = message.match(pattern);
    console.log(`Pattern ${index + 1}:`, pattern, match ? `Found: ${match[1]}` : 'No match');
  });
  
  // Test customer name patterns
  console.log('\n--- Customer Name Patterns ---');
  CUSTOMER_NAME_PATTERNS.forEach((pattern, index) => {
    const match = message.match(pattern);
    console.log(`Pattern ${index + 1}:`, pattern, match ? `Found: ${match[1]}` : 'No match');
  });
  
  // Test phone patterns
  console.log('\n--- Phone Number Patterns ---');
  PHONE_NUMBER_PATTERNS.forEach((pattern, index) => {
    const match = message.match(pattern);
    console.log(`Pattern ${index + 1}:`, pattern, match ? `Found: ${match[1] || match[0]}` : 'No match');
  });
  
  console.log('\n=== END DEBUG ===');
}
