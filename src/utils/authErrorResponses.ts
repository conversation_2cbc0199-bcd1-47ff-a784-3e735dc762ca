/**
 * Standardized Authentication Error Response Utilities
 * Provides consistent error handling across all authentication endpoints
 */

import { NextResponse } from 'next/server';

export interface AuthErrorResponse {
  success: false;
  error: string;
  code: string;
  timestamp: string;
  requestId?: string;
  retryAfter?: number;
}

export interface AuthSuccessResponse {
  success: true;
  [key: string]: any;
}

/**
 * Create a standardized error response
 */
export function createAuthErrorResponse(
  error: string,
  code: string,
  status: number,
  requestId?: string,
  retryAfter?: number
): NextResponse {
  const response: AuthErrorResponse = {
    success: false,
    error,
    code,
    timestamp: new Date().toISOString(),
    ...(requestId && { requestId }),
    ...(retryAfter && { retryAfter })
  };

  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
  };

  if (requestId) {
    headers['X-Request-ID'] = requestId;
  }

  if (retryAfter) {
    headers['Retry-After'] = retryAfter.toString();
  }

  return NextResponse.json(response, { status, headers });
}

/**
 * Create a standardized success response
 */
export function createAuthSuccessResponse(
  data: Omit<AuthSuccessResponse, 'success'>,
  status: number = 200,
  requestId?: string
): NextResponse {
  const response: AuthSuccessResponse = {
    success: true,
    ...data
  };

  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
  };

  if (requestId) {
    headers['X-Request-ID'] = requestId;
  }

  return NextResponse.json(response, { status, headers });
}

/**
 * Standard authentication error codes
 */
export const AUTH_ERROR_CODES = {
  INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
  MISSING_CREDENTIALS: 'MISSING_CREDENTIALS',
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  SESSION_EXPIRED: 'SESSION_EXPIRED',
  INSUFFICIENT_PERMISSIONS: 'INSUFFICIENT_PERMISSIONS',
  INVALID_SESSION: 'INVALID_SESSION',
  SERVER_ERROR: 'SERVER_ERROR',
  USER_INACTIVE: 'USER_INACTIVE',
  NO_SESSION: 'NO_SESSION',
  INVALID_REQUEST: 'INVALID_REQUEST',
  SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE'
} as const;

/**
 * Standard authentication success codes
 */
export const AUTH_SUCCESS_CODES = {
  LOGIN_SUCCESS: 'LOGIN_SUCCESS',
  LOGOUT_SUCCESS: 'LOGOUT_SUCCESS',
  SESSION_VALID: 'SESSION_VALID',
  SESSION_REFRESHED: 'SESSION_REFRESHED'
} as const;

/**
 * Helper function to determine if an error is security-related
 */
export function isSecurityError(error: string): boolean {
  const securityKeywords = [
    'security violation',
    'invalid characters',
    'suspicious',
    'malicious',
    'injection',
    'xss',
    'csrf'
  ];
  
  return securityKeywords.some(keyword => 
    error.toLowerCase().includes(keyword)
  );
}

/**
 * Helper function to determine if an error is a timeout
 */
export function isTimeoutError(error: string): boolean {
  return error.toLowerCase().includes('timeout');
}

/**
 * Get appropriate error response based on error type
 */
export function getErrorResponse(
  error: Error | string,
  requestId?: string
): NextResponse {
  const errorMessage = typeof error === 'string' ? error : error.message;
  
  if (isSecurityError(errorMessage)) {
    return createAuthErrorResponse(
      'Invalid request',
      AUTH_ERROR_CODES.INVALID_REQUEST,
      400,
      requestId
    );
  }
  
  if (isTimeoutError(errorMessage)) {
    return createAuthErrorResponse(
      'Service temporarily unavailable',
      AUTH_ERROR_CODES.SERVICE_UNAVAILABLE,
      503,
      requestId
    );
  }
  
  return createAuthErrorResponse(
    'Internal server error',
    AUTH_ERROR_CODES.SERVER_ERROR,
    500,
    requestId
  );
} 