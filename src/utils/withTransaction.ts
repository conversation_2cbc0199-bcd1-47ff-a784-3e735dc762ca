import prisma from '@/lib/prisma';
import { log } from './logger';
import { TransactionContext } from '@/types/api';
import { DatabaseError } from '@/errors/ApiErrors';

/**
 * Prisma transaction type
 */
export type PrismaTransaction = Omit<typeof prisma, '$connect' | '$disconnect' | '$on' | '$transaction' | '$use'>;

/**
 * Transaction callback function type
 */
export type TransactionCallback<T> = (tx: PrismaTransaction, context: TransactionContext) => Promise<T>;

/**
 * Transaction options
 */
export interface TransactionOptions {
  /**
   * Maximum time the transaction can run before being cancelled and rolled back
   * Default: 5000ms (5 seconds)
   */
  maxWait?: number;
  
  /**
   * Maximum time to wait for the transaction to start
   * Default: 2000ms (2 seconds)
   */
  timeout?: number;
  
  /**
   * Isolation level for the transaction
   */
  isolationLevel?: 'ReadUncommitted' | 'ReadCommitted' | 'RepeatableRead' | 'Serializable';
}

/**
 * Execute a function within a database transaction
 * Provides automatic rollback on errors and logging
 */
export async function withTransaction<T>(
  callback: TransactionCallback<T>,
  options: TransactionOptions = {},
  requestId?: string,
  userId?: string
): Promise<T> {
  const transactionId = `tx_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  const startTime = Date.now();
  
  const context: TransactionContext = {
    tx: null as any, // Will be set when transaction starts
    requestId: requestId || `req_${Date.now()}`,
    userId
  };

  const logContext = {
    requestId: context.requestId,
    userId: context.userId,
    action: 'database_transaction',
    resource: 'database',
    metadata: {
      transactionId,
      options
    }
  };

  try {
    log.debug('Starting database transaction', logContext);

    const result = await prisma.$transaction(
      async (tx) => {
        context.tx = tx;
        
        log.debug('Transaction started', {
          ...logContext,
          metadata: {
            ...logContext.metadata,
            transactionStarted: true
          }
        });

        // Execute the callback with transaction context
        return await callback(tx, context);
      },
      {
        maxWait: options.maxWait || 5000,
        timeout: options.timeout || 2000,
        isolationLevel: options.isolationLevel
      }
    );

    const duration = Date.now() - startTime;
    
    log.info('Transaction completed successfully', {
      ...logContext,
      duration,
      metadata: {
        ...logContext.metadata,
        success: true,
        resultType: typeof result
      }
    });

    return result;

  } catch (error) {
    const duration = Date.now() - startTime;
    
    log.error('Transaction failed and rolled back', error as Error, {
      ...logContext,
      duration,
      metadata: {
        ...logContext.metadata,
        success: false,
        errorType: error instanceof Error ? error.constructor.name : 'unknown'
      }
    });

    // Convert to database error if not already an APIError
    if (error instanceof Error) {
      throw new DatabaseError(
        `Transaction failed: ${error.message}`,
        { 
          originalError: error.message,
          transactionId,
          duration
        },
        context.requestId
      );
    }

    throw error;
  }
}

/**
 * Execute multiple database operations in a single transaction
 * Each operation is a function that receives the transaction object
 */
export async function withBatchTransaction<T extends Record<string, any>>(
  operations: {
    [K in keyof T]: (tx: PrismaTransaction) => Promise<T[K]>;
  },
  options: TransactionOptions = {},
  requestId?: string,
  userId?: string
): Promise<T> {
  return withTransaction(
    async (tx) => {
      const results = {} as T;
      
      // Execute all operations sequentially within the transaction
      for (const [key, operation] of Object.entries(operations)) {
        try {
          results[key as keyof T] = await operation(tx);
        } catch (error) {
          log.error(`Batch operation '${key}' failed`, error as Error, {
            requestId: requestId || `req_${Date.now()}`,
            userId,
            action: 'batch_operation_failed',
            resource: key,
            metadata: {
              operationKey: key,
              totalOperations: Object.keys(operations).length
            }
          });
          throw error;
        }
      }
      
      return results;
    },
    options,
    requestId,
    userId
  );
}

/**
 * Wrapper for read-only database operations
 * Provides consistent logging without transaction overhead
 */
export async function withDatabaseRead<T>(
  operation: (prisma: typeof prisma) => Promise<T>,
  operationName: string,
  requestId?: string,
  userId?: string
): Promise<T> {
  const startTime = Date.now();
  
  const logContext = {
    requestId: requestId || `req_${Date.now()}`,
    userId,
    action: 'database_read',
    resource: 'database',
    metadata: {
      operationName,
      readonly: true
    }
  };

  try {
    log.debug(`Starting read operation: ${operationName}`, logContext);
    
    const result = await operation(prisma);
    
    const duration = Date.now() - startTime;
    
    log.debug(`Read operation completed: ${operationName}`, {
      ...logContext,
      duration,
      metadata: {
        ...logContext.metadata,
        success: true,
        resultType: typeof result
      }
    });

    return result;

  } catch (error) {
    const duration = Date.now() - startTime;
    
    log.error(`Read operation failed: ${operationName}`, error as Error, {
      ...logContext,
      duration,
      metadata: {
        ...logContext.metadata,
        success: false
      }
    });

    throw error;
  }
}

/**
 * Utility for performing upsert operations (create or update)
 */
export async function withUpsert<T>(
  callback: TransactionCallback<T>,
  options: TransactionOptions = {},
  requestId?: string,
  userId?: string
): Promise<T> {
  return withTransaction(
    async (tx, context) => {
      log.debug('Performing upsert operation', {
        requestId: context.requestId,
        userId: context.userId,
        action: 'database_upsert',
        resource: 'database'
      });

      return await callback(tx, context);
    },
    options,
    requestId,
    userId
  );
}

/**
 * Retry wrapper for database operations
 * Useful for handling temporary connection issues
 */
export async function withRetry<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delayMs: number = 1000,
  requestId?: string
): Promise<T> {
  let lastError: Error;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error as Error;
      
      log.warn(`Database operation failed, attempt ${attempt}/${maxRetries}`, {
        requestId: requestId || `req_${Date.now()}`,
        action: 'database_retry',
        resource: 'database',
        metadata: {
          attempt,
          maxRetries,
          errorMessage: lastError.message,
          willRetry: attempt < maxRetries
        }
      });

      if (attempt === maxRetries) {
        throw new DatabaseError(
          `Operation failed after ${maxRetries} attempts: ${lastError.message}`,
          { 
            attempts: maxRetries,
            lastError: lastError.message
          },
          requestId
        );
      }

      // Wait before retrying (exponential backoff)
      await new Promise(resolve => setTimeout(resolve, delayMs * attempt));
    }
  }

  throw lastError!;
}

/**
 * Transaction manager for complex multi-step operations
 */
export class TransactionManager {
  private steps: Array<{
    name: string;
    operation: (tx: PrismaTransaction) => Promise<any>;
    rollback?: (tx: PrismaTransaction, result: any) => Promise<void>;
  }> = [];

  /**
   * Add a step to the transaction
   */
  addStep(
    name: string,
    operation: (tx: PrismaTransaction) => Promise<any>,
    rollback?: (tx: PrismaTransaction, result: any) => Promise<void>
  ): this {
    this.steps.push({ name, operation, rollback });
    return this;
  }

  /**
   * Execute all steps in order within a single transaction
   */
  async execute(
    options: TransactionOptions = {},
    requestId?: string,
    userId?: string
  ): Promise<any[]> {
    return withTransaction(
      async (tx, context) => {
        const results: any[] = [];
        
        for (const [index, step] of this.steps.entries()) {
          try {
            log.debug(`Executing transaction step: ${step.name}`, {
              requestId: context.requestId,
              userId: context.userId,
              action: 'transaction_step',
              resource: step.name,
              metadata: {
                stepIndex: index + 1,
                totalSteps: this.steps.length
              }
            });

            const result = await step.operation(tx);
            results.push(result);

          } catch (error) {
            log.error(`Transaction step failed: ${step.name}`, error as Error, {
              requestId: context.requestId,
              userId: context.userId,
              action: 'transaction_step_failed',
              resource: step.name,
              metadata: {
                stepIndex: index + 1,
                totalSteps: this.steps.length
              }
            });

            throw error; // Let transaction rollback handle cleanup
          }
        }

        return results;
      },
      options,
      requestId,
      userId
    );
  }

  /**
   * Clear all steps
   */
  clear(): this {
    this.steps = [];
    return this;
  }
} 