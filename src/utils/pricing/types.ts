/**
 * Pricing and product types extracted from product page
 * for better maintainability and reusability
 */

export interface Unit {
  id: string;
  name: string;
  displayName: string;
  plural: string;
  shortForm?: string;
  category: string;
}

export interface PricingItem {
  id: string;
  service: string;
  price: number;
  designFee?: number;
  description?: string;
  features?: string[];
  icon?: string;
  popular?: boolean;
  imageUrl?: string;
  imageUrl2?: string;
  imageUrl3?: string;
  category?: string;
  pricingType?: string;
  unitType?: string; // Deprecated - for backward compatibility
  unitId?: string;
  unit?: Unit;
  minQuantity?: number;
  maxQuantity?: number;
  pricePerMeter?: number;
  minMeters?: number;
  maxMeters?: number;
  createdAt: string;
  updatedAt: string;
}

export interface UnitDisplay {
  singular: string;
  plural: string;
  short: string;
} 