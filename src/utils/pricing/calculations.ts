import type { PricingItem, UnitDisplay } from './types';

/**
 * Utility to get unit display information from a product
 * Handles both new unit system and backward compatibility with old unitType
 */
export const getUnitDisplay = (product: PricingItem | null): UnitDisplay => {
  if (!product) return { singular: 'copy', plural: 'copies', short: 'pcs' };
  
  // Use new unit system if available
  if (product.unit) {
    return {
      singular: product.unit.displayName.toLowerCase(),
      plural: product.unit.plural.toLowerCase(),
      short: product.unit.shortForm || product.unit.displayName.toLowerCase()
    };
  }
  
  // Fallback to old unitType for backward compatibility
  if (product.unitType) {
    const unitType = product.unitType.toLowerCase();
    if (unitType.includes('meter')) {
      return { singular: 'meter', plural: 'meters', short: 'm' };
    }
    if (unitType.includes('page')) {
      return { singular: 'page', plural: 'pages', short: 'pg' };
    }
    if (unitType.includes('piece')) {
      return { singular: 'piece', plural: 'pieces', short: 'pc' };
    }
    if (unitType.includes('item')) {
      return { singular: 'item', plural: 'items', short: 'item' };
    }
  }
  
  // Default fallback
  return { singular: 'copy', plural: 'copies', short: 'pcs' };
};

/**
 * Calculate production cost based on pricing type
 */
export const getProductionCost = (
  product: PricingItem | null, 
  quantity: string, 
  meters: string
): number => {
  if (!product) return 0;
  
  const pricingType = product.pricingType || 'fixed';
  
  if (pricingType === 'per_meter' && product.pricePerMeter) {
    const meterValue = parseFloat(meters) || 1;
    return product.pricePerMeter * meterValue;
  } else {
    // Fixed pricing or quantity-based
    const quantityValue = parseInt(quantity) || 1;
    return product.price * quantityValue;
  }
};

/**
 * Get design fee from product data or fallback to default based on product type
 */
export const getDesignFee = (product: PricingItem | null): number => {
  if (product && typeof product.designFee === 'number' && product.designFee > 0) {
    return product.designFee;
  }
  
  // Fallback calculation if design fee is not set in database
  const productName = product?.service || '';
  const productNameLower = productName.toLowerCase();
  
  // Design fee mapping based on product complexity
  const designFeeMap: Record<string, number> = {
    'business card': 1500,
    'id card': 1500,
    'letterhead': 1200,
    'invoice': 1200,
    'flyer': 2000,
    'poster': 1600,
    'brochure': 2500,
    'banner': 1800,
    'catalogue': 3000,
    'catalog': 3000,
    'company profile': 4000,
    'menu': 2200,
    'logo': 3000,
    'label': 1500,
    'sticker': 1500,
    'social media': 1000,
    'packaging': 4000,
  };
  
  // Find matching product type
  for (const [key, fee] of Object.entries(designFeeMap)) {
    if (productNameLower.includes(key)) {
      return fee;
    }
  }
  
  // Default design fee for other products
  return 2000;
}; 