import { NextRequest, NextResponse } from 'next/server';

// Rate limit configuration types
export interface RateLimitConfig {
  windowMs: number; // Time window in milliseconds
  maxRequests: number; // Maximum requests per window
  keyGenerator?: (req: NextRequest) => string; // Custom key generator
  skipIf?: (req: NextRequest) => boolean; // Skip rate limiting for certain requests
  onLimitReached?: (req: NextRequest, key: string) => void; // Callback when limit is reached
}

// In-memory store for rate limiting (production should use Redis)
const requestCounts = new Map<string, { count: number; resetTime: number }>();

// Default rate limit configurations for different endpoints
export const RATE_LIMIT_CONFIGS = {
  // Authentication endpoints - stricter limits
  auth: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 5, // 5 attempts per 15 minutes
  },
  
  // Admin API endpoints - moderate limits
  admin: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 60, // 60 requests per minute
  },
  
  // File upload endpoints - very strict limits
  upload: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 10, // 10 uploads per minute
  },
  
  // Search/query endpoints - moderate limits
  search: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 30, // 30 searches per minute
  },
  
  // General API endpoints - lenient limits
  general: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 100, // 100 requests per minute
  },
} as const;

/**
 * Generate a rate limit key based on IP and endpoint
 */
function generateKey(req: NextRequest, endpoint?: string): string {
  const ip = req.headers.get('x-forwarded-for')?.split(',')[0] || 
    req.headers.get('x-real-ip') || 
    req.headers.get('cf-connecting-ip') || // Cloudflare
    'unknown';
  
  const userAgent = req.headers.get('user-agent') || 'unknown';
  const path = endpoint || new URL(req.url).pathname;
  
  // Create a more specific key to prevent abuse across different endpoints
  return `${ip}:${path}:${userAgent.slice(0, 50)}`;
}

/**
 * Clean up expired entries from the in-memory store
 */
function cleanup(): void {
  const now = Date.now();
  for (const [key, data] of requestCounts.entries()) {
    if (now > data.resetTime) {
      requestCounts.delete(key);
    }
  }
}

/**
 * Server-side rate limiting middleware
 */
export function createRateLimiter(config: RateLimitConfig) {
  return (req: NextRequest): NextResponse | null => {
    // Skip rate limiting if condition is met
    if (config.skipIf && config.skipIf(req)) {
      return null;
    }

    // Generate key for this request
    const key = config.keyGenerator ? config.keyGenerator(req) : generateKey(req);
    
    // Clean up expired entries periodically
    if (Math.random() < 0.01) { // 1% chance to trigger cleanup
      cleanup();
    }
    
    const now = Date.now();
    const windowStart = now - config.windowMs;
    
    // Get or create entry for this key
    let entry = requestCounts.get(key);
    
    if (!entry || now > entry.resetTime) {
      // Create new entry or reset expired entry
      entry = {
        count: 1,
        resetTime: now + config.windowMs
      };
      requestCounts.set(key, entry);
      return null; // Allow request
    }
    
    // Increment request count
    entry.count++;
    
    // Check if limit exceeded
    if (entry.count > config.maxRequests) {
      // Call callback if provided
      if (config.onLimitReached) {
        config.onLimitReached(req, key);
      }
      
      // Calculate retry after time
      const retryAfter = Math.ceil((entry.resetTime - now) / 1000);
      
      return NextResponse.json(
        {
          error: 'Rate limit exceeded',
          message: `Too many requests. Please try again in ${retryAfter} seconds.`,
          retryAfter
        },
        {
          status: 429,
          headers: {
            'Retry-After': retryAfter.toString(),
            'X-RateLimit-Limit': config.maxRequests.toString(),
            'X-RateLimit-Remaining': '0',
            'X-RateLimit-Reset': entry.resetTime.toString(),
          }
        }
      );
    }
    
    return null; // Allow request
  };
}

/**
 * Apply rate limiting to API route handler
 */
export function withRateLimit(
  handler: (req: NextRequest) => Promise<NextResponse>,
  config: RateLimitConfig
) {
  return async (req: NextRequest): Promise<NextResponse> => {
    const rateLimitResponse = createRateLimiter(config)(req);
    
    if (rateLimitResponse) {
      return rateLimitResponse;
    }
    
    return handler(req);
  };
}

/**
 * Client-side rate limiting and request throttling
 */
export class ClientRateLimiter {
  private requestTimes: Map<string, number[]> = new Map();
  private pendingRequests: Map<string, Promise<any>> = new Map();
  
  constructor(
    private windowMs: number = 60000, // 1 minute default
    private maxRequests: number = 30 // 30 requests per minute default
  ) {}
  
  /**
   * Check if request is allowed
   */
  isAllowed(key: string): boolean {
    const now = Date.now();
    const times = this.requestTimes.get(key) || [];
    
    // Remove expired timestamps
    const validTimes = times.filter(time => now - time < this.windowMs);
    
    if (validTimes.length >= this.maxRequests) {
      return false;
    }
    
    // Add current timestamp
    validTimes.push(now);
    this.requestTimes.set(key, validTimes);
    
    return true;
  }
  
  /**
   * Get time until next request is allowed
   */
  getRetryAfter(key: string): number {
    const times = this.requestTimes.get(key) || [];
    if (times.length === 0) return 0;
    
    const oldestTime = Math.min(...times);
    const retryAfter = this.windowMs - (Date.now() - oldestTime);
    
    return Math.max(0, Math.ceil(retryAfter / 1000));
  }
  
  /**
   * Throttled request execution with deduplication
   */
  async execute<T>(
    key: string,
    requestFn: () => Promise<T>,
    options: {
      deduplicate?: boolean;
      throwOnLimit?: boolean;
    } = {}
  ): Promise<T> {
    const { deduplicate = true, throwOnLimit = true } = options;
    
    // Check for pending request (deduplication)
    if (deduplicate && this.pendingRequests.has(key)) {
      return this.pendingRequests.get(key)!;
    }
    
    // Check rate limit
    if (!this.isAllowed(key)) {
      if (throwOnLimit) {
        const retryAfter = this.getRetryAfter(key);
        throw new Error(`Rate limit exceeded. Retry after ${retryAfter} seconds.`);
      } else {
        // Return a delayed promise
        const retryAfter = this.getRetryAfter(key);
        await new Promise(resolve => setTimeout(resolve, retryAfter * 1000));
        return this.execute(key, requestFn, options);
      }
    }
    
    // Execute request
    const promise = requestFn().finally(() => {
      if (deduplicate) {
        this.pendingRequests.delete(key);
      }
    });
    
    if (deduplicate) {
      this.pendingRequests.set(key, promise);
    }
    
    return promise;
  }
  
  /**
   * Clear rate limit data for a key
   */
  clear(key?: string): void {
    if (key) {
      this.requestTimes.delete(key);
      this.pendingRequests.delete(key);
    } else {
      this.requestTimes.clear();
      this.pendingRequests.clear();
    }
  }
  
  /**
   * Get current request count for a key
   */
  getCurrentCount(key: string): number {
    const now = Date.now();
    const times = this.requestTimes.get(key) || [];
    return times.filter(time => now - time < this.windowMs).length;
  }
  
  /**
   * Get remaining requests for a key
   */
  getRemainingRequests(key: string): number {
    return Math.max(0, this.maxRequests - this.getCurrentCount(key));
  }
}

/**
 * Debounce function for search inputs
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), wait);
  };
}

/**
 * Throttle function for high-frequency events
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

/**
 * Create a rate-limited fetch function
 */
export function createRateLimitedFetch(limiter: ClientRateLimiter) {
  return async function rateLimitedFetch(
    url: string,
    options?: RequestInit
  ): Promise<Response> {
    const key = `fetch:${url}:${options?.method || 'GET'}`;
    
    return limiter.execute(key, () => fetch(url, options), {
      deduplicate: options?.method === 'GET',
      throwOnLimit: false
    });
  };
}

// Global client-side rate limiter instances
export const adminApiLimiter = new ClientRateLimiter(60000, 60); // 60 requests per minute
export const uploadLimiter = new ClientRateLimiter(60000, 10);   // 10 uploads per minute
export const searchLimiter = new ClientRateLimiter(60000, 30);   // 30 searches per minute

// Rate-limited fetch instances
export const rateLimitedFetch = createRateLimitedFetch(adminApiLimiter);
export const rateLimitedUploadFetch = createRateLimitedFetch(uploadLimiter);
export const rateLimitedSearchFetch = createRateLimitedFetch(searchLimiter); 