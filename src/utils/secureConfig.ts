import { createCipher<PERSON>, createDecipheriv, randomBytes } from 'crypto';

const ENCRYPTION_KEY = process.env.CONFIG_ENCRYPTION_KEY || 'your-32-character-secret-key!!';
const ALGORITHM = 'aes-256-cbc';

/**
 * Encrypt sensitive configuration data
 */
export function encryptConfig(text: string): { encrypted: string; iv: string } {
  const iv = randomBytes(16);
  const cipher = createCipheriv(ALGORITHM, Buffer.from(ENCRYPTION_KEY), iv);
  let encrypted = cipher.update(text);
  encrypted = Buffer.concat([encrypted, cipher.final()]);
  
  return {
    encrypted: encrypted.toString('hex'),
    iv: iv.toString('hex')
  };
}

/**
 * Decrypt sensitive configuration data
 */
export function decryptConfig(encryptedData: { encrypted: string; iv: string }): string {
  const iv = Buffer.from(encryptedData.iv, 'hex');
  const encryptedText = Buffer.from(encryptedData.encrypted, 'hex');
  const decipher = createDecipheriv(ALGORITHM, Buffer.from(ENCRYPTION_KEY), iv);
  let decrypted = decipher.update(encryptedText);
  decrypted = Buffer.concat([decrypted, decipher.final()]);
  
  return decrypted.toString();
}

/**
 * Secure storage configuration interface
 */
export interface SecureStorageConfig {
  id: string;
  provider: string;
  region: string;
  endpoint: string;
  bucketName: string;
  // Encrypted credentials
  accessKeyId: string;
  secretAccessKey: string;
  isDefault: boolean;
}

/**
 * Get storage configuration with decrypted credentials
 * Falls back to environment variables if database config is not available
 */
export async function getSecureStorageConfig(): Promise<SecureStorageConfig | null> {
  try {
    // First try environment variables (most secure)
    if (process.env.S3_ACCESS_KEY_ID && process.env.S3_SECRET_ACCESS_KEY) {
      return {
        id: 'env-config',
        provider: process.env.S3_PROVIDER || 'linode',
        region: process.env.S3_REGION || 'fr-par-1',
        endpoint: process.env.S3_ENDPOINT || 'https://fr-par-1.linodeobjects.com',
        bucketName: process.env.S3_BUCKET_NAME || 'mocky-storage',
        accessKeyId: process.env.S3_ACCESS_KEY_ID,
        secretAccessKey: process.env.S3_SECRET_ACCESS_KEY,
        isDefault: true
      };
    }

    // If no environment variables, check database (but decrypt credentials)
    const { default: prisma } = await import('@/lib/prisma');
    
    const dbConfig = await prisma.storageConfig.findFirst({
      where: { isDefault: true }
    });

    if (!dbConfig) {
      console.warn('No storage configuration found');
      return null;
    }

    // Decrypt the credentials if they're encrypted
    let accessKeyId = dbConfig.accessKeyId;
    let secretAccessKey = dbConfig.secretAccessKey;

    try {
      // Try to decrypt (assuming they're stored as JSON with encrypted + iv)
      const encryptedKeyId = JSON.parse(accessKeyId);
      const encryptedSecret = JSON.parse(secretAccessKey);
      
      accessKeyId = decryptConfig(encryptedKeyId);
      secretAccessKey = decryptConfig(encryptedSecret);
    } catch (e) {
      // If decryption fails, they're probably stored in plain text (legacy)
      console.warn('Storage credentials may be stored in plain text - consider re-encrypting');
    }

    return {
      id: dbConfig.id,
      provider: dbConfig.provider,
      region: dbConfig.region,
      endpoint: dbConfig.endpoint,
      bucketName: dbConfig.bucketName,
      accessKeyId,
      secretAccessKey,
      isDefault: dbConfig.isDefault
    };
  } catch (error) {
    console.error('Error getting secure storage config:', error);
    return null;
  }
}

/**
 * Save storage configuration with encrypted credentials
 */
export async function saveSecureStorageConfig(config: Omit<SecureStorageConfig, 'id'>) {
  const { default: prisma } = await import('@/lib/prisma');
  
  // Encrypt the sensitive credentials
  const encryptedKeyId = encryptConfig(config.accessKeyId);
  const encryptedSecret = encryptConfig(config.secretAccessKey);

  // First, set all existing configs to non-default if this is default
  if (config.isDefault) {
    await prisma.storageConfig.updateMany({
      where: { isDefault: true },
      data: { isDefault: false }
    });
  }

  return await prisma.storageConfig.create({
    data: {
      provider: config.provider,
      region: config.region,
      endpoint: config.endpoint,
      bucketName: config.bucketName,
      accessKeyId: JSON.stringify(encryptedKeyId),
      secretAccessKey: JSON.stringify(encryptedSecret),
      isDefault: config.isDefault
    }
  });
}

/**
 * Get storage configuration for API responses (without credentials)
 */
export function getSafeStorageConfigForAPI(config: SecureStorageConfig) {
  return {
    id: config.id,
    provider: config.provider,
    region: config.region,
    endpoint: config.endpoint,
    bucketName: config.bucketName,
    isDefault: config.isDefault,
    // Never expose credentials in API responses
    hasCredentials: !!(config.accessKeyId && config.secretAccessKey)
  };
} 