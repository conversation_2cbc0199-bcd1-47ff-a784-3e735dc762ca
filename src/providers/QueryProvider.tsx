'use client';
import React from 'react';

import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { useState, ReactNode } from 'react';

/**
 * React Query Provider
 * Optimized configuration for server state management
 */

interface QueryProviderProps {
  children: ReactNode;
}

export function QueryProvider({ children }: QueryProviderProps) {
  const [queryClient] = useState(() => 
    new QueryClient({
      defaultOptions: {
        queries: {
          // Cache data for 5 minutes
          staleTime: 5 * 60 * 1000,
          // Keep data in cache for 10 minutes after being unused
          gcTime: 10 * 60 * 1000,
          // Retry failed requests 3 times with exponential backoff
          retry: 3,
          retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
          // Don't refetch on window focus by default
          refetchOnWindowFocus: false,
          // Don't refetch on reconnect by default
          refetchOnReconnect: false,
          // Don't refetch on mount if data is fresh
          refetchOnMount: false,
        },
        mutations: {
          // Retry failed mutations once
          retry: 1,
          retryDelay: 1000,
        },
      },
    })
  );

  return (
    <QueryClientProvider client={queryClient}>
      {children}
      {process.env.NODE_ENV === 'development' && (
        <ReactQueryDevtools 
          initialIsOpen={false}
        />
      )}
    </QueryClientProvider>
  );
} 