'use client';

import StyledImage from './StyledImage';

export default function StyledImageExample() {
  return (
    <div className="container mx-auto px-4 py-16 space-y-16">
      <div className="text-center">
        <h1 className="text-4xl font-bold text-[#0A2647] mb-4">StyledImage Component Examples</h1>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Demonstrating the beautiful image styling from the CEO image applied to different use cases
        </p>
      </div>

      {/* Portfolio Grid Example */}
      <section>
        <h2 className="text-2xl font-bold text-[#0A2647] mb-8">Portfolio Grid Layout</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <StyledImage
            src="/images/portfolio/logo-types/wordmark1.png"
            alt="Logo Design Example"
            aspectRatio="aspect-square"
            className="max-w-sm mx-auto"
          />
          <StyledImage
            src="/images/portfolio/logo-types/lettermark1.png"
            alt="Lettermark Design"
            aspectRatio="aspect-square"
            className="max-w-sm mx-auto"
          />
          <StyledImage
            src="/images/about/ceo.jpg"
            alt="Team Member"
            aspectRatio="aspect-[4/5]"
            className="max-w-sm mx-auto"
          />
        </div>
      </section>

      {/* Different Aspect Ratios */}
      <section>
        <h2 className="text-2xl font-bold text-[#0A2647] mb-8">Different Aspect Ratios</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div>
            <h3 className="text-lg font-semibold mb-4">Square (1:1)</h3>
            <StyledImage
              src="/images/portfolio/logo-types/wordmark1.png"
              alt="Square Image"
              aspectRatio="aspect-square"
              className="max-w-md mx-auto"
            />
          </div>
          <div>
            <h3 className="text-lg font-semibold mb-4">Portrait (4:5)</h3>
            <StyledImage
              src="/images/about/ceo.jpg"
              alt="Portrait Image"
              aspectRatio="aspect-[4/5]"
              className="max-w-md mx-auto"
            />
          </div>
        </div>
      </section>

      {/* Customization Options */}
      <section>
        <h2 className="text-2xl font-bold text-[#0A2647] mb-8">Customization Options</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <div>
            <h3 className="text-lg font-semibold mb-4">No Decorations</h3>
            <StyledImage
              src="/images/portfolio/logo-types/lettermark1.png"
              alt="No Decorations"
              aspectRatio="aspect-square"
              showDecorations={false}
              className="max-w-sm mx-auto"
            />
          </div>
          <div>
            <h3 className="text-lg font-semibold mb-4">No Hover Effect</h3>
            <StyledImage
              src="/images/portfolio/logo-types/wordmark1.png"
              alt="No Hover"
              aspectRatio="aspect-square"
              hoverEffect={false}
              className="max-w-sm mx-auto"
            />
          </div>
          <div>
            <h3 className="text-lg font-semibold mb-4">No Gradient Overlay</h3>
            <StyledImage
              src="/images/about/ceo.jpg"
              alt="No Gradient"
              aspectRatio="aspect-square"
              overlayGradient={false}
              className="max-w-sm mx-auto"
            />
          </div>
        </div>
      </section>

      {/* Usage Code Examples */}
      <section className="bg-gray-50 rounded-2xl p-8">
        <h2 className="text-2xl font-bold text-[#0A2647] mb-6">Usage Examples</h2>
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-semibold mb-2">Basic Usage</h3>
            <pre className="bg-gray-800 text-green-400 p-4 rounded-lg overflow-x-auto text-sm">
{`<StyledImage
  src="/path/to/image.jpg"
  alt="Description"
  aspectRatio="aspect-square"
/>`}
            </pre>
          </div>
          
          <div>
            <h3 className="text-lg font-semibold mb-2">Portfolio Grid Item</h3>
            <pre className="bg-gray-800 text-green-400 p-4 rounded-lg overflow-x-auto text-sm">
{`<StyledImage
  src="/images/portfolio/logo.png"
  alt="Logo Design"
  aspectRatio="aspect-square"
  className="max-w-sm mx-auto"
  priority={true}
/>`}
            </pre>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-2">Team Member Photo</h3>
            <pre className="bg-gray-800 text-green-400 p-4 rounded-lg overflow-x-auto text-sm">
{`<StyledImage
  src="/images/team/member.jpg"
  alt="Team Member"
  aspectRatio="aspect-[4/5]"
  objectPosition="center 20%"
  className="max-w-md"
/>`}
            </pre>
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-2">Minimal Style (No Decorations)</h3>
            <pre className="bg-gray-800 text-green-400 p-4 rounded-lg overflow-x-auto text-sm">
{`<StyledImage
  src="/images/product.jpg"
  alt="Product Image"
  aspectRatio="aspect-[3/4]"
  showDecorations={false}
  overlayGradient={false}
/>`}
            </pre>
          </div>
        </div>
      </section>
    </div>
  );
} 