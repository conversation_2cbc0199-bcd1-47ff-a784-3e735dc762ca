'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import type { ImageItem } from '@/utils/getImages';

interface LogosGalleryWrapperProps {
  logos?: ImageItem[];
}

export default function LogosGalleryWrapper({ logos = [] }: LogosGalleryWrapperProps) {
  const [isClient, setIsClient] = useState(false);
  const [showingDownload, setShowingDownload] = useState<string | number | null>(null);

  // Handle download functionality
  const handleDownload = async (item: ImageItem) => {
    const imageUrl = item.url || item.src;
    const fileName = `${item.alt || item.title || 'logo'}.jpg`;
    
    try {
      // Fetch the image as blob to force download without redirect
      const response = await fetch(imageUrl);
      if (!response.ok) throw new Error('Network response was not ok');
      
      const blob = await response.blob();
      
      // Create download link with blob URL
      const link = document.createElement('a');
      const url = window.URL.createObjectURL(blob);
      link.href = url;
      link.download = fileName;
      
      // Trigger download
      document.body.appendChild(link);
      link.click();
      
      // Cleanup
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Download failed:', error);
      // Fallback: try direct link method for same-origin images
      const link = document.createElement('a');
      link.href = imageUrl;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
    
    // Hide download button after download
    setShowingDownload(null);
  };

  // Initialize on the client side only
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Don't render anything on the server side
  if (!isClient) {
    return (
      <div className="w-full text-center py-12">
        <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto"></div>
      </div>
    );
  }

  // Ensure logos is always an array with required properties
  const safeLogos = Array.isArray(logos) ? logos.filter(logo =>
    logo && typeof logo === 'object' && (
      (logo.url && typeof logo.url === 'string') ||
      (logo.src && typeof logo.src === 'string')
    )
  ) : [];

  if (!safeLogos.length) {
    return (
      <div className="text-center py-12 text-gray-500">
        <p>No logo examples available at the moment.</p>
        <p className="mt-2 text-sm">Please check back later or contact us to see our portfolio.</p>
      </div>
    );
  }

  return (
    <div className="w-full">
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 md:gap-6 lg:gap-8">
        {safeLogos.map((item) => (
          <div
            key={item.id}
            className="relative aspect-square bg-gray-100 rounded-lg overflow-hidden cursor-pointer transition-transform hover:scale-105"
            onClick={() => {
              // Show download button for all devices
              const itemId = item.id;
              setShowingDownload(showingDownload === itemId ? null : itemId);
            }}
          >
            <Image
              src={item.url || item.src}
              alt={item.alt || `Logo design example`}
              fill
              className="object-contain hover:opacity-90 transition-opacity duration-300"
              sizes="(max-width: 640px) 50vw, (max-width: 768px) 50vw, 33vw"
              loading="lazy"
            />

            {/* Download Button - shows when image is clicked */}
            {showingDownload === item.id && (
              <div className="absolute inset-0 bg-black/30 backdrop-blur-sm flex items-center justify-center z-20 p-3 sm:p-4">
                <div className="bg-white/5 backdrop-blur-xl border border-white/15 rounded-lg sm:rounded-2xl p-3 sm:p-6 mx-3 sm:mx-4 shadow-2xl w-[280px] sm:max-w-sm sm:w-full" style={{
                  background: 'rgba(255, 255, 255, 0.05)',
                  backdropFilter: 'blur(15px)',
                  WebkitBackdropFilter: 'blur(15px)',
                  border: '1px solid rgba(255, 255, 255, 0.15)',
                  boxShadow: '0 8px 32px 0 rgba(31, 38, 135, 0.2)'
                }}>
                  <div className="text-center space-y-2 sm:space-y-4">
                    <div className="w-10 h-10 sm:w-16 sm:h-16 bg-[#FF5400] rounded-full flex items-center justify-center mx-auto">
                      <svg className="w-5 h-5 sm:w-8 sm:h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="text-sm sm:text-lg font-semibold text-white">
                        Download Logo
                      </h3>
                    </div>
                    <div className="flex flex-col sm:flex-row space-y-1.5 sm:space-y-0 sm:space-x-3">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          setShowingDownload(null);
                        }}
                        className="flex-1 px-3 sm:px-4 py-1.5 sm:py-2.5 border border-white/30 text-white rounded-md sm:rounded-lg hover:bg-white/10 transition-colors duration-200 backdrop-blur-sm text-xs sm:text-base"
                      >
                        Cancel
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDownload(item);
                        }}
                        className="flex-1 px-3 sm:px-4 py-1.5 sm:py-2.5 bg-[#FF5400] text-white rounded-md sm:rounded-lg hover:bg-[#FF7A3A] transition-colors duration-200 flex items-center justify-center space-x-1.5 sm:space-x-2 text-xs sm:text-base"
                      >
                        <svg className="w-3 h-3 sm:w-4 sm:h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3" />
                        </svg>
                        <span>Download</span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>


    </div>
  );
}