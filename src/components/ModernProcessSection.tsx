'use client';
import React from 'react';

import { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence, useAnimation } from 'framer-motion';

interface ProcessStep {
  step: string;
  title: string;
  description: string;
  icon?: string;
}

interface ModernProcessSectionProps {
  steps: ProcessStep[];
  title?: string;
  description?: string;
  badge?: string;
  badgeIcon?: string;
  highlightWord?: string;
}

export default function ModernProcessSection({
  steps,
  title = "Our Process",
  description = "We follow a systematic approach to deliver exceptional results",
  badge,
  badgeIcon = "fas fa-cogs",
  highlightWord
}: ModernProcessSectionProps) {
  const [mounted, setMounted] = useState(false);
  const [activeStep, setActiveStep] = useState<number | null>(0);
  const [autoPlay, setAutoPlay] = useState(true);
  const autoPlayTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Helper function to render title with highlighted word
  const renderTitle = () => {
    if (!highlightWord || !title.includes(highlightWord)) {
      return title;
    }

    const parts = title.split(highlightWord);
    return (
      <>
        {parts[0]}
        <span className="text-[#FF5400]">{highlightWord}</span>
        {parts[1]}
      </>
    );
  };

  // Auto-cycle through steps
  useEffect(() => {
    if (autoPlay && mounted) {
      autoPlayTimerRef.current = setInterval(() => {
        setActiveStep(prev => {
          if (prev === null) return 0;
          return (prev + 1) % steps.length;
        });
      }, 5000); // Change step every 5 seconds
    }

    return () => {
      if (autoPlayTimerRef.current) {
        clearInterval(autoPlayTimerRef.current);
      }
    };
  }, [autoPlay, steps.length, mounted]);

  // Pause auto-play when user interacts
  const handleStepClick = (index: number) => {
    setActiveStep(index);
    setAutoPlay(false); // Stop auto-cycling when user clicks

    // Resume auto-play after 15 seconds of inactivity
    if (autoPlayTimerRef.current) {
      clearTimeout(autoPlayTimerRef.current);
    }

    autoPlayTimerRef.current = setTimeout(() => {
      setAutoPlay(true);
    }, 15000);
  };



  useEffect(() => {
    setMounted(true);

    // Clean up any timers when component unmounts
    return () => {
      if (autoPlayTimerRef.current) {
        clearInterval(autoPlayTimerRef.current);
      }
    };
  }, []);

  if (!mounted) {
    return (
      <section className="py-10 sm:py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-6 sm:mb-8">
            {badge && (
              <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-[#FF5400]/10 border border-[#FF5400]/20 mb-6">
                <i className={`${badgeIcon} text-[#FF5400]`}></i>
                <span className="text-sm font-medium text-[#FF5400]">{badge}</span>
              </div>
            )}
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-black text-gray-900 mb-6">
              {renderTitle()}
            </h2>
            <p className="text-base md:text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed">
              {description}
            </p>
          </div>

          <div className="max-w-5xl mx-auto">
            <div className="flex justify-between mb-6 sm:mb-8 animate-pulse px-2">
              {[1, 2, 3, 4].map((i) => (
                <div key={i} className="w-10 h-10 sm:w-16 sm:h-16 bg-gray-200 rounded-full"></div>
              ))}
            </div>
            <div className="h-[150px] sm:h-[200px] bg-gray-200 rounded-xl animate-pulse"></div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-10 sm:py-16 bg-white">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="text-center mb-6 sm:mb-10"
        >
          {badge && (
            <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-[#FF5400]/10 border border-[#FF5400]/20 mb-6">
              <i className={`${badgeIcon} text-[#FF5400]`}></i>
              <span className="text-sm font-medium text-[#FF5400]">{badge}</span>
            </div>
          )}
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-black text-gray-900 mb-6">
            {renderTitle()}
          </h2>
          <p className="text-base md:text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed">
            {description}
          </p>
        </motion.div>

        <div className="max-w-5xl mx-auto">
          {/* Process Timeline */}
          <div className="mb-8 sm:mb-12">
            {/* Desktop Timeline */}
            <div className="hidden md:block relative">
              {/* Timeline Line */}
              <div className="absolute left-0 right-0 h-[2px] top-[40px] bg-gray-200"></div>

              {/* Progress Line */}
              <div
                className="absolute left-0 h-[2px] top-[40px] bg-[#FF5400] transition-all duration-500"
                style={{
                  width: activeStep !== null ? `${(activeStep / (steps.length - 1)) * 100}%` : '0%',
                }}
              ></div>

              {/* Steps */}
              <div className="flex justify-between relative">
                {steps.map((step, index) => (
                  <div key={step.step} className="flex flex-col items-center relative">
                    <motion.button
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.4, delay: index * 0.1 }}
                      className="relative z-10 mb-3"
                      onClick={() => setActiveStep(index)}
                      onMouseEnter={() => {
                        setActiveStep(index);
                        setAutoPlay(false);
                      }}
                    >
                      <div
                        className={`w-16 h-16 lg:w-20 lg:h-20 rounded-full flex items-center justify-center text-lg lg:text-xl font-bold transition-all duration-300 ${
                          activeStep === index
                            ? 'bg-[#FF5400] text-white scale-110 shadow-lg'
                            : 'bg-white text-[#0A1929] border-2 border-[#0A1929]/10 hover:border-[#FF5400]/50'
                        }`}
                      >
                        {step.step}
                      </div>
                    </motion.button>
                    <motion.div
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ duration: 0.4, delay: index * 0.1 + 0.2 }}
                      className="text-center"
                    >
                      <h3 className={`text-sm lg:text-base font-bold transition-colors duration-300 ${
                        activeStep === index ? 'text-[#FF5400]' : 'text-[#0A1929]'
                      }`}>
                        {step.title}
                      </h3>
                    </motion.div>
                  </div>
                ))}
              </div>
            </div>

            {/* Mobile Timeline */}
            <div className="md:hidden">
              {/* Mobile Progress Indicator */}
              <div className="relative h-1 bg-gray-200 rounded-full mb-4 mx-4">
                <motion.div
                  className="absolute left-0 h-1 bg-[#FF5400] rounded-full"
                  initial={{ width: '0%' }}
                  animate={{
                    width: activeStep !== null ? `${(activeStep / (steps.length - 1)) * 100}%` : '0%'
                  }}
                  transition={{ duration: 0.5 }}
                ></motion.div>
              </div>

                             {/* Mobile Steps */}
               <div className="flex overflow-x-auto py-2 px-2 space-x-3 hide-scrollbar snap-x snap-mandatory">
                 {steps.map((step, index) => (
                   <motion.button
                     key={step.step}
                     initial={{ opacity: 0, x: 20 }}
                     animate={{ opacity: 1, x: 0 }}
                     whileTap={{ scale: 0.95 }}
                     transition={{ duration: 0.4, delay: index * 0.1 }}
                     className={`flex flex-col items-center min-w-[70px] p-2 rounded-lg transition-all snap-start ${
                       activeStep === index
                         ? 'bg-[#FF5400]/10'
                         : 'bg-transparent hover:bg-gray-100'
                     }`}
                     onClick={() => handleStepClick(index)}
                   >
                     <div
                       className={`w-12 h-12 rounded-full flex items-center justify-center text-base font-bold mb-1.5 transition-all duration-300 ${
                         activeStep === index
                           ? 'bg-[#FF5400] text-white scale-110 shadow-sm'
                           : 'bg-white text-[#0A1929] border border-[#0A1929]/20'
                       }`}
                     >
                       {step.step}
                     </div>
                     <div className={`text-xs font-medium text-center ${
                       activeStep === index ? 'text-[#FF5400]' : 'text-[#0A1929]'
                     }`}>
                       {step.title}
                     </div>
                   </motion.button>
                 ))}
               </div>
            </div>
          </div>

          
        </div>
      </div>
    </section>
  );
}
