'use client';

import { motion } from 'framer-motion';

export default function ServicesSlider() {
  return (
    <div className="bg-[#0A1929] py-4 relative overflow-hidden">
      <motion.div
        animate={{
          x: [0, -1000],
        }}
        transition={{
          x: {
            duration: 20,
            repeat: Infinity,
            ease: "linear"
          }
        }}
        className="whitespace-nowrap"
      >
        <div className="inline-flex items-center text-white text-lg font-medium">
          <span>Web Development</span>
          <span className="mx-4 text-[#FF5400]">. . .</span>
          <span>Graphic Design</span>
          <span className="mx-4 text-[#FF5400]">. . .</span>
          <span>Digital Marketing</span>
          <span className="mx-4 text-[#FF5400]">. . .</span>
          <span>Brand Identity</span>
          <span className="mx-4 text-[#FF5400]">. . .</span>
          <span>UI/UX Design</span>
          <span className="mx-4 text-[#FF5400]">. . .</span>
          <span>Social Media Management</span>
          <span className="mx-4 text-[#FF5400]">. . .</span>
          <span>SEO Optimization</span>
          <span className="mx-4 text-[#FF5400]">. . .</span>
          <span>Content Creation</span>
          <span className="mx-4 text-[#FF5400]">. . .</span>
          <span>E-commerce Solutions</span>
          <span className="mx-4 text-[#FF5400]">. . .</span>
          <span>Mobile App Development</span>
          {/* Duplicate the list to create seamless loop */}
          <span className="mx-4 text-[#FF5400]">. . .</span>
          <span>Web Development</span>
          <span className="mx-4 text-[#FF5400]">. . .</span>
          <span>Graphic Design</span>
          <span className="mx-4 text-[#FF5400]">. . .</span>
          <span>Digital Marketing</span>
          <span className="mx-4 text-[#FF5400]">. . .</span>
          <span>Brand Identity</span>
          <span className="mx-4 text-[#FF5400]">. . .</span>
          <span>UI/UX Design</span>
          <span className="mx-4 text-[#FF5400]">. . .</span>
          <span>Social Media Management</span>
          <span className="mx-4 text-[#FF5400]">. . .</span>
          <span>SEO Optimization</span>
          <span className="mx-4 text-[#FF5400]">. . .</span>
          <span>Content Creation</span>
          <span className="mx-4 text-[#FF5400]">. . .</span>
          <span>E-commerce Solutions</span>
          <span className="mx-4 text-[#FF5400]">. . .</span>
          <span>Mobile App Development</span>
        </div>
      </motion.div>
    </div>
  );
} 