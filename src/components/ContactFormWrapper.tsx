'use client';

import React, { useState, useEffect } from 'react';
import dynamic from 'next/dynamic';

// Dynamically import the ContactForm component with proper error handling
const ContactForm = dynamic(() => import('./ContactForm'), {
  ssr: false,
  loading: () => (
    <div className="space-y-6" aria-label="Loading contact form">
      <div className="h-12 bg-gray-100 rounded animate-pulse"></div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="h-16 bg-gray-100 rounded animate-pulse"></div>
        <div className="h-16 bg-gray-100 rounded animate-pulse"></div>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="h-16 bg-gray-100 rounded animate-pulse"></div>
        <div className="h-16 bg-gray-100 rounded animate-pulse"></div>
      </div>
      <div className="h-32 bg-gray-100 rounded animate-pulse"></div>
      <div className="h-14 bg-gray-100 rounded animate-pulse"></div>
    </div>
  )
});

export default function ContactFormWrapper() {
  const [mounted, setMounted] = useState(false);
  const [hasError, setHasError] = useState(false);

  // Set mounted state when component mounts
  useEffect(() => {
    setMounted(true);
    return () => setMounted(false);
  }, []);

  // Error handler for dynamic import failures
  const handleDynamicImportError = (error: Error) => {
    console.error('Error loading ContactForm:', error);
    setHasError(true);
  };

  // Show error state if dynamic import failed
  if (hasError) {
    return (
      <div className="p-4 bg-red-50 text-red-700 rounded-lg">
        <p className="font-medium">Unable to load contact form</p>
        <p className="text-sm mt-1">
          There was an error loading the contact form. Please try again later or contact us directly at{' '}
          <a href="mailto:<EMAIL>" className="underline hover:no-underline">
            <EMAIL>
          </a>
        </p>
        <button
          onClick={() => window.location.reload()}
          className="mt-2 px-3 py-1 bg-red-100 text-red-800 rounded text-sm hover:bg-red-200 transition-colors"
        >
          Retry
        </button>
      </div>
    );
  }

  // Only render ContactForm on the client-side
  if (!mounted) {
    return (
      <div className="space-y-6" aria-label="Loading contact form">
        <div className="h-12 bg-gray-100 rounded animate-pulse"></div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="h-16 bg-gray-100 rounded animate-pulse"></div>
          <div className="h-16 bg-gray-100 rounded animate-pulse"></div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="h-16 bg-gray-100 rounded animate-pulse"></div>
          <div className="h-16 bg-gray-100 rounded animate-pulse"></div>
        </div>
        <div className="h-32 bg-gray-100 rounded animate-pulse"></div>
        <div className="h-14 bg-gray-100 rounded animate-pulse"></div>
      </div>
    );
  }

  // Render the ContactForm with error boundary
  try {
    return (
      <React.Suspense 
        fallback={
          <div className="space-y-6" aria-label="Loading contact form">
            <div className="h-12 bg-gray-100 rounded animate-pulse"></div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="h-16 bg-gray-100 rounded animate-pulse"></div>
              <div className="h-16 bg-gray-100 rounded animate-pulse"></div>
            </div>
            <div className="h-32 bg-gray-100 rounded animate-pulse"></div>
            <div className="h-14 bg-gray-100 rounded animate-pulse"></div>
          </div>
        }
      >
        <ContactForm />
      </React.Suspense>
    );
  } catch (error) {
    handleDynamicImportError(error as Error);
    return null; // Component will re-render with error state
  }
} 