'use client';

import { useEffect, useState } from 'react';

interface ClientLayoutProps {
  children: React.ReactNode;
}

export default function ClientLayout({ children }: ClientLayoutProps) {
  const [isAdminPath, setIsAdminPath] = useState(false);
  const [isLoginPage, setIsLoginPage] = useState(false);

  useEffect(() => {
    // Check if we're in the admin section after component mounts
    const pathname = window.location.pathname;
    setIsAdminPath(pathname.startsWith('/admin'));
    setIsLoginPage(pathname === '/admin/login');
  }, []);

  // If it's the admin login page, don't add any padding
  if (isLoginPage) {
    return <>{children}</>;
  }

  // If it's an admin page, wrap with admin layout
  if (isAdminPath) {
    return (
      <div className="min-h-screen pt-16">
        {children}
      </div>
    );
  }

  // Otherwise, apply the client-side layout
  return (
    <div className="min-h-screen pt-20">
      {children}
    </div>
  );
} 