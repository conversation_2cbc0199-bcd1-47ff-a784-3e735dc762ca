'use client';
import React from 'react';

import { useState, useEffect, useMemo } from 'react';
import Image from 'next/image';
import type { ImageItem } from '@/utils/getImages';

// Enhanced fallback image with better quality
const FALLBACK_IMAGE = '/images/placeholder.jpg';
// Number of images to load initially and when "Load More" is clicked
const ITEMS_PER_PAGE = 12;

interface ClientGallerySectionProps {
  items?: ImageItem[];
  gridCols?: string;
  aspectRatio?: string;
  objectFit?: string;
  category?: string;
}

// Enhanced image validation and URL normalization
function normalizeImageUrl(url: string): string {
  if (!url || typeof url !== 'string') return FALLBACK_IMAGE;
  
  // Handle relative URLs
  if (url.startsWith('/')) return url;
  
  // Validate S3 URLs
  if (url.includes('linodeobjects.com') || url.includes('amazonaws.com')) {
    return url;
  }
  
  // If it doesn't look like a valid URL, return fallback
  if (!url.startsWith('http')) return FALLBACK_IMAGE;
  
  return url;
}

// Enhanced image validation
function isValidImageItem(item: any): item is ImageItem {
  return (
    item &&
    typeof item === 'object' &&
    (typeof item.id === 'number' || typeof item.id === 'string') &&
    (typeof item.url === 'string' || typeof item.src === 'string') &&
    (item.url || item.src)
  );
}

export default function ClientGallerySection({
  items = [],
  gridCols = "grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-3",
  aspectRatio = "aspect-video",
  objectFit = "object-cover",
  category = "gallery"
}: ClientGallerySectionProps) {
  // Client rendering flag
  const [mounted, setMounted] = useState(false);
  // Manage client-side state
  const [loadingImages, setLoadingImages] = useState<Set<string | number>>(new Set());
  const [failedImages, setFailedImages] = useState<Set<string | number>>(new Set());
  const [loadedImages, setLoadedImages] = useState<Set<string | number>>(new Set());
  const [visibleItems, setVisibleItems] = useState<number>(ITEMS_PER_PAGE);
  const [isLoadingMore, setIsLoadingMore] = useState<boolean>(false);
  const [retryAttempts, setRetryAttempts] = useState<Map<string | number, number>>(new Map());

  // Function to detect mobile devices
  const isMobileDevice = () => {
    if (typeof window === 'undefined') return false;
    return window.innerWidth <= 768 || /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
  };

  // State for showing download button on mobile
  const [showingDownload, setShowingDownload] = useState<string | number | null>(null);

  // Handle download functionality
  const handleDownload = async (item: ImageItem) => {
    const imageUrl = normalizeImageUrl(item.url || item.src);
    const fileName = `${item.alt || item.title || 'image'}.jpg`;
    
    try {
      // Fetch the image as blob to force download without redirect
      const response = await fetch(imageUrl);
      if (!response.ok) throw new Error('Network response was not ok');
      
      const blob = await response.blob();
      
      // Create download link with blob URL
      const link = document.createElement('a');
      const url = window.URL.createObjectURL(blob);
      link.href = url;
      link.download = fileName;
      
      // Trigger download
      document.body.appendChild(link);
      link.click();
      
      // Cleanup
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Download failed:', error);
      // Fallback: try direct link method for same-origin images
      const link = document.createElement('a');
      link.href = imageUrl;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
    
    // Hide download button after download
    setShowingDownload(null);
  };

  // Enhanced items validation and processing
  const safeItems = useMemo(() => {
    if (!Array.isArray(items)) return [];
    
    return items
      .filter(isValidImageItem)
      .map((item, index) => ({
        ...item,
        id: item.id || `fallback-${index}`,
        url: item.url || item.src || FALLBACK_IMAGE,
        src: item.src || item.url || FALLBACK_IMAGE,
        alt: item.alt || item.title || `${category} image ${index + 1}`,
        title: item.title || item.alt || `${category} image ${index + 1}`
      }));
  }, [items, category]);

  // Set mounted state and prepare safe items
  useEffect(() => {
    setMounted(true);
  }, []);

  // Initialize loadingImages only for initial items after component mounts
  useEffect(() => {
    if (safeItems.length > 0 && mounted && loadingImages.size === 0) {
      const initialLoadingSet = new Set<string | number>();
      safeItems.slice(0, ITEMS_PER_PAGE).forEach(item => {
        if (item && item.id) {
          initialLoadingSet.add(item.id);
        }
      });
      setLoadingImages(initialLoadingSet);
    }
  }, [safeItems, mounted]); // Removed visibleItems dependency to prevent re-initialization



  // Cleanup on unmount
  useEffect(() => {
    return () => {
      setLoadingImages(new Set());
      setFailedImages(new Set());
      setLoadedImages(new Set());
    };
  }, []);

  // Enhanced image load handler
  const handleImageLoad = (id: string | number) => {
    setLoadingImages(prev => {
      const next = new Set(prev);
      next.delete(id);
      return next;
    });
    
    setLoadedImages(prev => {
      const next = new Set(prev);
      next.add(id);
      return next;
    });
    
    // Clear any retry attempts for successful loads
    setRetryAttempts(prev => {
      const next = new Map(prev);
      next.delete(id);
      return next;
    });
  };

  // Enhanced error handler with retry logic
  const handleImageError = (id: string | number, originalUrl: string) => {
    const currentAttempts = retryAttempts.get(id) || 0;
    const maxRetries = 2;

    // Remove from loading state
    setLoadingImages(prev => {
      const next = new Set(prev);
      next.delete(id);
      return next;
    });

    if (currentAttempts < maxRetries) {
      // Increment retry attempts
      setRetryAttempts(prev => {
        const next = new Map(prev);
        next.set(id, currentAttempts + 1);
        return next;
      });

      // Retry after a delay
      setTimeout(() => {
        // Force image reload by updating the src
        const imgElement = document.querySelector(`img[data-image-id="${id}"]`) as HTMLImageElement;
        if (imgElement) {
          imgElement.src = `${originalUrl}?retry=${currentAttempts + 1}&t=${Date.now()}`;
        }
      }, 1000 * (currentAttempts + 1)); // Exponential backoff
    } else {
      // Max retries reached, mark as failed
      setFailedImages(prev => {
        const next = new Set(prev);
        next.add(id);
        return next;
      });

      // Only log in development to avoid console spam in production
      if (process.env.NODE_ENV === 'development') {
        console.warn(`Image failed to load after ${maxRetries} retries:`, originalUrl);
      }
    }
  };

  const handleLoadMore = () => {
    // Only run on client side
    if (!mounted) return;

    setIsLoadingMore(true);
    const previousVisible = visibleItems;
    const newVisible = Math.min(visibleItems + ITEMS_PER_PAGE, safeItems.length);

    // Add loading state only for new images
    const newItems = safeItems.slice(previousVisible, newVisible);
    setLoadingImages(prev => {
      const next = new Set(prev);
      newItems.forEach(item => {
        if (item && item.id && !loadedImages.has(item.id)) {
          next.add(item.id);
        }
      });
      return next;
    });

    // Simulate loading delay for better UX
    setTimeout(() => {
      setVisibleItems(newVisible);
      setIsLoadingMore(false);
    }, 500);
  };

  // Pre-mounting state - very simple JSX to avoid hydration issues
  if (!mounted) {
    return (
      <div className="w-full min-h-[300px] py-8">
        <div className={`grid ${gridCols} gap-4 md:gap-6 lg:gap-8`}>
          {Array.from({ length: Math.min(ITEMS_PER_PAGE, Array.isArray(items) ? items.length : 0) }).map((_, index) => (
            <div
              key={`skeleton-${index}`}
              className={`relative ${aspectRatio} bg-gradient-to-br from-gray-100 to-gray-200 rounded-2xl overflow-hidden animate-pulse shadow-sm`}
            />
          ))}
        </div>
      </div>
    );
  }

  // Post-mounting state with user interaction
  if (!safeItems.length) {
    return (
      <div className="w-full py-20 text-center">
        <div className="max-w-md mx-auto">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
          </div>
          <p className="text-gray-500 text-lg">No images available</p>
          <p className="text-gray-400 text-sm mt-2">Images will appear here once they are uploaded</p>
        </div>
      </div>
    );
  }

  // Only show the number of items set in visibleItems state
  const displayedItems = safeItems.slice(0, visibleItems);

  return (
    <div className="w-full">
      <div className={`grid ${gridCols} gap-4 md:gap-6 lg:gap-8`}>
        {displayedItems.map((item, index) => {
          const imageUrl = normalizeImageUrl(item.url || item.src);
          const isLoading = loadingImages.has(item.id) && !loadedImages.has(item.id);
          const hasFailed = failedImages.has(item.id);
          const retryCount = retryAttempts.get(item.id) || 0;

          return (
            <div
              key={item.id}
              className={`group relative ${aspectRatio} bg-white rounded-2xl overflow-hidden cursor-pointer shadow-sm hover:shadow-xl transition-all duration-500 transform hover:-translate-y-2 border border-gray-100 hover:border-gray-200`}
              onClick={() => {
                // Show download button for all devices instead of modal
                const itemId = item.id;
                setShowingDownload(showingDownload === itemId ? null : itemId);
              }}
            >
              {/* Loading state - only show for images that haven't loaded yet */}
              {isLoading && (
                <div className="absolute inset-0 flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 z-10">
                  <div className="flex flex-col items-center space-y-2">
                    <div className="w-8 h-8 border-3 border-[#FF5400] border-t-transparent rounded-full animate-spin" />
                    <span className="text-xs text-gray-500">
                      {retryCount > 0 ? `Retrying... (${retryCount}/2)` : 'Loading...'}
                    </span>
                  </div>
                </div>
              )}

              {/* Error state */}
              {hasFailed && (
                <div className="absolute inset-0 flex items-center justify-center bg-gradient-to-br from-gray-100 to-gray-200 z-10">
                  <div className="flex flex-col items-center space-y-2 text-center p-4">
                    <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                    <span className="text-xs text-gray-500">Image unavailable</span>
                  </div>
                </div>
              )}
              
              {/* Main Image */}
              <Image
                src={hasFailed ? FALLBACK_IMAGE : imageUrl}
                alt={item.alt || `${category} image`}
                fill
                className={`${objectFit} transition-all duration-700 group-hover:scale-110 ${hasFailed ? 'opacity-50' : ''}`}
                sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw"
                data-image-id={item.id}
                onLoadStart={() => {
                  // Only add to loading if not already loaded
                  if (!loadedImages.has(item.id)) {
                    setLoadingImages(prev => {
                      const next = new Set(prev);
                      next.add(item.id);
                      return next;
                    });
                  }
                }}
                onLoad={() => handleImageLoad(item.id)}
                onError={() => handleImageError(item.id, imageUrl)}
                loading={index < 4 ? "eager" : "lazy"}
                priority={index < 4}
                unoptimized={process.env.NODE_ENV === 'development'}
              />

              {/* Overlay */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-500 flex flex-col justify-between p-4">
                <div className="flex justify-end">
                  <span className="px-3 py-1 bg-white/20 backdrop-blur-sm text-white text-xs font-medium rounded-full border border-white/20">
                    {category}
                  </span>
                </div>
                
                <div className="space-y-2">
                  <h3 className="text-white text-lg font-semibold leading-tight line-clamp-2">
                    {item.alt || item.title || 'Design Piece'}
                  </h3>
                  <div className="flex items-center justify-between">
                    <p className="text-white/80 text-sm capitalize">
                      {item.category || category}
                    </p>
                    <div className="w-8 h-8 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center border border-white/20">
                      <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>

              {/* Download Button - shows when image is clicked */}
              {showingDownload === item.id && (
                <div className="absolute inset-0 bg-black/30 backdrop-blur-sm flex items-center justify-center z-20 p-3 sm:p-4">
                  <div className="bg-white/5 backdrop-blur-xl border border-white/15 rounded-lg sm:rounded-2xl p-3 sm:p-6 mx-3 sm:mx-4 shadow-2xl w-[280px] sm:max-w-sm sm:w-full" style={{
                    background: 'rgba(255, 255, 255, 0.05)',
                    backdropFilter: 'blur(15px)',
                    WebkitBackdropFilter: 'blur(15px)',
                    border: '1px solid rgba(255, 255, 255, 0.15)',
                    boxShadow: '0 8px 32px 0 rgba(31, 38, 135, 0.2)'
                  }}>
                    <div className="text-center space-y-2 sm:space-y-4">
                      <div className="w-10 h-10 sm:w-16 sm:h-16 bg-[#FF5400] rounded-full flex items-center justify-center mx-auto">
                        <svg className="w-5 h-5 sm:w-8 sm:h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                      </div>
                      <div>
                        <h3 className="text-sm sm:text-lg font-semibold text-white">
                          Download Image
                        </h3>
                      </div>
                      <div className="flex flex-col sm:flex-row space-y-1.5 sm:space-y-0 sm:space-x-3">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            setShowingDownload(null);
                          }}
                          className="flex-1 px-3 sm:px-4 py-1.5 sm:py-2.5 border border-white/30 text-white rounded-md sm:rounded-lg hover:bg-white/10 transition-colors duration-200 backdrop-blur-sm text-xs sm:text-base"
                        >
                          Cancel
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleDownload({
                              ...item,
                              id: typeof item.id === 'string' ? parseInt(item.id) || 0 : item.id
                            });
                          }}
                          className="flex-1 px-3 sm:px-4 py-1.5 sm:py-2.5 bg-[#FF5400] text-white rounded-md sm:rounded-lg hover:bg-[#FF7A3A] transition-colors duration-200 flex items-center justify-center space-x-1.5 sm:space-x-2 text-xs sm:text-base"
                        >
                          <svg className="w-3 h-3 sm:w-4 sm:h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3" />
                          </svg>
                          <span>Download</span>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Corner accent for square images */}
              {aspectRatio === "aspect-square" && (
                <div className="absolute top-4 left-4 w-3 h-3 bg-gradient-to-br from-[#FF5400] to-[#FF7A3A] rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              )}
            </div>
          );
        })}
      </div>

      {/* Enhanced Load More Button */}
      {visibleItems < safeItems.length && (
        <div className="mt-12 text-center">
          <button
            onClick={handleLoadMore}
            disabled={isLoadingMore}
            className="group px-8 py-4 bg-gradient-to-r from-[#FF5400] to-[#FF7A3A] text-white rounded-full font-medium hover:from-[#FF7A3A] hover:to-[#FF5400] transition-all duration-300 disabled:opacity-75 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
          >
            {isLoadingMore ? (
              <>
                <span className="inline-block w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></span>
                Loading {category}...
              </>
            ) : (
              <>
                Load More {category}
                <span className="ml-2 group-hover:translate-x-1 transition-transform duration-200">→</span>
              </>
            )}
          </button>
          <p className="text-gray-500 text-sm mt-3">
            Showing {visibleItems} of {safeItems.length} items
          </p>
        </div>
      )}

    </div>
  );
}