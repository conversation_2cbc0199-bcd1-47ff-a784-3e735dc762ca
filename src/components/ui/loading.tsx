import React from 'react';
import { cn } from '@/lib/utils';
import { Loader2, ShoppingCart, Upload, CheckCircle } from 'lucide-react';

interface LoadingProps {
  variant?: 'spinner' | 'dots' | 'pulse' | 'skeleton';
  size?: 'sm' | 'md' | 'lg';
  text?: string;
  fullScreen?: boolean;
  overlay?: boolean;
  className?: string;
}

interface ProgressLoadingProps {
  progress: number;
  text?: string;
  icon?: React.ReactNode;
  className?: string;
}

interface SkeletonProps {
  className?: string;
  lines?: number;
}

// Basic loading spinner
export const Loading: React.FC<LoadingProps> = ({
  variant = 'spinner',
  size = 'md',
  text,
  fullScreen = false,
  overlay = false,
  className
}) => {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-8 w-8',
    lg: 'h-12 w-12'
  };

  const textSizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg'
  };

  const LoadingContent = () => {
    switch (variant) {
      case 'spinner':
        return (
          <div className="flex flex-col items-center gap-3">
            <Loader2 className={cn('animate-spin text-orange-500', sizeClasses[size])} />
            {text && (
              <p className={cn('text-gray-600 font-medium', textSizeClasses[size])}>
                {text}
              </p>
            )}
          </div>
        );

      case 'dots':
        return (
          <div className="flex flex-col items-center gap-3">
            <div className="flex space-x-1">
              {[0, 1, 2].map((i) => (
                <div
                  key={i}
                  className={cn(
                    'bg-orange-500 rounded-full animate-pulse',
                    size === 'sm' ? 'h-1 w-1' : size === 'md' ? 'h-2 w-2' : 'h-3 w-3'
                  )}
                  style={{
                    animationDelay: `${i * 0.2}s`,
                    animationDuration: '1s'
                  }}
                />
              ))}
            </div>
            {text && (
              <p className={cn('text-gray-600 font-medium', textSizeClasses[size])}>
                {text}
              </p>
            )}
          </div>
        );

      case 'pulse':
        return (
          <div className="flex flex-col items-center gap-3">
            <div className={cn(
              'bg-orange-500 rounded-full animate-pulse',
              sizeClasses[size]
            )} />
            {text && (
              <p className={cn('text-gray-600 font-medium', textSizeClasses[size])}>
                {text}
              </p>
            )}
          </div>
        );

      default:
        return (
          <div className="flex flex-col items-center gap-3">
            <Loader2 className={cn('animate-spin text-orange-500', sizeClasses[size])} />
            {text && (
              <p className={cn('text-gray-600 font-medium', textSizeClasses[size])}>
                {text}
              </p>
            )}
          </div>
        );
    }
  };

  if (fullScreen) {
    return (
      <div className="fixed inset-0 z-50 flex items-center justify-center bg-white">
        <LoadingContent />
      </div>
    );
  }

  if (overlay) {
    return (
      <div className="absolute inset-0 z-10 flex items-center justify-center bg-white bg-opacity-90">
        <LoadingContent />
      </div>
    );
  }

  return (
    <div className={cn('flex items-center justify-center p-4', className)}>
      <LoadingContent />
    </div>
  );
};

// Progress loading for file uploads
export const ProgressLoading: React.FC<ProgressLoadingProps> = ({
  progress,
  text,
  icon,
  className
}) => {
  return (
    <div className={cn('space-y-3', className)}>
      <div className="flex items-center gap-3">
        {icon || <Upload className="h-5 w-5 text-orange-500" />}
        <div className="flex-1">
          <p className="text-sm font-medium text-gray-700">{text || 'Processing...'}</p>
          <p className="text-xs text-gray-500">{Math.round(progress)}% complete</p>
        </div>
      </div>
      
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div 
          className="bg-orange-500 h-2 rounded-full transition-all duration-300 ease-out"
          style={{ width: `${Math.min(progress, 100)}%` }}
        />
      </div>
    </div>
  );
};

// Skeleton loader for content
export const Skeleton: React.FC<SkeletonProps> = ({
  className,
  lines = 3
}) => {
  return (
    <div className={cn('space-y-3', className)}>
      {Array.from({ length: lines }).map((_, i) => (
        <div
          key={i}
          className={cn(
            'bg-gray-200 rounded animate-pulse h-4',
            i === lines - 1 ? 'w-3/4' : 'w-full'
          )}
        />
      ))}
    </div>
  );
};

// Order status loading
export const OrderStatusLoading: React.FC<{
  status: 'submitting' | 'processing' | 'success' | 'error';
  message?: string;
}> = ({ status, message }) => {
  const statusConfig = {
    submitting: {
      icon: <Loader2 className="h-6 w-6 animate-spin text-orange-500" />,
      text: message || 'Submitting your order...',
      color: 'text-orange-600'
    },
    processing: {
      icon: <ShoppingCart className="h-6 w-6 text-blue-500" />,
      text: message || 'Processing your request...',
      color: 'text-blue-600'
    },
    success: {
      icon: <CheckCircle className="h-6 w-6 text-green-500" />,
      text: message || 'Order submitted successfully!',
      color: 'text-green-600'
    },
    error: {
      icon: <div className="h-6 w-6 rounded-full bg-red-500 flex items-center justify-center text-white text-xs font-bold">!</div>,
      text: message || 'Something went wrong. Please try again.',
      color: 'text-red-600'
    }
  };

  const config = statusConfig[status];

  return (
    <div className="flex flex-col items-center gap-3 p-6">
      {config.icon}
      <p className={cn('font-medium text-center', config.color)}>
        {config.text}
      </p>
    </div>
  );
}; 