import React, { forwardRef, useState } from 'react';
import { cn } from '@/lib/utils';
import { Input } from './input';
import { Textarea } from './textarea';
import { Label } from './label';
import { AlertCircle, CheckCircle, Eye, EyeOff } from 'lucide-react';

interface FormFieldProps {
  label: string;
  name: string;
  type?: 'text' | 'email' | 'tel' | 'password' | 'number' | 'textarea';
  value: string | number;
  onChange: (value: string | number) => void;
  onBlur?: () => void;
  error?: string;
  success?: string;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  description?: string;
  rows?: number;
  min?: number;
  max?: number;
  validate?: (value: string | number) => string | null;
  className?: string;
}

export const FormField = forwardRef<HTMLInputElement | HTMLTextAreaElement, FormFieldProps>(
  ({
    label,
    name,
    type = 'text',
    value,
    onChange,
    onBlur,
    error,
    success,
    placeholder,
    required = false,
    disabled = false,
    description,
    rows = 4,
    min,
    max,
    validate,
    className
  }, ref) => {
    const [showPassword, setShowPassword] = useState(false);
    const [touched, setTouched] = useState(false);
    const [localError, setLocalError] = useState<string | null>(null);

    const handleBlur = () => {
      setTouched(true);
      
      if (validate && value) {
        const validationError = validate(value);
        setLocalError(validationError);
      }
      
      onBlur?.();
    };

    const handleChange = (newValue: string | number) => {
      onChange(newValue);
      
      // Clear local error when user starts typing
      if (localError) {
        setLocalError(null);
      }
      
      // Real-time validation for certain fields
      if (validate && touched && newValue) {
        const validationError = validate(newValue);
        setLocalError(validationError);
      }
    };

    const displayError = error || localError;
    const hasError = Boolean(displayError);
    const hasSuccess = Boolean(success && !hasError && touched && value);

    const inputProps = {
      id: name,
      name,
      value,
      onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => 
        handleChange(type === 'number' ? Number(e.target.value) : e.target.value),
      onBlur: handleBlur,
      placeholder,
      disabled,
      min,
      max,
      'aria-invalid': hasError,
      'aria-describedby': `${name}-description ${name}-error`,
      className: cn(
        'transition-all duration-200',
        hasError && 'border-red-500 focus:border-red-500 focus:ring-red-500/20',
        hasSuccess && 'border-green-500 focus:border-green-500 focus:ring-green-500/20',
        className
      )
    };

    return (
      <div className="space-y-2">
        <Label 
          htmlFor={name}
          className={cn(
            'block text-sm font-medium transition-colors',
            hasError ? 'text-red-700' : 'text-gray-700',
            required && "after:content-['*'] after:ml-0.5 after:text-red-500"
          )}
        >
          {label}
        </Label>

        <div className="relative">
          {type === 'textarea' ? (
            <Textarea
              {...inputProps}
              rows={rows}
              ref={ref as React.Ref<HTMLTextAreaElement>}
            />
          ) : (
            <Input
              {...inputProps}
              type={type === 'password' ? (showPassword ? 'text' : 'password') : type}
              ref={ref as React.Ref<HTMLInputElement>}
            />
          )}

          {/* Password visibility toggle */}
          {type === 'password' && (
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600 focus:outline-none focus:text-gray-600"
              aria-label={showPassword ? 'Hide password' : 'Show password'}
            >
              {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
            </button>
          )}

          {/* Status indicators */}
          <div className="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none">
            {hasError && type !== 'password' && (
              <AlertCircle className="h-4 w-4 text-red-500" />
            )}
            {hasSuccess && type !== 'password' && (
              <CheckCircle className="h-4 w-4 text-green-500" />
            )}
          </div>
        </div>

        {/* Description */}
        {description && !hasError && (
          <p id={`${name}-description`} className="text-xs text-gray-500">
            {description}
          </p>
        )}

        {/* Error message */}
        {displayError && (
          <div
            id={`${name}-error`}
            className="flex items-start gap-1 text-sm text-red-600 animate-in slide-in-from-top-1 duration-200"
            role="alert"
            aria-live="polite"
          >
            <AlertCircle className="h-4 w-4 mt-0.5 flex-shrink-0" />
            <span>{displayError}</span>
          </div>
        )}

        {/* Success message */}
        {success && hasSuccess && (
          <div
            className="flex items-start gap-1 text-sm text-green-600 animate-in slide-in-from-top-1 duration-200"
            role="status"
            aria-live="polite"
          >
            <CheckCircle className="h-4 w-4 mt-0.5 flex-shrink-0" />
            <span>{success}</span>
          </div>
        )}
      </div>
    );
  }
);

FormField.displayName = 'FormField'; 