'use client';
import React from 'react';

import { useState, useRef, useEffect } from 'react';
import { useCurrency } from '@/hooks/useCurrency';
import { ChevronDownIcon, GlobeAltIcon } from '@heroicons/react/24/outline';

interface CurrencySelectorProps {
  className?: string;
  showLabel?: boolean;
  compact?: boolean;
}

export default function CurrencySelector({ 
  className = '', 
  showLabel = true,
  compact = false 
}: CurrencySelectorProps) {
  const { currency, setCurrency, supportedCurrencies, location, isLoading } = useCurrency();
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleCurrencyChange = (newCurrency: string) => {
    setCurrency(newCurrency);
    setIsOpen(false);
  };

  if (isLoading) {
    return (
      <div className={`${className}`}>
        <div className="animate-pulse bg-gray-200 rounded h-8 w-24"></div>
      </div>
    );
  }

  const currentCurrency = supportedCurrencies[currency];

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {showLabel && !compact && (
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Currency
        </label>
      )}
      
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`
          ${compact ? 'px-2 py-1 text-sm' : 'px-3 py-2'}
          bg-white border border-gray-300 rounded-md shadow-sm 
          hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-[#FF5400] focus:border-[#FF5400]
          flex items-center justify-between min-w-[100px]
        `}
      >
        <div className="flex items-center">
          <GlobeAltIcon className={`${compact ? 'w-3 h-3' : 'w-4 h-4'} text-gray-400 mr-2`} />
          <span className="font-medium">
            {currentCurrency?.symbol} {currency}
          </span>
        </div>
        <ChevronDownIcon 
          className={`${compact ? 'w-3 h-3' : 'w-4 h-4'} text-gray-400 ml-1 transition-transform ${
            isOpen ? 'rotate-180' : ''
          }`} 
        />
      </button>

      {isOpen && (
        <div className="absolute z-50 mt-1 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
          <div className="py-1">
            {Object.entries(supportedCurrencies).map(([code, currencyInfo]) => (
              <button
                key={code}
                onClick={() => handleCurrencyChange(code)}
                className={`
                  w-full px-3 py-2 text-left hover:bg-gray-100 flex items-center justify-between
                  ${currency === code ? 'bg-[#FF5400]/10 text-[#FF5400]' : 'text-gray-900'}
                `}
              >
                <div className="flex items-center">
                  <span className="font-medium mr-2">{currencyInfo.symbol}</span>
                  <span className="text-sm">{code}</span>
                </div>
                <span className="text-xs text-gray-500 truncate ml-2">
                  {currencyInfo.name}
                </span>
              </button>
            ))}
          </div>
          
          {location && (
            <div className="border-t border-gray-200 px-3 py-2 bg-gray-50">
              <div className="text-xs text-gray-500">
                <GlobeAltIcon className="w-3 h-3 inline mr-1" />
                Detected location: {location.country}
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
} 