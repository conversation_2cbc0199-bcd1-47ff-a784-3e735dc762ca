'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { WebsitePortfolioItem } from '@/types/portfolio';

const WebDevelopmentPortfolio = React.memo(function WebDevelopmentPortfolio() {
  const [websitePortfolioItems, setWebsitePortfolioItems] = useState<WebsitePortfolioItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    const fetchWebsitePortfolioItems = async () => {
      try {
        setLoading(true);
        setError('');

        const response = await fetch(`/api/website-portfolio?t=${Date.now()}`, {
          cache: 'no-store',
          headers: {
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache'
          }
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch website portfolio items: ${response.statusText}`);
        }

        const data = await response.json();
        console.log('Portfolio items received:', data);
        setWebsitePortfolioItems(data);
      } catch (err) {
        console.error('Error fetching website portfolio items:', err);
        setError('Failed to load website portfolio items. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchWebsitePortfolioItems();
  }, []);

  if (loading) {
    return (
      <section className="py-24 bg-gradient-to-b from-gray-50 to-white relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-[0.03]">
          <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
            <defs>
              <pattern id="portfolio-grid" width="15" height="15" patternUnits="userSpaceOnUse">
                <path d="M 15 0 L 0 0 0 15" fill="none" stroke="#FF5400" strokeWidth="0.3"/>
                <circle cx="7.5" cy="7.5" r="0.5" fill="#FF5400" opacity="0.1"/>
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#portfolio-grid)" />
          </svg>
        </div>

        {/* Floating Elements */}
        <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-br from-purple-200/40 to-pink-200/40 rounded-full blur-2xl"></div>
        <div className="absolute bottom-20 right-10 w-40 h-40 bg-gradient-to-br from-orange-200/40 to-red-200/40 rounded-full blur-3xl"></div>

        <div className="container mx-auto px-6 relative z-10">
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-[#FF5400]/10 border border-[#FF5400]/20 mb-6">
              <i className="fas fa-code text-[#FF5400]"></i>
              <span className="text-sm font-medium text-[#FF5400]">Our Work</span>
            </div>
            
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-black text-gray-900 mb-6">
              Web Development <span className="text-[#FF5400]">Portfolio</span>
            </h2>
            
            <p className="text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Discover our latest web development projects showcasing modern design and functionality
            </p>
          </div>

          <div className="flex justify-center">
            <div className="inline-block h-8 w-8 animate-spin rounded-full border-[3px] border-solid border-[#FF5400] border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]"></div>
          </div>
          <p className="mt-4 text-sm text-gray-500 text-center">Loading portfolio items...</p>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section className="py-24 bg-gradient-to-b from-gray-50 to-white relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-[0.03]">
          <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
            <defs>
              <pattern id="portfolio-grid" width="15" height="15" patternUnits="userSpaceOnUse">
                <path d="M 15 0 L 0 0 0 15" fill="none" stroke="#FF5400" strokeWidth="0.3"/>
                <circle cx="7.5" cy="7.5" r="0.5" fill="#FF5400" opacity="0.1"/>
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#portfolio-grid)" />
          </svg>
        </div>

        {/* Floating Elements */}
        <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-br from-purple-200/40 to-pink-200/40 rounded-full blur-2xl"></div>
        <div className="absolute bottom-20 right-10 w-40 h-40 bg-gradient-to-br from-orange-200/40 to-red-200/40 rounded-full blur-3xl"></div>

        <div className="container mx-auto px-6 relative z-10">
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-[#FF5400]/10 border border-[#FF5400]/20 mb-6">
              <i className="fas fa-code text-[#FF5400]"></i>
              <span className="text-sm font-medium text-[#FF5400]">Our Work</span>
            </div>
            
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-black text-gray-900 mb-6">
              Web Development <span className="text-[#FF5400]">Portfolio</span>
            </h2>
            
            <p className="text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Discover our latest web development projects showcasing modern design and functionality
            </p>
          </div>

          <div className="max-w-md mx-auto p-6 bg-red-50 rounded-2xl border border-red-100">
            <div className="flex items-center gap-3 text-red-500 mb-2">
              <i className="fas fa-exclamation-circle text-xl"></i>
              <p className="font-medium">Error Loading Portfolio</p>
            </div>
            <p className="text-red-600">{error}</p>
          </div>
        </div>
      </section>
    );
  }

  if (websitePortfolioItems.length === 0) {
    return (
      <section className="py-24 bg-gradient-to-b from-gray-50 to-white relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-[0.03]">
          <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
            <defs>
              <pattern id="portfolio-grid" width="15" height="15" patternUnits="userSpaceOnUse">
                <path d="M 15 0 L 0 0 0 15" fill="none" stroke="#FF5400" strokeWidth="0.3"/>
                <circle cx="7.5" cy="7.5" r="0.5" fill="#FF5400" opacity="0.1"/>
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#portfolio-grid)" />
          </svg>
        </div>

        {/* Floating Elements */}
        <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-br from-purple-200/40 to-pink-200/40 rounded-full blur-2xl"></div>
        <div className="absolute bottom-20 right-10 w-40 h-40 bg-gradient-to-br from-orange-200/40 to-red-200/40 rounded-full blur-3xl"></div>

        <div className="container mx-auto px-6 relative z-10">
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-[#FF5400]/10 border border-[#FF5400]/20 mb-6">
              <i className="fas fa-code text-[#FF5400]"></i>
              <span className="text-sm font-medium text-[#FF5400]">Our Work</span>
            </div>
            
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-black text-gray-900 mb-6">
              Web Development <span className="text-[#FF5400]">Portfolio</span>
            </h2>
            
            <p className="text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Discover our latest web development projects showcasing modern design and functionality
            </p>
          </div>

          <div className="text-center text-gray-500">
            <i className="fas fa-folder-open text-4xl mb-4 text-gray-400"></i>
            <p>No portfolio items found. Check back soon for updates.</p>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-20 bg-gradient-to-b from-gray-50 to-white relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-[0.03]">
        <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
          <defs>
            <pattern id="portfolio-grid" width="15" height="15" patternUnits="userSpaceOnUse">
              <path d="M 15 0 L 0 0 0 15" fill="none" stroke="#FF5400" strokeWidth="0.3"/>
              <circle cx="7.5" cy="7.5" r="0.5" fill="#FF5400" opacity="0.1"/>
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#portfolio-grid)" />
        </svg>
      </div>

      {/* Floating Elements */}
      <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-br from-purple-200/40 to-pink-200/40 rounded-full blur-2xl"></div>
      <div className="absolute bottom-20 right-10 w-40 h-40 bg-gradient-to-br from-orange-200/40 to-red-200/40 rounded-full blur-3xl"></div>

      <div className="container mx-auto px-6 relative z-10">
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-[#FF5400]/10 border border-[#FF5400]/20 mb-6">
            <i className="fas fa-code text-[#FF5400]"></i>
            <span className="text-sm font-medium text-[#FF5400]">Our Work</span>
          </div>
          
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-black text-gray-900 mb-6">
            Web Development <span className="text-[#FF5400]">Portfolio</span>
          </h2>
          
          <p className="text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Discover our latest web development projects showcasing modern design and functionality
          </p>
        </div>

        {/* Portfolio Grid - 3 per row */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {websitePortfolioItems.map((item, index) => (
            <motion.div
              key={item.id}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="group relative bg-white rounded-3xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden border border-gray-100"
            >
              {/* Image Container */}
              <div className="relative aspect-[4/3] overflow-hidden">
                <Image
                  src={item.imageSrc || '/images/placeholder.jpg'}
                  alt={item.title}
                  fill
                  sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw"
                  className="object-cover transition-all duration-500 group-hover:scale-110"
                  loading={index < 3 ? "eager" : "lazy"}
                  quality={75}
                />

                {/* Overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <div className="absolute bottom-0 left-0 right-0 p-6">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="inline-block px-3 py-1 bg-[#FF5400] text-white text-xs font-medium rounded-full mb-2">
                          {item.category}
                        </div>
                        <h3 className="text-white text-lg font-semibold line-clamp-1">
                          {item.title}
                        </h3>
                      </div>

                      {/* External link icon */}
                      <div className="ml-4">
                        <div className="bg-white/20 backdrop-blur-sm rounded-full p-2 transform group-hover:rotate-12 transition-transform duration-300">
                          <i className="fas fa-external-link-alt w-5 h-5 text-white text-sm"></i>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Content */}
              <div className="p-6">
                <div className="flex items-center justify-between mb-3">
                  <span className="inline-block px-3 py-1 bg-[#FF5400]/10 text-[#FF5400] text-xs font-medium rounded-full">
                    {item.category}
                  </span>
                  {item.featured && (
                    <span className="inline-flex items-center gap-1 px-3 py-1 bg-yellow-100 text-yellow-800 text-xs font-medium rounded-full">
                      <i className="fas fa-star text-yellow-600 text-xs"></i>
                      Featured
                    </span>
                  )}
                </div>

                <h3 className="text-xl font-bold mb-3 text-gray-800 line-clamp-1 group-hover:text-[#FF5400] transition-colors">
                  {item.title}
                </h3>

                <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                  {item.description}
                </p>

                {/* Action Buttons */}
                <div className="flex gap-3">
                  <a
                    href={item.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex-1 bg-[#FF5400] hover:bg-[#FF5400]/90 text-white text-center py-2.5 px-4 rounded-xl font-medium transition-all duration-300 hover:scale-105 flex items-center justify-center gap-2 text-sm"
                  >
                    <i className="fas fa-external-link-alt text-xs"></i>
                    Visit Site
                  </a>
                  <Link
                    href={`/web-development?project=${item.id}`}
                    className="flex-1 border border-gray-200 hover:border-[#FF5400] hover:text-[#FF5400] text-gray-700 text-center py-2.5 px-4 rounded-xl font-medium transition-all duration-300 hover:scale-105 flex items-center justify-center gap-2 text-sm"
                  >
                    <i className="fas fa-info-circle text-xs"></i>
                    View Details
                  </Link>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* View All Button */}
        <div className="text-center">
          <Link
            href="/web-development"
            className="inline-flex items-center gap-2 px-8 py-4 bg-[#FF5400] hover:bg-[#FF5400]/90 text-white font-medium rounded-xl transition-all duration-300 hover:scale-105 group"
          >
            View All Web Projects
            <i className="fas fa-arrow-right text-sm transform group-hover:translate-x-1 transition-transform"></i>
          </Link>
        </div>
      </div>
    </section>
  );
});

export default WebDevelopmentPortfolio;
