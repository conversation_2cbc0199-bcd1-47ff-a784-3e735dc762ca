'use client';
import React from 'react';

import { useState, useEffect, useRef, FormEvent } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import SmartPrice from './SmartPrice';

interface LogoDesignBriefModalProps {
  isOpen: boolean;
  onClose: () => void;
  packageName: string;
  packagePrice: number;
  packageFeatures: string[];
  whatsappMessage: string;
}

export default function LogoDesignBriefModal({
  isOpen,
  onClose,
  packageName,
  packagePrice,
  packageFeatures,
  whatsappMessage
}: LogoDesignBriefModalProps) {
  const [formData, setFormData] = useState({
    businessName: '',
    customerName: '',
    email: '',
    phone: '',
    industry: '',
    logoType: 'wordmark',
    slogan: '',
    additionalInfo: ''
  });
  const [showSuccessPopup, setShowSuccessPopup] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const modalRef = useRef<HTMLDivElement>(null);

  // Handle escape key press
  useEffect(() => {
    if (!isOpen) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    // Handle clicks outside the modal
    const handleClickOutside = (e: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(e.target as Node)) {
        onClose();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('mousedown', handleClickOutside);

    // Prevent body scrolling
    document.body.style.overflow = 'hidden';

    // Clean up
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('mousedown', handleClickOutside);
      document.body.style.overflow = '';
    };
  }, [isOpen, onClose]);

  // If modal is not open, don't render anything
  if (!isOpen) return null;

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Get the pending order info from session storage
      const pendingOrderInfo = sessionStorage.getItem('pendingLogoOrder');
      if (!pendingOrderInfo) {
        throw new Error('No pending order found');
      }

      const { orderId } = JSON.parse(pendingOrderInfo);
      if (!orderId) {
        throw new Error('Invalid order data');
      }
        
      // Update the order with the actual customer data
      const updateResponse = await fetch(`/api/admin/logo-orders/${orderId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          customerName: formData.customerName || formData.businessName,
          email: formData.email,
          phone: formData.phone || null,
          businessName: formData.businessName,
          industry: formData.industry,
          logoType: formData.logoType,
          slogan: formData.slogan || null,
          additionalInfo: formData.additionalInfo || null,
          status: 'confirmed', // Update status to confirmed
          whatsappSent: true, // Mark that WhatsApp was sent
        }),
      });

      if (!updateResponse.ok) {
        const errorData = await updateResponse.json();
        throw new Error(errorData.error || 'Failed to update order with customer data');
      }

      const updateResult = await updateResponse.json();
      if (!updateResult.success) {
        throw new Error(updateResult.error || 'Failed to update order with customer data');
      }

      console.log('Order updated successfully with customer data');
          
      // Send email notification now that we have customer details
      const emailResponse = await fetch('/api/admin/logo-orders/send-notification', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ orderId }),
      });

      if (!emailResponse.ok) {
        console.error('Failed to send email notification');
        // Don't throw error here, continue with WhatsApp
      }

      // Clear the pending order from session storage
      sessionStorage.removeItem('pendingLogoOrder');

      // Create enhanced WhatsApp message with form data
      const enhancedMessage = `${whatsappMessage}

*Customer Information:*
Name: ${formData.customerName}
Email: ${formData.email}
${formData.phone ? `Phone: ${formData.phone}` : ''}

*Logo Design Brief:*
Business Name: ${formData.businessName}
Industry/Niche: ${formData.industry}
Logo Type: ${formData.logoType}
${formData.slogan ? `Slogan: ${formData.slogan}` : ''}
${formData.additionalInfo ? `Additional Information: ${formData.additionalInfo}` : ''}`;

      // Show success popup
      setShowSuccessPopup(true);

      // Wait for 2 seconds before redirecting to WhatsApp
      setTimeout(() => {
        // Create WhatsApp URL with the enhanced message
        const whatsappUrl = `https://wa.me/254741590670?text=${encodeURIComponent(enhancedMessage)}`;
        // Open WhatsApp in a new tab
        window.open(whatsappUrl, '_blank', 'noopener,noreferrer');
        // Close the modal
        onClose();
      }, 2000);
    } catch (error) {
      console.error('Error updating order with customer data:', error);
      alert(error instanceof Error ? error.message : 'An error occurred while submitting your order. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const logoTypes = [
    { value: 'wordmark', label: 'Wordmark (Text-based logo like Coca-Cola, Google)' },
    { value: 'lettermark', label: 'Lettermark (Initials/acronyms like IBM, HBO)' },
    { value: 'symbol', label: 'Symbol/Icon (Graphic symbol like Apple, Twitter)' },
    { value: 'combination', label: 'Combination (Text and symbol together)' },
    { value: 'emblem', label: 'Emblem (Text inside a symbol like Starbucks)' },
    { value: 'mascot', label: 'Mascot (Character-based logo)' },
    { value: 'abstract', label: 'Abstract (Geometric forms like Pepsi)' },
    { value: 'not-sure', label: 'Not sure (Designer will recommend)' }
  ];

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}
          className="fixed inset-0 z-50 bg-black/80 flex items-center justify-center p-2 sm:p-4 backdrop-blur-sm"
        >
          {showSuccessPopup ? (
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              className="bg-white rounded-xl p-6 max-w-sm w-full text-center shadow-xl"
            >
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">Order Submitted Successfully!</h3>
              <p className="text-gray-600 mb-4">Redirecting you to WhatsApp to complete your order...</p>
              <div className="animate-spin w-6 h-6 border-2 border-[#FF5400] border-t-transparent rounded-full mx-auto"></div>
            </motion.div>
          ) : (
            <motion.div
              ref={modalRef}
              initial={{ opacity: 0, scale: 0.95, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.95, y: 20 }}
              transition={{ duration: 0.4, type: "spring" }}
              className="bg-white rounded-xl sm:rounded-2xl shadow-2xl max-w-3xl w-full max-h-[98vh] sm:max-h-[95vh] overflow-y-auto border border-gray-100"
              onClick={(e) => e.stopPropagation()}
            >
              {/* Header */}
              <div className="sticky top-0 bg-white border-b border-gray-100 px-4 sm:px-6 lg:px-8 py-4 sm:py-6 rounded-t-xl sm:rounded-t-2xl">
                <div className="flex justify-between items-start sm:items-center">
                  <div className="flex-1 pr-4">
                    <h3 className="text-xl sm:text-2xl font-bold text-gray-900 mb-1">Logo Design Brief</h3>
                    <p className="text-xs sm:text-sm text-gray-500">Help us create the perfect logo for your brand</p>
                  </div>
                  <motion.button
                    whileHover={{ scale: 1.1, rotate: 90 }}
                    whileTap={{ scale: 0.9 }}
                    onClick={onClose}
                    className="p-2 hover:bg-gray-100 rounded-full transition-colors text-gray-400 hover:text-gray-600 flex-shrink-0"
                    aria-label="Close"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 sm:h-6 sm:w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </motion.button>
                </div>
              </div>

              <div className="px-4 sm:px-6 lg:px-8 py-4 sm:py-6">
                {/* Package Summary */}
                <div className="mb-6 sm:mb-8">
                  <div className="bg-gradient-to-r from-[#FF5400]/5 to-orange-50 p-4 sm:p-6 rounded-xl border border-[#FF5400]/10">
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-4 mb-4">
                      <h4 className="font-semibold text-lg sm:text-xl text-gray-900">{packageName}</h4>
                      <div className="bg-white px-3 sm:px-4 py-2 rounded-lg shadow-sm border self-start sm:self-auto">
                        <SmartPrice kesAmount={packagePrice} size="md" />
                      </div>
                    </div>
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-2 sm:gap-3">
                      {packageFeatures.map((feature, index) => (
                        <div key={index} className="flex items-start gap-2 sm:gap-3">
                          <div className="flex-shrink-0 w-4 h-4 sm:w-5 sm:h-5 bg-green-100 rounded-full flex items-center justify-center mt-0.5">
                            <svg className="w-2.5 h-2.5 sm:w-3 sm:h-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                          </div>
                          <span className="text-xs sm:text-sm text-gray-700 leading-relaxed">{feature}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="mt-4 sm:mt-6 p-3 sm:p-4 bg-blue-50 rounded-lg border border-blue-100">
                    <div className="flex gap-2 sm:gap-3">
                      <div className="flex-shrink-0">
                        <svg className="w-4 h-4 sm:w-5 sm:h-5 text-blue-600 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div>
                        <p className="text-xs sm:text-sm font-medium text-blue-900 mb-1">Quick Brief Form</p>
                        <p className="text-xs sm:text-sm text-blue-700">
                          Fill out this brief form to help our designers understand your vision and create a logo that perfectly represents your brand.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Form */}
                <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
                    {/* Customer Name */}
                    <div>
                      <label htmlFor="customerName" className="block text-sm font-semibold text-gray-900 mb-2">
                        Your Name *
                      </label>
                      <input
                        type="text"
                        id="customerName"
                        name="customerName"
                        value={formData.customerName}
                        onChange={handleChange}
                        className="w-full px-3 sm:px-4 py-3 sm:py-3.5 border border-gray-200 rounded-lg sm:rounded-xl focus:ring-2 focus:ring-[#FF5400]/20 focus:border-[#FF5400] transition-all duration-200 text-gray-900 placeholder-gray-400 bg-gray-50/50 hover:bg-white text-sm sm:text-base"
                        placeholder="Enter your full name"
                        required
                      />
                    </div>

                    {/* Email */}
                    <div>
                      <label htmlFor="email" className="block text-sm font-semibold text-gray-900 mb-2">
                        Email Address *
                      </label>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        value={formData.email}
                        onChange={handleChange}
                        className="w-full px-3 sm:px-4 py-3 sm:py-3.5 border border-gray-200 rounded-lg sm:rounded-xl focus:ring-2 focus:ring-[#FF5400]/20 focus:border-[#FF5400] transition-all duration-200 text-gray-900 placeholder-gray-400 bg-gray-50/50 hover:bg-white text-sm sm:text-base"
                        placeholder="<EMAIL>"
                        required
                      />
                    </div>

                    {/* Phone */}
                    <div>
                      <label htmlFor="phone" className="block text-sm font-semibold text-gray-900 mb-2">
                        Phone Number <span className="text-gray-400 font-normal">(Optional)</span>
                      </label>
                      <input
                        type="tel"
                        id="phone"
                        name="phone"
                        value={formData.phone}
                        onChange={handleChange}
                        className="w-full px-3 sm:px-4 py-3 sm:py-3.5 border border-gray-200 rounded-lg sm:rounded-xl focus:ring-2 focus:ring-[#FF5400]/20 focus:border-[#FF5400] transition-all duration-200 text-gray-900 placeholder-gray-400 bg-gray-50/50 hover:bg-white text-sm sm:text-base"
                        placeholder="+254 700 000 000"
                      />
                    </div>

                    {/* Business Name */}
                    <div>
                      <label htmlFor="businessName" className="block text-sm font-semibold text-gray-900 mb-2">
                        Business Name *
                      </label>
                      <input
                        type="text"
                        id="businessName"
                        name="businessName"
                        value={formData.businessName}
                        onChange={handleChange}
                        className="w-full px-3 sm:px-4 py-3 sm:py-3.5 border border-gray-200 rounded-lg sm:rounded-xl focus:ring-2 focus:ring-[#FF5400]/20 focus:border-[#FF5400] transition-all duration-200 text-gray-900 placeholder-gray-400 bg-gray-50/50 hover:bg-white text-sm sm:text-base"
                        placeholder="Enter your business or brand name"
                        required
                      />
                    </div>

                    {/* Industry */}
                    <div>
                      <label htmlFor="industry" className="block text-sm font-semibold text-gray-900 mb-2">
                        Industry/Niche *
                      </label>
                      <input
                        type="text"
                        id="industry"
                        name="industry"
                        value={formData.industry}
                        onChange={handleChange}
                        className="w-full px-3 sm:px-4 py-3 sm:py-3.5 border border-gray-200 rounded-lg sm:rounded-xl focus:ring-2 focus:ring-[#FF5400]/20 focus:border-[#FF5400] transition-all duration-200 text-gray-900 placeholder-gray-400 bg-gray-50/50 hover:bg-white text-sm sm:text-base"
                        placeholder="e.g. Restaurant, Technology, Fashion"
                        required
                      />
                    </div>

                    {/* Logo Type */}
                    <div>
                      <label htmlFor="logoType" className="block text-sm font-semibold text-gray-900 mb-2">
                        Logo Type *
                      </label>
                      <select
                        id="logoType"
                        name="logoType"
                        value={formData.logoType}
                        onChange={handleChange}
                        className="w-full px-3 sm:px-4 py-3 sm:py-3.5 border border-gray-200 rounded-lg sm:rounded-xl focus:ring-2 focus:ring-[#FF5400]/20 focus:border-[#FF5400] transition-all duration-200 text-gray-900 bg-gray-50/50 hover:bg-white text-sm sm:text-base"
                        required
                      >
                        {logoTypes.map((type) => (
                          <option key={type.value} value={type.value}>
                            {type.label}
                          </option>
                        ))}
                      </select>
                    </div>

                    {/* Slogan */}
                    <div className="md:col-span-2">
                      <label htmlFor="slogan" className="block text-sm font-semibold text-gray-900 mb-2">
                        Slogan/Tagline <span className="text-gray-400 font-normal">(Optional)</span>
                      </label>
                      <input
                        type="text"
                        id="slogan"
                        name="slogan"
                        value={formData.slogan}
                        onChange={handleChange}
                        className="w-full px-3 sm:px-4 py-3 sm:py-3.5 border border-gray-200 rounded-lg sm:rounded-xl focus:ring-2 focus:ring-[#FF5400]/20 focus:border-[#FF5400] transition-all duration-200 text-gray-900 placeholder-gray-400 bg-gray-50/50 hover:bg-white text-sm sm:text-base"
                        placeholder="Your business slogan or tagline if you have one"
                      />
                    </div>

                    {/* Additional Information */}
                    <div className="md:col-span-2">
                      <label htmlFor="additionalInfo" className="block text-sm font-semibold text-gray-900 mb-2">
                        Additional Information <span className="text-gray-400 font-normal">(Optional)</span>
                      </label>
                      <textarea
                        id="additionalInfo"
                        name="additionalInfo"
                        value={formData.additionalInfo}
                        onChange={handleChange}
                        rows={4}
                        className="w-full px-3 sm:px-4 py-3 sm:py-3.5 border border-gray-200 rounded-lg sm:rounded-xl focus:ring-2 focus:ring-[#FF5400]/20 focus:border-[#FF5400] transition-all duration-200 text-gray-900 placeholder-gray-400 bg-gray-50/50 hover:bg-white resize-none text-sm sm:text-base"
                        placeholder="Tell us about your brand personality, color preferences, style preferences, or any specific requirements..."
                      ></textarea>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex flex-col sm:flex-row justify-end gap-3 sm:gap-4 pt-4 sm:pt-6 border-t border-gray-100">
                    <motion.button
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      type="button"
                      onClick={onClose}
                      disabled={isSubmitting}
                      className="px-6 sm:px-8 py-3 sm:py-3.5 border border-gray-200 rounded-lg sm:rounded-xl text-gray-700 hover:bg-gray-50 transition-all duration-200 font-medium text-sm sm:text-base order-2 sm:order-1 disabled:opacity-50"
                    >
                      Cancel
                    </motion.button>
                    <motion.button
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      type="submit"
                      disabled={isSubmitting}
                      className="px-6 sm:px-8 py-3 sm:py-3.5 bg-gradient-to-r from-[#FF5400] to-[#E54D00] text-white rounded-lg sm:rounded-xl hover:from-[#E54D00] hover:to-[#CC4400] focus:outline-none focus:ring-2 focus:ring-[#FF5400]/20 shadow-lg hover:shadow-xl transition-all duration-200 font-semibold flex items-center justify-center gap-2 text-sm sm:text-base order-1 sm:order-2 disabled:opacity-50"
                    >
                      {isSubmitting ? (
                        <>
                          <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full"></div>
                          <span>Submitting...</span>
                        </>
                      ) : (
                        <>
                          <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.707l-3-3a1 1 0 00-1.414 1.414L10.586 9H7a1 1 0 100 2h3.586l-1.293 1.293a1 1 0 101.414 1.414l3-3a1 1 0 000-1.414z" clipRule="evenodd" />
                          </svg>
                          <span className="hidden xs:inline">Submit & Continue to WhatsApp</span>
                          <span className="xs:hidden">Submit & Continue</span>
                        </>
                      )}
                    </motion.button>
                  </div>
                </form>
              </div>
            </motion.div>
          )}
        </motion.div>
      )}
    </AnimatePresence>
  );
}
