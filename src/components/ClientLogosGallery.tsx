'use client';

import React, { useState, useEffect, useMemo } from 'react';
import Image from 'next/image';
import type { ImageItem } from '@/utils/getImages';
import ClientGallerySection from './ClientGallerySection';

interface ClientLogosGalleryProps {
  logos?: ImageItem[];
}

export default function ClientLogosGallery({ logos = [] }: ClientLogosGalleryProps) {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  // Fisher-Yates shuffle algorithm for randomizing array order
  const shuffleArray = (array: ImageItem[]) => {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  };

  // Don't render anything on the server side
  if (!isClient) {
    return null;
  }

  // Ensure logos is always an array and has required properties, then shuffle
  const safeLogos = useMemo(() => {
    const filtered = Array.isArray(logos) ? logos.filter(logo =>
      logo && typeof logo === 'object' && (logo.url || logo.src)
    ) : [];
    return shuffleArray(filtered);
  }, [logos]);

  if (!safeLogos.length) {
    return (
      <div className="text-center py-20 text-gray-500">
        No logos available at the moment
      </div>
    );
  }

  return (
    <ClientGallerySection
      items={safeLogos}
      gridCols="grid-cols-1 sm:grid-cols-2 md:grid-cols-3"
      aspectRatio="aspect-square"
      objectFit="object-contain"
      category="logos"
    />
  );
}