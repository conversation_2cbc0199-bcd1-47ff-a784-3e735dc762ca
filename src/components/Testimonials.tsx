'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { motion, AnimatePresence } from 'framer-motion';

interface Testimonial {
  id: number;
  name: string;
  location: string;
  project: string;
  testimonial: string;
  rating: number;
  company?: string;
  active: boolean;
  order: number;
  createdAt: string;
  updatedAt: string;
}

// Minimal star rating component
const StarRating = ({ rating }: { rating: number }) => {
  return (
    <div className="flex justify-center gap-0.5 mb-8">
      {[...Array(5)].map((_, i) => (
        <motion.div
          key={i}
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: i * 0.05, duration: 0.3 }}
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill={i < rating ? "#FF5400" : "#e5e7eb"}
            className="w-5 h-5"
          >
            <path fillRule="evenodd" d="M10.788 3.21c.448-1.077 1.976-1.077 2.424 0l2.082 5.007 5.404.433c1.164.093 1.636 1.545.749 2.305l-4.117 3.527 1.257 5.273c.271 1.136-.964 2.033-1.96 1.425L12 18.354 7.373 21.18c-.996.608-2.231-.29-1.96-1.425l1.257-5.273-4.117-3.527c-.887-.76-.415-2.212.749-2.305l5.404-.433 2.082-5.006z" clipRule="evenodd" />
          </svg>
        </motion.div>
      ))}
    </div>
  );
};

export default function Testimonials() {
  const [testimonials, setTestimonials] = useState<Testimonial[]>([]);
  const [activeIndex, setActiveIndex] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);
  
  // Fetch testimonials from API
  useEffect(() => {
    const fetchTestimonials = async () => {
      try {
        const response = await fetch('/api/testimonials');
        if (response.ok) {
          const data = await response.json();
          setTestimonials(data);
          setError(false);
        } else {
          console.error('Failed to fetch testimonials');
          setError(true);
        }
      } catch (error) {
        console.error('Error fetching testimonials:', error);
        setError(true);
      } finally {
        setLoading(false);
      }
    };

    fetchTestimonials();
  }, []);

  // Auto-rotate testimonials
  useEffect(() => {
    if (!isAutoPlaying || testimonials.length === 0) return;
    
    const interval = setInterval(() => {
      setActiveIndex((current) => (current + 1) % testimonials.length);
    }, 5000);
    
    return () => clearInterval(interval);
  }, [isAutoPlaying, testimonials.length]);
  
  // Show specific testimonial
  const showTestimonial = (index: number) => {
    setActiveIndex(index);
    setIsAutoPlaying(false);
    
    // Resume auto-rotation after 10 seconds of inactivity
    const timer = setTimeout(() => {
      setIsAutoPlaying(true);
    }, 10000);
    
    return () => clearTimeout(timer);
  };

  // Fallback testimonials for when database is empty or error occurs
  const fallbackTestimonials = [
    {
      id: 1,
      name: "John Mwangi",
      company: "KenyaTech Solutions",
      location: "Nairobi, Kenya",
      project: "Website Development",
      testimonial: "Mocky Digital transformed our brand identity with exceptional design work. Their team delivered beyond our expectations, providing a website that perfectly captures our vision.",
      rating: 5,
      active: true,
      order: 1,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
    {
      id: 2,
      name: "Sarah Okello",
      company: "Nairobi Retail Group",
      location: "Nairobi, Kenya", 
      project: "Digital Marketing",
      testimonial: "Working with Mocky Digital was a game-changer for our online presence. Their creative solutions and attention to detail helped us stand out in a competitive market.",
      rating: 5,
      active: true,
      order: 2,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
    {
      id: 3,
      name: "David Ochieng",
      company: "EastAfrica Tours",
      location: "Mombasa, Kenya",
      project: "E-commerce Development", 
      testimonial: "The team at Mocky Digital understands the unique needs of Kenyan businesses. They created a beautiful, functional website that's already bringing in new customers.",
      rating: 5,
      active: true,
      order: 3,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }
  ];

  // Use database testimonials if available, otherwise use fallback
  const displayTestimonials = testimonials.length > 0 ? testimonials : fallbackTestimonials;
  
  if (loading) {
    return (
      <section className="py-24 bg-white">
        <div className="container mx-auto px-4">
          <div className="flex justify-center items-center py-20">
            <div className="w-8 h-8 border-2 border-gray-200 border-t-[#FF5400] rounded-full animate-spin"></div>
          </div>
        </div>
      </section>
    );
  }

  if (displayTestimonials.length === 0) {
    return null;
  }

  const currentTestimonial = displayTestimonials[activeIndex];

  return (
    <section className="py-24 bg-white">
      <div className="container mx-auto px-4">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
            Client Testimonials
          </h2>
          <p className="text-gray-600 text-lg max-w-2xl mx-auto">
            Real feedback from our valued clients across Kenya
          </p>
        </motion.div>
        
        {/* Main Testimonial Display */}
        <div className="max-w-4xl mx-auto">
          <AnimatePresence mode="wait">
            <motion.div
              key={activeIndex}
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -30 }}
              transition={{ duration: 0.5, ease: "easeInOut" }}
              className="text-center"
            >
              {/* Star Rating */}
              <StarRating rating={currentTestimonial.rating} />
              
              {/* Quote */}
              <blockquote className="text-xl md:text-2xl text-gray-700 font-normal leading-relaxed mb-12 max-w-3xl mx-auto">
                "{currentTestimonial.testimonial}"
              </blockquote>
              
              {/* Client Info */}
              <div className="flex flex-col items-center">
                {/* Avatar */}
                <div className="w-16 h-16 rounded-full overflow-hidden mb-4 ring-2 ring-gray-100">
                  <Image 
                    src={`/images/testimonials/client-${currentTestimonial.id}.jpg`}
                    alt={currentTestimonial.name}
                    width={64}
                    height={64}
                    className="object-cover w-full h-full"
                    onError={(e) => {
                      e.currentTarget.src = '/images/placeholder.svg';
                    }}
                  />
                </div>
                
                {/* Client Details */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-1">
                    {currentTestimonial.name}
                  </h3>
                  {currentTestimonial.company && (
                    <p className="text-[#FF5400] font-medium mb-1">
                      {currentTestimonial.company}
                    </p>
                  )}
                  <p className="text-gray-500 text-sm">
                    {currentTestimonial.location}
                  </p>
                </div>
              </div>
            </motion.div>
          </AnimatePresence>
          
          {/* Navigation */}
          {displayTestimonials.length > 1 && (
            <div className="flex items-center justify-center mt-12 gap-8">
              {/* Previous Button */}
              <button
                onClick={() => showTestimonial(activeIndex > 0 ? activeIndex - 1 : displayTestimonials.length - 1)}
                className="p-2 text-gray-400 hover:text-[#FF5400] transition-colors duration-200"
                aria-label="Previous testimonial"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              </button>
              
              {/* Dots Navigation */}
              <div className="flex items-center gap-2">
                {displayTestimonials.map((_, index) => (
                  <button 
                    key={index}
                    onClick={() => showTestimonial(index)}
                    className={`w-2 h-2 rounded-full transition-all duration-300 ${
                      index === activeIndex 
                        ? 'bg-[#FF5400] w-8' 
                        : 'bg-gray-300 hover:bg-gray-400'
                    }`}
                    aria-label={`View testimonial ${index + 1}`}
                  />
                ))}
              </div>

              {/* Next Button */}
              <button
                onClick={() => showTestimonial(activeIndex < displayTestimonials.length - 1 ? activeIndex + 1 : 0)}
                className="p-2 text-gray-400 hover:text-[#FF5400] transition-colors duration-200"
                aria-label="Next testimonial"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </button>
            </div>
          )}
        </div>


      </div>
    </section>
  );
} 