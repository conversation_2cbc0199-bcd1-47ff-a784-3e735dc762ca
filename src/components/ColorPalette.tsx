'use client';
import React from 'react';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { ClipboardIcon, HeartIcon, ShareIcon } from '@heroicons/react/24/outline';
import { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid';
import toast from 'react-hot-toast';

export interface ColorPaletteProps {
  id: string;
  name: string;
  colors: string[];
  likes?: number;
  tags?: string[];
  onLike?: (id: string) => void;
  onShare?: (id: string) => void;
}

export default function ColorPalette({ 
  id, 
  name, 
  colors, 
  likes = 0, 
  tags = [],
  onLike,
  onShare 
}: ColorPaletteProps) {
  const [isLiked, setIsLiked] = useState(false);
  const [currentLikes, setCurrentLikes] = useState(likes);
  const [copiedColor, setCopiedColor] = useState<string | null>(null);

  const handleLike = () => {
    setIsLiked(!isLiked);
    setCurrentLikes(prev => isLiked ? prev - 1 : prev + 1);
    onLike?.(id);
  };

  const handleShare = async () => {
    try {
      const shareData = {
        title: `${name} - Color Palette`,
        text: `Check out this beautiful color palette: ${colors.join(', ')}`,
        url: window.location.href
      };

      if (navigator.share) {
        await navigator.share(shareData);
      } else {
        // Fallback: copy to clipboard
        await navigator.clipboard.writeText(`${shareData.title}\n${shareData.text}\n${shareData.url}`);
        toast.success('Palette details copied to clipboard!');
      }
      onShare?.(id);
    } catch (error) {
      console.error('Error sharing:', error);
      toast.error('Failed to share palette');
    }
  };

  const copyColor = async (color: string) => {
    try {
      await navigator.clipboard.writeText(color);
      setCopiedColor(color);
      toast.success(`Copied ${color} to clipboard!`);
      
      // Reset copied state after 2 seconds
      setTimeout(() => setCopiedColor(null), 2000);
    } catch (error) {
      console.error('Error copying color:', error);
      toast.error('Failed to copy color');
    }
  };

  const copyAllColors = async () => {
    try {
      const colorString = colors.join(', ');
      await navigator.clipboard.writeText(colorString);
      toast.success('All colors copied to clipboard!');
    } catch (error) {
      console.error('Error copying colors:', error);
      toast.error('Failed to copy colors');
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
      className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 group"
    >
      {/* Color Swatches */}
      <div className="flex h-32 sm:h-40">
        {colors.map((color, index) => (
          <motion.div
            key={index}
            className="flex-1 cursor-pointer relative group/color"
            style={{ backgroundColor: color }}
            onClick={() => copyColor(color)}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            {/* Color overlay with hex code */}
            <div className="absolute inset-0 bg-black bg-opacity-0 group-hover/color:bg-opacity-20 transition-all duration-200 flex items-center justify-center">
              <span className="text-white text-xs font-mono opacity-0 group-hover/color:opacity-100 transition-opacity duration-200 bg-black bg-opacity-50 px-2 py-1 rounded">
                {color}
              </span>
            </div>
            
            {/* Copy indicator */}
            {copiedColor === color && (
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                exit={{ scale: 0 }}
                className="absolute inset-0 bg-green-500 bg-opacity-20 flex items-center justify-center"
              >
                <ClipboardIcon className="w-6 h-6 text-green-600" />
              </motion.div>
            )}
          </motion.div>
        ))}
      </div>

      {/* Palette Info */}
      <div className="p-4">
        <div className="flex items-start justify-between mb-3">
          <div className="flex-1">
            <h3 className="font-semibold text-gray-900 text-lg mb-1 line-clamp-1">
              {name}
            </h3>
            <p className="text-sm text-gray-500">
              {colors.length} colors
            </p>
          </div>
          
          {/* Action buttons */}
          <div className="flex items-center space-x-2 ml-3">
            <button
              onClick={handleLike}
              className="flex items-center space-x-1 text-gray-500 hover:text-red-500 transition-colors duration-200"
            >
              {isLiked ? (
                <HeartSolidIcon className="w-5 h-5 text-red-500" />
              ) : (
                <HeartIcon className="w-5 h-5" />
              )}
              <span className="text-sm">{currentLikes}</span>
            </button>
            
            <button
              onClick={handleShare}
              className="text-gray-500 hover:text-blue-500 transition-colors duration-200"
            >
              <ShareIcon className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Tags */}
        {tags.length > 0 && (
          <div className="flex flex-wrap gap-1 mb-3">
            {tags.slice(0, 3).map((tag, index) => (
              <span
                key={index}
                className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full"
              >
                {tag}
              </span>
            ))}
            {tags.length > 3 && (
              <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                +{tags.length - 3}
              </span>
            )}
          </div>
        )}

        {/* Color codes row */}
        <div className="flex flex-wrap gap-1 text-xs font-mono text-gray-600 mb-3">
          {colors.map((color, index) => (
            <button
              key={index}
              onClick={() => copyColor(color)}
              className="hover:text-gray-900 transition-colors duration-200 bg-gray-50 px-2 py-1 rounded"
            >
              {color}
            </button>
          ))}
        </div>

        {/* Copy all button */}
        <button
          onClick={copyAllColors}
          className="w-full flex items-center justify-center space-x-2 py-2 px-4 bg-[#1A237E] text-white rounded-lg hover:bg-[#0D47A1] transition-colors duration-200 text-sm font-medium"
        >
          <ClipboardIcon className="w-4 h-4" />
          <span>Copy All Colors</span>
        </button>
      </div>
    </motion.div>
  );
}
