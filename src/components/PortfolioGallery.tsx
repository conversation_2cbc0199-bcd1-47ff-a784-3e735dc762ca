'use client';
import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';

import Image from 'next/image';
import { ImageItem } from '@/utils/getImages';
import { useSocket } from '@/hooks/useSocket';

interface Props {
  initialLogos?: ImageItem[];
  initialGraphics?: ImageItem[];
  initialFliers?: ImageItem[];
  initialWebsites?: ImageItem[];
}

const PortfolioGallery = React.memo<Props>(function PortfolioGallery({
  initialLogos = [],
  initialGraphics = [],
  initialFliers = [],
  initialWebsites = []
}: Props) {
  // Process initial data once with memoization
  const processedInitialData = useMemo(() => {
    return {
      logos: initialLogos.map(logo => ({ ...logo, category: 'logo' })),
      graphics: initialGraphics.map(graphic => ({ ...graphic, category: 'graphic' })),
      fliers: initialFliers.map(flier => ({ ...flier, category: 'flier' })),
      websites: initialWebsites.map(website => ({ ...website, category: 'website' }))
    };
  }, [initialLogos, initialGraphics, initialFliers, initialWebsites]);

  // State
  const [activeFilter, setActiveFilter] = useState('all');
  const [selectedImage, setSelectedImage] = useState<ImageItem | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [allImages, setAllImages] = useState(processedInitialData);

  // Visible counts for each category
  const [visibleCounts, setVisibleCounts] = useState({
    logos: 8,
    graphics: 8,
    fliers: 8,
    websites: 8
  });

  // Refs for intersection observer and scroll restoration
  const galleryRef = useRef<HTMLDivElement>(null);

  // Initialize socket
  const socket = useSocket();

  // Function to fetch images for a specific category if needed
  const fetchImagesIfNeeded = useCallback(async (category: 'logos' | 'graphics' | 'fliers' | 'websites', path: string) => {
    try {
      // FIXED: Check current state instead of depending on allImages to prevent infinite re-renders
      setIsLoading(true);

      // Use the images API with S3 as the source
      const response = await fetch(`/api/images?path=${path}&source=s3`, {
        headers: { 'Cache-Control': 'no-cache' }
      });
      
      if (!response.ok) {
        throw new Error(`Failed to fetch ${category}: ${response.statusText}`);
      }
      
      const data = await response.json();

      if (!Array.isArray(data)) {
        console.error(`Invalid data format for ${category}`);
        setIsLoading(false);
        return [];
      }

      // Map the data to our format and ensure type safety
      const mappedData = data.map((item: any) => {
        // Create a safe date string
        let dateStr = new Date().toISOString();
        try {
          if (item.createdAt) {
            dateStr = new Date(String(item.createdAt)).toISOString();
          }
        } catch (e) {
          // Silent error handling for date parsing
        }

        return {
          id: item.id || Math.random(),
          title: item.title || '',
          src: item.src || '',
          alt: item.alt || '',
          category: category.slice(0, -1),
          date: dateStr,
          createdAt: item.createdAt || dateStr
        };
      });

      // Sort by date if possible
      const sortedData = mappedData.sort((a, b) => {
        return new Date(b.date).getTime() - new Date(a.date).getTime();
      });

      setAllImages(prev => ({
        ...prev,
        [category]: sortedData
      }));

      setIsLoading(false);
      return sortedData;
    } catch (error) {
      console.error(`Error fetching ${category} images:`, error);
      setIsLoading(false);
      return [];
    }
  }, []); // FIXED: Empty dependency array - function doesn't need to depend on allImages

  // Prefetch all categories on initial load
  useEffect(() => {
    // Check which categories need to be fetched
    const fetchMissingData = async () => {
      setIsLoading(true);
      const fetchPromises = [];

      // FIXED: Only fetch if we don't have initial data
      if (initialLogos.length === 0) {
        fetchPromises.push(fetchImagesIfNeeded('logos', '/images/portfolio/logos'));
      }

      if (initialGraphics.length === 0) {
        fetchPromises.push(fetchImagesIfNeeded('graphics', '/images/portfolio/branding'));
      }

      if (initialFliers.length === 0) {
        fetchPromises.push(fetchImagesIfNeeded('fliers', '/images/portfolio/fliers'));
      }

      if (initialWebsites.length === 0) {
        fetchPromises.push(fetchImagesIfNeeded('websites', '/images/portfolio/websites'));
      }

      if (fetchPromises.length > 0) {
        await Promise.all(fetchPromises);
      }
      setIsLoading(false);
    };

    fetchMissingData();
    // FIXED: Removed fetchImagesIfNeeded from dependencies to prevent infinite re-renders
  }, [initialLogos.length, initialGraphics.length, initialFliers.length, initialWebsites.length]);

  // Handle real-time updates
  useEffect(() => {
    if (!socket) return;

    const handleImageUploaded = (data: { category: string; image: ImageItem }) => {
      const { category, image } = data;

      setAllImages(prev => {
        // Map the category name to our state key
        const categoryKey = category.endsWith('s') ? category : `${category}s`;

        if (categoryKey in prev) {
          return {
            ...prev,
            [categoryKey]: [image, ...prev[categoryKey as keyof typeof prev]]
          };
        }
        return prev;
      });
    };

    socket.on('imageUploaded', handleImageUploaded);

    return () => {
      socket.off('imageUploaded', handleImageUploaded);
    };
  }, [socket]);

  // Scroll to section when clicking on a filter
  const scrollToSection = useCallback((sectionId: string) => {
    const section = document.getElementById(sectionId);
    if (section) {
      section.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  }, []);

  // Handle "Load More" for a specific category
  const handleLoadMore = useCallback((category: 'logos' | 'graphics' | 'fliers' | 'websites') => {
    setVisibleCounts(prev => ({
      ...prev,
      [category]: prev[category] + 8 // Load 8 more images
    }));
  }, []);

  // Sort images by date
  const getSortedImages = useCallback((images: ImageItem[]) => {
    return [...images].sort((a, b) => {
      const dateA = a.createdAt || '';
      const dateB = b.createdAt || '';
      return new Date(dateB).getTime() - new Date(dateA).getTime();
    });
  }, []);

  // Get visible images for a category
  const getVisibleImages = useCallback((category: 'logos' | 'graphics' | 'fliers' | 'websites') => {
    const images = allImages[category];
    const sortedImages = getSortedImages(images);
    return sortedImages.slice(0, visibleCounts[category]);
  }, [allImages, visibleCounts, getSortedImages]);

  return (
    <div className="portfolio-gallery" ref={galleryRef}>
      {/* Category navigation - sticky on mobile, responsive tabs */}
      <div className="sticky top-16 z-10 bg-white/95 backdrop-blur-sm shadow-sm py-4 px-2 overflow-x-auto mb-8">
        <div className="filter-nav flex justify-start md:justify-center gap-2 min-w-max">
          <FilterButton
            active={activeFilter === 'all'}
            onClick={() => {
              setActiveFilter('all');
              galleryRef.current?.scrollIntoView({ behavior: 'smooth' });
            }}
          >
            All Projects
          </FilterButton>
          <FilterButton
            active={activeFilter === 'logos'}
            onClick={() => {
              setActiveFilter('logos');
              scrollToSection('logos-section');
            }}
          >
            Logos
          </FilterButton>
          <FilterButton
            active={activeFilter === 'graphics'}
            onClick={() => {
              setActiveFilter('graphics');
              scrollToSection('graphics-section');
            }}
          >
            Graphics
          </FilterButton>
          <FilterButton
            active={activeFilter === 'fliers'}
            onClick={() => {
              setActiveFilter('fliers');
              scrollToSection('fliers-section');
            }}
          >
            Fliers
          </FilterButton>
          <FilterButton
            active={activeFilter === 'websites'}
            onClick={() => {
              setActiveFilter('websites');
              scrollToSection('websites-section');
            }}
          >
            Websites
          </FilterButton>
        </div>
      </div>

      {/* Show all categories with their own sections */}
      <div className="space-y-20">
        {/* Logos Section */}
        <section id="logos-section" className="category-section">
          <div className="category-header mb-8">
            <h2 className="text-2xl md:text-3xl font-bold text-gray-900 text-center">Logo Design</h2>
            <p className="text-gray-600 mt-2 max-w-2xl mx-auto text-center">
              Professional logos that establish brand identity and make lasting impressions
            </p>
          </div>

          {/* Logos Gallery */}
          <div className="min-h-[200px]">
            {isLoading && allImages.logos.length === 0 ? (
              <LoadingSkeleton count={4} />
            ) : (
              <>
                <div className="gallery-grid grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {getVisibleImages('logos').map((image, index) => (
                    <GalleryItem
                      key={`logo-${image.id}`}
                      image={image}
                      onClick={() => setSelectedImage(image)}
                      priority={index < 4}
                      index={index}
                    />
                  ))}
                </div>

                {/* Load More Logos Button */}
                {allImages.logos.length > visibleCounts.logos && (
                  <div className="text-center mt-8">
                    <button
                      onClick={() => handleLoadMore('logos')}
                      className="px-6 py-3 bg-primary text-white rounded-full hover:bg-primary-dark transition-colors inline-flex items-center gap-2"
                    >
                      Load More Logos
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </button>
                  </div>
                )}
              </>
            )}
          </div>
        </section>

        {/* Graphics Section */}
        <section id="graphics-section" className="category-section">
          <div className="category-header mb-8">
            <h2 className="text-2xl md:text-3xl font-bold text-gray-900 text-center">Graphics & Branding</h2>
            <p className="text-gray-600 mt-2 max-w-2xl mx-auto text-center">
              Comprehensive branding solutions that communicate your unique identity and values
            </p>
          </div>

          {/* Graphics Gallery */}
          <div className="min-h-[200px]">
            {isLoading && allImages.graphics.length === 0 ? (
              <LoadingSkeleton count={4} />
            ) : (
              <>
                <div className="gallery-grid grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {getVisibleImages('graphics').map((image, index) => (
                    <GalleryItem
                      key={`graphic-${image.id}`}
                      image={image}
                      onClick={() => setSelectedImage(image)}
                      priority={index < 4}
                      index={index}
                    />
                  ))}
                </div>

                {/* Load More Graphics Button */}
                {allImages.graphics.length > visibleCounts.graphics && (
                  <div className="text-center mt-8">
                    <button
                      onClick={() => handleLoadMore('graphics')}
                      className="px-6 py-3 bg-primary text-white rounded-full hover:bg-primary-dark transition-colors inline-flex items-center gap-2"
                    >
                      Load More Graphics
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </button>
                  </div>
                )}
              </>
            )}
          </div>
        </section>

        {/* Fliers Section */}
        <section id="fliers-section" className="category-section">
          <div className="category-header mb-8">
            <h2 className="text-2xl md:text-3xl font-bold text-gray-900 text-center">Fliers & Marketing Materials</h2>
            <p className="text-gray-600 mt-2 max-w-2xl mx-auto text-center">
              Eye-catching marketing materials that drive engagement and promote your business
            </p>
          </div>

          {/* Fliers Gallery */}
          <div className="min-h-[200px]">
            {isLoading && allImages.fliers.length === 0 ? (
              <LoadingSkeleton count={4} />
            ) : (
              <>
                <div className="gallery-grid grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {getVisibleImages('fliers').map((image, index) => (
                    <GalleryItem
                      key={`flier-${image.id}`}
                      image={image}
                      onClick={() => setSelectedImage(image)}
                      priority={index < 4}
                      index={index}
                    />
                  ))}
                </div>

                {/* Load More Fliers Button */}
                {allImages.fliers.length > visibleCounts.fliers && (
                  <div className="text-center mt-8">
                    <button
                      onClick={() => handleLoadMore('fliers')}
                      className="px-6 py-3 bg-primary text-white rounded-full hover:bg-primary-dark transition-colors inline-flex items-center gap-2"
                    >
                      Load More Fliers
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </button>
                  </div>
                )}
              </>
            )}
          </div>
        </section>

        {/* Websites Section */}
        <section id="websites-section" className="category-section">
          <div className="category-header mb-8">
            <h2 className="text-2xl md:text-3xl font-bold text-gray-900 text-center">Website Projects</h2>
            <p className="text-gray-600 mt-2 max-w-2xl mx-auto text-center">
              Custom websites built for performance and exceptional user experience
            </p>
          </div>

          {/* Websites Gallery */}
          <div className="min-h-[200px]">
            {isLoading && allImages.websites.length === 0 ? (
              <LoadingSkeleton count={4} />
            ) : (
              <>
                <div className="gallery-grid grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {getVisibleImages('websites').map((image, index) => (
                    <GalleryItem
                      key={`website-${image.id}`}
                      image={image}
                      onClick={() => setSelectedImage(image)}
                      priority={index < 4}
                      index={index}
                    />
                  ))}
                </div>

                {/* Load More Websites Button */}
                {allImages.websites.length > visibleCounts.websites && (
                  <div className="text-center mt-8">
                    <button
                      onClick={() => handleLoadMore('websites')}
                      className="px-6 py-3 bg-primary text-white rounded-full hover:bg-primary-dark transition-colors inline-flex items-center gap-2"
                    >
                      Load More Websites
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </button>
                  </div>
                )}
              </>
            )}
          </div>
        </section>
      </div>

      {/* Image Modal */}
      {selectedImage && (
        <ImageModal
          image={selectedImage}
          onClose={() => setSelectedImage(null)}
        />
      )}

      {/* Custom animation definitions */}
      <style jsx global>{`
        @keyframes fadeIn {
          from { opacity: 0; }
          to { opacity: 1; }
        }

        @keyframes pulse-subtle {
          0% { opacity: 0.5; }
          50% { opacity: 0.7; }
          100% { opacity: 0.5; }
        }

        .animate-pulse-subtle {
          animation: pulse-subtle 2s ease-in-out infinite;
        }

        .gallery-item {
          will-change: transform, opacity;
          transform: translateZ(0);
        }

        .category-section {
          scroll-margin-top: 100px;
        }
      `}</style>
    </div>
  );
});

// Loading skeleton component
function LoadingSkeleton({ count = 4 }: { count?: number }) {
  return (
    <div className="loading-skeleton grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
      {[...Array(count)].map((_, index) => (
        <div key={index} className="aspect-square bg-gray-100 rounded-xl">
          <div className="w-full h-full bg-gradient-to-r from-gray-100 to-gray-200 animate-pulse-subtle rounded-xl"></div>
        </div>
      ))}
    </div>
  );
}

// FilterButton component with improved accessibility
function FilterButton({
  active,
  onClick,
  children
}: {
  active: boolean;
  onClick: () => void;
  children: React.ReactNode;
}) {
  return (
    <button
      onClick={onClick}
      className={`px-4 py-2 rounded-full text-sm md:text-base transition-colors duration-300 whitespace-nowrap ${
        active
          ? 'bg-primary text-white'
          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
      }`}
      aria-current={active ? 'page' : undefined}
    >
      {children}
    </button>
  );
}

// GalleryItem component with improved loading and animation
function GalleryItem({
  image,
  onClick,
  priority = false,
  index = 0
}: {
  image: ImageItem;
  onClick: () => void;
  priority?: boolean;
  index?: number;
}) {
  const [isLoaded, setIsLoaded] = useState(false);

  return (
    <div
      className="gallery-item relative aspect-square overflow-hidden rounded-xl cursor-pointer shadow-sm hover:shadow-md transition-all duration-300 transform-gpu"
      onClick={onClick}
      style={{
        animationDelay: `${Math.min(index * 50, 500)}ms`,
        animationDuration: '400ms',
        animationFillMode: 'both',
        animationName: 'fadeIn',
        backfaceVisibility: 'hidden'
      }}
    >
      {/* Background gradient for placeholder */}
      <div className="absolute inset-0 bg-gradient-to-br from-gray-100 to-gray-200 rounded-xl"></div>

      {/* Loading indicator */}
      {!isLoaded && (
        <div className="absolute inset-0 flex items-center justify-center bg-transparent">
          <div className="w-7 h-7 border-2 border-primary/30 border-t-primary rounded-full animate-spin opacity-80"></div>
        </div>
      )}

      {/* Optimized image */}
      <div className="absolute inset-0 overflow-hidden rounded-xl">
        <Image
          src={image.src}
          alt={image.alt || 'Portfolio image'}
          fill
          sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw"
          className={`object-cover transition-opacity duration-500 ${
            isLoaded ? 'opacity-100' : 'opacity-0'
          }`}
          onLoad={() => {
            setTimeout(() => setIsLoaded(true), 50);
          }}
          loading={priority ? "eager" : "lazy"}
          quality={60}
          placeholder="blur"
          blurDataURL="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+P+/HgAEhAI6dtiLOgAAAABJRU5ErkJggg=="
        />
      </div>

      {/* Caption overlay */}
      <div
        className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/30 to-transparent p-4 flex flex-col justify-end transition-opacity duration-300 opacity-70 hover:opacity-100"
      >
        <h3 className="text-white text-lg font-medium line-clamp-1">{image.alt || image.title}</h3>
        <p className="text-white/80 text-sm">{
          image.category === 'logo' ? 'Logo Design' :
          image.category === 'graphic' ? 'Graphic Design' :
          image.category === 'flier' ? 'Marketing Material' : 'Web Project'
        }</p>
      </div>

      {/* Hover overlay effect */}
      <div className="absolute inset-0 bg-black/10 opacity-0 hover:opacity-100 transition-opacity duration-300"></div>
    </div>
  );
}

// ImageModal component with modern design
function ImageModal({
  image,
  onClose
}: {
  image: ImageItem;
  onClose: () => void
}) {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isZoomed, setIsZoomed] = useState(false);
  const [imageError, setImageError] = useState(false);
  const modalContentRef = useRef<HTMLDivElement>(null);

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    document.body.style.overflow = 'hidden';

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = '';
    };
  }, [onClose]);

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const toggleZoom = () => {
    setIsZoomed(!isZoomed);
  };

  const getCategoryName = (category: string) => {
    switch (category) {
      case 'logo': return 'Logo Design';
      case 'graphic': return 'Graphic Design';
      case 'flier': return 'Marketing Material';
      case 'cards': return 'Business Cards';
      case 'letterheads': return 'Letterheads';
      case 'profiles': return 'Profile Design';
      case 'branding': return 'Branding Package';
      default: return 'Creative Design';
    }
  };

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center p-4 sm:p-6 lg:p-8"
      style={{
        background: 'radial-gradient(circle at center, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0.9) 100%)',
        backdropFilter: 'blur(8px)',
        WebkitBackdropFilter: 'blur(8px)',
        animation: 'fadeIn 300ms ease-out'
      }}
      onClick={handleBackdropClick}
    >
      <div
        ref={modalContentRef}
        className="relative w-full max-w-6xl max-h-[95vh] overflow-hidden"
        style={{
          background: 'rgba(255, 255, 255, 0.95)',
          backdropFilter: 'blur(20px)',
          WebkitBackdropFilter: 'blur(20px)',
          border: '1px solid rgba(255, 255, 255, 0.2)',
          borderRadius: '24px',
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(255, 255, 255, 0.1)',
          animation: 'fadeIn 400ms ease-out forwards',
          animationDelay: '50ms',
          transform: 'scale(0.8)',
          animationName: 'modalSlideIn'
        }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="relative px-6 py-4 border-b border-gray-100/50 bg-white/30 backdrop-blur-sm">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-2 h-2 bg-red-500 rounded-full"></div>
              <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
            </div>
            
            <div className="absolute left-1/2 transform -translate-x-1/2">
              <h3 className="text-lg font-semibold text-gray-800 truncate max-w-xs">
                {image.alt || image.title || 'Portfolio Preview'}
              </h3>
            </div>

            <div className="flex items-center space-x-2">
              {/* Zoom Toggle Button */}
              <button
                onClick={toggleZoom}
                className="p-2 rounded-full bg-white/20 hover:bg-white/30 transition-all duration-200 backdrop-blur-sm hover:scale-110"
                aria-label={isZoomed ? "Zoom out" : "Zoom in"}
              >
                <svg 
                  xmlns="http://www.w3.org/2000/svg" 
                  className="h-5 w-5 text-gray-700" 
                  fill="none" 
                  viewBox="0 0 24 24" 
                  stroke="currentColor"
                >
                  {isZoomed ? (
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM13 10h-3m-3 0h3m0 0V7m0 3v3" />
                  ) : (
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                  )}
                </svg>
              </button>

              {/* Close Button */}
              <button
                onClick={onClose}
                className="p-2 rounded-full bg-red-500/10 hover:bg-red-500/20 text-red-600 hover:text-red-700 transition-all duration-200 hover:scale-110 hover:rotate-90"
                aria-label="Close modal"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>
        </div>

        {/* Image Container */}
        <div className="relative bg-gradient-to-br from-gray-50 to-gray-100 overflow-hidden">
          <div 
            className={`relative transition-all duration-500 ease-in-out ${
              isZoomed ? 'h-[80vh] cursor-zoom-out' : 'h-[60vh] sm:h-[70vh] cursor-zoom-in'
            }`}
            onClick={toggleZoom}
          >
            {/* Loading Background */}
            {!isLoaded && !imageError && (
              <div className="absolute inset-0 bg-gradient-to-r from-gray-200 via-gray-100 to-gray-200 animate-pulse">
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent animate-shimmer"></div>
              </div>
            )}

            {/* Loading Spinner */}
            {!isLoaded && !imageError && (
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-12 h-12 border-4 border-gray-300 border-t-[#FF5400] rounded-full animate-spin"></div>
              </div>
            )}

            {/* Error State */}
            {imageError && (
              <div className="absolute inset-0 flex flex-col items-center justify-center text-gray-500">
                <svg className="w-16 h-16 mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                <p className="text-lg font-medium">Failed to load image</p>
                <p className="text-sm text-gray-400 mt-1">Please try again later</p>
              </div>
            )}

            {/* Main Image */}
            {!imageError && (
              <div
                className="relative w-full h-full"
                style={{
                  transform: isLoaded ? 'scale(1)' : 'scale(0.9)',
                  opacity: isLoaded ? 1 : 0,
                  transition: 'all 0.5s ease-out'
                }}
              >
                <Image
                  src={image.src}
                  alt={image.alt || 'Portfolio image'}
                  fill
                  className={`object-contain transition-all duration-500 ${
                    isZoomed ? 'scale-150' : 'scale-100'
                  }`}
                  sizes="90vw"
                  quality={95}
                  priority
                  onLoad={() => setIsLoaded(true)}
                  onError={() => {
                    setImageError(true);
                    setIsLoaded(true);
                  }}
                />
              </div>
            )}

            {/* Zoom Indicator */}
            {isLoaded && !imageError && (
              <div className="absolute bottom-4 right-4">
                <div
                  className="px-3 py-1.5 bg-black/20 backdrop-blur-sm rounded-full text-white text-sm font-medium"
                  style={{
                    opacity: 1,
                    transform: 'scale(1)',
                    animation: 'fadeIn 0.3s ease-out'
                  }}
                >
                  {isZoomed ? 'Click to zoom out' : 'Click to zoom in'}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="px-6 py-4 bg-white/30 backdrop-blur-sm border-t border-gray-100/50">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-br from-[#FF5400] to-[#FF7A00] rounded-full flex items-center justify-center">
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
              </div>
              <div>
                <h4 className="font-semibold text-gray-800">
                  {image.alt || image.title || 'Professional Design'}
                </h4>
                <p className="text-sm text-gray-600">
                  {getCategoryName(image.category || '')}
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <button
                className="px-4 py-2 bg-gradient-to-r from-[#FF5400] to-[#FF7A00] text-white rounded-full text-sm font-medium hover:shadow-lg transition-all duration-200 hover:scale-105"
                onClick={() => window.open('/contact', '_blank')}
              >
                Get Similar Design
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Custom Styles */}
      <style jsx global>{`
        @keyframes modalSlideIn {
          0% { 
            opacity: 0; 
            transform: scale(0.8) translateY(50px); 
          }
          100% { 
            opacity: 1; 
            transform: scale(1) translateY(0); 
          }
        }
        @keyframes shimmer {
          0% { transform: translateX(-100%); }
          100% { transform: translateX(100%); }
        }
        .animate-shimmer {
          animation: shimmer 2s infinite;
        }
      `}</style>
    </div>
  );
}

export default PortfolioGallery;