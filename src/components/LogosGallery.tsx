'use client';
import React, { useState, useEffect, useCallback } from 'react';
import Image from 'next/image';
import PageHero from '@/components/PageHero';
import dynamic from 'next/dynamic';
import { motion, AnimatePresence } from 'framer-motion';

interface ImageItem {
  id: number;
  src: string;
  alt: string;
  imageUrl?: string;
  title?: string;
}

interface LogoType {
  title: string;
  description: string;
  examples: string;
  image: string;
}

interface Props {
  logos: ImageItem[];
}

const logoTypes: LogoType[] = [
  {
    title: 'Wordmark Logos',
    description: 'Text-based logos that focus on your company name',
    examples: 'Like Coca-Cola, Google, or Disney',
    image: '/images/portfolio/logo-types/wordmark.png'
  },
  {
    title: 'Lettermark Logos',
    description: 'Initials or acronyms of your company name',
    examples: 'Like IBM, CNN, or HP',
    image: '/images/portfolio/logo-types/lettermark.png'
  },
  {
    title: 'Symbol Logos',
    description: 'Iconic designs that represent your brand',
    examples: 'Like Apple\'s apple or Twitter\'s bird',
    image: '/images/portfolio/logo-types/symbol.png'
  },
  {
    title: 'Combination Marks',
    description: 'Text and symbols combined into one logo',
    examples: 'Like Burger King or Adidas',
    image: '/images/portfolio/logo-types/combination.png'
  },
  {
    title: 'Emblem Logos',
    description: 'Text inside a symbol or icon',
    examples: 'Like Starbucks or Harvard University',
    image: '/images/portfolio/logo-types/emblem.png'
  },
  {
    title: 'Dynamic Logos',
    description: 'Logos that can change while keeping their basic identity',
    examples: 'Like Google Doodles',
    image: '/images/portfolio/logo-types/dynamic.png'
  }
];

const updateLogoPath = (path: string | undefined): string => {
  if (!path) return '/images/portfolio/logos/placeholder-logo.jpg';

  // If it's already an S3 URL, return it as is
  if (path.includes('linodeobjects.com')) return path;

  // If it already has the portfolio path, use it
  if (path.includes('/portfolio/logos/')) return path;

  // Convert old path to new path
  if (path.includes('/logos/')) {
    return path.replace('/logos/', '/portfolio/logos/');
  }

  // If it's a relative path (without leading slash), prepend the path
  if (!path.startsWith('/')) {
    return `/images/portfolio/logos/${path}`;
  }

  // Otherwise, extract the filename and use it with the new path
  return `/images/portfolio/logos/${path.split('/').pop()}`;
};

// Dynamically import the modal component with improved loading
const LogoModal = dynamic(() => import('@/components/LogoModal'), {
  loading: () => (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-primary"></div>
    </div>
  ),
  ssr: false // Modal doesn't need server rendering
});

const pricingPlans = [
  {
    name: 'Starter Package',
    price: '5,000',
    description: 'Simple, effective logo design',
    features: [
      '3 initial concepts',
      '2 revision rounds',
      'Professional files (AI, EPS, SVG)',
      'Web formats (PNG, JPG, PDF)',
      'Black & white versions',
      'Basic color variations',
      'Standard delivery (7 days)'
    ]
  },
  {
    name: 'Standard Package',
    price: '15,000',
    description: 'Professional logo with more options',
    features: [
      '4 unique concepts',
      '3 revision rounds',
      'Professional files (AI, EPS, SVG)',
      'Web formats (PNG, JPG, PDF)',
      'Color variations & palettes',
      'Basic brand guidelines',
      'Social media formats',
      'Logo usage guide',
      'Express delivery (5 days)'
    ],
    popular: true
  },
  {
    name: 'Premium Package',
    price: '30,000',
    description: 'Premium logo design & branding elements',
    features: [
      '6 unique concepts',
      'Unlimited revisions',
      'Professional files (AI, EPS, SVG)',
      'All web formats & sizes (PNG, JPG, PDF)',
      'Extended color variations',
      'Comprehensive guidelines',
      'Icon/favicon design',
      'Social media kit',
      'Business card mockup',
      'Logo animation',
      'Priority delivery (3 days)'
    ]
  }
];

const LogosGallery = React.memo<Props>(function LogosGallery({ logos }: Props) {
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [selectedLogo, setSelectedLogo] = useState<ImageItem | null>(null);
  const [allLogos, setAllLogos] = useState<ImageItem[]>([]);
  const [displayedLogos, setDisplayedLogos] = useState<ImageItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [visibleRows, setVisibleRows] = useState(3);

  // Function to detect mobile devices
  const isMobileDevice = () => {
    if (typeof window === 'undefined') return false;
    return window.innerWidth <= 768 || /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
  }; // Start with 3 rows (12 logos)

  // Add better logging to see what's coming from the API
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log("Logos data received:", logos.length);
    }
  }, [logos]);

  // Initialize logos data
  useEffect(() => {
    if (logos && logos.length > 0) {
      if (process.env.NODE_ENV === 'development') {
        console.log("Setting all logos:", logos.length);
      }
      setAllLogos(logos);

      // Show initial logos (12 - first 3 rows)
      setDisplayedLogos(logos.slice(0, 12));
      setIsLoading(false);
    }
  }, [logos]);

  // Handle "Load More" button click
  const handleLoadMore = useCallback(() => {
    setIsLoading(true);

    setTimeout(() => {
      // Calculate next batch of logos to display
      const nextRowCount = visibleRows + 1;
      const nextLogosCount = nextRowCount * 4; // 4 logos per row

      setVisibleRows(nextRowCount);
      setDisplayedLogos(allLogos.slice(0, nextLogosCount));
      setIsLoading(false);
    }, 500); // Add a small delay for loading state to be visible
  }, [allLogos, visibleRows]);

  // Handle image click to open modal
  const handleImageClick = useCallback((logo: ImageItem) => {
    setSelectedLogo(logo);
    const imageSrc = logo.src || logo.imageUrl;
    if (!imageSrc) return;

    // If it's already a full URL (including S3 URLs), use it directly
    if (imageSrc.startsWith('http')) {
      setSelectedImage(imageSrc);
      return;
    }

    // Otherwise, apply our path normalization
    setSelectedImage(updateLogoPath(imageSrc));
  }, []);

  // Navigation functions
  const handleNext = useCallback(() => {
    if (!selectedLogo) return;
    const currentIndex = allLogos.findIndex(logo => logo.id === selectedLogo.id);
    const nextIndex = (currentIndex + 1) % allLogos.length;
    const nextLogo = allLogos[nextIndex];
    setSelectedLogo(nextLogo);
    setSelectedImage(updateLogoPath(nextLogo.src || nextLogo.imageUrl));
  }, [selectedLogo, allLogos]);

  const handlePrevious = useCallback(() => {
    if (!selectedLogo) return;
    const currentIndex = allLogos.findIndex(logo => logo.id === selectedLogo.id);
    const previousIndex = currentIndex === 0 ? allLogos.length - 1 : currentIndex - 1;
    const previousLogo = allLogos[previousIndex];
    setSelectedLogo(previousLogo);
    setSelectedImage(updateLogoPath(previousLogo.src || previousLogo.imageUrl));
  }, [selectedLogo, allLogos]);

  const getCurrentIndex = useCallback(() => {
    if (!selectedLogo) return 0;
    return allLogos.findIndex(logo => logo.id === selectedLogo.id);
  }, [selectedLogo, allLogos]);

  const closeModal = useCallback(() => {
    setSelectedImage(null);
    setSelectedLogo(null);
  }, []);

  return (
    <main className="pt-24 bg-gradient-to-b from-gray-50 to-white">
      <PageHero
        title="Logo Design Portfolio"
        description="Explore our collection of unique and memorable logo designs."
      />

      {/* Logo Types Section */}
      <section className="py-12 border-b border-gray-100">
        <div className="container max-w-6xl mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4 text-gray-800">Logo Types We Specialize In</h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Choose from a variety of logo styles to perfectly represent your brand's identity
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-6">
            {logoTypes.map((type) => (
              <div key={type.title} className="bg-white p-6 rounded-lg shadow-sm hover:shadow transition-shadow">
                <h3 className="text-xl font-semibold mb-2 text-gray-800">{type.title}</h3>
                <p className="text-gray-600 mb-2">{type.description}</p>
                <p className="text-sm text-gray-500 italic">{type.examples}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Redesigned Portfolio Gallery Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-10">
            <span className="inline-block px-4 py-1.5 bg-[#FF5400] text-white text-sm font-medium rounded-full mb-3 shadow-sm">SHOWCASE</span>
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-gray-900">Our Logo Designs</h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Browse through our collection of professionally crafted logos that have helped businesses establish strong brand identities
            </p>
          </div>

          {/* Gallery Grid */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-6">
            {displayedLogos.map((logo, index) => (
              <div
                key={`logo-${logo.id || index}`}
                className={`group relative aspect-square bg-gradient-to-br from-gray-100 to-gray-200 rounded-xl overflow-hidden shadow-sm hover:shadow-lg transition-all duration-500 ${isMobileDevice() ? 'cursor-pointer' : 'cursor-default'}`}
                onClick={() => {
                  // Only open modal on mobile devices
                  if (isMobileDevice()) {
                    handleImageClick(logo);
                  }
                }}
              >
                <div className="absolute inset-0 bg-gradient-to-br from-gray-50 to-gray-200"></div>

                <Image
                  src={updateLogoPath(logo.src || logo.imageUrl)}
                  alt={logo.alt || logo.title || 'Logo Design'}
                  fill
                  sizes="(max-width: 640px) 50vw, 25vw"
                  className="object-cover w-full h-full group-hover:scale-105 transition-transform duration-500"
                  loading={index < 8 ? "eager" : "lazy"}
                  placeholder="blur"
                  blurDataURL="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+P+/HgAEhAI6dtiLOgAAAABJRU5ErkJggg=="
                />

                <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex flex-col justify-end p-4">
                  <h3 className="text-white text-lg font-medium">
                    {logo.title || logo.alt || 'Logo Design'}
                  </h3>
                </div>
              </div>
            ))}
          </div>

          {/* Load More Button */}
          {allLogos.length > displayedLogos.length && (
            <div className="text-center mt-12">
              <button
                onClick={handleLoadMore}
                disabled={isLoading}
                className="px-6 py-3 bg-primary text-white rounded-full hover:bg-primary-dark transition-colors disabled:opacity-70 disabled:cursor-not-allowed inline-flex items-center gap-2"
              >
                {isLoading ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Loading More...
                  </>
                ) : (
                  <>
                    Load More Logos
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </>
                )}
              </button>
            </div>
          )}
        </div>
      </section>

      {/* Pricing CTA Section */}
      <section className="py-20 bg-gradient-to-br from-gray-900 to-gray-800 relative overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0 overflow-hidden opacity-10">
          <div className="absolute -left-1/4 -top-1/4 w-1/2 h-1/2 bg-primary rounded-full blur-3xl"></div>
          <div className="absolute -right-1/4 -bottom-1/4 w-1/2 h-1/2 bg-primary rounded-full blur-3xl"></div>
        </div>

        <div className="container mx-auto px-4 relative">
          <div className="max-w-4xl mx-auto text-center">
            <span className="inline-block px-4 py-1.5 bg-primary/20 text-primary text-sm font-medium rounded-full mb-4">Ready to Get Started?</span>
            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-white">Transform Your Brand with a Professional Logo</h2>
            <p className="text-xl text-gray-300 mb-10 max-w-2xl mx-auto">
              Choose from our range of affordable logo design packages and take the first step towards building a memorable brand identity.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <a
                href="/catalogue"
                className="inline-flex items-center px-8 py-4 bg-primary text-white rounded-xl font-medium hover:bg-primary/90 transition-all duration-300 group shadow-lg hover:shadow-primary/20"
              >
                View Pricing Packages
                <svg className="w-5 h-5 ml-2 transform transition-transform group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                </svg>
              </a>
              <a
                href="/contact"
                className="inline-flex items-center px-8 py-4 bg-white/10 text-white rounded-xl font-medium hover:bg-white/20 transition-all duration-300"
              >
                Contact Us
              </a>
            </div>
          </div>
        </div>
      </section>

      {/* Image Modal */}
      <AnimatePresence mode="wait">
        {selectedImage && selectedLogo && (
          <LogoModal
            key="logo-modal-main"
            src={selectedImage}
            alt={selectedLogo.alt || 'Logo design'}
            title={selectedLogo.title || selectedLogo.alt || 'Logo Design'}
            onClose={closeModal}
            onNext={allLogos.length > 1 ? handleNext : undefined}
            onPrevious={allLogos.length > 1 ? handlePrevious : undefined}
            hasNext={allLogos.length > 1}
            hasPrevious={allLogos.length > 1}
            currentIndex={getCurrentIndex()}
            totalCount={allLogos.length}
          />
        )}
      </AnimatePresence>
    </main>
  );
});

export default LogosGallery;

// Add a new export for a component that doesn't include the hero section
export function LogosGalleryWithoutHero({ logos }: Props) {
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [selectedLogo, setSelectedLogo] = useState<ImageItem | null>(null);
  const [allLogos, setAllLogos] = useState<ImageItem[]>([]);
  const [displayedLogos, setDisplayedLogos] = useState<ImageItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [visibleRows, setVisibleRows] = useState(3);

  // Function to detect mobile devices
  const isMobileDevice = () => {
    if (typeof window === 'undefined') return false;
    return window.innerWidth <= 768 || /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
  };

  // Initialize logos data
  useEffect(() => {
    if (logos && logos.length > 0) {
      if (process.env.NODE_ENV === 'development') {
        console.log("Setting all logos:", logos.length);
      }
      setAllLogos(logos);

      // Show initial logos (12 - first 3 rows)
      setDisplayedLogos(logos.slice(0, 12));
      setIsLoading(false);
    }
  }, [logos]);

  // Handle "Load More" button click
  const handleLoadMore = useCallback(() => {
    setIsLoading(true);

    setTimeout(() => {
      // Calculate next batch of logos to display
      const nextRowCount = visibleRows + 1;
      const nextLogosCount = nextRowCount * 4; // 4 logos per row

      setVisibleRows(nextRowCount);
      setDisplayedLogos(allLogos.slice(0, nextLogosCount));
      setIsLoading(false);
    }, 500);
  }, [allLogos, visibleRows]);

  // Handle image click to open modal
  const handleImageClick = useCallback((logo: ImageItem) => {
    setSelectedLogo(logo);
    const imageSrc = logo.src || logo.imageUrl;
    if (!imageSrc) return;

    // If it's already a full URL (including S3 URLs), use it directly
    if (imageSrc.startsWith('http')) {
      setSelectedImage(imageSrc);
      return;
    }

    // Otherwise, apply our path normalization
    setSelectedImage(updateLogoPath(imageSrc));
  }, []);

  // Navigation functions
  const handleNext = useCallback(() => {
    if (!selectedLogo) return;
    const currentIndex = allLogos.findIndex(logo => logo.id === selectedLogo.id);
    const nextIndex = (currentIndex + 1) % allLogos.length;
    const nextLogo = allLogos[nextIndex];
    setSelectedLogo(nextLogo);
    setSelectedImage(updateLogoPath(nextLogo.src || nextLogo.imageUrl));
  }, [selectedLogo, allLogos]);

  const handlePrevious = useCallback(() => {
    if (!selectedLogo) return;
    const currentIndex = allLogos.findIndex(logo => logo.id === selectedLogo.id);
    const previousIndex = currentIndex === 0 ? allLogos.length - 1 : currentIndex - 1;
    const previousLogo = allLogos[previousIndex];
    setSelectedLogo(previousLogo);
    setSelectedImage(updateLogoPath(previousLogo.src || previousLogo.imageUrl));
  }, [selectedLogo, allLogos]);

  const getCurrentIndex = useCallback(() => {
    if (!selectedLogo) return 0;
    return allLogos.findIndex(logo => logo.id === selectedLogo.id);
  }, [selectedLogo, allLogos]);

  const closeModal = useCallback(() => {
    setSelectedImage(null);
    setSelectedLogo(null);
  }, []);

  return (
    <main className="bg-gradient-to-b from-gray-50 to-white">
      {/* Logo Types Section */}
      <section className="py-12 border-b border-gray-100">
        <div className="container max-w-6xl mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4 text-gray-800">Logo Types We Specialize In</h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Choose from a variety of logo styles to perfectly represent your brand's identity
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-6">
            {logoTypes.map((type) => (
              <div key={type.title} className="bg-white p-6 rounded-lg shadow-sm hover:shadow transition-shadow">
                <h3 className="text-xl font-semibold mb-2 text-gray-800">{type.title}</h3>
                <p className="text-gray-600 mb-2">{type.description}</p>
                <p className="text-sm text-gray-500 italic">{type.examples}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Gallery Section with Original Styling */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-10">
            <span className="inline-block px-4 py-1.5 bg-[#FF5400] text-white text-sm font-medium rounded-full mb-3 shadow-sm">SHOWCASE</span>
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-gray-900">Our Logo Designs</h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Browse through our collection of professionally crafted logos that have helped businesses establish strong brand identities
            </p>
          </div>

          {/* Gallery Grid - Using Original Styling */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-6">
            {displayedLogos.map((logo, index) => (
              <div
                key={`logo-${logo.id || index}`}
                className={`group relative aspect-square bg-gradient-to-br from-gray-100 to-gray-200 rounded-xl overflow-hidden shadow-sm hover:shadow-lg transition-all duration-500 ${isMobileDevice() ? 'cursor-pointer' : 'cursor-default'}`}
                onClick={() => {
                  // Only open modal on mobile devices
                  if (isMobileDevice()) {
                    handleImageClick(logo);
                  }
                }}
              >
                <div className="absolute inset-0 bg-gradient-to-br from-gray-50 to-gray-200"></div>

                <Image
                  src={updateLogoPath(logo.src || logo.imageUrl)}
                  alt={logo.alt || logo.title || 'Logo Design'}
                  fill
                  sizes="(max-width: 640px) 50vw, 25vw"
                  className="object-cover w-full h-full group-hover:scale-105 transition-transform duration-500"
                  loading={index < 8 ? "eager" : "lazy"}
                  placeholder="blur"
                  blurDataURL="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+P+/HgAEhAI6dtiLOgAAAABJRU5ErkJggg=="
                />

                <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex flex-col justify-end p-4">
                  <h3 className="text-white text-lg font-medium">
                    {logo.title || logo.alt || 'Logo Design'}
                  </h3>
                </div>
              </div>
            ))}
          </div>

          {/* Load More Button */}
          {allLogos.length > displayedLogos.length && (
            <div className="text-center mt-12">
              <button
                onClick={handleLoadMore}
                disabled={isLoading}
                className="px-6 py-3 bg-primary text-white rounded-full hover:bg-primary-dark transition-colors disabled:opacity-70 disabled:cursor-not-allowed inline-flex items-center gap-2"
              >
                {isLoading ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Loading More...
                  </>
                ) : (
                  <>
                    Load More Logos
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </>
                )}
              </button>
            </div>
          )}
        </div>
      </section>

      {/* Image Modal */}
      <AnimatePresence mode="wait">
        {selectedImage && selectedLogo && (
          <LogoModal
            key="logo-modal-without-hero"
            src={selectedImage}
            alt={selectedLogo.alt || 'Logo design'}
            title={selectedLogo.title || selectedLogo.alt || 'Logo Design'}
            onClose={closeModal}
            onNext={allLogos.length > 1 ? handleNext : undefined}
            onPrevious={allLogos.length > 1 ? handlePrevious : undefined}
            hasNext={allLogos.length > 1}
            hasPrevious={allLogos.length > 1}
            currentIndex={getCurrentIndex()}
            totalCount={allLogos.length}
          />
        )}
      </AnimatePresence>
    </main>
  );
}