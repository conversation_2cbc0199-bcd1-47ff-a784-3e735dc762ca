'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { ArrowTopRightOnSquareIcon } from '@heroicons/react/24/outline';
import { WebsitePortfolioItem } from '@/types/portfolio';

export default function WebDevelopmentPortfolioSection() {
  const [portfolioItems, setPortfolioItems] = useState<WebsitePortfolioItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch website portfolio items - using the same approach as the working WebDevelopmentPortfolio component
  useEffect(() => {
    async function fetchPortfolioItems() {
      try {
        setIsLoading(true);
        setError(null);
        
        // Use cache-busting approach like the working component
        const response = await fetch(`/api/website-portfolio?t=${Date.now()}`, {
          cache: 'no-store',
          headers: {
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache'
          }
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch portfolio items: ${response.statusText}`);
        }

        const data: WebsitePortfolioItem[] = await response.json();
        console.log('Home page portfolio items received:', data);

        // Shuffle the array and take only 6 items for homepage
        const shuffled = [...data].sort(() => 0.5 - Math.random());
        const randomSix = shuffled.slice(0, 6);

        setPortfolioItems(randomSix);
      } catch (err) {
        console.error('Error fetching portfolio items:', err);
        setError(`Failed to load portfolio items: ${err instanceof Error ? err.message : 'Unknown error'}`);
      } finally {
        setIsLoading(false);
      }
    }

    fetchPortfolioItems();
  }, []);

  if (isLoading) {
    return (
      <section className="py-20 bg-white">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-[#FF5400]">
              Web Development Portfolio
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Explore our latest web development projects
            </p>
          </div>

          {/* Loading skeleton */}
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {Array.from({ length: 6 }).map((_, index) => (
              <div key={index} className="bg-gray-200 rounded-xl aspect-[4/3] animate-pulse"></div>
            ))}
          </div>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section className="py-20 bg-white">
        <div className="container mx-auto px-6">
          <div className="text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-[#FF5400]">
              Web Development Portfolio
            </h2>
            <p className="text-lg text-gray-600 mb-8">
              {error}
            </p>
            <button
              onClick={() => window.location.reload()}
              className="px-6 py-3 bg-[#FF5400] text-white rounded-lg hover:bg-[#FF5400]/90 transition-colors"
            >
              Try Again
            </button>
          </div>
        </div>
      </section>
    );
  }

  if (portfolioItems.length === 0) {
    return (
      <section className="py-20 bg-white">
        <div className="container mx-auto px-6">
          <div className="text-center">
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-[#FF5400]">
              Web Development Portfolio
            </h2>
            <p className="text-lg text-gray-600">
              No portfolio items available at the moment.
            </p>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-4 text-[#FF5400]">
            Web Development Portfolio
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Discover our latest web development projects showcasing modern design and functionality
          </p>
        </div>

        {/* Portfolio Grid - 3 per row */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {portfolioItems.map((item, index) => (
            <div
              key={item.id}
              className="group relative bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100"
            >
              {/* Image Container */}
              <div className="relative aspect-[4/3] overflow-hidden bg-gray-100">
                <Image
                  src={item.imageSrc || '/images/placeholder.jpg'}
                  alt={item.title}
                  fill
                  sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw"
                  className="object-cover transition-all duration-500 group-hover:scale-110"
                  loading={index < 3 ? "eager" : "lazy"}
                  quality={75}
                />

                {/* Overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <div className="absolute bottom-0 left-0 right-0 p-6">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="inline-block px-3 py-1 bg-[#FF5400] text-white text-xs font-medium rounded-full mb-2 capitalize">
                          {item.category}
                        </div>
                        <h3 className="text-white text-lg font-semibold line-clamp-1">
                          {item.title}
                        </h3>
                      </div>

                      {/* External link icon */}
                      <div className="ml-4">
                        <div className="bg-white/20 backdrop-blur-sm rounded-full p-2">
                          <ArrowTopRightOnSquareIcon className="w-5 h-5 text-white" />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Content */}
              <div className="p-6">
                <div className="flex items-center justify-between mb-3">
                  <span className="inline-block px-3 py-1 bg-[#FF5400]/10 text-[#FF5400] text-xs font-medium rounded-full capitalize">
                    {item.category}
                  </span>
                  {item.featured && (
                    <span className="inline-block px-2 py-1 bg-yellow-100 text-yellow-800 text-xs font-medium rounded-full">
                      Featured
                    </span>
                  )}
                </div>

                <h3 className="text-xl font-bold mb-3 text-gray-800 line-clamp-1">
                  {item.title}
                </h3>

                <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                  {item.description || 'Professional web development project showcasing modern design and functionality.'}
                </p>

                {/* Action Buttons */}
                <div className="flex gap-3">
                  <a
                    href={item.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex-1 bg-[#FF5400] hover:bg-[#FF5400]/90 text-white text-center py-2 px-4 rounded-lg font-medium transition-colors text-sm"
                  >
                    Visit Site
                  </a>
                  <Link
                    href={`/web-development?project=${item.id}`}
                    className="flex-1 border border-gray-300 hover:border-[#FF5400] hover:text-[#FF5400] text-gray-700 text-center py-2 px-4 rounded-lg font-medium transition-colors text-sm"
                  >
                    View Details
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* View All Button */}
        <div className="text-center">
          <Link
            href="/web-development"
            className="inline-block px-8 py-4 bg-[#FF5400] hover:bg-[#FF5400]/90 text-white font-medium rounded-full transition-colors"
          >
            View All Web Projects
          </Link>
        </div>
      </div>
    </section>
  );
}
