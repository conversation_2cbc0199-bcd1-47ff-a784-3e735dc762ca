'use client';

import React, { useState, useEffect } from 'react';
import { usePathname } from 'next/navigation';
import ClientLayout from '@/components/ClientLayout';
import Navbar from '@/components/Navbar';
import Footer from '@/components/Footer';
import CookieConsent from '@/components/CookieConsent';
import { Toaster } from 'react-hot-toast';
import Script from 'next/script';

export default function RootLayoutWrapper({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();
  const [isLoaded, setIsLoaded] = useState(false);
  const [isAdminPath, setIsAdminPath] = useState(false);

  // Generate hreflang tags dynamically
  const baseUrl = 'https://mocky.co.ke';
  const supportedLanguages = ['en-US', 'sw-KE'];
  const pathSegments = pathname ? pathname.split('/').filter(Boolean) : [];
  const hasLangPrefix = pathSegments.length > 0 && supportedLanguages.includes(pathSegments[0]);

  // Get the path without language prefix
  const pathWithoutLang = hasLangPrefix
    ? '/' + pathSegments.slice(1).join('/')
    : pathname;

  // Get the current language from URL or default
  const currentLang = hasLangPrefix ? pathSegments[0] : 'x-default';

  // Add the hreflang structured data to help search engines
  const hreflangStructuredData = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "url": `${baseUrl}${pathname}`,
    "inLanguage": currentLang === 'x-default' ? 'en' : currentLang,
    "potentialAction": {
      "@type": "ReadAction",
      "target": supportedLanguages.map(lang => ({
        "@type": "EntryPoint",
        "urlTemplate": `${baseUrl}/${lang}${pathWithoutLang}`,
        "inLanguage": lang
      }))
    }
  };

  useEffect(() => {
    const handleError = (event: ErrorEvent) => {
      console.error('Unhandled error:', event.error);
    };

    window.addEventListener('error', handleError);
    return () => window.removeEventListener('error', handleError);
  }, []);

  // Check if we're on an admin page
  useEffect(() => {
    setIsAdminPath(pathname?.startsWith('/admin') || false);
  }, [pathname]);

  // Handle online/offline status
  useEffect(() => {
    // Check if we're in the browser
    if (typeof window === 'undefined') return;

    // Function to handle when the user comes back online
    const handleOnline = async () => {
      console.log('[Network] Browser is online');
    };

    // Function to handle when the user goes offline
    const handleOffline = () => {
      console.log('[Network] Browser is offline');
    };

    // Add event listeners
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Clean up event listeners
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  useEffect(() => {
    // Mark as fully loaded after everything is ready
    if (document.readyState === 'complete') {
      setIsLoaded(true);
    } else {
      window.addEventListener('load', () => setIsLoaded(true));

      // Fallback in case the load event doesn't fire
      setTimeout(() => setIsLoaded(true), 3000);
    }

    // Initialize AOS (Animate on Scroll) with optimized settings
    const initAOS = () => {
      try {
        if (typeof window !== 'undefined' && (window as any).AOS) {
          (window as any).AOS.init({
            once: true, // Only animate elements once
            disable: 'phone', // Disable on mobile for better performance
            duration: 600, // Shorter animation duration for better performance
            easing: 'ease-out-cubic',
            delay: 0, // No delay to prevent layout shifts
            throttleDelay: 99, // Better throttling
          });
        }
      } catch (error) {
        console.error('Error initializing AOS:', error);
      }
    };

    // Load AOS script dynamically
    const loadAOS = () => {
      if (typeof window !== 'undefined' && !(window as any).AOS) {
        const script = document.createElement('script');
        script.src = 'https://unpkg.com/aos@next/dist/aos.js';
        script.async = true;
        script.onload = initAOS;
        document.body.appendChild(script);
      } else {
        initAOS();
      }
    };

    // Delay the loading of non-critical scripts
    const timer = setTimeout(() => {
      loadAOS();
    }, 1000);

    return () => {
      clearTimeout(timer);
      window.removeEventListener('load', () => setIsLoaded(true));
    };
  }, []);

  return (
    <>
      {/* Add structured data for hreflang */}
      <Script id="hreflang-structured-data" type="application/ld+json">
        {JSON.stringify(hreflangStructuredData)}
      </Script>

      {/* Dynamically insert proper hreflang links */}
      <Script id="hreflang-tags" strategy="beforeInteractive">
        {`
          (function() {
            // Remove any existing hreflang tags
            document.querySelectorAll('link[rel="alternate"][hreflang]').forEach(el => el.remove());

            // Create and add the tags
            const createHreflangTag = (lang, url) => {
              const link = document.createElement('link');
              link.rel = 'alternate';
              link.hrefLang = lang;
              link.href = url;
              document.head.appendChild(link);
            };

            // Current page/language
            createHreflangTag('${currentLang}', '${baseUrl}${pathname}');

            // Default x-default tag
            createHreflangTag('x-default', '${baseUrl}${pathWithoutLang}');

            // Language alternatives
            ${supportedLanguages
              .filter(lang => lang !== currentLang)
              .map(lang => `createHreflangTag('${lang}', '${baseUrl}/${lang}${pathWithoutLang}');`)
              .join('\n')
            }
          })();
        `}
      </Script>

      <div id="fb-root"></div>
      {/* Only render the Navbar component on public pages (exclude admin) */}
      {!isAdminPath && <Navbar />}
      {!isLoaded && (
        <div
          id="loading-indicator"
          className="fixed top-0 left-0 w-full h-1 bg-primary-light z-[9999]"
          style={{
            animation: 'loadingProgress 2s ease-in-out',
            transformOrigin: 'left',
          }}
        />
      )}
      <ClientLayout>{children}</ClientLayout>
      {/* Only render the Footer component on public pages (exclude admin) */}
      {!isAdminPath && <Footer />}
      {/* Only show cookie consent on public pages (exclude admin) */}
      {!isAdminPath && <CookieConsent />}
      <Toaster position="top-right" />
    </>
  );
}