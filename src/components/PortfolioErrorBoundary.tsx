'use client';

import React from 'react';

interface PortfolioErrorBoundaryProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

interface PortfolioErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

export class PortfolioErrorBoundary extends React.Component<
  PortfolioErrorBoundaryProps,
  PortfolioErrorBoundaryState
> {
  constructor(props: PortfolioErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): PortfolioErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    if (process.env.NODE_ENV === 'development') {
      console.error('Portfolio Error Boundary caught an error:', error, errorInfo);
    }
  }

  render() {
    if (this.state.hasError) {
      return (
        this.props.fallback || (
          <div className="w-full py-20 text-center text-gray-500">
            <div className="max-w-md mx-auto">
              <h3 className="text-lg font-medium mb-2">Unable to load portfolio images</h3>
              <p className="text-sm text-gray-400 mb-4">
                There was an error loading the portfolio content. Please try refreshing the page.
              </p>
              <button
                onClick={() => window.location.reload()}
                className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors"
              >
                Refresh Page
              </button>
            </div>
          </div>
        )
      );
    }

    return this.props.children;
  }
}

// Simple functional wrapper for easier use
export function withPortfolioErrorBoundary<T extends object>(
  Component: React.ComponentType<T>,
  fallback?: React.ReactNode
) {
  return function WrappedComponent(props: T) {
    return (
      <PortfolioErrorBoundary fallback={fallback}>
        <Component {...props} />
      </PortfolioErrorBoundary>
    );
  };
}
