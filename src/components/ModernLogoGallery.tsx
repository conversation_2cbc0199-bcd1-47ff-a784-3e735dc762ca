'use client';
import React, { useState, useEffect, useCallback } from 'react';

import Image from 'next/image';
import { motion } from 'framer-motion';
import type { ImageItem } from '@/utils/getImages';

// Fallback image for errors
const FALLBACK_IMAGE = '/images/placeholder.jpg';

interface ModernLogoGalleryProps {
  logos: ImageItem[];
}

export default function ModernLogoGallery({ logos }: ModernLogoGalleryProps) {
  const [mounted, setMounted] = useState(false);
  const [visibleLogos, setVisibleLogos] = useState<number>(8); // Show 8 logos initially
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [showingDownload, setShowingDownload] = useState<string | null>(null);
  const [safeLogos, setSafeLogos] = useState<ImageItem[]>([]);

  // Fisher-Yates shuffle algorithm for randomizing array order
  const shuffleArray = useCallback((array: ImageItem[]) => {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  }, []);

  // Set mounted state and prepare safe logos on client-side only
  useEffect(() => {
    setMounted(true);

    // Process logos on client side only
    if (Array.isArray(logos)) {
      const filtered = logos.filter(item =>
        item && typeof item === 'object' && (item.url || item.src)
      );
      // Shuffle the logos to show different order each time
      const shuffled = shuffleArray(filtered);
      setSafeLogos(shuffled);
    }
  }, [logos, shuffleArray]);

  const handleLoadMore = useCallback(() => {
    // Only run on client side
    if (!mounted) return;

    setIsLoading(true);
    // Add a small delay for better UX
    setTimeout(() => {
      setVisibleLogos(prev => Math.min(prev + 8, safeLogos.length));
      setIsLoading(false);
    }, 500);
  }, [mounted, safeLogos.length]);

  const handleLogoClick = useCallback((logo: ImageItem) => {
    setShowingDownload(logo.id?.toString() || logo.src || '');
  }, []);

  const handleDownload = useCallback(async (logo: ImageItem) => {
    const imageUrl = logo.url || logo.src || '';
    const fileName = logo.alt || logo.title || 'logo';
    
    try {
      // Fetch the image as blob to force download without redirect
      const response = await fetch(imageUrl);
      if (!response.ok) throw new Error('Network response was not ok');
      
      const blob = await response.blob();
      
      // Create download link with blob URL
      const link = document.createElement('a');
      const url = window.URL.createObjectURL(blob);
      link.href = url;
      link.download = `${fileName}.jpg`;
      
      // Trigger download
      document.body.appendChild(link);
      link.click();
      
      // Cleanup
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Download failed:', error);
      // Fallback: try direct link method for same-origin images
      const link = document.createElement('a');
      link.href = imageUrl;
      link.download = `${fileName}.jpg`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
    
    setShowingDownload(null);
  }, []);

  // Show loading skeletons before mounting
  if (!mounted) {
    return (
      <div className="min-h-[300px] py-8">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-6 lg:gap-8">
          {Array.from({ length: 8 }).map((_, index) => (
            <div
              key={`skeleton-${index}`}
              className="relative aspect-square bg-gray-100 rounded-lg overflow-hidden animate-pulse"
            />
          ))}
        </div>
      </div>
    );
  }

  // Handle case where no logos are provided
  if (!safeLogos.length) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-600">No logo examples are available at the moment.</p>
      </div>
    );
  }

  // Get the visible subset of logos
  const displayedLogos = safeLogos.slice(0, visibleLogos);

  return (
    <>
      <div className="min-h-[300px]">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
          className="grid grid-cols-2 md:grid-cols-4 gap-4 md:gap-6 lg:gap-8"
        >
          {displayedLogos.map((logo, index) => {
            const logoId = logo.id?.toString() || logo.src || '';
            return (
              <motion.div
                key={logo.id || index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.05 }}
                className="group relative aspect-square bg-gray-100 rounded-lg overflow-hidden cursor-pointer transition-transform hover:scale-105"
                onClick={(e) => {
                  e.stopPropagation();
                  handleLogoClick(logo);
                }}
              >
                <Image
                  src={logo.url || logo.src || FALLBACK_IMAGE}
                  alt={logo.alt || 'Logo design example'}
                  fill
                  sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, 25vw"
                  className="object-cover transition-all group-hover:scale-105 duration-500"
                  loading={index < 4 ? "eager" : "lazy"}
                  onError={(e) => {
                    // Fallback for images that fail to load
                    (e.target as HTMLImageElement).src = FALLBACK_IMAGE;
                  }}
                />

                <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex flex-col justify-end p-4">
                  <h3 className="text-white text-lg font-medium">
                    {logo.alt || logo.title || 'Logo Design'}
                  </h3>
                  <p className="text-white/80 text-sm capitalize">
                    Logo Design
                  </p>
                </div>

                {/* Download Dialog */}
                {showingDownload === logoId && (
                  <div className="absolute inset-0 bg-black/30 backdrop-blur-sm flex items-center justify-center z-20 p-3 sm:p-4">
                    <div className="bg-white/5 backdrop-blur-xl border border-white/15 rounded-lg sm:rounded-2xl p-3 sm:p-6 mx-3 sm:mx-4 shadow-2xl w-[280px] sm:max-w-sm sm:w-full" style={{
                      background: 'rgba(255, 255, 255, 0.05)',
                      backdropFilter: 'blur(15px)',
                      WebkitBackdropFilter: 'blur(15px)',
                      border: '1px solid rgba(255, 255, 255, 0.15)',
                      boxShadow: '0 8px 32px 0 rgba(31, 38, 135, 0.2)'
                    }}>
                      <div className="text-center space-y-2 sm:space-y-4">
                        <div className="w-10 h-10 sm:w-16 sm:h-16 bg-[#FF5400] rounded-full flex items-center justify-center mx-auto">
                          <svg className="w-5 h-5 sm:w-8 sm:h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                          </svg>
                        </div>
                        <div>
                          <h3 className="text-sm sm:text-lg font-semibold text-white">
                            Download Logo
                          </h3>
                        </div>
                        <div className="flex flex-col sm:flex-row space-y-1.5 sm:space-y-0 sm:space-x-3">
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDownload(logo);
                            }}
                            className="flex-1 bg-[#FF5400] hover:bg-[#E04800] text-white py-1.5 sm:py-2.5 px-3 sm:px-4 rounded-md sm:rounded-lg font-medium transition-colors text-xs sm:text-base"
                          >
                            Download
                          </button>
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              setShowingDownload(null);
                            }}
                            className="flex-1 bg-white/10 hover:bg-white/20 text-white py-1.5 sm:py-2.5 px-3 sm:px-4 rounded-md sm:rounded-lg font-medium transition-colors text-xs sm:text-base"
                          >
                            Cancel
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </motion.div>
            );
          })}
        </motion.div>

        {/* Load More Button */}
        {visibleLogos < safeLogos.length && (
          <div className="mt-12 text-center">
            <motion.button
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.5 }}
              onClick={handleLoadMore}
              disabled={isLoading}
              className="px-8 py-3 bg-[#0A1929] text-white rounded-full font-medium hover:bg-[#152A3B] transition-colors disabled:opacity-75 shadow-md hover:shadow-lg"
            >
              {isLoading ? (
                <>
                  <span className="inline-block w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></span>
                  Loading...
                </>
              ) : (
                'Load More Logos'
              )}
            </motion.button>
          </div>
        )}
      </div>

      {/* Click outside to close download dialog */}
      {showingDownload && (
        <div 
          className="fixed inset-0 z-10" 
          onClick={() => setShowingDownload(null)}
        />
      )}

      {/* Custom Styles */}
      <style jsx global>{`
        @keyframes shimmer {
          0% { transform: translateX(-100%); }
          100% { transform: translateX(100%); }
        }
        .animate-shimmer {
          animation: shimmer 2s infinite;
        }
      `}</style>
    </>
  );
}
