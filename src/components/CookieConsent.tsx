'use client';

import React, { useState, useEffect } from 'react';
import Cookies from 'js-cookie';

const COOKIE_MARKETING_KEY = 'cookie-marketing-consent';
const COOKIE_CONSENT_KEY = 'cookie-consent-given';

export default function CookieConsent() {
  const [showConsent, setShowConsent] = useState(false);
  const [showPreferences, setShowPreferences] = useState(false);
  const [marketingConsent, setMarketingConsent] = useState(true);

  useEffect(() => {
    const consentGiven = Cookies.get(COOKIE_CONSENT_KEY);
    if (!consentGiven) {
      setShowConsent(true);
    }
  }, []);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const savedMarketingConsent = Cookies.get(COOKIE_MARKETING_KEY) === 'true';
      setMarketingConsent(savedMarketingConsent);
    }
  }, []);

  const acceptAll = () => {
    setMarketingConsent(true);
    savePreferences(true);
    setShowConsent(false);
  };

  const rejectAll = () => {
    setMarketingConsent(false);
    savePreferences(false);
    setShowConsent(false);
  };

  const openPreferences = () => {
    setShowPreferences(true);
  };

  const savePreferences = (marketing: boolean) => {
    const expiryDays = 365;
    
    Cookies.set(COOKIE_CONSENT_KEY, 'true', { expires: expiryDays, sameSite: 'strict' });
    Cookies.set(COOKIE_MARKETING_KEY, marketing.toString(), { expires: expiryDays, sameSite: 'strict' });
    
    setShowConsent(false);
    setShowPreferences(false);
  };

  if (!showConsent && !showPreferences) {
    return null;
  }

  return (
    <>
      {/* Cookie Consent Banner */}
      {showConsent && !showPreferences && (
        <div className="fixed bottom-0 left-0 right-0 bg-white border-t-2 border-[#FF5400] shadow-lg z-50 p-4">
          <div className="max-w-6xl mx-auto flex flex-col md:flex-row items-center justify-between gap-4">
            <div className="flex-1">
              <p className="text-sm text-gray-700">
                We use cookies to enhance your browsing experience, serve personalized content, and analyze our traffic.
                <span className="font-medium"> By clicking "Accept All", you consent to our use of cookies.</span>
              </p>
            </div>
            <div className="flex gap-2 flex-wrap">
              <button
                onClick={rejectAll}
                className="px-4 py-2 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
              >
                Reject All
              </button>
              <button
                onClick={openPreferences}
                className="px-4 py-2 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
              >
                Manage Preferences
              </button>
              <button
                onClick={acceptAll}
                className="px-4 py-2 text-sm bg-[#FF5400] text-white rounded-md hover:bg-[#e64800] transition-colors"
              >
                Accept All
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Preference Modal */}
      {showPreferences && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full max-h-[80vh] overflow-y-auto">
            <div className="p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Cookie Preferences</h3>
              <p className="text-sm text-gray-600 mb-6">
                Manage your cookie preferences. You can enable or disable different types of cookies below.
              </p>

              <div className="space-y-4">
                {/* Essential Cookies */}
                <div className="flex items-center justify-between">
                  <div>
                    <label className="font-medium text-gray-700">Essential</label>
                    <p className="text-xs text-gray-500">Required for basic site functionality</p>
                  </div>
                  <input
                    type="checkbox"
                    checked={true}
                    disabled={true}
                    className="rounded border-gray-300"
                  />
                </div>

                {/* Marketing Cookies */}
                <div className="flex items-center justify-between">
                  <div>
                    <label htmlFor="marketing" className="font-medium text-gray-700">Marketing</label>
                    <p className="text-xs text-gray-500">Used for advertising and targeting</p>
                  </div>
                  <input
                    type="checkbox"
                    id="marketing"
                    checked={marketingConsent}
                    onChange={(e) => setMarketingConsent(e.target.checked)}
                    className="rounded border-gray-300"
                  />
                </div>
              </div>

              <div className="flex gap-2 mt-6">
                <button
                  onClick={() => savePreferences(marketingConsent)}
                  className="flex-1 px-4 py-2 bg-[#FF5400] text-white rounded-md hover:bg-[#e64800] transition-colors"
                >
                  Save Preferences
                </button>
                <button
                  onClick={() => setShowPreferences(false)}
                  className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
