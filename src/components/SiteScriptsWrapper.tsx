'use client';

import { useEffect, useState } from 'react';

interface Script {
  id: string;
  content: string;
}

interface SiteScriptsWrapperProps {
  scriptType: 'head' | 'body' | 'footer';
}

export default function SiteScriptsWrapper({ scriptType }: SiteScriptsWrapperProps) {
  const [scripts, setScripts] = useState<Script[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchScripts = async () => {
      try {
        const response = await fetch(`/api/scripts?type=${scriptType}`);
        if (response.ok) {
          const data = await response.json();
          if (Array.isArray(data)) {
            setScripts(data);
          }
        }
      } catch (error) {
        console.error(`Error fetching ${scriptType} scripts:`, error);
      } finally {
        setLoading(false);
      }
    };

    fetchScripts();
  }, [scriptType]);

  // Return null if loading or no scripts
  if (loading || !scripts.length) {
    return null;
  }

  // For head scripts, render script tags
  if (scriptType === 'head') {
    return (
      <>
        {scripts.map((script) => (
          <script
            key={script.id}
            dangerouslySetInnerHTML={{ __html: script.content }}
          />
        ))}
      </>
    );
  }

  // For body and footer scripts, render divs
  return (
    <>
      {scripts.map((script) => (
        <div key={script.id} dangerouslySetInnerHTML={{ __html: script.content }} />
      ))}
    </>
  );
}
