import { useEffect, useState } from 'react';
import Image from 'next/image';
import { ImageItem } from '@/utils/getImages';

interface ImageModalProps {
  image: ImageItem;
  onClose: () => void;
}

/**
 * Full-screen image modal with zoom functionality
 * Extracted from PortfolioGallery for better modularity
 */
export default function ImageModal({ image, onClose }: ImageModalProps) {
  const [isZoomed, setIsZoomed] = useState(false);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    document.body.style.overflow = 'hidden';

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'unset';
    };
  }, [onClose]);

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const toggleZoom = () => {
    setIsZoomed(!isZoomed);
  };

  const getCategoryName = (category: string) => {
    switch (category) {
      case 'logo': return 'Logo Design';
      case 'graphic': return 'Graphic Design';
      case 'flier': return 'Flyer Design';
      case 'website': return 'Website Design';
      default: return 'Design';
    }
  };

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50 p-4"
      onClick={handleBackdropClick}
    >
      <div className="relative max-w-4xl max-h-full w-full h-full flex flex-col">
        {/* Header */}
        <div className="flex justify-between items-center mb-4 text-white">
          <div>
            <h2 className="text-xl font-bold">{image.title || 'Untitled'}</h2>
            <p className="text-gray-300">{getCategoryName(image.category || 'design')}</p>
          </div>
          <div className="flex items-center space-x-4">
            <button
              onClick={toggleZoom}
              className="p-2 hover:bg-white hover:bg-opacity-20 rounded-full transition-colors"
              aria-label={isZoomed ? 'Zoom out' : 'Zoom in'}
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                {isZoomed ? (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM13 10h-3m0 0V7m0 3v3" />
                ) : (
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                )}
              </svg>
            </button>
            <button
              onClick={onClose}
              className="p-2 hover:bg-white hover:bg-opacity-20 rounded-full transition-colors"
              aria-label="Close modal"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Image */}
        <div className="flex-1 relative overflow-hidden rounded-lg">
          <div 
            className={`relative w-full h-full transition-transform duration-300 ${
              isZoomed ? 'scale-150 cursor-zoom-out' : 'cursor-zoom-in'
            }`}
            onClick={toggleZoom}
          >
            <Image
              src={image.src}
              alt={image.alt || image.title || 'Portfolio item'}
              fill
              className="object-contain"
              sizes="100vw"
              priority
            />
          </div>
        </div>

        {/* Instructions */}
        <div className="mt-4 text-center text-gray-400 text-sm">
          <p>Click image to zoom • Press ESC or click outside to close</p>
        </div>
      </div>
    </div>
  );
} 