interface FilterButtonProps {
  active: boolean;
  onClick: () => void;
  children: React.ReactNode;
}

/**
 * Filter button for portfolio categories
 * Extracted from PortfolioGallery for better modularity
 */
export default function FilterButton({
  active,
  onClick,
  children
}: FilterButtonProps) {
  return (
    <button
      onClick={onClick}
      className={`px-3 md:px-6 py-2 md:py-3 rounded-full text-sm md:text-base font-medium transition-all duration-300 transform hover:scale-105 ${
        active
          ? 'bg-gradient-to-r from-orange-500 to-red-500 text-white shadow-lg'
          : 'bg-white text-gray-700 hover:bg-gray-50 shadow-md hover:shadow-lg'
      }`}
    >
      {children}
    </button>
  );
} 