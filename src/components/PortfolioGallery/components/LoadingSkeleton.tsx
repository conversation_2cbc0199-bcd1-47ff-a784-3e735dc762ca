interface LoadingSkeletonProps {
  count?: number;
}

/**
 * Loading skeleton for portfolio gallery items
 * Extracted from PortfolioGallery for better modularity
 */
export default function LoadingSkeleton({ count = 4 }: LoadingSkeletonProps) {
  return (
    <>
      {Array.from({ length: count }).map((_, i) => (
        <div key={i} className="animate-pulse">
          <div className="aspect-square bg-gray-200 rounded-lg mb-2"></div>
          <div className="h-4 bg-gray-200 rounded w-3/4 mb-1"></div>
          <div className="h-3 bg-gray-200 rounded w-1/2"></div>
        </div>
      ))}
    </>
  );
} 