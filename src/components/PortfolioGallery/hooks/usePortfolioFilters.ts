import { useState, useCallback } from 'react';
import { ImageItem } from '@/utils/getImages';

/**
 * Custom hook for managing portfolio filtering and UI state
 * Extracted from PortfolioGallery for better separation of concerns
 */
export function usePortfolioFilters() {
  // State
  const [activeFilter, setActiveFilter] = useState('all');
  const [selectedImage, setSelectedImage] = useState<ImageItem | null>(null);

  // Visible counts for each category
  const [visibleCounts, setVisibleCounts] = useState({
    logos: 8,
    graphics: 8,
    fliers: 8,
    websites: 8
  });

  // Scroll to section when clicking on a filter
  const scrollToSection = useCallback((sectionId: string) => {
    const section = document.getElementById(sectionId);
    if (section) {
      section.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  }, []);

  // Handle filter change
  const handleFilterChange = useCallback((filter: string) => {
    setActiveFilter(filter);
    
    // If not "all", scroll to the specific section
    if (filter !== 'all') {
      scrollToSection(`${filter}-section`);
    } else {
      // Scroll to top for "all" filter
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  }, [scrollToSection]);

  // Handle "Load More" for a specific category
  const handleLoadMore = useCallback((category: 'logos' | 'graphics' | 'fliers' | 'websites') => {
    setVisibleCounts(prev => ({
      ...prev,
      [category]: prev[category] + 8 // Load 8 more images
    }));
  }, []);

  // Handle image selection for modal
  const handleImageSelect = useCallback((image: ImageItem | null) => {
    setSelectedImage(image);
  }, []);

  // Reset filters and counts
  const resetFilters = useCallback(() => {
    setActiveFilter('all');
    setSelectedImage(null);
    setVisibleCounts({
      logos: 8,
      graphics: 8,
      fliers: 8,
      websites: 8
    });
  }, []);

  return {
    activeFilter,
    selectedImage,
    visibleCounts,
    handleFilterChange,
    handleLoadMore,
    handleImageSelect,
    resetFilters,
    scrollToSection
  };
} 