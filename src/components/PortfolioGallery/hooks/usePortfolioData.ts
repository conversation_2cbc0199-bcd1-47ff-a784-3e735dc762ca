import { useState, useEffect, useCallback, useMemo } from 'react';
import { ImageItem } from '@/utils/getImages';
import { useSocket } from '@/hooks/useSocket';

interface PortfolioData {
  logos: ImageItem[];
  graphics: ImageItem[];
  fliers: ImageItem[];
  websites: ImageItem[];
}

interface UsePortfolioDataProps {
  initialLogos?: ImageItem[];
  initialGraphics?: ImageItem[];
  initialFliers?: ImageItem[];
  initialWebsites?: ImageItem[];
}

/**
 * Custom hook for managing portfolio data and real-time updates
 * Extracted from PortfolioGallery for better separation of concerns
 */
export function usePortfolioData({
  initialLogos = [],
  initialGraphics = [],
  initialFliers = [],
  initialWebsites = []
}: UsePortfolioDataProps) {
  // Process initial data once with memoization
  const processedInitialData = useMemo(() => {
    return {
      logos: initialLogos.map(logo => ({ ...logo, category: 'logo' })),
      graphics: initialGraphics.map(graphic => ({ ...graphic, category: 'graphic' })),
      fliers: initialFliers.map(flier => ({ ...flier, category: 'flier' })),
      websites: initialWebsites.map(website => ({ ...website, category: 'website' }))
    };
  }, [initialLogos, initialGraphics, initialFliers, initialWebsites]);

  // State
  const [allImages, setAllImages] = useState<PortfolioData>(processedInitialData);
  const [isLoading, setIsLoading] = useState(false);

  // Initialize socket
  const socket = useSocket();

  // Function to fetch images for a specific category if needed
  const fetchImagesIfNeeded = useCallback(async (
    category: 'logos' | 'graphics' | 'fliers' | 'websites', 
    path: string
  ) => {
    try {
      setIsLoading(true);

      // Use the images API with S3 as the source
      const response = await fetch(`/api/images?path=${path}&source=s3`, {
        headers: { 'Cache-Control': 'no-cache' }
      });
      
      if (!response.ok) {
        throw new Error(`Failed to fetch ${category}: ${response.statusText}`);
      }
      
      const data = await response.json();

      if (!Array.isArray(data)) {
        console.error(`Invalid data format for ${category}`);
        setIsLoading(false);
        return [];
      }

      // Map the data to our format and ensure type safety
      const mappedData = data.map((item: any) => {
        // Create a safe date string
        let dateStr = new Date().toISOString();
        try {
          if (item.createdAt) {
            dateStr = new Date(String(item.createdAt)).toISOString();
          }
        } catch (e) {
          // Silent error handling for date parsing
        }

        return {
          id: item.id || Math.random(),
          title: item.title || '',
          src: item.src || '',
          alt: item.alt || '',
          category: category.slice(0, -1),
          date: dateStr,
          createdAt: item.createdAt || dateStr
        };
      });

      // Sort by date if possible
      const sortedData = mappedData.sort((a, b) => {
        return new Date(b.date).getTime() - new Date(a.date).getTime();
      });

      setAllImages(prev => ({
        ...prev,
        [category]: sortedData
      }));

      setIsLoading(false);
      return sortedData;
    } catch (error) {
      console.error(`Error fetching ${category} images:`, error);
      setIsLoading(false);
      return [];
    }
  }, []);

  // Prefetch all categories on initial load
  useEffect(() => {
    const fetchMissingData = async () => {
      setIsLoading(true);
      const fetchPromises = [];

      // Only fetch if we don't have initial data
      if (initialLogos.length === 0) {
        fetchPromises.push(fetchImagesIfNeeded('logos', '/images/portfolio/logos'));
      }

      if (initialGraphics.length === 0) {
        fetchPromises.push(fetchImagesIfNeeded('graphics', '/images/portfolio/branding'));
      }

      if (initialFliers.length === 0) {
        fetchPromises.push(fetchImagesIfNeeded('fliers', '/images/portfolio/fliers'));
      }

      if (initialWebsites.length === 0) {
        fetchPromises.push(fetchImagesIfNeeded('websites', '/images/portfolio/websites'));
      }

      if (fetchPromises.length > 0) {
        await Promise.all(fetchPromises);
      }
      setIsLoading(false);
    };

    fetchMissingData();
  }, [initialLogos.length, initialGraphics.length, initialFliers.length, initialWebsites.length, fetchImagesIfNeeded]);

  // Handle real-time updates
  useEffect(() => {
    if (!socket) return;

    const handleImageUploaded = (data: { category: string; image: ImageItem }) => {
      const { category, image } = data;

      setAllImages(prev => {
        // Map the category name to our state key
        const categoryKey = category.endsWith('s') ? category : `${category}s`;

        if (categoryKey in prev) {
          return {
            ...prev,
            [categoryKey]: [image, ...prev[categoryKey as keyof typeof prev]]
          };
        }
        return prev;
      });
    };

    socket.on('imageUploaded', handleImageUploaded);

    return () => {
      socket.off('imageUploaded', handleImageUploaded);
    };
  }, [socket]);

  // Sort images by date
  const getSortedImages = useCallback((images: ImageItem[]) => {
    return [...images].sort((a, b) => {
      const dateA = a.createdAt || '';
      const dateB = b.createdAt || '';
      return new Date(dateB).getTime() - new Date(dateA).getTime();
    });
  }, []);

  return {
    allImages,
    isLoading,
    getSortedImages,
    fetchImagesIfNeeded
  };
} 