import React, { useState, useCallback } from 'react';
import { Upload, X, FileText, Image, AlertCircle, CheckCircle } from 'lucide-react';

interface UploadedFile {
  originalName: string;
  fileName: string;
  filePath: string;
  fileSize: number;
  fileType: string;
  uploadedAt: string;
}

interface ArtworkUploadProps {
  orderNumber?: string;
  onFilesUploaded?: (files: UploadedFile[]) => void;
  maxFiles?: number;
  disabled?: boolean;
}

const ArtworkUpload: React.FC<ArtworkUploadProps> = ({
  orderNumber,
  onFilesUploaded,
  maxFiles = 5,
  disabled = false
}) => {
  const [dragActive, setDragActive] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [errors, setErrors] = useState<string[]>([]);

  // File validation
  const validateFiles = (files: FileList) => {
    const validFiles: File[] = [];
    const newErrors: string[] = [];

    const allowedTypes = [
      'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/avif',
      'application/pdf', 'image/svg+xml', 'application/postscript',
      'application/eps', 'image/vnd.adobe.photoshop'
    ];

    const maxSize = 10 * 1024 * 1024; // 10MB

    Array.from(files).forEach(file => {
      if (!allowedTypes.includes(file.type)) {
        newErrors.push(`${file.name}: Unsupported file type. Please use JPG, PNG, PDF, AI, EPS, PSD, or SVG files.`);
        return;
      }

      if (file.size > maxSize) {
        newErrors.push(`${file.name}: File too large. Maximum size is 10MB.`);
        return;
      }

      if (uploadedFiles.length + validFiles.length >= maxFiles) {
        newErrors.push(`Maximum ${maxFiles} files allowed.`);
        return;
      }

      validFiles.push(file);
    });

    setErrors(newErrors);
    return validFiles;
  };

  // Handle file upload
  const uploadFiles = async (files: File[]) => {
    if (!files.length) return;

    setUploading(true);
    setUploadProgress(0);

    try {
      const formData = new FormData();
      files.forEach(file => {
        formData.append('files', file);
      });
      
      if (orderNumber) {
        formData.append('orderNumber', orderNumber);
      } else {
        // Generate temporary order number for preview
        formData.append('orderNumber', `TEMP-${Date.now()}`);
      }

      const response = await fetch('/api/upload/artwork', {
        method: 'POST',
        body: formData
      });

      const result = await response.json();

      if (result.success) {
        const newUploadedFiles = [...uploadedFiles, ...result.uploadedFiles];
        setUploadedFiles(newUploadedFiles);
        onFilesUploaded?.(newUploadedFiles);

        if (result.errors) {
          setErrors(prev => [...prev, ...result.errors]);
        }
      } else {
        setErrors(prev => [...prev, result.error || 'Upload failed']);
      }

    } catch (error) {
      console.error('Upload error:', error);
      setErrors(prev => [...prev, 'Upload failed. Please try again.']);
    } finally {
      setUploading(false);
      setUploadProgress(0);
    }
  };

  // Handle drag events
  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (disabled) return;

    const files = e.dataTransfer.files;
    if (files?.length) {
      const validFiles = validateFiles(files);
      if (validFiles.length > 0) {
        uploadFiles(validFiles);
      }
    }
  }, [disabled, uploadedFiles.length, maxFiles]);

  const handleFileInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (disabled) return;

    const files = e.target.files;
    if (files?.length) {
      const validFiles = validateFiles(files);
      if (validFiles.length > 0) {
        uploadFiles(validFiles);
      }
    }
    e.target.value = ''; // Reset input
  };

  const removeFile = (index: number) => {
    const newFiles = uploadedFiles.filter((_, i) => i !== index);
    setUploadedFiles(newFiles);
    onFilesUploaded?.(newFiles);
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (fileType: string) => {
    if (fileType.startsWith('image/')) {
      return <Image className="w-5 h-5 text-blue-500" />;
    }
    return <FileText className="w-5 h-5 text-gray-500" />;
  };

  return (
    <div className="w-full">
      {/* Upload Area */}
      <div
        className={`
          border-2 border-dashed rounded-lg p-6 text-center transition-colors
          ${dragActive ? 'border-blue-500 bg-blue-50' : 'border-gray-300'}
          ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer hover:border-gray-400'}
        `}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
        onClick={() => !disabled && document.getElementById('file-input')?.click()}
      >
        <input
          id="file-input"
          type="file"
          multiple
          accept=".jpg,.jpeg,.png,.gif,.webp,.pdf,.svg,.ai,.eps,.psd"
          onChange={handleFileInput}
          className="hidden"
          disabled={disabled}
        />

        <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        
        {uploading ? (
          <div className="space-y-2">
            <p className="text-sm font-medium text-gray-700">Uploading files...</p>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${uploadProgress}%` }}
              />
            </div>
          </div>
        ) : (
          <div className="space-y-2">
            <p className="text-lg font-medium text-gray-700">
              Drop your artwork files here
            </p>
            <p className="text-sm text-gray-500">
              or click to browse files
            </p>
            <p className="text-xs text-gray-400">
              Supports: JPG, PNG, PDF, AI, EPS, PSD, SVG (max 10MB each, {maxFiles} files max)
            </p>
          </div>
        )}
      </div>

      {/* Error Messages */}
      {errors.length > 0 && (
        <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-start">
            <AlertCircle className="w-5 h-5 text-red-500 mt-0.5 mr-2 flex-shrink-0" />
            <div className="space-y-1">
              {errors.map((error, index) => (
                <p key={index} className="text-sm text-red-700">{error}</p>
              ))}
            </div>
            <button
              onClick={() => setErrors([])}
              className="ml-auto text-red-500 hover:text-red-700"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>
      )}

      {/* Uploaded Files List */}
      {uploadedFiles.length > 0 && (
        <div className="mt-4">
          <h4 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
            <CheckCircle className="w-4 h-4 text-green-500 mr-1" />
            Uploaded Files ({uploadedFiles.length})
          </h4>
          <div className="space-y-2">
            {uploadedFiles.map((file, index) => (
              <div
                key={index}
                className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border"
              >
                <div className="flex items-center space-x-3">
                  {getFileIcon(file.fileType)}
                  <div>
                    <p className="text-sm font-medium text-gray-900">
                      {file.originalName}
                    </p>
                    <p className="text-xs text-gray-500">
                      {formatFileSize(file.fileSize)} • {new Date(file.uploadedAt).toLocaleString()}
                    </p>
                  </div>
                </div>
                <button
                  onClick={() => removeFile(index)}
                  className="text-red-500 hover:text-red-700 p-1"
                  disabled={disabled}
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default ArtworkUpload; 