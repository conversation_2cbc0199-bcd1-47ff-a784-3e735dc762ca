'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import LogoDesignBriefModal from './LogoDesignBriefModal';
import SmartPrice from './SmartPrice';
import CurrencySelector from './CurrencySelector';
import {
  SparklesIcon,
  CheckCircleIcon,
  StarIcon,
  ArrowRightIcon,
  PaintBrushIcon,
  DocumentTextIcon,
  SwatchIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';

interface PricingPlan {
  name: string;
  price: number; // Changed to number for currency conversion
  description: string;
  features: string[];
  highlight?: boolean;
  whatsappMessage: string;
}

const pricingPlans = [
  {
    name: 'Basic Package',
    price: 5000,
    description: 'Perfect for small businesses and startups',
    features: [
      '3 Unique Concepts',
      'Professional files (AI, EPS, SVG)',
      '4 Rounds Of Revision',
      'Business Card Design',
      'Letterhead Design'
    ],
    highlight: false,
    whatsappMessage: "Hello Mocky Digital! 👋 I'm interested in the Basic Package (KSH 5,000) that includes:\n• 3 Unique Concepts\n• Professional files (AI, EPS, SVG)\n• 4 Rounds Of Revision\n• Business Card Design\n• Letterhead Design\n\nCan you help me with my logo design?"
  },
  {
    name: 'Standard Package',
    price: 15000,
    description: 'Logo design with comprehensive brand guidelines and stationery',
    features: [
      '3 Unique Concepts',
      'Professional files (AI, EPS, SVG)',
      '5 Rounds Of Revision',
      'Basic Brand Guidelines',
      'Full Stationery Kit (Business Cards, Letterhead, Envelope, Invoice)'
    ],
    highlight: true,
    whatsappMessage: "Hello Mocky Digital! 👋 I'm interested in the Standard Package (KSH 15,000) that includes:\n• 3 Unique Concepts\n• Professional files (AI, EPS, SVG)\n• 5 Rounds Of Revision\n• Basic Brand Guidelines\n• Full Stationery Kit (Business Cards, Letterhead, Envelope, Invoice)\n\nCan you help me with my professional logo design?"
  },
  {
    name: 'Premium Package',
    price: 20000,
    description: 'Complete brand identity with digital marketing assets',
    features: [
      '3 Unique Concepts',
      'Professional files (AI, EPS, SVG)',
      '6 Rounds Of Revision',
      'Comprehensive Brand Guidelines',
      'Full Stationery Kit (Business Cards, Letterhead, Envelope, Invoice)',
      'Social Media Kit (Profile Images, Cover Photos, Post Templates)',
      'Branded Mockups & Presentations'
    ],
    highlight: false,
    whatsappMessage: "Hello Mocky Digital! 👋 I'm interested in the Premium Package (KSH 20,000) that includes:\n• 3 Unique Concepts\n• Professional files (AI, EPS, SVG)\n• 6 Rounds Of Revision\n• Comprehensive Brand Guidelines\n• Full Stationery Kit (Business Cards, Letterhead, Envelope, Invoice)\n• Social Media Kit (Profile Images, Cover Photos, Post Templates)\n• Branded Mockups & Presentations\n\nCan you help me with my premium logo design?"
  }
];

export default function LogoPackagesSection() {
  const [selectedPlan, setSelectedPlan] = useState<PricingPlan | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const handleOrderClick = (plan: PricingPlan) => {
    // Set the selected plan and open the modal
    setSelectedPlan(plan);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedPlan(null);
  };

  if (!mounted) {
    return (
      <section className="relative py-20 lg:py-32 overflow-hidden">
        {/* Modern Background */}
        <div className="absolute inset-0 bg-gradient-to-br from-slate-50 via-white to-orange-50/30"></div>
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top_right,_var(--tw-gradient-stops))] from-orange-100/20 via-transparent to-transparent"></div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 bg-orange-100/80 backdrop-blur-sm text-orange-600 px-4 py-2 rounded-full text-sm font-semibold mb-6 animate-pulse">
              <div className="w-4 h-4 bg-orange-300 rounded-full"></div>
              <span>Loading Packages...</span>
            </div>
            <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-slate-900 mb-6 animate-pulse">
              <div className="bg-gray-200 rounded-lg h-12 w-96 mx-auto"></div>
            </h2>
            <div className="bg-gray-200 rounded-lg h-6 w-80 mx-auto animate-pulse"></div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-10">
            {pricingPlans.map((_, index) => (
              <div
                key={index}
                className="relative bg-white/60 backdrop-blur-sm rounded-3xl overflow-hidden shadow-xl h-[600px] animate-pulse border border-white/20"
              >
                <div className="absolute inset-0 bg-gradient-to-br from-white/40 via-white/20 to-transparent"></div>
              </div>
            ))}
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="relative py-20 lg:py-32 overflow-hidden">
      {/* Modern Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-50 via-white to-orange-50/30"></div>
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top_right,_var(--tw-gradient-stops))] from-orange-100/20 via-transparent to-transparent"></div>

      {/* Floating Elements */}
      <div className="absolute top-20 left-10 w-20 h-20 bg-gradient-to-br from-orange-200/40 to-pink-200/40 rounded-full blur-xl animate-pulse"></div>
      <div className="absolute bottom-20 right-10 w-32 h-32 bg-gradient-to-br from-blue-200/30 to-purple-200/30 rounded-full blur-xl animate-pulse delay-1000"></div>
      <div className="absolute top-1/2 left-1/4 w-16 h-16 bg-gradient-to-br from-green-200/30 to-teal-200/30 rounded-full blur-lg animate-pulse delay-500"></div>

      <div className="container mx-auto px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-20"
        >
          {/* Premium Badge */}
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="inline-flex items-center gap-2 bg-gradient-to-r from-orange-100 to-red-100 backdrop-blur-sm text-orange-600 px-6 py-3 rounded-full text-sm font-bold mb-8 border border-orange-200/50 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
          >
            <SparklesIcon className="w-5 h-5 animate-pulse" />
            <span className="bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">
              PROFESSIONAL LOGO PACKAGES
            </span>
          </motion.div>

          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7, delay: 0.3 }}
            className="text-4xl md:text-5xl lg:text-6xl font-bold text-slate-900 mb-6 leading-tight"
          >
            Choose Your Perfect
            <span className="block bg-gradient-to-r from-orange-500 to-red-500 bg-clip-text text-transparent">
              Logo Package
            </span>
          </motion.h2>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7, delay: 0.4 }}
            className="text-slate-600 max-w-3xl mx-auto text-lg md:text-xl mb-8 leading-relaxed"
          >
            Professional logo design packages tailored for every business size and budget.
            Get a unique, memorable logo that represents your brand perfectly.
          </motion.p>

          {/* Currency Selector */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.7, delay: 0.5 }}
            className="flex justify-center mb-6"
          >
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-4 shadow-lg border border-white/50">
              <CurrencySelector compact={true} showLabel={false} />
            </div>
          </motion.div>

          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.7, delay: 0.6 }}
            className="text-sm text-slate-500 mb-8 flex items-center justify-center gap-2"
          >
            <span className="text-lg">💡</span>
            Prices automatically convert to your local currency based on your location
          </motion.p>

          <motion.div
            initial={{ width: 0 }}
            animate={{ width: 96 }}
            transition={{ duration: 0.8, delay: 0.7 }}
            className="h-1 bg-gradient-to-r from-orange-400 to-red-400 mx-auto rounded-full shadow-lg"
          ></motion.div>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-10">
          {pricingPlans.map((plan, index) => {
            // Get package icon based on name
            const getPackageIcon = (name: string) => {
              if (name.toLowerCase().includes('basic')) {
                return <PaintBrushIcon className="w-8 h-8" />;
              } else if (name.toLowerCase().includes('standard')) {
                return <SwatchIcon className="w-8 h-8" />;
              } else {
                return <DocumentTextIcon className="w-8 h-8" />;
              }
            };

            return (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className={`group relative bg-white/80 backdrop-blur-lg rounded-3xl overflow-hidden shadow-xl hover:shadow-2xl transition-all duration-700 border border-white/20 ${
                  plan.highlight ? 'ring-2 ring-orange-400 ring-offset-2 ring-offset-transparent' : ''
                } transform hover:-translate-y-3 hover:scale-[1.02] flex flex-col h-full`}
              >
                {/* Glass overlay effect */}
                <div className="absolute inset-0 bg-gradient-to-br from-white/30 via-white/10 to-transparent pointer-events-none" />

                {/* Popular Badge */}
                {plan.highlight && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.5, delay: 0.3 }}
                    className="absolute -top-4 -right-4 z-20"
                  >
                    <div className="bg-gradient-to-r from-orange-500 to-red-500 text-white px-6 py-3 rounded-full shadow-lg transform rotate-12 group-hover:rotate-6 transition-transform duration-300">
                      <div className="flex items-center gap-2 text-sm font-bold">
                        <StarIconSolid className="w-4 h-4" />
                        <span>Most Popular</span>
                      </div>
                    </div>
                  </motion.div>
                )}

                <div className="relative z-10 p-8 lg:p-10 flex flex-col flex-grow">
                  {/* Package Icon & Header */}
                  <div className="flex items-center gap-4 mb-6">
                    <div className="flex-shrink-0 w-16 h-16 bg-gradient-to-br from-orange-100 to-red-100 rounded-2xl flex items-center justify-center text-orange-600 group-hover:scale-110 transition-transform duration-300">
                      {getPackageIcon(plan.name)}
                    </div>
                    <div className="flex-grow">
                      <h3 className="text-2xl font-bold text-slate-900 mb-1 group-hover:text-orange-600 transition-colors duration-300">
                        {plan.name}
                      </h3>
                      <p className="text-slate-600 text-sm leading-relaxed">
                        {plan.description}
                      </p>
                    </div>
                  </div>

                  {/* Price Section */}
                  <div className="mb-8">
                    <div className="bg-gradient-to-r from-orange-50 to-red-50 rounded-2xl p-6 border border-orange-100/50">
                      <SmartPrice
                        key={`price-${plan.name}`}
                        kesAmount={plan.price}
                        size="xl"
                        showOriginal={true}
                        className="text-center"
                      />
                    </div>
                  </div>

                  {/* Features Section */}
                  <div className="flex-grow">
                    <div className="flex items-center gap-2 mb-4">
                      <CheckCircleIcon className="w-5 h-5 text-green-500" />
                      <h4 className="text-lg font-semibold text-slate-900">What's included:</h4>
                    </div>

                    <div className="space-y-3 max-h-[240px] overflow-y-auto pr-2 custom-scrollbar">
                      {plan.features.map((feature, idx) => (
                        <motion.div
                          key={idx}
                          initial={{ opacity: 0, x: -10 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ duration: 0.4, delay: 0.1 * idx }}
                          className="flex items-start gap-3 group/item"
                        >
                          <div className="flex-shrink-0 w-6 h-6 bg-gradient-to-br from-green-100 to-emerald-100 rounded-full flex items-center justify-center mt-0.5 group-hover/item:scale-110 transition-transform duration-200">
                            <CheckCircleIcon className="w-4 h-4 text-green-600" />
                          </div>
                          <span className="text-slate-700 text-sm leading-relaxed group-hover/item:text-slate-900 transition-colors duration-200">
                            {feature}
                          </span>
                        </motion.div>
                      ))}
                    </div>
                  </div>

                  {/* CTA Button */}
                  <div className="mt-8 pt-6 border-t border-slate-100">
                    <motion.button
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                      onClick={() => handleOrderClick(plan)}
                      className="w-full bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white font-bold py-4 px-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 group/button"
                      aria-label={`Order ${plan.name || plan.description} package now`}
                    >
                      <span className="flex items-center justify-center gap-3">
                        <span className="text-lg">Get Started</span>
                        <ArrowRightIcon className="w-5 h-5 group-hover/button:translate-x-1 transition-transform duration-300" />
                      </span>
                    </motion.button>
                  </div>
                </div>
              </motion.div>
            );
          })}
        </div>
      </div>

      {/* Custom Styles */}
      <style jsx>{`
        .custom-scrollbar::-webkit-scrollbar {
          width: 4px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
          background: #f1f5f9;
          border-radius: 2px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
          background: linear-gradient(to bottom, #f97316, #dc2626);
          border-radius: 2px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
          background: linear-gradient(to bottom, #ea580c, #b91c1c);
        }
      `}</style>

      {/* Logo Design Brief Modal */}
      {selectedPlan && (
        <LogoDesignBriefModal
          isOpen={isModalOpen}
          onClose={handleCloseModal}
          packageName={selectedPlan.name}
          packagePrice={selectedPlan.price}
          packageFeatures={selectedPlan.features}
          whatsappMessage={selectedPlan.whatsappMessage}
        />
      )}
    </section>
  );
}
