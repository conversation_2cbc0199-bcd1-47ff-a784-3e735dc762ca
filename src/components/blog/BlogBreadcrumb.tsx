import Link from 'next/link';
import { ChevronRightIcon, HomeIcon } from '@heroicons/react/24/outline';

interface BlogBreadcrumbProps {
  title: string;
  category?: string;
}

export default function BlogBreadcrumb({ title, category }: BlogBreadcrumbProps) {
  return (
    <nav className="flex items-center space-x-2 text-sm text-gray-500 mb-6" aria-label="Breadcrumb">
      <Link 
        href="/" 
        className="flex items-center hover:text-gray-700 transition-colors"
        title="Home"
      >
        <HomeIcon className="h-4 w-4" />
        <span className="sr-only">Home</span>
      </Link>
      
      <ChevronRightIcon className="h-4 w-4 text-gray-400" />
      
      <Link 
        href="/blog" 
        className="hover:text-gray-700 transition-colors"
      >
        Blog
      </Link>
      
      {category && (
        <>
          <ChevronRightIcon className="h-4 w-4 text-gray-400" />
          <Link 
            href={`/blog?category=${encodeURIComponent(category)}`}
            className="hover:text-gray-700 transition-colors capitalize"
          >
            {category.replace(/-/g, ' ')}
          </Link>
        </>
      )}
      
      <ChevronRightIcon className="h-4 w-4 text-gray-400" />
      
      <span className="text-gray-900 font-medium truncate max-w-xs" title={title}>
        {title}
      </span>
    </nav>
  );
} 