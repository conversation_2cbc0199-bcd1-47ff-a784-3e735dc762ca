import { BlogPost } from '@/types/blog';
import { getBlogPostUrl, getBaseUrl } from '@/utils/url';

interface BlogStructuredDataProps {
  post: BlogPost;
}

export default function BlogStructuredData({ post }: BlogStructuredDataProps) {
  const postUrl = getBlogPostUrl(post.slug);
  const baseUrl = getBaseUrl();

  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'BlogPosting',
    headline: post.title,
    description: post.excerpt,
    image: post.featuredImage ? [post.featuredImage] : [`${baseUrl}/images/blog-default-og.jpg`],
    datePublished: post.publishedAt || post.createdAt,
    dateModified: post.updatedAt,
    author: {
      '@type': 'Organization',
      name: post.author || 'Mocky Digital',
      url: baseUrl,
    },
    publisher: {
      '@type': 'Organization',
      name: '<PERSON>cky Digital',
      url: baseUrl,
      logo: {
        '@type': 'ImageObject',
        url: `${baseUrl}/images/logo.png`,
        width: 200,
        height: 60,
      },
    },
    mainEntityOfPage: {
      '@type': 'WebPage',
      '@id': postUrl,
    },
    url: postUrl,
    keywords: post.tags?.join(', ') || '',
    wordCount: post.content.replace(/<[^>]*>/g, '').split(/\s+/).length,
    timeRequired: `PT${post.readingTime || 1}M`,
    articleSection: post.category,
    inLanguage: 'en-US',
    isAccessibleForFree: true,
    ...(post.seoKeywords && post.seoKeywords.length > 0 && {
      about: post.seoKeywords.map(keyword => ({
        '@type': 'Thing',
        name: keyword,
      })),
    }),
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(structuredData, null, 2),
      }}
    />
  );
} 