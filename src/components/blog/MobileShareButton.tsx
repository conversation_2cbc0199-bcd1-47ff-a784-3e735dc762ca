'use client';
import React from 'react';

import { useState } from 'react';
import { ShareIcon } from '@heroicons/react/24/outline';

interface MobileShareButtonProps {
  url: string;
  title: string;
  description: string;
}

export default function MobileShareButton({ url, title, description }: MobileShareButtonProps) {
  const [isSharing, setIsSharing] = useState(false);

  const handleShare = async () => {
    setIsSharing(true);

    try {
      // Check if the Web Share API is available
      if (navigator.share) {
        await navigator.share({
          title,
          text: description,
          url,
        });
      } else {
        // Fallback: copy to clipboard
        await navigator.clipboard.writeText(`${title}\n\n${description}\n\n${url}`);
        
        // Show a temporary notification
        const notification = document.createElement('div');
        notification.textContent = 'Link copied to clipboard!';
        notification.className = 'fixed bottom-4 left-1/2 transform -translate-x-1/2 bg-gray-900 text-white px-4 py-2 rounded-lg text-sm z-50';
        document.body.appendChild(notification);
        
        setTimeout(() => {
          document.body.removeChild(notification);
        }, 2000);
      }
    } catch (error) {
      console.error('Error sharing:', error);
    } finally {
      setIsSharing(false);
    }
  };

  return (
    <button
      onClick={handleShare}
      disabled={isSharing}
      className="lg:hidden fixed bottom-6 right-6 bg-gray-900 hover:bg-black text-white p-4 rounded-full shadow-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 z-40"
      title="Share this article"
    >
      <ShareIcon className={`h-6 w-6 ${isSharing ? 'animate-pulse' : ''}`} />
    </button>
  );
} 