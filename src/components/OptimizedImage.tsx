'use client';

import Image, { ImageProps } from 'next/image';
import React, { useState, useEffect } from 'react';
import { useLazyImage } from '@/hooks/useIntersectionObserver';
import { getCDNUrl, generateSrcSet } from '@/lib/cdn';

interface OptimizedImageProps extends Omit<ImageProps, 'onLoadingComplete'> {
  lowQualitySrc?: string;
  fadeIn?: boolean;
  lazy?: boolean;
  responsive?: boolean;
  cdnOptimization?: boolean;
  aspectRatio?: number;
}

/**
 * A wrapper around Next.js Image component with additional optimizations:
 * - Progressive loading with blur-up effect
 * - Fade-in animation on load
 * - Better error handling
 * - Proper sizes attribute for responsive images
 * - CDN optimization
 * - Lazy loading with intersection observer
 * - Responsive image generation
 */
export default function OptimizedImage({
  src,
  alt,
  className = '',
  fill = false,
  lowQualitySrc,
  fadeIn = true,
  lazy = true,
  responsive = true,
  cdnOptimization = true,
  aspectRatio,
  sizes = '100vw',
  quality = 90,
  priority = false,
  ...props
}: OptimizedImageProps) {
  const [isLoaded, setIsLoaded] = useState(false);
  const [error, setError] = useState(false);

  // Use lazy loading if enabled and not priority
  const lazyImage = useLazyImage(src, {
    skip: !lazy || priority,
    threshold: 0.1,
    rootMargin: '100px',
  });

  // Determine the actual src to use
  const actualSrc = lazy && !priority ? lazyImage.src : (typeof src === 'string' ? src : src.src);

  // Reset loading state when src changes
  useEffect(() => {
    setIsLoaded(false);
    setError(false);
  }, [actualSrc]);

  // Default blur data URL if none provided
  const defaultBlurDataURL = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=";

  // Generate CDN-optimized URL if enabled
  const optimizedSrc = actualSrc && cdnOptimization
    ? getCDNUrl(actualSrc, { quality, format: 'webp' })
    : actualSrc;

  // Generate responsive srcSet if enabled
  const srcSet = responsive && actualSrc && !fill
    ? generateSrcSet(actualSrc, [320, 640, 768, 1024, 1280, 1920], { quality, format: 'webp' })
    : undefined;

  // Calculate appropriate sizes based on layout
  let sizeValue = sizes;
  if (fill && !sizes) {
    sizeValue = '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw';
  }

  // Styles for fade-in effect
  const imageStyle = {
    transition: fadeIn ? 'opacity 0.5s ease, filter 0.5s ease' : 'none',
    opacity: isLoaded || (lazy && !lazyImage.isVisible) ? 1 : 0.5,
    filter: isLoaded || (lazy && !lazyImage.isVisible) ? 'none' : 'blur(10px)',
  };

  // Container style for aspect ratio
  const containerStyle = aspectRatio && !fill ? {
    aspectRatio: aspectRatio.toString(),
    position: 'relative' as const,
  } : {};

  // Don't render anything if lazy loading and not visible yet
  if (lazy && !priority && !lazyImage.isVisible && !actualSrc) {
    return (
      <div
        ref={lazyImage.ref}
        className={`bg-gray-100 ${className}`}
        style={{ ...containerStyle, ...props.style }}
      >
        <div className="animate-pulse bg-gray-200 w-full h-full" />
      </div>
    );
  }

  const imageElement = (
    <Image
      ref={lazy && !priority ? lazyImage.ref : undefined}
      src={optimizedSrc || src}
      alt={alt || ''}
      className={className}
      sizes={sizeValue}
      quality={quality}
      fill={fill || !!aspectRatio}
      priority={priority}
      placeholder={lowQualitySrc || props.blurDataURL ? "blur" : "empty"}
      blurDataURL={props.blurDataURL || lowQualitySrc || defaultBlurDataURL}
      style={{
        ...props.style,
        ...imageStyle,
        ...(aspectRatio && !fill ? { objectFit: 'cover' } : {}),
      }}
      onLoad={() => {
        setIsLoaded(true);
        if (lazy) lazyImage.handleLoad();
      }}
      onError={() => {
        setError(true);
        if (lazy) lazyImage.handleError();
      }}
      {...(srcSet && { srcSet })}
      {...props}
    />
  );

  if (error) {
    return (
      <div
        className={`relative flex items-center justify-center bg-gray-100 ${className}`}
        style={{ ...containerStyle, ...props.style }}
      >
        <span className="text-sm text-gray-500">Failed to load image</span>
      </div>
    );
  }

  if (aspectRatio && !fill) {
    return (
      <div style={containerStyle} className="overflow-hidden">
        {imageElement}
      </div>
    );
  }

  return imageElement;
}