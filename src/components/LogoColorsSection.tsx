'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { ArrowPathIcon, ExclamationTriangleIcon, PaintBrushIcon } from '@heroicons/react/24/outline';
import ColorPalette from '@/components/ColorPalette';
import toast from 'react-hot-toast';
import Link from 'next/link';

interface ColorPaletteData {
  id: string;
  name: string;
  colors: string[];
  likes?: number;
  tags?: string[];
  createdAt: Date;
  updatedAt: Date;
}

interface ApiResponse {
  success: boolean;
  palettes: ColorPaletteData[];
  cached?: boolean;
  fallback?: boolean;
  error?: string;
  timestamp: Date;
}

export default function LogoColorsSection() {
  const [palettes, setPalettes] = useState<ColorPaletteData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchPalettes = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch('/api/colors/trending');
      const data: ApiResponse = await response.json();
      
      if (data.success) {
        // Limit to first 8 palettes for the logos page
        setPalettes(data.palettes.slice(0, 8));
        
        if (data.fallback) {
          console.log('Using fallback color palettes');
        }
      } else {
        setError(data.error || 'Failed to load color palettes');
        setPalettes(data.palettes?.slice(0, 8) || []);
      }
    } catch (err) {
      console.error('Error fetching palettes:', err);
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPalettes();
  }, []);

  const handleLike = (paletteId: string) => {
    setPalettes(prev => 
      prev.map(palette => 
        palette.id === paletteId 
          ? { ...palette, likes: (palette.likes || 0) + 1 }
          : palette
      )
    );
  };

  const handleShare = (paletteId: string) => {
    console.log(`Shared palette: ${paletteId}`);
  };

  return (
    <section className="py-20 lg:py-32 bg-gradient-to-br from-slate-50 via-white to-orange-50/30 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute top-20 left-10 w-20 h-20 bg-gradient-to-br from-orange-200/40 to-pink-200/40 rounded-full blur-xl animate-pulse"></div>
      <div className="absolute bottom-20 right-10 w-32 h-32 bg-gradient-to-br from-blue-200/30 to-purple-200/30 rounded-full blur-xl animate-pulse delay-1000"></div>

      <div className="container mx-auto px-4 relative z-10">
        {/* Section Header */}
        <div className="text-center mb-16 lg:mb-20">
          <div className="inline-flex items-center gap-2 bg-gradient-to-r from-orange-100 to-red-100 backdrop-blur-sm text-orange-600 px-6 py-3 rounded-full text-sm font-bold mb-8 border border-orange-200/50 shadow-lg">
            <span className="bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">
              COLOR INSPIRATION
            </span>
          </div>
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 text-slate-900 leading-[1.1] py-2">
            Trending Color Palettes
            <span className="block relative inline-block">
              <span className="bg-gradient-to-r from-orange-500 to-red-500 bg-clip-text text-transparent">
                for Logo Design
              </span>
              <div className="absolute -bottom-2 left-0 right-0 h-1 bg-gradient-to-r from-orange-400 to-red-400 rounded-full opacity-60 transform -rotate-2"></div>
            </span>
          </h2>
          <p className="text-lg md:text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed mb-8">
            Get inspired by these trending color combinations perfect for logo design.
            Click on any color to copy its hex code and use it in your brand identity.
          </p>

          {/* View All Colors Button */}
          <Link
            href="/colors"
            className="inline-flex items-center gap-3 bg-gradient-to-r from-slate-700 to-slate-900 text-white px-8 py-4 rounded-2xl font-bold text-lg shadow-xl hover:shadow-2xl transition-all duration-300 hover:scale-105 hover:-translate-y-1"
          >
            <PaintBrushIcon className="w-6 h-6" />
            <span>View All Color Palettes</span>
          </Link>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mb-8">
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 max-w-2xl mx-auto">
              <div className="flex items-center space-x-2">
                <ExclamationTriangleIcon className="w-5 h-5 text-red-500" />
                <span className="text-red-700">Failed to load some palettes, showing available ones</span>
              </div>
            </div>
          </div>
        )}

        {/* Loading State */}
        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {[...Array(8)].map((_, index) => (
              <div key={index} className="bg-white rounded-xl shadow-lg overflow-hidden animate-pulse">
                <div className="h-32 bg-gray-200"></div>
                <div className="p-4">
                  <div className="h-4 bg-gray-200 rounded mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-2/3 mb-3"></div>
                  <div className="h-8 bg-gray-200 rounded"></div>
                </div>
              </div>
            ))}
          </div>
                 ) : palettes.length > 0 ? (
           /* Color Palettes Grid */
           <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
             {palettes.map((palette, index) => (
               <motion.div
                 key={palette.id}
                 initial={{ opacity: 0, y: 20 }}
                 animate={{ opacity: 1, y: 0 }}
                 transition={{ duration: 0.3, delay: index * 0.1 }}
               >
                 <ColorPalette
                   id={palette.id}
                   name={palette.name}
                   colors={palette.colors}
                   likes={palette.likes || 0}
                   tags={palette.tags || []}
                   onLike={handleLike}
                   onShare={handleShare}
                 />
               </motion.div>
             ))}
           </div>
        ) : (
          /* No Palettes Available */
          <div className="text-center py-12">
            <ExclamationTriangleIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No color palettes available</h3>
            <p className="text-gray-500 mb-4">
              We couldn't load color palettes at the moment. Please try again later.
            </p>
            <button
              onClick={fetchPalettes}
              className="px-4 py-2 bg-[#1A237E] text-white rounded-lg hover:bg-[#0D47A1] transition-colors duration-200"
            >
              Try Again
            </button>
          </div>
        )}

        {/* Info Section */}
        <div className="mt-12 bg-white rounded-xl shadow-sm p-8">
          <div className="max-w-4xl mx-auto">
            <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">
              How to Use Color Palettes in Logo Design
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="w-12 h-12 bg-[#1A237E] rounded-full mx-auto mb-3 flex items-center justify-center">
                  <span className="text-white font-bold">1</span>
                </div>
                <h4 className="font-medium text-gray-900 mb-2">Choose Your Palette</h4>
                <p className="text-gray-600 text-sm">
                  Select a color palette that aligns with your brand personality and industry
                </p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-[#FF5400] rounded-full mx-auto mb-3 flex items-center justify-center">
                  <span className="text-white font-bold">2</span>
                </div>
                <h4 className="font-medium text-gray-900 mb-2">Copy Colors</h4>
                <p className="text-gray-600 text-sm">
                  Click on any color swatch to copy its hex code to your clipboard
                </p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-gray-600 rounded-full mx-auto mb-3 flex items-center justify-center">
                  <span className="text-white font-bold">3</span>
                </div>
                <h4 className="font-medium text-gray-900 mb-2">Apply to Logo</h4>
                <p className="text-gray-600 text-sm">
                  Use the colors in your logo design to create a cohesive brand identity
                </p>
              </div>
            </div>
            
            <div className="mt-8 text-center">
              <p className="text-gray-600 mb-4">
                Need a custom logo design with these colors?
              </p>
              <Link 
                href="#pricing-section"
                className="inline-flex items-center space-x-2 px-6 py-3 bg-[#FF5400] text-white rounded-lg hover:bg-[#E04900] transition-colors duration-200 font-medium"
              >
                <span>Start Your Logo Project</span>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
} 