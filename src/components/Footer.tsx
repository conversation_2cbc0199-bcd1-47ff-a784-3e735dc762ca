import Link from 'next/link';

export default function Footer() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-[#0A1929] text-white relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        }} />
      </div>

      {/* Main Footer Content */}
      <div className="relative z-10">
        {/* Top Section */}
        <div className="border-b border-white/10">
          <div className="container mx-auto px-6 py-16">
            <div className="grid grid-cols-1 lg:grid-cols-12 gap-12">
              {/* Company Info - Takes more space */}
              <div className="lg:col-span-5">
                <div className="mb-8">
                  <Link href="/" className="inline-block">
                    <h2 className="text-3xl font-bold bg-gradient-to-r from-[#FF5400] to-[#FF7A00] bg-clip-text text-transparent mb-4">
                      Mocky Digital
                    </h2>
                  </Link>
                  <p className="text-gray-300 text-lg leading-relaxed mb-6 max-w-md">
                    Your trusted partner for innovative digital solutions. We transform ideas into powerful digital experiences that drive growth and success.
                  </p>
                  
                  {/* Key Stats */}
                  <div className="grid grid-cols-3 gap-6 mb-8">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-[#FF5400] mb-1">500+</div>
                      <div className="text-xs text-gray-400 uppercase tracking-wide">Projects</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-[#FF5400] mb-1">5+</div>
                      <div className="text-xs text-gray-400 uppercase tracking-wide">Years</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-[#FF5400] mb-1">24/7</div>
                      <div className="text-xs text-gray-400 uppercase tracking-wide">Support</div>
                    </div>
                  </div>
                </div>

                {/* Social Media */}
                <div>
                  <h4 className="text-white font-semibold mb-4">Follow Us</h4>
                  <div className="flex gap-3">
                    {[
                      { icon: 'fab fa-facebook-f', href: '#', label: 'Facebook', color: 'hover:bg-blue-600' },
                      { icon: 'fab fa-twitter', href: '#', label: 'Twitter', color: 'hover:bg-blue-400' },
                      { icon: 'fab fa-instagram', href: '#', label: 'Instagram', color: 'hover:bg-pink-600' },
                      { icon: 'fab fa-linkedin-in', href: '#', label: 'LinkedIn', color: 'hover:bg-blue-700' },
                      { icon: 'fab fa-tiktok', href: '#', label: 'TikTok', color: 'hover:bg-black' },
                      { icon: 'fab fa-whatsapp', href: 'https://wa.me/254741590670', label: 'WhatsApp', color: 'hover:bg-green-600' }
                    ].map((social, index) => (
                      <a
                        key={index}
                        href={social.href}
                        target="_blank"
                        rel="noopener noreferrer"
                        className={`w-12 h-12 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 flex items-center justify-center text-white transition-all duration-300 hover:scale-110 hover:border-white/40 ${social.color}`}
                        aria-label={social.label}
                      >
                        <i className={`${social.icon} text-lg`}></i>
                      </a>
                    ))}
                  </div>
                </div>
              </div>

              {/* Navigation Links */}
              <div className="lg:col-span-7">
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
                  {/* Quick Links */}
                  <div>
                    <h4 className="text-white font-semibold text-lg mb-6 relative">
                      Quick Links
                      <div className="absolute -bottom-2 left-0 w-8 h-0.5 bg-gradient-to-r from-[#FF5400] to-[#FF7A00]"></div>
                    </h4>
                    <ul className="space-y-3">
                      {[
                        { href: '/about', label: 'About Us' },
                        { href: '/portfolio', label: 'Portfolio' },
                        { href: '/catalogue', label: 'Catalogue' },
                        { href: '/blog', label: 'Blog' },
                        { href: '/contact', label: 'Contact Us' },
                        { href: '/freelance', label: 'Freelance' },
                        { href: '/training', label: 'Training' }
                      ].map((link, index) => (
                        <li key={index}>
                          <Link 
                            href={link.href} 
                            className="text-gray-300 hover:text-white hover:translate-x-1 transition-all duration-300 flex items-center group"
                          >
                            <span className="w-1 h-1 rounded-full bg-[#FF5400] mr-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
                            {link.label}
                          </Link>
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* Services */}
                  <div>
                    <h4 className="text-white font-semibold text-lg mb-6 relative">
                      Services
                      <div className="absolute -bottom-2 left-0 w-8 h-0.5 bg-gradient-to-r from-[#FF5400] to-[#FF7A00]"></div>
                    </h4>
                    <ul className="space-y-3">
                      {[
                        { href: '/graphics', label: 'Graphics Design' },
                        { href: '/web-development', label: 'Web Development' },
                        { href: '/social-media', label: 'Social Media' },
                        { href: '/services', label: 'Branding' },
                        { href: '/services', label: 'Digital Marketing' },
                        { href: '/services', label: 'Consultancy' }
                      ].map((service, index) => (
                        <li key={index}>
                          <Link 
                            href={service.href} 
                            className="text-gray-300 hover:text-white hover:translate-x-1 transition-all duration-300 flex items-center group"
                          >
                            <span className="w-1 h-1 rounded-full bg-[#FF5400] mr-3 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></span>
                            {service.label}
                          </Link>
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* Contact Info */}
                  <div>
                    <h4 className="text-white font-semibold text-lg mb-6 relative">
                      Get In Touch
                      <div className="absolute -bottom-2 left-0 w-8 h-0.5 bg-gradient-to-r from-[#FF5400] to-[#FF7A00]"></div>
                    </h4>
                    <ul className="space-y-4">
                      <li className="flex items-start gap-3">
                        <div className="w-8 h-8 rounded-lg bg-[#FF5400]/20 flex items-center justify-center flex-shrink-0 mt-0.5">
                          <i className="fas fa-map-marker-alt text-[#FF5400] text-sm"></i>
                        </div>
                        <div>
                          <div className="text-white font-medium">Location</div>
                          <div className="text-gray-300 text-sm">Nairobi, Kenya</div>
                        </div>
                      </li>
                      <li className="flex items-start gap-3">
                        <div className="w-8 h-8 rounded-lg bg-[#FF5400]/20 flex items-center justify-center flex-shrink-0 mt-0.5">
                          <i className="fas fa-phone text-[#FF5400] text-sm"></i>
                        </div>
                        <div>
                          <div className="text-white font-medium">Phone</div>
                          <a href="tel:+254741590670" className="text-gray-300 hover:text-[#FF5400] transition-colors text-sm">
                            +254 741 590 670
                          </a>
                        </div>
                      </li>
                      <li className="flex items-start gap-3">
                        <div className="w-8 h-8 rounded-lg bg-[#FF5400]/20 flex items-center justify-center flex-shrink-0 mt-0.5">
                          <i className="fas fa-envelope text-[#FF5400] text-sm"></i>
                        </div>
                        <div>
                          <div className="text-white font-medium">Email</div>
                          <a href="mailto:<EMAIL>" className="text-gray-300 hover:text-[#FF5400] transition-colors text-sm">
                            <EMAIL>
                          </a>
                        </div>
                      </li>
                    </ul>

                    {/* CTA Button */}
                    <div className="mt-6">
                      <Link 
                        href="/contact"
                        className="inline-flex items-center gap-2 bg-gradient-to-r from-[#FF5400] to-[#FF7A00] text-white px-6 py-3 rounded-full font-medium hover:shadow-lg hover:shadow-[#FF5400]/25 transition-all duration-300 hover:scale-105"
                      >
                        Start Your Project
                        <i className="fas fa-arrow-right text-sm"></i>
                      </Link>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Newsletter Section */}
        <div className="border-b border-white/10">
          <div className="container mx-auto px-6 py-12">
            <div className="max-w-4xl mx-auto text-center">
              <h3 className="text-2xl font-bold text-white mb-4">Stay Updated</h3>
              <p className="text-gray-300 mb-8 max-w-2xl mx-auto">
                Subscribe to our newsletter for the latest updates, design tips, and exclusive offers.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
                <input
                  type="email"
                  placeholder="Enter your email"
                  className="flex-1 px-4 py-3 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 text-white placeholder-gray-400 focus:outline-none focus:border-[#FF5400] focus:bg-white/20 transition-all duration-300"
                />
                <button className="px-8 py-3 bg-gradient-to-r from-[#FF5400] to-[#FF7A00] text-white rounded-full font-medium hover:shadow-lg hover:shadow-[#FF5400]/25 transition-all duration-300 hover:scale-105 whitespace-nowrap">
                  Subscribe
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="container mx-auto px-6 py-8">
          <div className="flex flex-col lg:flex-row justify-between items-center gap-6">
            {/* Copyright */}
            <div className="text-center lg:text-left">
              <p className="text-gray-400 text-sm">
                © {currentYear} Mocky Digital. All rights reserved.
              </p>
              <p className="text-gray-500 text-xs mt-1">
                Crafted with ❤️ in Nairobi, Kenya
              </p>
            </div>

            {/* Legal Links */}
            <div className="flex flex-wrap items-center gap-6 text-sm">
              <Link href="/terms" className="text-gray-400 hover:text-white transition-colors">
                Terms & Conditions
              </Link>
              <Link href="/privacy" className="text-gray-400 hover:text-white transition-colors">
                Privacy Policy
              </Link>
              <Link href="/sitemap" className="text-gray-400 hover:text-white transition-colors">
                Sitemap
              </Link>
            </div>

            {/* Back to Top */}
            <button
              onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
              className="w-12 h-12 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 flex items-center justify-center text-white hover:bg-[#FF5400] hover:border-[#FF5400] transition-all duration-300 hover:scale-110"
              aria-label="Back to top"
            >
              <i className="fas fa-arrow-up text-sm"></i>
            </button>
          </div>
        </div>
      </div>
    </footer>
  );
}