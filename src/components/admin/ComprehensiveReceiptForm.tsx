'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import {
  DocumentTextIcon,
  ArrowPathIcon,
  PlusIcon,
  TrashIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline';
import { useNotification } from '@/contexts/NotificationContext';
import { parseTransactionMessage } from '@/utils/transactionParser';

interface Service {
  id: string;
  name: string;
  price: number;
  description?: string;
}

interface ReceiptItem {
  serviceId: string;
  quantity: number;
  unitPrice: number;
  description: string;
  serviceName?: string;
  isCustomService: boolean;
  discount: number;
  discountType: 'fixed' | 'percentage';
}

interface ComprehensiveReceiptFormProps {
  onReceiptCreated?: (receiptId: string) => void;
}

export default function ComprehensiveReceiptForm({ onReceiptCreated }: ComprehensiveReceiptFormProps) {
  const [mpesaMessage, setMpesaMessage] = useState('');
  const [parsedData, setParsedData] = useState<any>(null);
  const [customerName, setCustomerName] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [email, setEmail] = useState('');
  const [notes, setNotes] = useState('');
  const [receiptItems, setReceiptItems] = useState<ReceiptItem[]>([]);
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);
  const [parsing, setParsing] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isDuplicateReceipt, setIsDuplicateReceipt] = useState(false);
  const [existingReceiptId, setExistingReceiptId] = useState<string | null>(null);

  const router = useRouter();
  const { showNotification } = useNotification();

  // Load services on component mount
  useEffect(() => {
    fetchServices();
  }, []);

  const fetchServices = async () => {
    try {
      const response = await fetch('/api/admin/services');
      if (response.ok) {
        const data = await response.json();
        setServices(data.data || []);
      }
    } catch (err) {
      console.error('Error fetching services:', err);
    } finally {
      setLoading(false);
    }
  };

  // Parse M-Pesa message
  const handleParseMessage = async () => {
    if (!mpesaMessage.trim()) {
      setError('Please enter an M-Pesa transaction message');
      return;
    }

    setParsing(true);
    setError(null);
    setIsDuplicateReceipt(false);
    setExistingReceiptId(null);

    try {
      // Parse the message locally first
      console.log('[ComprehensiveReceiptForm] Attempting to parse message:', mpesaMessage.substring(0, 100) + '...');
      const parsed = parseTransactionMessage(mpesaMessage);
      if (!parsed) {
        throw new Error('Failed to parse M-Pesa message. Please ensure it contains transaction ID, amount, customer name, and phone number.');
      }
      console.log('[ComprehensiveReceiptForm] Successfully parsed:', parsed);

      // Check if receipt already exists
      console.log('[ComprehensiveReceiptForm] Making request to check for duplicates...');
      const checkResponse = await fetch('/api/admin/comprehensive-receipts/check', {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          mpesaTransactionId: parsed.transactionId
        }),
      });
      
      if (checkResponse.ok) {
        const checkResult = await checkResponse.json();
        console.log('[ComprehensiveReceiptForm] Check result:', checkResult);

        if (checkResult?.exists) {
          setIsDuplicateReceipt(true);
          setExistingReceiptId(checkResult.receiptId);
          throw new Error(`A receipt already exists for this transaction (Receipt #${checkResult.receiptNumber})`);
        }
      }

      // Set parsed data and prefill form
      setParsedData(parsed);
      setCustomerName(parsed.customerName);
      setPhoneNumber(parsed.phoneNumber);
      
      // Add a default service item if services are available
      if (services.length > 0 && receiptItems.length === 0) {
        addReceiptItem();
      }

      showNotification('success', 'M-Pesa message parsed successfully');
    } catch (err) {
      console.error('Error parsing M-Pesa message:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to parse M-Pesa message';
      setError(errorMessage);
      showNotification('error', errorMessage);
    } finally {
      setParsing(false);
    }
  };

  // Add receipt item
  const addReceiptItem = () => {
    const defaultService = services[0];
    setReceiptItems([
      ...receiptItems,
      {
        serviceId: defaultService?.id || '',
        quantity: 1,
        unitPrice: defaultService?.price || 0,
        description: defaultService?.name || '',
        serviceName: defaultService?.name || '',
        isCustomService: false,
        discount: 0,
        discountType: 'fixed' as const
      }
    ]);
  };

  // Add custom receipt item
  const addCustomReceiptItem = () => {
    setReceiptItems([
      ...receiptItems,
      {
        serviceId: 'custom',
        quantity: 1,
        unitPrice: 0,
        description: '',
        serviceName: '',
        isCustomService: true,
        discount: 0,
        discountType: 'fixed' as const
      }
    ]);
  };

  // Update receipt item
  const updateReceiptItem = (index: number, field: keyof ReceiptItem, value: any) => {
    const updatedItems = [...receiptItems];
    updatedItems[index] = { ...updatedItems[index], [field]: value };

    // If service ID changed, update related fields
    if (field === 'serviceId') {
      if (value === 'custom') {
        updatedItems[index].isCustomService = true;
        updatedItems[index].unitPrice = 0;
        updatedItems[index].description = '';
        updatedItems[index].serviceName = '';
      } else {
        const selectedService = services.find(s => s.id === value);
        if (selectedService) {
          updatedItems[index].isCustomService = false;
          updatedItems[index].unitPrice = selectedService.price;
          updatedItems[index].description = selectedService.name;
          updatedItems[index].serviceName = selectedService.name;
        }
      }
    }

    setReceiptItems(updatedItems);
  };

  // Remove receipt item
  const removeReceiptItem = (index: number) => {
    setReceiptItems(receiptItems.filter((_, i) => i !== index));
  };

  // Calculate item total with discount
  const calculateItemTotal = (item: ReceiptItem) => {
    const subtotal = item.quantity * item.unitPrice;
    let discount = 0;
    
    if (item.discountType === 'percentage') {
      discount = (subtotal * item.discount) / 100;
    } else {
      discount = item.discount;
    }
    
    return Math.max(0, subtotal - discount);
  };

  // Calculate total amount
  const calculateTotal = () => {
    return receiptItems.reduce((total, item) => {
      return total + calculateItemTotal(item);
    }, 0);
  };

  // Calculate total discount
  const calculateTotalDiscount = () => {
    return receiptItems.reduce((total, item) => {
      const subtotal = item.quantity * item.unitPrice;
      let discount = 0;
      
      if (item.discountType === 'percentage') {
        discount = (subtotal * item.discount) / 100;
      } else {
        discount = item.discount;
      }
      
      return total + discount;
    }, 0);
  };

  // Submit form
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!parsedData) {
      setError('Please parse an M-Pesa message first');
      return;
    }



    if (receiptItems.length === 0) {
      setError('Please add at least one service item');
      return;
    }

    // Validate receipt items
    for (let i = 0; i < receiptItems.length; i++) {
      const item = receiptItems[i];
      if (!item.serviceId || (item.isCustomService && !item.description.trim())) {
        setError(`Please complete all fields for item ${i + 1}`);
        return;
      }
      if (item.unitPrice <= 0) {
        setError(`Please enter a valid price for item ${i + 1}`);
        return;
      }
      if (item.quantity <= 0) {
        setError(`Please enter a valid quantity for item ${i + 1}`);
        return;
      }
    }

    setSubmitting(true);
    setError(null);

    try {
      console.log('[ComprehensiveReceiptForm] Making request to create receipt...');
      console.log('[ComprehensiveReceiptForm] Receipt items:', receiptItems);
      
      const response = await fetch('/api/admin/comprehensive-receipts', {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          mpesaMessage,
          customerName,
          phoneNumber,
          email: email || undefined,
          notes: notes || undefined,
          items: receiptItems.map(item => ({
            serviceId: item.isCustomService ? null : item.serviceId,
            quantity: item.quantity,
            unitPrice: item.unitPrice,
            description: item.description,
            discount: item.discount,
            discountType: item.discountType,
            isCustomService: item.isCustomService
          }))
        }),
      });
      
      console.log('[ComprehensiveReceiptForm] Response status:', response.status);
      console.log('[ComprehensiveReceiptForm] Response headers:', Object.fromEntries(response.headers.entries()));
      
      if (!response.ok) {
        // Handle specific error cases
        if (response.status === 401) {
          throw new Error('Authentication required. Please refresh the page and try again.');
        }
        
        // Try to get error details from response
        let errorDetails = `HTTP ${response.status}`;
        try {
          const errorData = await response.json();
          errorDetails = errorData.error || errorData.message || errorDetails;
        } catch (parseError) {
          // If we can't parse JSON, try to get text
          try {
            const errorText = await response.text();
            if (errorText) {
              errorDetails = errorText;
            }
          } catch (textError) {
            // Use default error
          }
        }
        throw new Error(`Failed to create receipt: ${errorDetails}`);
      }

      const result = await response.json();
      console.log('[ComprehensiveReceiptForm] Create result:', result);

      // Validate result data
      if (!result || !result.success) {
        throw new Error(`Server error: ${result?.error || 'Unknown error occurred'}`);
      }
      
      if (!result.data || !result.data.id) {
        throw new Error('Invalid response data - missing receipt ID');
      }

      showNotification('success', 'Receipt created successfully');

      // Clear form
      setMpesaMessage('');
      setParsedData(null);
      setCustomerName('');
      setPhoneNumber('');
      setEmail('');
      setNotes('');
      setReceiptItems([]);

      // Navigate to receipt or call callback
      if (onReceiptCreated) {
        onReceiptCreated(result.data.id);
      } else {
        router.push(`/admin/comprehensive-receipts/${result.data.id}`);
      }
    } catch (err) {
      console.error('Error creating receipt:', err);
      
      // Better error handling
      let errorMessage = 'Failed to create receipt';
      
      if (err instanceof Error) {
        errorMessage = err.message;
      } else if (typeof err === 'string') {
        errorMessage = err;
      } else if (err && typeof err === 'object') {
        errorMessage = (err as any).error || (err as any).message || errorMessage;
      }
      
      setError(errorMessage);
      showNotification('error', errorMessage);
    } finally {
      setSubmitting(false);
    }
  };

  if (loading) {
    return (
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex justify-center items-center h-40">
          <ArrowPathIcon className="animate-spin h-8 w-8 text-blue-500" />
          <span className="ml-2 text-gray-600">Loading services...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white shadow rounded-lg">
      <div className="px-6 py-4 border-b border-gray-200">
        <h2 className="text-lg font-medium text-gray-900">Receipt Details</h2>
      </div>

      <form onSubmit={handleSubmit} className="p-6">
        {/* Error Display */}
        {error && (
          <div className={`${isDuplicateReceipt ? 'bg-yellow-50 border-yellow-400' : 'bg-red-50 border-red-400'} border-l-4 p-4 mb-6`}>
            <div className="flex">
              <div className="flex-shrink-0">
                {isDuplicateReceipt ? (
                  <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400" />
                ) : (
                  <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
                )}
              </div>
              <div className="ml-3">
                <p className={`text-sm ${isDuplicateReceipt ? 'text-yellow-700' : 'text-red-700'}`}>{error}</p>
                {isDuplicateReceipt && existingReceiptId && (
                  <div className="mt-2">
                    <button
                      type="button"
                      onClick={() => router.push(`/admin/comprehensive-receipts/${existingReceiptId}`)}
                      className="inline-flex items-center text-sm font-medium text-yellow-700 hover:text-yellow-900"
                    >
                      <DocumentTextIcon className="-ml-0.5 mr-1 h-4 w-4" />
                      View Existing Receipt
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* M-Pesa Message Input */}
        <div className="mb-6">
          <label htmlFor="mpesaMessage" className="block text-sm font-medium text-gray-700 mb-2">
            M-Pesa Transaction Message
          </label>
          <div className="relative">
            <textarea
              id="mpesaMessage"
              rows={4}
              value={mpesaMessage}
              onChange={(e) => setMpesaMessage(e.target.value)}
              placeholder="Paste the M-Pesa transaction message here..."
              className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              disabled={!!parsedData}
            />
            {!parsedData && (
              <div className="absolute right-2 bottom-2">
                <button
                  type="button"
                  onClick={handleParseMessage}
                  disabled={parsing || !mpesaMessage.trim()}
                  className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {parsing ? (
                    <>
                      <ArrowPathIcon className="animate-spin -ml-0.5 mr-1 h-3 w-3" />
                      Parsing...
                    </>
                  ) : (
                    <>
                      <CheckCircleIcon className="-ml-0.5 mr-1 h-3 w-3" />
                      Parse
                    </>
                  )}
                </button>
              </div>
            )}
          </div>
          {parsedData && (
            <div className="mt-2 p-3 bg-green-50 border border-green-200 rounded-md">
              <div className="flex items-center">
                <CheckCircleIcon className="h-4 w-4 text-green-500 mr-2" />
                <span className="text-sm text-green-700">
                  Parsed: {parsedData.transactionId} - KES {parsedData.amount.toLocaleString()} from {parsedData.customerName}
                </span>
                <button
                  type="button"
                  onClick={() => {
                    setParsedData(null);
                    setCustomerName('');
                    setPhoneNumber('');
                    setReceiptItems([]);
                  }}
                  className="ml-auto text-xs text-green-600 hover:text-green-800"
                >
                  Reset
                </button>
              </div>
            </div>
          )}
        </div>

        {parsedData && (
          <>
            {/* Customer Information */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6 mb-6">
              <div>
                <label htmlFor="customerName" className="block text-sm font-medium text-gray-700 mb-1">
                  Customer Name
                </label>
                <input
                  type="text"
                  id="customerName"
                  value={customerName}
                  onChange={(e) => setCustomerName(e.target.value)}
                  className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  required
                />
              </div>
              <div>
                <label htmlFor="phoneNumber" className="block text-sm font-medium text-gray-700 mb-1">
                  Phone Number
                </label>
                <input
                  type="text"
                  id="phoneNumber"
                  value={phoneNumber}
                  onChange={(e) => setPhoneNumber(e.target.value)}
                  className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6 mb-6">
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                  Email (Optional)
                </label>
                <input
                  type="email"
                  id="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                />
              </div>
              <div>
                <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">
                  Notes (Optional)
                </label>
                <input
                  type="text"
                  id="notes"
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                />
              </div>
            </div>

            {/* Receipt Items */}
            <div className="mb-6">
              <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-4 space-y-2 sm:space-y-0">
                <h3 className="text-sm font-medium text-gray-900">Receipt Items</h3>
                <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
                  <button
                    type="button"
                    onClick={addReceiptItem}
                    className="inline-flex items-center justify-center px-3 py-1.5 border border-gray-300 rounded-md text-xs font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    <PlusIcon className="-ml-0.5 mr-1 h-3 w-3" />
                    Add Service
                  </button>
                  <button
                    type="button"
                    onClick={addCustomReceiptItem}
                    className="inline-flex items-center justify-center px-3 py-1.5 border border-blue-300 rounded-md text-xs font-medium text-blue-700 bg-blue-50 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    <PlusIcon className="-ml-0.5 mr-1 h-3 w-3" />
                    Add Custom
                  </button>
                </div>
              </div>

              {receiptItems.map((item, index) => (
                <div key={index} className="mb-4 p-3 sm:p-4 border border-gray-200 rounded-lg bg-gray-50">
                  {/* Service Selection Row */}
                  <div className="grid grid-cols-1 lg:grid-cols-4 gap-3 mb-3">
                    <div className="lg:col-span-2">
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        Service
                      </label>
                      {item.isCustomService ? (
                        <input
                          type="text"
                          value={item.description}
                          onChange={(e) => updateReceiptItem(index, 'description', e.target.value)}
                          placeholder="Enter custom service name"
                          className="block w-full border border-gray-300 rounded-md shadow-sm py-1.5 px-2 text-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        />
                      ) : (
                        <select
                          value={item.serviceId}
                          onChange={(e) => updateReceiptItem(index, 'serviceId', e.target.value)}
                          className="block w-full border border-gray-300 rounded-md shadow-sm py-1.5 px-2 text-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        >
                          <option value="">Select Service</option>
                          <option value="custom">Custom Service</option>
                          {services.map((service) => (
                            <option key={service.id} value={service.id}>
                              {service.name} - KES {service.price.toLocaleString()}
                            </option>
                          ))}
                        </select>
                      )}
                    </div>
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        Quantity
                      </label>
                      <input
                        type="number"
                        min="1"
                        value={item.quantity}
                        onChange={(e) => updateReceiptItem(index, 'quantity', parseInt(e.target.value) || 1)}
                        className="block w-full border border-gray-300 rounded-md shadow-sm py-1.5 px-2 text-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Qty"
                      />
                    </div>
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        Unit Price (KES)
                      </label>
                      <input
                        type="number"
                        min="0"
                        step="0.01"
                        value={item.unitPrice}
                        onChange={(e) => updateReceiptItem(index, 'unitPrice', parseFloat(e.target.value) || 0)}
                        className="block w-full border border-gray-300 rounded-md shadow-sm py-1.5 px-2 text-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        placeholder="0.00"
                      />
                    </div>
                  </div>

                  {/* Discount and Actions Row */}
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 mb-3">
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        Discount Type
                      </label>
                      <select
                        value={item.discountType}
                        onChange={(e) => updateReceiptItem(index, 'discountType', e.target.value as 'fixed' | 'percentage')}
                        className="block w-full border border-gray-300 rounded-md shadow-sm py-1.5 px-2 text-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="fixed">Fixed Amount</option>
                        <option value="percentage">Percentage</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-1">
                        Discount {item.discountType === 'percentage' ? '(%)' : '(KES)'}
                      </label>
                      <input
                        type="number"
                        min="0"
                        step={item.discountType === 'percentage' ? '0.1' : '0.01'}
                        max={item.discountType === 'percentage' ? '100' : undefined}
                        value={item.discount}
                        onChange={(e) => updateReceiptItem(index, 'discount', parseFloat(e.target.value) || 0)}
                        className="block w-full border border-gray-300 rounded-md shadow-sm py-1.5 px-2 text-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        placeholder="0"
                      />
                    </div>
                    <div className="flex flex-col justify-between">
                      <div className="text-sm text-gray-600">
                        <div>Subtotal: KES {(item.quantity * item.unitPrice).toLocaleString()}</div>
                        <div className="text-green-600 font-medium">Total: KES {calculateItemTotal(item).toLocaleString()}</div>
                      </div>
                    </div>
                    <div className="flex items-end justify-start sm:justify-end">
                      <button
                        type="button"
                        onClick={() => removeReceiptItem(index)}
                        className="inline-flex items-center px-2 py-1 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-md text-sm"
                      >
                        <TrashIcon className="h-4 w-4 mr-1" />
                        Remove
                      </button>
                    </div>
                  </div>
                </div>
              ))}

              {receiptItems.length > 0 && (
                <div className="mt-4 p-3 sm:p-4 bg-white border border-gray-200 rounded-lg">
                  <h4 className="text-sm font-medium text-gray-900 mb-3">Receipt Summary</h4>
                  
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">Subtotal:</span>
                      <span className="text-sm text-gray-900">
                        KES {receiptItems.reduce((total, item) => total + (item.quantity * item.unitPrice), 0).toLocaleString()}
                      </span>
                    </div>
                    
                    {calculateTotalDiscount() > 0 && (
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">Total Discount:</span>
                        <span className="text-sm text-green-600">
                          -KES {calculateTotalDiscount().toLocaleString()}
                        </span>
                      </div>
                    )}
                    
                    <div className="border-t border-gray-200 pt-2">
                      <div className="flex justify-between items-center">
                        <span className="text-base font-medium text-gray-900">Total Amount:</span>
                        <span className="text-lg font-bold text-gray-900">KES {calculateTotal().toLocaleString()}</span>
                      </div>
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">Amount Paid:</span>
                      <span className="text-sm text-gray-600">KES {parsedData.amount.toLocaleString()}</span>
                    </div>
                    
                    <div className="border-t border-gray-200 pt-2">
                      <div className="flex justify-between items-center">
                        <span className="text-base font-medium text-gray-900">Balance:</span>
                        <span className={`text-base font-bold ${
                          (calculateTotal() - parsedData.amount) > 0 ? 'text-red-600' : 
                          (calculateTotal() - parsedData.amount) < 0 ? 'text-blue-600' : 'text-green-600'
                        }`}>
                          {(calculateTotal() - parsedData.amount) < 0 ? '+' : ''}KES {Math.abs(calculateTotal() - parsedData.amount).toLocaleString()}
                        </span>
                      </div>
                      {(calculateTotal() - parsedData.amount) < 0 && (
                        <div className="mt-1 text-xs text-blue-600">
                          💡 Customer has overpaid (tip/credit)
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Submit Button */}
            <div className="flex flex-col sm:flex-row justify-end space-y-3 sm:space-y-0 sm:space-x-4">
              {existingReceiptId && (
                <button
                  type="button"
                  onClick={() => router.push(`/admin/comprehensive-receipts/${existingReceiptId}`)}
                  className="inline-flex justify-center items-center px-4 py-2 border border-blue-300 rounded-md shadow-sm text-sm font-medium text-blue-700 bg-blue-50 hover:bg-blue-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  View Existing Receipt
                </button>
              )}
              <button
                type="submit"
                disabled={submitting || receiptItems.length === 0}
                className="inline-flex justify-center items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {submitting ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Creating Receipt...
                  </>
                ) : (
                  'Create Receipt'
                )}
              </button>
            </div>
          </>
        )}
      </form>
    </div>
  );
} 