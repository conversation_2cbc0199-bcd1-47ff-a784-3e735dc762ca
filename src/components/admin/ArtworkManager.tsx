import React, { useState, useEffect } from 'react';
import { Download, Eye, Trash2, FileText, Image, AlertCircle, RefreshCw } from 'lucide-react';

interface ArtworkFile {
  fileName: string;
  originalName: string;
  filePath: string;
  fileSize: number;
  fileType: string;
  uploadedAt: string;
}

interface ArtworkManagerProps {
  orderNumber: string;
  editable?: boolean;
}

const ArtworkManager: React.FC<ArtworkManagerProps> = ({
  orderNumber,
  editable = false
}) => {
  const [files, setFiles] = useState<ArtworkFile[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [previewFile, setPreviewFile] = useState<ArtworkFile | null>(null);

  // Fetch artwork files
  const fetchFiles = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/upload/artwork?orderNumber=${orderNumber}`);
      const result = await response.json();

      if (result.success) {
        setFiles(result.files || []);
      } else {
        setError(result.error || 'Failed to load artwork files');
      }
    } catch (err) {
      console.error('Error fetching artwork files:', err);
      setError('Failed to load artwork files');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (orderNumber) {
      fetchFiles();
    }
  }, [orderNumber]);

  // Download file
  const downloadFile = async (file: ArtworkFile) => {
    try {
      const response = await fetch(
        `/api/upload/artwork/download?orderNumber=${orderNumber}&fileName=${file.fileName}`
      );

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = file.originalName;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      } else {
        const error = await response.json();
        alert(`Download failed: ${error.error || 'Unknown error'}`);
      }
    } catch (err) {
      console.error('Download error:', err);
      alert('Download failed. Please try again.');
    }
  };

  // Preview file (for images)
  const previewImage = (file: ArtworkFile) => {
    if (file.fileType.startsWith('image/')) {
      setPreviewFile(file);
    }
  };

  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Get file icon
  const getFileIcon = (fileType: string) => {
    if (fileType.startsWith('image/')) {
      return <Image className="w-5 h-5 text-blue-500" />;
    }
    return <FileText className="w-5 h-5 text-gray-500" />;
  };

  // Get file type label
  const getFileTypeLabel = (fileType: string) => {
    switch (fileType) {
      case 'application/pdf': return 'PDF';
      case 'image/jpeg': return 'JPEG';
      case 'image/png': return 'PNG';
      case 'image/gif': return 'GIF';
      case 'image/webp': return 'WebP';
      case 'image/svg+xml': return 'SVG';
      case 'application/postscript': return 'AI';
      case 'application/eps': return 'EPS';
      case 'image/vnd.adobe.photoshop': return 'PSD';
      default: return 'File';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="w-6 h-6 animate-spin text-gray-500 mr-2" />
        <span className="text-gray-600">Loading artwork files...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
        <div className="flex items-start">
          <AlertCircle className="w-5 h-5 text-red-500 mt-0.5 mr-2 flex-shrink-0" />
          <div className="flex-1">
            <p className="text-sm text-red-700">{error}</p>
            <button
              onClick={fetchFiles}
              className="mt-2 text-sm text-red-600 hover:text-red-800 underline"
            >
              Try again
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (files.length === 0) {
    return (
      <div className="text-center p-8 text-gray-500">
        <FileText className="w-12 h-12 mx-auto mb-2 text-gray-300" />
        <p>No artwork files uploaded for this order</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900">
          Artwork Files ({files.length})
        </h3>
        <button
          onClick={fetchFiles}
          className="text-sm text-gray-600 hover:text-gray-800 flex items-center"
        >
          <RefreshCw className="w-4 h-4 mr-1" />
          Refresh
        </button>
      </div>

      {/* Files Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {files.map((file, index) => (
          <div
            key={index}
            className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
          >
            {/* File Header */}
            <div className="flex items-start justify-between mb-3">
              <div className="flex items-center space-x-2">
                {getFileIcon(file.fileType)}
                <span className="text-xs font-medium text-gray-500 bg-gray-100 px-2 py-1 rounded">
                  {getFileTypeLabel(file.fileType)}
                </span>
              </div>
            </div>

            {/* File Preview/Thumbnail */}
            {file.fileType.startsWith('image/') ? (
              <div className="mb-3">
                <img
                  src={file.filePath}
                  alt={file.originalName}
                  className="w-full h-32 object-cover rounded border cursor-pointer hover:opacity-75 transition-opacity"
                  onClick={() => previewImage(file)}
                />
              </div>
            ) : (
              <div className="mb-3 h-32 bg-gray-50 rounded border flex items-center justify-center">
                <FileText className="w-12 h-12 text-gray-300" />
              </div>
            )}

            {/* File Info */}
            <div className="space-y-2">
              <h4 className="font-medium text-gray-900 text-sm truncate" title={file.originalName}>
                {file.originalName}
              </h4>
              <div className="text-xs text-gray-500 space-y-1">
                <p>{formatFileSize(file.fileSize)}</p>
                <p>{new Date(file.uploadedAt).toLocaleString()}</p>
              </div>
            </div>

            {/* Actions */}
            <div className="flex items-center justify-between mt-4 pt-3 border-t border-gray-100">
              <div className="flex items-center space-x-2">
                {file.fileType.startsWith('image/') && (
                  <button
                    onClick={() => previewImage(file)}
                    className="text-blue-600 hover:text-blue-800 p-1"
                    title="Preview"
                  >
                    <Eye className="w-4 h-4" />
                  </button>
                )}
                <button
                  onClick={() => downloadFile(file)}
                  className="text-green-600 hover:text-green-800 p-1"
                  title="Download"
                >
                  <Download className="w-4 h-4" />
                </button>
              </div>
              
              {editable && (
                <button
                  className="text-red-600 hover:text-red-800 p-1"
                  title="Delete"
                >
                  <Trash2 className="w-4 h-4" />
                </button>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Image Preview Modal */}
      {previewFile && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
          <div className="max-w-4xl max-h-full p-4">
            <div className="bg-white rounded-lg overflow-hidden">
              <div className="flex items-center justify-between p-4 border-b">
                <h3 className="text-lg font-medium">{previewFile.originalName}</h3>
                <button
                  onClick={() => setPreviewFile(null)}
                  className="text-gray-500 hover:text-gray-700"
                >
                  <span className="sr-only">Close</span>
                  <svg className="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              <div className="p-4">
                <img
                  src={previewFile.filePath}
                  alt={previewFile.originalName}
                  className="max-w-full max-h-96 mx-auto"
                />
              </div>
              <div className="flex justify-end p-4 border-t space-x-2">
                <button
                  onClick={() => downloadFile(previewFile)}
                  className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 flex items-center"
                >
                  <Download className="w-4 h-4 mr-2" />
                  Download
                </button>
                <button
                  onClick={() => setPreviewFile(null)}
                  className="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ArtworkManager; 