'use client';

import { useState, useEffect } from 'react';
import {
  ExclamationTriangleIcon,
  InformationCircleIcon,
  CheckCircleIcon,
  XCircleIcon,
  BellIcon,
  XMarkIcon,
  ClockIcon,
  ShieldExclamationIcon,
  CurrencyDollarIcon,
  ServerIcon,
  DocumentTextIcon,
} from '@heroicons/react/24/outline';

// Types for alerts
interface Alert {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  timestamp: string;
  category: 'system' | 'orders' | 'security' | 'revenue' | 'users';
  actionUrl?: string;
  actionText?: string;
  dismissible: boolean;
  priority: 'low' | 'medium' | 'high' | 'critical';
}

interface DashboardAlertsProps {
  className?: string;
}

// Mock alerts data - replace with real API data
const mockAlerts: Alert[] = [
  {
    id: '1',
    type: 'warning',
    title: 'High Order Volume',
    message: 'You have 23 pending orders that need attention.',
    timestamp: '2024-01-20T10:30:00Z',
    category: 'orders',
    actionUrl: '/admin/orders?status=pending',
    actionText: 'View Orders',
    dismissible: true,
    priority: 'high',
  },
  {
    id: '2',
    type: 'info',
    title: 'Storage Usage Alert',
    message: 'Your storage usage is at 68%. Consider optimizing images.',
    timestamp: '2024-01-20T09:15:00Z',
    category: 'system',
    actionUrl: '/admin/settings/storage',
    actionText: 'Manage Storage',
    dismissible: true,
    priority: 'medium',
  },
  {
    id: '3',
    type: 'success',
    title: 'Revenue Milestone',
    message: 'Congratulations! You\'ve reached KSh 2.8M in total revenue.',
    timestamp: '2024-01-20T08:45:00Z',
    category: 'revenue',
    dismissible: true,
    priority: 'low',
  },
  {
    id: '4',
    type: 'error',
    title: 'Failed Login Attempts',
    message: '5 failed login attempts detected in the last hour.',
    timestamp: '2024-01-20T07:30:00Z',
    category: 'security',
    actionUrl: '/admin/activity-logs?filter=security',
    actionText: 'View Logs',
    dismissible: false,
    priority: 'critical',
  },
];

// Alert icon mapping
const alertIcons = {
  info: InformationCircleIcon,
  success: CheckCircleIcon,
  warning: ExclamationTriangleIcon,
  error: XCircleIcon,
};

// Alert colors
const alertColors = {
  info: {
    bg: 'bg-blue-50',
    border: 'border-blue-200',
    icon: 'text-blue-600',
    text: 'text-blue-800',
  },
  success: {
    bg: 'bg-green-50',
    border: 'border-green-200',
    icon: 'text-green-600',
    text: 'text-green-800',
  },
  warning: {
    bg: 'bg-yellow-50',
    border: 'border-yellow-200',
    icon: 'text-yellow-600',
    text: 'text-yellow-800',
  },
  error: {
    bg: 'bg-red-50',
    border: 'border-red-200',
    icon: 'text-red-600',
    text: 'text-red-800',
  },
};

// Category icons
const categoryIcons = {
  system: ServerIcon,
  orders: DocumentTextIcon,
  security: ShieldExclamationIcon,
  revenue: CurrencyDollarIcon,
  users: BellIcon,
};

export default function DashboardAlerts({ className = '' }: DashboardAlertsProps) {
  const [alerts, setAlerts] = useState<Alert[]>(mockAlerts);
  const [filter, setFilter] = useState<'all' | Alert['category']>('all');
  const [showDismissed, setShowDismissed] = useState(false);
  const [dismissedAlerts, setDismissedAlerts] = useState<string[]>([]);

  // Filter alerts based on current filter
  const filteredAlerts = alerts.filter(alert => {
    if (filter !== 'all' && alert.category !== filter) return false;
    if (!showDismissed && dismissedAlerts.includes(alert.id)) return false;
    return true;
  });

  // Sort alerts by priority and timestamp
  const sortedAlerts = filteredAlerts.sort((a, b) => {
    const priorityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
    const aPriority = priorityOrder[a.priority];
    const bPriority = priorityOrder[b.priority];
    
    if (aPriority !== bPriority) {
      return bPriority - aPriority;
    }
    
    return new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime();
  });

  // Dismiss alert
  const dismissAlert = (alertId: string) => {
    setDismissedAlerts(prev => [...prev, alertId]);
  };

  // Get time ago
  const getTimeAgo = (timestamp: string) => {
    const now = new Date();
    const alertTime = new Date(timestamp);
    const diffInMinutes = Math.floor((now.getTime() - alertTime.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  // Count alerts by category
  const alertCounts = alerts.reduce((counts, alert) => {
    if (!dismissedAlerts.includes(alert.id)) {
      counts[alert.category] = (counts[alert.category] || 0) + 1;
    }
    return counts;
  }, {} as Record<Alert['category'], number>);

  return (
    <div className={`bg-white rounded-xl shadow-sm border border-gray-100 p-6 ${className}`}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
        <div className="flex items-center mb-4 sm:mb-0">
          <BellIcon className="h-6 w-6 text-gray-700 mr-3" />
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Alerts & Notifications</h3>
            <p className="text-sm text-gray-600">
              {sortedAlerts.length} active alerts
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <label className="flex items-center text-sm text-gray-600">
            <input
              type="checkbox"
              checked={showDismissed}
              onChange={(e) => setShowDismissed(e.target.checked)}
              className="mr-2 text-blue-600 focus:ring-blue-500"
            />
            Show dismissed
          </label>
        </div>
      </div>

      {/* Filter Tabs */}
      <div className="flex flex-wrap gap-2 mb-6 border-b border-gray-200 pb-4">
        <button
          onClick={() => setFilter('all')}
          className={`px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
            filter === 'all'
              ? 'bg-blue-100 text-blue-700 border border-blue-200'
              : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
          }`}
        >
          All ({sortedAlerts.length})
        </button>
        
        {(['system', 'orders', 'security', 'revenue', 'users'] as Alert['category'][]).map((category) => {
          const CategoryIcon = categoryIcons[category];
          const count = alertCounts[category] || 0;
          
          return (
            <button
              key={category}
              onClick={() => setFilter(category)}
              className={`px-3 py-2 text-sm font-medium rounded-lg transition-colors flex items-center ${
                filter === category
                  ? 'bg-blue-100 text-blue-700 border border-blue-200'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
              }`}
            >
              <CategoryIcon className="h-4 w-4 mr-1" />
              {category.charAt(0).toUpperCase() + category.slice(1)} ({count})
            </button>
          );
        })}
      </div>

      {/* Alerts List */}
      <div className="space-y-4">
        {sortedAlerts.length === 0 ? (
          <div className="text-center py-8">
            <BellIcon className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <p className="text-gray-500">No alerts to show</p>
            <p className="text-sm text-gray-400 mt-1">
              {filter !== 'all' ? `No ${filter} alerts` : 'All clear!'}
            </p>
          </div>
        ) : (
          sortedAlerts.map((alert) => {
            const IconComponent = alertIcons[alert.type];
            const colors = alertColors[alert.type];
            const isDismissed = dismissedAlerts.includes(alert.id);
            
            return (
              <div
                key={alert.id}
                className={`p-4 rounded-lg border ${colors.bg} ${colors.border} ${
                  isDismissed ? 'opacity-50' : ''
                } transition-opacity`}
              >
                <div className="flex items-start">
                  <div className="flex-shrink-0">
                    <IconComponent className={`h-5 w-5 ${colors.icon}`} />
                  </div>
                  
                  <div className="ml-3 flex-1 min-w-0">
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <h4 className={`text-sm font-medium ${colors.text}`}>
                          {alert.title}
                          {alert.priority === 'critical' && (
                            <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
                              Critical
                            </span>
                          )}
                          {alert.priority === 'high' && (
                            <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-orange-100 text-orange-800">
                              High
                            </span>
                          )}
                        </h4>
                        <p className={`text-sm ${colors.text} mt-1 opacity-90`}>
                          {alert.message}
                        </p>
                        
                        <div className="flex items-center mt-2 space-x-4">
                          <div className="flex items-center text-xs text-gray-500">
                            <ClockIcon className="h-3 w-3 mr-1" />
                            {getTimeAgo(alert.timestamp)}
                          </div>
                          
                          {alert.actionUrl && (
                            <a
                              href={alert.actionUrl}
                              className={`text-xs font-medium hover:underline ${colors.text}`}
                            >
                              {alert.actionText || 'Learn More'}
                            </a>
                          )}
                        </div>
                      </div>
                      
                      {alert.dismissible && !isDismissed && (
                        <button
                          onClick={() => dismissAlert(alert.id)}
                          className={`ml-4 flex-shrink-0 ${colors.text} hover:opacity-75`}
                        >
                          <XMarkIcon className="h-4 w-4" />
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            );
          })
        )}
      </div>
      
      {/* Footer */}
      {sortedAlerts.length > 0 && (
        <div className="mt-6 pt-4 border-t border-gray-200 text-center">
          <button className="text-sm text-blue-600 hover:text-blue-700 font-medium">
            View All Notifications
          </button>
        </div>
      )}
    </div>
  );
} 