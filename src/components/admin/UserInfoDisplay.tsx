import React from 'react';
import {
  UserIcon,
  ShieldCheckIcon,
  ClockIcon,
  CalendarIcon,
  InformationCircleIcon,
  IdentificationIcon,
  CogIcon,
  CheckCircleIcon,
  XCircleIcon,
} from '@heroicons/react/24/outline';
import { ShieldCheckIcon as ShieldCheckSolidIcon, CheckCircleIcon as CheckCircleSolidIcon } from '@heroicons/react/24/solid';
import { format } from 'date-fns';
import { User, Role, ActivityLog } from '@/types/user';

interface UserOverviewProps {
  user: User;
}

export function UserOverview({ user }: UserOverviewProps) {
  const formatLastLogin = (lastLogin?: string | null) => {
    if (!lastLogin) return 'Never';
    const date = new Date(lastLogin);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);
    
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${Math.floor(diffInHours)}h ago`;
    if (diffInHours < 168) return `${Math.floor(diffInHours / 24)}d ago`;
    return date.toLocaleDateString();
  };

  const getRoleColor = (roleName: string) => {
    const name = roleName.toLowerCase();
    if (name === 'admin' || name === 'administrator') return 'bg-red-100 text-red-800';
    if (name === 'editor' || name === 'manager') return 'bg-blue-100 text-blue-800';
    if (name === 'viewer' || name === 'user') return 'bg-green-100 text-green-800';
    return 'bg-gray-100 text-gray-800';
  };

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-xl font-semibold text-gray-900 flex items-center gap-2">
          <InformationCircleIcon className="h-6 w-6 text-blue-500" />
          User Overview
        </h2>
        <div className="flex items-center gap-2">
          {user.active ? (
            <CheckCircleIcon className="h-6 w-6 text-green-500" />
          ) : (
            <XCircleIcon className="h-6 w-6 text-red-500" />
          )}
          <span className={`text-sm font-medium ${user.active ? 'text-green-700' : 'text-red-700'}`}>
            {user.active ? 'Active Account' : 'Inactive Account'}
          </span>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-xl">
          <IdentificationIcon className="h-8 w-8 text-blue-500" />
          <div>
            <p className="text-sm text-gray-600">User ID</p>
            <p className="font-mono text-sm text-gray-900" title={user.id}>
              {user.id.slice(0, 8)}...
            </p>
          </div>
        </div>
        
        <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-xl">
          <CalendarIcon className="h-8 w-8 text-green-500" />
          <div>
            <p className="text-sm text-gray-600">Created</p>
            <p className="text-sm font-medium text-gray-900">
              {format(new Date(user.createdAt), 'MMM d, yyyy')}
            </p>
          </div>
        </div>
        
        <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-xl">
          <ClockIcon className="h-8 w-8 text-orange-500" />
          <div>
            <p className="text-sm text-gray-600">Last Login</p>
            <p className="text-sm font-medium text-gray-900">
              {formatLastLogin(user.lastLogin)}
            </p>
          </div>
        </div>
        
        <div className="flex items-center gap-3 p-4 bg-gray-50 rounded-xl">
          <CogIcon className="h-8 w-8 text-purple-500" />
          <div>
            <p className="text-sm text-gray-600">Last Updated</p>
            <p className="text-sm font-medium text-gray-900">
              {format(new Date(user.updatedAt), 'MMM d, yyyy')}
            </p>
          </div>
        </div>
      </div>

      {/* Role Badge */}
      <div className="mt-6 pt-6 border-t border-gray-200">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium text-gray-600">Current Role</span>
          <span className={`inline-flex items-center gap-2 px-3 py-1 rounded-full text-sm font-medium ${getRoleColor(user.role.name)}`}>
            {user.role.name.toLowerCase() === 'admin' && <ShieldCheckSolidIcon className="h-4 w-4" />}
            {user.role.name}
          </span>
        </div>
        {user.role.description && (
          <p className="text-xs text-gray-500 mt-1">{user.role.description}</p>
        )}
      </div>
    </div>
  );
}

interface RolePermissionsProps {
  role: Role;
}

export function RolePermissions({ role }: RolePermissionsProps) {
  const getRoleColor = (roleName: string) => {
    const name = roleName.toLowerCase();
    if (name === 'admin' || name === 'administrator') return 'bg-red-100 text-red-800';
    if (name === 'editor' || name === 'manager') return 'bg-blue-100 text-blue-800';
    if (name === 'viewer' || name === 'user') return 'bg-green-100 text-green-800';
    return 'bg-gray-100 text-gray-800';
  };

  return (
    <div>
      <h3 className="text-lg font-semibold text-gray-900 mb-6 flex items-center gap-2">
        <ShieldCheckIcon className="h-6 w-6 text-green-500" />
        Role Permissions
      </h3>
      <div className="bg-gray-50 rounded-xl p-6">
        <div className="flex items-center justify-between mb-4">
          <span className={`inline-flex items-center gap-2 px-3 py-1 rounded-full text-sm font-medium ${getRoleColor(role.name)}`}>
            {role.name.toLowerCase() === 'admin' && <ShieldCheckSolidIcon className="h-4 w-4" />}
            {role.name}
          </span>
          <span className="text-sm text-gray-600">
            {role.permissions.length} permissions
          </span>
        </div>
        {role.description && (
          <p className="text-sm text-gray-600 mb-4">{role.description}</p>
        )}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
          {role.permissions.map((permission) => (
            <div key={permission} className="flex items-center gap-2 p-2 bg-white rounded-lg">
              <CheckCircleSolidIcon className="h-4 w-4 text-green-500" />
              <span className="text-sm text-gray-700">{permission}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

interface ActivityLogsProps {
  activityLogs: ActivityLog[];
}

export function ActivityLogs({ activityLogs }: ActivityLogsProps) {
  return (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
        <ClockIcon className="h-6 w-6 text-orange-500" />
        Recent Activity
      </h3>
      
      {activityLogs.length === 0 ? (
        <div className="text-center py-12">
          <ClockIcon className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h4 className="text-lg font-semibold text-gray-900 mb-2">No Activity Found</h4>
          <p className="text-gray-600">No recent activity logs available for this user.</p>
        </div>
      ) : (
        <div className="space-y-4">
          {activityLogs.map((log) => (
            <div key={log.id} className="flex items-start gap-4 p-4 bg-gray-50 rounded-xl">
              <div className="flex-shrink-0 w-2 h-2 bg-orange-500 rounded-full mt-2"></div>
              <div className="flex-1">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium text-gray-900">{log.action}</h4>
                  <span className="text-sm text-gray-500">
                    {format(new Date(log.createdAt), 'MMM d, yyyy HH:mm')}
                  </span>
                </div>
                <p className="text-sm text-gray-600 mt-1">{log.details}</p>
                {log.ipAddress && (
                  <p className="text-xs text-gray-500 mt-1">IP: {log.ipAddress}</p>
                )}
                {log.userAgent && (
                  <p className="text-xs text-gray-500 mt-1" title={log.userAgent}>
                    User Agent: {log.userAgent.length > 50 ? `${log.userAgent.slice(0, 50)}...` : log.userAgent}
                  </p>
                )}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

interface UserHeaderProps {
  user: User;
  hasChanges?: boolean;
}

export function UserHeader({ user, hasChanges = false }: UserHeaderProps) {
  const getRoleColor = (roleName: string) => {
    const name = roleName.toLowerCase();
    if (name === 'admin' || name === 'administrator') return 'bg-red-100 text-red-800';
    if (name === 'editor' || name === 'manager') return 'bg-blue-100 text-blue-800';
    if (name === 'viewer' || name === 'user') return 'bg-green-100 text-green-800';
    return 'bg-gray-100 text-gray-800';
  };

  return (
    <div className="flex items-center gap-4">
      <div className="h-12 w-12 rounded-full bg-gradient-to-br from-orange-400 to-red-500 flex items-center justify-center">
        <span className="text-lg font-semibold text-white">
          {(user.name || user.username).charAt(0).toUpperCase()}
        </span>
      </div>
      <div>
        <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
          Edit User
          <span className={`inline-flex items-center gap-1 px-3 py-1 rounded-full text-sm font-medium ${getRoleColor(user.role.name)}`}>
            {user.role.name.toLowerCase() === 'admin' && <ShieldCheckSolidIcon className="h-4 w-4" />}
            {user.role.name}
          </span>
        </h1>
        <p className="text-gray-600 mt-1">
          Manage {user.name || user.username}'s account settings and permissions
        </p>
        {hasChanges && (
          <div className="flex items-center gap-2 mt-2">
            <div className="w-2 h-2 bg-amber-500 rounded-full animate-pulse"></div>
            <span className="text-sm text-amber-700 font-medium">You have unsaved changes</span>
          </div>
        )}
      </div>
    </div>
  );
} 