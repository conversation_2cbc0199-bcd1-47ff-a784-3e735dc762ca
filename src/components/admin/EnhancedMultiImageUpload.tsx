'use client';

import React, { useState, useCallback, useRef } from 'react';
import { 
  PhotoIcon, 
  XMarkIcon, 
  ArrowUpTrayIcon, 
  StarIcon,
  EyeIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  ClockIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';
import { chunkedUploadService, UploadProgress } from '../../services/chunkedUploadService';
import { detectConnectionSpeed, ConnectionSpeed } from '../../utils/connectionUtils';
import { shouldCompressFile } from '../../utils/imageCompressionUtils';

interface ImageFile {
  id: string;
  file: File | null;
  preview: string;
  uploading: boolean;
  uploaded: boolean;
  error: string | null;
  progress: UploadProgress | null;
  url?: string;
  isFeatured: boolean;
  resumable?: boolean;
  uploadId?: string;
}

interface EnhancedMultiImageUploadProps {
  maxImages?: number;
  maxFileSize?: number;
  acceptedTypes?: string[];
  onImagesChange: (images: { url: string; isFeatured: boolean }[]) => void;
  initialImages?: { url: string; isFeatured: boolean }[];
  category?: string;
}

export default function EnhancedMultiImageUpload({
  maxImages = 10,
  maxFileSize = 200, // 200MB for chunked uploads
  acceptedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif', 'image/avif'],
  onImagesChange,
  initialImages = [],
  category = 'catalogue'
}: EnhancedMultiImageUploadProps) {
  const [images, setImages] = useState<ImageFile[]>([]);
  const [dragActive, setDragActive] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [connectionSpeed, setConnectionSpeed] = useState<ConnectionSpeed | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Detect connection speed on mount
  React.useEffect(() => {
    detectConnectionSpeed().then(connectionInfo => {
      setConnectionSpeed(connectionInfo.speed);
    });
  }, []);

  // Initialize with existing images
  React.useEffect(() => {
    if (initialImages.length > 0 && images.length === 0) {
      const existingImages: ImageFile[] = initialImages.map((img, index) => ({
        id: `existing-${index}`,
        file: null,
        preview: img.url,
        uploading: false,
        uploaded: true,
        error: null,
        progress: null,
        url: img.url,
        isFeatured: img.isFeatured
      }));
      setImages(existingImages);
    }
  }, [initialImages.length, images.length]);

  // Update parent when images change
  React.useEffect(() => {
    const uploadedImages = images
      .filter(img => img.uploaded && img.url)
      .map(img => ({ url: img.url!, isFeatured: img.isFeatured }));
    onImagesChange(uploadedImages);
  }, [images, onImagesChange]);

  const validateFile = (file: File): string | null => {
    if (!acceptedTypes.includes(file.type)) {
      return `File type ${file.type} not supported. Supported: ${acceptedTypes.join(', ')}`;
    }
    if (file.size > maxFileSize * 1024 * 1024) {
      return `File size must be less than ${maxFileSize}MB`;
    }
    return null;
  };

  const createImageFile = (file: File): ImageFile => ({
    id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    file,
    preview: URL.createObjectURL(file),
    uploading: false,
    uploaded: false,
    error: null,
    progress: null,
    isFeatured: false
  });

  const handleFiles = useCallback((files: FileList | File[]) => {
    const fileArray = Array.from(files);
    const validFiles: ImageFile[] = [];
    const errors: string[] = [];

    fileArray.forEach(file => {
      const error = validateFile(file);
      if (error) {
        errors.push(`${file.name}: ${error}`);
      } else if (images.length + validFiles.length < maxImages) {
        validFiles.push(createImageFile(file));
      } else {
        errors.push(`Maximum ${maxImages} images allowed`);
      }
    });

    if (errors.length > 0) {
      alert(errors.join('\n'));
    }

    if (validFiles.length > 0) {
      setImages(prev => [...prev, ...validFiles]);
    }
  }, [images.length, maxImages]);

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      handleFiles(e.dataTransfer.files);
    }
  }, [handleFiles]);

  const handleFileInput = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      handleFiles(e.target.files);
    }
  }, [handleFiles]);

  const removeImage = useCallback((id: string) => {
    setImages(prev => {
      const imageToRemove = prev.find(img => img.id === id);
      
      // Cancel upload if in progress
      if (imageToRemove?.uploading && imageToRemove.uploadId) {
        chunkedUploadService.cancelUpload(imageToRemove.uploadId);
      }
      
      const updated = prev.filter(img => img.id !== id);
      
      // If removed image was featured, make first image featured
      if (imageToRemove?.isFeatured && updated.length > 0) {
        updated[0].isFeatured = true;
      }
      
      return updated;
    });
  }, []);

  const setFeaturedImage = useCallback((id: string) => {
    setImages(prev => prev.map(img => ({
      ...img,
      isFeatured: img.id === id
    })));
  }, []);

  const uploadSingleImage = async (imageId: string) => {
    const image = images.find(img => img.id === imageId);
    if (!image || !image.file || image.uploaded || image.uploading) return;

    setImages(prev => prev.map(img => 
      img.id === imageId ? { ...img, uploading: true, error: null } : img
    ));

    try {
      const result = await chunkedUploadService.startUpload(
        image.file,
        (progress) => {
          setImages(prev => prev.map(img => 
            img.id === imageId ? { 
              ...img, 
              progress,
              uploadId: progress.uploadId
            } : img
          ));
        },
        {
          endpoint: '/api/upload',
          metadata: { folder: category }
        }
      );

      if (result.success && result.data) {
        setImages(prev => prev.map(img => 
          img.id === imageId ? { 
            ...img, 
            uploading: false, 
            uploaded: true, 
            url: result.data.url,
            progress: null,
            error: null
          } : img
        ));
      } else {
        throw new Error(result.error || 'Upload failed');
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Upload failed';
      setImages(prev => prev.map(img => 
        img.id === imageId ? { 
          ...img, 
          uploading: false, 
          error: errorMessage,
          progress: null,
          resumable: true
        } : img
      ));
    }
  };

  const uploadAllImages = async () => {
    const imagesToUpload = images.filter(img => !img.uploaded && img.file && !img.uploading);
    if (imagesToUpload.length === 0) return;

    setUploading(true);

    try {
      // Upload images sequentially to avoid overwhelming the server
      for (const image of imagesToUpload) {
        await uploadSingleImage(image.id);
      }
    } finally {
      setUploading(false);
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getProgressColor = (stage: string): string => {
    switch (stage) {
      case 'preparing': return 'bg-blue-500';
      case 'compressing': return 'bg-orange-500';
      case 'uploading': return 'bg-green-500';
      case 'finalizing': return 'bg-purple-500';
      case 'complete': return 'bg-green-600';
      case 'error': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  const pendingUploads = images.filter(img => !img.uploaded && img.file).length;
  const uploadedCount = images.filter(img => img.uploaded).length;
  const failedUploads = images.filter(img => img.error && !img.uploading).length;

  return (
    <div className="w-full">
      {/* Connection Status */}
      {connectionSpeed && (
        <div className="mb-4 p-3 bg-gray-50 rounded-lg">
          <div className="flex items-center justify-between text-sm">
            <div className="flex items-center space-x-2">
              <span className="font-medium">Connection:</span>
              <span className={`px-2 py-1 rounded text-xs ${
                connectionSpeed === 'fast' ? 'bg-green-100 text-green-800' :
                connectionSpeed === 'slow' ? 'bg-yellow-100 text-yellow-800' :
                'bg-red-100 text-red-800'
              }`}>
                {connectionSpeed.toUpperCase()}
              </span>
            </div>
            <div className="text-gray-600">
              Max file size: {maxFileSize}MB
            </div>
          </div>
        </div>
      )}

      {/* Upload Area */}
      <div
        className={`relative border-2 border-dashed rounded-lg p-6 transition-colors ${
          dragActive 
            ? 'border-blue-400 bg-blue-50' 
            : 'border-gray-300 hover:border-gray-400'
        }`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept={acceptedTypes.join(',')}
          onChange={handleFileInput}
          className="hidden"
        />

        <div className="text-center">
          <PhotoIcon className="mx-auto h-12 w-12 text-gray-400" />
          <div className="mt-4">
            <button
              type="button"
              onClick={() => fileInputRef.current?.click()}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <ArrowUpTrayIcon className="h-4 w-4 mr-2" />
              Select Images
            </button>
          </div>
          <p className="mt-2 text-sm text-gray-500">
            or drag and drop images here
          </p>
          <p className="text-xs text-gray-400 mt-1">
            Supports: {acceptedTypes.map(type => type.split('/')[1]).join(', ')} • Max {maxFileSize}MB each • Up to {maxImages} images
          </p>
        </div>
      </div>

      {/* Upload Status */}
      {images.length > 0 && (
        <div className="mt-4 p-3 bg-gray-50 rounded-lg">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600">
              {uploadedCount}/{images.length} uploaded
              {pendingUploads > 0 && ` • ${pendingUploads} pending`}
              {failedUploads > 0 && ` • ${failedUploads} failed`}
            </div>
            {pendingUploads > 0 && (
              <button
                onClick={uploadAllImages}
                disabled={uploading}
                className="inline-flex items-center px-3 py-1 border border-transparent text-xs font-medium rounded text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
              >
                {uploading ? (
                  <>
                    <ArrowPathIcon className="h-3 w-3 mr-1 animate-spin" />
                    Uploading...
                  </>
                ) : (
                  <>
                    <ArrowUpTrayIcon className="h-3 w-3 mr-1" />
                    Upload All
                  </>
                )}
              </button>
            )}
          </div>
        </div>
      )}

      {/* Images Grid */}
      {images.length > 0 && (
        <div className="mt-4 grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {images.map((image) => (
            <div key={image.id} className="relative group">
              <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden">
                <img
                  src={image.preview}
                  alt="Preview"
                  className="w-full h-full object-cover"
                />

                {/* Upload Progress Overlay */}
                {image.progress && (
                  <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                    <div className="bg-white rounded-lg p-3 max-w-xs w-full mx-2">
                      <div className="text-xs font-medium text-center mb-2 capitalize">
                        {image.progress.stage.replace('-', ' ')}
                      </div>
                      
                      <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                        <div
                          className={`h-2 rounded-full transition-all duration-300 ${getProgressColor(image.progress.stage)}`}
                          style={{ width: `${image.progress.percentage}%` }}
                        />
                      </div>
                      
                      <div className="flex justify-between text-xs text-gray-600">
                        <span>{image.progress.percentage}%</span>
                        {image.progress.totalChunks > 1 && (
                          <span>{image.progress.chunksCompleted}/{image.progress.totalChunks} chunks</span>
                        )}
                      </div>
                    </div>
                  </div>
                )}

                {/* Status Icons */}
                <div className="absolute top-2 right-2 flex space-x-1">
                  {image.uploaded && (
                    <CheckCircleIcon className="h-5 w-5 text-green-500 bg-white rounded-full" />
                  )}
                  {image.error && !image.uploading && (
                    <ExclamationTriangleIcon className="h-5 w-5 text-red-500 bg-white rounded-full" />
                  )}
                  {image.resumable && (
                    <ClockIcon className="h-5 w-5 text-orange-500 bg-white rounded-full" />
                  )}
                </div>

                {/* Featured Star */}
                <button
                  onClick={() => setFeaturedImage(image.id)}
                  className="absolute top-2 left-2 p-1 rounded-full bg-white shadow-sm hover:bg-gray-50"
                  title={image.isFeatured ? "Featured image" : "Set as featured"}
                >
                  {image.isFeatured ? (
                    <StarIconSolid className="h-4 w-4 text-yellow-500" />
                  ) : (
                    <StarIcon className="h-4 w-4 text-gray-400" />
                  )}
                </button>

                {/* Remove Button */}
                <button
                  onClick={() => removeImage(image.id)}
                  className="absolute bottom-2 right-2 p-1 rounded-full bg-red-500 text-white hover:bg-red-600 opacity-0 group-hover:opacity-100 transition-opacity"
                  title="Remove image"
                >
                  <XMarkIcon className="h-4 w-4" />
                </button>

                {/* Action Buttons */}
                <div className="absolute bottom-2 left-2 flex space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                  {!image.uploaded && !image.uploading && image.file && (
                    <button
                      onClick={() => uploadSingleImage(image.id)}
                      className="p-1 rounded-full bg-blue-500 text-white hover:bg-blue-600 text-xs"
                      title="Upload this image"
                    >
                      <ArrowUpTrayIcon className="h-3 w-3" />
                    </button>
                  )}
                  
                  {image.uploaded && image.url && (
                    <a
                      href={image.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="p-1 rounded-full bg-green-500 text-white hover:bg-green-600 text-xs"
                      title="View uploaded image"
                    >
                      <EyeIcon className="h-3 w-3" />
                    </a>
                  )}
                </div>
              </div>

              {/* Image Info */}
              <div className="mt-2 text-xs text-gray-600">
                <div className="truncate font-medium">
                  {image.file?.name || 'Existing image'}
                </div>
                <div className="flex justify-between items-center">
                  <span>{image.file ? formatFileSize(image.file.size) : ''}</span>
                  {connectionSpeed && image.file && shouldCompressFile(image.file, connectionSpeed) && (
                    <span className="text-blue-600">Will compress</span>
                  )}
                </div>
                {image.error && (
                  <div className="text-red-600 text-xs mt-1 break-words">
                    {image.error}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Help Text */}
      {images.length === 0 && (
        <div className="mt-4 text-center text-sm text-gray-500">
          <p>No images selected. Choose images to upload for your catalogue item.</p>
          <p className="text-xs mt-1">
            The first image will be featured by default. Click the star to change featured image.
          </p>
        </div>
      )}
    </div>
  );
} 