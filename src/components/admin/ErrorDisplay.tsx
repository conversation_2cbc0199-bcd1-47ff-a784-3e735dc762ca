'use client';

import React from 'react';
import { 
  ExclamationTriangleIcon, 
  ArrowPathIcon,
  ExclamationCircleIcon 
} from '@heroicons/react/24/outline';
import { AppError } from '@/utils/errorHandling';

interface ErrorDisplayProps {
  error: AppError | string | null;
  title?: string;
  onRetry?: (() => void) | undefined;
  retryText?: string;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  showDetails?: boolean;
}

export default function ErrorDisplay({
  error,
  title = 'Error',
  onRetry,
  retryText = 'Try Again',
  className = '',
  size = 'md',
  showDetails = false
}: ErrorDisplayProps) {
  if (!error) return null;

  const errorMessage = typeof error === 'string' 
    ? error 
    : error.message || 'An unexpected error occurred';

  const errorDetails = typeof error === 'object' && error.details 
    ? error.details 
    : null;

  const isRetryable = typeof error === 'object' 
    ? (!error.statusCode || (error.statusCode >= 500 && error.statusCode < 600))
    : true;

  const sizeClasses = {
    sm: {
      container: 'p-4 max-w-sm',
      icon: 'h-8 w-8',
      title: 'text-base font-medium',
      message: 'text-sm',
      button: 'px-3 py-1.5 text-sm'
    },
    md: {
      container: 'p-6 max-w-md',
      icon: 'h-12 w-12',
      title: 'text-lg font-semibold',
      message: 'text-sm',
      button: 'px-4 py-2 text-sm'
    },
    lg: {
      container: 'p-8 max-w-lg',
      icon: 'h-16 w-16',
      title: 'text-xl font-semibold',
      message: 'text-base',
      button: 'px-6 py-3 text-base'
    }
  };

  const classes = sizeClasses[size];

  return (
    <div className={`text-center ${className}`}>
      <div className={`bg-red-50 border border-red-200 rounded-xl ${classes.container} mx-auto`}>
        <ExclamationTriangleIcon className={`${classes.icon} text-red-500 mx-auto mb-4`} />
        
        <h3 className={`${classes.title} text-red-900 mb-2`}>
          {title}
        </h3>
        
        <p className={`${classes.message} text-red-700 mb-4`}>
          {errorMessage}
        </p>
        
        {showDetails && errorDetails && (
          <details className="mb-4 text-left">
            <summary className="cursor-pointer text-xs text-red-600 hover:text-red-700 font-medium">
              Show Details
            </summary>
            <div className="mt-2 p-3 bg-red-100 rounded text-xs text-red-800 font-mono overflow-auto max-h-32">
              {typeof errorDetails === 'string' ? errorDetails : JSON.stringify(errorDetails, null, 2)}
            </div>
          </details>
        )}
        
        {onRetry && isRetryable && (
          <button
            onClick={onRetry}
            className={`${classes.button} bg-red-600 text-white rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 transition-colors duration-200 inline-flex items-center`}
          >
            <ArrowPathIcon className="w-4 h-4 mr-2" />
            {retryText}
          </button>
        )}
      </div>
    </div>
  );
}

// Specialized error displays for common scenarios
export function LoadingError({ 
  message = 'Failed to load data', 
  onRetry,
  className = '' 
}: {
  message?: string;
  onRetry?: () => void;
  className?: string;
}) {
  return (
    <ErrorDisplay
      error={message}
      title="Loading Error"
      onRetry={onRetry}
      className={className}
      size="md"
    />
  );
}

export function FormError({ 
  error,
  onDismiss,
  className = '' 
}: {
  error: AppError | string | null;
  onDismiss?: () => void;
  className?: string;
}) {
  if (!error) return null;

  const errorMessage = typeof error === 'string' 
    ? error 
    : error.message || 'Form submission failed';

  return (
    <div className={`bg-red-50 border border-red-200 rounded-lg p-4 ${className}`}>
      <div className="flex">
        <ExclamationCircleIcon className="h-5 w-5 text-red-400 flex-shrink-0" />
        <div className="ml-3 flex-1">
          <p className="text-sm text-red-700">{errorMessage}</p>
        </div>
        {onDismiss && (
          <button
            onClick={onDismiss}
            className="ml-4 text-red-400 hover:text-red-500"
            aria-label="Dismiss error"
          >
            <span className="sr-only">Dismiss</span>
            <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </button>
        )}
      </div>
    </div>
  );
}

export function NetworkError({ 
  onRetry,
  className = '' 
}: {
  onRetry?: () => void;
  className?: string;
}) {
  return (
    <ErrorDisplay
      error="Network connection failed. Please check your internet connection and try again."
      title="Connection Error"
      onRetry={onRetry}
      retryText="Retry Connection"
      className={className}
      size="md"
    />
  );
} 