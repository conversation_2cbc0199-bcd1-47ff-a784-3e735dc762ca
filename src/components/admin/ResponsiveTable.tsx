'use client';

import { ReactNode } from 'react';

interface Column {
  key: string;
  header: string;
  className?: string;
  render?: (item: any) => ReactNode;
}

interface ResponsiveTableProps {
  columns: Column[];
  data: any[];
  mobileCardComponent?: (item: any) => ReactNode;
  loading?: boolean;
  emptyState?: ReactNode;
  className?: string;
}

interface ResponsiveTableSkeletonProps {
  columns: number;
  rows?: number;
}

function ResponsiveTableSkeleton({ columns, rows = 5 }: ResponsiveTableSkeletonProps) {
  return (
    <div className="admin-table-mobile-fallback bg-white shadow-sm rounded-lg overflow-hidden">
      <div className="admin-table-container">
        <table className="admin-table">
          <thead className="bg-gray-50">
            <tr>
              {Array.from({ length: columns }).map((_, i) => (
                <th key={i} className="px-6 py-3">
                  <div className="admin-skeleton-text w-20"></div>
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {Array.from({ length: rows }).map((_, i) => (
              <tr key={i}>
                {Array.from({ length: columns }).map((_, j) => (
                  <td key={j} className="px-6 py-4">
                    <div className="admin-skeleton-text w-full max-w-xs"></div>
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
}

function DefaultMobileCard({ item, columns }: { item: any; columns: Column[] }) {
  return (
    <div className="admin-mobile-card">
      <div className="admin-mobile-card-header">
        <h3 className="admin-mobile-card-title">
          {item.title || item.name || item.id}
        </h3>
      </div>
      <div className="admin-mobile-card-content">
        {columns.slice(0, -1).map((column) => (
          <p key={column.key}>
            <span className="font-medium">{column.header}:</span>{' '}
            {column.render ? column.render(item) : item[column.key]}
          </p>
        ))}
      </div>
      {/* Actions would be handled by the parent component */}
    </div>
  );
}

export default function ResponsiveTable({
  columns,
  data,
  mobileCardComponent,
  loading = false,
  emptyState,
  className = ''
}: ResponsiveTableProps) {
  if (loading) {
    return <ResponsiveTableSkeleton columns={columns.length} />;
  }

  if (data.length === 0 && emptyState) {
    return <div className={className}>{emptyState}</div>;
  }

  return (
    <div className={className}>
      {/* Desktop Table View */}
      <div className="admin-table-mobile-fallback bg-white shadow-sm rounded-lg overflow-hidden">
        <div className="admin-table-container">
          <table className="admin-table">
            <thead className="bg-gray-50">
              <tr>
                {columns.map((column) => (
                  <th
                    key={column.key}
                    className={`px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ${
                      column.className || ''
                    }`}
                  >
                    {column.header}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {data.map((item, index) => (
                <tr key={item.id || index} className="hover:bg-gray-50 transition-colors duration-200">
                  {columns.map((column) => (
                    <td
                      key={column.key}
                      className={`px-6 py-4 whitespace-nowrap ${column.className || ''}`}
                    >
                      {column.render ? column.render(item) : item[column.key]}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Mobile Card View */}
      <div className="admin-mobile-cards">
        {data.map((item, index) => (
          <div key={item.id || index}>
            {mobileCardComponent ? (
              mobileCardComponent(item)
            ) : (
              <DefaultMobileCard item={item} columns={columns} />
            )}
          </div>
        ))}
      </div>
    </div>
  );
}

// Export helper components for reuse
export { ResponsiveTableSkeleton, DefaultMobileCard };
export type { Column, ResponsiveTableProps }; 