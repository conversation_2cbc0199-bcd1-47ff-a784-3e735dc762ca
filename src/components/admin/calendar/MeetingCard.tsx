'use client';

import React from 'react';
import { 
  Users, 
  Phone, 
  AlertTriangle, 
  Clock, 
  Calendar as CalendarIcon,
  Video,
  Webcam,
  Monitor,
  Smartphone,
  Link2,
  Copy,
  ExternalLink,
  MoreVertical,
  Edit,
  Trash2,
  Eye
} from 'lucide-react';
import { 
  CalendarEvent, 
  MeetingCardProps, 
  statusColors, 
  eventTypeColors,
  meetingPlatforms
} from '@/types/calendar';
import { 
  formatTime, 
  formatDateTime, 
  getEventDuration, 
  getClientName,
  copyToClipboard
} from '@/utils/calendarUtils';

const typeIcons = {
  meeting: Users,
  call: Phone,
  deadline: AlertTriangle,
  reminder: Clock,
  appointment: CalendarIcon,
};

const platformIcons = {
  zoom: Video,
  'google-meet': Webcam,
  teams: Monitor,
  webex: Smartphone,
  custom: Link2,
};

export default function MeetingCard({ 
  event, 
  className = '',
  onEdit,
  onDelete,
  onView
}: MeetingCardProps) {
  const [showDropdown, setShowDropdown] = React.useState(false);
  
  const TypeIcon = typeIcons[event.type];
  const platform = event.meetingPlatform ? meetingPlatforms[event.meetingPlatform] : null;
  const PlatformIcon = event.meetingPlatform ? platformIcons[event.meetingPlatform] : null;
  
  const duration = getEventDuration(event.startTime, event.endTime);
  const clientName = getClientName(event.client);

  const handleCopyMeetingLink = async (e: React.MouseEvent) => {
    e.stopPropagation();
    if (event.meetingLink) {
      const success = await copyToClipboard(event.meetingLink);
      if (success) {
        // You might want to show a toast notification here
        console.log('Meeting link copied to clipboard');
      }
    }
  };

  const handleJoinMeeting = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (event.meetingLink) {
      window.open(event.meetingLink, '_blank');
    }
  };

  const handleDropdownAction = (action: string, e: React.MouseEvent) => {
    e.stopPropagation();
    setShowDropdown(false);
    
    switch (action) {
      case 'view':
        onView?.(event);
        break;
      case 'edit':
        onEdit?.(event);
        break;
      case 'delete':
        onDelete?.(event.id);
        break;
    }
  };

  return (
    <div 
      className={`relative bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200 cursor-pointer group ${className}`}
      onClick={() => onView?.(event)}
    >
      {/* Event header */}
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center space-x-2 min-w-0 flex-1">
          <div className={`flex-shrink-0 p-1.5 rounded-lg ${eventTypeColors[event.type]}`}>
            <TypeIcon className="w-4 h-4" />
          </div>
          <div className="min-w-0 flex-1">
            <h3 className="font-medium text-gray-900 truncate text-sm">
              {event.title}
            </h3>
            <div className="flex items-center space-x-2 mt-1">
              <span className={`px-2 py-0.5 text-xs font-medium rounded-full ${statusColors[event.status]}`}>
                {event.status}
              </span>
              {event.isVirtual && PlatformIcon && (
                <div className={`flex items-center space-x-1 px-2 py-0.5 rounded-full text-xs ${platform?.bgColor} ${platform?.textColor}`}>
                  <PlatformIcon className="w-3 h-3" />
                  <span>{platform?.name}</span>
                </div>
              )}
            </div>
          </div>
        </div>
        
        {/* Actions dropdown */}
        <div className="relative flex-shrink-0">
          <button
            onClick={(e) => {
              e.stopPropagation();
              setShowDropdown(!showDropdown);
            }}
            className="p-1 text-gray-400 hover:text-gray-600 rounded opacity-0 group-hover:opacity-100 transition-opacity"
          >
            <MoreVertical className="w-4 h-4" />
          </button>
          
          {showDropdown && (
            <div className="absolute right-0 top-full mt-1 w-32 bg-white border border-gray-200 rounded-md shadow-lg z-50">
              <button
                onClick={(e) => handleDropdownAction('view', e)}
                className="w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-2"
              >
                <Eye className="w-4 h-4" />
                <span>View</span>
              </button>
              {onEdit && (
                <button
                  onClick={(e) => handleDropdownAction('edit', e)}
                  className="w-full px-3 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center space-x-2"
                >
                  <Edit className="w-4 h-4" />
                  <span>Edit</span>
                </button>
              )}
              {onDelete && (
                <button
                  onClick={(e) => handleDropdownAction('delete', e)}
                  className="w-full px-3 py-2 text-left text-sm text-red-600 hover:bg-red-50 flex items-center space-x-2"
                >
                  <Trash2 className="w-4 h-4" />
                  <span>Delete</span>
                </button>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Event details */}
      <div className="space-y-2 text-sm text-gray-600">
        {/* Time */}
        <div className="flex items-center space-x-2">
          <Clock className="w-3.5 h-3.5 flex-shrink-0" />
          <span>
            {formatTime(event.startTime)} - {formatTime(event.endTime)}
            {duration > 0 && <span className="text-gray-400 ml-1">({duration}m)</span>}
          </span>
        </div>
        
        {/* Location or Virtual info */}
        {event.location && !event.isVirtual && (
          <div className="flex items-center space-x-2">
            <svg className="w-3.5 h-3.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            <span className="truncate">{event.location}</span>
          </div>
        )}
        
        {/* Client */}
        {event.client && (
          <div className="flex items-center space-x-2">
            <Users className="w-3.5 h-3.5 flex-shrink-0" />
            <span className="truncate">{clientName}</span>
          </div>
        )}
        
        {/* Project */}
        {event.project && (
          <div className="flex items-center space-x-2">
            <svg className="w-3.5 h-3.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
            </svg>
            <span className="truncate">{event.project.name}</span>
          </div>
        )}
        
        {/* Description */}
        {event.description && (
          <div className="pt-1 border-t border-gray-100">
            <p className="text-xs text-gray-500 line-clamp-2">
              {event.description}
            </p>
          </div>
        )}
      </div>

      {/* Meeting actions */}
      {event.isVirtual && event.meetingLink && (
        <div className="mt-3 pt-3 border-t border-gray-100 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <button
              onClick={handleJoinMeeting}
              className={`flex items-center space-x-2 px-3 py-1.5 rounded-md text-xs font-medium transition-colors ${platform?.bgColor} ${platform?.textColor} hover:opacity-80`}
            >
              <ExternalLink className="w-3 h-3" />
              <span>{platform?.joinText || 'Join Meeting'}</span>
            </button>
          </div>
          
          <button
            onClick={handleCopyMeetingLink}
            className="p-1.5 text-gray-400 hover:text-gray-600 rounded transition-colors"
            title="Copy meeting link"
          >
            <Copy className="w-3.5 h-3.5" />
          </button>
        </div>
      )}

      {/* Meeting details */}
      {event.isVirtual && event.meetingDetails && (
        <div className="mt-2 text-xs text-gray-500">
          {event.meetingDetails.meetingId && (
            <div>ID: {event.meetingDetails.meetingId}</div>
          )}
          {event.meetingDetails.passcode && (
            <div>Passcode: {event.meetingDetails.passcode}</div>
          )}
          {event.meetingDetails.waitingRoom && (
            <div className="text-blue-600">Waiting room enabled</div>
          )}
          {event.meetingDetails.recordingEnabled && (
            <div className="text-red-600">Recording enabled</div>
          )}
        </div>
      )}
    </div>
  );
} 