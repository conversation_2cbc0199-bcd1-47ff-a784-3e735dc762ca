'use client';

import { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  Area,
  AreaChart,
} from 'recharts';

// Types for chart data
interface OrdersByMonth {
  month: string;
  orders: number;
  revenue: number;
}

interface OrdersByStatus {
  name: string;
  value: number;
  color: string;
}

interface RevenueByCategory {
  category: string;
  revenue: number;
  orders: number;
}

interface DashboardChartsProps {
  className?: string;
}

// Mock chart data - replace with real API data
const mockOrdersByMonth: OrdersByMonth[] = [
  { month: 'Jan', orders: 65, revenue: 320000 },
  { month: 'Feb', orders: 78, revenue: 420000 },
  { month: 'Mar', orders: 90, revenue: 580000 },
  { month: 'Apr', orders: 81, revenue: 490000 },
  { month: 'May', orders: 95, revenue: 650000 },
  { month: 'Jun', orders: 110, revenue: 720000 },
];

const mockOrdersByStatus: OrdersByStatus[] = [
  { name: 'Completed', value: 189, color: '#10B981' },
  { name: 'Processing', value: 12, color: '#3B82F6' },
  { name: 'Pending', value: 23, color: '#F59E0B' },
  { name: 'Cancelled', value: 5, color: '#EF4444' },
];

const mockRevenueByCategory: RevenueByCategory[] = [
  { category: 'Business Cards', revenue: 450000, orders: 156 },
  { category: 'Banners', revenue: 380000, orders: 89 },
  { category: 'Brochures', revenue: 320000, orders: 67 },
  { category: 'Flyers', revenue: 280000, orders: 234 },
  { category: 'Posters', revenue: 190000, orders: 45 },
];

// Custom tooltip for charts
const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-white p-3 rounded-lg shadow-lg border border-gray-200">
        <p className="font-medium text-gray-900">{label}</p>
        {payload.map((entry: any, index: number) => (
          <p key={index} className="text-sm" style={{ color: entry.color }}>
            {entry.name}: {entry.name.toLowerCase().includes('revenue') 
              ? `KSh ${entry.value.toLocaleString()}` 
              : entry.value.toLocaleString()
            }
          </p>
        ))}
      </div>
    );
  }
  return null;
};

// Format currency for chart labels
const formatCurrency = (value: number) => {
  return new Intl.NumberFormat('en-KE', {
    style: 'currency',
    currency: 'KES',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
    notation: 'compact',
  }).format(value);
};

export default function DashboardCharts({ className = '' }: DashboardChartsProps) {
  const [activeChart, setActiveChart] = useState<'orders' | 'revenue' | 'status' | 'category'>('orders');
  const [orderData, setOrderData] = useState(mockOrdersByMonth);
  const [statusData, setStatusData] = useState(mockOrdersByStatus);
  const [categoryData, setCategoryData] = useState(mockRevenueByCategory);

  // In production, fetch real chart data from APIs
  useEffect(() => {
    // fetchChartData();
  }, []);

  return (
    <div className={`bg-white rounded-xl shadow-sm border border-gray-100 p-6 ${className}`}>
      {/* Chart Navigation */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 sm:mb-0">Analytics Overview</h3>
        
        <div className="flex flex-wrap gap-2">
          {[
            { key: 'orders', label: 'Orders Trend' },
            { key: 'revenue', label: 'Revenue Trend' },
            { key: 'status', label: 'Order Status' },
            { key: 'category', label: 'By Category' },
          ].map((tab) => (
            <button
              key={tab.key}
              onClick={() => setActiveChart(tab.key as any)}
              className={`px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                activeChart === tab.key
                  ? 'bg-blue-100 text-blue-700 border border-blue-200'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </div>
      </div>

      {/* Chart Container */}
      <div className="h-80">
        {activeChart === 'orders' && (
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart data={orderData}>
              <defs>
                <linearGradient id="orderGradient" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#3B82F6" stopOpacity={0.3}/>
                  <stop offset="95%" stopColor="#3B82F6" stopOpacity={0}/>
                </linearGradient>
              </defs>
              <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
              <XAxis 
                dataKey="month" 
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 12, fill: '#6B7280' }}
              />
              <YAxis 
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 12, fill: '#6B7280' }}
              />
              <Tooltip content={<CustomTooltip />} />
              <Area
                type="monotone"
                dataKey="orders"
                stroke="#3B82F6"
                strokeWidth={2}
                fillOpacity={1}
                fill="url(#orderGradient)"
                name="Orders"
              />
            </AreaChart>
          </ResponsiveContainer>
        )}

        {activeChart === 'revenue' && (
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={orderData}>
              <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
              <XAxis 
                dataKey="month" 
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 12, fill: '#6B7280' }}
              />
              <YAxis 
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 12, fill: '#6B7280' }}
                tickFormatter={formatCurrency}
              />
              <Tooltip content={<CustomTooltip />} />
              <Bar 
                dataKey="revenue" 
                fill="#10B981" 
                radius={[4, 4, 0, 0]}
                name="Revenue"
              />
            </BarChart>
          </ResponsiveContainer>
        )}

        {activeChart === 'status' && (
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={statusData}
                cx="50%"
                cy="50%"
                innerRadius={60}
                outerRadius={120}
                paddingAngle={2}
                dataKey="value"
              >
                {statusData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip 
                formatter={(value: number, name: string) => [
                  `${value} orders`,
                  name
                ]}
              />
            </PieChart>
          </ResponsiveContainer>
        )}

        {activeChart === 'category' && (
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={categoryData} layout="horizontal">
              <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
              <XAxis 
                type="number"
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 12, fill: '#6B7280' }}
                tickFormatter={formatCurrency}
              />
              <YAxis 
                type="category"
                dataKey="category"
                axisLine={false}
                tickLine={false}
                tick={{ fontSize: 12, fill: '#6B7280' }}
                width={100}
              />
              <Tooltip content={<CustomTooltip />} />
              <Bar 
                dataKey="revenue" 
                fill="#8B5CF6" 
                radius={[0, 4, 4, 0]}
                name="Revenue"
              />
            </BarChart>
          </ResponsiveContainer>
        )}
      </div>

      {/* Chart Legend for Pie Chart */}
      {activeChart === 'status' && (
        <div className="mt-6 pt-6 border-t border-gray-200">
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
            {statusData.map((item, index) => (
              <div key={index} className="flex items-center space-x-2">
                <div 
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: item.color }}
                />
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">{item.name}</p>
                  <p className="text-xs text-gray-500">{item.value} orders</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Chart Summary for Category */}
      {activeChart === 'category' && (
        <div className="mt-6 pt-6 border-t border-gray-200">
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 text-center">
            <div>
              <p className="text-2xl font-bold text-gray-900">
                {categoryData.reduce((sum, item) => sum + item.orders, 0)}
              </p>
              <p className="text-sm text-gray-600">Total Orders</p>
            </div>
            <div>
              <p className="text-2xl font-bold text-gray-900">
                {formatCurrency(categoryData.reduce((sum, item) => sum + item.revenue, 0))}
              </p>
              <p className="text-sm text-gray-600">Total Revenue</p>
            </div>
            <div>
              <p className="text-2xl font-bold text-gray-900">
                {formatCurrency(
                  categoryData.reduce((sum, item) => sum + item.revenue, 0) / 
                  categoryData.reduce((sum, item) => sum + item.orders, 0)
                )}
              </p>
              <p className="text-sm text-gray-600">Avg Order Value</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 