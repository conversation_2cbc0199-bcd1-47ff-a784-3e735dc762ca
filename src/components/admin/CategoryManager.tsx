'use client';

import React, { useState } from 'react';
import { PlusIcon, XMarkIcon } from '@heroicons/react/24/outline';

interface CategoryManagerProps {
  categories: string[];
  selectedCategory: string;
  onCategoryChange: (category: string) => void;
  onCategoryAdded?: (category: string) => void;
  disabled?: boolean;
  className?: string;
}

export default function CategoryManager({
  categories,
  selectedCategory,
  onCategoryChange,
  onCategoryAdded,
  disabled = false,
  className = ''
}: CategoryManagerProps) {
  const [showAddForm, setShowAddForm] = useState(false);
  const [newCategory, setNewCategory] = useState('');
  const [adding, setAdding] = useState(false);

  const handleAddCategory = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!newCategory.trim()) return;
    
    const categoryName = newCategory.trim();
    
    // Check if category already exists
    if (categories.includes(categoryName)) {
      alert('This category already exists');
      return;
    }

    setAdding(true);
    
    try {
      // Save the category to the database
      const response = await fetch('/api/admin/catalogue/categories', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ name: categoryName }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to create category');
      }

      if (result.success) {
        // Notify parent component
        onCategoryAdded?.(categoryName);
        onCategoryChange(categoryName);
        setNewCategory('');
        setShowAddForm(false);
      } else {
        throw new Error(result.error || 'Failed to create category');
      }
    } catch (error) {
      console.error('Error adding category:', error);
      alert(error instanceof Error ? error.message : 'Failed to add category');
    } finally {
      setAdding(false);
    }
  };

  const handleAddCategorySubmit = async () => {
    if (!newCategory.trim()) return;
    
    const categoryName = newCategory.trim();
    
    // Check if category already exists
    if (categories.includes(categoryName)) {
      alert('This category already exists');
      return;
    }

    setAdding(true);
    
    try {
      // Save the category to the database
      const response = await fetch('/api/admin/catalogue/categories', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ name: categoryName }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to create category');
      }

      if (result.success) {
        // Notify parent component
        onCategoryAdded?.(categoryName);
        onCategoryChange(categoryName);
        setNewCategory('');
        setShowAddForm(false);
      } else {
        throw new Error(result.error || 'Failed to create category');
      }
    } catch (error) {
      console.error('Error adding category:', error);
      alert(error instanceof Error ? error.message : 'Failed to add category');
    } finally {
      setAdding(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddCategorySubmit();
    }
  };

  const handleCancel = () => {
    setNewCategory('');
    setShowAddForm(false);
  };

  return (
    <div className={className}>
      <div className="flex items-center gap-2">
        <select
          value={selectedCategory}
          onChange={(e) => onCategoryChange(e.target.value)}
          disabled={disabled}
          className="flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {categories.map((category) => (
            <option key={category} value={category}>
              {category}
            </option>
          ))}
        </select>
        
        {!showAddForm && (
          <button
            type="button"
            onClick={() => setShowAddForm(true)}
            disabled={disabled}
            className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            title="Add new category"
          >
            <PlusIcon className="h-4 w-4" />
          </button>
        )}
      </div>

      {showAddForm && (
        <div className="mt-3 p-3 bg-gray-50 rounded-md border">
          <div className="flex items-center gap-2">
            <input
              type="text"
              value={newCategory}
              onChange={(e) => setNewCategory(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Enter new category name"
              className="flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
              maxLength={50}
              required
            />
            <button
              type="button"
              onClick={handleAddCategorySubmit}
              disabled={adding || !newCategory.trim()}
              className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {adding ? 'Adding...' : 'Add'}
            </button>
            <button
              type="button"
              onClick={handleCancel}
              className="inline-flex items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <XMarkIcon className="h-4 w-4" />
            </button>
          </div>
          <p className="mt-1 text-xs text-gray-500">
            Create a new category for your catalogue items. This will be saved to the database.
          </p>
        </div>
      )}
    </div>
  );
} 