import React from 'react';
import {
  UserIcon,
  EnvelopeIcon,
  KeyIcon,
  EyeIcon,
  EyeSlashIcon,
  ExclamationTriangleIcon,
  UserGroupIcon,
  CheckCircleIcon,
  XCircleIcon,
  AtSymbolIcon,
} from '@heroicons/react/24/outline';
import { Role, UserFormData } from '@/types/user';

interface FormFieldProps {
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  required?: boolean;
  error?: string;
  children: React.ReactNode;
}

function FormField({ label, icon: Icon, required = false, error, children }: FormFieldProps) {
  return (
    <div>
      <label className="block text-sm font-semibold text-gray-700 mb-2">
        <div className="flex items-center gap-2">
          <Icon className="h-4 w-4 text-gray-500" />
          {label} {required && <span className="text-red-500">*</span>}
        </div>
      </label>
      {children}
      {error && (
        <p className="mt-2 text-sm text-red-600 flex items-center gap-1">
          <ExclamationTriangleIcon className="h-4 w-4" />
          {error}
        </p>
      )}
    </div>
  );
}

interface UserFormFieldsProps {
  formData: UserFormData;
  errors: Record<string, string>;
  roles: Role[];
  showPasswords: {
    password: boolean;
    confirmPassword: boolean;
  };
  onFieldChange: (field: keyof UserFormData, value: string | boolean) => void;
  onTogglePassword: (field: 'password' | 'confirmPassword') => void;
  isEditMode?: boolean;
}

export function UserFormFields({
  formData,
  errors,
  roles,
  showPasswords,
  onFieldChange,
  onTogglePassword,
  isEditMode = false,
}: UserFormFieldsProps) {
  return (
    <div className="space-y-6">
      {/* Basic Information Section */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
          <UserIcon className="h-5 w-5 text-blue-500" />
          Basic Information
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Username Field */}
          <FormField
            label="Username"
            icon={AtSymbolIcon}
            required
            error={errors.username}
          >
            <input
              type="text"
              value={formData.username}
              onChange={(e) => onFieldChange('username', e.target.value)}
              className={`w-full px-4 py-3 border rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200 ${
                errors.username 
                  ? 'border-red-300 bg-red-50 focus:ring-red-500' 
                  : 'border-gray-300 hover:border-gray-400'
              }`}
              placeholder="Enter username"
              aria-describedby={errors.username ? 'username-error' : undefined}
            />
          </FormField>

          {/* Email Field */}
          <FormField
            label="Email Address"
            icon={EnvelopeIcon}
            required
            error={errors.email}
          >
            <input
              type="email"
              value={formData.email}
              onChange={(e) => onFieldChange('email', e.target.value)}
              className={`w-full px-4 py-3 border rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200 ${
                errors.email 
                  ? 'border-red-300 bg-red-50 focus:ring-red-500' 
                  : 'border-gray-300 hover:border-gray-400'
              }`}
              placeholder="Enter email address"
              aria-describedby={errors.email ? 'email-error' : undefined}
            />
          </FormField>

          {/* Full Name Field */}
          <div className="md:col-span-2">
            <FormField
              label="Full Name"
              icon={UserIcon}
              error={errors.name}
            >
              <input
                type="text"
                value={formData.name}
                onChange={(e) => onFieldChange('name', e.target.value)}
                className={`w-full px-4 py-3 border rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200 ${
                  errors.name 
                    ? 'border-red-300 bg-red-50 focus:ring-red-500' 
                    : 'border-gray-300 hover:border-gray-400'
                }`}
                placeholder="Enter full name (optional)"
                aria-describedby={errors.name ? 'name-error' : undefined}
              />
            </FormField>
          </div>
        </div>
      </div>

      {/* Password Section */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
          <KeyIcon className="h-5 w-5 text-red-500" />
          {isEditMode ? 'Change Password' : 'Password'}
          {isEditMode && <span className="text-sm font-normal text-gray-500">(leave blank to keep current)</span>}
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Password Field */}
          <FormField
            label={isEditMode ? "New Password" : "Password"}
            icon={KeyIcon}
            required={!isEditMode}
            error={errors.password}
          >
            <div className="relative">
              <input
                type={showPasswords.password ? 'text' : 'password'}
                value={formData.password}
                onChange={(e) => onFieldChange('password', e.target.value)}
                className={`w-full px-4 py-3 pr-12 border rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200 ${
                  errors.password 
                    ? 'border-red-300 bg-red-50 focus:ring-red-500' 
                    : 'border-gray-300 hover:border-gray-400'
                }`}
                placeholder={isEditMode ? "Leave blank to keep current password" : "Enter password"}
                aria-describedby={errors.password ? 'password-error' : undefined}
              />
              <button
                type="button"
                onClick={() => onTogglePassword('password')}
                className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 transition-colors"
                aria-label={showPasswords.password ? 'Hide password' : 'Show password'}
              >
                {showPasswords.password ? (
                  <EyeSlashIcon className="h-5 w-5" />
                ) : (
                  <EyeIcon className="h-5 w-5" />
                )}
              </button>
            </div>
          </FormField>

          {/* Confirm Password Field */}
          <FormField
            label="Confirm Password"
            icon={KeyIcon}
            required={!isEditMode || !!formData.password}
            error={errors.confirmPassword}
          >
            <div className="relative">
              <input
                type={showPasswords.confirmPassword ? 'text' : 'password'}
                value={formData.confirmPassword}
                onChange={(e) => onFieldChange('confirmPassword', e.target.value)}
                className={`w-full px-4 py-3 pr-12 border rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200 ${
                  errors.confirmPassword 
                    ? 'border-red-300 bg-red-50 focus:ring-red-500' 
                    : 'border-gray-300 hover:border-gray-400'
                }`}
                placeholder="Confirm password"
                aria-describedby={errors.confirmPassword ? 'confirm-password-error' : undefined}
              />
              <button
                type="button"
                onClick={() => onTogglePassword('confirmPassword')}
                className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 transition-colors"
                aria-label={showPasswords.confirmPassword ? 'Hide password' : 'Show password'}
              >
                {showPasswords.confirmPassword ? (
                  <EyeSlashIcon className="h-5 w-5" />
                ) : (
                  <EyeIcon className="h-5 w-5" />
                )}
              </button>
            </div>
          </FormField>
        </div>
      </div>

      {/* Role & Status Section */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
          <UserGroupIcon className="h-5 w-5 text-purple-500" />
          Role & Status
        </h3>
        
        <div className="space-y-6">
          {/* Role Selection */}
          <FormField
            label="User Role"
            icon={UserGroupIcon}
            required
            error={errors.roleId}
          >
            <select
              value={formData.roleId}
              onChange={(e) => onFieldChange('roleId', e.target.value)}
              className={`w-full px-4 py-3 border rounded-xl focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200 ${
                errors.roleId 
                  ? 'border-red-300 bg-red-50 focus:ring-red-500' 
                  : 'border-gray-300 hover:border-gray-400'
              }`}
              aria-describedby={errors.roleId ? 'role-error' : undefined}
            >
              <option value="">Select a role</option>
              {roles.map((role) => (
                <option key={role.id} value={role.id}>
                  {role.name} {role.description ? `- ${role.description}` : ''}
                </option>
              ))}
            </select>
          </FormField>

          {/* Account Status Toggle */}
          <div className="flex items-center gap-4 p-4 bg-gray-50 rounded-xl">
            <input
              type="checkbox"
              id="active"
              checked={formData.active}
              onChange={(e) => onFieldChange('active', e.target.checked)}
              className="h-5 w-5 text-orange-600 focus:ring-orange-500 border-gray-300 rounded transition-colors"
            />
            <label htmlFor="active" className="flex items-center gap-2 text-sm font-medium text-gray-700 cursor-pointer">
              {formData.active ? (
                <CheckCircleIcon className="h-5 w-5 text-green-500" />
              ) : (
                <XCircleIcon className="h-5 w-5 text-red-500" />
              )}
              Account Active
            </label>
            <span className="text-sm text-gray-500">
              {formData.active ? 'User can log in and access the system' : 'User account is disabled'}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
} 