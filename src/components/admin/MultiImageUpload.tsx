'use client';

import React, { useState, useCallback, useRef } from 'react';
import { 
  PhotoIcon, 
  XMarkIcon, 
  ArrowUpTrayIcon, 
  StarIcon,
  EyeIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';
import { ImageUploadService } from '@/utils/imageUploadUtils';

interface ImageFile {
  id: string;
  file: File | null;
  preview: string;
  uploading: boolean;
  uploaded: boolean;
  error: string | null;
  progress: number;
  url?: string;
  isFeatured: boolean;
}

interface MultiImageUploadProps {
  maxImages?: number;
  maxFileSize?: number;
  acceptedTypes?: string[];
  onImagesChange: (images: { url: string; isFeatured: boolean }[]) => void;
  initialImages?: { url: string; isFeatured: boolean }[];
  category?: string;
}

export default function MultiImageUpload({
  maxImages = 10,
  maxFileSize = 50, // Increased from 10MB to 50MB
  acceptedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/avif'],
  onImagesChange,
  initialImages = [],
  category = 'catalogue'
}: MultiImageUploadProps) {
  const [images, setImages] = useState<ImageFile[]>([]);
  const [dragActive, setDragActive] = useState(false);
  const [uploading, setUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Initialize with existing images
  React.useEffect(() => {
    if (initialImages.length > 0 && images.length === 0) {
      const existingImages: ImageFile[] = initialImages.map((img, index) => ({
        id: `existing-${index}`,
        file: null,
        preview: img.url,
        uploading: false,
        uploaded: true,
        error: null,
        progress: 100,
        url: img.url,
        isFeatured: img.isFeatured
      }));
      setImages(existingImages);
    }
  }, [initialImages.length, images.length]); // Only initialize if images is empty

  const validateFile = (file: File): string | null => {
    if (!acceptedTypes.includes(file.type)) {
      return `File type ${file.type} not supported`;
    }
    if (file.size > maxFileSize * 1024 * 1024) {
      return `File size must be less than ${maxFileSize}MB`;
    }
    return null;
  };

  const createImageFile = (file: File): ImageFile => ({
    id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    file,
    preview: URL.createObjectURL(file),
    uploading: false,
    uploaded: false,
    error: null,
    progress: 0,
    isFeatured: false
  });

  const handleFiles = useCallback((files: FileList | File[]) => {
    const fileArray = Array.from(files);
    const validFiles: ImageFile[] = [];
    const errors: string[] = [];

    fileArray.forEach(file => {
      const error = validateFile(file);
      if (error) {
        errors.push(`${file.name}: ${error}`);
      } else if (images.length + validFiles.length < maxImages) {
        validFiles.push(createImageFile(file));
      } else {
        errors.push(`Maximum ${maxImages} images allowed`);
      }
    });

    if (errors.length > 0) {
      alert(errors.join('\n'));
    }

    if (validFiles.length > 0) {
      setImages(prev => [...prev, ...validFiles]);
    }
  }, [images.length, maxImages]);

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
      handleFiles(e.dataTransfer.files);
    }
  }, [handleFiles]);

  const handleFileInput = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      handleFiles(e.target.files);
    }
  }, [handleFiles]);

  const removeImage = useCallback((id: string) => {
    setImages(prev => {
      const updated = prev.filter(img => img.id !== id);
      if (prev.find(img => img.id === id)?.isFeatured && updated.length > 0) {
        updated[0].isFeatured = true;
      }
      return updated;
    });
  }, []);

  const setFeaturedImage = useCallback((id: string) => {
    setImages(prev => prev.map(img => ({
      ...img,
      isFeatured: img.id === id
    })));
  }, []);

  const uploadAllImages = async () => {
    const imagesToUpload = images.filter(img => !img.uploaded && img.file);
    if (imagesToUpload.length === 0) return;

    setUploading(true);

    try {
      const uploadPromises = imagesToUpload.map(async (image) => {
        setImages(prev => prev.map(img => 
          img.id === image.id ? { ...img, uploading: true, progress: 0, error: null } : img
        ));

        try {
          const result = await ImageUploadService.uploadImage(
            image.file!,
            {
              category,
              timeoutMs: 600000, // 10 minutes timeout for large files
              maxRetries: 5, // Increase retry attempts
              onProgress: (progress: number) => {
                setImages(prev => prev.map(img => 
                  img.id === image.id ? { ...img, progress } : img
                ));
              },
              onStatusUpdate: (message: string, type: 'info' | 'warning' | 'error') => {
                console.log(`Upload ${image.id}: ${message} (${type})`);
              }
            }
          );

          setImages(prev => prev.map(img => 
            img.id === image.id ? { 
              ...img, 
              uploading: false, 
              uploaded: true, 
              url: result.url,
              progress: 100,
              error: null
            } : img
          ));

          return { success: true, id: image.id, url: result.url };
        } catch (error) {
          console.error(`Upload failed for image ${image.id}:`, error);
          
          let errorMessage = 'Upload failed';
          if (error instanceof Error) {
            errorMessage = ImageUploadService.cleanErrorMessage(error.message);
          }
          
          setImages(prev => prev.map(img => 
            img.id === image.id ? { 
              ...img, 
              uploading: false, 
              error: errorMessage,
              progress: 0
            } : img
          ));
          
          return { success: false, id: image.id, error: errorMessage };
        }
      });

      const results = await Promise.all(uploadPromises);
      const successCount = results.filter(r => r.success).length;
      const failCount = results.filter(r => !r.success).length;

      // Provide better user feedback
      if (failCount === 0) {
        // All successful
        console.log(`Successfully uploaded ${successCount} image${successCount > 1 ? 's' : ''}!`);
      } else if (successCount === 0) {
        // All failed
        alert(`Upload failed: All ${failCount} image${failCount > 1 ? 's' : ''} failed to upload. Please check your internet connection and try again.`);
      } else {
        // Partial success
        alert(`Upload completed: ${successCount} successful, ${failCount} failed. You can retry the failed uploads.`);
      }

    } catch (error) {
      console.error('Batch upload error:', error);
      alert('An unexpected error occurred during upload. Please try again.');
    } finally {
      setUploading(false);
    }
  };

  // Update parent component when images change
  React.useEffect(() => {
    const uploadedImages = images
      .filter(img => img.uploaded && img.url)
      .map(img => ({ url: img.url!, isFeatured: img.isFeatured }));
    onImagesChange(uploadedImages);
  }, [images]); // Remove onImagesChange from dependencies to avoid infinite loops

  const canUpload = images.some(img => !img.uploaded && img.file);
  const hasUploaded = images.some(img => img.uploaded);

  return (
    <div className="space-y-6">
      {/* Upload Area */}
      <div
        className={`relative border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
          dragActive 
            ? 'border-blue-500 bg-blue-50' 
            : 'border-gray-300 hover:border-gray-400'
        }`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept={acceptedTypes.join(',')}
          onChange={handleFileInput}
          className="hidden"
        />
        
        <PhotoIcon className="mx-auto h-12 w-12 text-gray-400" />
        <div className="mt-4">
          <p className="text-lg font-medium text-gray-900">
            Drop images here or click to browse
          </p>
          <p className="text-sm text-gray-500 mt-2">
            Support: {acceptedTypes.join(', ')} • Max {maxFileSize}MB each • Up to {maxImages} images
          </p>
        </div>
        
        <button
          type="button"
          onClick={() => fileInputRef.current?.click()}
          className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <PhotoIcon className="h-4 w-4 mr-2" />
          Select Images
        </button>
      </div>

      {/* Batch Upload Controls */}
      {canUpload && (
        <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
          <div className="flex items-center space-x-4">
            <span className="text-sm text-gray-600">
              {images.filter(img => !img.uploaded).length} images ready to upload
            </span>
          </div>
          
          <button
            type="button"
            onClick={uploadAllImages}
            disabled={uploading}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <ArrowUpTrayIcon className="h-4 w-4 mr-2" />
            {uploading ? 'Uploading...' : 'Upload All'}
          </button>
        </div>
      )}

      {/* Images Grid */}
      {images.length > 0 && (
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {images.map((image) => (
            <div key={image.id} className="relative group">
              <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden">
                <img
                  src={image.preview}
                  alt="Upload preview"
                  className="w-full h-full object-cover"
                />
                
                {/* Upload Status Overlay */}
                {image.uploading && (
                  <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                    <div className="text-center text-white">
                      <div className="w-16 bg-gray-200 rounded-full h-2 mb-2">
                        <div 
                          className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${image.progress}%` }}
                        />
                      </div>
                      <span className="text-xs">{Math.round(image.progress)}%</span>
                    </div>
                  </div>
                )}

                {/* Success/Error Overlay */}
                {image.uploaded && (
                  <div className="absolute top-2 right-2">
                    <CheckCircleIcon className="h-6 w-6 text-green-500 bg-white rounded-full" />
                  </div>
                )}
                
                {image.error && (
                  <div className="absolute inset-0 bg-red-500 bg-opacity-75 flex items-center justify-center">
                    <div className="text-center text-white p-2">
                      <ExclamationTriangleIcon className="h-6 w-6 mx-auto mb-1" />
                      <span className="text-xs">{image.error}</span>
                    </div>
                  </div>
                )}
              </div>

              {/* Image Controls */}
              <div className="absolute top-2 left-2 flex space-x-1">
                <button
                  type="button"
                  onClick={() => setFeaturedImage(image.id)}
                  className={`p-1 rounded-full ${
                    image.isFeatured 
                      ? 'bg-yellow-500 text-white' 
                      : 'bg-white bg-opacity-75 text-gray-600 hover:bg-yellow-100'
                  }`}
                  title={image.isFeatured ? 'Featured Image' : 'Set as Featured'}
                >
                  {image.isFeatured ? (
                    <StarIconSolid className="h-4 w-4" />
                  ) : (
                    <StarIcon className="h-4 w-4" />
                  )}
                </button>
              </div>

              {/* Action Buttons */}
              <div className="absolute top-2 right-2 flex space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                {image.uploaded && (
                  <button
                    type="button"
                    onClick={() => window.open(image.url, '_blank')}
                    className="p-1 bg-white bg-opacity-75 rounded-full text-gray-600 hover:bg-blue-100"
                    title="View Full Size"
                  >
                    <EyeIcon className="h-4 w-4" />
                  </button>
                )}
                
                <button
                  type="button"
                  onClick={() => removeImage(image.id)}
                  className="p-1 bg-white bg-opacity-75 rounded-full text-gray-600 hover:bg-red-100"
                  title="Remove Image"
                >
                  <XMarkIcon className="h-4 w-4" />
                </button>
              </div>

              {/* Featured Badge */}
              {image.isFeatured && (
                <div className="absolute bottom-2 left-2 bg-yellow-500 text-white text-xs px-2 py-1 rounded-full">
                  Featured
                </div>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Summary */}
      {hasUploaded && (
        <div className="p-4 bg-green-50 rounded-lg">
          <div className="flex items-center">
            <CheckCircleIcon className="h-5 w-5 text-green-500 mr-2" />
            <span className="text-sm text-green-800">
              {images.filter(img => img.uploaded).length} of {images.length} images uploaded successfully
              {images.some(img => img.isFeatured) && (
                <span className="ml-2">• Featured image selected</span>
              )}
            </span>
          </div>
        </div>
      )}
    </div>
  );
} 