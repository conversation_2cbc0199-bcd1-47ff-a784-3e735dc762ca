'use client';
import React from 'react';

import { useState, useEffect, useCallback, useRef, Fragment, Component, ErrorInfo, ReactNode } from 'react';
import { usePathname, useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import AdminHeader from './AdminHeader';
import AdminSidebar from './AdminSidebar';
import AdminFooter from './AdminFooter';
import { NotificationProvider } from '@/contexts/NotificationContext';

// Types for better TypeScript support
interface AdminLayoutState {
  sidebarOpen: boolean;
  mounted: boolean;
}

interface AdminLayoutProps {
  children: React.ReactNode;
}

// Auth Error Display Component
function AuthErrorDisplay({ error, onRetry }: { error: string; onRetry: () => void }) {
  const router = useRouter();
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-6">
        <div className="text-center">
          <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
            <svg className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.545-1.66 1.848-3.12L8.928 5.12c-.696-1.46-2.6-1.46-3.296 0L2.784 16.88C2.087 18.34 3.094 20 4.632 20z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Authentication Error</h3>
          <p className="text-sm text-gray-600 mb-6">
            {error || 'There was a problem with authentication. Please try logging in again.'}
          </p>
          <div className="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-3">
            <button
              onClick={onRetry}
              className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:text-sm"
            >
              Try Again
            </button>
            <button
              onClick={() => router.push('/admin/login')}
              className="w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:text-sm"
            >
              Login
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

// Auth Loading Display Component
function AuthLoadingDisplay() {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">Authenticating</h3>
        <p className="text-sm text-gray-600">
          Verifying your session...
        </p>
      </div>
    </div>
  );
}

// Custom hook for sidebar management with industry standards
function useSidebarManager() {
  const [state, setState] = useState<AdminLayoutState>({
    sidebarOpen: false,
    mounted: false
  });
  
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const overlayRef = useRef<HTMLDivElement>(null);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  const setSidebarOpen = useCallback((open: boolean) => {
    setState(prev => ({ ...prev, sidebarOpen: open }));
  }, []);

  const toggleSidebar = useCallback(() => {
    setState(prev => ({ ...prev, sidebarOpen: !prev.sidebarOpen }));
  }, []);

  const closeSidebar = useCallback(() => {
    setState(prev => ({ ...prev, sidebarOpen: false }));
  }, []);

  // Mount detection with proper timing
  useEffect(() => {
    const timer = setTimeout(() => {
      setState(prev => ({ ...prev, mounted: true }));
    }, 0);

    return () => clearTimeout(timer);
  }, []);

  // Enhanced body scroll lock with better cleanup - MOBILE ONLY
  useEffect(() => {
    if (typeof window !== 'undefined' && state.mounted && state.sidebarOpen) {
      // Check if we're on mobile (below md breakpoint)
      const checkIsMobile = () => window.innerWidth < 768;
      let isMobile = checkIsMobile();
      
      if (!isMobile) {
        // Don't apply scroll lock on desktop
        return;
      }
      
      const body = document.body;
      const html = document.documentElement;
      
      // Store original styles
      const originalBodyOverflow = body.style.overflow;
      const originalBodyPosition = body.style.position;
      const originalBodyTop = body.style.top;
      const originalBodyWidth = body.style.width;
      const originalHtmlOverflow = html.style.overflow;
      
      // Apply lock styles only on mobile
      const scrollY = window.scrollY;
      body.style.overflow = 'hidden';
      body.style.position = 'fixed';
      body.style.top = `-${scrollY}px`;
      body.style.width = '100%';
      html.style.overflow = 'hidden';
      
      // Handle window resize to remove scroll lock if screen becomes desktop size
      const handleResize = () => {
        if (!checkIsMobile()) {
          // Screen became desktop size, remove scroll lock
          body.style.overflow = originalBodyOverflow;
          body.style.position = originalBodyPosition;
          body.style.top = originalBodyTop;
          body.style.width = originalBodyWidth;
          html.style.overflow = originalHtmlOverflow;
          window.scrollTo(0, scrollY);
        }
      };
      
      window.addEventListener('resize', handleResize);
      
      return () => {
        // Remove resize listener
        window.removeEventListener('resize', handleResize);
        
        // Restore original styles
        body.style.overflow = originalBodyOverflow;
        body.style.position = originalBodyPosition;
        body.style.top = originalBodyTop;
        body.style.width = originalBodyWidth;
        html.style.overflow = originalHtmlOverflow;
        
        // Restore scroll position
        window.scrollTo(0, scrollY);
      };
    }
  }, [state.sidebarOpen, state.mounted]);

  // Keyboard event handling with proper cleanup
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && state.sidebarOpen) {
        event.preventDefault();
        closeSidebar();
      }
    };

    if (state.mounted) {
      document.addEventListener('keydown', handleKeyDown, { passive: false });
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [state.sidebarOpen, state.mounted, closeSidebar]);

  // Focus management for accessibility
  useEffect(() => {
    if (state.sidebarOpen && state.mounted) {
      // Focus the first focusable element in the sidebar after it opens
      timeoutRef.current = setTimeout(() => {
        const sidebar = document.querySelector('[data-sidebar="mobile"]');
        const firstFocusable = sidebar?.querySelector('button, a, input, select, textarea, [tabindex]:not([tabindex="-1"])') as HTMLElement;
        firstFocusable?.focus();
      }, 100);
    }
  }, [state.sidebarOpen, state.mounted]);

  return {
    sidebarOpen: state.sidebarOpen,
    mounted: state.mounted,
    setSidebarOpen,
    toggleSidebar,
    closeSidebar,
    overlayRef
  };
}

// Error Boundary Component
function AdminLayoutErrorBoundary({ children }: { children: React.ReactNode }) {
  const [hasError, setHasError] = useState(false);

  useEffect(() => {
    const handleError = (error: ErrorEvent) => {
      // Enhanced error logging with proper fallbacks
      const errorInfo = {
        message: error.message || 'Unknown error',
        filename: error.filename || 'Unknown file',
        lineno: error.lineno || 0,
        colno: error.colno || 0,
        error: error.error || 'No error details available'
      };
      
      // Only log meaningful errors, skip empty or trivial errors
      if (errorInfo.message && errorInfo.message !== 'Script error.') {
        console.error('AdminLayout Error:', errorInfo);
        setHasError(true);
      }
    };

    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      // Handle promise rejections that might cause empty error objects
      const reason = event.reason;
      if (reason && typeof reason === 'object' && Object.keys(reason).length > 0) {
        console.error('AdminLayout Promise Rejection:', reason);
        setHasError(true);
      }
    };

    window.addEventListener('error', handleError);
    window.addEventListener('unhandledrejection', handleUnhandledRejection);
    
    return () => {
      window.removeEventListener('error', handleError);
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, []);

  if (hasError) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Something went wrong</h1>
          <p className="text-gray-600 mb-4">Please refresh the page to try again.</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
          >
            Refresh Page
          </button>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}

// React Error Boundary Class Component for better React error handling
class ReactErrorBoundary extends Component<
  { children: ReactNode; fallback?: ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: ReactNode; fallback?: ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log error details only if they contain meaningful information
    if (error && error.message && error.message.trim() !== '') {
      console.error('React Error Boundary caught an error:', {
        error: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack
      });
    }
  }

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      return this.props.fallback || (
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Component Error</h1>
            <p className="text-gray-600 mb-4">A component error occurred. Please refresh the page.</p>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
            >
              Refresh Page
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default function AdminLayoutClient({ children }: AdminLayoutProps) {
  const { data: session, status } = useSession();
  const router = useRouter();
  
  const isLoading = status === 'loading';
  const user = session?.user;
  
  const {
    sidebarOpen,
    mounted,
    toggleSidebar,
    closeSidebar,
    overlayRef
  } = useSidebarManager();
  
  const pathname = usePathname();
  const isLoginPage = pathname === '/admin/login';

  // Handle overlay click with proper event handling
  const handleOverlayClick = useCallback((event: React.MouseEvent<HTMLDivElement>) => {
    // Ensure we only close if clicking the overlay itself, not its children
    if (event.target === event.currentTarget) {
      event.preventDefault();
      event.stopPropagation();
      closeSidebar();
    }
  }, [closeSidebar]);

  // Handle overlay touch events for mobile
  const handleOverlayTouch = useCallback((event: React.TouchEvent<HTMLDivElement>) => {
    if (event.target === event.currentTarget) {
      event.preventDefault();
      closeSidebar();
    }
  }, [closeSidebar]);

  // For login page, don't show auth loading/error states
  if (isLoginPage) {
    return (
      <ReactErrorBoundary>
        <AdminLayoutErrorBoundary>
          <NotificationProvider>
            {children}
          </NotificationProvider>
        </AdminLayoutErrorBoundary>
      </ReactErrorBoundary>
    );
  }

  // Show authentication loading state
  if (isLoading) {
    return <AuthLoadingDisplay />;
  }

  // Show unauthenticated state
  if (!user) {
    return (
      <AuthErrorDisplay 
        error="You must be logged in to access this page."
        onRetry={() => router.push('/admin/login')}
      />
    );
  }

  // SSR safety - don't render until mounted
  if (!mounted) {
    return (
      <div className="min-h-screen bg-gray-50 admin-layout-responsive">
        <div className="animate-pulse">
          {/* Loading skeleton for admin layout with header */}
          <div className="bg-white shadow-sm">
            <div className="px-4 sm:px-6 lg:px-8">
              <div className="flex justify-between items-center py-6">
                <div className="flex items-center">
                  <div className="h-8 w-8 bg-gray-200 rounded-lg"></div>
                  <div className="ml-4 h-6 w-32 bg-gray-200 rounded"></div>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="h-8 w-8 bg-gray-200 rounded-full"></div>
                  <div className="h-8 w-8 bg-gray-200 rounded-full"></div>
                </div>
              </div>
            </div>
          </div>
          
          {/* Loading skeleton for main content */}
          <div className="flex flex-1">
            <div className="hidden md:flex md:w-64 md:flex-col">
              <div className="flex flex-col flex-1 min-h-0 bg-gray-800">
                <div className="flex-1 flex flex-col pt-5 pb-4 overflow-y-auto">
                  <div className="flex items-center flex-shrink-0 px-4">
                    <div className="h-8 w-32 bg-gray-700 rounded"></div>
                  </div>
                  <nav className="mt-5 flex-1 px-2 space-y-1">
                    {[...Array(8)].map((_, i) => (
                      <div key={i} className="h-10 bg-gray-700 rounded"></div>
                    ))}
                  </nav>
                </div>
              </div>
            </div>
            
            <div className="flex flex-col flex-1 overflow-hidden">
              <main className="flex-1 relative overflow-y-auto focus:outline-none">
                <div className="py-6">
                  <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
                    <div className="h-96 bg-gray-200 rounded-lg animate-pulse"></div>
                  </div>
                </div>
              </main>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <ReactErrorBoundary>
      <AdminLayoutErrorBoundary>
        <NotificationProvider>
          <div className="min-h-screen bg-gray-50 admin-layout-responsive">
          {/* Mobile sidebar overlay */}
          {sidebarOpen && (
            <div 
              ref={overlayRef}
              className="fixed inset-0 flex z-40 md:hidden"
              onClick={handleOverlayClick}
              onTouchEnd={handleOverlayTouch}
            >
              <div 
                className="fixed inset-0 bg-gray-600 bg-opacity-75 transition-opacity"
                aria-hidden="true"
              />
                             <AdminSidebar 
                 open={sidebarOpen} 
                 onClose={closeSidebar}
               />
            </div>
          )}

                     {/* Static sidebar for desktop */}
           <AdminSidebar 
             open={sidebarOpen} 
             onClose={closeSidebar}
           />

           {/* Main content area */}
           <div className="md:pl-64 flex flex-col flex-1 min-h-screen">
             <AdminHeader 
               sidebarOpen={sidebarOpen} 
               onToggleSidebar={toggleSidebar} 
             />
            
            <main className="flex-1">
              <div className="py-6">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
                  {/* Auth Debug Info (only in development) */}
                  {process.env.NODE_ENV === 'development' && user && (
                    <div className="mb-4 p-2 bg-green-50 border border-green-200 rounded text-xs text-green-700">
                      <strong>Authenticated:</strong> {user.username} ({typeof user.role === 'object' ? (user.role as any)?.name : user.role})
                    </div>
                  )}
                  
                  {children}
                </div>
              </div>
            </main>
            
            {/* Admin Footer */}
            <AdminFooter />
          </div>
        </div>
      </NotificationProvider>
    </AdminLayoutErrorBoundary>
    </ReactErrorBoundary>
  );
}