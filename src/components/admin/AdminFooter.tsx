'use client';

import React, { useState, useEffect } from 'react';

export default function AdminFooter() {
  const [currentYear] = useState(new Date().getFullYear());
  const [uptime, setUptime] = useState<string>('');

  useEffect(() => {
    // Calculate simple uptime display
    const startTime = Date.now();
    const updateUptime = () => {
      const elapsed = Date.now() - startTime;
      const minutes = Math.floor(elapsed / 60000);
      const hours = Math.floor(minutes / 60);
      
      if (hours > 0) {
        setUptime(`${hours}h ${minutes % 60}m`);
      } else {
        setUptime(`${minutes}m`);
      }
    };

    // Update every minute
    const interval = setInterval(updateUptime, 60000);
    updateUptime(); // Initial call

    return () => clearInterval(interval);
  }, []);

  return (
    <footer className="bg-white border-t border-gray-200 mt-auto">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
        <div className="py-4">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
            {/* Left side - Copyright */}
            <div className="flex items-center space-x-4 text-sm text-gray-500">
              <span>© {currentYear} Mocky Digital</span>
              <span className="hidden sm:inline">•</span>
              <span className="text-xs">Admin Panel v1.0</span>
            </div>

            {/* Right side - Status and Links */}
            <div className="flex items-center space-x-4 text-sm text-gray-500">
              {/* System Status */}
              <div className="flex items-center space-x-1">
                <div className="h-2 w-2 bg-green-400 rounded-full animate-pulse"></div>
                <span className="text-xs">Online</span>
              </div>
              
              {/* Session Uptime */}
              {uptime && (
                <>
                  <span className="hidden sm:inline">•</span>
                  <span className="text-xs">Session: {uptime}</span>
                </>
              )}

              {/* Quick Links */}
              <span className="hidden md:inline">•</span>
              <div className="hidden md:flex items-center space-x-3">
                <a 
                  href="/admin/settings" 
                  className="text-xs text-gray-400 hover:text-gray-600 transition-colors"
                >
                  Settings
                </a>
                <a 
                  href="/admin/activity-logs" 
                  className="text-xs text-gray-400 hover:text-gray-600 transition-colors"
                >
                  Logs
                </a>
                <button 
                  onClick={() => window.open('/admin/dashboard', '_blank')}
                  className="text-xs text-gray-400 hover:text-gray-600 transition-colors"
                >
                  Dashboard
                </button>
              </div>
            </div>
          </div>

          {/* Mobile Links */}
          <div className="md:hidden flex items-center justify-center space-x-4 mt-2 pt-2 border-t border-gray-100">
            <a 
              href="/admin/settings" 
              className="text-xs text-gray-400 hover:text-gray-600 transition-colors"
            >
              Settings
            </a>
            <span>•</span>
            <a 
              href="/admin/activity-logs" 
              className="text-xs text-gray-400 hover:text-gray-600 transition-colors"
            >
              Activity Logs
            </a>
            <span>•</span>
            <button 
              onClick={() => window.open('/admin/dashboard', '_blank')}
              className="text-xs text-gray-400 hover:text-gray-600 transition-colors"
            >
              Dashboard
            </button>
          </div>
        </div>
      </div>
    </footer>
  );
}
