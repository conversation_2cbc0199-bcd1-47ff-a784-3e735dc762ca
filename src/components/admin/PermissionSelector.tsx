'use client';
import React from 'react';

import { useState } from 'react';
import { CheckIcon } from '@heroicons/react/24/outline';

interface PermissionSelectorProps {
  selectedPermissions: string[];
  onChange: (permissions: string[]) => void;
  disabled?: boolean;
}

// Define comprehensive enterprise permission groups
const permissionGroups = [
  {
    name: 'Dashboard & Analytics',
    description: 'Access to dashboard and analytics features',
    permissions: [
      { id: 'dashboard:read', name: 'View Dashboard', description: 'Access main dashboard and overview' },
      { id: 'analytics:read', name: 'View Analytics', description: 'Access analytics and reports' },
      { id: 'analytics:export', name: 'Export Analytics', description: 'Export analytics data and reports' },
    ]
  },
  {
    name: 'Content Management',
    description: 'Blog posts, articles, and content creation',
    permissions: [
      { id: 'blog:read', name: 'View Blog Posts', description: 'View all blog posts and articles' },
      { id: 'blog:write', name: 'Create/Edit Blog Posts', description: 'Create, edit, and manage blog posts' },
      { id: 'blog:delete', name: 'Delete Blog Posts', description: 'Delete blog posts and articles' },
      { id: 'blog:publish', name: 'Publish Blog Posts', description: 'Publish and unpublish blog posts' },
      { id: 'blog:schedule', name: 'Schedule Blog Posts', description: 'Schedule posts for future publication' },
      { id: 'categories:read', name: 'View Categories', description: 'View content categories' },
      { id: 'categories:write', name: 'Manage Categories', description: 'Create, edit, and delete categories' },
      { id: 'tags:read', name: 'View Tags', description: 'View content tags' },
      { id: 'tags:write', name: 'Manage Tags', description: 'Create, edit, and delete tags' },
    ]
  },
  {
    name: 'Portfolio & Showcase',
    description: 'Portfolio items and showcase management',
    permissions: [
      { id: 'portfolio:read', name: 'View Portfolio', description: 'View portfolio items and projects' },
      { id: 'portfolio:write', name: 'Manage Portfolio', description: 'Create, edit, and manage portfolio items' },
      { id: 'portfolio:delete', name: 'Delete Portfolio Items', description: 'Delete portfolio items and projects' },
      { id: 'testimonials:read', name: 'View Testimonials', description: 'View client testimonials' },
      { id: 'testimonials:write', name: 'Manage Testimonials', description: 'Create, edit, and manage testimonials' },
    ]
  },
  {
    name: 'Team & Organization',
    description: 'Team member and organizational management',
    permissions: [
      { id: 'team:read', name: 'View Team Members', description: 'View team member profiles' },
      { id: 'team:write', name: 'Manage Team Members', description: 'Create, edit, and manage team members' },
      { id: 'team:delete', name: 'Remove Team Members', description: 'Remove team members from organization' },
    ]
  },

  {
    name: 'Financial Management',
    description: 'Invoicing, payments, and financial operations',
    permissions: [
      { id: 'transactions:read', name: 'View Transactions', description: 'View financial transactions and history' },
      { id: 'transactions:write', name: 'Manage Transactions', description: 'Create and manage financial transactions' },
      { id: 'receipts:read', name: 'View Receipts', description: 'View payment receipts and records' },
      { id: 'receipts:write', name: 'Create/Edit Receipts', description: 'Generate and manage receipts' },
      { id: 'receipts:delete', name: 'Delete Receipts', description: 'Delete receipt records' },
      { id: 'invoices:read', name: 'View Invoices', description: 'View client invoices and billing' },
      { id: 'invoices:write', name: 'Create/Edit Invoices', description: 'Generate and manage invoices' },
      { id: 'invoices:delete', name: 'Delete Invoices', description: 'Delete invoice records' },
      { id: 'quotes:read', name: 'View Quotes', description: 'View project quotes and estimates' },
      { id: 'quotes:write', name: 'Create/Edit Quotes', description: 'Generate and manage quotes' },
      { id: 'quotes:delete', name: 'Delete Quotes', description: 'Delete quote records' },
      { id: 'financial-reports:read', name: 'View Financial Reports', description: 'Access financial reports and analytics' },
    ]
  },
  {
    name: 'Service & Catalog Management',
    description: 'Service offerings and pricing management',
    permissions: [
      { id: 'services:read', name: 'View Services', description: 'View service catalog and offerings' },
      { id: 'services:write', name: 'Manage Services', description: 'Create, edit, and manage services' },
      { id: 'services:delete', name: 'Delete Services', description: 'Remove services from catalog' },
      { id: 'pricing:read', name: 'View Pricing', description: 'View pricing information and structures' },
      { id: 'pricing:write', name: 'Manage Pricing', description: 'Set and manage pricing for services' },
      { id: 'catalogue:read', name: 'View Catalogue', description: 'View service catalogue and products' },
      { id: 'catalogue:write', name: 'Manage Catalogue', description: 'Manage service catalogue and products' },
    ]
  },
  {
    name: 'User Management',
    description: 'User accounts, roles, and permissions',
    permissions: [
      { id: 'users:read', name: 'View Users', description: 'View user accounts and profiles' },
      { id: 'users:write', name: 'Manage Users', description: 'Create, edit, and manage user accounts' },
      { id: 'users:delete', name: 'Delete Users', description: 'Delete user accounts and data' },
      { id: 'users:impersonate', name: 'Impersonate Users', description: 'Login as other users for support' },
      { id: 'roles:read', name: 'View Roles', description: 'View user roles and permissions' },
      { id: 'roles:write', name: 'Manage Roles', description: 'Create, edit, and manage user roles' },
      { id: 'roles:delete', name: 'Delete Roles', description: 'Delete user roles and permissions' },
    ]
  },
  {
    name: 'System Administration',
    description: 'System configuration and maintenance',
    permissions: [
      { id: 'logs:read', name: 'View Activity Logs', description: 'View system and user activity logs' },
      { id: 'logs:export', name: 'Export Activity Logs', description: 'Export activity logs and audit trails' },
      { id: 'database:read', name: 'View Database', description: 'View database information and status' },
      { id: 'database:write', name: 'Manage Database', description: 'Perform database operations and maintenance' },
      { id: 'database:backup', name: 'Database Backup', description: 'Create and manage database backups' },
      { id: 'database:restore', name: 'Database Restore', description: 'Restore database from backups' },
      { id: 'system:maintenance', name: 'System Maintenance', description: 'Perform system maintenance tasks' },
      { id: 'system:monitoring', name: 'System Monitoring', description: 'Monitor system health and performance' },
    ]
  },
  {
    name: 'Configuration & Settings',
    description: 'System and application configuration',
    permissions: [
      { id: 'settings:read', name: 'View Settings', description: 'View system and application settings' },
      { id: 'settings:write', name: 'Manage Settings', description: 'Modify system and application settings' },
      { id: 'settings:security', name: 'Security Settings', description: 'Manage security and authentication settings' },
      { id: 'settings:integrations', name: 'Integration Settings', description: 'Manage third-party integrations' },
      { id: 'settings:notifications', name: 'Notification Settings', description: 'Configure system notifications' },
      { id: 'storage:read', name: 'View Storage', description: 'View storage configuration and usage' },
      { id: 'storage:write', name: 'Manage Storage', description: 'Configure storage settings and providers' },
    ]
  },
  {
    name: 'API & Development',
    description: 'API access and development tools',
    permissions: [
      { id: 'api:read', name: 'API Read Access', description: 'Read-only access to API endpoints' },
      { id: 'api:write', name: 'API Write Access', description: 'Full access to API endpoints' },
      { id: 'api:admin', name: 'API Administration', description: 'Manage API keys and access controls' },
      { id: 'webhooks:read', name: 'View Webhooks', description: 'View webhook configurations' },
      { id: 'webhooks:write', name: 'Manage Webhooks', description: 'Create and manage webhooks' },
      { id: 'integrations:read', name: 'View Integrations', description: 'View third-party integrations' },
      { id: 'integrations:write', name: 'Manage Integrations', description: 'Configure third-party integrations' },
    ]
  },
  {
    name: 'Super Admin',
    description: 'Ultimate system access - use with extreme caution',
    permissions: [
      { id: '*', name: 'All Permissions (Super Admin)', description: 'Complete access to all system features and data' },
    ]
  }
];

export default function PermissionSelector({ selectedPermissions, onChange, disabled = false }: PermissionSelectorProps) {
  const [expandedGroups, setExpandedGroups] = useState<Record<string, boolean>>({});
  const [searchTerm, setSearchTerm] = useState('');

  const toggleGroup = (groupName: string) => {
    if (disabled) return;
    
    setExpandedGroups(prev => ({
      ...prev,
      [groupName]: !prev[groupName]
    }));
  };

  const togglePermission = (permissionId: string) => {
    if (disabled) return;
    
    // If selecting the wildcard permission, clear all others
    if (permissionId === '*') {
      onChange(['*']);
      return;
    }
    
    // If a specific permission is selected and wildcard was previously selected, remove wildcard
    let newPermissions = [...selectedPermissions];
    if (selectedPermissions.includes('*')) {
      newPermissions = [];
    }
    
    // Toggle the permission
    if (newPermissions.includes(permissionId)) {
      newPermissions = newPermissions.filter(id => id !== permissionId);
    } else {
      newPermissions.push(permissionId);
    }
    
    onChange(newPermissions);
  };

  const toggleGroupPermissions = (groupPermissions: { id: string, name: string }[]) => {
    if (disabled) return;
    
    // Check if all permissions in the group are already selected
    const permissionIds = groupPermissions.map(p => p.id);
    const allSelected = permissionIds.every(id => selectedPermissions.includes(id));
    
    let newPermissions = [...selectedPermissions];
    
    // Remove wildcard if it exists
    if (newPermissions.includes('*')) {
      newPermissions = newPermissions.filter(id => id !== '*');
    }
    
    if (allSelected) {
      // Remove all permissions in this group
      newPermissions = newPermissions.filter(id => !permissionIds.includes(id));
    } else {
      // Add all permissions in this group that aren't already selected
      permissionIds.forEach(id => {
        if (!newPermissions.includes(id)) {
          newPermissions.push(id);
        }
      });
    }
    
    onChange(newPermissions);
  };

  // Filter groups and permissions based on search
  const filteredGroups = permissionGroups.map(group => ({
    ...group,
    permissions: group.permissions.filter(permission =>
      permission.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      permission.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      permission.id.toLowerCase().includes(searchTerm.toLowerCase())
    )
  })).filter(group => 
    group.permissions.length > 0 || 
    group.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    group.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const selectedCount = selectedPermissions.includes('*') ? 'All' : selectedPermissions.length;
  const totalPermissions = permissionGroups.reduce((total, group) => total + group.permissions.length, 0);

  return (
    <div className={`border border-gray-300 rounded-lg overflow-hidden ${disabled ? 'opacity-75' : ''}`}>
      {/* Header with search and summary */}
      <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-sm font-medium text-gray-900">Permissions</h3>
          <span className="text-xs text-gray-500">
            {selectedCount} of {totalPermissions} selected
          </span>
        </div>
        <div className="relative">
          <input
            type="text"
            placeholder="Search permissions..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="block w-full pl-3 pr-3 py-2 border border-gray-300 rounded-md text-sm placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
            disabled={disabled}
          />
        </div>
      </div>

      {/* Permission Groups */}
      <div className="max-h-96 overflow-y-auto">
        {filteredGroups.length === 0 ? (
          <div className="p-4 text-center text-gray-500 text-sm">
            No permissions found matching your search.
          </div>
        ) : (
          filteredGroups.map((group) => (
            <div key={group.name} className="border-b border-gray-200 last:border-b-0">
              <div 
                className="flex items-center justify-between p-4 bg-gray-50 cursor-pointer hover:bg-gray-100 transition-colors"
                onClick={() => toggleGroup(group.name)}
              >
                <div className="flex items-center flex-1">
                  <div 
                    className={`w-5 h-5 mr-3 border rounded flex items-center justify-center transition-colors ${
                      group.permissions.every(p => selectedPermissions.includes(p.id) || selectedPermissions.includes('*'))
                        ? 'bg-blue-500 border-blue-500 text-white'
                        : group.permissions.some(p => selectedPermissions.includes(p.id))
                        ? 'bg-blue-200 border-blue-300'
                        : 'border-gray-300 hover:border-gray-400'
                    }`}
                    onClick={(e) => {
                      e.stopPropagation();
                      if (group.name !== 'Super Admin') {
                        toggleGroupPermissions(group.permissions);
                      }
                    }}
                  >
                    {group.permissions.every(p => selectedPermissions.includes(p.id) || selectedPermissions.includes('*')) && (
                      <CheckIcon className="h-3 w-3" />
                    )}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center">
                      <span className="font-medium text-gray-900">{group.name}</span>
                      <span className="ml-2 text-xs text-gray-500">
                        ({group.permissions.length} permissions)
                      </span>
                    </div>
                    <p className="text-xs text-gray-600 mt-1">{group.description}</p>
                  </div>
                </div>
                <svg 
                  className={`h-5 w-5 text-gray-500 transform transition-transform ${expandedGroups[group.name] ? 'rotate-180' : ''}`} 
                  fill="none" 
                  viewBox="0 0 24 24" 
                  stroke="currentColor"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </div>
              
              {expandedGroups[group.name] && (
                <div className="bg-white">
                  {group.permissions.map((permission) => (
                    <div 
                      key={permission.id}
                      className="flex items-start p-3 hover:bg-gray-50 cursor-pointer transition-colors border-t border-gray-100"
                      onClick={() => togglePermission(permission.id)}
                    >
                      <div 
                        className={`w-4 h-4 mt-0.5 mr-3 border rounded flex items-center justify-center flex-shrink-0 transition-colors ${
                          selectedPermissions.includes(permission.id) || selectedPermissions.includes('*')
                            ? 'bg-blue-500 border-blue-500 text-white'
                            : 'border-gray-300 hover:border-gray-400'
                        }`}
                      >
                        {(selectedPermissions.includes(permission.id) || selectedPermissions.includes('*')) && (
                          <CheckIcon className="h-2.5 w-2.5" />
                        )}
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center">
                          <span className="text-sm font-medium text-gray-900">{permission.name}</span>
                          {permission.id === '*' && (
                            <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800">
                              Dangerous
                            </span>
                          )}
                        </div>
                        <p className="text-xs text-gray-600 mt-1">{permission.description}</p>
                        <code className="text-xs text-gray-500 bg-gray-100 px-1 py-0.5 rounded mt-1 inline-block">
                          {permission.id}
                        </code>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))
        )}
      </div>

      {/* Footer with quick actions */}
      {!disabled && (
        <div className="bg-gray-50 px-4 py-3 border-t border-gray-200">
          <div className="flex justify-between items-center">
            <div className="flex space-x-2">
              <button
                type="button"
                onClick={() => onChange([])}
                className="text-xs text-gray-600 hover:text-gray-800 underline"
              >
                Clear All
              </button>
              <button
                type="button"
                onClick={() => {
                  const readOnlyPermissions = permissionGroups
                    .flatMap(group => group.permissions)
                    .filter(p => p.id.endsWith(':read'))
                    .map(p => p.id);
                  onChange(readOnlyPermissions);
                }}
                className="text-xs text-blue-600 hover:text-blue-800 underline"
              >
                Read Only
              </button>
            </div>
            <span className="text-xs text-gray-500">
              {selectedPermissions.includes('*') ? 'Super Admin Mode' : `${selectedPermissions.length} selected`}
            </span>
          </div>
        </div>
      )}
    </div>
  );
}
