'use client';

import React, { useState } from 'react';
import { CreditCardIcon, XMarkIcon, CheckCircleIcon, ArrowPathIcon, InformationCircleIcon } from '@heroicons/react/24/outline';
import { parseTransactionMessage } from '@/utils/transactionParser';

interface Receipt {
  id: string;
  receiptNumber: string;
  totalAmount: number;
  amountPaid: number;
  balance: number;
  customerName: string;
  status: string;
}

interface AddPaymentFormProps {
  receipt: Receipt;
  isOpen: boolean;
  onClose: () => void;
  onPaymentAdded: (updatedReceipt: any) => void;
}

export default function AddPaymentForm({ receipt, isOpen, onClose, onPaymentAdded }: AddPaymentFormProps) {
  const [paymentMethod, setPaymentMethod] = useState<'manual' | 'mpesa'>('manual');
  const [amount, setAmount] = useState('');
  const [paymentDateTime, setPaymentDateTime] = useState('');
  const [mpesaMessage, setMpesaMessage] = useState('');
  const [parsedData, setParsedData] = useState<any>(null);
  const [notes, setNotes] = useState('');
  const [submitting, setSubmitting] = useState(false);
  const [parsing, setParsing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Debug: Log receipt data when form opens
  console.log('[AddPaymentForm] Receipt data:', {
    id: receipt.id,
    totalAmount: receipt.totalAmount,
    amountPaid: receipt.amountPaid,
    balance: receipt.balance,
    isOpen
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);
    setError(null);

    try {
      console.log(`[AddPaymentForm] Submitting payment for receipt: ${receipt.id}`);
      console.log(`[AddPaymentForm] Payment amount: ${amount}`);
      
      // Prepare request body based on payment method
      const requestBody = paymentMethod === 'mpesa' 
        ? {
            mpesaMessage: mpesaMessage.trim(),
            notes: notes.trim() || undefined,
            allowOverpayment: true // Allow overpayments for tips
          }
        : {
            amount: parseFloat(amount),
            paymentDateTime: paymentDateTime || undefined,
            notes: notes.trim() || undefined,
            allowOverpayment: true // Allow overpayments for manual payments too
          };

      const response = await fetch(`/api/admin/comprehensive-receipts/${receipt.id}/add-payment`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      console.log(`[AddPaymentForm] Response status:`, response.status);

      if (!response.ok) {
        let errorDetails = `HTTP ${response.status}`;
        try {
          const errorData = await response.json();
          errorDetails = errorData.error || errorData.message || errorDetails;
        } catch (parseError) {
          try {
            const errorText = await response.text();
            if (errorText) {
              errorDetails = errorText;
            }
          } catch (textError) {
            // Use default error
          }
        }
        throw new Error(`Failed to add payment: ${errorDetails}`);
      }

      const data = await response.json();
      console.log(`[AddPaymentForm] API response:`, data);

      if (!data || !data.success) {
        const errorMessage = data?.error || data?.message || 'Failed to add payment - no success response';
        console.error(`[AddPaymentForm] API error:`, errorMessage);
        throw new Error(errorMessage);
      }

      console.log(`[AddPaymentForm] Payment added successfully`);
      onPaymentAdded(data.data);
      handleClose();
    } catch (err) {
      console.error('[AddPaymentForm] Full error object:', err);
      
      let errorMessage = 'Failed to add payment';
      
      if (err instanceof Error) {
        errorMessage = err.message;
      } else if (typeof err === 'string') {
        errorMessage = err;
      } else if (err && typeof err === 'object' && 'message' in err) {
        errorMessage = (err as any).message;
      }
      
      console.error('[AddPaymentForm] Setting error message:', errorMessage);
      setError(errorMessage);
    } finally {
      setSubmitting(false);
    }
  };

  const handleParseMessage = async () => {
    if (!mpesaMessage.trim()) {
      setError('Please enter an M-Pesa transaction message');
      return;
    }

    setParsing(true);
    setError(null);

    try {
      console.log('[AddPaymentForm] Parsing M-Pesa message:', mpesaMessage.substring(0, 100) + '...');
      const parsed = parseTransactionMessage(mpesaMessage);
      if (!parsed) {
        throw new Error('Failed to parse M-Pesa message. Please ensure it contains transaction ID, amount, customer name, and phone number.');
      }
      
      console.log('[AddPaymentForm] Successfully parsed:', parsed);
      setParsedData(parsed);
      setAmount(parsed.amount.toString()); // Set amount for validation
      
    } catch (err) {
      console.error('Error parsing M-Pesa message:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to parse M-Pesa message';
      setError(errorMessage);
    } finally {
      setParsing(false);
    }
  };

  const handleClose = () => {
    if (!submitting) {
      setPaymentMethod('manual');
      setAmount('');
      setPaymentDateTime('');
      setMpesaMessage('');
      setParsedData(null);
      setNotes('');
      setError(null);
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" onClick={onClose}>
      <div className="relative top-20 mx-auto p-4 sm:p-5 border border-gray-300 w-full max-w-md sm:max-w-lg bg-white rounded-lg shadow-lg" onClick={e => e.stopPropagation()}>
        <div className="flex items-center justify-between pb-3 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Add Payment</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        <div className="mt-4 space-y-4">
          {/* Receipt Info */}
          <div className="bg-gray-50 p-3 sm:p-4 rounded-lg">
            <h4 className="text-sm font-medium text-gray-900 mb-2">Receipt Details</h4>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Receipt:</span>
                <span className="font-medium text-gray-900 break-all ml-2">{receipt.receiptNumber}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Customer:</span>
                <span className="font-medium text-gray-900 break-words ml-2">{receipt.customerName}</span>
              </div>
            </div>
          </div>

          {/* Payment Summary */}
          <div className="bg-blue-50 p-3 sm:p-4 rounded-lg">
            <h4 className="text-sm font-medium text-gray-900 mb-3">Payment Summary</h4>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Total Amount:</span>
                <span className="text-sm font-medium text-gray-900">KES {receipt.totalAmount.toLocaleString()}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Already Paid:</span>
                <span className="text-sm font-medium text-green-600">KES {receipt.amountPaid.toLocaleString()}</span>
              </div>
              <div className="flex justify-between border-t border-blue-200 pt-2">
                <span className="text-sm font-medium text-gray-900">Outstanding:</span>
                <span className="text-sm font-medium text-red-600">KES {receipt.balance.toLocaleString()}</span>
              </div>
            </div>
          </div>

          {/* Payment Method Selection */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Payment Method
            </label>
            <div className="flex space-x-4">
              <label className="flex items-center">
                <input
                  type="radio"
                  name="paymentMethod"
                  value="manual"
                  checked={paymentMethod === 'manual'}
                  onChange={(e) => setPaymentMethod(e.target.value as 'manual' | 'mpesa')}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                />
                <span className="ml-2 text-sm text-gray-700">Manual Amount</span>
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  name="paymentMethod"
                  value="mpesa"
                  checked={paymentMethod === 'mpesa'}
                  onChange={(e) => setPaymentMethod(e.target.value as 'manual' | 'mpesa')}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                />
                <span className="ml-2 text-sm text-gray-700">M-Pesa Message</span>
              </label>
            </div>
          </div>

          {/* Payment Form */}
          <form onSubmit={handleSubmit} className="space-y-4">
            {paymentMethod === 'manual' ? (
              <div className="space-y-4">
                <div>
                  <label htmlFor="amount" className="block text-sm font-medium text-gray-700 mb-2">
                    Payment Amount (KES)
                  </label>
                  <input
                    type="number"
                    id="amount"
                    min="0"
                    step="0.01"
                    value={amount}
                    onChange={(e) => setAmount(e.target.value)}
                    className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    placeholder="0.00"
                    required
                  />
                </div>
                <div>
                  <label htmlFor="paymentDateTime" className="block text-sm font-medium text-gray-700 mb-2">
                    Payment Date & Time (Optional)
                  </label>
                  <input
                    type="datetime-local"
                    id="paymentDateTime"
                    value={paymentDateTime}
                    onChange={(e) => setPaymentDateTime(e.target.value)}
                    className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    max={new Date().toISOString().slice(0, 16)} // Prevent future dates
                  />
                  <p className="mt-1 text-xs text-gray-500">
                    Leave empty to use current date and time
                  </p>
                </div>
              </div>
            ) : (
              <div>
                <label htmlFor="mpesaMessage" className="block text-sm font-medium text-gray-700 mb-2">
                  M-Pesa Transaction Message
                </label>
                <div className="relative">
                  <textarea
                    id="mpesaMessage"
                    rows={4}
                    value={mpesaMessage}
                    onChange={(e) => setMpesaMessage(e.target.value)}
                    placeholder="Paste the M-Pesa transaction message here..."
                    className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    disabled={!!parsedData}
                  />
                  {!parsedData && (
                    <div className="absolute right-2 bottom-2">
                      <button
                        type="button"
                        onClick={handleParseMessage}
                        disabled={parsing || !mpesaMessage.trim()}
                        className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {parsing ? (
                          <>
                            <ArrowPathIcon className="animate-spin -ml-0.5 mr-1 h-3 w-3" />
                            Parsing...
                          </>
                        ) : (
                          <>
                            <CheckCircleIcon className="-ml-0.5 mr-1 h-3 w-3" />
                            Parse
                          </>
                        )}
                      </button>
                    </div>
                  )}
                </div>
                {parsedData && (
                  <div className="mt-2 p-3 bg-green-50 border border-green-200 rounded-md">
                    <div className="flex items-center">
                      <CheckCircleIcon className="h-4 w-4 text-green-500 mr-2" />
                      <span className="text-sm text-green-700">
                        Parsed: {parsedData.transactionId} - KES {parsedData.amount.toLocaleString()} from {parsedData.customerName}
                      </span>
                      <button
                        type="button"
                        onClick={() => {
                          setParsedData(null);
                          setAmount('');
                        }}
                        className="ml-auto text-xs text-green-600 hover:text-green-800"
                      >
                        Reset
                      </button>
                    </div>
                  </div>
                )}
              </div>
            )}

            <div>
              <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-2">
                Payment Notes (Optional)
              </label>
              <textarea
                id="notes"
                rows={3}
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder="Enter any additional notes about this payment..."
              />
            </div>

            {/* Warning for overpayment */}
            {amount && parseFloat(amount) > receipt.balance && (
              <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
                <div className="flex items-start">
                  <div className="flex-shrink-0">
                    <InformationCircleIcon className="h-5 w-5 text-blue-400" />
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-blue-800">Overpayment Notice</h3>
                    <div className="mt-2 text-sm text-blue-700">
                      <p>The payment amount exceeds the outstanding balance.</p>
                      <p>Any amount above the outstanding balance will be recorded as a credit/tip on this receipt.</p>
                      {paymentMethod === 'mpesa' && parsedData && (
                        <p className="mt-1 font-medium">
                          Credit amount: KES {(parsedData.amount - receipt.balance).toLocaleString()}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Different payer warning for M-Pesa */}
            {paymentMethod === 'mpesa' && parsedData && parsedData.customerName.toLowerCase() !== receipt.customerName.toLowerCase() && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3">
                <div className="flex items-start">
                  <div className="flex-shrink-0">
                    <InformationCircleIcon className="h-5 w-5 text-yellow-400" />
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-yellow-800">Different Payer Detected</h3>
                    <div className="mt-2 text-sm text-yellow-700">
                      <p>Original customer: <span className="font-medium">{receipt.customerName}</span></p>
                      <p>Payment from: <span className="font-medium">{parsedData.customerName}</span></p>
                      <p>This payment will be recorded as received from {parsedData.customerName}.</p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Fully paid warning */}
            {receipt.balance <= 0 && (
              <div className="bg-green-50 border border-green-200 rounded-md p-3">
                <div className="flex items-start">
                  <div className="flex-shrink-0">
                    <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div className="ml-3">
                    <h3 className="text-sm font-medium text-green-800">Receipt Fully Paid</h3>
                    <div className="mt-2 text-sm text-green-700">
                      <p><strong>Tip:</strong> This receipt has already been marked as fully paid.</p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="flex-1 inline-flex justify-center items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={submitting || (paymentMethod === 'manual' ? (!amount || parseFloat(amount) <= 0) : (!parsedData || !mpesaMessage.trim()))}
                className="flex-1 inline-flex justify-center items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {submitting ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Processing...
                  </>
                ) : (
                  <>
                    <CreditCardIcon className="h-4 w-4 mr-2" />
                    Add Payment
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
} 