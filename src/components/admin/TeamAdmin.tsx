'use client';

import React, { useEffect, useState } from 'react';
import Image from 'next/image';
import { FaLinkedin, FaTwitter, FaGithub, FaEnvelope, FaEdit, FaTrash, FaPlus } from 'react-icons/fa';

interface TeamMember {
  id: string;
  name: string;
  role: string;
  bio: string;
  imageSrc: string;
  order: number;
  linkedinUrl?: string;
  twitterUrl?: string;
  githubUrl?: string;
  emailAddress?: string;
}

const initialForm: Omit<TeamMember, 'id'> & { image?: File | null } = {
  name: '',
  role: '',
  bio: '',
  imageSrc: '',
  order: 0,
  linkedinUrl: '',
  twitterUrl: '',
  githubUrl: '',
  emailAddress: '',
  image: null,
};

export default function TeamAdmin() {
  const [team, setTeam] = useState<TeamMember[]>([]);
  const [form, setForm] = useState(initialForm);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [preview, setPreview] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  useEffect(() => {
    const initializePage = async () => {
      // Fetch CSRF token first
      // Then fetch team data
      await fetchTeam();
    };
    
    initializePage();
  }, [fetchCsrfToken]);

  async function fetchTeam() {
    setLoading(true);
    setError('');
    try {
      const res = await fetch('/api/team');
      const data = await res.json();
      setTeam(Array.isArray(data) ? data : []);
    } catch (err) {
      setError('Failed to load team members');
    } finally {
      setLoading(false);
    }
  }

  function handleInput(e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) {
    const { name, value } = e.target;
    setForm(f => ({ ...f, [name]: value }));
  }

  function handleImage(e: React.ChangeEvent<HTMLInputElement>) {
    const file = e.target.files?.[0] || null;
    setForm(f => ({ ...f, image: file }));
    if (file) {
      setPreview(URL.createObjectURL(file));
    } else {
      setPreview('');
    }
  }

  function startEdit(member: TeamMember) {
    setEditingId(member.id);
    setForm({ ...member, image: null });
    setPreview(member.imageSrc);
  }

  function resetForm() {
    setEditingId(null);
    setForm(initialForm);
    setPreview('');
  }

  async function handleSubmit(e: React.FormEvent) {
    e.preventDefault();
    setLoading(true);
    setError('');
    
    try {
      // Ensure we have a CSRF token
      if (!csrfToken) {
      }

      let imageSrc = form.imageSrc;
      if (form.image) {
        // Upload image to S3/local with CSRF protection
        const formData = new FormData();
        formData.append('file', form.image);
        
        const uploadRes = await fetch('/api/upload', { 
          method: 'POST', 
          headers: {
            'x-csrf-token': csrfToken || '',
          },
          credentials: 'include',
          body: formData 
        });
        
        if (!uploadRes.ok) {
          throw new Error('Failed to upload image');
        }
        
        const uploadData = await uploadRes.json();
        console.log('Upload response:', uploadData);
        
        // Handle both old and new response formats
        imageSrc = uploadData.url || uploadData.path || '';
        
        if (!imageSrc) {
          throw new Error('No image URL returned from upload');
        }
      }
      
      const payload = { ...form, imageSrc };
      
      if (editingId) {
        const response = await fetch('/api/team', {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          credentials: 'include',
          body: JSON.stringify({ id: editingId, ...payload }),
        });
        
        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(errorData.error || 'Failed to update team member');
        }
        
        const responseData = await response.json();
        console.log('Update response:', responseData);
      } else {
        const response = await fetch('/api/team', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          credentials: 'include',
          body: JSON.stringify(payload),
        });
        
        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(errorData.error || 'Failed to create team member');
        }
        
        const responseData = await response.json();
        console.log('Create response:', responseData);
      }
      
      await fetchTeam();
      resetForm();
    } catch (err) {
      console.error('Error saving team member:', err);
      setError(err instanceof Error ? err.message : 'Failed to save team member');
    } finally {
      setLoading(false);
    }
  }

  async function handleDelete(id: string) {
    if (!confirm('Delete this team member?')) return;
    setLoading(true);
    setError('');
    
    try {
      // Ensure we have a CSRF token
      if (!csrfToken) {
      }

      const response = await fetch('/api/team', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({ id }),
      });
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Failed to delete team member');
      }
      
      const responseData = await response.json();
      console.log('Delete response:', responseData);
      
      await fetchTeam();
    } catch (err) {
      console.error('Error deleting team member:', err);
      setError(err instanceof Error ? err.message : 'Failed to delete team member');
    } finally {
      setLoading(false);
    }
  }

  return (
    <div className="max-w-4xl mx-auto py-10">
      <h2 className="text-2xl font-bold mb-6">Team Members Admin</h2>
      {error && <div className="mb-4 text-red-500">{error}</div>}
      <form onSubmit={handleSubmit} className="bg-white rounded-lg shadow p-6 mb-8">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block font-medium mb-1">Name</label>
            <input name="name" value={form.name} onChange={handleInput} className="w-full border rounded px-3 py-2" required />
          </div>
          <div>
            <label className="block font-medium mb-1">Role</label>
            <input name="role" value={form.role} onChange={handleInput} className="w-full border rounded px-3 py-2" required />
          </div>
          <div className="md:col-span-2">
            <label className="block font-medium mb-1">Bio</label>
            <textarea name="bio" value={form.bio} onChange={handleInput} className="w-full border rounded px-3 py-2" rows={3} required />
          </div>
          <div>
            <label className="block font-medium mb-1">Order</label>
            <input name="order" type="number" value={form.order} onChange={handleInput} className="w-full border rounded px-3 py-2" min={0} />
          </div>
          <div>
            <label className="block font-medium mb-1">LinkedIn</label>
            <input name="linkedinUrl" value={form.linkedinUrl || ''} onChange={handleInput} className="w-full border rounded px-3 py-2" />
          </div>
          <div>
            <label className="block font-medium mb-1">Twitter</label>
            <input name="twitterUrl" value={form.twitterUrl || ''} onChange={handleInput} className="w-full border rounded px-3 py-2" />
          </div>
          <div>
            <label className="block font-medium mb-1">GitHub</label>
            <input name="githubUrl" value={form.githubUrl || ''} onChange={handleInput} className="w-full border rounded px-3 py-2" />
          </div>
          <div>
            <label className="block font-medium mb-1">Email</label>
            <input name="emailAddress" value={form.emailAddress || ''} onChange={handleInput} className="w-full border rounded px-3 py-2" />
          </div>
          <div>
            <label className="block font-medium mb-1">Image</label>
            <input type="file" accept="image/*" onChange={handleImage} className="w-full" />
            {preview && (
              <div className="mt-2">
                <Image src={preview} alt="Preview" width={80} height={80} className="rounded-full object-cover" />
              </div>
            )}
          </div>
        </div>
        <div className="mt-4 flex gap-2">
          <button type="submit" className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700" disabled={loading}>
            {editingId ? 'Update' : 'Add'} Member
          </button>
          {editingId && (
            <button type="button" onClick={resetForm} className="bg-gray-200 px-4 py-2 rounded hover:bg-gray-300">Cancel</button>
          )}
        </div>
      </form>
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
          <FaPlus className="text-green-500" /> Team Members
        </h3>
        {loading ? (
          <div>Loading...</div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {team.map(member => (
              <div key={member.id} className="flex items-center gap-4 border-b pb-4 mb-4">
                <Image src={member.imageSrc || '/images/placeholder.jpg'} alt={member.name} width={64} height={64} className="rounded-full object-cover" />
                <div className="flex-1">
                  <div className="font-bold">{member.name}</div>
                  <div className="text-sm text-gray-600">{member.role}</div>
                  <div className="text-xs text-gray-500 line-clamp-2">{member.bio}</div>
                  <div className="flex gap-2 mt-1">
                    {member.linkedinUrl && <a href={member.linkedinUrl} target="_blank" rel="noopener noreferrer"><FaLinkedin /></a>}
                    {member.twitterUrl && <a href={member.twitterUrl} target="_blank" rel="noopener noreferrer"><FaTwitter /></a>}
                    {member.githubUrl && <a href={member.githubUrl} target="_blank" rel="noopener noreferrer"><FaGithub /></a>}
                    {member.emailAddress && <a href={`mailto:${member.emailAddress}`}><FaEnvelope /></a>}
                  </div>
                </div>
                <button onClick={() => startEdit(member)} className="text-blue-500 hover:text-blue-700"><FaEdit /></button>
                <button onClick={() => handleDelete(member.id)} className="text-red-500 hover:text-red-700"><FaTrash /></button>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
} 