'use client';

import React, { useState, useRef, useCallback } from 'react';
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Placeholder from '@tiptap/extension-placeholder';
import Link from '@tiptap/extension-link';
import Image from '@tiptap/extension-image';
import TextAlign from '@tiptap/extension-text-align';
import Highlight from '@tiptap/extension-highlight';
import Typography from '@tiptap/extension-typography';
import CodeBlockLowlight from '@tiptap/extension-code-block-lowlight';
import { createLowlight } from 'lowlight';
import {
  Bold,
  Italic,
  Underline,
  Strikethrough,
  List,
  ListOrdered,
  Quote,
  Undo,
  Redo,
  Link as LinkIcon,
  ImageIcon,
  Heading1,
  Heading2,
  Heading3,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Code,
  Highlighter,
  Separator,
  Upload,
  X,
  Check,
  AlertCircle
} from 'lucide-react';

// Initialize syntax highlighting
const lowlight = createLowlight();

interface AdvancedRichTextEditorProps {
  content: string;
  onChange: (content: string) => void;
  placeholder?: string;
  minHeight?: string;
}

interface ImageUploadState {
  uploading: boolean;
  progress: number;
  error: string | null;
  success: boolean;
}

export default function AdvancedRichTextEditor({ 
  content, 
  onChange, 
  placeholder = 'Start writing your blog post... Use the toolbar above for formatting and drag & drop images directly into the editor.',
  minHeight = '500px'
}: AdvancedRichTextEditorProps) {
  const [imageUpload, setImageUpload] = useState<ImageUploadState>({
    uploading: false,
    progress: 0,
    error: null,
    success: false
  });
  const [showLinkDialog, setShowLinkDialog] = useState(false);
  const [linkUrl, setLinkUrl] = useState('');
  const fileInputRef = useRef<HTMLInputElement>(null);

  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        codeBlock: false, // We'll use CodeBlockLowlight instead
      }),
      Placeholder.configure({
        placeholder,
      }),
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'text-blue-600 hover:text-blue-800 underline cursor-pointer',
        },
      }),
      Image.configure({
        HTMLAttributes: {
          class: 'rounded-lg shadow-md max-w-full h-auto my-4',
        },
      }),
      TextAlign.configure({
        types: ['heading', 'paragraph'],
      }),
      Highlight.configure({
        HTMLAttributes: {
          class: 'bg-yellow-200 px-1 rounded',
        },
      }),
      Typography,
      CodeBlockLowlight.configure({
        lowlight,
        HTMLAttributes: {
          class: 'bg-gray-100 border rounded-lg p-4 my-4 font-mono text-sm overflow-x-auto',
        },
      }),
    ],
    content,
    onUpdate: ({ editor }) => {
      onChange(editor.getHTML());
    },
    editorProps: {
      attributes: {
        class: 'prose prose-lg max-w-none focus:outline-none',
        style: `min-height: ${minHeight}; padding: 1.5rem;`,
      },
      handleDrop: (view, event, slice, moved) => {
        if (!moved && event.dataTransfer && event.dataTransfer.files && event.dataTransfer.files.length > 0) {
          const file = Array.from(event.dataTransfer.files).find(file => 
            file.type.startsWith('image/')
          );
          if (file) {
            event.preventDefault();
            handleImageUpload(file);
            return true;
          }
        }
        return false;
      },
      handlePaste: (view, event, slice) => {
        const items = Array.from(event.clipboardData?.items || []);
        const imageItem = items.find(item => item.type.startsWith('image/'));
        
        if (imageItem) {
          event.preventDefault();
          const file = imageItem.getAsFile();
          if (file) {
            handleImageUpload(file);
          }
          return true;
        }
        return false;
      },
    },
  });

  const resetImageUploadState = useCallback(() => {
    setImageUpload({
      uploading: false,
      progress: 0,
      error: null,
      success: false
    });
  }, []);

  const handleImageUpload = useCallback(async (file: File) => {
    if (!editor) return;

    // Validate file
    const maxSize = 15 * 1024 * 1024; // 15MB
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif', 'image/avif'];

    if (!allowedTypes.includes(file.type)) {
      setImageUpload(prev => ({
        ...prev,
        error: `Unsupported file type. Please use: ${allowedTypes.map(t => t.split('/')[1].toUpperCase()).join(', ')}`
      }));
      return;
    }

    if (file.size > maxSize) {
      setImageUpload(prev => ({
        ...prev,
        error: `File too large (${(file.size / 1024 / 1024).toFixed(1)}MB). Maximum size is 15MB.`
      }));
      return;
    }

    resetImageUploadState();
    setImageUpload(prev => ({ ...prev, uploading: true }));

    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('type', 'blog');

      // Simulate progress
      const progressInterval = setInterval(() => {
        setImageUpload(prev => ({
          ...prev,
          progress: Math.min(prev.progress + Math.random() * 15, 85)
        }));
      }, 300);

      const response = await fetch('/api/admin/upload', {
        method: 'POST',
        body: formData,
      });

      clearInterval(progressInterval);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Upload failed' }));
        throw new Error(errorData.error || 'Failed to upload image');
      }

      const data = await response.json();
      
      setImageUpload(prev => ({ ...prev, progress: 100, success: true }));

      // Insert image into editor
      editor.chain().focus().setImage({ src: data.url, alt: file.name }).run();

      // Clear success state after 2 seconds
      setTimeout(() => {
        resetImageUploadState();
      }, 2000);

    } catch (error) {
      console.error('Error uploading image:', error);
      setImageUpload(prev => ({
        ...prev,
        uploading: false,
        error: error instanceof Error ? error.message : 'Failed to upload image. Please try again.'
      }));
    }
  }, [editor, resetImageUploadState]);

  const handleFileInputChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      handleImageUpload(file);
    }
    // Reset input
    event.target.value = '';
  }, [handleImageUpload]);

  const openImagePicker = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  const addLink = useCallback(() => {
    if (editor) {
      const { from, to } = editor.state.selection;
      const selectedText = editor.state.doc.textBetween(from, to);
      setLinkUrl(selectedText || '');
      setShowLinkDialog(true);
    }
  }, [editor]);

  const insertLink = useCallback(() => {
    if (editor && linkUrl.trim()) {
      editor
        .chain()
        .focus()
        .extendMarkRange('link')
        .setLink({ href: linkUrl.trim() })
        .run();
    }
    setShowLinkDialog(false);
    setLinkUrl('');
  }, [editor, linkUrl]);

  const removeLink = useCallback(() => {
    if (editor) {
      editor.chain().focus().unsetLink().run();
    }
  }, [editor]);

  if (!editor) {
    return (
      <div className="border border-gray-300 rounded-lg overflow-hidden">
        <div className="bg-gray-50 border-b border-gray-300 p-4 animate-pulse">
          <div className="h-10 bg-gray-200 rounded"></div>
        </div>
        <div className="p-6">
          <div className="h-96 bg-gray-100 rounded animate-pulse"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="border border-gray-300 rounded-lg overflow-hidden shadow-sm">
      {/* Toolbar */}
      <div className="bg-gray-50 border-b border-gray-300 p-3">
        <div className="flex flex-wrap items-center gap-1">
          {/* Text Formatting */}
          <div className="flex items-center gap-1 mr-2">
            <button
              onClick={() => editor.chain().focus().toggleBold().run()}
              className={`p-2 rounded hover:bg-gray-200 transition-colors ${
                editor.isActive('bold') ? 'bg-gray-200 text-blue-600' : ''
              }`}
              title="Bold (Ctrl+B)"
            >
              <Bold className="w-4 h-4" />
            </button>
            <button
              onClick={() => editor.chain().focus().toggleItalic().run()}
              className={`p-2 rounded hover:bg-gray-200 transition-colors ${
                editor.isActive('italic') ? 'bg-gray-200 text-blue-600' : ''
              }`}
              title="Italic (Ctrl+I)"
            >
              <Italic className="w-4 h-4" />
            </button>
            <button
              onClick={() => editor.chain().focus().toggleStrike().run()}
              className={`p-2 rounded hover:bg-gray-200 transition-colors ${
                editor.isActive('strike') ? 'bg-gray-200 text-blue-600' : ''
              }`}
              title="Strikethrough"
            >
              <Strikethrough className="w-4 h-4" />
            </button>
            <button
              onClick={() => editor.chain().focus().toggleHighlight().run()}
              className={`p-2 rounded hover:bg-gray-200 transition-colors ${
                editor.isActive('highlight') ? 'bg-gray-200 text-yellow-600' : ''
              }`}
              title="Highlight"
            >
              <Highlighter className="w-4 h-4" />
            </button>
          </div>

          <div className="w-px h-6 bg-gray-300 mx-1" />

          {/* Headings */}
          <div className="flex items-center gap-1 mr-2">
            <button
              onClick={() => editor.chain().focus().toggleHeading({ level: 1 }).run()}
              className={`p-2 rounded hover:bg-gray-200 transition-colors ${
                editor.isActive('heading', { level: 1 }) ? 'bg-gray-200 text-blue-600' : ''
              }`}
              title="Heading 1"
            >
              <Heading1 className="w-4 h-4" />
            </button>
            <button
              onClick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
              className={`p-2 rounded hover:bg-gray-200 transition-colors ${
                editor.isActive('heading', { level: 2 }) ? 'bg-gray-200 text-blue-600' : ''
              }`}
              title="Heading 2"
            >
              <Heading2 className="w-4 h-4" />
            </button>
            <button
              onClick={() => editor.chain().focus().toggleHeading({ level: 3 }).run()}
              className={`p-2 rounded hover:bg-gray-200 transition-colors ${
                editor.isActive('heading', { level: 3 }) ? 'bg-gray-200 text-blue-600' : ''
              }`}
              title="Heading 3"
            >
              <Heading3 className="w-4 h-4" />
            </button>
          </div>

          <div className="w-px h-6 bg-gray-300 mx-1" />

          {/* Lists and Quote */}
          <div className="flex items-center gap-1 mr-2">
            <button
              onClick={() => editor.chain().focus().toggleBulletList().run()}
              className={`p-2 rounded hover:bg-gray-200 transition-colors ${
                editor.isActive('bulletList') ? 'bg-gray-200 text-blue-600' : ''
              }`}
              title="Bullet List"
            >
              <List className="w-4 h-4" />
            </button>
            <button
              onClick={() => editor.chain().focus().toggleOrderedList().run()}
              className={`p-2 rounded hover:bg-gray-200 transition-colors ${
                editor.isActive('orderedList') ? 'bg-gray-200 text-blue-600' : ''
              }`}
              title="Numbered List"
            >
              <ListOrdered className="w-4 h-4" />
            </button>
            <button
              onClick={() => editor.chain().focus().toggleBlockquote().run()}
              className={`p-2 rounded hover:bg-gray-200 transition-colors ${
                editor.isActive('blockquote') ? 'bg-gray-200 text-blue-600' : ''
              }`}
              title="Quote"
            >
              <Quote className="w-4 h-4" />
            </button>
            <button
              onClick={() => editor.chain().focus().toggleCodeBlock().run()}
              className={`p-2 rounded hover:bg-gray-200 transition-colors ${
                editor.isActive('codeBlock') ? 'bg-gray-200 text-blue-600' : ''
              }`}
              title="Code Block"
            >
              <Code className="w-4 h-4" />
            </button>
          </div>

          <div className="w-px h-6 bg-gray-300 mx-1" />

          {/* Alignment */}
          <div className="flex items-center gap-1 mr-2">
            <button
              onClick={() => editor.chain().focus().setTextAlign('left').run()}
              className={`p-2 rounded hover:bg-gray-200 transition-colors ${
                editor.isActive({ textAlign: 'left' }) ? 'bg-gray-200 text-blue-600' : ''
              }`}
              title="Align Left"
            >
              <AlignLeft className="w-4 h-4" />
            </button>
            <button
              onClick={() => editor.chain().focus().setTextAlign('center').run()}
              className={`p-2 rounded hover:bg-gray-200 transition-colors ${
                editor.isActive({ textAlign: 'center' }) ? 'bg-gray-200 text-blue-600' : ''
              }`}
              title="Align Center"
            >
              <AlignCenter className="w-4 h-4" />
            </button>
            <button
              onClick={() => editor.chain().focus().setTextAlign('right').run()}
              className={`p-2 rounded hover:bg-gray-200 transition-colors ${
                editor.isActive({ textAlign: 'right' }) ? 'bg-gray-200 text-blue-600' : ''
              }`}
              title="Align Right"
            >
              <AlignRight className="w-4 h-4" />
            </button>
          </div>

          <div className="w-px h-6 bg-gray-300 mx-1" />

          {/* Links and Images */}
          <div className="flex items-center gap-1 mr-2">
            <button
              onClick={addLink}
              className={`p-2 rounded hover:bg-gray-200 transition-colors ${
                editor.isActive('link') ? 'bg-gray-200 text-blue-600' : ''
              }`}
              title="Add Link"
            >
              <LinkIcon className="w-4 h-4" />
            </button>
            <button
              onClick={openImagePicker}
              className="p-2 rounded hover:bg-gray-200 transition-colors"
              title="Upload Image"
              disabled={imageUpload.uploading}
            >
              {imageUpload.uploading ? (
                <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin" />
              ) : (
                <ImageIcon className="w-4 h-4" />
              )}
            </button>
          </div>

          <div className="flex-1" />

          {/* Undo/Redo */}
          <div className="flex items-center gap-1">
            <button
              onClick={() => editor.chain().focus().undo().run()}
              className="p-2 rounded hover:bg-gray-200 transition-colors"
              title="Undo (Ctrl+Z)"
              disabled={!editor.can().undo()}
            >
              <Undo className="w-4 h-4" />
            </button>
            <button
              onClick={() => editor.chain().focus().redo().run()}
              className="p-2 rounded hover:bg-gray-200 transition-colors"
              title="Redo (Ctrl+Y)"
              disabled={!editor.can().redo()}
            >
              <Redo className="w-4 h-4" />
            </button>
          </div>
        </div>

        {/* Image Upload Status */}
        {(imageUpload.uploading || imageUpload.error || imageUpload.success) && (
          <div className="mt-3 p-3 rounded-lg bg-white border">
            {imageUpload.uploading && (
              <div className="flex items-center gap-3">
                <Upload className="w-4 h-4 text-blue-600" />
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-sm text-gray-700">Uploading image...</span>
                    <span className="text-sm text-gray-500">{Math.round(imageUpload.progress)}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${imageUpload.progress}%` }}
                    />
                  </div>
                </div>
              </div>
            )}
            
            {imageUpload.error && (
              <div className="flex items-center gap-3 text-red-600">
                <AlertCircle className="w-4 h-4" />
                <span className="text-sm">{imageUpload.error}</span>
                <button
                  onClick={resetImageUploadState}
                  className="ml-auto p-1 hover:bg-red-50 rounded"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
            )}
            
            {imageUpload.success && (
              <div className="flex items-center gap-3 text-green-600">
                <Check className="w-4 h-4" />
                <span className="text-sm">Image uploaded successfully!</span>
              </div>
            )}
          </div>
        )}

        {/* Link Dialog */}
        {showLinkDialog && (
          <div className="mt-3 p-3 rounded-lg bg-white border">
            <div className="flex items-center gap-3">
              <LinkIcon className="w-4 h-4 text-blue-600" />
              <input
                type="text"
                value={linkUrl}
                onChange={(e) => setLinkUrl(e.target.value)}
                placeholder="Enter URL (e.g., https://example.com)"
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    insertLink();
                  } else if (e.key === 'Escape') {
                    setShowLinkDialog(false);
                    setLinkUrl('');
                  }
                }}
                autoFocus
              />
              <button
                onClick={insertLink}
                className="px-3 py-2 bg-blue-600 text-white rounded-md text-sm hover:bg-blue-700 transition-colors"
              >
                Add Link
              </button>
              {editor.isActive('link') && (
                <button
                  onClick={removeLink}
                  className="px-3 py-2 bg-red-600 text-white rounded-md text-sm hover:bg-red-700 transition-colors"
                >
                  Remove
                </button>
              )}
              <button
                onClick={() => {
                  setShowLinkDialog(false);
                  setLinkUrl('');
                }}
                className="p-2 hover:bg-gray-100 rounded-md transition-colors"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Editor Content */}
      <div className="relative">
        <EditorContent 
          editor={editor} 
          className="focus-within:bg-gray-50/30 transition-colors"
        />
        
        {/* Drop Zone Overlay */}
        <div className="absolute bottom-4 right-4 text-xs text-gray-400 bg-white/80 px-2 py-1 rounded">
          💡 Tip: Drag & drop images or paste from clipboard
        </div>
      </div>

      {/* Hidden File Input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileInputChange}
        className="hidden"
      />
    </div>
  );
} 