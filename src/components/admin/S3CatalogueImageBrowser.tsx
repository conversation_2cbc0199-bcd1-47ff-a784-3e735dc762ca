'use client';
import React from 'react';

import { useState, useEffect, useCallback } from 'react';
import Image from 'next/image';
import { 
  XMarkIcon, 
  MagnifyingGlassIcon, 
  PhotoIcon,
  ArrowPathIcon,
  FunnelIcon
} from '@heroicons/react/24/outline';

interface S3Image {
  key: string;
  filename: string;
  category: string;
  url: string;
  size: number;
  lastModified: string;
  displayName: string;
}

interface S3CatalogueImageBrowserProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectImage: (imageUrl: string, filename: string) => void;
  selectedCategory?: string;
  forceRefresh?: boolean;
}

// Catalogue categories for filtering
const CATALOGUE_CATEGORIES = [
  { value: 'all', label: 'All Categories' },
  { value: 'banners', label: 'Banners' },
  { value: 'drinkware', label: 'Drinkware' },
  { value: 'office-essentials', label: 'Office Essentials' },
  { value: 'diaries-notebooks', label: 'Diaries & Notebooks' },
  { value: 'gift-sets', label: 'Gift Sets' },
  { value: 'printing-services', label: 'Printing Services' },
  { value: 'digital-services', label: 'Digital Services' },
  { value: 'awards', label: 'Awards' },
  { value: 'general', label: 'General' }
];

export default function S3CatalogueImageBrowser({ 
  isOpen, 
  onClose, 
  onSelectImage, 
  selectedCategory,
  forceRefresh
}: S3CatalogueImageBrowserProps) {
  const [images, setImages] = useState<S3Image[]>([]);
  const [filteredImages, setFilteredImages] = useState<S3Image[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [searchQuery, setSearchQuery] = useState('');
  const [categoryFilter, setCategoryFilter] = useState(selectedCategory || 'all');

  // Fetch images from S3
  const fetchImages = useCallback(async () => {
    if (!isOpen) return;
    
    setLoading(true);
    setError('');

    try {
      const params = new URLSearchParams();
      if (categoryFilter !== 'all') {
        params.append('category', categoryFilter);
      }
      
      // Set prefix for catalogue images
      params.append('prefix', 'images/catalogue');
      
      // Add timestamp for cache busting - use a more aggressive timestamp when force refreshing
      if (forceRefresh) {
        params.append('t', `force_${Date.now()}`);
      } else {
        params.append('t', Date.now().toString());
      }

      const response = await fetch(`/api/admin/s3-catalogue-images?${params.toString()}`, {
        headers: {
          'Cache-Control': 'no-cache',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch catalogue images');
      }

      const data = await response.json();
      setImages(data);
    } catch (err) {
      console.error('Error fetching S3 catalogue images:', err);
      setError(err instanceof Error ? err.message : 'Failed to load catalogue images');
    } finally {
      setLoading(false);
    }
  }, [isOpen, categoryFilter, forceRefresh]);

  // Filter images based on search query
  useEffect(() => {
    let filtered = images;

    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(image => 
        image.filename.toLowerCase().includes(query) ||
        image.displayName.toLowerCase().includes(query) ||
        image.category.toLowerCase().includes(query)
      );
    }

    setFilteredImages(filtered);
  }, [images, searchQuery]);

  // Fetch images when modal opens or category changes
  useEffect(() => {
    fetchImages();
  }, [fetchImages]);

  // Update category filter when selectedCategory prop changes
  useEffect(() => {
    if (selectedCategory) {
      setCategoryFilter(selectedCategory);
    }
  }, [selectedCategory]);

  const handleImageSelect = (image: S3Image) => {
    onSelectImage(image.url, image.filename);
    onClose();
  };

  const handleCategoryChange = (category: string) => {
    setCategoryFilter(category);
    setSearchQuery(''); // Clear search when changing category
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div 
          className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
          onClick={onClose}
        />

        {/* Modal panel */}
        <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-6xl sm:w-full">
          {/* Header */}
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg leading-6 font-medium text-gray-900">
                  Browse Catalogue Images
                </h3>
                <p className="mt-1 text-sm text-gray-500">
                  Select an existing image from your S3 catalogue storage
                </p>
              </div>
              <button
                onClick={onClose}
                className="rounded-md text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-orange-500"
              >
                <XMarkIcon className="h-6 w-6" />
              </button>
            </div>

            {/* Filters */}
            <div className="mt-4 flex flex-col sm:flex-row gap-4">
              {/* Search */}
              <div className="flex-1 relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder="Search catalogue images..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-sm"
                />
              </div>

              {/* Category Filter */}
              <div className="flex items-center gap-2">
                <FunnelIcon className="h-5 w-5 text-gray-400" />
                <select
                  value={categoryFilter}
                  onChange={(e) => handleCategoryChange(e.target.value)}
                  className="block px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500 text-sm"
                >
                  {CATALOGUE_CATEGORIES.map(category => (
                    <option key={category.value} value={category.value}>
                      {category.label}
                    </option>
                  ))}
                </select>
              </div>

              {/* Refresh Button */}
              <button
                onClick={fetchImages}
                disabled={loading}
                className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:opacity-50"
              >
                <ArrowPathIcon className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
              </button>
            </div>
          </div>

          {/* Content */}
          <div className="bg-white px-4 pt-5 pb-4 sm:p-6" style={{ maxHeight: '60vh', overflowY: 'auto' }}>
            {loading ? (
              <div className="flex items-center justify-center py-12">
                <div className="text-center">
                  <ArrowPathIcon className="h-8 w-8 text-orange-500 animate-spin mx-auto" />
                  <p className="mt-2 text-sm text-gray-500">Loading catalogue images...</p>
                </div>
              </div>
            ) : error ? (
              <div className="flex items-center justify-center py-12">
                <div className="text-center">
                  <PhotoIcon className="h-12 w-12 text-gray-300 mx-auto" />
                  <p className="mt-2 text-sm text-red-600">{error}</p>
                  <button
                    onClick={fetchImages}
                    className="mt-2 text-sm text-orange-600 hover:text-orange-500"
                  >
                    Try again
                  </button>
                </div>
              </div>
            ) : filteredImages.length === 0 ? (
              <div className="flex items-center justify-center py-12">
                <div className="text-center">
                  <PhotoIcon className="h-12 w-12 text-gray-300 mx-auto" />
                  <p className="mt-2 text-sm text-gray-500">
                    {searchQuery ? 'No catalogue images match your search' : 'No catalogue images found'}
                  </p>
                  <p className="mt-1 text-xs text-gray-400">
                    Images should be in the "images/catalogue/" folder in your S3 bucket
                  </p>
                </div>
              </div>
            ) : (
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
                {filteredImages.map((image) => (
                  <div
                    key={image.key}
                    onClick={() => handleImageSelect(image)}
                    className="relative aspect-square bg-gray-100 rounded-lg overflow-hidden cursor-pointer hover:ring-2 hover:ring-orange-500 hover:ring-offset-2 transition-all group"
                  >
                    <Image
                      src={image.url}
                      alt={image.displayName}
                      fill
                      className="object-cover group-hover:scale-105 transition-transform"
                      sizes="(max-width: 640px) 50vw, (max-width: 768px) 33vw, (max-width: 1024px) 25vw, 16vw"
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all" />
                    <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/60 to-transparent p-2">
                      <p className="text-white text-xs font-medium truncate">
                        {image.displayName}
                      </p>
                      <p className="text-white/80 text-xs truncate">
                        {formatFileSize(image.size)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              onClick={onClose}
              className="w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 sm:ml-3 sm:w-auto sm:text-sm"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  );
} 