'use client';

import React, { ReactNode, useState, useEffect } from 'react';
import { 
  ChevronDownIcon, 
  ChevronUpIcon, 
  MagnifyingGlassIcon,
  AdjustmentsHorizontalIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  InformationCircleIcon,
  XCircleIcon
} from '@heroicons/react/24/outline';

// ============================================================================
// Responsive Layout Components
// ============================================================================

interface ResponsiveContainerProps {
  children: ReactNode;
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full';
  className?: string;
}

export function ResponsiveContainer({ 
  children, 
  maxWidth = 'full', 
  className = '' 
}: ResponsiveContainerProps) {
  const maxWidthClasses = {
    sm: 'max-w-3xl',
    md: 'max-w-4xl',
    lg: 'max-w-6xl',
    xl: 'max-w-7xl',
    '2xl': 'max-w-screen-2xl',
    full: 'max-w-full'
  };

  return (
    <div className={`admin-container ${maxWidthClasses[maxWidth]} ${className}`}>
      {children}
    </div>
  );
}

interface ResponsiveGridProps {
  children: ReactNode;
  columns?: {
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
  gap?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function ResponsiveGrid({ 
  children, 
  columns = { sm: 1, md: 2, lg: 3, xl: 4 },
  gap = 'md',
  className = '' 
}: ResponsiveGridProps) {
  const gapClasses = {
    sm: 'gap-4',
    md: 'gap-6',
    lg: 'gap-8'
  };

  const gridClasses = [
    'grid',
    gapClasses[gap],
    `grid-cols-${columns.sm || 1}`,
    columns.md && `md:grid-cols-${columns.md}`,
    columns.lg && `lg:grid-cols-${columns.lg}`,
    columns.xl && `xl:grid-cols-${columns.xl}`,
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={gridClasses}>
      {children}
    </div>
  );
}

// ============================================================================
// Enhanced Card Components
// ============================================================================

interface ResponsiveCardProps {
  children: ReactNode;
  title?: string;
  subtitle?: string;
  icon?: ReactNode;
  actions?: ReactNode;
  footer?: ReactNode;
  variant?: 'default' | 'compact' | 'elevated';
  className?: string;
  clickable?: boolean;
  onClick?: () => void;
}

export function ResponsiveCard({
  children,
  title,
  subtitle,
  icon,
  actions,
  footer,
  variant = 'default',
  className = '',
  clickable = false,
  onClick
}: ResponsiveCardProps) {
  const baseClasses = variant === 'compact' ? 'admin-card-compact' : 'admin-card';
  const clickableClasses = clickable ? 'cursor-pointer hover:shadow-lg active:scale-98 transition-all duration-200' : '';
  const elevatedClasses = variant === 'elevated' ? 'shadow-lg hover:shadow-xl' : '';

  return (
    <div 
      className={`${baseClasses} ${clickableClasses} ${elevatedClasses} ${className}`}
      onClick={onClick}
      role={clickable ? 'button' : undefined}
      tabIndex={clickable ? 0 : undefined}
      onKeyDown={clickable ? (e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          onClick?.();
        }
      } : undefined}
    >
      {/* Header */}
      {(title || subtitle || icon || actions) && (
        <div className="flex flex-col sm:flex-row sm:items-start justify-between gap-3 mb-4 pb-4 border-b border-gray-100">
          <div className="flex items-start space-x-3 flex-1 min-w-0">
            {icon && (
              <div className="flex-shrink-0 p-2 bg-blue-50 text-blue-600 rounded-lg">
                {icon}
              </div>
            )}
            <div className="flex-1 min-w-0">
              {title && (
                <h3 className="text-lg font-semibold text-gray-900 truncate">
                  {title}
                </h3>
              )}
              {subtitle && (
                <p className="text-sm text-gray-600 mt-1 admin-text-truncate">
                  {subtitle}
                </p>
              )}
            </div>
          </div>
          {actions && (
            <div className="flex items-center space-x-2 flex-shrink-0">
              {actions}
            </div>
          )}
        </div>
      )}

      {/* Content */}
      <div className="space-y-4">
        {children}
      </div>

      {/* Footer */}
      {footer && (
        <div className="mt-6 pt-4 border-t border-gray-100">
          {footer}
        </div>
      )}
    </div>
  );
}

// ============================================================================
// Enhanced Search and Filter Components
// ============================================================================

interface ResponsiveSearchProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  onSubmit?: () => void;
  className?: string;
}

export function ResponsiveSearch({
  value,
  onChange,
  placeholder = "Search...",
  onSubmit,
  className = ''
}: ResponsiveSearchProps) {
  return (
    <form 
      onSubmit={(e) => {
        e.preventDefault();
        onSubmit?.();
      }}
      className={`admin-search-container ${className}`}
    >
      <div className="relative">
        <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
        <input
          type="text"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          placeholder={placeholder}
          className="admin-search-input"
        />
      </div>
    </form>
  );
}

interface ResponsiveFiltersProps {
  children: ReactNode;
  title?: string;
  collapsible?: boolean;
  defaultExpanded?: boolean;
  onClear?: () => void;
  className?: string;
}

export function ResponsiveFilters({
  children,
  title = "Filters",
  collapsible = true,
  defaultExpanded = true,
  onClear,
  className = ''
}: ResponsiveFiltersProps) {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded);

  return (
    <div className={`admin-card ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <AdjustmentsHorizontalIcon className="h-5 w-5 text-gray-600" />
          <h3 className="text-lg font-medium text-gray-900">{title}</h3>
        </div>
        <div className="flex items-center space-x-2">
          {onClear && (
            <button
              type="button"
              onClick={onClear}
              className="admin-button-sm bg-gray-100 text-gray-700 hover:bg-gray-200"
            >
              Clear
            </button>
          )}
          {collapsible && (
            <button
              type="button"
              onClick={() => setIsExpanded(!isExpanded)}
              className="admin-button-sm bg-gray-100 text-gray-700 hover:bg-gray-200"
              aria-label={isExpanded ? "Collapse filters" : "Expand filters"}
            >
              {isExpanded ? (
                <ChevronUpIcon className="h-4 w-4" />
              ) : (
                <ChevronDownIcon className="h-4 w-4" />
              )}
            </button>
          )}
        </div>
      </div>
      
      {(!collapsible || isExpanded) && (
        <div className="admin-filters">
          {children}
        </div>
      )}
    </div>
  );
}

// ============================================================================
// Enhanced Button Components
// ============================================================================

interface ResponsiveButtonProps {
  children: ReactNode;
  variant?: 'primary' | 'secondary' | 'danger' | 'success' | 'warning';
  size?: 'xs' | 'sm' | 'md' | 'lg';
  icon?: ReactNode;
  iconPosition?: 'left' | 'right';
  disabled?: boolean;
  loading?: boolean;
  fullWidth?: boolean;
  onClick?: () => void;
  type?: 'button' | 'submit' | 'reset';
  className?: string;
}

export function ResponsiveButton({
  children,
  variant = 'primary',
  size = 'md',
  icon,
  iconPosition = 'left',
  disabled = false,
  loading = false,
  fullWidth = false,
  onClick,
  type = 'button',
  className = ''
}: ResponsiveButtonProps) {
  const variantClasses = {
    primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500',
    secondary: 'bg-gray-100 text-gray-900 hover:bg-gray-200 focus:ring-gray-500',
    danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500',
    success: 'bg-green-600 text-white hover:bg-green-700 focus:ring-green-500',
    warning: 'bg-yellow-500 text-white hover:bg-yellow-600 focus:ring-yellow-500'
  };

  const sizeClasses = {
    xs: 'admin-button-xs',
    sm: 'admin-button-sm',
    md: 'admin-button',
    lg: 'admin-button px-6 py-3 text-base'
  };

  const baseClasses = [
    sizeClasses[size],
    variantClasses[variant],
    'admin-focus-ring',
    fullWidth ? 'w-full' : '',
    disabled || loading ? 'opacity-50 cursor-not-allowed' : '',
    className
  ].filter(Boolean).join(' ');

  return (
    <button
      type={type}
      onClick={onClick}
      disabled={disabled || loading}
      className={baseClasses}
    >
      {loading ? (
        <div className="flex items-center justify-center space-x-2">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
          <span>Loading...</span>
        </div>
      ) : (
        <div className="flex items-center justify-center space-x-2">
          {icon && iconPosition === 'left' && <span className="admin-icon-sm">{icon}</span>}
          <span>{children}</span>
          {icon && iconPosition === 'right' && <span className="admin-icon-sm">{icon}</span>}
        </div>
      )}
    </button>
  );
}

interface ResponsiveButtonGroupProps {
  children: ReactNode;
  orientation?: 'horizontal' | 'vertical';
  spacing?: 'tight' | 'normal' | 'loose';
  className?: string;
}

export function ResponsiveButtonGroup({
  children,
  orientation = 'horizontal',
  spacing = 'normal',
  className = ''
}: ResponsiveButtonGroupProps) {
  const spacingClasses = {
    tight: 'gap-1',
    normal: 'gap-2',
    loose: 'gap-4'
  };

  const orientationClasses = orientation === 'vertical' 
    ? 'flex-col' 
    : 'flex-row flex-wrap sm:flex-nowrap';

  return (
    <div className={`admin-button-group flex ${orientationClasses} ${spacingClasses[spacing]} ${className}`}>
      {children}
    </div>
  );
}

// ============================================================================
// Enhanced Form Components
// ============================================================================

interface ResponsiveFormGroupProps {
  label: string;
  children: ReactNode;
  error?: string;
  required?: boolean;
  helpText?: string;
  className?: string;
}

export function ResponsiveFormGroup({
  label,
  children,
  error,
  required = false,
  helpText,
  className = ''
}: ResponsiveFormGroupProps) {
  return (
    <div className={`admin-form-group ${className}`}>
      <label className="admin-label">
        {label}
        {required && <span className="text-red-500 ml-1">*</span>}
      </label>
      {children}
      {error && <p className="admin-error">{error}</p>}
      {helpText && !error && <p className="text-sm text-gray-500 mt-1">{helpText}</p>}
    </div>
  );
}

interface ResponsiveInputProps {
  type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'url';
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  error?: boolean;
  className?: string;
}

export function ResponsiveInput({
  type = 'text',
  value,
  onChange,
  placeholder,
  disabled = false,
  error = false,
  className = ''
}: ResponsiveInputProps) {
  const errorClasses = error ? 'border-red-500 focus:ring-red-500 focus:border-red-500' : '';
  
  return (
    <input
      type={type}
      value={value}
      onChange={(e) => onChange(e.target.value)}
      placeholder={placeholder}
      disabled={disabled}
      className={`admin-input ${errorClasses} ${className}`}
    />
  );
}

interface ResponsiveSelectProps {
  value: string;
  onChange: (value: string) => void;
  options: { value: string; label: string }[];
  placeholder?: string;
  disabled?: boolean;
  error?: boolean;
  className?: string;
}

export function ResponsiveSelect({
  value,
  onChange,
  options,
  placeholder,
  disabled = false,
  error = false,
  className = ''
}: ResponsiveSelectProps) {
  const errorClasses = error ? 'border-red-500 focus:ring-red-500 focus:border-red-500' : '';
  
  return (
    <select
      value={value}
      onChange={(e) => onChange(e.target.value)}
      disabled={disabled}
      className={`admin-select ${errorClasses} ${className}`}
    >
      {placeholder && <option value="">{placeholder}</option>}
      {options.map((option) => (
        <option key={option.value} value={option.value}>
          {option.label}
        </option>
      ))}
    </select>
  );
}

interface ResponsiveTextareaProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  rows?: number;
  disabled?: boolean;
  error?: boolean;
  className?: string;
}

export function ResponsiveTextarea({
  value,
  onChange,
  placeholder,
  rows = 4,
  disabled = false,
  error = false,
  className = ''
}: ResponsiveTextareaProps) {
  const errorClasses = error ? 'border-red-500 focus:ring-red-500 focus:border-red-500' : '';
  
  return (
    <textarea
      value={value}
      onChange={(e) => onChange(e.target.value)}
      placeholder={placeholder}
      rows={rows}
      disabled={disabled}
      className={`admin-textarea ${errorClasses} ${className}`}
    />
  );
}

// ============================================================================
// Enhanced Notification Components
// ============================================================================

interface ResponsiveAlertProps {
  type: 'success' | 'error' | 'warning' | 'info';
  title?: string;
  children: ReactNode;
  dismissible?: boolean;
  onDismiss?: () => void;
  className?: string;
}

export function ResponsiveAlert({
  type,
  title,
  children,
  dismissible = false,
  onDismiss,
  className = ''
}: ResponsiveAlertProps) {
  const typeConfig = {
    success: {
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200',
      textColor: 'text-green-800',
      icon: <CheckCircleIcon className="h-5 w-5 text-green-400" />
    },
    error: {
      bgColor: 'bg-red-50',
      borderColor: 'border-red-200',
      textColor: 'text-red-800',
      icon: <XCircleIcon className="h-5 w-5 text-red-400" />
    },
    warning: {
      bgColor: 'bg-yellow-50',
      borderColor: 'border-yellow-200',
      textColor: 'text-yellow-800',
      icon: <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400" />
    },
    info: {
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200',
      textColor: 'text-blue-800',
      icon: <InformationCircleIcon className="h-5 w-5 text-blue-400" />
    }
  };

  const config = typeConfig[type];

  return (
    <div className={`rounded-lg border p-4 ${config.bgColor} ${config.borderColor} ${className}`}>
      <div className="flex items-start">
        <div className="flex-shrink-0">
          {config.icon}
        </div>
        <div className="ml-3 flex-1">
          {title && (
            <h3 className={`text-sm font-medium ${config.textColor} mb-2`}>
              {title}
            </h3>
          )}
          <div className={`text-sm ${config.textColor}`}>
            {children}
          </div>
        </div>
        {dismissible && onDismiss && (
          <div className="ml-auto pl-3">
            <button
              type="button"
              onClick={onDismiss}
              className={`inline-flex rounded-md p-1.5 ${config.textColor} hover:bg-white/20 focus:outline-none focus:ring-2 focus:ring-offset-2`}
            >
              <span className="sr-only">Dismiss</span>
              <XCircleIcon className="h-5 w-5" />
            </button>
          </div>
        )}
      </div>
    </div>
  );
}

// ============================================================================
// Enhanced Loading Components
// ============================================================================

interface ResponsiveSkeletonProps {
  type?: 'text' | 'title' | 'button' | 'card' | 'table';
  rows?: number;
  className?: string;
}

export function ResponsiveSkeleton({
  type = 'text',
  rows = 1,
  className = ''
}: ResponsiveSkeletonProps) {
  if (type === 'card') {
    return (
      <div className={`admin-card ${className}`}>
        <div className="animate-pulse space-y-4">
          <div className="admin-skeleton-title"></div>
          <div className="space-y-2">
            <div className="admin-skeleton-text"></div>
            <div className="admin-skeleton-text w-4/5"></div>
          </div>
          <div className="admin-skeleton-button"></div>
        </div>
      </div>
    );
  }

  if (type === 'table') {
    return (
      <div className={`admin-card ${className}`}>
        <div className="animate-pulse space-y-4">
          <div className="admin-skeleton-title w-1/3"></div>
          {Array.from({ length: rows || 5 }).map((_, i) => (
            <div key={i} className="flex space-x-4">
              <div className="admin-skeleton-text flex-1"></div>
              <div className="admin-skeleton-text flex-1"></div>
              <div className="admin-skeleton-text flex-1"></div>
              <div className="admin-skeleton-button w-20"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  const skeletonClasses = {
    text: 'admin-skeleton-text',
    title: 'admin-skeleton-title',
    button: 'admin-skeleton-button'
  };

  return (
    <div className={`animate-pulse space-y-2 ${className}`}>
      {Array.from({ length: rows }).map((_, i) => (
        <div key={i} className={skeletonClasses[type]}></div>
      ))}
    </div>
  );
}

interface ResponsiveLoadingProps {
  size?: 'sm' | 'md' | 'lg';
  text?: string;
  className?: string;
}

export function ResponsiveLoading({
  size = 'md',
  text,
  className = ''
}: ResponsiveLoadingProps) {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-8 w-8',
    lg: 'h-12 w-12'
  };

  return (
    <div className={`flex items-center justify-center space-x-2 ${className}`}>
      <div className={`animate-spin rounded-full border-b-2 border-blue-600 ${sizeClasses[size]}`}></div>
      {text && <span className="text-sm text-gray-600">{text}</span>}
    </div>
  );
}

// ============================================================================
// Enhanced Modal Component
// ============================================================================

interface ResponsiveModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: ReactNode;
  footer?: ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  className?: string;
}

export function ResponsiveModal({
  isOpen,
  onClose,
  title,
  children,
  footer,
  size = 'md',
  className = ''
}: ResponsiveModalProps) {
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  if (!isOpen) return null;

  const sizeClasses = {
    sm: 'max-w-md',
    md: 'max-w-lg',
    lg: 'max-w-2xl',
    xl: 'max-w-4xl',
    full: 'max-w-7xl'
  };

  return (
    <div className="admin-modal">
      <div 
        className="admin-modal-overlay"
        onClick={onClose}
      />
      <div className="admin-modal-container">
        <div className={`admin-modal-content ${sizeClasses[size]} ${className}`}>
          {/* Header */}
          {title && (
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">{title}</h3>
              <button
                type="button"
                onClick={onClose}
                className="admin-button-sm bg-gray-100 text-gray-700 hover:bg-gray-200"
              >
                <XCircleIcon className="h-5 w-5" />
              </button>
            </div>
          )}

          {/* Content */}
          <div className="p-6">
            {children}
          </div>

          {/* Footer */}
          {footer && (
            <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50">
              {footer}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// ============================================================================
// Enhanced Pagination Component
// ============================================================================

interface ResponsivePaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  showPageInfo?: boolean;
  pageSize?: number;
  totalItems?: number;
  className?: string;
}

export function ResponsivePagination({
  currentPage,
  totalPages,
  onPageChange,
  showPageInfo = true,
  pageSize,
  totalItems,
  className = ''
}: ResponsivePaginationProps) {
  const getVisiblePages = () => {
    const delta = 2;
    const range = [];
    const rangeWithDots = [];

    for (let i = Math.max(2, currentPage - delta); i <= Math.min(totalPages - 1, currentPage + delta); i++) {
      range.push(i);
    }

    if (currentPage - delta > 2) {
      rangeWithDots.push(1, '...');
    } else {
      rangeWithDots.push(1);
    }

    rangeWithDots.push(...range);

    if (currentPage + delta < totalPages - 1) {
      rangeWithDots.push('...', totalPages);
    } else if (totalPages > 1) {
      rangeWithDots.push(totalPages);
    }

    return rangeWithDots;
  };

  const visiblePages = getVisiblePages();

  return (
    <div className={`admin-pagination ${className}`}>
      {/* Page Info */}
      {showPageInfo && totalItems && pageSize && (
        <div className="admin-pagination-info">
          <p className="text-sm text-gray-700">
            Showing{' '}
            <span className="font-medium">
              {((currentPage - 1) * pageSize) + 1}
            </span>{' '}
            to{' '}
            <span className="font-medium">
              {Math.min(currentPage * pageSize, totalItems)}
            </span>{' '}
            of{' '}
            <span className="font-medium">{totalItems}</span>{' '}
            results
          </p>
        </div>
      )}

      {/* Pagination Controls */}
      <div className="admin-pagination-controls">
        <nav className="flex items-center space-x-1" aria-label="Pagination">
          {/* Previous Button */}
          <button
            onClick={() => onPageChange(currentPage - 1)}
            disabled={currentPage === 1}
            className="admin-button-sm bg-white border border-gray-300 text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Previous
          </button>

          {/* Page Numbers */}
          <div className="hidden sm:flex items-center space-x-1">
            {visiblePages.map((page, index) => (
              <button
                key={index}
                onClick={() => typeof page === 'number' ? onPageChange(page) : undefined}
                disabled={page === '...'}
                className={`admin-button-sm ${
                  page === currentPage
                    ? 'bg-blue-600 text-white'
                    : page === '...'
                    ? 'bg-white text-gray-400 cursor-default'
                    : 'bg-white border border-gray-300 text-gray-700 hover:bg-gray-50'
                }`}
              >
                {page}
              </button>
            ))}
          </div>

          {/* Mobile Page Display */}
          <div className="sm:hidden">
            <span className="admin-button-sm bg-white border border-gray-300 text-gray-700">
              {currentPage} of {totalPages}
            </span>
          </div>

          {/* Next Button */}
          <button
            onClick={() => onPageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
            className="admin-button-sm bg-white border border-gray-300 text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Next
          </button>
        </nav>
      </div>
    </div>
  );
}

// ============================================================================
// Exports
// ============================================================================

export default {
  ResponsiveContainer,
  ResponsiveGrid,
  ResponsiveCard,
  ResponsiveSearch,
  ResponsiveFilters,
  ResponsiveButton,
  ResponsiveButtonGroup,
  ResponsiveFormGroup,
  ResponsiveInput,
  ResponsiveSelect,
  ResponsiveTextarea,
  ResponsiveAlert,
  ResponsiveSkeleton,
  ResponsiveLoading,
  ResponsiveModal,
  ResponsivePagination
}; 