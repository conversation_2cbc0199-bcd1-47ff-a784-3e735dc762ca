'use client';

import React, { useState, useEffect } from 'react';
import {
  ServerIcon,
  CpuChipIcon,
  ArrowPathIcon,
  CircleStackIcon,
  ClockIcon,
  ComputerDesktopIcon,
  ArrowTrendingUpIcon,
  BoltIcon
} from '@heroicons/react/24/outline';
import { toast } from 'react-hot-toast';

interface ServerStatsProps {
  refreshInterval?: number; // in milliseconds
}

interface SystemStats {
  cpu: {
    usage: number;
    cores: number;
    model: string;
    loadAvg: number[];
  };
  memory: {
    total: string;
    free: string;
    used: string;
    usedPercent: number;
  };
  disk: {
    total: string;
    free: string;
    used: string;
    usedPercent: number;
  };
  uptime: {
    system: string;
    process: string;
  };
  os: {
    platform: string;
    release: string;
    hostname: string;
  };
  network: {
    interfaces: string[];
  };
}

const ServerStats = ({ refreshInterval = 30000 }: ServerStatsProps) => {
  const [stats, setStats] = useState<SystemStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  const fetchStats = async () => {
    try {
      setError(null);

      const response = await fetch('/api/admin/stats/server', {
        credentials: 'include'
      });

      if (!response.ok) {
        throw new Error('Failed to fetch server statistics');
      }

      const data = await response.json();
      setStats(data);
      setLastUpdated(new Date());
    } catch (err) {
      console.error('Error fetching server stats:', err);
      setError('Failed to load server statistics');
      toast.error('Failed to load server statistics');
    } finally {
      setIsLoading(false);
    }
  };

  // Initial fetch
  useEffect(() => {
    fetchStats();
  }, []);

  // Set up refresh interval
  useEffect(() => {
    const intervalId = setInterval(fetchStats, refreshInterval);

    // Clean up on unmount
    return () => clearInterval(intervalId);
  }, [refreshInterval]);

  // Handle manual refresh
  const handleRefresh = () => {
    setIsLoading(true);
    fetchStats();
  };

  // Render loading state
  if (isLoading && !stats) {
    return (
      <>
        <div className="ml-auto flex items-center gap-2">
          {lastUpdated && (
            <span className="text-xs text-gray-400">{lastUpdated.toLocaleTimeString()}</span>
          )}
          <button
            onClick={handleRefresh}
            disabled={isLoading}
            className="p-2 rounded hover:bg-gray-100"
            aria-label="Refresh statistics"
          >
            <ArrowPathIcon className={`w-5 h-5 text-gray-400 ${isLoading ? 'animate-spin' : ''}`} />
          </button>
        </div>
        {/* Loading skeleton cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {[...Array(4)].map((_, index) => (
            <div key={index} className="bg-gray-50 rounded-xl p-4 flex flex-col gap-2 shadow-sm animate-pulse">
              <div className="flex items-center gap-2 mb-2">
                <div className="w-5 h-5 bg-gray-200 rounded-full" />
                <div className="h-4 w-16 bg-gray-200 rounded" />
                <div className="ml-auto h-4 w-10 bg-gray-200 rounded" />
              </div>
              <div className="h-2 w-20 bg-gray-200 rounded mb-1" />
              <div className="w-full h-2 bg-gray-200 rounded-full mb-2" />
              <div className="grid grid-cols-2 gap-2 text-xs">
                <div className="bg-white rounded p-2 text-center">
                  <div className="h-4 w-8 bg-gray-200 rounded mx-auto mb-1" />
                  <div className="h-3 w-10 bg-gray-100 rounded mx-auto" />
                </div>
                <div className="bg-white rounded p-2 text-center">
                  <div className="h-4 w-8 bg-gray-200 rounded mx-auto mb-1" />
                  <div className="h-3 w-10 bg-gray-100 rounded mx-auto" />
                </div>
              </div>
            </div>
          ))}
        </div>
      </>
    );
  }

  // Render error state
  if (error && !stats) {
    return (
      <>
        <div className="ml-auto flex items-center gap-2">
          {lastUpdated && (
            <span className="text-xs text-gray-400">{lastUpdated.toLocaleTimeString()}</span>
          )}
          <button
            onClick={handleRefresh}
            disabled={isLoading}
            className="p-2 rounded hover:bg-gray-100"
            aria-label="Refresh statistics"
          >
            <ArrowPathIcon className={`w-5 h-5 text-gray-400 ${isLoading ? 'animate-spin' : ''}`} />
          </button>
        </div>
        <div className="p-4 bg-red-50 border border-red-100 text-red-700 rounded-lg flex items-center">
          <div className="p-2 bg-red-100 rounded-full mr-3">
            <svg className="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
          </div>
          <div>
            <h3 className="text-sm font-medium text-red-800 mb-1">Error Loading Statistics</h3>
            <p className="text-sm text-red-700">{error}. Click refresh to try again.</p>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <div className="ml-auto flex items-center gap-2 mb-6">
        {lastUpdated && (
          <span className="text-xs text-gray-400">{lastUpdated.toLocaleTimeString()}</span>
        )}
        <button
          onClick={handleRefresh}
          disabled={isLoading}
          className="p-2 rounded hover:bg-gray-100"
          aria-label="Refresh statistics"
        >
          <ArrowPathIcon className={`w-5 h-5 text-gray-400 ${isLoading ? 'animate-spin' : ''}`} />
        </button>
      </div>
      {stats && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* CPU Card */}
          <div className="bg-gray-50 rounded-xl p-4 flex flex-col gap-2 shadow-sm">
            <div className="flex items-center gap-2 mb-2">
              <CpuChipIcon className="w-5 h-5 text-blue-400" />
              <span className="font-semibold text-gray-800">CPU</span>
              <span className="ml-auto text-xs font-semibold text-green-600 bg-green-100 px-2 py-0.5 rounded">{stats.cpu.usage}%</span>
            </div>
            <div className="text-xs text-gray-500 mb-1">Usage</div>
            <div className="w-full h-2 bg-gray-200 rounded-full mb-2">
              <div className="h-2 bg-green-500 rounded-full" style={{ width: `${stats.cpu.usage}%` }} />
            </div>
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div className="bg-white rounded p-2 text-center">
                <div className="font-bold text-gray-800">{stats.cpu.cores}</div>
                <div className="text-gray-400">Cores</div>
              </div>
              <div className="bg-white rounded p-2 text-center">
                <div className="font-bold text-gray-800">{stats.cpu.loadAvg[0]}</div>
                <div className="text-gray-400">Load Avg</div>
              </div>
            </div>
          </div>
          {/* Memory Card */}
          <div className="bg-gray-50 rounded-xl p-4 flex flex-col gap-2 shadow-sm">
            <div className="flex items-center gap-2 mb-2">
              <CircleStackIcon className="w-5 h-5 text-purple-400" />
              <span className="font-semibold text-gray-800">Memory</span>
              <span className="ml-auto text-xs font-semibold text-green-600 bg-green-100 px-2 py-0.5 rounded">{stats.memory.usedPercent}%</span>
            </div>
            <div className="text-xs text-gray-500 mb-1">Usage</div>
            <div className="w-full h-2 bg-gray-200 rounded-full mb-2">
              <div className="h-2 bg-green-500 rounded-full" style={{ width: `${stats.memory.usedPercent}%` }} />
            </div>
            <div className="grid grid-cols-3 gap-2 text-xs">
              <div className="bg-white rounded p-2 text-center">
                <div className="font-bold text-gray-800">{stats.memory.total}</div>
                <div className="text-gray-400">Total</div>
              </div>
              <div className="bg-white rounded p-2 text-center">
                <div className="font-bold text-gray-800">{stats.memory.used}</div>
                <div className="text-gray-400">Used</div>
              </div>
              <div className="bg-white rounded p-2 text-center">
                <div className="font-bold text-gray-800">{stats.memory.free}</div>
                <div className="text-gray-400">Free</div>
              </div>
            </div>
          </div>
          {/* Disk Card */}
          <div className="bg-gray-50 rounded-xl p-4 flex flex-col gap-2 shadow-sm">
            <div className="flex items-center gap-2 mb-2">
              <BoltIcon className="w-5 h-5 text-green-400" />
              <span className="font-semibold text-gray-800">Disk</span>
              <span className="ml-auto text-xs font-semibold text-green-600 bg-green-100 px-2 py-0.5 rounded">{stats.disk.usedPercent}%</span>
            </div>
            <div className="text-xs text-gray-500 mb-1">Usage</div>
            <div className="w-full h-2 bg-gray-200 rounded-full mb-2">
              <div className="h-2 bg-green-500 rounded-full" style={{ width: `${stats.disk.usedPercent}%` }} />
            </div>
            <div className="grid grid-cols-3 gap-2 text-xs">
              <div className="bg-white rounded p-2 text-center">
                <div className="font-bold text-gray-800">{stats.disk.total}</div>
                <div className="text-gray-400">Total</div>
              </div>
              <div className="bg-white rounded p-2 text-center">
                <div className="font-bold text-gray-800">{stats.disk.used}</div>
                <div className="text-gray-400">Used</div>
              </div>
              <div className="bg-white rounded p-2 text-center">
                <div className="font-bold text-gray-800">{stats.disk.free}</div>
                <div className="text-gray-400">Free</div>
              </div>
            </div>
          </div>
          {/* System Card */}
          <div className="bg-gray-50 rounded-xl p-4 flex flex-col gap-2 shadow-sm">
            <div className="flex items-center gap-2 mb-2">
              <ComputerDesktopIcon className="w-5 h-5 text-orange-400" />
              <span className="font-semibold text-gray-800">System</span>
            </div>
            <div className="grid grid-cols-2 gap-2 text-xs mt-2">
              <div className="bg-white rounded p-2 text-center">
                <div className="font-bold text-gray-800">{stats.os.hostname}</div>
                <div className="text-gray-400">Hostname</div>
              </div>
              <div className="bg-white rounded p-2 text-center">
                <div className="font-bold text-gray-800">{stats.os.platform}</div>
                <div className="text-gray-400">Platform</div>
              </div>
              <div className="bg-white rounded p-2 text-center">
                <div className="font-bold text-gray-800">{stats.uptime.system}</div>
                <div className="text-gray-400">System Uptime</div>
              </div>
              <div className="bg-white rounded p-2 text-center">
                <div className="font-bold text-gray-800">{stats.uptime.process}</div>
                <div className="text-gray-400">Process Uptime</div>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default ServerStats;
