'use client';

import { useEffect, useState } from 'react';
import {
  DocumentTextIcon,
  FolderIcon,
  UserGroupIcon,
  Cog6ToothIcon,
  ChartBarIcon,
  PhotoIcon,
  GlobeAltIcon,
  CurrencyDollarIcon,
  ServerIcon,
  ChevronDownIcon,
  UsersIcon,
  BookOpenIcon,
  XMarkIcon,
  HomeIcon,
  ArrowTopRightOnSquareIcon,
  ReceiptRefundIcon,
  BanknotesIcon,
  ShoppingBagIcon,
  NewspaperIcon,
  SquaresPlusIcon,
  ShieldCheckIcon,
  ClockIcon,
  CodeBracketIcon,
  UserIcon,
  ArrowTrendingUpIcon,
  BriefcaseIcon,
  BuildingOfficeIcon,
  ChatBubbleLeftRightIcon,
  ClipboardDocumentListIcon,
  CalendarIcon,
  BeakerIcon,
  StarIcon,
  ArrowPathIcon,
  ShoppingCartIcon,
  PencilSquareIcon,
  SparklesIcon,
  CubeIcon,
} from '@heroicons/react/24/outline';
import { CircleStackIcon as DatabaseIcon } from '@heroicons/react/24/outline';
import { usePathname } from 'next/navigation';
import Link from 'next/link';

// Dashboard items
const dashboardItems = [
  {
    id: 'dashboard',
    name: 'Dashboard',
    icon: ChartBarIcon,
    path: '/admin/dashboard',
    highlight: true
  },
];

// Business Management submenu items (HIGHEST PRIORITY - business critical)
const businessMenuItems = [
  { id: 'orders', name: 'Order Management', icon: ShoppingCartIcon, path: '/admin/orders', highlight: true },
  { id: 'design-requests', name: 'Design Requests', icon: PencilSquareIcon, path: '/admin/design-requests', highlight: true },
  { id: 'logo-orders', name: 'Logo Orders', icon: SparklesIcon, path: '/admin/logo-orders', highlight: true },
  { id: 'logo-packages', name: 'Logo Packages', icon: CubeIcon, path: '/admin/logo-packages', highlight: true },
  { id: 'calendar', name: 'Calendar & Scheduling', icon: CalendarIcon, path: '/admin/calendar', highlight: true },
];

// Financial Suite submenu items (Second priority - business operations)
const financialMenuItems = [
  { id: 'comprehensive-receipts', name: 'Create Receipt', icon: SquaresPlusIcon, path: '/admin/comprehensive-receipts', highlight: true },
  { id: 'comprehensive-receipts-list', name: 'All Receipts', icon: ReceiptRefundIcon, path: '/admin/comprehensive-receipts/list', highlight: true },
  { id: 'invoices', name: 'Invoices', icon: DocumentTextIcon, path: '/admin/invoices', highlight: true },
  { id: 'quotes', name: 'Quotes', icon: NewspaperIcon, path: '/admin/quotes', highlight: true },
];

// Service Catalogue submenu items (Service management and categorization)
const serviceCatalogueMenuItems = [
  { id: 'services', name: 'Services', icon: ShoppingBagIcon, path: '/admin/services', highlight: true },
  { id: 'categories', name: 'Categories', icon: FolderIcon, path: '/admin/categories', highlight: true },
  { id: 'catalogue', name: 'Catalogue', icon: CurrencyDollarIcon, path: '/admin/catalogue', highlight: true },
  { id: 'units', name: 'Units Management', icon: SquaresPlusIcon, path: '/admin/units', highlight: true },
  { id: 'pricing', name: 'Pricing Management', icon: Cog6ToothIcon, path: '/admin/pricing', highlight: true },
];

// Blog submenu items (Third priority - blog management)
const blogMenuItems = [
  { id: 'blog', name: 'Blog Posts', icon: DocumentTextIcon, path: '/admin/blog', highlight: true },
  { id: 'blog-categories', name: 'Categories', icon: FolderIcon, path: '/admin/blog/categories', highlight: true },
  { id: 'posts', name: 'Posts', icon: NewspaperIcon, path: '/admin/posts', highlight: true },
  { id: 'scheduled-blog-posts', name: 'Scheduled Posts', icon: ClockIcon, path: '/admin/scheduled-blog-posts', highlight: true },
];

// Content Management submenu items (Fourth priority - website content)
const contentMenuItems = [
  { id: 'portfolio', name: 'Portfolio', icon: PhotoIcon, path: '/admin/portfolio', highlight: true },
  { id: 'website-portfolio', name: 'Website Portfolio', icon: GlobeAltIcon, path: '/admin/website-portfolio', highlight: true },
  { id: 'team', name: 'Team Members', icon: UsersIcon, path: '/admin/team', highlight: true },
  { id: 'testimonials', name: 'Testimonials', icon: StarIcon, path: '/admin/testimonials', highlight: true },
];

// System Administration submenu items (Fifth priority - admin functions)
const systemMenuItems = [
  { id: 'users', name: 'Users', icon: UserGroupIcon, path: '/admin/users', highlight: true },
  { id: 'roles', name: 'Roles & Permissions', icon: ShieldCheckIcon, path: '/admin/roles', highlight: true },
  { id: 'activity-logs', name: 'Activity Logs', icon: ClockIcon, path: '/admin/activity-logs', highlight: true },
  { id: 'database', name: 'Database Management', icon: DatabaseIcon, path: '/admin/database', highlight: true },
  { id: 'tests', name: 'System Tests', icon: BeakerIcon, path: '/admin/tests', highlight: true },
];

// Settings submenu items (Sixth priority - configuration)
const settingsMenuItems = [
  { id: 'general-settings', name: 'General Settings', icon: Cog6ToothIcon, path: '/admin/settings', highlight: true },
  { id: 'storage-settings', name: 'Storage Settings', icon: ServerIcon, path: '/admin/settings/storage', highlight: true },
  { id: 'cache', name: 'Cache Management', icon: ArrowPathIcon, path: '/admin/settings/cache', highlight: true },
  { id: 'scripts-settings', name: 'Site Scripts', icon: CodeBracketIcon, path: '/admin/settings/scripts', highlight: true },
];

interface AdminSidebarProps {
  open: boolean;
  onClose: () => void;
}

export default function AdminSidebar({ open, onClose }: AdminSidebarProps) {
  const [mounted, setMounted] = useState(false);
  const [settingsOpen, setSettingsOpen] = useState(false);
  const [businessOpen, setBusinessOpen] = useState(false);
  const [financialOpen, setFinancialOpen] = useState(false);
  const [serviceCatalogueOpen, setServiceCatalogueOpen] = useState(false);
  const [blogOpen, setBlogOpen] = useState(false);
  const [contentOpen, setContentOpen] = useState(false);
  const [systemOpen, setSystemOpen] = useState(false);
  const pathname = usePathname();

  useEffect(() => {
    setMounted(true);

    // Auto-expand sections based on current path
    if (pathname) {
      // Business section (orders, design-requests, logo-orders, logo-packages, calendar)
      if (pathname.startsWith('/admin/orders') ||
          pathname.startsWith('/admin/design-requests') ||
          pathname.startsWith('/admin/logo-orders') ||
          pathname.startsWith('/admin/logo-packages') ||
          pathname.startsWith('/admin/calendar')) {
        setBusinessOpen(true);
      }

      // Settings section
      if (pathname.startsWith('/admin/settings')) {
        setSettingsOpen(true);
      }

      // Financial Suite section (comprehensive-receipts, invoices, quotes)
      if (pathname.startsWith('/admin/comprehensive-receipts') ||
          pathname.startsWith('/admin/invoices') ||
          pathname.startsWith('/admin/quotes')) {
        setFinancialOpen(true);
      }

      // Service Catalogue section (services, categories, catalogue, units, pricing)
      if (pathname.startsWith('/admin/services') ||
          pathname.startsWith('/admin/categories') ||
          pathname.startsWith('/admin/catalogue') ||
          pathname.startsWith('/admin/units') ||
          pathname.startsWith('/admin/pricing')) {
        setServiceCatalogueOpen(true);
      }

      // Blog section (blog, blog/categories, posts, scheduled-blog-posts)
      if (pathname.startsWith('/admin/blog') ||
          pathname.startsWith('/admin/posts') ||
          pathname.startsWith('/admin/scheduled-blog-posts')) {
        setBlogOpen(true);
      }

      // Content Management section (portfolio, team, testimonials)
      if (pathname.startsWith('/admin/portfolio') ||
          pathname.startsWith('/admin/website-portfolio') ||
          pathname.startsWith('/admin/team') ||
          pathname.startsWith('/admin/testimonials')) {
        setContentOpen(true);
      }

      // System Administration section (users, roles, activity-logs, database, tests)
      if (pathname.startsWith('/admin/users') ||
          pathname.startsWith('/admin/roles') ||
          pathname.startsWith('/admin/activity-logs') ||
          pathname.startsWith('/admin/database') ||
          pathname.startsWith('/admin/tests')) {
        setSystemOpen(true);
      }
    }
  }, [pathname]);

  if (!mounted) {
    return null;
  }

  return (
    <>
      {/* Desktop sidebar - always visible on md and up */}
      <div 
        className="hidden md:block fixed left-0 w-60 bg-white border-r border-[#0A1929]/10 z-30 shadow-sm admin-desktop-sidebar"
        style={{ 
          top: '64px', 
          height: 'calc(100vh - 64px)',
          overflow: 'hidden'
        }}
      >
        <div className="h-full overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 hover:scrollbar-thumb-gray-400">
          <SidebarContent
            pathname={pathname}
            settingsOpen={settingsOpen}
            setSettingsOpen={setSettingsOpen}
            businessOpen={businessOpen}
            setBusinessOpen={setBusinessOpen}
            financialOpen={financialOpen}
            setFinancialOpen={setFinancialOpen}
            serviceCatalogueOpen={serviceCatalogueOpen}
            setServiceCatalogueOpen={setServiceCatalogueOpen}
            blogOpen={blogOpen}
            setBlogOpen={setBlogOpen}
            contentOpen={contentOpen}
            setContentOpen={setContentOpen}
            systemOpen={systemOpen}
            setSystemOpen={setSystemOpen}
          />
        </div>
      </div>

      {/* Mobile sidebar - slides in from left */}
      <div
        className={`md:hidden fixed inset-y-0 left-0 z-50 w-full max-w-[280px] bg-white shadow-2xl transform transition-transform duration-300 ease-in-out admin-mobile-sidebar flex flex-col ${
          open ? 'translate-x-0' : '-translate-x-full'
        }`}
        style={{ 
          willChange: 'transform',
          top: '64px', // Start below the header
          height: 'calc(100vh - 64px)' // Adjust height to account for header
        }}
        data-sidebar="mobile"
        data-testid="mobile-sidebar"
      >
        <div className="flex items-center justify-between p-4 border-b border-[#0A1929]/10 bg-white sticky top-0 z-10">
          <h2 className="text-lg font-medium text-[#0A1929]">Menu</h2>
          <button
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              onClose();
            }}
            className="p-2 rounded-md text-[#0A1929] hover:text-[#FF5400] focus:outline-none focus:ring-2 focus:ring-[#FF5400] transition-colors admin-mobile-button"
            aria-label="Close menu"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>
        <div className="flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 hover:scrollbar-thumb-gray-400 admin-mobile-sidebar-content">
          <div className="p-4 pt-2">
            <SidebarContent
              pathname={pathname}
              settingsOpen={settingsOpen}
              setSettingsOpen={setSettingsOpen}
              businessOpen={businessOpen}
              setBusinessOpen={setBusinessOpen}
              financialOpen={financialOpen}
              setFinancialOpen={setFinancialOpen}
              serviceCatalogueOpen={serviceCatalogueOpen}
              setServiceCatalogueOpen={setServiceCatalogueOpen}
              blogOpen={blogOpen}
              setBlogOpen={setBlogOpen}
              contentOpen={contentOpen}
              setContentOpen={setContentOpen}
              systemOpen={systemOpen}
              setSystemOpen={setSystemOpen}
              onLinkClick={() => onClose()}
            />
          </div>
        </div>
      </div>
    </>
  );
}

// Extracted sidebar content to avoid duplication
function SidebarContent({
  pathname,
  settingsOpen,
  setSettingsOpen,
  businessOpen,
  setBusinessOpen,
  financialOpen,
  setFinancialOpen,
  serviceCatalogueOpen,
  setServiceCatalogueOpen,
  blogOpen,
  setBlogOpen,
  contentOpen,
  setContentOpen,
  systemOpen,
  setSystemOpen,
  onLinkClick
}: {
  pathname: string | null;
  settingsOpen: boolean;
  setSettingsOpen: (open: boolean) => void;
  businessOpen?: boolean;
  setBusinessOpen?: (open: boolean) => void;
  financialOpen?: boolean;
  setFinancialOpen?: (open: boolean) => void;
  serviceCatalogueOpen?: boolean;
  setServiceCatalogueOpen?: (open: boolean) => void;
  blogOpen?: boolean;
  setBlogOpen?: (open: boolean) => void;
  contentOpen?: boolean;
  setContentOpen?: (open: boolean) => void;
  systemOpen?: boolean;
  setSystemOpen?: (open: boolean) => void;
  onLinkClick?: () => void;
}) {
  // Helper function to render a menu item
  const renderMenuItem = (item: any) => {
    const isActive = pathname === item.path;
    return (
      <Link
        key={item.id}
        href={item.path}
        className={`flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 ${
          isActive
            ? 'bg-[#0A1929]/10 text-[#0A1929] border-l-4 border-[#0A1929] ml-0'
            : 'text-[#0A1929] hover:bg-[#FF5400]/10 hover:text-[#FF5400] bg-white ml-0'
        }`}
        onClick={onLinkClick}
      >
        <item.icon className={`mr-3 h-5 w-5 flex-shrink-0 ${!isActive ? 'text-[#FF5400]' : ''}`} />
        <span className="truncate">{item.name}</span>
      </Link>
    );
  };

  // Helper function to render a submenu
  const renderSubmenu = (items: any[], isOpen: boolean) => {
    return (
      <div className={`overflow-hidden transition-all duration-300 ease-in-out ${
        isOpen ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
      }`}>
        <div className="ml-6 pl-3 border-l-2 border-[#FF5400]/20 space-y-1 py-2 mt-1">
          {items.map((item) => {
            const isActive = pathname === item.path;
            return (
              <Link
                key={item.id}
                href={item.path}
                className={`flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${
                  isActive
                    ? 'bg-[#0A1929]/10 text-[#0A1929] border-l-4 border-[#0A1929] ml-0'
                    : 'text-[#0A1929]/80 bg-white hover:bg-[#FF5400]/10 hover:text-[#FF5400] ml-0'
                }`}
                onClick={onLinkClick}
              >
                <item.icon className="mr-3 h-4 w-4 flex-shrink-0 text-[#FF5400]" />
                <span className="truncate">{item.name}</span>
              </Link>
            );
          })}
        </div>
      </div>
    );
  };

  return (
    <nav className="space-y-1 px-3 py-4">
      {/* Dashboard items */}
      <div className="mb-4">
        {dashboardItems.map(item => renderMenuItem(item))}
      </div>

      {/* Business Management section with collapsible submenu - PRIORITY 1 (HIGHEST) */}
      <div className="mb-3">
        <div className="h-px bg-[#0A1929]/10 mb-3" />
        <button
          onClick={() => setBusinessOpen && setBusinessOpen(!businessOpen)}
          className={`flex items-center justify-between w-full px-3 py-2.5 text-sm font-semibold rounded-lg transition-all duration-200 text-[#0A1929] bg-white hover:bg-[#FF5400]/10 hover:text-[#FF5400]`}
        >
          <div className="flex items-center">
            <BriefcaseIcon className="mr-3 h-5 w-5 flex-shrink-0 text-[#FF5400]" />
            <span className="truncate">Business Management</span>
          </div>
          <ChevronDownIcon className={`h-4 w-4 flex-shrink-0 transition-transform duration-200 ${businessOpen ? 'rotate-180' : ''}`} />
        </button>
        {renderSubmenu(businessMenuItems, businessOpen || false)}
      </div>

      {/* Financial Suite section with collapsible submenu - PRIORITY 2 */}
      <div className="mb-3">
        <div className="h-px bg-[#0A1929]/10 mb-3" />
        <button
          onClick={() => setFinancialOpen && setFinancialOpen(!financialOpen)}
          className={`flex items-center justify-between w-full px-3 py-2.5 text-sm font-semibold rounded-lg transition-all duration-200 text-[#0A1929] bg-white hover:bg-[#FF5400]/10 hover:text-[#FF5400]`}
        >
          <div className="flex items-center">
            <BanknotesIcon className="mr-3 h-5 w-5 flex-shrink-0 text-[#FF5400]" />
            <span className="truncate">Financial Suite</span>
          </div>
          <ChevronDownIcon className={`h-4 w-4 flex-shrink-0 transition-transform duration-200 ${financialOpen ? 'rotate-180' : ''}`} />
        </button>
        {renderSubmenu(financialMenuItems, financialOpen || false)}
      </div>

      {/* Service Catalogue section with collapsible submenu - PRIORITY 2 */}
      <div className="mb-3">
        <div className="h-px bg-[#0A1929]/10 mb-3" />
        <button
          onClick={() => setServiceCatalogueOpen && setServiceCatalogueOpen(!serviceCatalogueOpen)}
          className={`flex items-center justify-between w-full px-3 py-2.5 text-sm font-semibold rounded-lg transition-all duration-200 text-[#0A1929] bg-white hover:bg-[#FF5400]/10 hover:text-[#FF5400]`}
        >
          <div className="flex items-center">
            <ShoppingBagIcon className="mr-3 h-5 w-5 flex-shrink-0 text-[#FF5400]" />
            <span className="truncate">Service Catalogue</span>
          </div>
          <ChevronDownIcon className={`h-4 w-4 flex-shrink-0 transition-transform duration-200 ${serviceCatalogueOpen ? 'rotate-180' : ''}`} />
        </button>
        {renderSubmenu(serviceCatalogueMenuItems, serviceCatalogueOpen || false)}
      </div>

      {/* Blog section with collapsible submenu - PRIORITY 3 */}
      <div className="mb-3">
        <div className="h-px bg-[#0A1929]/10 mb-3" />
        <button
          onClick={() => setBlogOpen && setBlogOpen(!blogOpen)}
          className={`flex items-center justify-between w-full px-3 py-2.5 text-sm font-semibold rounded-lg transition-all duration-200 text-[#0A1929] bg-white hover:bg-[#FF5400]/10 hover:text-[#FF5400]`}
        >
          <div className="flex items-center">
            <DocumentTextIcon className="mr-3 h-5 w-5 flex-shrink-0 text-[#FF5400]" />
            <span className="truncate">Blog</span>
          </div>
          <ChevronDownIcon className={`h-4 w-4 flex-shrink-0 transition-transform duration-200 ${blogOpen ? 'rotate-180' : ''}`} />
        </button>
        {renderSubmenu(blogMenuItems, blogOpen || false)}
      </div>

      {/* Content Management section with collapsible submenu - PRIORITY 4 */}
      <div className="mb-3">
        <div className="h-px bg-[#0A1929]/10 mb-3" />
        <button
          onClick={() => setContentOpen && setContentOpen(!contentOpen)}
          className={`flex items-center justify-between w-full px-3 py-2.5 text-sm font-semibold rounded-lg transition-all duration-200 text-[#0A1929] bg-white hover:bg-[#FF5400]/10 hover:text-[#FF5400]`}
        >
          <div className="flex items-center">
            <SquaresPlusIcon className="mr-3 h-5 w-5 flex-shrink-0 text-[#FF5400]" />
            <span className="truncate">Content Management</span>
          </div>
          <ChevronDownIcon className={`h-4 w-4 flex-shrink-0 transition-transform duration-200 ${contentOpen ? 'rotate-180' : ''}`} />
        </button>
        {renderSubmenu(contentMenuItems, contentOpen || false)}
      </div>

      {/* System Administration section with collapsible submenu - PRIORITY 5 */}
      <div className="mb-3">
        <div className="h-px bg-[#0A1929]/10 mb-3" />
        <button
          onClick={() => setSystemOpen && setSystemOpen(!systemOpen)}
          className={`flex items-center justify-between w-full px-3 py-2.5 text-sm font-semibold rounded-lg transition-all duration-200 text-[#0A1929] bg-white hover:bg-[#FF5400]/10 hover:text-[#FF5400]`}
        >
          <div className="flex items-center">
            <ShieldCheckIcon className="mr-3 h-5 w-5 flex-shrink-0 text-[#FF5400]" />
            <span className="truncate">System Administration</span>
          </div>
          <ChevronDownIcon className={`h-4 w-4 flex-shrink-0 transition-transform duration-200 ${systemOpen ? 'rotate-180' : ''}`} />
        </button>
        {renderSubmenu(systemMenuItems, systemOpen || false)}
      </div>

      {/* Settings section with collapsible submenu - PRIORITY 6 (Last) */}
      <div className="mb-3">
        <div className="h-px bg-[#0A1929]/10 mb-3" />
        <button
          onClick={() => setSettingsOpen(!settingsOpen)}
          className={`flex items-center justify-between w-full px-3 py-2.5 text-sm font-semibold rounded-lg transition-all duration-200 text-[#0A1929] bg-white hover:bg-[#FF5400]/10 hover:text-[#FF5400]`}
        >
          <div className="flex items-center">
            <Cog6ToothIcon className="mr-3 h-5 w-5 flex-shrink-0 text-[#FF5400]" />
            <span className="truncate">Settings</span>
          </div>
          <ChevronDownIcon className={`h-4 w-4 flex-shrink-0 transition-transform duration-200 ${settingsOpen ? 'rotate-180' : ''}`} />
        </button>
        {renderSubmenu(settingsMenuItems, settingsOpen)}
      </div>

      {/* Main Site Link */}
      <div className="pt-3">
        <div className="h-px bg-[#0A1929]/10 mb-3" />
        <Link
          href="/"
          className="flex items-center justify-between px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 text-[#0A1929] bg-white hover:bg-[#FF5400]/10 hover:text-[#FF5400]"
          target="_blank"
          rel="noopener noreferrer"
          onClick={onLinkClick}
        >
          <div className="flex items-center">
            <HomeIcon className="mr-3 h-5 w-5 flex-shrink-0 text-[#FF5400]" />
            <span className="truncate">Visit Main Site</span>
          </div>
          <ArrowTopRightOnSquareIcon className="h-4 w-4 flex-shrink-0" />
        </Link>
      </div>
    </nav>
  );
}