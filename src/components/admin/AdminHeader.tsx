'use client';
import React from 'react';

import { useState, useRef, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useAuth } from '@/hooks/useAuth';
import { useRouter } from 'next/navigation';
import {
  Bars3Icon,
  BellIcon,
  HomeIcon,
  UserIcon,
  ArrowRightOnRectangleIcon,
  ChevronDownIcon,
  PlusIcon,
  DocumentTextIcon,
  UserGroupIcon,
  FolderIcon,
  Cog6ToothIcon,
  ClockIcon,
  GlobeAltIcon,
  ArrowTopRightOnSquareIcon,
  ShoppingCartIcon,
  PencilSquareIcon,
} from '@heroicons/react/24/outline';

interface AdminHeaderProps {
  sidebarOpen: boolean;
  onToggleSidebar: () => void;
}

export default function AdminHeader({ sidebarOpen, onToggleSidebar }: AdminHeaderProps) {
  const { user, logout } = useAuth();
  const router = useRouter();
  const [userMenuOpen, setUserMenuOpen] = useState(false);
  const [notificationCount] = useState(3); // Mock notification count
  const userMenuRef = useRef<HTMLDivElement>(null);
  const [showQuickActions, setShowQuickActions] = useState(false);
  const [showUserMenu, setShowUserMenu] = useState(false);

  // Close user menu when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
        setUserMenuOpen(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleSignOut = async () => {
    try {
      await logout();
      router.push('/admin/login');
    } catch (error) {
      console.error('Sign out error:', error);
      // Redirect anyway in case of error
      router.push('/admin/login');
    }
  };

  // Get user initials for avatar
  const getUserInitials = () => {
    if (!user?.name) return user?.username?.charAt(0).toUpperCase() || 'U';
    return user.name
      .split(' ')
      .map(name => name.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <header className="admin-header fixed top-0 left-0 right-0 z-50 h-16 bg-white/95 backdrop-blur-md border-b border-gray-200 shadow-sm">
      <div className="h-full max-w-full mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-full">
          {/* Left section - Logo and Mobile Menu */}
          <div className="flex items-center space-x-3 sm:space-x-4 min-w-0 flex-1">
            {/* Mobile menu button with enhanced touch target */}
            <button
              onClick={onToggleSidebar}
              className="md:hidden admin-button-sm bg-gray-100 text-gray-600 hover:text-gray-900 hover:bg-gray-200 admin-mobile-button"
              aria-label={sidebarOpen ? "Close sidebar" : "Open sidebar"}
              aria-expanded={sidebarOpen}
            >
              <Bars3Icon className="admin-icon" />
            </button>

            {/* Logo section with responsive sizing */}
            <Link href="/admin" className="flex items-center space-x-2 sm:space-x-3 min-w-0 flex-1 group">
              <div className="flex-shrink-0">
                <Image
                  src="/images/logo.png"
                  alt="Mocky Digital Logo"
                  width={32}
                  height={32}
                  className="rounded-md transition-transform duration-200 group-hover:scale-105"
                  priority
                />
              </div>
              <div className="min-w-0 flex-1 admin-hide-mobile sm:block">
                <h1 className="text-lg sm:text-xl font-bold text-gray-900 truncate">Mocky Digital</h1>
                <p className="text-xs sm:text-sm text-gray-500 truncate">Admin Dashboard</p>
              </div>
              {/* Mobile-only compact title */}
              <div className="admin-show-mobile min-w-0">
                <h1 className="text-base font-bold text-gray-900 truncate">Admin</h1>
              </div>
            </Link>
          </div>

          {/* Right section - Actions and User Menu */}
          <div className="flex items-center space-x-2 sm:space-x-3 flex-shrink-0">
            {/* Notifications button - responsive sizing */}
            <button
              className="admin-button-sm bg-gray-100 text-gray-600 hover:text-gray-900 hover:bg-gray-200 relative"
              aria-label="View notifications"
            >
              <BellIcon className="admin-icon" />
              {/* Notification indicator */}
              <span className="absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full border-2 border-white"></span>
            </button>

            {/* Quick actions dropdown - hide on mobile */}
            <div className="relative admin-hide-mobile">
              <button
                onClick={() => setShowQuickActions(!showQuickActions)}
                className="admin-button-sm bg-gray-100 text-gray-600 hover:text-gray-900 hover:bg-gray-200"
                aria-label="Quick actions"
                aria-expanded={showQuickActions}
              >
                <PlusIcon className="admin-icon" />
              </button>
              
              {showQuickActions && (
                <div className="absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
                  <div className="py-2">
                    <Link
                      href="/admin/comprehensive-receipts"
                      className="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                      onClick={() => setShowQuickActions(false)}
                    >
                      <DocumentTextIcon className="admin-icon-sm mr-3 text-green-500" />
                      <div className="flex-1">
                        <div className="font-medium">Receipt Generator</div>
                        <div className="text-xs text-gray-500">From M-Pesa message</div>
                      </div>
                    </Link>
                    <div className="my-1 border-t border-gray-100"></div>
                    <Link
                      href="/admin/blog/new"
                      className="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                      onClick={() => setShowQuickActions(false)}
                    >
                      <DocumentTextIcon className="admin-icon-sm mr-3 text-blue-500" />
                      <div>
                        <div className="font-medium">New Blog Post</div>
                        <div className="text-xs text-gray-500">Create a new article</div>
                      </div>
                    </Link>
                    <Link
                      href="/admin/clients/new"
                      className="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                      onClick={() => setShowQuickActions(false)}
                    >
                      <UserGroupIcon className="admin-icon-sm mr-3 text-green-500" />
                      <div>
                        <div className="font-medium">Add Client</div>
                        <div className="text-xs text-gray-500">Create client profile</div>
                      </div>
                    </Link>
                    <Link
                      href="/admin/portfolio/new"
                      className="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                      onClick={() => setShowQuickActions(false)}
                    >
                      <FolderIcon className="admin-icon-sm mr-3 text-purple-500" />
                      <div>
                        <div className="font-medium">Add Portfolio Item</div>
                        <div className="text-xs text-gray-500">Showcase your work</div>
                      </div>
                    </Link>
                    <div className="my-1 border-t border-gray-100"></div>
                    <Link
                      href="/admin/orders"
                      className="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                      onClick={() => setShowQuickActions(false)}
                    >
                      <ShoppingCartIcon className="admin-icon-sm mr-3 text-blue-500" />
                      <div>
                        <div className="font-medium">View Orders</div>
                        <div className="text-xs text-gray-500">Manage customer orders</div>
                      </div>
                    </Link>
                    <Link
                      href="/admin/design-requests"
                      className="flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                      onClick={() => setShowQuickActions(false)}
                    >
                      <PencilSquareIcon className="admin-icon-sm mr-3 text-orange-500" />
                      <div>
                        <div className="font-medium">Design Requests</div>
                        <div className="text-xs text-gray-500">Manage design services</div>
                      </div>
                    </Link>
                  </div>
                </div>
              )}
            </div>

            {/* User menu with enhanced responsive design */}
            <div className="relative">
              <button
                onClick={() => setShowUserMenu(!showUserMenu)}
                className="flex items-center space-x-2 sm:space-x-3 admin-button-sm bg-gray-100 text-gray-700 hover:bg-gray-200 max-w-48 sm:max-w-none"
                aria-label="User menu"
                aria-expanded={showUserMenu}
              >
                <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center flex-shrink-0">
                  <span className="text-sm font-medium text-blue-600">
                    {getUserInitials()}
                  </span>
                </div>
                <div className="admin-hide-mobile min-w-0 flex-1">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {user?.name || user?.username || 'User'}
                  </p>
                  <p className="text-xs text-gray-500 truncate capitalize">
                    {typeof user?.role === 'object' ? (user.role as any)?.name : user?.role || 'User'}
                  </p>
                </div>
                <ChevronDownIcon className="admin-icon-sm flex-shrink-0" />
              </button>

              {/* User menu dropdown */}
              {showUserMenu && (
                <div className="absolute right-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
                  <div className="py-2">
                    {/* User info section */}
                    <div className="px-4 py-3 border-b border-gray-100">
                      <div className="flex items-center space-x-3">
                        <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center flex-shrink-0">
                          <span className="text-base font-medium text-blue-600">
                            {getUserInitials()}
                          </span>
                        </div>
                        <div className="min-w-0 flex-1">
                          <p className="text-sm font-medium text-gray-900 truncate">
                            {user?.name || user?.username || 'User'}
                          </p>
                          <p className="text-xs text-gray-500 truncate">
                            {user?.email || 'No email'}
                          </p>
                          <p className="text-xs text-gray-400 truncate capitalize">
                            {typeof user?.role === 'object' ? (user.role as any)?.name : user?.role || 'User'} Role
                          </p>
                        </div>
                      </div>
                    </div>

                    {/* Menu items */}
                    <div className="py-1">
                      <Link
                        href="/admin/profile"
                        className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                        onClick={() => setShowUserMenu(false)}
                      >
                        <UserIcon className="admin-icon-sm mr-3 text-gray-400" />
                        Profile Settings
                      </Link>
                      <Link
                        href="/admin/time-tracking"
                        className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                        onClick={() => setShowUserMenu(false)}
                      >
                        <ClockIcon className="admin-icon-sm mr-3 text-gray-400" />
                        Time Tracking
                      </Link>
                      <Link
                        href="/admin/settings"
                        className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                        onClick={() => setShowUserMenu(false)}
                      >
                        <Cog6ToothIcon className="admin-icon-sm mr-3 text-gray-400" />
                        Settings
                      </Link>
                      <div className="my-1 border-t border-gray-100"></div>
                      <Link
                        href="/"
                        target="_blank"
                        className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                        onClick={() => setShowUserMenu(false)}
                      >
                        <GlobeAltIcon className="admin-icon-sm mr-3 text-gray-400" />
                        View Website
                        <ArrowTopRightOnSquareIcon className="admin-icon-sm ml-auto text-gray-300" />
                      </Link>
                      <div className="my-1 border-t border-gray-100"></div>
                      <button
                        onClick={() => {
                          setShowUserMenu(false);
                          handleSignOut();
                        }}
                        className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors"
                      >
                        <ArrowRightOnRectangleIcon className="admin-icon-sm mr-3" />
                        Sign Out
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </header>
  );
} 