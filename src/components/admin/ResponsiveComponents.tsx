'use client';

import React, { ReactNode, useState, useEffect } from 'react';
import { 
  ChevronDownIcon, 
  ChevronUpIcon, 
  MagnifyingGlassIcon,
  AdjustmentsHorizontalIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  InformationCircleIcon,
  XCircleIcon
} from '@heroicons/react/24/outline';

// ============================================================================
// Responsive Container Component
// ============================================================================

interface ResponsiveContainerProps {
  children: ReactNode;
  className?: string;
}

export function ResponsiveContainer({ children, className = '' }: ResponsiveContainerProps) {
  return (
    <div className={`admin-container ${className}`}>
      {children}
    </div>
  );
}

// ============================================================================
// Responsive Grid Component
// ============================================================================

interface ResponsiveGridProps {
  children: ReactNode;
  columns?: {
    mobile?: number;
    tablet?: number;
    desktop?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
  gap?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function ResponsiveGrid({ 
  children, 
  columns = { sm: 1, md: 2, lg: 3, xl: 4 },
  gap = 'md',
  className = '' 
}: ResponsiveGridProps) {
  const gapClasses = {
    sm: 'gap-4',
    md: 'gap-6',
    lg: 'gap-8'
  };

  // Handle both old format (sm/md/lg/xl) and new format (mobile/tablet/desktop)
  const mobile = columns.mobile || columns.sm || 1;
  const tablet = columns.tablet || columns.md || 2;
  const desktop = columns.desktop || columns.lg || 3;
  const xl = columns.xl || 4;

  // Build responsive grid classes
  const gridClasses = [
    'grid',
    gapClasses[gap],
    `grid-cols-${mobile}`,
    `md:grid-cols-${tablet}`,
    `lg:grid-cols-${desktop}`,
    `xl:grid-cols-${xl}`,
    className
  ].filter(Boolean).join(' ');

  return (
    <div className={gridClasses}>
      {children}
    </div>
  );
}

// ============================================================================
// Enhanced Button Component
// ============================================================================

interface ResponsiveButtonProps {
  children?: ReactNode;
  variant?: 'primary' | 'secondary' | 'danger' | 'success' | 'warning' | 'ghost';
  size?: 'xs' | 'sm' | 'md' | 'lg';
  icon?: ReactNode;
  iconPosition?: 'left' | 'right';
  disabled?: boolean;
  loading?: boolean;
  fullWidth?: boolean;
  onClick?: () => void;
  type?: 'button' | 'submit' | 'reset';
  className?: string;
}

export function ResponsiveButton({
  children,
  variant = 'primary',
  size = 'md',
  icon,
  iconPosition = 'left',
  disabled = false,
  loading = false,
  fullWidth = false,
  onClick,
  type = 'button',
  className = ''
}: ResponsiveButtonProps) {
  const variantClasses = {
    primary: 'bg-primary text-white hover:bg-primary-dark focus:ring-primary border-primary',
    secondary: 'bg-secondary text-white hover:bg-secondary-dark focus:ring-secondary border-secondary',
    danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 border-red-600',
    success: 'bg-green-600 text-white hover:bg-green-700 focus:ring-green-500 border-green-600',
    warning: 'bg-yellow-500 text-white hover:bg-yellow-600 focus:ring-yellow-500 border-yellow-500',
    ghost: 'bg-transparent text-gray-600 hover:bg-gray-100 focus:ring-gray-500 border-transparent'
  };

  const sizeClasses = {
    xs: 'admin-button-xs',
    sm: 'admin-button-sm',
    md: 'admin-button',
    lg: 'admin-button px-6 py-3 text-base'
  };

  const baseClasses = [
    sizeClasses[size],
    variantClasses[variant],
    'admin-focus-ring',
    fullWidth ? 'w-full' : '',
    disabled || loading ? 'opacity-50 cursor-not-allowed' : '',
    className
  ].filter(Boolean).join(' ');

  return (
    <button
      type={type}
      onClick={onClick}
      disabled={disabled || loading}
      className={baseClasses}
    >
      {loading ? (
        <div className="flex items-center justify-center space-x-2">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
          <span>Loading...</span>
        </div>
      ) : (
        <div className="flex items-center justify-center space-x-2">
          {icon && iconPosition === 'left' && <span className="admin-icon-sm">{icon}</span>}
          <span>{children}</span>
          {icon && iconPosition === 'right' && <span className="admin-icon-sm">{icon}</span>}
        </div>
      )}
    </button>
  );
}

// ============================================================================
// Enhanced Card Component
// ============================================================================

interface ResponsiveCardProps {
  children: ReactNode;
  title?: string;
  subtitle?: string;
  icon?: ReactNode;
  actions?: ReactNode;
  footer?: ReactNode;
  variant?: 'default' | 'compact' | 'elevated' | 'stat' | 'interactive';
  className?: string;
  clickable?: boolean;
  onClick?: () => void;
}

export function ResponsiveCard({
  children,
  title,
  subtitle,
  icon,
  actions,
  footer,
  variant = 'default',
  className = '',
  clickable = false,
  onClick
}: ResponsiveCardProps) {
  const getBaseClasses = () => {
    switch (variant) {
      case 'compact':
        return 'admin-card-compact';
      case 'stat':
        return 'admin-card admin-card-stat';
      case 'interactive':
        return 'admin-card admin-card-interactive';
      default:
        return 'admin-card';
    }
  };
  
  const baseClasses = getBaseClasses();
  const clickableClasses = clickable ? 'cursor-pointer hover:shadow-lg active:scale-98 transition-all duration-200' : '';
  const elevatedClasses = variant === 'elevated' ? 'shadow-lg hover:shadow-xl' : '';

  return (
    <div 
      className={`${baseClasses} ${clickableClasses} ${elevatedClasses} ${className}`}
      onClick={onClick}
      role={clickable ? 'button' : undefined}
      tabIndex={clickable ? 0 : undefined}
      onKeyDown={clickable ? (e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          onClick?.();
        }
      } : undefined}
    >
      {/* Header */}
      {(title || subtitle || icon || actions) && (
        <div className="flex flex-col sm:flex-row sm:items-start justify-between gap-3 mb-4 pb-4 border-b border-gray-100">
          <div className="flex items-start space-x-3 flex-1 min-w-0">
            {icon && (
              <div className="flex-shrink-0 p-2 bg-primary/10 text-primary rounded-lg">
                {icon}
              </div>
            )}
            <div className="flex-1 min-w-0">
              {title && (
                <h3 className="text-lg font-semibold text-gray-900 truncate">
                  {title}
                </h3>
              )}
              {subtitle && (
                <p className="text-sm text-gray-600 mt-1 admin-text-truncate">
                  {subtitle}
                </p>
              )}
            </div>
          </div>
          {actions && (
            <div className="flex items-center space-x-2 flex-shrink-0">
              {actions}
            </div>
          )}
        </div>
      )}

      {/* Content */}
      <div className="space-y-4">
        {children}
      </div>

      {/* Footer */}
      {footer && (
        <div className="mt-6 pt-4 border-t border-gray-100">
          {footer}
        </div>
      )}
    </div>
  );
}

// ============================================================================
// Enhanced Search Component
// ============================================================================

interface ResponsiveSearchProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  onSubmit?: () => void;
  className?: string;
}

export function ResponsiveSearch({
  value,
  onChange,
  placeholder = "Search...",
  onSubmit,
  className = ''
}: ResponsiveSearchProps) {
  return (
    <form 
      onSubmit={(e) => {
        e.preventDefault();
        onSubmit?.();
      }}
      className={`admin-search-container ${className}`}
    >
      <div className="relative">
        <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
        <input
          type="text"
          value={value}
          onChange={(e) => onChange(e.target.value)}
          placeholder={placeholder}
          className="admin-search-input"
        />
      </div>
    </form>
  );
}

// ============================================================================
// Enhanced Filters Component
// ============================================================================

interface ResponsiveFiltersProps {
  children: ReactNode;
  title?: string;
  collapsible?: boolean;
  defaultExpanded?: boolean;
  onClear?: () => void;
  className?: string;
}

export function ResponsiveFilters({
  children,
  title = "Filters",
  collapsible = true,
  defaultExpanded = true,
  onClear,
  className = ''
}: ResponsiveFiltersProps) {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded);

  return (
    <div className={`admin-card ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <AdjustmentsHorizontalIcon className="h-5 w-5 text-gray-600" />
          <h3 className="text-lg font-medium text-gray-900">{title}</h3>
        </div>
        <div className="flex items-center space-x-2">
          {onClear && (
            <ResponsiveButton
              size="sm"
              variant="secondary"
              onClick={onClear}
            >
              Clear
            </ResponsiveButton>
          )}
          {collapsible && (
            <ResponsiveButton
              size="sm"
              variant="secondary"
              onClick={() => setIsExpanded(!isExpanded)}
              icon={isExpanded ? <ChevronUpIcon /> : <ChevronDownIcon />}
              aria-label={isExpanded ? "Collapse filters" : "Expand filters"}
            />
          )}
        </div>
      </div>
      
      {(!collapsible || isExpanded) && (
        <div className="admin-filters">
          {children}
        </div>
      )}
    </div>
  );
}

// ============================================================================
// Enhanced Alert Component
// ============================================================================

interface ResponsiveAlertProps {
  type: 'success' | 'error' | 'warning' | 'info';
  title?: string;
  children: ReactNode;
  dismissible?: boolean;
  onDismiss?: () => void;
  className?: string;
}

export function ResponsiveAlert({
  type,
  title,
  children,
  dismissible = false,
  onDismiss,
  className = ''
}: ResponsiveAlertProps) {
  const typeConfig = {
    success: {
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200',
      textColor: 'text-green-800',
      icon: <CheckCircleIcon className="h-5 w-5 text-green-400" />
    },
    error: {
      bgColor: 'bg-red-50',
      borderColor: 'border-red-200',
      textColor: 'text-red-800',
      icon: <XCircleIcon className="h-5 w-5 text-red-400" />
    },
    warning: {
      bgColor: 'bg-yellow-50',
      borderColor: 'border-yellow-200',
      textColor: 'text-yellow-800',
      icon: <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400" />
    },
    info: {
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200',
      textColor: 'text-blue-800',
      icon: <InformationCircleIcon className="h-5 w-5 text-blue-400" />
    }
  };

  const config = typeConfig[type];

  return (
    <div className={`rounded-lg border p-4 ${config.bgColor} ${config.borderColor} ${className}`}>
      <div className="flex items-start">
        <div className="flex-shrink-0">
          {config.icon}
        </div>
        <div className="ml-3 flex-1">
          {title && (
            <h3 className={`text-sm font-medium ${config.textColor} mb-2`}>
              {title}
            </h3>
          )}
          <div className={`text-sm ${config.textColor}`}>
            {children}
          </div>
        </div>
        {dismissible && onDismiss && (
          <div className="ml-auto pl-3">
            <button
              type="button"
              onClick={onDismiss}
              className={`inline-flex rounded-md p-1.5 ${config.textColor} hover:bg-white/20 focus:outline-none focus:ring-2 focus:ring-offset-2`}
            >
              <span className="sr-only">Dismiss</span>
              <XCircleIcon className="h-5 w-5" />
            </button>
          </div>
        )}
      </div>
    </div>
  );
}

// ============================================================================
// Enhanced Loading Component
// ============================================================================

interface ResponsiveLoadingProps {
  size?: 'sm' | 'md' | 'lg';
  text?: string;
  rows?: number;
  className?: string;
}

export function ResponsiveLoading({
  size = 'md',
  text,
  rows = 3,
  className = ''
}: ResponsiveLoadingProps) {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-8 w-8',
    lg: 'h-12 w-12'
  };

  // If rows is specified, show skeleton loading
  if (rows > 0) {
    return (
      <div className={`admin-loading ${className}`}>
        {Array.from({ length: rows }).map((_, index) => (
          <div key={index} className="admin-loading-item">
            <div className="admin-loading-skeleton admin-loading-skeleton-title" />
            <div className="admin-loading-skeleton admin-loading-skeleton-text" />
            <div className="admin-loading-skeleton admin-loading-skeleton-text admin-loading-skeleton-short" />
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className={`flex items-center justify-center space-x-2 ${className}`}>
      <div className={`animate-spin rounded-full border-b-2 border-blue-600 ${sizeClasses[size]}`}></div>
      {text && <span className="text-sm text-gray-600">{text}</span>}
    </div>
  );
}

// ============================================================================
// Enhanced Modal Component
// ============================================================================

interface ResponsiveModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: ReactNode;
  footer?: ReactNode;
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  className?: string;
}

export function ResponsiveModal({
  isOpen,
  onClose,
  title,
  children,
  footer,
  size = 'md',
  className = ''
}: ResponsiveModalProps) {
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  if (!isOpen) return null;

  const sizeClasses = {
    sm: 'max-w-md',
    md: 'max-w-lg',
    lg: 'max-w-2xl',
    xl: 'max-w-4xl',
    full: 'max-w-7xl'
  };

  return (
    <div className="admin-modal">
      <div 
        className="admin-modal-overlay"
        onClick={onClose}
      />
      <div className="admin-modal-container">
        <div className={`admin-modal-content ${sizeClasses[size]} ${className}`}>
          {/* Header */}
          {title && (
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">{title}</h3>
              <ResponsiveButton
                size="sm"
                variant="secondary"
                onClick={onClose}
                icon={<XCircleIcon />}
                aria-label="Close modal"
              >
                <span className="sr-only">Close</span>
              </ResponsiveButton>
            </div>
          )}

          {/* Content */}
          <div className="p-6">
            {children}
          </div>

          {/* Footer */}
          {footer && (
            <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50">
              {footer}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// ============================================================================
// Enhanced Table Component
// ============================================================================

interface ResponsiveTableProps {
  children: ReactNode;
  className?: string;
}

export function ResponsiveTable({
  children,
  className = ''
}: ResponsiveTableProps) {
  return (
    <div className={`admin-table-container ${className}`}>
      <table className="admin-table">
        {children}
      </table>
    </div>
  );
}

// ============================================================================
// Enhanced Pagination Component
// ============================================================================

interface ResponsivePaginationProps {
  currentPage: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  onPageChange: (page: number) => void;
  className?: string;
}

export function ResponsivePagination({
  currentPage,
  totalPages,
  hasNextPage,
  hasPreviousPage,
  onPageChange,
  className = ''
}: ResponsivePaginationProps) {
  return (
    <div className={`admin-pagination ${className}`}>
      <div className="admin-pagination-controls">
        <ResponsiveButton
          variant="secondary"
          size="sm"
          onClick={() => onPageChange(currentPage - 1)}
          disabled={!hasPreviousPage}
        >
          Previous
        </ResponsiveButton>
        
        <span className="admin-pagination-info">
          Page {currentPage} of {totalPages}
        </span>
        
        <ResponsiveButton
          variant="secondary"
          size="sm"
          onClick={() => onPageChange(currentPage + 1)}
          disabled={!hasNextPage}
        >
          Next
        </ResponsiveButton>
      </div>
    </div>
  );
}

export default {
  ResponsiveContainer,
  ResponsiveGrid,
  ResponsiveButton,
  ResponsiveCard,
  ResponsiveSearch,
  ResponsiveFilters,
  ResponsiveAlert,
  ResponsiveLoading,
  ResponsiveModal,
  ResponsiveTable,
  ResponsivePagination
}; 