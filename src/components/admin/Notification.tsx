'use client';

import { Fragment, useState, useEffect } from 'react';
import { Transition } from '@headlessui/react';
import {
  CheckCircleIcon,
  ExclamationCircleIcon,
  InformationCircleIcon,
  XMarkIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

export type NotificationType = 'success' | 'error' | 'info' | 'warning';

interface NotificationProps {
  type: NotificationType;
  title: string;
  message?: string;
  show: boolean;
  onClose: () => void;
  autoClose?: boolean;
  duration?: number;
  action?: {
    label: string;
    onClick: () => void;
  };
}

export default function Notification({
  type,
  title,
  message,
  show,
  onClose,
  autoClose = true,
  duration = 5000,
  action,
}: NotificationProps) {
  const [isVisible, setIsVisible] = useState(show);
  const [progress, setProgress] = useState(100);

  useEffect(() => {
    setIsVisible(show);
    if (show) {
      setProgress(100);
    }
  }, [show]);

  useEffect(() => {
    if (autoClose && isVisible) {
      const startTime = Date.now();
      const timer = setTimeout(() => {
        setIsVisible(false);
        setTimeout(onClose, 300); // Wait for exit animation
      }, duration);

      // Progress bar animation
      const progressTimer = setInterval(() => {
        const elapsed = Date.now() - startTime;
        const remaining = Math.max(0, (duration - elapsed) / duration * 100);
        setProgress(remaining);
      }, 50);

      return () => {
        clearTimeout(timer);
        clearInterval(progressTimer);
      };
    }
  }, [autoClose, duration, isVisible, onClose]);

  const getIcon = () => {
    const iconClass = "h-6 w-6";
    switch (type) {
      case 'success':
        return <CheckCircleIcon className={`${iconClass} text-green-500`} aria-hidden="true" />;
      case 'error':
        return <ExclamationCircleIcon className={`${iconClass} text-red-500`} aria-hidden="true" />;
      case 'warning':
        return <ExclamationTriangleIcon className={`${iconClass} text-orange-500`} aria-hidden="true" />;
      case 'info':
        return <InformationCircleIcon className={`${iconClass} text-blue-500`} aria-hidden="true" />;
      default:
        return <InformationCircleIcon className={`${iconClass} text-blue-500`} aria-hidden="true" />;
    }
  };

  const getStyles = () => {
    switch (type) {
      case 'success':
        return {
          background: 'bg-gradient-to-r from-green-50 to-emerald-50',
          border: 'border-green-200',
          accent: 'bg-green-500',
          iconBg: 'bg-green-100',
          titleColor: 'text-green-900',
          messageColor: 'text-green-700',
          closeHover: 'hover:bg-green-100',
          actionButton: 'bg-green-600 hover:bg-green-700 text-white'
        };
      case 'error':
        return {
          background: 'bg-gradient-to-r from-red-50 to-rose-50',
          border: 'border-red-200',
          accent: 'bg-red-500',
          iconBg: 'bg-red-100',
          titleColor: 'text-red-900',
          messageColor: 'text-red-700',
          closeHover: 'hover:bg-red-100',
          actionButton: 'bg-red-600 hover:bg-red-700 text-white'
        };
      case 'warning':
        return {
          background: 'bg-gradient-to-r from-orange-50 to-amber-50',
          border: 'border-orange-200',
          accent: 'bg-orange-500',
          iconBg: 'bg-orange-100',
          titleColor: 'text-orange-900',
          messageColor: 'text-orange-700',
          closeHover: 'hover:bg-orange-100',
          actionButton: 'bg-orange-600 hover:bg-orange-700 text-white'
        };
      case 'info':
        return {
          background: 'bg-gradient-to-r from-blue-50 to-indigo-50',
          border: 'border-blue-200',
          accent: 'bg-blue-500',
          iconBg: 'bg-blue-100',
          titleColor: 'text-blue-900',
          messageColor: 'text-blue-700',
          closeHover: 'hover:bg-blue-100',
          actionButton: 'bg-blue-600 hover:bg-blue-700 text-white'
        };
      default:
        return {
          background: 'bg-gradient-to-r from-gray-50 to-slate-50',
          border: 'border-gray-200',
          accent: 'bg-gray-500',
          iconBg: 'bg-gray-100',
          titleColor: 'text-gray-900',
          messageColor: 'text-gray-700',
          closeHover: 'hover:bg-gray-100',
          actionButton: 'bg-gray-600 hover:bg-gray-700 text-white'
        };
    }
  };

  const styles = getStyles();

  const handleClose = () => {
    setIsVisible(false);
    setTimeout(onClose, 300); // Wait for exit animation
  };

  return (
    <div
      aria-live="assertive"
      className="fixed inset-0 flex items-end px-4 py-6 pointer-events-none sm:p-6 sm:items-start z-50"
    >
      <div className="w-full flex flex-col items-center space-y-4 sm:items-end">
        <Transition
          show={isVisible}
          as={Fragment}
          enter="transform ease-out duration-500 transition"
          enterFrom="translate-y-2 opacity-0 sm:translate-y-0 sm:translate-x-2 scale-95"
          enterTo="translate-y-0 opacity-100 sm:translate-x-0 scale-100"
          leave="transition ease-in duration-300"
          leaveFrom="opacity-100 scale-100"
          leaveTo="opacity-0 scale-95"
        >
          <div className={`max-w-md w-full ${styles.background} backdrop-blur-sm shadow-2xl rounded-2xl pointer-events-auto border ${styles.border} overflow-hidden`}>
            {/* Progress bar */}
            {autoClose && (
              <div className="h-1 bg-gray-200">
                <div
                  className={`h-full ${styles.accent} transition-all duration-100 ease-linear`}
                  style={{ width: `${progress}%` }}
                />
              </div>
            )}

            <div className="p-5">
              <div className="flex items-start">
                {/* Icon with background */}
                <div className={`flex-shrink-0 p-2 rounded-xl ${styles.iconBg}`}>
                  {getIcon()}
                </div>

                {/* Content */}
                <div className="ml-4 w-0 flex-1">
                  <p className={`text-base font-semibold ${styles.titleColor} leading-tight`}>
                    {title}
                  </p>
                  {message && (
                    <p className={`mt-2 text-sm ${styles.messageColor} leading-relaxed`}>
                      {message}
                    </p>
                  )}

                  {/* Action button */}
                  {action && (
                    <div className="mt-4">
                      <button
                        type="button"
                        className={`inline-flex items-center px-4 py-2 text-sm font-medium rounded-lg ${styles.actionButton} transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-opacity-50`}
                        onClick={() => {
                          action.onClick();
                          handleClose();
                        }}
                      >
                        {action.label}
                      </button>
                    </div>
                  )}
                </div>

                {/* Close button */}
                <div className="ml-4 flex-shrink-0">
                  <button
                    type="button"
                    className={`p-2 rounded-lg text-gray-400 hover:text-gray-600 ${styles.closeHover} transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500`}
                    onClick={handleClose}
                  >
                    <span className="sr-only">Close</span>
                    <XMarkIcon className="h-5 w-5" aria-hidden="true" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </Transition>
      </div>
    </div>
  );
}
