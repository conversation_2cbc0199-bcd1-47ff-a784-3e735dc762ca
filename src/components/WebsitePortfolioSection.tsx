'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { WebsitePortfolioItem, WEBSITE_CATEGORIES } from '@/types/portfolio';
import { getS3ImageUrl } from '@/utils/imageUtils';

interface WebsitePortfolioSectionProps {
  title?: string;
  subtitle?: string;
}

export default function WebsitePortfolioSection({ 
  title = "Website Development", 
  subtitle = "Custom websites designed for performance and user experience" 
}: WebsitePortfolioSectionProps) {
  const [websitePortfolioItems, setWebsitePortfolioItems] = useState<WebsitePortfolioItem[]>([]);
  const [filteredItems, setFilteredItems] = useState<WebsitePortfolioItem[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [imageUrls, setImageUrls] = useState<Record<string, string>>({});

  useEffect(() => {
    const fetchWebsitePortfolioItems = async () => {
      try {
        setLoading(true);
        setError('');

        const response = await fetch(`/api/website-portfolio?t=${Date.now()}`, {
          cache: 'no-store',
          headers: {
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache'
          }
        });

        if (!response.ok) {
          throw new Error(`Failed to fetch website portfolio items: ${response.statusText}`);
        }

        const data = await response.json();
        setWebsitePortfolioItems(data);
        setFilteredItems(data);

        // Fetch S3 URLs for all images
        const urls: Record<string, string> = {};
        for (const item of data) {
          urls[item.id] = await getS3ImageUrl(item.imageKey);
        }
        setImageUrls(urls);
      } catch (err) {
        console.error('Error fetching website portfolio items:', err);
        setError('Failed to load website portfolio items. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchWebsitePortfolioItems();
  }, []);

  useEffect(() => {
    if (selectedCategory === 'all') {
      setFilteredItems(websitePortfolioItems);
    } else {
      setFilteredItems(websitePortfolioItems.filter(item => item.category === selectedCategory));
    }
  }, [selectedCategory, websitePortfolioItems]);

  if (loading) {
    return (
      <section id="websites" className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-8 text-center">{title}</h2>
          <p className="text-center text-gray-600 max-w-3xl mx-auto mb-12">{subtitle}</p>
          <div className="flex justify-center">
            <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-primary border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]"></div>
          </div>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section id="websites" className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-8 text-center">{title}</h2>
          <p className="text-center text-gray-600 max-w-3xl mx-auto mb-12">{subtitle}</p>
          <div className="text-center py-12">
            <p className="text-red-500">{error}</p>
          </div>
        </div>
      </section>
    );
  }

  if (websitePortfolioItems.length === 0) {
    return (
      <section id="websites" className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold mb-8 text-center">{title}</h2>
          <p className="text-center text-gray-600 max-w-3xl mx-auto mb-12">{subtitle}</p>
          <div className="text-center py-12">
            <p className="text-gray-600">No website portfolio items found.</p>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section id="websites" className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <h2 className="text-3xl font-bold mb-8 text-center">{title}</h2>
        <p className="text-center text-gray-600 max-w-3xl mx-auto mb-12">{subtitle}</p>
        
        {/* Category Filter */}
        <div className="flex flex-wrap justify-center gap-2 mb-12">
          <button
            onClick={() => setSelectedCategory('all')}
            className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
              selectedCategory === 'all'
                ? 'bg-primary text-white'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            All
          </button>
          {WEBSITE_CATEGORIES.map((category) => (
            <button
              key={category.value}
              onClick={() => setSelectedCategory(category.value)}
              className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                selectedCategory === category.value
                  ? 'bg-primary text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              {category.label}
            </button>
          ))}
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {filteredItems.map((item) => (
            <div
              key={item.id}
              className="group relative bg-white rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all duration-300 border border-gray-100"
            >
              <div className="relative aspect-video bg-gray-100">
                <Image
                  src={imageUrls[item.id] || '/images/placeholder.jpg'}
                  alt={item.title}
                  fill
                  className="object-cover transition-all group-hover:scale-105 duration-500"
                  sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw"
                />
              </div>
              <div className="p-4">
                <h3 className="text-lg font-semibold mb-1 group-hover:text-primary transition-colors">
                  {item.title}
                </h3>
                <p className="text-sm text-gray-600 mb-3 line-clamp-2">{item.description}</p>
                <div className="flex justify-between items-center">
                  <span className="text-xs px-2 py-1 bg-gray-100 text-gray-700 rounded-full">
                    {WEBSITE_CATEGORIES.find(cat => cat.value === item.category)?.label || item.category}
                  </span>
                  <a
                    href={item.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm text-primary hover:text-primary-dark transition-colors flex items-center gap-1"
                  >
                    Visit <i className="fas fa-external-link-alt text-xs"></i>
                  </a>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
