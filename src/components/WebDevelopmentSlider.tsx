'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { ChevronLeftIcon, ChevronRightIcon, ArrowTopRightOnSquareIcon, ArrowRightIcon } from '@heroicons/react/24/outline';
import { motion } from 'framer-motion';

interface WebsitePortfolioItem {
  id: string;
  title: string;
  description: string;
  imageSrc: string;
  url: string;
  category: string;
  technologies: string[];
}

export default function WebDevelopmentSlider() {
  const [websites, setWebsites] = useState<WebsitePortfolioItem[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  // Fetch website portfolio items
  useEffect(() => {
    async function fetchWebsites() {
      try {
        const response = await fetch('/api/website-portfolio');
        if (response.ok) {
          const data = await response.json();
          // Take only first 6 items for the slider
          setWebsites(data.slice(0, 6));
        }
      } catch (error) {
        console.error('Error fetching website portfolio:', error);
      } finally {
        setIsLoading(false);
      }
    }

    fetchWebsites();
  }, []);

  // Get slides per view based on screen size
  const getSlidesPerView = () => {
    if (typeof window !== 'undefined') {
      if (window.innerWidth < 768) return 1; // Mobile
      if (window.innerWidth < 1024) return 2; // Tablet
      return 3; // Desktop - 3 websites in a row
    }
    return 3;
  };

  const [slidesPerView, setSlidesPerView] = useState(3);

  // Update slides per view on resize
  useEffect(() => {
    const handleResize = () => {
      setSlidesPerView(getSlidesPerView());
    };

    handleResize(); // Set initial value
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Calculate max index based on slides per view
  const maxIndex = Math.max(0, websites.length - slidesPerView);

  // Auto-rotate slides
  useEffect(() => {
    if (!isAutoPlaying || websites.length === 0 || maxIndex === 0) return;

    const interval = setInterval(() => {
      setCurrentIndex((current) => (current + 1) % (maxIndex + 1));
    }, 6000);

    return () => clearInterval(interval);
  }, [isAutoPlaying, websites.length, maxIndex]);

  const goToPrevious = () => {
    setCurrentIndex((current) => (current === 0 ? maxIndex : current - 1));
    setIsAutoPlaying(false);
    setTimeout(() => setIsAutoPlaying(true), 10000);
  };

  const goToNext = () => {
    setCurrentIndex((current) => (current === maxIndex ? 0 : current + 1));
    setIsAutoPlaying(false);
    setTimeout(() => setIsAutoPlaying(true), 10000);
  };

  if (isLoading) {
    return (
      <section className="relative py-24 bg-gray-50 overflow-hidden">
        <div className="container mx-auto px-6">
          <div className="text-center mb-20">
            <div className="animate-pulse">
              <div className="h-4 bg-gray-300 rounded w-32 mx-auto mb-4"></div>
              <div className="h-12 bg-gray-300 rounded w-96 mx-auto mb-4"></div>
              <div className="h-6 bg-gray-300 rounded w-64 mx-auto"></div>
            </div>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {Array.from({ length: 3 }).map((_, index) => (
              <div key={index} className="bg-gray-300 rounded-xl aspect-[4/3] animate-pulse"></div>
            ))}
          </div>
        </div>
      </section>
    );
  }

  if (websites.length === 0) {
    return null;
  }

  return (
    <section className="relative py-24 bg-gray-50 overflow-hidden">
      {/* Modern Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Subtle geometric shapes */}
        <div className="absolute top-20 left-10 w-2 h-2 bg-[#FF5400] rounded-full opacity-60"></div>
        <div className="absolute top-40 right-20 w-1 h-1 bg-[#FF5400] rounded-full opacity-40"></div>
        <div className="absolute bottom-32 left-1/4 w-3 h-3 bg-[#FF5400]/20 rounded-full"></div>
        <div className="absolute bottom-20 right-1/3 w-2 h-2 bg-[#FF5400]/30 rounded-full"></div>
        
        {/* Subtle gradient overlays */}
        <div className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-[#FF5400]/5 to-transparent rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 right-0 w-96 h-96 bg-gradient-to-tl from-[#0A1929]/5 to-transparent rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {(() => {
          const HeaderContent = () => (
            <div className="text-center mb-12 sm:mb-16 lg:mb-20">
              {/* Section Tag */}
              <div className="flex items-center justify-center gap-3 mb-8">
                <div className="w-8 h-[2px] bg-[#FF5400]"></div>
                <span className="text-[#FF5400] uppercase tracking-wider font-semibold text-sm">
                  Web Development
                </span>
                <div className="w-8 h-[2px] bg-[#FF5400]"></div>
              </div>

              {/* Main Heading */}
              <h2 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-[#0A1929] mb-6 sm:mb-8 leading-tight">
                Website
                <span className="block text-[#FF5400] relative">
                  Showcase
                  <span className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-16 sm:w-20 md:w-24 h-1 bg-[#FF5400]/20 rounded-full"></span>
                </span>
              </h2>

              {/* Description */}
              <p className="text-lg sm:text-xl text-gray-600 max-w-2xl sm:max-w-3xl lg:max-w-4xl mx-auto leading-relaxed font-light mb-3 sm:mb-4 px-4 sm:px-0">
                Explore our latest web development projects featuring modern design, cutting-edge technology, and exceptional user experiences.
              </p>

              <p className="text-base sm:text-lg text-gray-700 max-w-xl sm:max-w-2xl lg:max-w-3xl mx-auto leading-relaxed px-4 sm:px-0">
                From business websites to e-commerce platforms, each project is crafted to deliver outstanding performance and results.
              </p>

              {/* View All Portfolio Button */}
              <div className="mt-10">
                <Link 
                  href="/web-development" 
                  className="inline-flex items-center gap-3 bg-[#FF5400] hover:bg-[#FF5400]/90 text-white px-8 py-4 rounded-full font-semibold transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl group"
                >
                  <span>View All Websites</span>
                  <ArrowRightIcon className="w-5 h-5 transition-transform group-hover:translate-x-1" />
                </Link>
              </div>
            </div>
          );

          return isMounted ? (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <HeaderContent />
            </motion.div>
          ) : (
            <HeaderContent />
          );
        })()}

        {/* Slider Container */}
        <div className="relative max-w-7xl mx-auto">
          {/* Navigation Arrows - Only show if there are multiple slides */}
          {maxIndex > 0 && (
            <>
              {isMounted ? (
                <motion.button
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={goToPrevious}
                  className="absolute left-2 md:-left-6 top-1/2 -translate-y-1/2 z-20 bg-white hover:bg-gray-50 text-[#0A1929] p-3 md:p-4 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-200"
                  aria-label="Previous websites"
                >
                  <ChevronLeftIcon className="w-6 h-6 md:w-7 md:h-7" />
                </motion.button>
              ) : (
                <button
                  onClick={goToPrevious}
                  className="absolute left-2 md:-left-6 top-1/2 -translate-y-1/2 z-20 bg-white hover:bg-gray-50 text-[#0A1929] p-3 md:p-4 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-200"
                  aria-label="Previous websites"
                >
                  <ChevronLeftIcon className="w-6 h-6 md:w-7 md:h-7" />
                </button>
              )}

              {isMounted ? (
                <motion.button
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.95 }}
                  onClick={goToNext}
                  className="absolute right-2 md:-right-6 top-1/2 -translate-y-1/2 z-20 bg-white hover:bg-gray-50 text-[#0A1929] p-3 md:p-4 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-200"
                  aria-label="Next websites"
                >
                  <ChevronRightIcon className="w-6 h-6 md:w-7 md:h-7" />
                </motion.button>
              ) : (
                <button
                  onClick={goToNext}
                  className="absolute right-2 md:-right-6 top-1/2 -translate-y-1/2 z-20 bg-white hover:bg-gray-50 text-[#0A1929] p-3 md:p-4 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-200"
                  aria-label="Next websites"
                >
                  <ChevronRightIcon className="w-6 h-6 md:w-7 md:h-7" />
                </button>
              )}
            </>
          )}

          {/* Websites Container */}
          <div className="overflow-hidden rounded-xl sm:rounded-2xl bg-white/50 border border-gray-100 p-4 sm:p-6 lg:p-8">
            <div
              className="flex transition-transform duration-700 ease-out"
              style={{ transform: `translateX(-${currentIndex * (100 / slidesPerView)}%)` }}
            >
              {websites.map((website, index) => (
                <div
                  key={website.id}
                  className="w-full md:w-1/2 lg:w-1/3 flex-shrink-0 px-2 sm:px-3"
                  style={{ width: `${100 / slidesPerView}%` }}
                >
                  <div className="group relative bg-white rounded-lg sm:rounded-xl shadow-md hover:shadow-xl transition-all duration-500 transform hover:-translate-y-1 border border-gray-200 overflow-hidden h-full flex flex-col">
                    {/* Image Container */}
                    <div className="relative aspect-[4/3] overflow-hidden">
                      <Image
                        src={website.imageSrc || '/images/placeholder.jpg'}
                        alt={website.title}
                        fill
                        sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw"
                        className="object-cover transition-all duration-700 group-hover:scale-105"
                        loading={index < 3 ? "eager" : "lazy"}
                        quality={85}
                      />

                      {/* Modern Overlay */}
                      <div className="absolute inset-0 bg-gradient-to-t from-[#0A1929]/90 via-[#0A1929]/40 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-500">
                        <div className="absolute bottom-0 left-0 right-0 p-4 sm:p-5 lg:p-6">
                          <div>
                            <div className="inline-flex items-center px-2.5 sm:px-3 py-1 bg-[#FF5400] text-white text-xs font-semibold rounded-full mb-2 sm:mb-3 shadow-lg">
                              <span className="mr-1">🌐</span>
                              {website.category || 'Website'}
                            </div>
                            <h3 className="text-white text-base sm:text-lg font-bold line-clamp-2 mb-1 sm:mb-2 leading-tight">
                              {website.title}
                            </h3>
                            <div className="flex items-center text-white/80 text-xs sm:text-sm">
                              <span>Visit Website</span>
                              <ArrowTopRightOnSquareIcon className="w-3 h-3 sm:w-4 sm:h-4 ml-2 transition-transform group-hover:translate-x-1" />
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Content */}
                    <div className="p-4 sm:p-5 lg:p-6 flex-1 flex flex-col">
                      <h3 className="text-lg sm:text-xl font-bold text-[#0A1929] mb-2 group-hover:text-[#FF5400] transition-colors leading-tight">
                        {website.title}
                      </h3>
                      <p className="text-gray-600 text-sm sm:text-base mb-3 sm:mb-4 line-clamp-2 leading-relaxed flex-1">
                        {website.description}
                      </p>

                      {/* Technologies */}
                      {website.technologies && website.technologies.length > 0 && (
                        <div className="flex flex-wrap gap-1.5 sm:gap-2 mb-3 sm:mb-4">
                          {website.technologies.slice(0, 3).map((tech, techIndex) => (
                            <span
                              key={techIndex}
                              className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full font-medium"
                            >
                              {tech}
                            </span>
                          ))}
                        </div>
                      )}

                      {/* Action Buttons */}
                      <div className="flex flex-col sm:flex-row gap-2 sm:gap-3 mt-auto">
                        <a
                          href={website.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex-1 bg-[#FF5400] hover:bg-[#FF5400]/90 text-white text-center py-2.5 sm:py-2 px-4 rounded-lg font-medium transition-colors text-sm"
                        >
                          Visit Site
                        </a>
                        <Link
                          href={`/web-development?project=${website.id}`}
                          className="flex-1 border border-gray-300 hover:border-[#FF5400] hover:text-[#FF5400] text-gray-700 text-center py-2.5 sm:py-2 px-4 rounded-lg font-medium transition-colors text-sm"
                        >
                          View Details
                        </Link>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Modern Dots Indicator */}
          {maxIndex > 0 && (
            <div className="flex justify-center mt-12 space-x-3">
              {Array.from({ length: maxIndex + 1 }).map((_, index) => (
                <button
                  key={index}
                  onClick={() => {
                    setCurrentIndex(index);
                    setIsAutoPlaying(false);
                    setTimeout(() => setIsAutoPlaying(true), 10000);
                  }}
                  className={`relative transition-all duration-500 hover:scale-110 ${
                    currentIndex === index
                      ? 'w-8 h-3'
                      : 'w-3 h-3'
                  }`}
                  aria-label={`Go to slide ${index + 1}`}
                >
                  <div className={`w-full h-full rounded-full transition-all duration-500 ${
                    currentIndex === index
                      ? 'bg-[#FF5400] shadow-lg'
                      : 'bg-gray-300 hover:bg-gray-400'
                  }`} />
                </button>
              ))}
            </div>
          )}
        </div>
      </div>
    </section>
  );
}
