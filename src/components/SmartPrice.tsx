'use client';

import React, { useState, useEffect } from 'react';
import { useCurrency } from '@/hooks/useCurrency';

interface SmartPriceProps {
  kesAmount: number;
  className?: string;
  showOriginal?: boolean;
  showCurrencyName?: boolean;
  size?: 'sm' | 'md' | 'lg' | 'xl';
}

export default function SmartPrice({ 
  kesAmount, 
  className = '', 
  showOriginal = false,
  showCurrencyName = false,
  size = 'md'
}: SmartPriceProps) {
  const { currency, convertAndFormat, format, isLoading, location } = useCurrency();
  const [convertedPrice, setConvertedPrice] = useState<string>('');
  const [originalPrice, setOriginalPrice] = useState<string>('');
  const [lastCurrency, setLastCurrency] = useState<string>('');

  useEffect(() => {
    const updatePrices = async () => {
      try {
        const isDev = process.env.NODE_ENV === 'development';
        if (isDev) console.log(`Converting ${kesAmount} KES to ${currency} (was ${lastCurrency})`);
        
        // Convert to user's currency
        const converted = await convertAndFormat(kesAmount);
        if (isDev) console.log(`Converted price: ${converted}`);
        setConvertedPrice(converted);
        setLastCurrency(currency);

        // Format original KES price
        if (showOriginal && currency !== 'KES') {
          const original = format(kesAmount, 'KES');
          setOriginalPrice(original);
        } else {
          setOriginalPrice('');
        }
      } catch (error) {
        console.error('Failed to convert price:', error);
        // Fallback to KES
        setConvertedPrice(format(kesAmount, 'KES'));
        setLastCurrency(currency);
      }
    };

    // Always update if currency changed or if not loading
    if (!isLoading && (currency !== lastCurrency || !convertedPrice)) {
      updatePrices();
    }
  }, [kesAmount, currency, convertAndFormat, format, isLoading, showOriginal, lastCurrency, convertedPrice]);

  // Size classes
  const sizeClasses = {
    sm: 'text-sm',
    md: 'text-lg',
    lg: 'text-xl',
    xl: 'text-2xl'
  };

  // Loading state
  if (isLoading) {
    return (
      <div className={`${sizeClasses[size]} ${className}`}>
        <div className="animate-pulse bg-gray-200 rounded h-6 w-20"></div>
      </div>
    );
  }

  return (
    <div className={`${sizeClasses[size]} ${className}`}>
      <span className="font-bold text-[#FF5400]">
        {convertedPrice || format(kesAmount, 'KES')}
      </span>
      
      {showCurrencyName && currency !== 'KES' && (
        <span className="text-xs text-gray-500 ml-1">
          {currency}
        </span>
      )}
      
      {showOriginal && originalPrice && currency !== 'KES' && (
        <span className="text-xs text-gray-400 ml-2 line-through">
          {originalPrice}
        </span>
      )}
      
      {location && currency !== 'KES' && (
        <span className="text-xs text-gray-500 block">
          (Converted from KES for {location.country})
        </span>
      )}
    </div>
  );
} 