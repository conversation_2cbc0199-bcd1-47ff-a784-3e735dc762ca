'use client';
import React from 'react';

import { useState, useTransition, useEffect, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { MagnifyingGlassIcon, XMarkIcon } from '@heroicons/react/24/outline';

interface BlogSearchProps {
  initialValue?: string;
}

export default function BlogSearch({ initialValue = '' }: BlogSearchProps) {
  const [searchTerm, setSearchTerm] = useState(initialValue);
  const [isPending, startTransition] = useTransition();
  const router = useRouter();
  const searchParams = useSearchParams();

  // Update search term when initialValue changes (e.g., when navigating back)
  useEffect(() => {
    setSearchTerm(initialValue);
  }, [initialValue]);

  // Get current search from URL params
  useEffect(() => {
    const currentSearch = searchParams?.get('search') || '';
    setSearchTerm(currentSearch);
  }, [searchParams]);

  // Debounced search function
  const debouncedSearch = useCallback(
    debounce((term: string) => {
      startTransition(() => {
        if (term.trim()) {
          router.push(`/blog?search=${encodeURIComponent(term.trim())}`);
        } else {
          router.push('/blog');
        }
      });
    }, 500), // 500ms delay
    [router]
  );

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);
    
    // Trigger debounced search
    debouncedSearch(value);
  };

  const handleClear = () => {
    setSearchTerm('');
    startTransition(() => {
      router.push('/blog');
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Immediate search on form submit (bypasses debounce)
    startTransition(() => {
      if (searchTerm.trim()) {
        router.push(`/blog?search=${encodeURIComponent(searchTerm.trim())}`);
      } else {
        router.push('/blog');
      }
    });
  };

  return (
    <form onSubmit={handleSubmit} className="w-full relative">
      <div className="relative">
        <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
          <MagnifyingGlassIcon className="w-5 h-5 text-gray-400" />
        </div>
        <input
          type="text"
          value={searchTerm}
          onChange={handleInputChange}
          className="bg-white border border-gray-200 text-gray-900 text-sm block w-full pl-10 pr-10 p-3 rounded-lg focus:outline-none focus:border-[#FF5400] focus:ring-2 focus:ring-[#FF5400]/20 transition-all duration-200"
          placeholder="Search blog posts..."
          aria-label="Search blog posts"
        />
        
        {/* Clear button */}
        {searchTerm && (
          <button
            type="button"
            onClick={handleClear}
            className="absolute right-8 inset-y-0 flex items-center text-gray-400 hover:text-gray-600 transition-colors"
            aria-label="Clear search"
          >
            <XMarkIcon className="w-4 h-4" />
          </button>
        )}

        {/* Loading spinner */}
        {isPending && (
          <div className="absolute right-2.5 inset-y-0 flex items-center">
            <div className="w-4 h-4 border-2 border-[#FF5400] border-t-transparent rounded-full animate-spin"></div>
          </div>
        )}
      </div>
      <button type="submit" className="sr-only">
        Search
      </button>
    </form>
  );
}

// Debounce utility function
function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}