import type { NavItem } from '../types';

/**
 * Navigation configuration - single source of truth for navigation items
 * Extracted from Navbar component for better maintainability
 */
export const NAV_ITEMS: NavItem[] = [
  { href: "/", label: "Home", icon: "home" },
  { href: "/about", label: "About", icon: "users" },
  {
    label: "Services",
    icon: "briefcase",
    href: "#",
    dropdown: [
      { href: "/services", label: "All Services", icon: "grid" },
      {
        label: "Design & Creative",
        isGroup: true,
        items: [
          { href: "/logos", label: "Logo Design", icon: "star" },
          { href: "/graphics", label: "Graphic Design", icon: "brush" },
          { href: "/branding", label: "Brand Identity", icon: "fingerprint" }
        ]
      },
      {
        label: "Digital Services",
        isGroup: true,
        items: [
          { href: "/web-development", label: "Web Development", icon: "code" },
          { href: "/social-media", label: "Social Media", icon: "hash" },
          { href: "/digital-marketing", label: "Digital Marketing", icon: "speaker" }
        ]
      }
    ]
  },
  { href: "/portfolio", label: "Portfolio", icon: "folder" },
  { href: "/catalogue", label: "Catalogue", icon: "grid" },
  { href: "/blog", label: "Blog", icon: "document", badge: "New" },
  { href: "/contact", label: "Contact", icon: "mail" }
]; 