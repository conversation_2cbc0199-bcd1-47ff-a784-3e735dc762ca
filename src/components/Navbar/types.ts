import type { IconName } from '@/components/icons';

export interface NavDropdownGroup {
  label: string;
  isGroup: true;
  items: { href: string; label: string; icon: IconName }[];
}

export interface NavDropdownItem {
  href: string;
  label: string;
  icon: IconName;
}

export interface NavItem {
  href?: string;
  label: string;
  badge?: string;
  dropdown?: (NavDropdownItem | NavDropdownGroup)[];
  icon?: IconName;
}

export interface NavbarState {
  isOpen: boolean;
  activeDropdown: string | null;
  scrolled: boolean;
  mounted: boolean;
} 