import { memo, useState } from 'react';
import Link from 'next/link';
import Image from 'next/image';

/**
 * Navigation logo component with loading states
 * Extracted from main Navbar for better modularity and reusability
 */
const NavLogo = memo(function NavLogo() {
  const [isLoading, setIsLoading] = useState(true);

  return (
    <Link
      href="/"
      className="flex items-center space-x-3 group focus:outline-none focus:ring-2 focus:ring-orange-500 rounded-lg p-1 transition-all duration-200"
    >
      <div className="relative">
        {isLoading && (
          <>
            {/* Loading placeholder */}
            <div className="absolute inset-0 bg-gray-200 rounded-lg animate-pulse" />
          </>
        )}
        <Image
          src="/images/logo.png"
          alt="Mocky Digital Logo"
          width={40}
          height={40}
          className={`rounded-lg transition-all duration-200 group-hover:scale-105 ${
            isLoading ? 'opacity-0' : 'opacity-100'
          }`}
          priority
          onLoad={() => setIsLoading(false)}
        />
      </div>
      
      {/* Mobile text */}
      <div className="block sm:hidden">
        <div className="font-bold text-gray-900 text-base group-hover:text-orange-600 transition-colors">
          Mocky Digital
        </div>
      </div>
      
      {/* Desktop text */}
      <div className="hidden sm:block">
        <div className="font-bold text-gray-900 text-lg group-hover:text-orange-600 transition-colors">
          Mocky Digital
        </div>
        <div className="text-xs text-gray-500 -mt-1">Creative Agency</div>
      </div>
    </Link>
  );
});

NavLogo.displayName = 'NavLogo';

export default NavLogo; 