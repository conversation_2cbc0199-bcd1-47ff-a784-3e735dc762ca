import { memo } from 'react';
import Link from 'next/link';
import { Icon } from '@/components/icons';

interface CTAButtonsProps {
  isMobile?: boolean;
}

/**
 * Call-to-action buttons for navigation (admin link, etc.)
 * Extracted from main Navbar for better modularity and reusability
 */
const CTAButtons = memo<CTAButtonsProps>(function CTAButtons({ isMobile = false }) {
  const baseClasses = isMobile 
    ? "w-full px-6 py-3 rounded-lg font-medium text-center transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2" 
    : "px-4 py-2 rounded-lg font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2";

  return (
    <div className={`flex ${isMobile ? 'flex-col space-y-3' : 'items-center space-x-3'}`}>
      <Link
        href="/admin"
        className={`${baseClasses} text-white bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 focus:ring-orange-500 flex items-center shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 group`}
      >
        <Icon name="cog" className="w-4 h-4 mr-2 transition-transform group-hover:rotate-180" />
        Admin
      </Link>
    </div>
  );
});

CTAButtons.displayName = 'CTAButtons';

export default CTAButtons; 