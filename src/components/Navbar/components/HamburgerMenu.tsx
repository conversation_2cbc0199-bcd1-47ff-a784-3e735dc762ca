import { memo } from 'react';
import { motion } from 'framer-motion';

interface HamburgerMenuProps {
  isOpen: boolean;
  onToggle: () => void;
}

/**
 * Animated hamburger menu button for mobile navigation
 * Extracted from main Navbar for better modularity and reusability
 */
const HamburgerMenu = memo<HamburgerMenuProps>(function HamburgerMenu({ 
  isOpen, 
  onToggle 
}) {
  return (
    <button
      onClick={onToggle}
      className="relative w-10 h-10 lg:hidden focus:outline-none focus:ring-2 focus:ring-orange-500 rounded-lg p-2"
      data-mobile-menu-toggle="true"
      aria-label="Toggle navigation menu"
      aria-expanded={isOpen}
    >
      <div className="absolute inset-0 flex items-center justify-center">
        <div className="w-5 h-4 relative">
          <motion.div
            className="absolute left-0 w-full h-0.5 bg-gray-700"
            animate={{
              top: isOpen ? '50%' : '0%',
              rotate: isOpen ? 45 : 0,
            }}
            transition={{ duration: 0.2 }}
          />
          <motion.div
            className="absolute left-0 top-2 w-full h-0.5 bg-gray-700"
            animate={{
              opacity: isOpen ? 0 : 1,
            }}
            transition={{ duration: 0.2 }}
          />
          <motion.div
            className="absolute left-0 w-full h-0.5 bg-gray-700"
            animate={{
              top: isOpen ? '50%' : '100%',
              rotate: isOpen ? -45 : 0,
            }}
            transition={{ duration: 0.2 }}
          />
        </div>
      </div>
    </button>
  );
});

HamburgerMenu.displayName = 'HamburgerMenu';

export default HamburgerMenu; 