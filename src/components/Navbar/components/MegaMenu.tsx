import { memo } from 'react';
import Link from 'next/link';
import { motion, AnimatePresence } from 'framer-motion';
import { Icon } from '@/components/icons';
import type { NavItem, NavDropdownGroup, NavDropdownItem } from '../types';

interface MegaMenuProps {
  item: NavItem;
  isOpen: boolean;
  onClose: () => void;
}

/**
 * Mega menu dropdown component for complex navigation structures
 * Extracted from main Navbar for better modularity and reusability
 */
const MegaMenu = memo<MegaMenuProps>(function MegaMenu({ 
  item, 
  isOpen, 
  onClose 
}) {
  if (!item.dropdown || !isOpen) return null;

  const groupItems = item.dropdown.filter(i => 'isGroup' in i && i.isGroup) as NavDropdownGroup[];
  const nonGroupItems = item.dropdown.filter(i => !('isGroup' in i)) as NavDropdownItem[];

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: -10 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -10 }}
        transition={{ duration: 0.2 }}
        className="absolute top-full left-1/2 transform -translate-x-1/2 mt-2 w-screen max-w-4xl z-50 dropdown-container"
        data-dropdown-toggle="true"
      >
        <div className="bg-white rounded-2xl shadow-2xl border border-gray-100 overflow-hidden">
          <div className="p-8">
            {/* Featured (non-group) items as a single row */}
            {nonGroupItems.length > 0 && (
              <div className="mb-4 flex flex-col space-y-2">
                {nonGroupItems.map((featuredItem) => (
                  <Link
                    key={featuredItem.href}
                    href={featuredItem.href || '#'}
                    onClick={onClose}
                    className="group p-4 rounded-xl bg-gradient-to-br from-orange-50 to-red-50 hover:from-orange-100 hover:to-red-100 border border-orange-200 transition-all duration-200 transform hover:-translate-y-1 flex items-center space-x-4"
                  >
                    <div className="p-3 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg text-white transform group-hover:scale-110 transition-transform">
                      <Icon name={featuredItem.icon || 'grid'} className="w-5 h-5" />
                    </div>
                    <h3 className="font-semibold text-gray-900 group-hover:text-orange-600 transition-colors">
                      {featuredItem.label}
                    </h3>
                  </Link>
                ))}
              </div>
            )}

            {/* Grouped categories in a grid */}
            {groupItems.length > 0 && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                {groupItems.map((dropdownItem) => (
                  <div key={dropdownItem.label}>
                    <h3 className="font-bold text-gray-900 mb-4 text-lg border-b border-gray-200 pb-2">
                      {dropdownItem.label}
                    </h3>
                    <div className="space-y-3">
                      {dropdownItem.items?.map((service) => (
                        <Link
                          key={service.href}
                          href={service.href || '#'}
                          onClick={onClose}
                          className="group flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-all duration-200"
                        >
                          <div className="p-2 bg-gray-100 rounded-lg group-hover:bg-orange-100 group-hover:text-orange-600 transition-all duration-200">
                            <Icon name={service.icon || 'star'} className="w-4 h-4" />
                          </div>
                          <div>
                            <div className="font-medium text-gray-900 group-hover:text-orange-600 transition-colors">
                              {service.label}
                            </div>
                          </div>
                        </Link>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
});

MegaMenu.displayName = 'MegaMenu';

export default MegaMenu; 