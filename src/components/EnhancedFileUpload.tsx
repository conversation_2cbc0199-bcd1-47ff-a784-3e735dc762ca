'use client';

import React, { useState, useCallback, useRef } from 'react';
import { Button } from './ui/button';
import { Card } from './ui/card';
import { chunkedUploadService, UploadProgress } from '../services/chunkedUploadService';
import { detectConnectionSpeed, ConnectionSpeed } from '../utils/connectionUtils';
import { shouldCompressFile } from '../utils/imageCompressionUtils';

interface EnhancedFileUploadProps {
  onUploadComplete?: (result: { url: string; fileName: string; fileSize: number }) => void;
  onUploadError?: (error: string) => void;
  accept?: string;
  maxFileSize?: number;
  folder?: string;
  className?: string;
  disabled?: boolean;
}

interface UploadState {
  isUploading: boolean;
  progress: UploadProgress | null;
  connectionSpeed: ConnectionSpeed | null;
  compressionEnabled: boolean;
  error: string | null;
  resumableUploads: Array<{
    uploadId: string;
    fileName: string;
    progress: number;
  }>;
}

export default function EnhancedFileUpload({
  onUploadComplete,
  onUploadError,
  accept = 'image/*',
  maxFileSize = 100 * 1024 * 1024, // 100MB default
  folder = 'uploads',
  className = '',
  disabled = false
}: EnhancedFileUploadProps) {
  const [state, setState] = useState<UploadState>({
    isUploading: false,
    progress: null,
    connectionSpeed: null,
    compressionEnabled: false,
    error: null,
    resumableUploads: []
  });

  const fileInputRef = useRef<HTMLInputElement>(null);
  const abortControllerRef = useRef<AbortController | null>(null);

  // Detect connection speed on component mount
  React.useEffect(() => {
    detectConnectionSpeed().then(connectionInfo => {
      setState(prev => ({
        ...prev,
        connectionSpeed: connectionInfo.speed
      }));
    });

    // Load resumable uploads
    const resumable = chunkedUploadService.getResumableUploads();
    setState(prev => ({
      ...prev,
      resumableUploads: resumable
    }));
  }, []);

  const handleFileSelect = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file size
    if (file.size > maxFileSize) {
      const error = `File size (${Math.round(file.size / 1024 / 1024)}MB) exceeds maximum allowed size (${Math.round(maxFileSize / 1024 / 1024)}MB)`;
      setState(prev => ({ ...prev, error }));
      onUploadError?.(error);
      return;
    }

    await startUpload(file);
  }, [maxFileSize]);

  const startUpload = async (file: File, resumeUploadId?: string) => {
    try {
      setState(prev => ({
        ...prev,
        isUploading: true,
        error: null,
        progress: null,
        compressionEnabled: state.connectionSpeed ? shouldCompressFile(file, state.connectionSpeed) : false
      }));

      // Create new abort controller
      abortControllerRef.current = new AbortController();

      const result = await chunkedUploadService.startUpload(
        file,
        (progress) => {
          setState(prev => ({
            ...prev,
            progress
          }));
        },
        {
          endpoint: '/api/upload',
          metadata: { folder },
          resumeUploadId
        }
      );

      if (result.success && result.data) {
        setState(prev => ({
          ...prev,
          isUploading: false,
          progress: null,
          resumableUploads: chunkedUploadService.getResumableUploads()
        }));

        onUploadComplete?.({
          url: result.data.url,
          fileName: result.data.fileName,
          fileSize: result.data.fileSize
        });

        // Clear file input
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
      } else {
        throw new Error(result.error || 'Upload failed');
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Upload failed';
      setState(prev => ({
        ...prev,
        isUploading: false,
        error: errorMessage,
        progress: null
      }));
      onUploadError?.(errorMessage);
    }
  };

  const handleCancel = useCallback(() => {
    if (state.progress?.uploadId) {
      chunkedUploadService.cancelUpload(state.progress.uploadId);
    }
    
    abortControllerRef.current?.abort();
    
    setState(prev => ({
      ...prev,
      isUploading: false,
      progress: null,
      error: null
    }));
  }, [state.progress?.uploadId]);

  const handleResume = useCallback(async (uploadId: string, fileName: string) => {
    // For resume, we need to re-select the file
    // In a real implementation, you might store file references or ask user to re-select
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = accept;
    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file && file.name === fileName) {
        await startUpload(file, uploadId);
      } else {
        setState(prev => ({
          ...prev,
          error: 'Please select the same file to resume upload'
        }));
      }
    };
    input.click();
  }, [accept]);

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatTime = (seconds: number): string => {
    if (seconds < 60) return `${Math.round(seconds)}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.round(seconds % 60);
    return `${minutes}m ${remainingSeconds}s`;
  };

  const getProgressColor = (stage: string): string => {
    switch (stage) {
      case 'preparing': return 'bg-blue-500';
      case 'compressing': return 'bg-orange-500';
      case 'uploading': return 'bg-green-500';
      case 'finalizing': return 'bg-purple-500';
      case 'complete': return 'bg-green-600';
      case 'error': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Connection Status */}
      {state.connectionSpeed && (
        <div className="text-sm text-gray-600 mb-4">
          <span className="font-medium">Connection:</span> 
          <span className={`ml-1 px-2 py-1 rounded text-xs ${
            state.connectionSpeed === 'fast' ? 'bg-green-100 text-green-800' :
            state.connectionSpeed === 'slow' ? 'bg-yellow-100 text-yellow-800' :
            'bg-red-100 text-red-800'
          }`}>
            {state.connectionSpeed.toUpperCase()}
          </span>
          {state.compressionEnabled && (
            <span className="ml-2 px-2 py-1 rounded text-xs bg-blue-100 text-blue-800">
              Auto-compression enabled
            </span>
          )}
        </div>
      )}

      {/* File Input */}
      <div className="flex gap-2">
        <input
          ref={fileInputRef}
          type="file"
          accept={accept}
          onChange={handleFileSelect}
          disabled={disabled || state.isUploading}
          className="hidden"
        />
        
        <Button
          onClick={() => fileInputRef.current?.click()}
          disabled={disabled || state.isUploading}
          className="flex-1"
        >
          {state.isUploading ? 'Uploading...' : 'Select File'}
        </Button>

        {state.isUploading && (
          <Button
            onClick={handleCancel}
            variant="outline"
            className="px-4"
          >
            Cancel
          </Button>
        )}
      </div>

      {/* Upload Progress */}
      {state.progress && (
        <Card className="p-4">
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="font-medium truncate">{state.progress.fileName}</span>
              <span className="text-sm text-gray-500">
                {formatFileSize(state.progress.totalSize)}
              </span>
            </div>

            {/* Progress Bar */}
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className={`h-2 rounded-full transition-all duration-300 ${getProgressColor(state.progress.stage)}`}
                style={{ width: `${state.progress.percentage}%` }}
              />
            </div>

            {/* Progress Details */}
            <div className="flex justify-between text-sm text-gray-600">
              <span className="capitalize">{state.progress.stage.replace('-', ' ')}</span>
              <span>{state.progress.percentage}%</span>
            </div>

            {/* Chunked Upload Details */}
            {state.progress.totalChunks > 1 && (
              <div className="text-xs text-gray-500">
                Chunks: {state.progress.chunksCompleted}/{state.progress.totalChunks}
                {state.progress.uploadSpeed && (
                  <span className="ml-4">
                    Speed: {formatFileSize(state.progress.uploadSpeed)}/s
                  </span>
                )}
                {state.progress.estimatedTimeRemaining && (
                  <span className="ml-4">
                    ETA: {formatTime(state.progress.estimatedTimeRemaining)}
                  </span>
                )}
              </div>
            )}
          </div>
        </Card>
      )}

      {/* Error Display */}
      {state.error && (
        <Card className="p-4 border-red-200 bg-red-50">
          <div className="text-red-800 text-sm">
            <strong>Upload Error:</strong> {state.error}
          </div>
        </Card>
      )}

      {/* Resumable Uploads */}
      {state.resumableUploads.length > 0 && (
        <Card className="p-4">
          <h4 className="font-medium mb-3">Resume Previous Uploads</h4>
          <div className="space-y-2">
            {state.resumableUploads.map((upload) => (
              <div key={upload.uploadId} className="flex justify-between items-center p-2 bg-gray-50 rounded">
                <div className="flex-1">
                  <div className="font-medium text-sm truncate">{upload.fileName}</div>
                  <div className="text-xs text-gray-500">{Math.round(upload.progress)}% complete</div>
                </div>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleResume(upload.uploadId, upload.fileName)}
                  disabled={state.isUploading}
                >
                  Resume
                </Button>
              </div>
            ))}
          </div>
        </Card>
      )}
    </div>
  );
} 