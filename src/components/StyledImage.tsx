'use client';

import { motion } from 'framer-motion';
import Image from 'next/image';

interface StyledImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  aspectRatio?: string;
  showDecorations?: boolean;
  priority?: boolean;
  objectPosition?: string;
  overlayGradient?: boolean;
  hoverEffect?: boolean;
}

export default function StyledImage({
  src,
  alt,
  width = 600,
  height = 700,
  className = '',
  aspectRatio = 'aspect-[4/5]',
  showDecorations = true,
  priority = false,
  objectPosition = 'center 10%',
  overlayGradient = true,
  hoverEffect = true
}: StyledImageProps) {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      whileInView={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.8 }}
      viewport={{ once: true }}
      className={`relative ${hoverEffect ? 'group' : ''} ${className}`}
    >
      {/* Modern image container */}
      <div className="relative rounded-2xl overflow-hidden shadow-2xl bg-white p-2">
        <div className="relative rounded-xl overflow-hidden">
          <Image
            src={src}
            alt={alt}
            width={width}
            height={height}
            className={`object-cover w-full ${aspectRatio} ${
              hoverEffect ? 'transition-transform duration-500 group-hover:scale-105' : ''
            }`}
            style={{ objectPosition }}
            priority={priority}
          />

          {/* Subtle gradient overlay */}
          {overlayGradient && (
            <div className="absolute inset-0 bg-gradient-to-t from-[#0A2647]/80 via-transparent to-transparent"></div>
          )}
        </div>
      </div>

      {/* Decorative elements */}
      {showDecorations && (
        <>
          <div className="absolute -top-4 -left-4 w-8 h-8 border-2 border-[#FF5400] rounded-full opacity-60"></div>
          <div className="absolute top-1/4 -right-2 w-4 h-4 bg-[#FF5400] rounded-full opacity-40"></div>
        </>
      )}
    </motion.div>
  );
} 