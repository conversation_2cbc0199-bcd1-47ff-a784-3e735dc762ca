'use client';
import React from 'react';

import { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import { ChevronLeftIcon, ChevronRightIcon, SparklesIcon, PrinterIcon, PaintBrushIcon, PlayIcon, PauseIcon } from '@heroicons/react/24/outline';

interface Unit {
  id: string;
  name: string;
  displayName: string;
  plural: string;
  shortForm?: string;
  category: string;
}

interface SimplePricingCardProps {
  id: string;
  service: string;
  price: number;
  designFee?: number | undefined;
  description?: string | undefined;
  features?: string[] | undefined;
  icon?: string | undefined;
  popular?: boolean | undefined;
  imageUrl?: string | undefined;
  imageUrl2?: string | undefined;
  imageUrl3?: string | undefined;
  category?: string | undefined;
  pricingType?: string | undefined;
  unitType?: string | undefined; // Deprecated - for backward compatibility
  unitId?: string | undefined;
  unit?: Unit | undefined;
  pricePerMeter?: number | undefined;
  autoSlideInterval?: number; // Auto-slide interval in milliseconds (default: 4000)
  autoSlideEnabled?: boolean; // Enable/disable auto-slide (default: true)
}

export default function SimplePricingCard({
  id,
  service,
  price,
  designFee = 0,
  description,
  features,
  icon,
  popular = false,
  imageUrl,
  imageUrl2,
  imageUrl3,
  category,
  pricingType,
  unitType,
  unitId,
  unit,
  pricePerMeter,
  autoSlideInterval = 4000,
  autoSlideEnabled = true
}: SimplePricingCardProps) {
  // State for image carousel
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [isAutoSliding, setIsAutoSliding] = useState(autoSlideEnabled);
  const [isPaused, setIsPaused] = useState(false);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  // Collect all available images and ensure they have proper URLs
  const images = [imageUrl, imageUrl2, imageUrl3]
    .filter(Boolean)
    .map(url => {
      // If URL is missing protocol but contains linodeobjects.com, add https://
      if (url && url.includes('linodeobjects.com') && !url.startsWith('http')) {
        return `https://${url}`;
      }
      return url;
    }) as string[];

  const hasMultipleImages = images.length > 1;

  // Auto-slide functionality
  useEffect(() => {
    if (!hasMultipleImages || !isAutoSliding || isPaused) {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      return;
    }

    intervalRef.current = setInterval(() => {
      setCurrentImageIndex((prevIndex) =>
        prevIndex === images.length - 1 ? 0 : prevIndex + 1
      );
    }, autoSlideInterval);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [hasMultipleImages, isAutoSliding, isPaused, images.length, autoSlideInterval]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  // Utility function to get unit display information
  const getUnitDisplay = () => {
    // Use new unit system if available
    if (unit) {
      return {
        singular: unit.displayName.toLowerCase(),
        plural: unit.plural.toLowerCase(),
        short: unit.shortForm || unit.displayName.toLowerCase()
      };
    }
    
    // Fallback to old unitType for backward compatibility
    if (unitType) {
      const type = unitType.toLowerCase();
      if (type.includes('meter')) {
        return { singular: 'meter', plural: 'meters', short: 'm' };
      }
      if (type.includes('page')) {
        return { singular: 'page', plural: 'pages', short: 'pg' };
      }
      if (type.includes('piece')) {
        return { singular: 'piece', plural: 'pieces', short: 'pc' };
      }
      if (type.includes('item')) {
        return { singular: 'item', plural: 'items', short: 'item' };
      }
    }
    
    // Default fallback
    return { singular: 'copy', plural: 'copies', short: 'pcs' };
  };

  // Format price to include commas for thousands
  const isPerMeter = pricingType === 'per_meter';
  const displayPrice = isPerMeter ? (pricePerMeter || price) : price;
  const formattedPrice = displayPrice.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  const formattedDesignFee = designFee.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  const unitDisplay = getUnitDisplay();

  // Image navigation handlers
  const goToNextImage = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setCurrentImageIndex((prevIndex) =>
      prevIndex === images.length - 1 ? 0 : prevIndex + 1
    );
  };

  const goToPrevImage = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setCurrentImageIndex((prevIndex) =>
      prevIndex === 0 ? images.length - 1 : prevIndex - 1
    );
  };

  // Auto-slide control handlers
  const toggleAutoSlide = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsAutoSliding(!isAutoSliding);
  };

  const handleMouseEnter = () => {
    setIsPaused(true);
  };

  const handleMouseLeave = () => {
    setIsPaused(false);
  };

  const goToImage = (e: React.MouseEvent, index: number) => {
    e.preventDefault();
    e.stopPropagation();
    setCurrentImageIndex(index);
  };

  return (
    <div className="group relative">
      <Link href={`/product/${id}`} className="block">
        {/* Glassmorphism Card */}
        <div className="relative flex flex-col h-full bg-white/80 backdrop-blur-lg rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 border border-white/20 cursor-pointer transform hover:-translate-y-2 hover:scale-[1.02]">
          
          {/* Glass overlay effect */}
          <div className="absolute inset-0 bg-gradient-to-br from-white/30 via-white/10 to-transparent pointer-events-none" />
          


          {/* Image Section */}
          {images.length > 0 && (
            <div 
              className="relative w-full pt-[75%] bg-gradient-to-br from-gray-50/50 to-gray-100/50 overflow-hidden"
              onMouseEnter={handleMouseEnter}
              onMouseLeave={handleMouseLeave}
            >
              <img
                src={images[currentImageIndex]}
                alt={`${service} - image ${currentImageIndex + 1}`}
                className="absolute inset-0 w-full h-full object-cover transition-all duration-500 group-hover:scale-110"
              />

              {/* Category tag with glassmorphism */}
              {category && (
                <div className="absolute top-4 left-4 bg-white/30 backdrop-blur-md text-gray-800 text-xs px-3 py-1.5 rounded-full font-medium z-10 border border-white/40 shadow-lg">
                  {category}
                </div>
              )}

              {/* Auto-slide control button */}
              {hasMultipleImages && (
                <button
                  onClick={toggleAutoSlide}
                  className="absolute top-4 right-16 bg-black/20 backdrop-blur-md hover:bg-black/30 text-white rounded-full p-2 transition-all duration-300 shadow-lg opacity-0 group-hover:opacity-100 border border-white/20 z-10"
                  aria-label={isAutoSliding ? 'Pause auto-slide' : 'Resume auto-slide'}
                  title={isAutoSliding ? 'Pause auto-slide' : 'Resume auto-slide'}
                >
                  {isAutoSliding ? (
                    <PauseIcon className="h-3 w-3" />
                  ) : (
                    <PlayIcon className="h-3 w-3" />
                  )}
                </button>
              )}

              {/* Image counter indicator */}
              {hasMultipleImages && (
                <div className="absolute top-4 right-4 bg-black/20 backdrop-blur-md text-white text-xs px-3 py-1.5 rounded-full font-medium shadow-lg z-10 border border-white/20">
                  {currentImageIndex + 1}/{images.length}
                </div>
              )}

              {/* Navigation arrows for multiple images */}
              {hasMultipleImages && (
                <>
                  <button
                    onClick={goToPrevImage}
                    className="absolute left-3 top-1/2 -translate-y-1/2 bg-white/20 backdrop-blur-md hover:bg-white/30 text-gray-800 rounded-full p-2.5 transition-all duration-300 shadow-lg opacity-0 group-hover:opacity-100 border border-white/30"
                    aria-label="Previous image"
                  >
                    <ChevronLeftIcon className="h-4 w-4" />
                  </button>

                  <button
                    onClick={goToNextImage}
                    className="absolute right-3 top-1/2 -translate-y-1/2 bg-white/20 backdrop-blur-md hover:bg-white/30 text-gray-800 rounded-full p-2.5 transition-all duration-300 shadow-lg opacity-0 group-hover:opacity-100 border border-white/30"
                    aria-label="Next image"
                  >
                    <ChevronRightIcon className="h-4 w-4" />
                  </button>
                </>
              )}

              {/* Image navigation dots */}
              {hasMultipleImages && (
                <div className="absolute bottom-4 left-0 right-0 flex justify-center space-x-2">
                  {images.map((_, index) => (
                    <button
                      key={index}
                      onClick={(e) => goToImage(e, index)}
                      className={`w-2 h-2 rounded-full transition-all duration-300 ${
                        currentImageIndex === index
                          ? 'bg-orange-500 border-2 border-white shadow-lg w-3 h-3'
                          : 'bg-white/50 backdrop-blur-sm hover:bg-white/70 border border-white/30'
                      }`}
                      aria-label={`Go to image ${index + 1}`}
                    />
                  ))}
                </div>
              )}

            </div>
          )}

          <div className="relative p-6 flex flex-col flex-grow">
            {/* Icon (if no images) */}
            {images.length === 0 && icon && (
              <div className="text-orange-500 mb-4 p-3 bg-orange-100/50 rounded-full w-fit">
                <i className={`fas fa-${icon} text-xl`}></i>
              </div>
            )}

            {/* Service Title */}
            <h3 className="text-lg font-bold text-gray-900 mb-3 hover:text-orange-600 transition-colors duration-300 line-clamp-2 min-h-[3.5rem] group-hover:text-orange-500">{service}</h3>

            {/* Description */}
            {description && (
              <p className="text-sm text-gray-600 mb-4 line-clamp-2 leading-relaxed">{description}</p>
            )}

            {/* Simplified Price Display */}
            <div className="mb-4 flex-grow flex items-end">
              <div className="text-center w-full">
                <div className="bg-gradient-to-r from-orange-500 to-red-500 bg-clip-text text-transparent">
                  <div className="text-lg font-medium mb-1">
                    {isPerMeter ? 'KSh' : 'From KSh'}
                  </div>
                  <div className="text-4xl font-black">{formattedPrice}</div>
                  {isPerMeter && (
                    <div className="text-sm font-medium mt-1">per {unitDisplay.singular}</div>
                  )}
                </div>
              </div>
            </div>

            {/* Clean Pricing Section */}
            <div className="relative mt-auto -mx-6 -mb-6 bg-white/50 backdrop-blur-sm border-t border-white/30">
              <div className="p-4 space-y-3">
                {/* Production Cost */}
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Production Cost (per {unitDisplay.singular})</span>
                  <span className="font-semibold text-gray-800">KSh {formattedPrice}</span>
                </div>

                {/* Design Fee */}
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Design Fee (one-time)</span>
                  <span className="font-semibold text-gray-800">
                    {designFee > 0 ? `KSh ${formattedDesignFee}` : 'Quote on request'}
                  </span>
                </div>

                {/* Action Button */}
                <div className="pt-2 mt-3">
                  <button
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      window.location.href = `/product/${id}`;
                    }}
                    className="w-full bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white font-medium py-2 px-3 transition-all duration-200 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-1"
                  >
                    View Details
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Link>
    </div>
  );
}
