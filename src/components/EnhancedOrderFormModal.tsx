import React, { useState, useEffect, useCallback } from 'react';
import { XMarkIcon, CheckIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { FormField } from '@/components/ui/form-field';
import { Loading, OrderStatusLoading } from '@/components/ui/loading';
import ArtworkUpload from '@/components/ArtworkUpload';
import { fieldValidators } from '@/lib/validation/order-schemas';

interface EnhancedOrderFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: () => void;
  isSubmitting: boolean;
  product: any;
  customerInfo: {
    name: string;
    phone: string;
    email: string;
    notes: string;
  };
  setCustomerInfo: (info: any) => void;
  designBrief: string;
  setDesignBrief: (value: string) => void;
  uploadedArtwork: any[];
  setUploadedArtwork: (files: any[]) => void;
  quantity: number;
  orderType: 'print' | 'design';
}

interface FormErrors {
  name?: string;
  phone?: string;
  email?: string;
  designBrief?: string;
  general?: string;
}

interface FormTouched {
  name: boolean;
  phone: boolean;
  email: boolean;
  designBrief: boolean;
}

export default function EnhancedOrderFormModal({
  isOpen,
  onClose,
  onSubmit,
  isSubmitting,
  product,
  customerInfo,
  setCustomerInfo,
  designBrief,
  setDesignBrief,
  uploadedArtwork,
  setUploadedArtwork,
  quantity,
  orderType
}: EnhancedOrderFormModalProps) {
  const [errors, setErrors] = useState<FormErrors>({});
  const [touched, setTouched] = useState<FormTouched>({
    name: false,
    phone: false,
    email: false,
    designBrief: false
  });
  const [submissionStatus, setSubmissionStatus] = useState<'idle' | 'submitting' | 'success' | 'error'>('idle');
  const [validationDebounce, setValidationDebounce] = useState<{ [key: string]: NodeJS.Timeout }>({});

  // Real-time validation with debouncing
  const validateField = useCallback(async (fieldName: string, value: string | number) => {
    // Clear existing debounce
    if (validationDebounce[fieldName]) {
      clearTimeout(validationDebounce[fieldName]);
    }

    // Set new debounce
    const timeout = setTimeout(async () => {
      try {
        let error: string | null = null;

        switch (fieldName) {
          case 'name':
            error = fieldValidators.name(value as string);
            break;
          case 'phone':
            error = fieldValidators.phone(value as string);
            break;
          case 'email':
            error = fieldValidators.email(value as string);
            break;
          case 'designBrief':
            error = fieldValidators.designBrief(value as string, orderType);
            break;
        }

        setErrors(prev => ({
          ...prev,
          [fieldName]: error || undefined
        }));

      } catch (err) {
        console.error('Validation error:', err);
      }
    }, 300); // 300ms debounce

    setValidationDebounce(prev => ({
      ...prev,
      [fieldName]: timeout
    }));
  }, [orderType, validationDebounce]);

  // Clean up debounce timers
  useEffect(() => {
    return () => {
      Object.values(validationDebounce).forEach(clearTimeout);
    };
  }, [validationDebounce]);

  // Handle field changes with validation
  const handleFieldChange = (fieldName: keyof typeof customerInfo, value: string) => {
    setCustomerInfo({
      ...customerInfo,
      [fieldName]: value
    });

    // Validate if field has been touched
    if (touched[fieldName as keyof FormTouched]) {
      validateField(fieldName, value);
    }
  };

  const handleFieldBlur = (fieldName: keyof FormTouched) => {
    setTouched(prev => ({
      ...prev,
      [fieldName]: true
    }));

    // Validate on blur
    const value = fieldName === 'designBrief' ? designBrief : customerInfo[fieldName];
    if (value) {
      validateField(fieldName, value);
    }
  };

  const handleDesignBriefChange = (value: string) => {
    setDesignBrief(value);
    
    if (touched.designBrief) {
      validateField('designBrief', value);
    }
  };

  // Check if form is valid
  const isFormValid = () => {
    const hasErrors = Object.values(errors).some(error => error);
    const hasRequiredFields = customerInfo.name && customerInfo.phone && customerInfo.email;
    const hasDesignBriefIfNeeded = orderType === 'design' ? designBrief.trim().length >= 10 : true;
    const hasArtworkOrDesign = orderType === 'print' ? 
      (uploadedArtwork.length > 0 || designBrief.trim().length >= 10) : true;

    return !hasErrors && hasRequiredFields && hasDesignBriefIfNeeded && hasArtworkOrDesign;
  };

  // Enhanced submit handler
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Mark all fields as touched
    setTouched({
      name: true,
      phone: true,
      email: true,
      designBrief: true
    });

    // Validate all fields
    const nameError = fieldValidators.name(customerInfo.name);
    const phoneError = fieldValidators.phone(customerInfo.phone);
    const emailError = fieldValidators.email(customerInfo.email);
    const designBriefError = fieldValidators.designBrief(designBrief, orderType);

    const formErrors: FormErrors = {};
    if (nameError) formErrors.name = nameError;
    if (phoneError) formErrors.phone = phoneError;
    if (emailError) formErrors.email = emailError;
    if (designBriefError) formErrors.designBrief = designBriefError;

    setErrors(formErrors);

    // Check if form is valid
    if (!isFormValid() || Object.keys(formErrors).length > 0) {
      setErrors(prev => ({
        ...prev,
        general: 'Please fix the errors above before submitting.'
      }));
      return;
    }

    setSubmissionStatus('submitting');
    
    try {
      await onSubmit();
      setSubmissionStatus('success');
      
      // Close modal after success
      setTimeout(() => {
        onClose();
        setSubmissionStatus('idle');
      }, 2000);
      
    } catch (error) {
      setSubmissionStatus('error');
      setErrors({
        general: 'Failed to submit order. Please try again.'
      });
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50 overflow-y-auto">
      <div className="bg-white rounded-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto shadow-2xl">
        {/* Header */}
        <div className="sticky top-0 bg-white border-b border-gray-200 px-6 py-4 rounded-t-xl">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-xl font-semibold text-gray-900">
                Complete Your Order
              </h3>
              <p className="text-sm text-gray-500 mt-1">
                {product?.service} • {orderType === 'design' ? 'Design Service' : `${quantity.toLocaleString()} units`}
              </p>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors p-2 rounded-lg hover:bg-gray-100"
              disabled={isSubmitting}
            >
              <XMarkIcon className="h-5 w-5" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {submissionStatus === 'submitting' ? (
            <OrderStatusLoading status="submitting" message="Submitting your order..." />
          ) : submissionStatus === 'success' ? (
            <OrderStatusLoading status="success" message="Order submitted successfully! Redirecting to WhatsApp..." />
          ) : submissionStatus === 'error' ? (
            <OrderStatusLoading status="error" message="Failed to submit order. Please try again." />
          ) : (
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* General Error */}
              {errors.general && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <div className="flex items-start">
                    <ExclamationTriangleIcon className="h-5 w-5 text-red-500 mt-0.5 mr-3 flex-shrink-0" />
                    <div>
                      <h4 className="text-sm font-medium text-red-800">Error</h4>
                      <p className="text-sm text-red-700 mt-1">{errors.general}</p>
                    </div>
                  </div>
                </div>
              )}

              {/* Customer Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Customer Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    label="Full Name"
                    name="name"
                    type="text"
                    value={customerInfo.name}
                    onChange={(value) => handleFieldChange('name', value as string)}
                    onBlur={() => handleFieldBlur('name')}
                    error={errors.name}
                    required
                    placeholder="Enter your full name"
                    success={touched.name && !errors.name && customerInfo.name ? "Looks good!" : undefined}
                  />

                  <FormField
                    label="Phone Number"
                    name="phone"
                    type="tel"
                    value={customerInfo.phone}
                    onChange={(value) => handleFieldChange('phone', value as string)}
                    onBlur={() => handleFieldBlur('phone')}
                    error={errors.phone}
                    required
                    placeholder="0712345678"
                    description="We'll use this to contact you about your order"
                    success={touched.phone && !errors.phone && customerInfo.phone ? "Valid phone number" : undefined}
                  />

                  <FormField
                    label="Email Address"
                    name="email"
                    type="email"
                    value={customerInfo.email}
                    onChange={(value) => handleFieldChange('email', value as string)}
                    onBlur={() => handleFieldBlur('email')}
                    error={errors.email}
                    required
                    placeholder="<EMAIL>"
                    description="We'll send order updates to this email"
                    success={touched.email && !errors.email && customerInfo.email ? "Valid email address" : undefined}
                  />

                  <FormField
                    label="Special Instructions"
                    name="notes"
                    type="textarea"
                    value={customerInfo.notes}
                    onChange={(value) => handleFieldChange('notes', value as string)}
                    placeholder="Any special requirements or notes..."
                    description="Optional: Let us know if you have any specific requirements"
                    rows={3}
                  />
                </CardContent>
              </Card>

              {/* Design Brief or Artwork Upload */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">
                    {orderType === 'design' ? 'Design Requirements' : 'Design Brief or Artwork'}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    label={orderType === 'design' ? 'Design Brief' : 'Design Brief (Optional)'}
                    name="designBrief"
                    type="textarea"
                    value={designBrief}
                    onChange={handleDesignBriefChange}
                    onBlur={() => handleFieldBlur('designBrief')}
                    error={errors.designBrief}
                    required={orderType === 'design'}
                    placeholder="Describe what you want designed... (colors, style, text, dimensions, etc.)"
                    description={orderType === 'design' 
                      ? "Please provide detailed requirements for your design" 
                      : "Describe your design needs, or upload artwork files below"
                    }
                    rows={4}
                    success={touched.designBrief && !errors.designBrief && designBrief.length >= 10 ? "Great description!" : undefined}
                  />

                  {orderType === 'print' && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Upload Artwork Files {designBrief.trim().length < 10 && <span className="text-red-500">*</span>}
                      </label>
                      <ArtworkUpload
                        onFilesUploaded={(files) => {
                          setUploadedArtwork(files.map(f => ({
                            name: f.originalName,
                            fileName: f.fileName,
                            filePath: f.filePath,
                            uploaded: true,
                            size: f.fileSize,
                            type: f.fileType
                          })));
                        }}
                        maxFiles={5}
                        disabled={false}
                      />
                      {designBrief.trim().length < 10 && uploadedArtwork.length === 0 && (
                        <p className="text-sm text-gray-500 mt-2">
                          Either provide a design brief above or upload your artwork files
                        </p>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Order Summary */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Order Summary</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Product:</span>
                      <span className="font-medium">{product?.service}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Service Type:</span>
                      <span className="font-medium">
                        {orderType === 'design' ? 'Design Only' : 'Print Service'}
                      </span>
                    </div>
                    {orderType === 'print' && (
                      <div className="flex justify-between">
                        <span className="text-gray-600">Quantity:</span>
                        <span className="font-medium">{quantity.toLocaleString()} units</span>
                      </div>
                    )}
                    <div className="border-t pt-3">
                      <div className="flex justify-between text-lg font-semibold">
                        <span>Estimated Total:</span>
                        <span className="text-orange-600">
                          {orderType === 'design' ? 'Quote on Request' : `KSh ${((product?.price || 0) * quantity).toLocaleString()}`}
                        </span>
                      </div>
                      <p className="text-xs text-gray-500 mt-1">
                        Final pricing will be confirmed before payment
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Action Buttons */}
              <div className="flex gap-3 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={onClose}
                  className="flex-1"
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  className="flex-1"
                  disabled={isSubmitting || !isFormValid()}
                >
                  {isSubmitting ? (
                    <>
                      <Loading variant="spinner" size="sm" className="mr-2" />
                      Submitting...
                    </>
                  ) : (
                    <>
                      <CheckIcon className="h-4 w-4 mr-2" />
                      Submit Order
                    </>
                  )}
                </Button>
              </div>
            </form>
          )}
        </div>
      </div>
    </div>
  );
} 