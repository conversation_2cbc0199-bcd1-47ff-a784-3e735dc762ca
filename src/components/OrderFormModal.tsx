import React from 'react';
import { XMarkIcon, CheckIcon, ClipboardDocumentIcon, PhotoIcon, DocumentIcon } from '@heroicons/react/24/outline';
import ArtworkUpload from '@/components/ArtworkUpload';
import { getOrderFormData, getMissingRequiredFields, saveOrderFormData } from '@/utils/orderFormUtils';

interface OrderFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: () => void;
  isSubmitting: boolean;
  product: any;
  customerInfo: {
    name: string;
    phone: string;
    email: string;
    notes: string;
  };
  setCustomerInfo: (info: any) => void;
  needsDesign: boolean;
  setNeedsDesign: (value: boolean) => void;
  designOnly?: boolean;
  designBrief: string;
  setDesignBrief: (value: string) => void;
  referenceFiles?: File[];
  selectedDesignService: any;
  artworkFiles: any[];
  setArtworkFiles: (files: any[]) => void;
  setUploadedArtwork: (files: any[]) => void;
  uploadedArtwork: any[];
  selectedPageType: string;
  selectedPrintingOption: string;
  quantity: number;
}

const OrderFormModal: React.FC<OrderFormModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  isSubmitting,
  product,
  customerInfo,
  setCustomerInfo,
  needsDesign,
  setNeedsDesign,
  designOnly = false,
  designBrief,
  setDesignBrief,
  referenceFiles = [],
  selectedDesignService,
  artworkFiles,
  setArtworkFiles,
  setUploadedArtwork,
  uploadedArtwork,
  selectedPageType,
  selectedPrintingOption,
  quantity
}) => {
  if (!isOpen) return null;

  // Calculate missing fields directly from current props instead of localStorage
  const currentOrderData = {
    needsDesign,
    designOnly,
    designBrief,
    referenceFiles: referenceFiles || [],
    selectedDesignService,
    artworkFiles: artworkFiles || [],
    uploadedArtwork: uploadedArtwork || [],
    customerInfo
  };
  
  const missingFields = getMissingRequiredFields(currentOrderData);
  const isFormComplete = missingFields.length === 0;

  // Check if any files have been uploaded
  const hasArtworkFiles = (artworkFiles && artworkFiles.length > 0);
  const hasReferenceFiles = (referenceFiles && referenceFiles.length > 0);

  // Helper function to render file count badge
  const renderFileCount = (count: number) => {
    if (count === 0) return null;
    return (
      <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
        {count} file{count !== 1 ? 's' : ''}
      </span>
    );
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-lg max-w-lg w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">
              {isFormComplete ? 'Confirm Your Order' : 'Complete Your Order'}
            </h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <XMarkIcon className="h-5 w-5" />
            </button>
          </div>

          {/* Status Messages */}
          {isFormComplete && (
            <div className="mb-6 p-4 bg-green-50 rounded-lg border border-green-200">
              <div className="flex items-center gap-2 mb-2">
                <CheckIcon className="h-5 w-5 text-green-600" />
                <span className="text-sm font-medium text-green-800">Order Ready!</span>
              </div>
              <p className="text-sm text-green-700">
                All required information has been provided. Click "Submit Order" to proceed with WhatsApp.
              </p>
            </div>
          )}

          {!isFormComplete && (
            <div className="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
              <div className="flex items-center gap-2 mb-2">
                <ClipboardDocumentIcon className="h-5 w-5 text-blue-600" />
                <span className="text-sm font-medium text-blue-800">Complete These Fields</span>
              </div>
              <p className="text-sm text-blue-700 mb-2">
                Please provide the following information to complete your order:
              </p>
              <ul className="text-xs text-blue-600 space-y-1">
                {missingFields.map((field: string, index: number) => (
                  <li key={index} className="flex items-center gap-1">
                    <span className="w-1 h-1 bg-blue-400 rounded-full"></span>
                    {field}
                  </li>
                ))}
              </ul>
            </div>
          )}

          <form onSubmit={(e) => { e.preventDefault(); onSubmit(); }} className="space-y-6">
            {/* Customer Information Section */}
            <div className="space-y-4">
              <h4 className="font-medium text-gray-900">Customer Information</h4>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Full Name *</label>
                <input
                  type="text"
                  required
                  value={customerInfo.name}
                  onChange={(e) => setCustomerInfo({...customerInfo, name: e.target.value})}
                  className="w-full border border-gray-300 rounded px-3 py-2 text-sm focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter your full name"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Phone Number *</label>
                <input
                  type="tel"
                  required
                  value={customerInfo.phone}
                  onChange={(e) => setCustomerInfo({...customerInfo, phone: e.target.value})}
                  className="w-full border border-gray-300 rounded px-3 py-2 text-sm focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="07XXXXXXXX"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Email Address *</label>
                <input
                  type="email"
                  required
                  value={customerInfo.email}
                  onChange={(e) => setCustomerInfo({...customerInfo, email: e.target.value})}
                  className="w-full border border-gray-300 rounded px-3 py-2 text-sm focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="<EMAIL>"
                />
              </div>
            </div>

            {/* Order Type Selection */}
            {!designOnly && (
              <div className="space-y-4">
                <h4 className="font-medium text-gray-900">Service Type</h4>
                <div className="grid grid-cols-2 gap-4">
                  <label className="relative flex flex-col items-center bg-white border rounded-lg p-4 cursor-pointer hover:border-blue-500 transition-colors">
                    <input
                      type="radio"
                      name="designOption"
                      checked={!needsDesign}
                      onChange={() => setNeedsDesign(false)}
                      className="sr-only"
                    />
                    <DocumentIcon className="h-8 w-8 text-gray-400 mb-2" />
                    <span className="text-sm font-medium text-gray-900">Print Only</span>
                    <span className="text-xs text-gray-500">Upload ready artwork</span>
                    <div className={`absolute inset-0 border-2 rounded-lg ${!needsDesign ? 'border-blue-500' : 'border-transparent'}`} />
                  </label>
                  <label className="relative flex flex-col items-center bg-white border rounded-lg p-4 cursor-pointer hover:border-blue-500 transition-colors">
                    <input
                      type="radio"
                      name="designOption"
                      checked={needsDesign}
                      onChange={() => setNeedsDesign(true)}
                      className="sr-only"
                    />
                    <PhotoIcon className="h-8 w-8 text-gray-400 mb-2" />
                    <span className="text-sm font-medium text-gray-900">Design + Print</span>
                    <span className="text-xs text-gray-500">We design for you</span>
                    <div className={`absolute inset-0 border-2 rounded-lg ${needsDesign ? 'border-blue-500' : 'border-transparent'}`} />
                  </label>
                </div>
              </div>
            )}

            {/* Design Service Section */}
            {needsDesign && (
              <div className="space-y-4">
                <h4 className="font-medium text-gray-900">Design Requirements</h4>
                {selectedDesignService && (
                  <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                    <div className="flex justify-between items-center">
                      <div>
                        <p className="font-medium text-blue-900">{selectedDesignService.name}</p>
                        <p className="text-sm text-blue-700">{selectedDesignService.description}</p>
                      </div>
                      <span className="font-bold text-blue-900">
                        KSh {selectedDesignService.price.toLocaleString()}
                      </span>
                    </div>
                  </div>
                )}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Design Brief *</label>
                  <textarea
                    required={needsDesign}
                    value={designBrief}
                    onChange={(e) => setDesignBrief(e.target.value)}
                    className="w-full border border-gray-300 rounded px-3 py-2 text-sm focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Describe what you want designed... Include colors, style preferences, and any specific requirements."
                    rows={4}
                  />
                </div>

                {/* Reference Files Upload */}
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <label className="block text-sm font-medium text-gray-700">Reference Files</label>
                    {renderFileCount(referenceFiles.length)}
                  </div>
                  <ArtworkUpload
                    onFilesUploaded={(files) => {
                      setArtworkFiles([...artworkFiles, ...files]);
                      setUploadedArtwork([...uploadedArtwork, ...files.map(f => ({ name: f.originalName } as File))]);
                    }}
                    maxFiles={5}
                    disabled={false}
                  />
                  <p className="mt-2 text-xs text-gray-500">
                    Upload logos, brand assets, or inspiration images that will help us understand your requirements better.
                  </p>
                </div>
              </div>
            )}

            {/* Artwork Upload Section - for print-only or both */}
            {!designOnly && (
              <div className="space-y-4">
                <h4 className="font-medium text-gray-900">
                  {needsDesign ? 'Additional Artwork' : 'Print-Ready Artwork'}
                </h4>
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <label className="block text-sm font-medium text-gray-700">
                      {needsDesign ? 'Upload Additional Files' : 'Upload Artwork Files *'}
                    </label>
                    {renderFileCount(artworkFiles.length)}
                  </div>
                  <ArtworkUpload
                    onFilesUploaded={(files) => {
                      setArtworkFiles([...artworkFiles, ...files]);
                      setUploadedArtwork([...uploadedArtwork, ...files.map(f => ({ name: f.originalName } as File))]);
                    }}
                    maxFiles={5}
                    disabled={false}
                  />
                  <p className="mt-2 text-xs text-gray-500">
                    {needsDesign 
                      ? 'Upload any additional artwork or files that should be incorporated into the design.'
                      : 'Upload your print-ready artwork files. Accepted formats: AI, PSD, PDF, JPG, PNG.'}
                  </p>
                </div>
              </div>
            )}

            {/* Special Instructions */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Special Instructions</label>
              <textarea
                value={customerInfo.notes}
                onChange={(e) => setCustomerInfo({...customerInfo, notes: e.target.value})}
                className="w-full border border-gray-300 rounded px-3 py-2 text-sm focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Any special requirements or notes..."
                rows={3}
              />
            </div>

            {/* Submit Button */}
            <button
              type="submit"
              disabled={!isFormComplete || isSubmitting}
              className={`w-full py-3 px-4 rounded-lg text-white font-medium ${
                isFormComplete && !isSubmitting
                  ? 'bg-blue-600 hover:bg-blue-700'
                  : 'bg-gray-400 cursor-not-allowed'
              }`}
            >
              {isSubmitting ? 'Processing...' : 'Submit Order'}
            </button>
          </form>
        </div>
      </div>
    </div>
  );
};

export default OrderFormModal; 