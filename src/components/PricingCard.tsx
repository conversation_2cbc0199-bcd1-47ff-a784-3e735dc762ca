'use client';
import React from 'react';

import { useState } from 'react';
import Link from 'next/link';

interface PricingCardProps {
  id: string;
  service: string;
  price: number;
  description?: string;
  features?: string[];
  icon?: string;
  popular?: boolean;
}

export default function PricingCard({
  id,
  service,
  price,
  description,
  features = [],
  icon,
  popular = false,
}: PricingCardProps) {
  // Format price to include commas for thousands
  const formattedPrice = price.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');

  // Create WhatsApp message for inquiry
  const productUrl = `https://mocky.co.ke/product/${id}`;
  const whatsappMessage = `Hello! I'm interested in the ${service} service (KSh ${formattedPrice}). Can you provide more information?\n\nProduct Link: ${productUrl}`;
  const whatsappLink = `https://wa.me/254741590670?text=${encodeURIComponent(whatsappMessage)}`;

  return (
    <div
      className={`relative bg-white rounded-xl overflow-hidden shadow-md hover:shadow-lg transition-all duration-300 border ${
        popular ? 'border-[#FF5400]' : 'border-gray-200'
      }`}
    >
      {popular && (
        <div className="bg-[#FF5400] text-white text-center py-2 px-4 text-sm font-medium">
          Most Popular
        </div>
      )}

      <div className="p-6">
        <div className="flex items-center justify-between mb-4">
          {icon && (
            <div className="text-3xl mb-2">
              {icon}
            </div>
          )}
        </div>

        <h3 className="text-xl font-bold text-gray-900 mb-2">{service}</h3>

        <div className="mb-4">
          <div className="flex items-baseline">
            <span className="text-sm font-medium text-gray-500 mr-1">KSh</span>
            <span className="text-3xl font-bold text-[#FF5400]">{formattedPrice}</span>
          </div>
        </div>

        {description && (
          <p className="text-gray-600 mb-4 text-sm">{description}</p>
        )}

        {features && features.length > 0 ? (
          <ul className="space-y-2 mb-6 min-h-[180px]">
            {features.map((feature, idx) => (
              <li key={idx} className="flex items-start gap-2">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5 text-green-500 flex-shrink-0"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                    clipRule="evenodd"
                  />
                </svg>
                <span className="text-gray-600 text-sm">{feature}</span>
              </li>
            ))}
          </ul>
        ) : (
          <div className="min-h-[180px] flex items-center justify-center">
            <p className="text-gray-400 text-sm italic">Contact us for more details</p>
          </div>
        )}

        <div className="mt-auto">
          <a
            href={whatsappLink}
            target="_blank"
            rel="noopener noreferrer"
            className="block w-full py-2 px-4 bg-[#FF5400] hover:bg-[#E64A00] text-white text-center rounded-md transition-colors duration-300"
          >
            Get Started
          </a>
        </div>
      </div>
    </div>
  );
}
