import nodemailer from 'nodemailer';

interface EmailAttachment {
  filename: string;
  path: string;
  contentType?: string;
}

interface SendEmailOptions {
  to: string;
  subject: string;
  html: string;
  text?: string;
  attachments?: EmailAttachment[];
  from?: string;
}

// Configure your email service (now using Gmail with explicit SMTP settings)
const createTransporter = () => {
  // Use explicit Gmail SMTP configuration for better reliability
  return nodemailer.createTransport({
    host: 'smtp.gmail.com',
    port: 587,
    secure: false, // Use STARTTLS
    auth: {
      user: process.env.GMAIL_USER || '<EMAIL>', // Your Gmail address
      pass: process.env.GMAIL_PASS || 'sncr fuit apix bitk', // Your Gmail app password
    },
    tls: {
      rejectUnauthorized: false, // Accept self-signed certificates
    },
    connectionTimeout: 60000, // 60 seconds timeout
    greetingTimeout: 30000, // 30 seconds greeting timeout
    socketTimeout: 60000, // 60 seconds socket timeout
    debug: process.env.NODE_ENV === 'development', // Enable debug in dev
    logger: process.env.NODE_ENV === 'development', // Enable logging in dev
  });
};

// Alternative: Using SendGrid
const createSendGridTransporter = () => {
  return nodemailer.createTransport({
    service: 'SendGrid',
    auth: {
      user: 'apikey',
      pass: process.env.SENDGRID_API_KEY,
    },
  });
};

export async function sendEmailWithAttachments(options: SendEmailOptions) {
  try {
    const transporter = createTransporter();
    
    const mailOptions = {
      from: options.from || `"Mocky Digital" <${process.env.GMAIL_USER || '<EMAIL>'}>`,
      to: options.to,
      subject: options.subject,
      html: options.html,
      text: options.text,
      attachments: options.attachments?.map(att => ({
        filename: att.filename,
        path: att.path,
        contentType: att.contentType,
      })),
    };

    const result = await transporter.sendMail(mailOptions);
    
    return {
      success: true,
      messageId: result.messageId,
      response: result.response,
    };
  } catch (error) {
    console.error('Email sending error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

// Helper function to download S3 files temporarily for email attachment
export async function downloadS3FileForEmail(s3Url: string, fileName: string) {
  try {
    const response = await fetch(s3Url);
    if (!response.ok) throw new Error('Failed to download file');
    
    const buffer = await response.arrayBuffer();
    const tempPath = `/tmp/${fileName}`;
    
    // In a real implementation, you'd save this to a temporary file
    // For now, we'll return the buffer directly
    return {
      success: true,
      buffer: Buffer.from(buffer),
      tempPath,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Download failed',
    };
  }
} 