/**
 * Comprehensive Receipt Service
 * Merges transaction and receipt functionality into a single robust system
 * This eliminates the need for separate transaction management and creates receipts directly from M-Pesa messages
 */
import prisma from '@/lib/prisma';
import { generateReceiptPDF, ReceiptData, ReceiptItemData } from '@/utils/pdfGenerator';
import { parseTransactionMessage } from '@/utils/transactionParser';
import { getServiceById } from './serviceItemService';
import {
  validateReceiptData,
  ValidatedReceiptData,
  TeamMemberValidationError
} from '@/utils/validation';
import path from 'path';
import { promises as fs } from 'fs';

export interface ComprehensiveReceipt {
  id: string;
  receiptNumber: string;
  // Transaction data (merged from M-Pesa)
  mpesaTransactionId: string;
  transactionDate: Date;
  rawMessage: string;
  // Customer information
  customerName: string;
  phoneNumber: string;
  email: string | null;
  // Financial data
  totalAmount: number;
  amountPaid: number;
  balance: number;
  // Receipt metadata
  status: string;
  notes: string | null;
  createdAt: Date;
  updatedAt: Date;
  issuedAt: Date;
  paidAt: Date | null;
  // Items and PDF
  items: ComprehensiveReceiptItem[];
  pdfUrl?: string;
}

export interface ComprehensiveReceiptItem {
  id: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  description: string | null;
  serviceId: string | null;
  serviceName?: string;
  discount: number;
  discountType: 'fixed' | 'percentage';
  isCustomService: boolean;
}

/**
 * Generate a unique receipt number based on M-Pesa transaction ID
 * @param mpesaTransactionId The M-Pesa transaction ID
 * @returns A unique receipt number
 */
async function generateReceiptNumber(mpesaTransactionId: string): Promise<string> {
  console.log(`Generating receipt number for M-Pesa transaction: ${mpesaTransactionId}`);

  // Use M-Pesa transaction ID as base receipt number
  let receiptNumber = mpesaTransactionId;

  // Check if a receipt with this number already exists
  const existingReceipt = await prisma.receipt.findUnique({
    where: { receiptNumber }
  });

  if (existingReceipt) {
    console.log(`Receipt ${receiptNumber} already exists, generating alternative`);
    // Append timestamp to make it unique
    const timestamp = Date.now().toString().slice(-4);
    receiptNumber = `${mpesaTransactionId}-${timestamp}`;
  }

  return receiptNumber;
}

/**
 * Create a comprehensive receipt directly from M-Pesa message
 * This is the main function that replaces the transaction → receipt flow
 */
export async function createComprehensiveReceipt(data: {
  mpesaMessage: string;
  customerName?: string;
  phoneNumber?: string;
  email?: string;
  notes?: string;
  items: {
    serviceId: string | null;
    quantity: number;
    unitPrice?: number;
    description?: string;
    discount?: number;
    discountType?: 'fixed' | 'percentage';
    isCustomService?: boolean;
  }[];
}): Promise<ComprehensiveReceipt | null> {
  const startTime = Date.now();

  try {
    console.log(`[Comprehensive Receipt] Starting receipt creation from M-Pesa message`);
    console.log(`[Comprehensive Receipt] Input data:`, JSON.stringify(data, null, 2));

    // Step 1: Parse M-Pesa message
    console.log(`[Comprehensive Receipt] Parsing M-Pesa message: ${data.mpesaMessage.substring(0, 100)}...`);
    const parsedTransaction = parseTransactionMessage(data.mpesaMessage);
    if (!parsedTransaction) {
      console.error(`[Comprehensive Receipt] Failed to parse M-Pesa message`);
      throw new Error('Failed to parse M-Pesa message. Please check the format.');
    }

    console.log(`[Comprehensive Receipt] Parsed transaction: ${parsedTransaction.transactionId} - ${parsedTransaction.customerName} - KES ${parsedTransaction.amount}`);

    // Step 2: Check for existing receipt (prevent duplicates)
    const existingReceipt = await prisma.receipt.findUnique({
      where: { receiptNumber: parsedTransaction.transactionId },
      include: { items: true }
    });

    if (existingReceipt) {
      console.log(`[Comprehensive Receipt] Receipt already exists: ${existingReceipt.receiptNumber}`);
      return formatComprehensiveReceipt(existingReceipt, parsedTransaction);
    }

    // Step 3: Generate receipt number
    const receiptNumber = await generateReceiptNumber(parsedTransaction.transactionId);

    // Step 4: Process receipt items
    const receiptItems: {
      serviceId: string | null;
      quantity: number;
      unitPrice: number;
      totalPrice: number;
      description: string;
      discount: number;
      discountType: 'fixed' | 'percentage';
      isCustomService: boolean;
    }[] = [];
    let totalAmount = 0;

    for (let i = 0; i < data.items.length; i++) {
      const item = data.items[i];
      const isCustomService = item.isCustomService || false;
      const discount = item.discount || 0;
      const discountType = item.discountType || 'fixed';

      let unitPrice: number;
      let description: string;
      let serviceId: string | null = item.serviceId;

      if (isCustomService) {
        // Handle custom service
        if (!item.description?.trim()) {
          throw new Error(`Custom service description is required for item ${i + 1}`);
        }
        if (!item.unitPrice || item.unitPrice <= 0) {
          throw new Error(`Custom service unit price is required for item ${i + 1}`);
        }
        
        unitPrice = item.unitPrice;
        description = item.description.trim();
        serviceId = null; // Custom services don't have a serviceId
      } else {
        // Handle regular service
        if (!item.serviceId) {
          throw new Error(`Service ID is required for item ${i + 1}`);
        }

        console.log(`[Comprehensive Receipt] Looking up service: ${item.serviceId}`);
        const service = await getServiceById(item.serviceId);
        if (!service) {
          console.error(`[Comprehensive Receipt] Service not found: ${item.serviceId}`);
          // Let's also check what services are available
          const allServices = await prisma.service.findMany({
            select: { id: true, name: true }
          });
          console.log(`[Comprehensive Receipt] Available services:`, allServices);
          throw new Error(`Service ${item.serviceId} not found`);
        }

        console.log(`[Comprehensive Receipt] Service found: ${service.name} - KES ${service.price}`);
        // Use provided unit price or service price
        unitPrice = item.unitPrice && item.unitPrice > 0 ? item.unitPrice : Number(service.price);
        description = item.description || service.name;
      }

      if (unitPrice <= 0) {
        throw new Error(`Invalid unit price for item ${i + 1}`);
      }

      // Calculate subtotal
      const subtotal = Math.round((unitPrice * item.quantity) * 100) / 100;
      
      // Calculate discount
      let discountAmount = 0;
      if (discount > 0) {
        if (discountType === 'percentage') {
          discountAmount = Math.round((subtotal * discount / 100) * 100) / 100;
        } else {
          discountAmount = Math.round(discount * 100) / 100;
        }
      }

      // Calculate final total price after discount
      const totalPrice = Math.max(0, Math.round((subtotal - discountAmount) * 100) / 100);

      receiptItems.push({
        serviceId,
        quantity: item.quantity,
        unitPrice,
        totalPrice,
        description,
        discount,
        discountType,
        isCustomService
      });

      totalAmount += totalPrice;
      console.log(`[Comprehensive Receipt] Item ${i + 1}: ${description} x${item.quantity} @ KES ${unitPrice} ${discount > 0 ? `(discount: ${discountType === 'percentage' ? discount + '%' : 'KES ' + discount})` : ''} = KES ${totalPrice}`);
    }

    totalAmount = Math.round(totalAmount * 100) / 100;

    // Step 5: Calculate financial data
    const amountPaid = Number(parsedTransaction.amount);
    const balance = Math.round((totalAmount - amountPaid) * 100) / 100;
    const status = balance <= 0 ? 'paid' : 'issued';

    console.log(`[Comprehensive Receipt] Total: KES ${totalAmount}, Paid: KES ${amountPaid}, Balance: KES ${balance}, Status: ${status}`);

    // Step 6: Create receipt and transaction in a single database transaction
    console.log(`[Comprehensive Receipt] Creating receipt with number: ${receiptNumber}`);
    console.log(`[Comprehensive Receipt] Receipt items to create:`, receiptItems.length);
    
    const receipt = await prisma.$transaction(async (tx) => {
      console.log(`[Comprehensive Receipt] Starting database transaction`);
      
      // Create the comprehensive receipt (this replaces both transaction and receipt)
      const receiptData = {
        receiptNumber,
        totalAmount,
        amountPaid,
        balance,
        customerName: data.customerName || parsedTransaction.customerName,
        phoneNumber: data.phoneNumber || parsedTransaction.phoneNumber,
        email: data.email,
        status,
        notes: data.notes,
        issuedAt: new Date(),
        paidAt: status === 'paid' ? parsedTransaction.transactionDate : null, // Use actual M-Pesa transaction date
        // Store the transaction data directly in the receipt
        transactionId: parsedTransaction.transactionId, // Use M-Pesa ID as transaction reference
        items: {
          create: receiptItems.map(item => ({
            serviceId: item.serviceId,
            quantity: item.quantity,
            unitPrice: item.unitPrice,
            totalPrice: item.totalPrice,
            description: item.description,
            discount: item.discount,
            discountType: item.discountType,
            isCustomService: item.isCustomService
          }))
        }
      };
      
      console.log(`[Comprehensive Receipt] Receipt data to create:`, JSON.stringify(receiptData, null, 2));
      
      try {
        const newReceipt = await tx.receipt.create({
          data: receiptData,
          include: {
            items: {
              include: {
                service: true
              }
            }
          }
        });
        
        console.log(`[Comprehensive Receipt] Receipt created successfully in database`);
        return newReceipt;
      } catch (dbError) {
        console.error(`[Comprehensive Receipt] Database error creating receipt:`, dbError);
        throw dbError;
      }
    });

    console.log(`[Comprehensive Receipt] Receipt created successfully: ${receipt.receiptNumber}`);

    // Step 7: Generate PDF
    const formattedReceipt = formatComprehensiveReceipt(receipt, parsedTransaction);
    
    try {
      const receiptData = mapToReceiptData(formattedReceipt);
      const pdfUrl = await generateReceiptPDF(receiptData);
      
      // Update receipt with PDF URL
      await prisma.receipt.update({
        where: { id: receipt.id },
        data: { pdfUrl }
      });

      formattedReceipt.pdfUrl = pdfUrl;
      console.log(`[Comprehensive Receipt] PDF generated: ${pdfUrl}`);
    } catch (pdfError) {
      console.error('[Comprehensive Receipt] PDF generation failed:', pdfError);
      // Don't fail the entire process if PDF generation fails
    }

    const processingTime = Date.now() - startTime;
    console.log(`[Comprehensive Receipt] Process completed in ${processingTime}ms`);

    return formattedReceipt;
  } catch (error) {
    console.error('[Comprehensive Receipt] Error creating receipt:', error);
    throw error;
  }
}

/**
 * Get all comprehensive receipts
 */
export async function getAllComprehensiveReceipts(): Promise<ComprehensiveReceipt[]> {
  try {
    const receipts = await prisma.receipt.findMany({
      include: {
        items: {
          include: {
            service: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    return receipts.map(receipt => {
      // Get the associated transaction data
      const transactionData = {
        transactionId: receipt.receiptNumber, // Use receipt number as transaction ID
        transactionDate: receipt.issuedAt,
        rawMessage: '', // This would need to be stored separately if needed
        customerName: receipt.customerName,
        phoneNumber: receipt.phoneNumber,
        amount: receipt.amountPaid
      };

      return formatComprehensiveReceipt(receipt, transactionData);
    });
  } catch (error) {
    console.error('Error getting comprehensive receipts:', error);
    throw error;
  }
}

/**
 * Get comprehensive receipt by ID
 */
export async function getComprehensiveReceiptById(id: string): Promise<ComprehensiveReceipt | null> {
  try {
    const receipt = await prisma.receipt.findUnique({
      where: { id },
      include: {
        items: {
          include: {
            service: true
          }
        }
      }
    });

    if (!receipt) {
      return null;
    }

    // Get the associated transaction data
    const transactionData = {
      transactionId: receipt.receiptNumber,
      transactionDate: receipt.issuedAt,
      rawMessage: '',
      customerName: receipt.customerName,
      phoneNumber: receipt.phoneNumber,
      amount: receipt.amountPaid
    };

    return formatComprehensiveReceipt(receipt, transactionData);
  } catch (error) {
    console.error(`Error getting comprehensive receipt ${id}:`, error);
    throw error;
  }
}

/**
 * Check if a comprehensive receipt exists for M-Pesa transaction ID
 */
export async function checkComprehensiveReceiptExists(mpesaTransactionId: string): Promise<{ exists: boolean; receiptId?: string; receiptNumber?: string }> {
  try {
    const receipt = await prisma.receipt.findUnique({
      where: { receiptNumber: mpesaTransactionId },
      select: { id: true, receiptNumber: true }
    });

    if (receipt) {
      return {
        exists: true,
        receiptId: receipt.id,
        receiptNumber: receipt.receiptNumber
      };
    }

    return { exists: false };
  } catch (error) {
    console.error(`Error checking comprehensive receipt for ${mpesaTransactionId}:`, error);
    return { exists: false };
  }
}

/**
 * Format receipt data for API responses
 */
function formatComprehensiveReceipt(receipt: any, transactionData: any): ComprehensiveReceipt {
  return {
    id: receipt.id,
    receiptNumber: receipt.receiptNumber,
    mpesaTransactionId: transactionData.transactionId,
    transactionDate: new Date(transactionData.transactionDate),
    rawMessage: transactionData.rawMessage || '',
    customerName: receipt.customerName,
    phoneNumber: receipt.phoneNumber,
    email: receipt.email,
    totalAmount: Number(receipt.totalAmount),
    amountPaid: Number(receipt.amountPaid),
    balance: Number(receipt.balance),
    status: receipt.status,
    notes: receipt.notes,
    createdAt: new Date(receipt.createdAt),
    updatedAt: new Date(receipt.updatedAt),
    issuedAt: new Date(receipt.issuedAt),
    paidAt: receipt.paidAt ? new Date(receipt.paidAt) : null,
    pdfUrl: receipt.pdfUrl || undefined,
    items: receipt.items.map((item: any) => ({
      id: item.id,
      quantity: item.quantity,
      unitPrice: Number(item.unitPrice),
      totalPrice: Number(item.totalPrice),
      description: item.description,
      serviceId: item.serviceId,
      serviceName: item.service?.name || (item.isCustomService ? 'Custom Service' : 'Unknown Service'),
      discount: Number(item.discount || 0),
      discountType: item.discountType || 'fixed',
      isCustomService: item.isCustomService || false
    }))
  };
}

/**
 * Map comprehensive receipt to PDF data
 */
export function mapToReceiptData(receipt: ComprehensiveReceipt): ReceiptData {
  return {
    receiptNumber: receipt.receiptNumber,
    customerName: receipt.customerName,
    phoneNumber: receipt.phoneNumber,
    email: receipt.email || undefined,
    transactionId: receipt.mpesaTransactionId,
    transactionDate: receipt.transactionDate,
    items: receipt.items.map((item): ReceiptItemData => ({
      description: item.description || 'Service',
      quantity: item.quantity,
      unitPrice: item.unitPrice,
      totalPrice: item.totalPrice
    })),
    totalAmount: receipt.totalAmount,
    amountPaid: receipt.amountPaid,
    balance: receipt.balance,
    notes: receipt.notes || undefined,
    issuedAt: receipt.issuedAt
  };
}

/**
 * Update comprehensive receipt status
 */
export async function updateComprehensiveReceiptStatus(receiptId: string, status: string): Promise<ComprehensiveReceipt | null> {
  try {
    const receipt = await prisma.receipt.update({
      where: { id: receiptId },
      data: { 
        status,
        paidAt: status === 'paid' ? new Date() : null
      },
      include: {
        items: {
          include: {
            service: true
          }
        }
      }
    });

    const transactionData = {
      transactionId: receipt.receiptNumber,
      transactionDate: receipt.issuedAt,
      rawMessage: '',
      customerName: receipt.customerName,
      phoneNumber: receipt.phoneNumber,
      amount: receipt.amountPaid
    };

    return formatComprehensiveReceipt(receipt, transactionData);
  } catch (error) {
    console.error(`Error updating comprehensive receipt status ${receiptId}:`, error);
    throw error;
  }
}

/**
 * Delete comprehensive receipt
 */
export async function deleteComprehensiveReceipt(receiptId: string): Promise<boolean> {
  try {
    console.log(`[Comprehensive Receipt] Deleting receipt: ${receiptId}`);

    const existingReceipt = await prisma.receipt.findUnique({
      where: { id: receiptId },
      include: { items: true }
    });

    if (!existingReceipt) {
      console.log(`[Comprehensive Receipt] Receipt not found: ${receiptId}`);
      return false;
    }

    await prisma.$transaction(async (tx) => {
      // Delete receipt items
      await tx.receiptItem.deleteMany({
        where: { receiptId }
      });

      // Delete the receipt
      await tx.receipt.delete({
        where: { id: receiptId }
      });

      // Note: No transaction record to delete since we removed the transactions table
    });

    // Clean up PDF file
    if (existingReceipt.pdfUrl) {
      try {
        const pdfPath = path.join(process.cwd(), 'public', existingReceipt.pdfUrl);
        await fs.unlink(pdfPath);
      } catch (fileError) {
        console.warn(`Could not delete PDF file: ${existingReceipt.pdfUrl}`);
      }
    }

    console.log(`[Comprehensive Receipt] Receipt deleted successfully: ${receiptId}`);
    return true;
  } catch (error) {
    console.error(`Error deleting comprehensive receipt ${receiptId}:`, error);
    throw error;
  }
}

/**
 * Add additional payment to an existing receipt
 * This allows updating receipts that were created with partial payment
 */
export async function addPaymentToReceipt(data: {
  receiptId: string;
  mpesaMessage: string;
  notes?: string;
  allowOverpayment?: boolean; // New parameter to allow tips and overpayments
}): Promise<{ receipt: ComprehensiveReceipt; newAmountPaid: number; previousAmountPaid: number } | null> {
  const startTime = Date.now();

  try {
    console.log(`[Add Payment] Starting payment addition for receipt: ${data.receiptId}`);
    console.log(`[Add Payment] Allow overpayment: ${data.allowOverpayment || false}`);

    // Step 1: Parse the new M-Pesa message or handle manual payment
    let parsedTransaction;
    
    // Check if this is a synthetic manual payment message
    if (data.mpesaMessage.startsWith('Manual payment of KES')) {
      // Extract amount from synthetic message
      const amountMatch = data.mpesaMessage.match(/KES\s+([0-9,]+(?:\.[0-9]{2})?)/);
      if (!amountMatch) {
        throw new Error('Failed to parse manual payment amount.');
      }
      
      const amount = parseFloat(amountMatch[1].replace(/,/g, ''));
      
      // Create a synthetic transaction object
      parsedTransaction = {
        transactionId: `MANUAL-${Date.now()}`,
        amount: amount,
        customerName: 'Manual Payment',
        phoneNumber: '0000000000',
        transactionDate: new Date(),
        rawMessage: data.mpesaMessage
      };
    } else {
      // Parse actual M-Pesa message
      parsedTransaction = parseTransactionMessage(data.mpesaMessage);
      if (!parsedTransaction) {
        throw new Error('Failed to parse M-Pesa message. Please check the format.');
      }
    }

    console.log(`[Add Payment] Parsed new transaction: ${parsedTransaction.transactionId} - ${parsedTransaction.customerName} - KES ${parsedTransaction.amount}`);

    // Step 2: Get the existing receipt
    const existingReceipt = await prisma.receipt.findUnique({
      where: { id: data.receiptId },
      include: {
        items: {
          include: {
            service: true
          }
        }
      }
    });

    if (!existingReceipt) {
      console.log(`[Add Payment] Receipt not found: ${data.receiptId}`);
      return null;
    }

    // Step 3: Validate that this is a partial payment scenario (unless allowing overpayment)
    if (!data.allowOverpayment && existingReceipt.status === 'paid') {
      throw new Error('Cannot add payment to a receipt that is already fully paid');
    }

    if (!data.allowOverpayment && Number(existingReceipt.balance) <= 0) {
      throw new Error('Receipt has no outstanding balance');
    }

    // Step 4: Calculate new payment amounts
    const previousAmountPaid = Number(existingReceipt.amountPaid);
    const additionalPayment = Number(parsedTransaction.amount);
    const newAmountPaid = Math.round((previousAmountPaid + additionalPayment) * 100) / 100;
    const totalAmount = Number(existingReceipt.totalAmount);
    const newBalance = Math.round((totalAmount - newAmountPaid) * 100) / 100;
    
    // Determine status based on balance
    let newStatus = 'issued';
    if (newBalance <= 0) {
      newStatus = 'paid';
      if (newBalance < 0) {
        newStatus = 'overpaid'; // New status for overpaid receipts
      }
    }

    console.log(`[Add Payment] Payment calculation:`);
    console.log(`  Previous paid: KES ${previousAmountPaid}`);
    console.log(`  Additional payment: KES ${additionalPayment}`);
    console.log(`  New total paid: KES ${newAmountPaid}`);
    console.log(`  New balance: KES ${newBalance}`);
    console.log(`  New status: ${newStatus}`);

    // Step 5: Handle overpayment validation
    if (newAmountPaid > totalAmount) {
      const overpaymentAmount = Math.round((newAmountPaid - totalAmount) * 100) / 100;
      
      if (data.allowOverpayment) {
        console.log(`[Add Payment] Allowing overpayment of KES ${overpaymentAmount} (tip/overpayment)`);
        // The negative balance will be handled properly
      } else {
        throw new Error(`Payment amount (KES ${newAmountPaid.toLocaleString()}) exceeds total amount (KES ${totalAmount.toLocaleString()}). Overpayment of KES ${overpaymentAmount.toLocaleString()} detected. Please verify the payment amount with the customer or enable 'Allow Overpayment' if this is intentional (e.g., a tip).`);
      }
    }

    // Step 6: Update receipt with new payment information
    const updatedReceipt = await prisma.$transaction(async (tx) => {
      // Create enhanced notes entry to track the additional payment with timestamp
      const paymentDate = parsedTransaction.transactionDate.toLocaleString('en-KE', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        hour12: true
      });
      
      let paymentNotes = `Additional payment: ${parsedTransaction.transactionId} - KES ${additionalPayment.toLocaleString()} (${paymentDate})`;
      
      if (newBalance < 0) {
        const creditAmount = Math.abs(newBalance);
        paymentNotes += ` (includes KES ${creditAmount.toLocaleString()} credit/tip)`;
      }
      
      if (data.notes) {
        paymentNotes = `${data.notes} | ${paymentNotes}`;
      }
      
      const existingNotes = existingReceipt.notes || '';
      const updatedNotes = existingNotes 
        ? `${existingNotes}\n\n${paymentNotes}`
        : paymentNotes;

      // Update the receipt
      return await tx.receipt.update({
        where: { id: data.receiptId },
        data: {
          amountPaid: newAmountPaid,
          balance: newBalance, // Can be negative for overpayments
          status: newStatus,
          notes: updatedNotes,
          paidAt: newStatus === 'paid' || newStatus === 'overpaid' ? parsedTransaction.transactionDate : existingReceipt.paidAt, // Use actual M-Pesa transaction date
          updatedAt: new Date()
        },
        include: {
          items: {
            include: {
              service: true
            }
          }
        }
      });
    });

    console.log(`[Add Payment] Payment added successfully to receipt: ${existingReceipt.receiptNumber}`);
    if (newBalance < 0) {
      console.log(`[Add Payment] Credit balance of KES ${Math.abs(newBalance)} created`);
    }
    console.log(`[Add Payment] Process completed in ${Date.now() - startTime}ms`);

    // Step 7: Format and return the updated receipt
    const transactionData = {
      transactionId: existingReceipt.receiptNumber,
      transactionDate: existingReceipt.issuedAt,
      rawMessage: data.mpesaMessage,
      customerName: existingReceipt.customerName,
      phoneNumber: existingReceipt.phoneNumber,
      amount: newAmountPaid
    };

    const formattedReceipt = formatComprehensiveReceipt(updatedReceipt, transactionData);

    return {
      receipt: formattedReceipt,
      newAmountPaid,
      previousAmountPaid
    };

  } catch (error) {
    console.error(`[Add Payment] Error adding payment to receipt ${data.receiptId}:`, error);
    throw new Error(`Failed to add payment: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
} 