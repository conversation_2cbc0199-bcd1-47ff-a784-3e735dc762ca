import prisma from '@/lib/prisma';

interface Script {
  id: string;
  name: string;
  content: string;
  scriptType: 'head' | 'body' | 'footer';
  position: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

interface DbScript {
  id: string;
  name: string;
  content: string;
  scriptType: string;
  position: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  description: string | null;
}

// Helper function to validate script type
function isValidScriptType(type: string): type is 'head' | 'body' | 'footer' {
  return ['head', 'body', 'footer'].includes(type);
}

// Helper function to convert database script to Script interface
function convertToScript(dbScript: DbScript): Script | null {
  if (!isValidScriptType(dbScript.scriptType)) {
    console.error('Invalid script type:', dbScript.scriptType);
    return null;
  }

  return {
    id: dbScript.id,
    name: dbScript.name,
    content: dbScript.content,
    scriptType: dbScript.scriptType,
    position: dbScript.position,
    isActive: dbScript.isActive,
    createdAt: dbScript.createdAt,
    updatedAt: dbScript.updatedAt,
  };
}

/**
 * Get all scripts
 * @returns Array of scripts
 */
export async function getAllScripts(): Promise<Script[]> {
  try {
    const dbScripts = await prisma.siteScript.findMany({
      orderBy: [
        { scriptType: 'asc' },
        { position: 'asc' },
        { name: 'asc' }
      ],
    }) as DbScript[];
    return dbScripts.map(convertToScript).filter((script): script is Script => script !== null);
  } catch (error) {
    console.error('Error getting scripts:', error);
    return [];
  }
}

/**
 * Get active scripts by type
 * @param scriptType The type of scripts to get (head, body, footer)
 * @returns Array of active scripts of the specified type
 */
export async function getActiveScriptsByType(scriptType: 'head' | 'body' | 'footer'): Promise<Script[]> {
  try {
    const dbScripts = await prisma.siteScript.findMany({
      where: {
        scriptType,
        isActive: true,
      },
      orderBy: [
        { position: 'asc' },
        { name: 'asc' }
      ],
    }) as DbScript[];
    return dbScripts.map(convertToScript).filter((script): script is Script => script !== null);
  } catch (error) {
    console.error(`Error getting ${scriptType} scripts:`, error);
    return [];
  }
}

/**
 * Get a script by ID
 * @param id The ID of the script to get
 * @returns The script or null if not found
 */
export async function getScriptById(id: string): Promise<Script | null> {
  try {
    const dbScript = await prisma.siteScript.findUnique({
      where: { id },
    }) as DbScript | null;
    return dbScript ? convertToScript(dbScript) : null;
  } catch (error) {
    console.error('Error getting script by ID:', error);
    return null;
  }
}

/**
 * Create a new script
 * @param data The script data
 * @returns The created script
 */
export async function createScript(data: Omit<Script, 'id' | 'createdAt' | 'updatedAt'>): Promise<Script | null> {
  try {
    const dbScript = await prisma.siteScript.create({
      data,
    }) as DbScript;
    return convertToScript(dbScript);
  } catch (error) {
    console.error('Error creating script:', error);
    return null;
  }
}

/**
 * Update a script
 * @param id The ID of the script to update
 * @param data The updated script data
 * @returns The updated script
 */
export async function updateScript(id: string, data: Partial<Omit<Script, 'id' | 'createdAt' | 'updatedAt'>>): Promise<Script | null> {
  try {
    const dbScript = await prisma.siteScript.update({
      where: { id },
      data,
    }) as DbScript;
    return convertToScript(dbScript);
  } catch (error) {
    console.error('Error updating script:', error);
    return null;
  }
}

/**
 * Delete a script
 * @param id The ID of the script to delete
 * @returns The deleted script
 */
export async function deleteScript(id: string): Promise<Script | null> {
  try {
    const dbScript = await prisma.siteScript.delete({
      where: { id },
    }) as DbScript;
    return convertToScript(dbScript);
  } catch (error) {
    console.error('Error deleting script:', error);
    return null;
  }
}
