import { cacheService, CacheKeys, CacheTTL } from '@/lib/redis';
import { readPortfolioMetadata } from '@/app/api/admin/portfolio/route';
import { analyticsService } from './analyticsService';

interface WarmupResult {
  key: string;
  success: boolean;
  error?: string;
  timeMs: number;
}

interface WarmupSummary {
  totalKeys: number;
  successCount: number;
  failedCount: number;
  totalTimeMs: number;
  results: WarmupResult[];
}

class CacheWarmer {
  // Warm up all critical cache entries
  async warmupAllCaches(): Promise<WarmupSummary> {
    console.log('🔥 Starting cache warmup process...');
    const startTime = Date.now();
    const results: WarmupResult[] = [];

    // Portfolio cache warmup
    const portfolioResults = await this.warmupPortfolioCache();
    results.push(...portfolioResults);

    // Analytics cache warmup
    const analyticsResults = await this.warmupAnalyticsCache();
    results.push(...analyticsResults);

    // Core site data warmup
    const siteDataResults = await this.warmupSiteDataCache();
    results.push(...siteDataResults);

    const totalTimeMs = Date.now() - startTime;
    const successCount = results.filter(r => r.success).length;
    const failedCount = results.filter(r => !r.success).length;

    const summary: WarmupSummary = {
      totalKeys: results.length,
      successCount,
      failedCount,
      totalTimeMs,
      results,
    };

    console.log(`🔥 Cache warmup completed: ${successCount}/${results.length} keys warmed in ${totalTimeMs}ms`);
    return summary;
  }

  // Warm up portfolio-related caches
  async warmupPortfolioCache(): Promise<WarmupResult[]> {
    const results: WarmupResult[] = [];

    try {
      // Warm up main portfolio metadata
      const startTime = Date.now();
      const portfolioItems = await readPortfolioMetadata();
      const warmupTime = Date.now() - startTime;

      results.push({
        key: CacheKeys.portfolio(),
        success: true,
        timeMs: warmupTime,
      });

      // Warm up category-specific caches with parallel processing for better performance
      const categories = ['logos', 'cards', 'fliers', 'letterheads', 'profiles', 'branding', 'all'];
      
      const categoryPromises = categories.map(async (category) => {
        try {
          const categoryStartTime = Date.now();
          const categoryKey = CacheKeys.portfolio(category === 'all' ? undefined : category);
          
          // Filter items for this category
          const filteredItems = category === 'all' 
            ? portfolioItems 
            : portfolioItems.filter(item => item.category === category);

          // Cache the filtered results with appropriate TTL
          const ttl = category === 'logos' ? CacheTTL.MEDIUM : CacheTTL.SHORT;
          await cacheService.set(categoryKey, filteredItems, ttl);
          
          return {
            key: categoryKey,
            success: true,
            timeMs: Date.now() - categoryStartTime,
          };
        } catch (error) {
          return {
            key: CacheKeys.portfolio(category === 'all' ? undefined : category),
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error',
            timeMs: 0,
          };
        }
      });

      // Wait for all category caches to complete
      const categoryResults = await Promise.all(categoryPromises);
      results.push(...categoryResults);

      // Warm up the main pages that use portfolio data
      try {
        const pageWarmupStartTime = Date.now();
        
        // Pre-warm specific logo data for faster page loads
        const logoItems = portfolioItems.filter(item => item.category === 'logos' && !item.deletedAt);
        await cacheService.set('warm:logos:count', logoItems.length, CacheTTL.LONG);
        await cacheService.set('warm:logos:featured', logoItems.slice(0, 8), CacheTTL.MEDIUM);
        
        results.push({
          key: 'warm:pages:portfolio_logos',
          success: true,
          timeMs: Date.now() - pageWarmupStartTime,
        });
      } catch (pageWarmupError) {
        results.push({
          key: 'warm:pages:portfolio_logos',
          success: false,
          error: pageWarmupError instanceof Error ? pageWarmupError.message : 'Unknown error',
          timeMs: 0,
        });
      }

    } catch (error) {
      results.push({
        key: CacheKeys.portfolio(),
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timeMs: 0,
      });
    }

    return results;
  }

  // Warm up analytics caches
  async warmupAnalyticsCache(): Promise<WarmupResult[]> {
    const results: WarmupResult[] = [];
    const periods = ['today', 'week', 'month'];
    const metrics = ['dashboard', 'performance'];

    for (const metric of metrics) {
      if (metric === 'performance') {
        // Warm up performance metrics (no period dependency)
        try {
          const startTime = Date.now();
          await analyticsService.getPerformanceMetrics();
          
          results.push({
            key: CacheKeys.analytics(metric, 'current'),
            success: true,
            timeMs: Date.now() - startTime,
          });
        } catch (error) {
          results.push({
            key: CacheKeys.analytics(metric, 'current'),
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error',
            timeMs: 0,
          });
        }
      } else {
        // Warm up dashboard analytics for each period
        for (const period of periods) {
          try {
            const startTime = Date.now();
            await analyticsService.getAnalyticsData(period);
            
            results.push({
              key: CacheKeys.analytics(metric, period),
              success: true,
              timeMs: Date.now() - startTime,
            });
          } catch (error) {
            results.push({
              key: CacheKeys.analytics(metric, period),
              success: false,
              error: error instanceof Error ? error.message : 'Unknown error',
              timeMs: 0,
            });
          }
        }
      }
    }

    return results;
  }

  // Warm up site data caches (services, testimonials, etc.)
  async warmupSiteDataCache(): Promise<WarmupResult[]> {
    const results: WarmupResult[] = [];

    // Warm up services cache
    try {
      const startTime = Date.now();
      const servicesKey = CacheKeys.services();
      
      // Mock services data (replace with actual service fetching)
      const mockServices = [
        { id: '1', title: 'Logo Design', description: 'Professional logo design services' },
        { id: '2', title: 'Web Design', description: 'Modern web design solutions' },
        { id: '3', title: 'Branding', description: 'Complete branding packages' },
      ];
      
      await cacheService.set(servicesKey, mockServices, CacheTTL.LONG);
      
      results.push({
        key: servicesKey,
        success: true,
        timeMs: Date.now() - startTime,
      });
    } catch (error) {
      results.push({
        key: CacheKeys.services(),
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timeMs: 0,
      });
    }

    // Warm up testimonials cache
    try {
      const startTime = Date.now();
      const testimonialsKey = CacheKeys.testimonials();
      
      // Mock testimonials data (replace with actual testimonial fetching)
      const mockTestimonials = [
        { id: '1', name: 'John Doe', company: 'ABC Corp', text: 'Great service!' },
        { id: '2', name: 'Jane Smith', company: 'XYZ Ltd', text: 'Excellent work!' },
      ];
      
      await cacheService.set(testimonialsKey, mockTestimonials, CacheTTL.LONG);
      
      results.push({
        key: testimonialsKey,
        success: true,
        timeMs: Date.now() - startTime,
      });
    } catch (error) {
      results.push({
        key: CacheKeys.testimonials(),
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timeMs: 0,
      });
    }

    return results;
  }

  // Warm up a specific cache type
  async warmupCacheType(type: 'portfolio' | 'analytics' | 'sitedata'): Promise<WarmupResult[]> {
    switch (type) {
      case 'portfolio':
        return this.warmupPortfolioCache();
      case 'analytics':
        return this.warmupAnalyticsCache();
      case 'sitedata':
        return this.warmupSiteDataCache();
      default:
        return [];
    }
  }

  // Schedule cache warmup (for use in cron jobs or scheduled tasks)
  async scheduleWarmup(): Promise<void> {
    console.log('📅 Scheduling cache warmup...');
    
    // Immediate warmup
    await this.warmupAllCaches();
    
    // Schedule periodic warmup every hour
    setInterval(async () => {
      console.log('⏰ Running scheduled cache warmup...');
      await this.warmupAllCaches();
    }, 60 * 60 * 1000); // 1 hour
  }

  // Warm up cache based on usage patterns
  async intelligentWarmup(): Promise<WarmupSummary> {
    console.log('🧠 Starting intelligent cache warmup based on usage patterns...');
    const results: WarmupResult[] = [];

    // Get current hour to determine what to prioritize
    const currentHour = new Date().getHours();
    
    // Business hours (9 AM - 5 PM) - prioritize portfolio and services
    if (currentHour >= 9 && currentHour <= 17) {
      console.log('📊 Business hours detected - prioritizing portfolio and services');
      const portfolioResults = await this.warmupPortfolioCache();
      const siteDataResults = await this.warmupSiteDataCache();
      results.push(...portfolioResults, ...siteDataResults);
    }
    
    // Evening hours - prioritize analytics for next day
    if (currentHour >= 18 || currentHour <= 6) {
      console.log('🌙 Off-hours detected - prioritizing analytics');
      const analyticsResults = await this.warmupAnalyticsCache();
      results.push(...analyticsResults);
    }
    
    // Always warm up the most critical caches
    const criticalCaches = [
      { key: CacheKeys.portfolio(), warmup: () => this.warmupPortfolioCache() },
    ];
    
    for (const cache of criticalCaches) {
      const exists = await cacheService.exists(cache.key);
      if (!exists) {
        console.log(`🚨 Critical cache missing: ${cache.key}`);
        const cacheResults = await cache.warmup();
        results.push(...cacheResults);
      }
    }

    const totalTimeMs = Date.now();
    const successCount = results.filter(r => r.success).length;
    const failedCount = results.filter(r => !r.success).length;

    return {
      totalKeys: results.length,
      successCount,
      failedCount,
      totalTimeMs,
      results,
    };
  }

  // Get cache warmup statistics
  async getWarmupStats(): Promise<{
    cachedKeys: string[];
    missingKeys: string[];
    cacheHitEstimate: number;
  }> {
    const criticalKeys = [
      CacheKeys.portfolio(),
      CacheKeys.portfolio('logos'),
      CacheKeys.portfolio('cards'),
      CacheKeys.services(),
      CacheKeys.testimonials(),
      CacheKeys.analytics('dashboard', 'today'),
      CacheKeys.analytics('performance', 'current'),
    ];

    const cachedKeys: string[] = [];
    const missingKeys: string[] = [];

    for (const key of criticalKeys) {
      const exists = await cacheService.exists(key);
      if (exists) {
        cachedKeys.push(key);
      } else {
        missingKeys.push(key);
      }
    }

    const cacheHitEstimate = Math.round((cachedKeys.length / criticalKeys.length) * 100);

    return {
      cachedKeys,
      missingKeys,
      cacheHitEstimate,
    };
  }
}

// Export singleton instance
export const cacheWarmer = new CacheWarmer();

// Convenience functions
export async function warmupAllCaches(): Promise<WarmupSummary> {
  return cacheWarmer.warmupAllCaches();
}

export async function warmupCacheType(type: 'portfolio' | 'analytics' | 'sitedata'): Promise<WarmupResult[]> {
  return cacheWarmer.warmupCacheType(type);
}

export async function intelligentWarmup(): Promise<WarmupSummary> {
  return cacheWarmer.intelligentWarmup();
}

export async function getWarmupStats() {
  return cacheWarmer.getWarmupStats();
} 