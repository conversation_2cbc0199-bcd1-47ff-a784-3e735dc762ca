import { PrismaClient } from '@prisma/client';
import { 
  FamilyUser, 
  FamilyTransaction, 
  FamilyLoan, 
  FamilyBusiness,
  BusinessTransaction,
  UserSavingsGoal,
  DashboardStats,
  TransactionStats,
  LoanStats,
  CreateTransactionData,
  CreateLoanData,
  ApproveLoanData,
  CreateBusinessData,
  CreateBusinessTransactionData,
  CreateSavingsGoalData,
  TransactionFilters,
  LoanFilters,
  BusinessFilters
} from '@/types/family-savings';
import { Decimal } from 'decimal.js';

export class FamilySavingsService {
  private prisma: PrismaClient;

  constructor() {
    this.prisma = new PrismaClient();
  }

  // Dashboard Services
  async getDashboardStats(): Promise<DashboardStats> {
    const [
      totalContributions,
      totalLoans,
      totalBusinessRevenue,
      activeUsers,
      pendingTransactions
    ] = await Promise.all([
      this.prisma.familyTransaction.aggregate({
        where: { 
          status: 'COMPLETED',
          transactionType: 'DEPOSIT',
          isApproved: true
        },
        _sum: { amount: true }
      }),
      this.prisma.familyLoan.aggregate({
        where: { status: 'APPROVED' },
        _sum: { approvedAmount: true }
      }),
      this.prisma.businessTransaction.aggregate({
        where: { transactionType: 'INCOME' },
        _sum: { amount: true }
      }),
      this.prisma.familyUser.count({
        where: { isActive: true, isApproved: true }
      }),
      this.prisma.familyTransaction.count({
        where: { status: 'PENDING' }
      })
    ]);

    // Calculate monthly growth (comparing current vs previous month)
    const currentMonth = new Date();
    const previousMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1, 1);
    const currentMonthStart = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), 1);

    const [currentMonthTotal, previousMonthTotal] = await Promise.all([
      this.prisma.familyTransaction.aggregate({
        where: {
          status: 'COMPLETED',
          isApproved: true,
          timestamp: { gte: currentMonthStart }
        },
        _sum: { amount: true }
      }),
      this.prisma.familyTransaction.aggregate({
        where: {
          status: 'COMPLETED',
          isApproved: true,
          timestamp: {
            gte: previousMonth,
            lt: currentMonthStart
          }
        },
        _sum: { amount: true }
      })
    ]);

    const currentTotal = currentMonthTotal._sum.amount?.toNumber() || 0;
    const previousTotal = previousMonthTotal._sum.amount?.toNumber() || 0;
    const monthlyGrowth = previousTotal > 0 ? ((currentTotal - previousTotal) / previousTotal) * 100 : 0;

    return {
      totalContributions: totalContributions._sum.amount?.toNumber() || 0,
      totalLoans: totalLoans._sum.approvedAmount?.toNumber() || 0,
      totalBusinessRevenue: totalBusinessRevenue._sum.amount?.toNumber() || 0,
      activeUsers,
      pendingTransactions,
      monthlyGrowth: Math.round(monthlyGrowth * 100) / 100
    };
  }

  // User Management
  async createFamilyUser(userData: Omit<FamilyUser, 'id' | 'createdAt' | 'updatedAt'>): Promise<FamilyUser> {
    const user = await this.prisma.familyUser.create({
      data: userData
    });
    return this.mapPrismaUser(user);
  }

  async getFamilyUsers(filters?: { isApproved?: boolean; isActive?: boolean }): Promise<FamilyUser[]> {
    const users = await this.prisma.familyUser.findMany({
      where: filters,
      orderBy: { createdAt: 'desc' }
    });
    return users.map(this.mapPrismaUser);
  }

  async approveFamilyUser(userId: string): Promise<FamilyUser> {
    const user = await this.prisma.familyUser.update({
      where: { id: userId },
      data: { isApproved: true }
    });
    return this.mapPrismaUser(user);
  }

  // Transaction Services
  async createTransaction(userId: string, data: CreateTransactionData): Promise<FamilyTransaction> {
    const transactionCode = await this.generateTransactionCode();
    
    const transaction = await this.prisma.familyTransaction.create({
      data: {
        userId,
        transactionCode,
        amount: new Decimal(data.amount),
        transactionType: data.transactionType,
        description: data.description,
        reference: data.reference,
        timestamp: new Date()
      },
      include: {
        user: true,
        approvedBy: true
      }
    });

    return this.mapPrismaTransaction(transaction);
  }

  async getTransactions(filters?: TransactionFilters): Promise<FamilyTransaction[]> {
    const where: any = {};
    
    if (filters?.status) where.status = filters.status;
    if (filters?.transactionType) where.transactionType = filters.transactionType;
    if (filters?.userId) where.userId = filters.userId;
    if (filters?.startDate) where.timestamp = { gte: filters.startDate };
    if (filters?.endDate) {
      where.timestamp = { ...where.timestamp, lte: filters.endDate };
    }

    const transactions = await this.prisma.familyTransaction.findMany({
      where,
      include: {
        user: true,
        approvedBy: true
      },
      orderBy: { timestamp: 'desc' }
    });

    return transactions.map(this.mapPrismaTransaction);
  }

  async approveTransaction(transactionId: string, approvedById: string): Promise<FamilyTransaction> {
    const transaction = await this.prisma.familyTransaction.update({
      where: { id: transactionId },
      data: {
        isApproved: true,
        status: 'COMPLETED',
        approvedById,
        approvedAt: new Date()
      },
      include: {
        user: true,
        approvedBy: true
      }
    });

    return this.mapPrismaTransaction(transaction);
  }

  async getTransactionStats(): Promise<TransactionStats> {
    const [deposits, withdrawals, pending, monthlyVolume] = await Promise.all([
      this.prisma.familyTransaction.aggregate({
        where: { 
          transactionType: 'DEPOSIT',
          status: 'COMPLETED',
          isApproved: true
        },
        _sum: { amount: true }
      }),
      this.prisma.familyTransaction.aggregate({
        where: { 
          transactionType: 'WITHDRAWAL',
          status: 'COMPLETED',
          isApproved: true
        },
        _sum: { amount: true }
      }),
      this.prisma.familyTransaction.count({
        where: { status: 'PENDING' }
      }),
      this.prisma.familyTransaction.aggregate({
        where: {
          status: 'COMPLETED',
          isApproved: true,
          timestamp: {
            gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
          }
        },
        _sum: { amount: true }
      })
    ]);

    return {
      totalDeposits: deposits._sum.amount?.toNumber() || 0,
      totalWithdrawals: withdrawals._sum.amount?.toNumber() || 0,
      pendingApprovals: pending,
      monthlyVolume: monthlyVolume._sum.amount?.toNumber() || 0
    };
  }

  // Loan Services
  async createLoan(borrowerId: string, data: CreateLoanData): Promise<FamilyLoan> {
    const loan = await this.prisma.familyLoan.create({
      data: {
        borrowerId,
        amount: new Decimal(data.amount),
        purpose: data.purpose,
        termMonths: data.termMonths,
        repaymentDeadline: data.repaymentDeadline
      },
      include: {
        borrower: true,
        approvedBy: true,
        repayments: true
      }
    });

    return this.mapPrismaLoan(loan);
  }

  async getLoans(filters?: LoanFilters): Promise<FamilyLoan[]> {
    const where: any = {};
    
    if (filters?.status) where.status = filters.status;
    if (filters?.borrowerId) where.borrowerId = filters.borrowerId;
    if (filters?.startDate) where.applicationDate = { gte: filters.startDate };
    if (filters?.endDate) {
      where.applicationDate = { ...where.applicationDate, lte: filters.endDate };
    }

    const loans = await this.prisma.familyLoan.findMany({
      where,
      include: {
        borrower: true,
        approvedBy: true,
        repayments: true
      },
      orderBy: { applicationDate: 'desc' }
    });

    return loans.map(this.mapPrismaLoan);
  }

  async approveLoan(loanId: string, approvedById: string, data: ApproveLoanData): Promise<FamilyLoan> {
    const loan = await this.prisma.familyLoan.update({
      where: { id: loanId },
      data: {
        status: 'APPROVED',
        approvedById,
        approvalDate: new Date(),
        approvedAmount: new Decimal(data.approvedAmount),
        interestRate: new Decimal(data.interestRate),
        termMonths: data.termMonths,
        interestType: data.interestType,
        compoundingFrequency: data.compoundingFrequency
      },
      include: {
        borrower: true,
        approvedBy: true,
        repayments: true
      }
    });

    return this.mapPrismaLoan(loan);
  }

  async rejectLoan(loanId: string, rejectionReason: string): Promise<FamilyLoan> {
    const loan = await this.prisma.familyLoan.update({
      where: { id: loanId },
      data: {
        status: 'REJECTED',
        rejectionReason
      },
      include: {
        borrower: true,
        approvedBy: true,
        repayments: true
      }
    });

    return this.mapPrismaLoan(loan);
  }

  async getLoanStats(): Promise<LoanStats> {
    const [totalValue, activeCount, overdue, interest] = await Promise.all([
      this.prisma.familyLoan.aggregate({
        where: { status: 'APPROVED' },
        _sum: { approvedAmount: true }
      }),
      this.prisma.familyLoan.count({
        where: { status: 'APPROVED' }
      }),
      this.prisma.familyLoan.count({
        where: {
          status: 'APPROVED',
          repaymentDeadline: { lt: new Date() }
        }
      }),
      // Calculate interest - simplified calculation
      this.calculateTotalInterestGenerated()
    ]);

    return {
      totalLoanValue: totalValue._sum.approvedAmount?.toNumber() || 0,
      activeLoanCount: activeCount,
      overdueLoans: overdue,
      totalInterestGenerated: interest
    };
  }

  // Business Services
  async createBusiness(ownerId: string, data: CreateBusinessData): Promise<FamilyBusiness> {
    const business = await this.prisma.familyBusiness.create({
      data: {
        ownerId,
        name: data.name,
        businessType: data.businessType,
        description: data.description,
        registrationNumber: data.registrationNumber,
        location: data.location
      },
      include: {
        owner: true,
        transactions: true,
        categories: true,
        inventoryItems: true,
        reports: true
      }
    });

    return this.mapPrismaBusiness(business);
  }

  async getBusinesses(filters?: BusinessFilters): Promise<FamilyBusiness[]> {
    const where: any = {};
    
    if (filters?.businessType) where.businessType = filters.businessType;
    if (filters?.ownerId) where.ownerId = filters.ownerId;

    const businesses = await this.prisma.familyBusiness.findMany({
      where,
      include: {
        owner: true,
        transactions: {
          orderBy: { date: 'desc' },
          take: 10
        },
        categories: true,
        inventoryItems: true,
        reports: {
          orderBy: { generatedAt: 'desc' },
          take: 5
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    return businesses.map(this.mapPrismaBusiness);
  }

  async createBusinessTransaction(businessId: string, data: CreateBusinessTransactionData): Promise<BusinessTransaction> {
    const transaction = await this.prisma.businessTransaction.create({
      data: {
        businessId,
        categoryId: data.categoryId,
        transactionType: data.transactionType,
        amount: new Decimal(data.amount),
        date: data.date,
        description: data.description,
        paymentMethod: data.paymentMethod,
        referenceNumber: data.referenceNumber
      },
      include: {
        business: true,
        category: true
      }
    });

    return this.mapPrismaBusinessTransaction(transaction);
  }

  // Savings Goals Services
  async createSavingsGoal(userId: string, data: CreateSavingsGoalData): Promise<UserSavingsGoal> {
    const goal = await this.prisma.userSavingsGoal.create({
      data: {
        userId,
        title: data.title,
        monthlyTarget: new Decimal(data.monthlyTarget),
        startDate: data.startDate,
        endDate: data.endDate,
        description: data.description,
        reminderTime: data.reminderTime
      },
      include: {
        user: true
      }
    });

    return this.mapPrismaSavingsGoal(goal);
  }

  async getSavingsGoals(userId?: string): Promise<UserSavingsGoal[]> {
    const goals = await this.prisma.userSavingsGoal.findMany({
      where: userId ? { userId, isActive: true } : { isActive: true },
      include: {
        user: true
      },
      orderBy: { createdAt: 'desc' }
    });

    return goals.map(this.mapPrismaSavingsGoal);
  }

  // Utility Methods
  private async generateTransactionCode(): Promise<string> {
    const prefix = 'TXN';
    const timestamp = Date.now().toString().slice(-6);
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `${prefix}${timestamp}${random}`;
  }

  private async calculateTotalInterestGenerated(): Promise<number> {
    // Simplified interest calculation - in production, this would be more complex
    const approvedLoans = await this.prisma.familyLoan.findMany({
      where: { status: 'APPROVED' }
    });

    let totalInterest = 0;
    const currentDate = new Date();

    for (const loan of approvedLoans) {
      if (loan.approvalDate && loan.approvedAmount) {
        const monthsElapsed = Math.floor(
          (currentDate.getTime() - loan.approvalDate.getTime()) / (1000 * 60 * 60 * 24 * 30)
        );
        
        const principal = loan.approvedAmount.toNumber();
        const rate = loan.interestRate.toNumber() / 100 / 12; // Monthly rate
        
        if (loan.interestType === 'SIMPLE') {
          totalInterest += principal * rate * monthsElapsed;
        } else {
          // Compound interest calculation
          const compoundFactor = Math.pow(1 + rate, monthsElapsed);
          totalInterest += principal * (compoundFactor - 1);
        }
      }
    }

    return Math.round(totalInterest * 100) / 100;
  }

  // Mapping functions to convert Prisma objects to TypeScript interfaces
  private mapPrismaUser(user: any): FamilyUser {
    return {
      id: user.id,
      email: user.email,
      username: user.username,
      firstName: user.firstName,
      lastName: user.lastName,
      phoneNumber: user.phoneNumber,
      isApproved: user.isApproved,
      isActive: user.isActive,
      dateJoined: user.dateJoined,
      lastLogin: user.lastLogin,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt
    };
  }

  private mapPrismaTransaction(transaction: any): FamilyTransaction {
    return {
      id: transaction.id,
      userId: transaction.userId,
      transactionType: transaction.transactionType,
      transactionCode: transaction.transactionCode,
      amount: transaction.amount.toNumber(),
      status: transaction.status,
      reference: transaction.reference,
      description: transaction.description,
      timestamp: transaction.timestamp,
      rawMessage: transaction.rawMessage,
      senderName: transaction.senderName,
      isApproved: transaction.isApproved,
      approvedById: transaction.approvedById,
      approvedAt: transaction.approvedAt,
      createdAt: transaction.createdAt,
      updatedAt: transaction.updatedAt,
      user: transaction.user ? this.mapPrismaUser(transaction.user) : undefined,
      approvedBy: transaction.approvedBy ? this.mapPrismaUser(transaction.approvedBy) : undefined
    };
  }

  private mapPrismaLoan(loan: any): FamilyLoan {
    return {
      id: loan.id,
      borrowerId: loan.borrowerId,
      amount: loan.amount.toNumber(),
      approvedAmount: loan.approvedAmount?.toNumber(),
      termMonths: loan.termMonths,
      purpose: loan.purpose,
      status: loan.status,
      applicationDate: loan.applicationDate,
      approvalDate: loan.approvalDate,
      repaymentDeadline: loan.repaymentDeadline,
      approvedById: loan.approvedById,
      rejectionReason: loan.rejectionReason,
      transactionMessage: loan.transactionMessage,
      interestRate: loan.interestRate.toNumber(),
      interestType: loan.interestType,
      compoundingFrequency: loan.compoundingFrequency,
      createdAt: loan.createdAt,
      updatedAt: loan.updatedAt,
      borrower: loan.borrower ? this.mapPrismaUser(loan.borrower) : undefined,
      approvedBy: loan.approvedBy ? this.mapPrismaUser(loan.approvedBy) : undefined,
      repayments: loan.repayments?.map((r: any) => ({
        id: r.id,
        loanId: r.loanId,
        userId: r.userId,
        amount: r.amount.toNumber(),
        paymentDate: r.paymentDate,
        transactionReference: r.transactionReference,
        status: r.status,
        approvedById: r.approvedById,
        approvalDate: r.approvalDate,
        rejectionReason: r.rejectionReason,
        createdAt: r.createdAt,
        updatedAt: r.updatedAt
      }))
    };
  }

  private mapPrismaBusiness(business: any): FamilyBusiness {
    return {
      id: business.id,
      ownerId: business.ownerId,
      name: business.name,
      businessType: business.businessType,
      description: business.description,
      registrationNumber: business.registrationNumber,
      location: business.location,
      createdAt: business.createdAt,
      updatedAt: business.updatedAt,
      owner: business.owner ? this.mapPrismaUser(business.owner) : undefined,
      transactions: business.transactions?.map(this.mapPrismaBusinessTransaction),
      categories: business.categories?.map((c: any) => ({
        id: c.id,
        businessId: c.businessId,
        name: c.name,
        categoryType: c.categoryType,
        description: c.description,
        isActive: c.isActive,
        createdAt: c.createdAt
      })),
      inventoryItems: business.inventoryItems?.map((i: any) => ({
        id: i.id,
        businessId: i.businessId,
        name: i.name,
        description: i.description,
        quantity: i.quantity.toNumber(),
        unitPrice: i.unitPrice.toNumber(),
        reorderLevel: i.reorderLevel.toNumber(),
        createdAt: i.createdAt,
        updatedAt: i.updatedAt
      })),
      reports: business.reports?.map((r: any) => ({
        id: r.id,
        businessId: r.businessId,
        reportType: r.reportType,
        startDate: r.startDate,
        endDate: r.endDate,
        totalIncome: r.totalIncome.toNumber(),
        totalExpenses: r.totalExpenses.toNumber(),
        netProfitLoss: r.netProfitLoss.toNumber(),
        notes: r.notes,
        generatedAt: r.generatedAt
      }))
    };
  }

  private mapPrismaBusinessTransaction(transaction: any): BusinessTransaction {
    return {
      id: transaction.id,
      businessId: transaction.businessId,
      categoryId: transaction.categoryId,
      transactionType: transaction.transactionType,
      amount: transaction.amount.toNumber(),
      date: transaction.date,
      description: transaction.description,
      paymentMethod: transaction.paymentMethod,
      referenceNumber: transaction.referenceNumber,
      createdAt: transaction.createdAt,
      updatedAt: transaction.updatedAt,
      business: transaction.business ? this.mapPrismaBusiness(transaction.business) : undefined,
      category: transaction.category ? {
        id: transaction.category.id,
        businessId: transaction.category.businessId,
        name: transaction.category.name,
        categoryType: transaction.category.categoryType,
        description: transaction.category.description,
        isActive: transaction.category.isActive,
        createdAt: transaction.category.createdAt
      } : undefined
    };
  }

  private mapPrismaSavingsGoal(goal: any): UserSavingsGoal {
    return {
      id: goal.id,
      userId: goal.userId,
      title: goal.title,
      monthlyTarget: goal.monthlyTarget.toNumber(),
      startDate: goal.startDate,
      endDate: goal.endDate,
      description: goal.description,
      isActive: goal.isActive,
      reminderTime: goal.reminderTime,
      lastReminderSent: goal.lastReminderSent,
      createdAt: goal.createdAt,
      updatedAt: goal.updatedAt,
      user: goal.user ? this.mapPrismaUser(goal.user) : undefined
    };
  }

  async disconnect() {
    await this.prisma.$disconnect();
  }
}

export const familySavingsService = new FamilySavingsService(); 