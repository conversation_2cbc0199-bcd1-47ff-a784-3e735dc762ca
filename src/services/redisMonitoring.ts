import { getRedisClient } from '@/lib/redis';
import { rateLimitAdmin } from '@/lib/rateLimiter';

export interface RedisHealthCheck {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  responseTime: number;
  uptime: number;
  memory: {
    used: number;
    peak: number;
    fragmentation: number;
  };
  connections: {
    connected: number;
    total: number;
  };
  keyspace: {
    db0: {
      keys: number;
      expires: number;
    };
  };
  performance: {
    opsPerSecond: number;
    hitRate: number;
  };
  alerts: HealthAlert[];
}

export interface HealthAlert {
  level: 'warning' | 'critical';
  message: string;
  metric: string;
  value: number;
  threshold: number;
}

export interface RedisMonitoringConfig {
  healthCheckInterval: number; // milliseconds
  alertThresholds: {
    memoryUsage: number; // percentage
    responseTime: number; // milliseconds
    connectionUsage: number; // percentage
    hitRate: number; // percentage (minimum)
    fragmentation: number; // ratio
  };
  enableAlerting: boolean;
  alertingWebhook?: string;
}

class RedisMonitoringService {
  private redis = getRedisClient();
  private config: RedisMonitoringConfig;
  private healthCheckInterval: NodeJS.Timeout | null = null;
  private lastHealthCheck: RedisHealthCheck | null = null;
  private healthHistory: RedisHealthCheck[] = [];
  
  constructor(config?: Partial<RedisMonitoringConfig>) {
    this.config = {
      healthCheckInterval: 30000, // 30 seconds
      alertThresholds: {
        memoryUsage: 85, // 85%
        responseTime: 100, // 100ms
        connectionUsage: 80, // 80%
        hitRate: 70, // 70%
        fragmentation: 2.0, // 2.0 ratio
      },
      enableAlerting: true,
      ...config,
    };
  }

  // Start monitoring Redis health
  startMonitoring(): void {
    if (this.healthCheckInterval) {
      console.log('Redis monitoring already running');
      return;
    }

    console.log('🏥 Starting Redis health monitoring...');
    
    // Initial health check
    this.performHealthCheck();
    
    // Schedule regular health checks
    this.healthCheckInterval = setInterval(() => {
      this.performHealthCheck();
    }, this.config.healthCheckInterval);
  }

  // Stop monitoring
  stopMonitoring(): void {
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
      this.healthCheckInterval = null;
      console.log('🏥 Redis monitoring stopped');
    }
  }

  // Perform comprehensive health check
  async performHealthCheck(): Promise<RedisHealthCheck> {
    const startTime = Date.now();
    const alerts: HealthAlert[] = [];

    try {
      if (!this.redis) {
        const unhealthyCheck: RedisHealthCheck = {
          status: 'unhealthy',
          timestamp: new Date().toISOString(),
          responseTime: -1,
          uptime: 0,
          memory: { used: 0, peak: 0, fragmentation: 0 },
          connections: { connected: 0, total: 0 },
          keyspace: { db0: { keys: 0, expires: 0 } },
          performance: { opsPerSecond: 0, hitRate: 0 },
          alerts: [
            {
              level: 'critical',
              message: 'Redis client not available',
              metric: 'connection',
              value: 0,
              threshold: 1,
            },
          ],
        };
        
        this.lastHealthCheck = unhealthyCheck;
        this.addToHistory(unhealthyCheck);
        return unhealthyCheck;
      }

      // Test basic connectivity
      await this.redis.ping();
      const responseTime = Date.now() - startTime;

      // Get Redis info
      const info = await this.redis.info();
      const infoData = this.parseRedisInfo(info);

      // Get keyspace info
      const keyspaceInfo = await this.getKeyspaceInfo();

      // Calculate performance metrics
      const performanceMetrics = await this.calculatePerformanceMetrics(infoData);

      // Build health check result
      const healthCheck: RedisHealthCheck = {
        status: 'healthy', // Will be updated based on alerts
        timestamp: new Date().toISOString(),
        responseTime,
        uptime: parseInt(infoData.uptime_in_seconds || '0'),
        memory: {
          used: parseInt(infoData.used_memory || '0'),
          peak: parseInt(infoData.used_memory_peak || '0'),
          fragmentation: parseFloat(infoData.mem_fragmentation_ratio || '1'),
        },
        connections: {
          connected: parseInt(infoData.connected_clients || '0'),
          total: parseInt(infoData.maxclients || '10000'),
        },
        keyspace: {
          db0: keyspaceInfo,
        },
        performance: performanceMetrics,
        alerts: [],
      };

      // Check thresholds and generate alerts
      alerts.push(...this.checkThresholds(healthCheck));
      healthCheck.alerts = alerts;

      // Determine overall status
      if (alerts.some(a => a.level === 'critical')) {
        healthCheck.status = 'unhealthy';
      } else if (alerts.some(a => a.level === 'warning')) {
        healthCheck.status = 'degraded';
      }

      // Store results
      this.lastHealthCheck = healthCheck;
      this.addToHistory(healthCheck);

      // Send alerts if necessary
      if (this.config.enableAlerting && alerts.length > 0) {
        await this.sendAlerts(alerts);
      }

      console.log(`🏥 Redis health check completed: ${healthCheck.status} (${responseTime}ms)`);
      return healthCheck;
    } catch (error) {
      console.error('Redis health check failed:', error);
      
      const errorCheck: RedisHealthCheck = {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        responseTime: Date.now() - startTime,
        uptime: 0,
        memory: { used: 0, peak: 0, fragmentation: 0 },
        connections: { connected: 0, total: 0 },
        keyspace: { db0: { keys: 0, expires: 0 } },
        performance: { opsPerSecond: 0, hitRate: 0 },
        alerts: [
          {
            level: 'critical',
            message: `Health check failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
            metric: 'connection',
            value: 0,
            threshold: 1,
          },
        ],
      };
      
      this.lastHealthCheck = errorCheck;
      this.addToHistory(errorCheck);
      return errorCheck;
    }
  }

  // Parse Redis INFO command output
  private parseRedisInfo(info: string): Record<string, string> {
    const infoData: Record<string, string> = {};
    const lines = info.split('\r\n');
    
    for (const line of lines) {
      if (line.includes(':')) {
        const [key, value] = line.split(':');
        infoData[key] = value;
      }
    }
    
    return infoData;
  }

  // Get keyspace information
  private async getKeyspaceInfo(): Promise<{ keys: number; expires: number }> {
    try {
      if (!this.redis) return { keys: 0, expires: 0 };

      const info = await this.redis.info('keyspace');
      const lines = info.split('\r\n');
      
      for (const line of lines) {
        if (line.startsWith('db0:')) {
          const match = line.match(/keys=(\d+),expires=(\d+)/);
          if (match) {
            return {
              keys: parseInt(match[1]),
              expires: parseInt(match[2]),
            };
          }
        }
      }
      
      // Fallback: count keys manually (expensive operation)
      const keys = await this.redis.keys('*');
      return { keys: keys.length, expires: 0 };
    } catch (error) {
      console.error('Error getting keyspace info:', error);
      return { keys: 0, expires: 0 };
    }
  }

  // Calculate performance metrics
  private async calculatePerformanceMetrics(infoData: Record<string, string>): Promise<{
    opsPerSecond: number;
    hitRate: number;
  }> {
    try {
      const totalOps = parseInt(infoData.total_commands_processed || '0');
      const uptime = parseInt(infoData.uptime_in_seconds || '1');
      const opsPerSecond = Math.round(totalOps / uptime);

      // Calculate cache hit rate (simplified)
      const keyspaceHits = parseInt(infoData.keyspace_hits || '0');
      const keyspaceMisses = parseInt(infoData.keyspace_misses || '0');
      const totalRequests = keyspaceHits + keyspaceMisses;
      const hitRate = totalRequests > 0 ? Math.round((keyspaceHits / totalRequests) * 100) : 0;

      return { opsPerSecond, hitRate };
    } catch (error) {
      console.error('Error calculating performance metrics:', error);
      return { opsPerSecond: 0, hitRate: 0 };
    }
  }

  // Check thresholds and generate alerts
  private checkThresholds(healthCheck: RedisHealthCheck): HealthAlert[] {
    const alerts: HealthAlert[] = [];

    // Memory usage alert
    const memoryUsage = (healthCheck.memory.used / healthCheck.memory.peak) * 100;
    if (memoryUsage > this.config.alertThresholds.memoryUsage) {
      alerts.push({
        level: memoryUsage > 95 ? 'critical' : 'warning',
        message: `High memory usage: ${memoryUsage.toFixed(1)}%`,
        metric: 'memory_usage',
        value: memoryUsage,
        threshold: this.config.alertThresholds.memoryUsage,
      });
    }

    // Response time alert
    if (healthCheck.responseTime > this.config.alertThresholds.responseTime) {
      alerts.push({
        level: healthCheck.responseTime > 500 ? 'critical' : 'warning',
        message: `High response time: ${healthCheck.responseTime}ms`,
        metric: 'response_time',
        value: healthCheck.responseTime,
        threshold: this.config.alertThresholds.responseTime,
      });
    }

    // Connection usage alert
    const connectionUsage = (healthCheck.connections.connected / healthCheck.connections.total) * 100;
    if (connectionUsage > this.config.alertThresholds.connectionUsage) {
      alerts.push({
        level: connectionUsage > 95 ? 'critical' : 'warning',
        message: `High connection usage: ${connectionUsage.toFixed(1)}%`,
        metric: 'connection_usage',
        value: connectionUsage,
        threshold: this.config.alertThresholds.connectionUsage,
      });
    }

    // Hit rate alert
    if (healthCheck.performance.hitRate < this.config.alertThresholds.hitRate) {
      alerts.push({
        level: healthCheck.performance.hitRate < 50 ? 'critical' : 'warning',
        message: `Low cache hit rate: ${healthCheck.performance.hitRate}%`,
        metric: 'hit_rate',
        value: healthCheck.performance.hitRate,
        threshold: this.config.alertThresholds.hitRate,
      });
    }

    // Fragmentation alert
    if (healthCheck.memory.fragmentation > this.config.alertThresholds.fragmentation) {
      alerts.push({
        level: healthCheck.memory.fragmentation > 3.0 ? 'critical' : 'warning',
        message: `High memory fragmentation: ${healthCheck.memory.fragmentation.toFixed(2)}`,
        metric: 'fragmentation',
        value: healthCheck.memory.fragmentation,
        threshold: this.config.alertThresholds.fragmentation,
      });
    }

    return alerts;
  }

  // Send alerts (webhook or console)
  private async sendAlerts(alerts: HealthAlert[]): Promise<void> {
    try {
      const alertMessage = {
        service: 'Redis',
        timestamp: new Date().toISOString(),
        alerts: alerts.map(alert => ({
          level: alert.level,
          message: alert.message,
          metric: alert.metric,
          value: alert.value,
          threshold: alert.threshold,
        })),
      };

      if (this.config.alertingWebhook) {
        // Send to webhook
        const response = await fetch(this.config.alertingWebhook, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(alertMessage),
        });

        if (!response.ok) {
          console.error('Failed to send alert webhook:', response.statusText);
        }
      } else {
        // Log to console
        console.warn('🚨 Redis Alerts:', alertMessage);
      }
    } catch (error) {
      console.error('Error sending alerts:', error);
    }
  }

  // Add health check to history
  private addToHistory(healthCheck: RedisHealthCheck): void {
    this.healthHistory.push(healthCheck);
    
    // Keep only last 100 health checks
    if (this.healthHistory.length > 100) {
      this.healthHistory = this.healthHistory.slice(-100);
    }
  }

  // Get current health status
  getCurrentHealth(): RedisHealthCheck | null {
    return this.lastHealthCheck;
  }

  // Get health history
  getHealthHistory(limit: number = 50): RedisHealthCheck[] {
    return this.healthHistory.slice(-limit);
  }

  // Get monitoring statistics
  async getMonitoringStats(): Promise<{
    isMonitoring: boolean;
    lastCheck: string | null;
    totalChecks: number;
    averageResponseTime: number;
    alertCount: {
      warning: number;
      critical: number;
    };
    uptimePercentage: number;
  }> {
    const alertCount = { warning: 0, critical: 0 };
    let totalResponseTime = 0;
    let healthyChecks = 0;

    for (const check of this.healthHistory) {
      totalResponseTime += check.responseTime;
      if (check.status === 'healthy') healthyChecks++;
      
      for (const alert of check.alerts) {
        if (alert.level === 'warning') alertCount.warning++;
        if (alert.level === 'critical') alertCount.critical++;
      }
    }

    const averageResponseTime = this.healthHistory.length > 0 
      ? Math.round(totalResponseTime / this.healthHistory.length)
      : 0;

    const uptimePercentage = this.healthHistory.length > 0
      ? Math.round((healthyChecks / this.healthHistory.length) * 100)
      : 0;

    return {
      isMonitoring: this.healthCheckInterval !== null,
      lastCheck: this.lastHealthCheck?.timestamp || null,
      totalChecks: this.healthHistory.length,
      averageResponseTime,
      alertCount,
      uptimePercentage,
    };
  }

  // Update monitoring configuration
  updateConfig(newConfig: Partial<RedisMonitoringConfig>): void {
    this.config = { ...this.config, ...newConfig };
    console.log('🏥 Redis monitoring configuration updated');
  }
}

// Export singleton instance
export const redisMonitoring = new RedisMonitoringService();

// Convenience functions
export async function startRedisMonitoring(config?: Partial<RedisMonitoringConfig>): Promise<void> {
  if (config) {
    redisMonitoring.updateConfig(config);
  }
  redisMonitoring.startMonitoring();
}

export async function stopRedisMonitoring(): Promise<void> {
  redisMonitoring.stopMonitoring();
}

export async function getRedisHealth(): Promise<RedisHealthCheck | null> {
  return redisMonitoring.getCurrentHealth();
}

export async function performRedisHealthCheck(): Promise<RedisHealthCheck> {
  return redisMonitoring.performHealthCheck();
}

export async function getRedisMonitoringStats() {
  return redisMonitoring.getMonitoringStats();
} 