import prisma from '@/lib/prisma';

export interface TestimonialData {
  id?: number;
  name: string;
  location: string;
  project: string;
  testimonial: string;
  rating: number;
  company?: string;
  active?: boolean;
  order?: number;
}

export interface TestimonialFilters {
  active?: boolean;
  search?: string;
}

/**
 * Get all testimonials with optional filtering
 */
export async function getAllTestimonials(filters: TestimonialFilters = {}) {
  try {
    const where: any = {};

    if (filters.active !== undefined) {
      where.active = filters.active;
    }

    if (filters.search) {
      where.OR = [
        { name: { contains: filters.search, mode: 'insensitive' } },
        { company: { contains: filters.search, mode: 'insensitive' } },
        { location: { contains: filters.search, mode: 'insensitive' } },
        { testimonial: { contains: filters.search, mode: 'insensitive' } }
      ];
    }

    const testimonials = await prisma.testimonial.findMany({
      where,
      orderBy: [
        { order: 'asc' },
        { createdAt: 'desc' }
      ]
    });

    return testimonials;
  } catch (error) {
    console.error('Error fetching testimonials:', error);
    throw new Error('Failed to fetch testimonials');
  }
}

/**
 * Get active testimonials for public display
 */
export async function getActiveTestimonials() {
  try {
    const testimonials = await prisma.testimonial.findMany({
      where: { active: true },
      orderBy: [
        { order: 'asc' },
        { createdAt: 'desc' }
      ]
    });

    return testimonials;
  } catch (error) {
    console.error('Error fetching active testimonials:', error);
    throw new Error('Failed to fetch active testimonials');
  }
}

/**
 * Get testimonial by ID
 */
export async function getTestimonialById(id: number) {
  try {
    const testimonial = await prisma.testimonial.findUnique({
      where: { id }
    });

    if (!testimonial) {
      throw new Error('Testimonial not found');
    }

    return testimonial;
  } catch (error) {
    console.error('Error fetching testimonial:', error);
    throw error;
  }
}

/**
 * Create new testimonial
 */
export async function createTestimonial(data: TestimonialData) {
  try {
    // Get the next order number
    const maxOrder = await prisma.testimonial.aggregate({
      _max: { order: true }
    });

    const testimonial = await prisma.testimonial.create({
      data: {
        ...data,
        order: data.order ?? (maxOrder._max.order ?? 0) + 1
      }
    });

    return testimonial;
  } catch (error) {
    console.error('Error creating testimonial:', error);
    throw new Error('Failed to create testimonial');
  }
}

/**
 * Update testimonial
 */
export async function updateTestimonial(id: number, data: Partial<TestimonialData>) {
  try {
    const testimonial = await prisma.testimonial.update({
      where: { id },
      data
    });

    return testimonial;
  } catch (error) {
    console.error('Error updating testimonial:', error);
    throw new Error('Failed to update testimonial');
  }
}

/**
 * Delete testimonial
 */
export async function deleteTestimonial(id: number) {
  try {
    await prisma.testimonial.delete({
      where: { id }
    });

    return true;
  } catch (error) {
    console.error('Error deleting testimonial:', error);
    throw new Error('Failed to delete testimonial');
  }
}

/**
 * Reorder testimonials
 */
export async function reorderTestimonials(testimonialIds: number[]) {
  try {
    const updates = testimonialIds.map((id, index) =>
      prisma.testimonial.update({
        where: { id },
        data: { order: index + 1 }
      })
    );

    await prisma.$transaction(updates);
    return true;
  } catch (error) {
    console.error('Error reordering testimonials:', error);
    throw new Error('Failed to reorder testimonials');
  }
}

/**
 * Toggle testimonial active status
 */
export async function toggleTestimonialStatus(id: number) {
  try {
    const testimonial = await prisma.testimonial.findUnique({
      where: { id }
    });

    if (!testimonial) {
      throw new Error('Testimonial not found');
    }

    const updated = await prisma.testimonial.update({
      where: { id },
      data: { active: !testimonial.active }
    });

    return updated;
  } catch (error) {
    console.error('Error toggling testimonial status:', error);
    throw new Error('Failed to toggle testimonial status');
  }
}

/**
 * Get testimonials statistics
 */
export async function getTestimonialsStats() {
  try {
    const [total, active, inactive] = await Promise.all([
      prisma.testimonial.count(),
      prisma.testimonial.count({ where: { active: true } }),
      prisma.testimonial.count({ where: { active: false } })
    ]);

    return {
      total,
      active,
      inactive
    };
  } catch (error) {
    console.error('Error fetching testimonials stats:', error);
    return {
      total: 0,
      active: 0,
      inactive: 0
    };
  }
}
