import prisma from '@/lib/prisma';
import { BlogCategory } from '@/types/blog';

// Get all blog categories
export async function getAllBlogCategories(): Promise<BlogCategory[]> {
  try {
    console.log('Fetching all blog categories from database');
    const categories = await prisma.blogCategory.findMany({
      orderBy: {
        name: 'asc'
      }
    });

    console.log(`Found ${categories.length} blog categories in database`);
    return categories.map(formatBlogCategory);
  } catch (error) {
    console.error('Error getting all blog categories:', error);
    throw new Error('Failed to fetch blog categories from database');
  }
}

// Get a blog category by ID
export async function getBlogCategoryById(id: string): Promise<BlogCategory | null> {
  try {
    const category = await prisma.blogCategory.findUnique({
      where: {
        id
      }
    });

    if (!category) {
      return null;
    }

    return formatBlogCategory(category);
  } catch (error) {
    console.error(`Error getting blog category by ID ${id}:`, error);
    throw error;
  }
}

// Get a blog category by slug
export async function getBlogCategoryBySlug(slug: string): Promise<BlogCategory | null> {
  try {
    const category = await prisma.blogCategory.findUnique({
      where: {
        slug
      }
    });

    if (!category) {
      return null;
    }

    return formatBlogCategory(category);
  } catch (error) {
    console.error(`Error getting blog category by slug ${slug}:`, error);
    throw error;
  }
}

// Create a new blog category
export async function createBlogCategory(data: Omit<BlogCategory, 'id' | 'createdAt' | 'updatedAt'>): Promise<BlogCategory> {
  try {
    const newCategory = await prisma.blogCategory.create({
      data: {
        name: data.name,
        slug: data.slug,
        description: data.description || undefined
      }
    });

    return formatBlogCategory(newCategory);
  } catch (error) {
    console.error('Error creating blog category:', error);
    throw error;
  }
}

// Update a blog category
export async function updateBlogCategory(id: string, data: Partial<BlogCategory>): Promise<BlogCategory | null> {
  try {
    // Check if the category exists
    const existingCategory = await prisma.blogCategory.findUnique({
      where: {
        id
      }
    });

    if (!existingCategory) {
      return null;
    }

    // Create an update data object with only the fields that are provided
    const updateData: any = {};
    if (data.name !== undefined) updateData.name = data.name;
    if (data.slug !== undefined) updateData.slug = data.slug;
    if (data.description !== undefined) updateData.description = data.description;

    const updatedCategory = await prisma.blogCategory.update({
      where: {
        id
      },
      data: updateData
    });

    return formatBlogCategory(updatedCategory);
  } catch (error) {
    console.error(`Error updating blog category ${id}:`, error);
    throw error;
  }
}

// Delete a blog category
export async function deleteBlogCategory(id: string): Promise<boolean> {
  try {
    await prisma.blogCategory.delete({
      where: {
        id
      }
    });

    return true;
  } catch (error) {
    console.error(`Error deleting blog category ${id}:`, error);
    return false;
  }
}

// Helper function to format blog category data from the database
function formatBlogCategory(category: any): BlogCategory {
  return {
    id: category.id,
    name: category.name,
    slug: category.slug,
    description: category.description || undefined,
    createdAt: category.createdAt.toISOString(),
    updatedAt: category.updatedAt.toISOString()
  };
} 