import { S3Client, PutObjectCommand, GetObjectCommand, DeleteObjectCommand, ListObjectsV2Command } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { exec } from 'child_process';
import { promisify } from 'util';
import { readFile, writeFile, unlink, stat } from 'fs/promises';
import { join } from 'path';
import { getDefaultStorageConfig } from '@/lib/storageConfig';

const execAsync = promisify(exec);

interface BackupMetadata {
  id: string;
  filename: string;
  description: string;
  size: number;
  createdAt: string;
  type: 'manual' | 'scheduled';
  status: 'completed' | 'failed' | 'in_progress';
}

class S3BackupService {
  private s3Client: S3Client | null = null;
  private bucketName: string | null = null;
  private region: string | null = null;

  constructor() {
    // Initialize async configuration - will be loaded when needed
  }

  private async initializeS3Client() {
    if (this.s3Client) {
      return;
    }

    // Use the same configuration function as the working portfolio system
    const config = await getDefaultStorageConfig();
    
    if (!config) {
      throw new Error('No storage configuration found. Please configure storage settings first.');
    }

    this.region = config.region;
    this.bucketName = config.bucketName;

    // Create S3 client with the same configuration as portfolio system
    this.s3Client = new S3Client({
      region: config.region,
      endpoint: config.endpoint,
      credentials: {
        accessKeyId: config.accessKey || '',
        secretAccessKey: config.secretKey || '',
      },
      forcePathStyle: true, // Required for S3-compatible storage like Linode
    });

    console.log(`[S3BackupService] Initialized with config: ${config.id}, bucket: ${config.bucketName}, region: ${config.region}`);
  }

  private async getS3Client(): Promise<S3Client> {
    await this.initializeS3Client();
    if (!this.s3Client) {
      throw new Error('S3 client not initialized');
    }
    return this.s3Client;
  }

  private async getBucketName(): Promise<string> {
    await this.initializeS3Client();
    if (!this.bucketName) {
      throw new Error('Bucket name not configured');
    }
    return this.bucketName;
  }

  async createBackup(description?: string): Promise<BackupMetadata> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `backup_${timestamp}.sql`;
    const tempPath = join('/tmp', filename);
    
    try {
      // Create database dump
      const databaseUrl = process.env.DATABASE_URL;
      if (!databaseUrl) {
        throw new Error('Database URL not configured');
      }

      console.log('Creating database dump...');
      await execAsync(`pg_dump "${databaseUrl}" > "${tempPath}"`);

      // Read the dump file
      const fileBuffer = await readFile(tempPath);
      
      // Get S3 client and bucket name
      const s3Client = await this.getS3Client();
      const bucketName = await this.getBucketName();
      
      // Upload to S3
      const key = `backups/${filename}`;
      const uploadCommand = new PutObjectCommand({
        Bucket: bucketName,
        Key: key,
        Body: fileBuffer,
        ContentType: 'application/sql',
        Metadata: {
          description: description || 'Database backup',
          createdAt: new Date().toISOString(),
          type: 'manual',
        },
      });

      await s3Client.send(uploadCommand);

      // Clean up temp file
      await unlink(tempPath);

      const backup: BackupMetadata = {
        id: key,
        filename,
        description: description || 'Database backup',
        size: fileBuffer.length,
        createdAt: new Date().toISOString(),
        type: 'manual',
        status: 'completed',
      };

      console.log(`Backup created successfully: ${filename}`);
      return backup;

    } catch (error) {
      // Clean up temp file if it exists
      try {
        await unlink(tempPath);
      } catch {}
      
      console.error('Error creating backup:', error);
      throw error;
    }
  }

  async listBackups(): Promise<BackupMetadata[]> {
    try {
      const s3Client = await this.getS3Client();
      const bucketName = await this.getBucketName();
      
      const command = new ListObjectsV2Command({
        Bucket: bucketName,
        Prefix: 'backups/',
      });

      const response = await s3Client.send(command);
      const backups: BackupMetadata[] = [];

      if (response.Contents) {
        for (const object of response.Contents) {
          if (object.Key && object.Size && object.LastModified) {
            backups.push({
              id: object.Key,
              filename: object.Key.replace('backups/', ''),
              description: 'Database backup',
              size: object.Size,
              createdAt: object.LastModified.toISOString(),
              type: 'manual',
              status: 'completed',
            });
          }
        }
      }

      // Sort by creation date (newest first)
      return backups.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

    } catch (error) {
      console.error('Error listing backups:', error);
      throw error;
    }
  }

  async downloadBackup(backupId: string): Promise<{ url: string; filename: string }> {
    try {
      const filename = backupId.replace('backups/', '');
      
      const s3Client = await this.getS3Client();
      const bucketName = await this.getBucketName();
      
      // Generate presigned URL for download (valid for 1 hour)
      const command = new GetObjectCommand({
        Bucket: bucketName,
        Key: backupId,
      });

      const url = await getSignedUrl(s3Client, command, { expiresIn: 3600 });

      return { url, filename };

    } catch (error) {
      console.error('Error generating download URL:', error);
      throw error;
    }
  }

  async deleteBackup(backupId: string): Promise<void> {
    try {
      const s3Client = await this.getS3Client();
      const bucketName = await this.getBucketName();
      
      const command = new DeleteObjectCommand({
        Bucket: bucketName,
        Key: backupId,
      });

      await s3Client.send(command);
      console.log(`Backup deleted: ${backupId}`);

    } catch (error) {
      console.error('Error deleting backup:', error);
      throw error;
    }
  }

  async restoreBackup(backupId: string): Promise<void> {
    const tempPath = join('/tmp', `restore_${Date.now()}.sql`);
    
    try {
      const s3Client = await this.getS3Client();
      const bucketName = await this.getBucketName();
      
      // Download backup from S3
      const command = new GetObjectCommand({
        Bucket: bucketName,
        Key: backupId,
      });

      const response = await s3Client.send(command);
      
      if (!response.Body) {
        throw new Error('Backup file not found');
      }

      // Save to temp file
      const bodyContents = await response.Body.transformToByteArray();
      await writeFile(tempPath, bodyContents);

      // Verify downloaded file is not empty
      const stats = await stat(tempPath);
      if (stats.size === 0) {
        throw new Error('Downloaded backup file is empty');
      }

      // Restore database
      const databaseUrl = process.env.DATABASE_URL;
      if (!databaseUrl) {
        throw new Error('Database URL not configured');
      }

      console.log('Restoring database from backup...');
      console.log(`Downloaded backup size: ${stats.size} bytes`);
      
      // Use pg_restore for custom format backups, psql for SQL dumps
      let restoreCommand: string;
      if (backupId.endsWith('.sql')) {
        // Plain SQL dump - use psql
        restoreCommand = `psql "${databaseUrl}" < "${tempPath}"`;
      } else {
        // Custom format dump - use pg_restore with proper error handling
        restoreCommand = `pg_restore -d "${databaseUrl}" --clean --if-exists --no-owner --no-privileges --verbose --single-transaction --exit-on-error "${tempPath}"`;
      }
      
      console.log(`Executing restore command: ${restoreCommand.replace(databaseUrl, '[DATABASE_URL]')}`);
      
      const { stdout, stderr } = await execAsync(restoreCommand);
      
      // Log outputs for debugging
      if (stdout) {
        console.log(`Restore STDOUT: ${stdout}`);
      }
      if (stderr) {
        console.log(`Restore STDERR: ${stderr}`);
        
        // Check for critical errors in stderr (improved filtering)
        const errorLines = stderr.split('\n').filter(line => line.includes('ERROR:'));
        const criticalErrors = errorLines.filter(line => 
          !line.includes('ERROR: schema') && 
          !line.includes('ERROR: role') &&
          !line.includes('ERROR: permission denied for schema') &&
          !line.includes('ERROR: role "postgres" does not exist') &&
          !line.includes('ERROR: relation ') && // Tables/relations already exist
          !line.includes('ERROR: constraint ') && // Constraints already exist  
          !line.includes('ERROR: index ') && // Indexes already exist
          !line.includes('ERROR: type ') && // Types already exist
          !line.includes('ERROR: function ') && // Functions already exist
          !line.includes('ERROR: sequence ') && // Sequences already exist
          !line.includes('already exists') && // Generic "already exists" errors
          !line.includes('does not exist') && // Missing objects (normal in clean restores)
          !line.includes('duplicate key value violates unique constraint') && // Duplicate data (normal)
          !line.includes('multiple primary keys for table') && // Multiple PKs (normal)
          !line.includes('for relation') && // Generic relation conflicts
          line.trim() !== ''
        );
        
        if (criticalErrors.length > 0) {
          console.error('Critical errors detected:', criticalErrors);
          throw new Error(`Restore failed with critical errors: ${criticalErrors.join('; ')}`);
        } else {
          console.log('✅ Only non-critical warnings found (tables already exist - this is normal)');
        }
      }

      // Clean up temp file
      await unlink(tempPath);

      console.log(`Database restored from backup: ${backupId}`);

    } catch (error) {
      // Clean up temp file if it exists
      try {
        await unlink(tempPath);
      } catch {}
      
      console.error('Error restoring backup:', error);
      
      // Enhanced error logging
      if (error instanceof Error) {
        console.error('Restore error details:', {
          message: error.message,
          backupId,
          tempPath
        });
      }
      
      throw error;
    }
  }

  // Test S3 connection
  async testConnection(): Promise<boolean> {
    try {
      const s3Client = await this.getS3Client();
      const bucketName = await this.getBucketName();
      
      const command = new ListObjectsV2Command({
        Bucket: bucketName,
        MaxKeys: 1,
      });

      await s3Client.send(command);
      return true;

    } catch (error) {
      console.error('S3 connection test failed:', error);
      return false;
    }
  }
}

export const s3BackupService = new S3BackupService();
export default s3BackupService; 