/**
 * Chunked Upload Service with Resume Capability
 * Handles large file uploads by breaking them into smaller chunks
 */

import { detectConnectionSpeed, getUploadStrategy, ConnectionInfo } from '../utils/connectionUtils';
import { smartCompress } from '../utils/imageCompressionUtils';

export interface ChunkInfo {
  chunkNumber: number;
  start: number;
  end: number;
  size: number;
  hash?: string;
}

export interface UploadProgress {
  uploadId: string;
  fileName: string;
  totalSize: number;
  uploadedSize: number;
  percentage: number;
  chunksCompleted: number;
  totalChunks: number;
  currentChunk?: number;
  stage: 'preparing' | 'compressing' | 'uploading' | 'finalizing' | 'complete' | 'error';
  error?: string;
  estimatedTimeRemaining?: number | undefined;
  uploadSpeed?: number | undefined; // bytes per second
}

export interface UploadSession {
  uploadId: string;
  fileName: string;
  fileSize: number;
  totalChunks: number;
  chunkSize: number;
  completedChunks: Set<number>;
  failedChunks: Set<number>;
  connectionInfo: ConnectionInfo;
  startTime: number;
  lastProgressTime: number;
  uploadedBytes: number;
}

export class ChunkedUploadService {
  private activeSessions = new Map<string, UploadSession>();
  private readonly STORAGE_KEY = 'mocky_upload_sessions';
  private readonly MAX_CONCURRENT_CHUNKS = 3;
  private readonly MAX_RETRIES = 5;
  private sessionsLoaded = false;

  constructor() {
    // Don't load sessions in constructor to avoid SSR issues
    // Sessions will be loaded lazily when needed
  }

  /**
   * Ensure sessions are loaded (only in browser environment)
   */
  private ensureSessionsLoaded(): void {
    if (!this.sessionsLoaded && typeof window !== 'undefined') {
      this.loadSavedSessions();
      this.sessionsLoaded = true;
    }
  }

  /**
   * Start a chunked upload
   */
  async startUpload(
    file: File,
    onProgress: (progress: UploadProgress) => void,
    options: {
      endpoint?: string;
      metadata?: Record<string, any>;
      resumeUploadId?: string;
    } = {}
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    this.ensureSessionsLoaded();
    const uploadId = options.resumeUploadId || this.generateUploadId();
    
    try {
      // Detect connection and get strategy
      const connectionInfo = await detectConnectionSpeed();
      const strategy = getUploadStrategy(file.size, connectionInfo);
      
      onProgress({
        uploadId,
        fileName: file.name,
        totalSize: file.size,
        uploadedSize: 0,
        percentage: 0,
        chunksCompleted: 0,
        totalChunks: 0,
        stage: 'preparing'
      });

      // For small files or good connections, use direct upload instead of chunked
      if (strategy.useDirectUpload) {
        console.log(`[ChunkedUpload] Using direct upload for small file: ${file.name} (${Math.round(file.size/1024/1024)}MB)`);
        return await this.performDirectUpload(file, onProgress, {
          endpoint: options.endpoint || '/api/upload',
          ...(options.metadata && { metadata: options.metadata }),
          timeout: strategy.timeout
        });
      }

      console.log(`[ChunkedUpload] Using chunked upload for file: ${file.name} (${Math.round(file.size/1024/1024)}MB)`);

      // Compress file if needed
      let processedFile = file;
      if (strategy.shouldCompress) {
        onProgress({
          uploadId,
          fileName: file.name,
          totalSize: file.size,
          uploadedSize: 0,
          percentage: 0,
          chunksCompleted: 0,
          totalChunks: 0,
          stage: 'compressing'
        });

        processedFile = await smartCompress(
          file,
          connectionInfo.speed,
          (stage, progress) => {
            onProgress({
              uploadId,
              fileName: file.name,
              totalSize: file.size,
              uploadedSize: 0,
              percentage: Math.round(progress * 0.2), // Compression is 20% of total progress
              chunksCompleted: 0,
              totalChunks: 0,
              stage: 'compressing'
            });
          }
        );
      }

      // Create or resume upload session
      const session = this.createOrResumeSession(
        uploadId,
        processedFile,
        connectionInfo,
        strategy.chunkSize
      );

      // Start chunked upload
      const result = await this.performChunkedUpload(
        session,
        processedFile,
        onProgress,
        {
          endpoint: options.endpoint || '/api/upload',
          ...(options.metadata && { metadata: options.metadata }),
          timeout: strategy.timeout,
          maxRetries: strategy.maxRetries
        }
      );

      if (result.success) {
        this.cleanupSession(uploadId);
      }

      return result;

    } catch (error) {
      console.error('[ChunkedUpload] Upload failed:', error);
      onProgress({
        uploadId,
        fileName: file.name,
        totalSize: file.size,
        uploadedSize: 0,
        percentage: 0,
        chunksCompleted: 0,
        totalChunks: 0,
        stage: 'error',
        error: error instanceof Error ? error.message : 'Upload failed'
      });

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Upload failed'
      };
    }
  }

  /**
   * Perform direct upload for small files
   */
  private async performDirectUpload(
    file: File,
    onProgress: (progress: UploadProgress) => void,
    options: {
      endpoint: string;
      metadata?: Record<string, any>;
      timeout: number;
    }
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    const uploadId = this.generateUploadId();
    
    try {
      onProgress({
        uploadId,
        fileName: file.name,
        totalSize: file.size,
        uploadedSize: 0,
        percentage: 0,
        chunksCompleted: 0,
        totalChunks: 1,
        stage: 'uploading'
      });

      // Create form data for direct upload
      const formData = new FormData();
      formData.append('file', file);
      if (options.metadata) {
        for (const [key, value] of Object.entries(options.metadata)) {
          formData.append(key, value);
        }
      }

      // Upload directly to the main upload endpoint
      const response = await fetch(options.endpoint, {
        method: 'POST',
        body: formData,
        signal: AbortSignal.timeout(options.timeout)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      onProgress({
        uploadId,
        fileName: file.name,
        totalSize: file.size,
        uploadedSize: file.size,
        percentage: 100,
        chunksCompleted: 1,
        totalChunks: 1,
        stage: 'complete'
      });

      // Extract the first successful upload result
      if (result.results && result.results.length > 0) {
        const firstResult = result.results[0];
        if (firstResult.success) {
          return {
            success: true,
            data: {
              url: firstResult.url,
              fileName: firstResult.filename,
              fileSize: firstResult.size
            }
          };
        }
      }

      throw new Error(result.error || 'Direct upload failed');

    } catch (error) {
      console.error('[ChunkedUpload] Direct upload failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Direct upload failed'
      };
    }
  }

  /**
   * Resume a paused upload
   */
  async resumeUpload(
    uploadId: string,
    file: File,
    onProgress: (progress: UploadProgress) => void
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    this.ensureSessionsLoaded();
    const session = this.activeSessions.get(uploadId);
    if (!session) {
      return { success: false, error: 'Upload session not found' };
    }

    console.log(`[ChunkedUpload] Resuming upload ${uploadId}, ${session.completedChunks.size}/${session.totalChunks} chunks completed`);

    return this.startUpload(file, onProgress, { resumeUploadId: uploadId });
  }

  /**
   * Cancel an active upload
   */
  cancelUpload(uploadId: string): void {
    this.ensureSessionsLoaded();
    const session = this.activeSessions.get(uploadId);
    if (session) {
      console.log(`[ChunkedUpload] Cancelling upload ${uploadId}`);
      this.cleanupSession(uploadId);
    }
  }

  /**
   * Get list of resumable uploads
   */
  getResumableUploads(): Array<{
    uploadId: string;
    fileName: string;
    fileSize: number;
    progress: number;
    completedChunks: number;
    totalChunks: number;
  }> {
    this.ensureSessionsLoaded();
    return Array.from(this.activeSessions.values()).map(session => ({
      uploadId: session.uploadId,
      fileName: session.fileName,
      fileSize: session.fileSize,
      progress: (session.completedChunks.size / session.totalChunks) * 100,
      completedChunks: session.completedChunks.size,
      totalChunks: session.totalChunks
    }));
  }

  private createOrResumeSession(
    uploadId: string,
    file: File,
    connectionInfo: ConnectionInfo,
    chunkSize: number
  ): UploadSession {
    let session = this.activeSessions.get(uploadId);
    
    if (!session) {
      const totalChunks = Math.ceil(file.size / chunkSize);
      
      session = {
        uploadId,
        fileName: file.name,
        fileSize: file.size,
        totalChunks,
        chunkSize,
        completedChunks: new Set(),
        failedChunks: new Set(),
        connectionInfo,
        startTime: Date.now(),
        lastProgressTime: Date.now(),
        uploadedBytes: 0
      };
      
      this.activeSessions.set(uploadId, session);
      console.log(`[ChunkedUpload] Created new session ${uploadId}: ${totalChunks} chunks of ${Math.round(chunkSize/1024/1024)}MB`);
    } else {
      console.log(`[ChunkedUpload] Resuming session ${uploadId}: ${session.completedChunks.size}/${session.totalChunks} chunks completed`);
    }
    
    this.saveSessions();
    return session;
  }

  private async performChunkedUpload(
    session: UploadSession,
    file: File,
    onProgress: (progress: UploadProgress) => void,
    options: {
      endpoint: string;
      metadata?: Record<string, any>;
      timeout: number;
      maxRetries: number;
    }
  ): Promise<{ success: boolean; data?: any; error?: string }> {
    const { uploadId, totalChunks, chunkSize } = session;
    
    onProgress(this.createProgressUpdate(session, 'uploading'));

    // Initialize upload session on server
    try {
      await this.initializeServerSession(uploadId, file, options);
    } catch (error) {
      return { success: false, error: `Failed to initialize upload: ${error}` };
    }

    // Upload chunks with concurrency control
    const chunkPromises: Promise<void>[] = [];
    const semaphore = new Semaphore(this.MAX_CONCURRENT_CHUNKS);

    for (let chunkNumber = 0; chunkNumber < totalChunks; chunkNumber++) {
      if (session.completedChunks.has(chunkNumber)) {
        continue; // Skip already completed chunks
      }

      const chunkPromise = semaphore.acquire().then(async (release) => {
        try {
          await this.uploadChunk(session, file, chunkNumber, options, onProgress);
        } finally {
          release();
        }
      });

      chunkPromises.push(chunkPromise);
    }

    // Wait for all chunks to complete
    try {
      await Promise.all(chunkPromises);
    } catch (error) {
      return { success: false, error: `Chunk upload failed: ${error}` };
    }

    // Finalize upload on server
    onProgress(this.createProgressUpdate(session, 'finalizing'));
    
    try {
      const result = await this.finalizeServerUpload(uploadId, options);
      
      onProgress(this.createProgressUpdate(session, 'complete'));
      
      return { success: true, data: result };
    } catch (error) {
      return { success: false, error: `Failed to finalize upload: ${error}` };
    }
  }

  private async uploadChunk(
    session: UploadSession,
    file: File,
    chunkNumber: number,
    options: { endpoint: string; timeout: number; maxRetries: number },
    onProgress: (progress: UploadProgress) => void
  ): Promise<void> {
    const { chunkSize } = session;
    const start = chunkNumber * chunkSize;
    const end = Math.min(start + chunkSize, file.size);
    const chunkBlob = file.slice(start, end);

    let retries = 0;
    while (retries < options.maxRetries) {
      try {
        console.log(`[ChunkedUpload] Uploading chunk ${chunkNumber + 1}/${session.totalChunks} (${Math.round((end - start)/1024)}KB)`);

        const formData = new FormData();
        formData.append('uploadId', session.uploadId);
        formData.append('chunkNumber', chunkNumber.toString());
        formData.append('totalChunks', session.totalChunks.toString());
        formData.append('chunk', chunkBlob, `${session.fileName}.chunk.${chunkNumber}`);

        const response = await fetch(`${options.endpoint}/chunk`, {
          method: 'POST',
          body: formData,
          signal: AbortSignal.timeout(options.timeout)
        });

        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        // Mark chunk as completed
        session.completedChunks.add(chunkNumber);
        session.failedChunks.delete(chunkNumber);
        session.uploadedBytes += (end - start);
        
        // Update progress
        const now = Date.now();
        const timeDiff = (now - session.lastProgressTime) / 1000;
        const uploadSpeed = timeDiff > 0 ? (end - start) / timeDiff : 0;
        session.lastProgressTime = now;

        onProgress(this.createProgressUpdate(session, 'uploading', uploadSpeed));
        
        this.saveSessions();
        return;

      } catch (error) {
        retries++;
        session.failedChunks.add(chunkNumber);
        
        console.warn(`[ChunkedUpload] Chunk ${chunkNumber} failed (attempt ${retries}/${options.maxRetries}):`, error);
        
        if (retries >= options.maxRetries) {
          throw new Error(`Chunk ${chunkNumber} failed after ${options.maxRetries} attempts: ${error}`);
        }

        // Exponential backoff
        const delay = Math.min(1000 * Math.pow(2, retries - 1), 10000);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  private async initializeServerSession(
    uploadId: string,
    file: File,
    options: { endpoint: string; metadata?: Record<string, any> }
  ): Promise<void> {
    const response = await fetch(`${options.endpoint}/init`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        uploadId,
        fileName: file.name,
        fileSize: file.size,
        fileType: file.type,
        metadata: options.metadata
      })
    });

    if (!response.ok) {
      throw new Error(`Failed to initialize upload: ${response.statusText}`);
    }
  }

  private async finalizeServerUpload(
    uploadId: string,
    options: { endpoint: string }
  ): Promise<any> {
    const response = await fetch(`${options.endpoint}/finalize`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ uploadId })
    });

    if (!response.ok) {
      throw new Error(`Failed to finalize upload: ${response.statusText}`);
    }

    return response.json();
  }

  private createProgressUpdate(
    session: UploadSession,
    stage: UploadProgress['stage'],
    uploadSpeed?: number
  ): UploadProgress {
    const percentage = (session.completedChunks.size / session.totalChunks) * 100;
    const elapsedTime = (Date.now() - session.startTime) / 1000;
    const estimatedTimeRemaining = uploadSpeed && uploadSpeed > 0 
      ? (session.fileSize - session.uploadedBytes) / uploadSpeed 
      : undefined;

    return {
      uploadId: session.uploadId,
      fileName: session.fileName,
      totalSize: session.fileSize,
      uploadedSize: session.uploadedBytes,
      percentage: Math.round(percentage),
      chunksCompleted: session.completedChunks.size,
      totalChunks: session.totalChunks,
      stage,
      uploadSpeed,
      estimatedTimeRemaining
    };
  }

  private generateUploadId(): string {
    return `upload_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private saveSessions(): void {
    try {
      // Only save to localStorage in browser environment
      if (typeof window !== 'undefined' && typeof localStorage !== 'undefined') {
        const sessionsData = Array.from(this.activeSessions.entries()).map(([id, session]) => [
          id,
          {
            ...session,
            completedChunks: Array.from(session.completedChunks),
            failedChunks: Array.from(session.failedChunks)
          }
        ]);
        
        localStorage.setItem(this.STORAGE_KEY, JSON.stringify(sessionsData));
      }
    } catch (error) {
      console.warn('[ChunkedUpload] Failed to save sessions to localStorage:', error);
    }
  }

  private loadSavedSessions(): void {
    try {
      // Only load from localStorage in browser environment
      if (typeof window !== 'undefined' && typeof localStorage !== 'undefined') {
        const saved = localStorage.getItem(this.STORAGE_KEY);
        if (saved) {
          const sessionsData = JSON.parse(saved);
          
          for (const [id, sessionData] of sessionsData) {
            this.activeSessions.set(id, {
              ...sessionData,
              completedChunks: new Set(sessionData.completedChunks),
              failedChunks: new Set(sessionData.failedChunks)
            });
          }
          
          console.log(`[ChunkedUpload] Loaded ${this.activeSessions.size} saved sessions`);
        }
      }
    } catch (error) {
      console.warn('[ChunkedUpload] Failed to load saved sessions:', error);
    }
  }

  private cleanupSession(uploadId: string): void {
    this.activeSessions.delete(uploadId);
    this.saveSessions();
  }
}

/**
 * Simple semaphore for controlling concurrency
 */
class Semaphore {
  private permits: number;
  private waiting: Array<() => void> = [];

  constructor(permits: number) {
    this.permits = permits;
  }

  async acquire(): Promise<() => void> {
    return new Promise((resolve) => {
      if (this.permits > 0) {
        this.permits--;
        resolve(() => this.release());
      } else {
        this.waiting.push(() => {
          this.permits--;
          resolve(() => this.release());
        });
      }
    });
  }

  private release(): void {
    this.permits++;
    if (this.waiting.length > 0) {
      const next = this.waiting.shift()!;
      next();
    }
  }
}

// Export singleton instance
export const chunkedUploadService = new ChunkedUploadService(); 