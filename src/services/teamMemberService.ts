import prisma from '@/lib/prisma';
import {
  TeamMember,
  TeamMemberDB,
  TeamMemberNotFoundError,
  TeamMemberValidationError
} from '@/types/team';
import { validateTeamMemberData, ValidatedTeamMemberData } from '@/utils/validation';
import { ErrorTracker } from '@/services/errorLogger';

// Cache for team members
interface CacheEntry {
  data: TeamMember[];
  timestamp: number;
  ttl: number;
}

class TeamMemberCache {
  private cache = new Map<string, CacheEntry>();
  private readonly DEFAULT_TTL = 5 * 60 * 1000; // 5 minutes

  set(key: string, data: TeamMember[], ttl: number = this.DEFAULT_TTL): void {
    this.cache.set(key, {
      data: [...data], // Create a copy to prevent mutations
      timestamp: Date.now(),
      ttl
    });
  }

  get(key: string): TeamMember[] | null {
    const entry = this.cache.get(key);
    if (!entry) return null;

    const now = Date.now();
    if (now - entry.timestamp > entry.ttl) {
      this.cache.delete(key);
      return null;
    }

    return [...entry.data]; // Return a copy
  }

  invalidate(pattern?: string): void {
    if (pattern) {
      for (const key of this.cache.keys()) {
        if (key.includes(pattern)) {
          this.cache.delete(key);
        }
      }
    } else {
      this.cache.clear();
    }
  }

  cleanup(): void {
    const now = Date.now();
    for (const [key, entry] of this.cache.entries()) {
      if (now - entry.timestamp > entry.ttl) {
        this.cache.delete(key);
      }
    }
  }
}

const cache = new TeamMemberCache();

// Cleanup cache every 10 minutes
setInterval(() => cache.cleanup(), 10 * 60 * 1000);

/**
 * Formats a database team member to the standard interface
 */
function formatTeamMember(dbTeamMember: TeamMemberDB): TeamMember {
  return {
    id: dbTeamMember.id,
    name: dbTeamMember.name,
    role: dbTeamMember.role,
    bio: dbTeamMember.bio,
    imageSrc: dbTeamMember.imageSrc,
    order: dbTeamMember.order,
    linkedinUrl: dbTeamMember.linkedinUrl,
    twitterUrl: dbTeamMember.twitterUrl,
    githubUrl: dbTeamMember.githubUrl,
    emailAddress: dbTeamMember.emailAddress,
    createdAt: dbTeamMember.createdAt.toISOString(),
    updatedAt: dbTeamMember.updatedAt.toISOString()
  };
}

/**
 * Get all team members with caching
 */
export async function getAllTeamMembers(useCache: boolean = true): Promise<TeamMember[]> {
  const cacheKey = 'all_team_members';

  try {
    // Try to get from cache first
    if (useCache) {
      const cached = cache.get(cacheKey);
      if (cached) {
        console.log('Returning cached team members');
        return cached;
      }
    }

    console.log('Fetching all team members from database');

    const teamMembers = await prisma.$transaction(async (tx) => {
      return await tx.teamMember.findMany({
        orderBy: {
          order: 'asc'
        }
      });
    });

    console.log(`Found ${teamMembers.length} team members in database`);

    const formattedMembers = teamMembers.map(formatTeamMember);

    // Cache the results
    if (useCache) {
      cache.set(cacheKey, formattedMembers);
    }

    return formattedMembers;
  } catch (error) {
    ErrorTracker.trackError(
      error as Error,
      'getAllTeamMembers',
      { useCache, cacheKey }
    );
    console.error('Error getting all team members:', error);

    // Try to return cached data as fallback
    if (useCache) {
      const cached = cache.get(cacheKey);
      if (cached) {
        console.log('Returning stale cached data due to database error');
        return cached;
      }
    }

    return [];
  }
}

/**
 * Get a team member by ID with caching
 */
export async function getTeamMemberById(id: string, useCache: boolean = true): Promise<TeamMember | null> {
  if (!id || typeof id !== 'string') {
    throw new TeamMemberValidationError('Invalid team member ID');
  }

  const cacheKey = `team_member_${id}`;

  try {
    // Try to get from cache first
    if (useCache) {
      const allCached = cache.get('all_team_members');
      if (allCached) {
        const found = allCached.find(member => member.id === id);
        if (found) {
          console.log(`Returning cached team member: ${id}`);
          return found;
        }
      }
    }

    console.log(`Fetching team member with ID: ${id}`);

    const teamMember = await prisma.$transaction(async (tx) => {
      return await tx.teamMember.findUnique({
        where: { id }
      });
    });

    if (!teamMember) {
      console.log(`No team member found with ID: ${id}`);
      throw new TeamMemberNotFoundError(id);
    }

    const formatted = formatTeamMember(teamMember);
    return formatted;
  } catch (error) {
    if (error instanceof TeamMemberNotFoundError) {
      throw error;
    }

    ErrorTracker.trackError(
      error as Error,
      'getTeamMemberById',
      { id, useCache, cacheKey }
    );
    console.error(`Error getting team member with ID ${id}:`, error);
    throw new Error(`Failed to fetch team member: ${(error as Error).message}`);
  }
}

/**
 * Create a new team member with validation and transaction support
 */
export async function createTeamMember(
  data: ValidatedTeamMemberData & { imageSrc: string }
): Promise<TeamMember> {
  try {
    console.log('Creating new team member:', data.name);

    // Validate the data
    const validatedData = validateTeamMemberData(data);

    // Check for duplicate names (optional business rule)
    const existingMember = await prisma.teamMember.findFirst({
      where: {
        name: {
          equals: validatedData.name,
          mode: 'insensitive'
        }
      }
    });

    if (existingMember) {
      throw new TeamMemberValidationError(
        `A team member with the name "${validatedData.name}" already exists`,
        'name',
        'DUPLICATE_NAME'
      );
    }

    // Create the team member in a transaction
    const newTeamMember = await prisma.$transaction(async (tx) => {
      return await tx.teamMember.create({
        data: {
          name: validatedData.name,
          role: validatedData.role,
          bio: validatedData.bio,
          imageSrc: data.imageSrc,
          order: validatedData.order,
          linkedinUrl: validatedData.linkedinUrl,
          twitterUrl: validatedData.twitterUrl,
          githubUrl: validatedData.githubUrl,
          emailAddress: validatedData.emailAddress
        }
      });
    });

    console.log(`Created new team member with ID: ${newTeamMember.id}`);

    // Invalidate cache
    cache.invalidate();

    const formatted = formatTeamMember(newTeamMember);
    return formatted;
  } catch (error) {
    if (error instanceof TeamMemberValidationError) {
      throw error;
    }

    ErrorTracker.trackError(
      error as Error,
      'createTeamMember',
      { name: data.name, role: data.role }
    );
    console.error('Error creating team member:', error);
    throw new Error(`Failed to create team member: ${(error as Error).message}`);
  }
}

/**
 * Update a team member with validation and transaction support
 */
export async function updateTeamMember(
  id: string,
  data: Partial<ValidatedTeamMemberData> & { imageSrc?: string }
): Promise<TeamMember> {
  if (!id || typeof id !== 'string') {
    throw new TeamMemberValidationError('Invalid team member ID');
  }

  try {
    console.log(`Updating team member with ID: ${id}`);

    // First check if the team member exists
    const existingMember = await prisma.teamMember.findUnique({
      where: { id }
    });

    if (!existingMember) {
      throw new TeamMemberNotFoundError(id);
    }

    // Validate the update data
    const updateData: any = {};

    if (data.name !== undefined) {
      const validatedData = validateTeamMemberData({
        ...existingMember,
        ...data
      });

      // Check for duplicate names (excluding current member)
      if (data.name && data.name !== existingMember.name) {
        const duplicateMember = await prisma.teamMember.findFirst({
          where: {
            name: {
              equals: data.name,
              mode: 'insensitive'
            },
            id: {
              not: id
            }
          }
        });

        if (duplicateMember) {
          throw new TeamMemberValidationError(
            `A team member with the name "${data.name}" already exists`,
            'name',
            'DUPLICATE_NAME'
          );
        }
      }

      Object.assign(updateData, validatedData);
    } else {
      // If no name change, just validate the provided fields
      if (data.role !== undefined) updateData.role = data.role;
      if (data.bio !== undefined) updateData.bio = data.bio;
      if (data.order !== undefined) updateData.order = data.order;
      if (data.linkedinUrl !== undefined) updateData.linkedinUrl = data.linkedinUrl;
      if (data.twitterUrl !== undefined) updateData.twitterUrl = data.twitterUrl;
      if (data.githubUrl !== undefined) updateData.githubUrl = data.githubUrl;
      if (data.emailAddress !== undefined) updateData.emailAddress = data.emailAddress;
    }

    if (data.imageSrc !== undefined) {
      updateData.imageSrc = data.imageSrc;
    }

    // Update the team member in a transaction
    const updatedTeamMember = await prisma.$transaction(async (tx) => {
      return await tx.teamMember.update({
        where: { id },
        data: updateData
      });
    });

    console.log(`Updated team member with ID: ${id}`);

    // Invalidate cache
    cache.invalidate();

    const formatted = formatTeamMember(updatedTeamMember);
    return formatted;
  } catch (error) {
    if (error instanceof TeamMemberValidationError || error instanceof TeamMemberNotFoundError) {
      throw error;
    }

    ErrorTracker.trackError(
      error as Error,
      'updateTeamMember',
      { id, updateFields: Object.keys(data) }
    );
    console.error(`Error updating team member with ID ${id}:`, error);
    throw new Error(`Failed to update team member: ${(error as Error).message}`);
  }
}

/**
 * Delete a team member with transaction support
 */
export async function deleteTeamMember(id: string): Promise<boolean> {
  if (!id || typeof id !== 'string') {
    throw new TeamMemberValidationError('Invalid team member ID');
  }

  try {
    console.log(`Deleting team member with ID: ${id}`);

    // First check if the team member exists
    const existingMember = await prisma.teamMember.findUnique({
      where: { id }
    });

    if (!existingMember) {
      throw new TeamMemberNotFoundError(id);
    }

    // Delete the team member in a transaction
    await prisma.$transaction(async (tx) => {
      await tx.teamMember.delete({
        where: { id }
      });
    });

    console.log(`Deleted team member with ID: ${id}`);

    // Invalidate cache
    cache.invalidate();

    return true;
  } catch (error) {
    if (error instanceof TeamMemberValidationError || error instanceof TeamMemberNotFoundError) {
      throw error;
    }

    ErrorTracker.trackError(
      error as Error,
      'deleteTeamMember',
      { id }
    );
    console.error(`Error deleting team member with ID ${id}:`, error);
    throw new Error(`Failed to delete team member: ${(error as Error).message}`);
  }
}

/**
 * Get team members count
 */
export async function getTeamMembersCount(): Promise<number> {
  try {
    const count = await prisma.teamMember.count();
    return count;
  } catch (error) {
    ErrorTracker.trackError(
      error as Error,
      'getTeamMembersCount'
    );
    console.error('Error getting team members count:', error);
    return 0;
  }
}

/**
 * Reorder team members
 */
export async function reorderTeamMembers(memberOrders: { id: string; order: number }[]): Promise<boolean> {
  try {
    console.log('Reordering team members:', memberOrders);

    await prisma.$transaction(async (tx) => {
      for (const { id, order } of memberOrders) {
        await tx.teamMember.update({
          where: { id },
          data: { order }
        });
      }
    });

    console.log('Successfully reordered team members');

    // Invalidate cache
    cache.invalidate();

    return true;
  } catch (error) {
    ErrorTracker.trackError(
      error as Error,
      'reorderTeamMembers',
      { memberOrders }
    );
    console.error('Error reordering team members:', error);
    throw new Error(`Failed to reorder team members: ${(error as Error).message}`);
  }
}

/**
 * Search team members by name or role
 */
export async function searchTeamMembers(query: string): Promise<TeamMember[]> {
  if (!query || typeof query !== 'string') {
    return getAllTeamMembers();
  }

  try {
    console.log(`Searching team members with query: ${query}`);

    const teamMembers = await prisma.teamMember.findMany({
      where: {
        OR: [
          {
            name: {
              contains: query,
              mode: 'insensitive'
            }
          },
          {
            role: {
              contains: query,
              mode: 'insensitive'
            }
          }
        ]
      },
      orderBy: {
        order: 'asc'
      }
    });

    console.log(`Found ${teamMembers.length} team members matching query`);
    return teamMembers.map(formatTeamMember);
  } catch (error) {
    ErrorTracker.trackError(
      error as Error,
      'searchTeamMembers',
      { query }
    );
    console.error('Error searching team members:', error);
    return [];
  }
}

/**
 * Clear all caches
 */
export function clearCache(): void {
  cache.invalidate();
  console.log('Team member cache cleared');
}
