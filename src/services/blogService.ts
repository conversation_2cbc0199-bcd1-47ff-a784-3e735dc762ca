import prisma from '@/lib/prisma';
import { BlogPost } from '@/types/blog';

// Calculate reading time based on content
function calculateReadingTime(content: string): number {
  const wordsPerMinute = 200;
  const words = content.replace(/<[^>]*>/g, '').split(/\s+/).length;
  return Math.ceil(words / wordsPerMinute);
}

// Get all blog posts
export async function getAllBlogPosts(): Promise<BlogPost[]> {
  try {
    console.log('Fetching all blog posts from database');
    const posts = await prisma.blogPost.findMany({
      orderBy: {
        createdAt: 'desc'
      }
    });

    console.log(`Found ${posts.length} blog posts in database`);
    return posts.map(formatBlogPost);
  } catch (error) {
    console.error('Error getting all blog posts:', error);
    throw new Error('Failed to fetch blog posts from database');
  }
}

// Get published blog posts
export async function getPublishedBlogPosts(): Promise<BlogPost[]> {
  try {
    console.log('Fetching published blog posts from database');
    
    const posts = await prisma.blogPost.findMany({
      where: {
        status: 'published'
      },
      orderBy: {
        publishedAt: 'desc'
      }
    });

    console.log(`Found ${posts.length} published blog posts in database`);
    
    // If no posts are found, return a mock post for debugging
    if (posts.length === 0) {
      console.log('No posts found, returning a mock post for debugging');
      return [createMockPost()];
    }
    
    return posts.map(formatBlogPost);
  } catch (error) {
    console.error('Error getting published blog posts:', error);
    
    // In case of error, return a mock post for debugging
    console.log('Error occurred, returning a mock post for debugging');
    return [createMockPost()];
  }
}

// Get a blog post by slug
export async function getBlogPostBySlug(slug: string): Promise<BlogPost | null> {
  try {
    console.log(`Fetching blog post with slug: ${slug} from database`);
    const post = await prisma.blogPost.findUnique({
      where: {
        slug
      }
    });

    if (!post) {
      console.log(`No blog post found with slug: ${slug}`);
      return null;
    }

    // Increment view count
    await prisma.blogPost.update({
      where: { slug },
      data: { viewCount: { increment: 1 } }
    });

    console.log(`Found blog post: ${post.title}`);
    return formatBlogPost(post);
  } catch (error) {
    console.error(`Error getting blog post by slug ${slug}:`, error);
    throw new Error(`Failed to fetch blog post with slug: ${slug}`);
  }
}

// Get a blog post by ID
export async function getBlogPostById(id: string): Promise<BlogPost | null> {
  try {
    const postId = parseInt(id);
    const post = await prisma.blogPost.findUnique({
      where: {
        id: postId
      }
    });

    if (!post) {
      return null;
    }

    return formatBlogPost(post);
  } catch (error) {
    console.error(`Error getting blog post by ID ${id}:`, error);
    throw error;
  }
}

// Create a new blog post
export async function createBlogPost(post: Omit<BlogPost, 'id' | 'createdAt' | 'updatedAt'>): Promise<BlogPost> {
  try {
    // Always use current date if status is published
    const publishedAt = post.status === 'published' ? new Date() : null;
    const scheduledAt = post.status === 'scheduled' && post.scheduledDate ? new Date(post.scheduledDate) : null;
    const readingTime = calculateReadingTime(post.content);
    
    console.log(`Creating post with status: ${post.status}, publishedAt: ${publishedAt}`);

    const newPost = await prisma.blogPost.create({
      data: {
        title: post.title,
        slug: post.slug,
        content: post.content,
        excerpt: post.excerpt || '',
        featuredImage: post.featuredImage,
        author: post.author || 'Admin',
        category: post.category || '',
        tags: post.tags || [],
        status: post.status,
        readingTime,
        seoTitle: post.seoTitle,
        seoDescription: post.seoDescription,
        seoKeywords: post.seoKeywords || [],
        publishedAt,
        scheduledAt
      }
    });

    return formatBlogPost(newPost);
  } catch (error) {
    console.error('Error creating blog post:', error);
    throw error;
  }
}

// Update a blog post
export async function updateBlogPost(id: string, post: Partial<BlogPost> & { scheduledDate?: string }): Promise<BlogPost | null> {
  try {
    const postId = parseInt(id);

    // Check if the post exists
    const existingPost = await prisma.blogPost.findUnique({
      where: {
        id: postId
      }
    });

    if (!existingPost) {
      return null;
    }

    // Always set publishedAt to current date if status is being changed to published
    let publishedAt = undefined;
    if (post.status === 'published') {
      publishedAt = new Date();
      console.log(`Setting publishedAt to current date: ${publishedAt}`);
    }

    // Handle scheduled date
    let scheduledAt = undefined;
    if (post.status === 'scheduled' && post.scheduledDate) {
      scheduledAt = new Date(post.scheduledDate);
    }

    // Calculate reading time if content is being updated
    let readingTime = undefined;
    if (post.content) {
      readingTime = calculateReadingTime(post.content);
    }

    // Create an update data object with only the fields that are provided
    const updateData: any = {};
    if (post.title !== undefined) updateData.title = post.title;
    if (post.slug !== undefined) updateData.slug = post.slug;
    if (post.content !== undefined) updateData.content = post.content;
    if (post.excerpt !== undefined) updateData.excerpt = post.excerpt;
    if (post.featuredImage !== undefined) updateData.featuredImage = post.featuredImage;
    if (post.author !== undefined) updateData.author = post.author;
    if (post.category !== undefined) updateData.category = post.category;
    if (post.tags !== undefined) updateData.tags = post.tags;
    if (post.status !== undefined) updateData.status = post.status;
    if (post.seoTitle !== undefined) updateData.seoTitle = post.seoTitle;
    if (post.seoDescription !== undefined) updateData.seoDescription = post.seoDescription;
    if (post.seoKeywords !== undefined) updateData.seoKeywords = post.seoKeywords;
    if (publishedAt !== undefined) updateData.publishedAt = publishedAt;
    if (scheduledAt !== undefined) updateData.scheduledAt = scheduledAt;
    if (readingTime !== undefined) updateData.readingTime = readingTime;

    const updatedPost = await prisma.blogPost.update({
      where: {
        id: postId
      },
      data: updateData
    });

    return formatBlogPost(updatedPost);
  } catch (error) {
    console.error(`Error updating blog post ${id}:`, error);
    throw error;
  }
}

// Delete a blog post
export async function deleteBlogPost(id: string): Promise<boolean> {
  try {
    const postId = parseInt(id);

    await prisma.blogPost.delete({
      where: {
        id: postId
      }
    });

    return true;
  } catch (error) {
    console.error(`Error deleting blog post ${id}:`, error);
    return false;
  }
}

// Get blog posts by category
export async function getBlogPostsByCategory(category: string): Promise<BlogPost[]> {
  try {
    const posts = await prisma.blogPost.findMany({
      where: {
        category,
        status: 'published'
      },
      orderBy: {
        publishedAt: 'desc'
      }
    });

    return posts.map(formatBlogPost);
  } catch (error) {
    console.error(`Error getting blog posts by category ${category}:`, error);
    throw error;
  }
}

// Get blog posts by tag
export async function getBlogPostsByTag(tag: string): Promise<BlogPost[]> {
  try {
    const posts = await prisma.blogPost.findMany({
      where: {
        tags: {
          has: tag
        },
        status: 'published'
      },
      orderBy: {
        publishedAt: 'desc'
      }
    });

    return posts.map(formatBlogPost);
  } catch (error) {
    console.error(`Error getting blog posts by tag ${tag}:`, error);
    throw error;
  }
}

// Get popular blog posts (by view count)
export async function getPopularBlogPosts(limit = 5): Promise<BlogPost[]> {
  try {
    const posts = await prisma.blogPost.findMany({
      where: {
        status: 'published'
      },
      orderBy: {
        viewCount: 'desc'
      },
      take: limit
    });

    return posts.map(formatBlogPost);
  } catch (error) {
    console.error('Error getting popular blog posts:', error);
    throw error;
  }
}

// Get related blog posts
export async function getRelatedBlogPosts(postId: string, limit = 3): Promise<BlogPost[]> {
  try {
    const currentPost = await getBlogPostById(postId);
    if (!currentPost) return [];

    const posts = await prisma.blogPost.findMany({
      where: {
        AND: [
          { status: 'published' },
          { id: { not: parseInt(postId) } },
          {
            OR: [
              { category: currentPost.category },
              { tags: { hasSome: currentPost.tags || [] } }
            ]
          }
        ]
      },
      orderBy: {
        publishedAt: 'desc'
      },
      take: limit
    });

    return posts.map(formatBlogPost);
  } catch (error) {
    console.error(`Error getting related blog posts for ${postId}:`, error);
    throw error;
  }
}

// Helper function to format blog post data from the database
function formatBlogPost(post: any): BlogPost {
  // Get the current date to check against future dates
  const now = new Date();
  
  // Format dates, ensuring they aren't in the future
  const formatDate = (date: Date | null): string => {
    if (!date) return now.toISOString();
    
    // If date is in the future, use current date instead
    const dateToUse = date > now ? now : date;
    return dateToUse.toISOString();
  };
  
  return {
    id: post.id.toString(),
    title: post.title,
    slug: post.slug,
    content: post.content,
    excerpt: post.excerpt,
    featuredImage: post.featuredImage,
    author: post.author,
    category: post.category,
    tags: post.tags || [],
    status: post.status,
    readingTime: post.readingTime,
    viewCount: post.viewCount || 0,
    seoTitle: post.seoTitle,
    seoDescription: post.seoDescription,
    seoKeywords: post.seoKeywords || [],
    createdAt: formatDate(post.createdAt),
    updatedAt: formatDate(post.updatedAt),
    publishedAt: post.publishedAt ? formatDate(post.publishedAt) : null
  };
}

// Create a mock post for debugging
function createMockPost(): BlogPost {
  return {
    id: 'mock-1',
    title: 'Welcome to Our Blog',
    slug: 'welcome-to-our-blog',
    content: '<p>This is a sample blog post to demonstrate the blog functionality.</p>',
    excerpt: 'This is a sample blog post to demonstrate the blog functionality.',
    featuredImage: '/images/blog/sample.jpg',
    author: 'Admin',
    category: 'general',
    tags: ['welcome', 'sample'],
    status: 'published',
    readingTime: 1,
    viewCount: 0,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    publishedAt: new Date().toISOString()
  };
}
