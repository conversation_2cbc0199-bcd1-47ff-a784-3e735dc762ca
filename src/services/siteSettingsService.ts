/**
 * Site Settings Service
 * Following Next.js best practices for data fetching and error handling
 */

import { prisma } from '@/lib/prisma';
import { cache } from 'react';

// Default settings as fallback
const DEFAULT_SITE_SETTINGS = {
  id: 'default',
  siteName: 'Mocky Digital',
  siteDescription: 'Professional Digital Services & Web Development',
  contactEmail: '<EMAIL>',
  phoneNumber: null,
  address: null,
  facebookUrl: null,
  twitterUrl: null,
  instagramUrl: null,
  linkedinUrl: null,
  tiktokUrl: null,
  metaTitle: 'Mocky Digital - Professional Digital Services',
  metaDescription: 'Expert web development, design, and digital marketing services',
  googleAnalyticsId: null,
  createdAt: new Date(),
  updatedAt: new Date()
};

/**
 * Get site settings with caching and fallback
 * Uses React cache for performance optimization
 */
export const getSiteSettings = cache(async () => {
  try {
    // Try to get settings from database using camelCase model
    const settings = await prisma.siteSettings.findFirst({
      orderBy: { createdAt: 'desc' }
    });

    if (settings) {
      return settings;
    }

    // If no settings found, create default settings
    console.log('No site settings found, creating default settings...');
    const defaultSettings = await prisma.siteSettings.create({
      data: {
        siteName: DEFAULT_SITE_SETTINGS.siteName,
        siteDescription: DEFAULT_SITE_SETTINGS.siteDescription,
        contactEmail: DEFAULT_SITE_SETTINGS.contactEmail,
        metaTitle: DEFAULT_SITE_SETTINGS.metaTitle,
        metaDescription: DEFAULT_SITE_SETTINGS.metaDescription
      }
    });

    return defaultSettings;

  } catch (error) {
    console.error('Error fetching site settings:', error);
    
    // Return default settings as fallback
    console.log('Using fallback default settings');
    return DEFAULT_SITE_SETTINGS;
  }
});

/**
 * Update site settings
 */
export async function updateSiteSettings(data: {
  siteName?: string;
  siteDescription?: string;
  contactEmail?: string;
  phoneNumber?: string;
  address?: string;
  facebookUrl?: string;
  twitterUrl?: string;
  instagramUrl?: string;
  linkedinUrl?: string;
  tiktokUrl?: string;
  metaTitle?: string;
  metaDescription?: string;
  googleAnalyticsId?: string;
}) {
  try {
    // Try to update existing settings
    const existingSettings = await prisma.siteSettings.findFirst();
    
    if (existingSettings) {
      return await prisma.siteSettings.update({
        where: { id: existingSettings.id },
        data: {
          ...data,
          updatedAt: new Date()
        }
      });
    } else {
      // Create new settings if none exist
      return await prisma.siteSettings.create({
        data: {
          siteName: data.siteName || DEFAULT_SITE_SETTINGS.siteName,
          siteDescription: data.siteDescription || DEFAULT_SITE_SETTINGS.siteDescription,
          contactEmail: data.contactEmail || DEFAULT_SITE_SETTINGS.contactEmail,
          metaTitle: data.metaTitle || DEFAULT_SITE_SETTINGS.metaTitle,
          metaDescription: data.metaDescription || DEFAULT_SITE_SETTINGS.metaDescription,
          ...data
        }
      });
    }
  } catch (error) {
    console.error('Error updating site settings:', error);
    throw new Error('Failed to update site settings');
  }
}

/**
 * Get site settings for public use (cached)
 */
export const getPublicSiteSettings = cache(async () => {
  const settings = await getSiteSettings();
  
  return {
    siteName: settings.siteName,
    siteDescription: settings.siteDescription,
    contactEmail: settings.contactEmail,
    phoneNumber: settings.phoneNumber,
    address: settings.address,
    facebookUrl: settings.facebookUrl,
    twitterUrl: settings.twitterUrl,
    instagramUrl: settings.instagramUrl,
    linkedinUrl: settings.linkedinUrl,
    tiktokUrl: settings.tiktokUrl,
    metaTitle: settings.metaTitle,
    metaDescription: settings.metaDescription
  };
});

/**
 * Reset site settings to defaults
 */
export async function resetSiteSettings() {
  try {
    const existingSettings = await prisma.siteSettings.findFirst();
    
    if (existingSettings) {
      return await prisma.siteSettings.update({
        where: { id: existingSettings.id },
        data: {
          siteName: DEFAULT_SITE_SETTINGS.siteName,
          siteDescription: DEFAULT_SITE_SETTINGS.siteDescription,
          contactEmail: DEFAULT_SITE_SETTINGS.contactEmail,
          phoneNumber: null,
          address: null,
          facebookUrl: null,
          twitterUrl: null,
          instagramUrl: null,
          linkedinUrl: null,
          tiktokUrl: null,
          metaTitle: DEFAULT_SITE_SETTINGS.metaTitle,
          metaDescription: DEFAULT_SITE_SETTINGS.metaDescription,
          googleAnalyticsId: null,
          updatedAt: new Date()
        }
      });
    } else {
      return await prisma.siteSettings.create({
        data: {
          siteName: DEFAULT_SITE_SETTINGS.siteName,
          siteDescription: DEFAULT_SITE_SETTINGS.siteDescription,
          contactEmail: DEFAULT_SITE_SETTINGS.contactEmail,
          metaTitle: DEFAULT_SITE_SETTINGS.metaTitle,
          metaDescription: DEFAULT_SITE_SETTINGS.metaDescription
        }
      });
    }
  } catch (error) {
    console.error('Error resetting site settings:', error);
    throw new Error('Failed to reset site settings');
  }
}

/**
 * Upsert site settings (create or update)
 * This is an alias for updateSiteSettings to match API expectations
 */
export async function upsertSiteSettings(data: {
  siteName?: string;
  siteDescription?: string;
  contactEmail?: string;
  phoneNumber?: string;
  address?: string;
  facebookUrl?: string;
  twitterUrl?: string;
  instagramUrl?: string;
  linkedinUrl?: string;
  tiktokUrl?: string;
  metaTitle?: string;
  metaDescription?: string;
  googleAnalyticsId?: string;
}) {
  return await updateSiteSettings(data);
}

/**
 * Initialize site settings with defaults
 */
export async function initializeSiteSettings() {
  try {
    // Check if settings already exist
    const existingSettings = await prisma.siteSettings.findFirst();
    
    if (existingSettings) {
      return existingSettings;
    }

    // Create default settings
    const defaultSettings = await prisma.siteSettings.create({
      data: {
        siteName: DEFAULT_SITE_SETTINGS.siteName,
        siteDescription: DEFAULT_SITE_SETTINGS.siteDescription,
        contactEmail: DEFAULT_SITE_SETTINGS.contactEmail,
        metaTitle: DEFAULT_SITE_SETTINGS.metaTitle,
        metaDescription: DEFAULT_SITE_SETTINGS.metaDescription
      }
    });

    console.log('Initialized site settings with defaults');
    return defaultSettings;

  } catch (error) {
    console.error('Error initializing site settings:', error);
    throw new Error('Failed to initialize site settings');
  }
}
