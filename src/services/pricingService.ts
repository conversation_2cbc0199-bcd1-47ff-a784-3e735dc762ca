import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export interface PricingCalculation {
  basePrice: number;
  totalPrice: number;
  breakdown: {
    paperType?: string;
    printingSide?: string;
    quantity?: number;
    meters?: number;
    unitPrice?: number;
    additionalCharges?: { name: string; amount: number }[];
  };
  isValid: boolean;
  errors?: string[];
}

export interface PricingInput {
  productId: number;
  quantity?: number;
  paperType?: string;
  printingSide?: 'single' | 'double';
  meters?: number;
  width?: number;
  height?: number;
  customOptions?: Record<string, any>;
}

export class PricingService {
  
  /**
   * Calculate price for any product based on its pricing type
   */
  static async calculatePrice(input: PricingInput): Promise<PricingCalculation> {
    try {
      const product = await prisma.catalogue.findUnique({
        where: { id: input.productId }
      });

      if (!product) {
        return {
          basePrice: 0,
          totalPrice: 0,
          breakdown: {},
          isValid: false,
          errors: ['Product not found']
        };
      }

      switch (product.pricingType) {
        case 'fixed':
          return this.calculateFixedPrice(product, input);
        
        case 'paper_print':
          return this.calculatePaperPrintPrice(product, input);
        
        case 'banner_meter':
          return this.calculateBannerMeterPrice(product, input);
        
        case 'custom':
          return this.calculateCustomPrice(product, input);
        
        default:
          return this.calculateFixedPrice(product, input);
      }
    } catch (error) {
      console.error('Pricing calculation error:', error);
      return {
        basePrice: 0,
        totalPrice: 0,
        breakdown: {},
        isValid: false,
        errors: ['Pricing calculation failed']
      };
    }
  }

  /**
   * Fixed price products (like rollup banners)
   */
  private static calculateFixedPrice(product: any, input: PricingInput): PricingCalculation {
    const quantity = input.quantity || 1;
    const unitPrice = Number(product.price);
    const totalPrice = unitPrice * quantity;

    return {
      basePrice: unitPrice,
      totalPrice,
      breakdown: {
        quantity,
        unitPrice
      },
      isValid: true
    };
  }

  /**
   * Paper print pricing based on paper type and printing side
   */
  private static async calculatePaperPrintPrice(product: any, input: PricingInput): Promise<PricingCalculation> {
    const errors: string[] = [];
    
    if (!input.paperType) {
      errors.push('Paper type is required');
    }
    
    if (!input.printingSide) {
      errors.push('Printing side selection is required');
    }

    if (!input.quantity || input.quantity < 1) {
      errors.push('Valid quantity is required');
    }

    if (errors.length > 0) {
      return {
        basePrice: 0,
        totalPrice: 0,
        breakdown: {},
        isValid: false,
        errors
      };
    }

    try {
      // Get paper type pricing from database
      const paperType = await prisma.paperType.findFirst({
        where: { 
          name: input.paperType,
          active: true 
        }
      });

      if (!paperType) {
        return {
          basePrice: 0,
          totalPrice: 0,
          breakdown: {},
          isValid: false,
          errors: ['Invalid paper type selected']
        };
      }

      // Calculate unit price based on printing side
      const unitPrice = input.printingSide === 'double' 
        ? Number(paperType.twoSidedPrice) 
        : Number(paperType.oneSidedPrice);

      // Check if double-sided is available for this paper type
      if (input.printingSide === 'double' && Number(paperType.twoSidedPrice) === 0) {
        return {
          basePrice: 0,
          totalPrice: 0,
          breakdown: {},
          isValid: false,
          errors: [`Double-sided printing is not available for ${paperType.name} ${paperType.grammage}`]
        };
      }

      const quantity = input.quantity!;
      const totalPrice = unitPrice * quantity;

      return {
        basePrice: unitPrice,
        totalPrice,
        breakdown: {
          paperType: `${paperType.name} ${paperType.grammage}`.trim(),
          printingSide: input.printingSide,
          quantity,
          unitPrice
        },
        isValid: true
      };
    } catch (error) {
      console.error('Paper print pricing calculation error:', error);
      return {
        basePrice: 0,
        totalPrice: 0,
        breakdown: {},
        isValid: false,
        errors: ['Failed to calculate paper print pricing']
      };
    }
  }

  /**
   * Banner pricing based on meters
   */
  private static calculateBannerMeterPrice(product: any, input: PricingInput): PricingCalculation {
    const errors: string[] = [];
    
    if (!input.meters || input.meters <= 0) {
      errors.push('Valid meter measurement is required');
    }

    if (errors.length > 0) {
      return {
        basePrice: 0,
        totalPrice: 0,
        breakdown: {},
        isValid: false,
        errors
      };
    }

    const pricePerMeter = product.pricePerMeter ? Number(product.pricePerMeter) : Number(product.price);
    const meters = input.meters!;
    const totalPrice = pricePerMeter * meters;

    // Check minimum/maximum constraints
    if (product.minMeters && meters < Number(product.minMeters)) {
      errors.push(`Minimum ${product.minMeters} meters required`);
    }

    if (product.maxMeters && meters > Number(product.maxMeters)) {
      errors.push(`Maximum ${product.maxMeters} meters allowed`);
    }

    return {
      basePrice: pricePerMeter,
      totalPrice,
      breakdown: {
        meters,
        unitPrice: pricePerMeter
      },
      isValid: errors.length === 0,
      errors: errors.length > 0 ? errors : undefined
    };
  }

  /**
   * Custom pricing logic for complex products
   */
  private static calculateCustomPrice(product: any, input: PricingInput): PricingCalculation {
    // This can be extended for complex custom pricing logic
    // For now, fall back to fixed pricing
    return this.calculateFixedPrice(product, input);
  }

  /**
   * Get available paper types for paper print products
   */
  static async getAvailablePaperTypes(): Promise<any[]> {
    return await prisma.paperType.findMany({
      where: { active: true },
      orderBy: { order: 'asc' }
    });
  }

  /**
   * Get pricing options for a specific product
   */
  static async getProductPricingOptions(productId: number) {
    const product = await prisma.catalogue.findUnique({
      where: { id: productId }
    });

    if (!product) {
      return null;
    }

    const options: any = {
      pricingType: product.pricingType,
      unitType: product.unitType,
      minQuantity: product.minQuantity,
      maxQuantity: product.maxQuantity
    };

    switch (product.pricingType) {
      case 'paper_print':
        options.paperTypes = await this.getAvailablePaperTypes();
        options.printingSides = [
          { value: 'single', label: 'Single Sided' },
          { value: 'double', label: 'Double Sided' }
        ];
        break;
      
      case 'banner_meter':
        options.pricePerMeter = product.pricePerMeter;
        options.minMeters = product.minMeters;
        options.maxMeters = product.maxMeters;
        break;
      
      case 'fixed':
        options.basePrice = product.price;
        break;
    }

    return options;
  }

  /**
   * Update product pricing configuration
   */
  static async updateProductPricing(productId: number, pricingData: any) {
    return await prisma.catalogue.update({
      where: { id: productId },
      data: {
        pricingType: pricingData.pricingType,
        unitType: pricingData.unitType,
        minQuantity: pricingData.minQuantity,
        maxQuantity: pricingData.maxQuantity,
        pricePerMeter: pricingData.pricePerMeter,
        minMeters: pricingData.minMeters,
        maxMeters: pricingData.maxMeters,
        paperTypes: pricingData.paperTypes,
        pricingTiers: pricingData.pricingTiers,
        updatedAt: new Date()
      }
    });
  }

  /**
   * Get paper type details by name
   */
  static async getPaperTypeByName(name: string) {
    return await prisma.paperType.findFirst({
      where: { 
        name: name,
        active: true 
      }
    });
  }

  /**
   * Validate pricing input for a product
   */
  static async validatePricingInput(productId: number, input: PricingInput): Promise<string[]> {
    const errors: string[] = [];
    
    const product = await prisma.catalogue.findUnique({
      where: { id: productId }
    });

    if (!product) {
      errors.push('Product not found');
      return errors;
    }

    switch (product.pricingType) {
      case 'paper_print':
        if (!input.paperType) errors.push('Paper type is required');
        if (!input.printingSide) errors.push('Printing side is required');
        if (!input.quantity || input.quantity < 1) errors.push('Valid quantity is required');
        break;
      
      case 'banner_meter':
        if (!input.meters || input.meters <= 0) errors.push('Valid meter measurement is required');
        if (product.minMeters && input.meters < Number(product.minMeters)) {
          errors.push(`Minimum ${product.minMeters} meters required`);
        }
        if (product.maxMeters && input.meters > Number(product.maxMeters)) {
          errors.push(`Maximum ${product.maxMeters} meters allowed`);
        }
        break;
      
      case 'fixed':
        if (!input.quantity || input.quantity < 1) errors.push('Valid quantity is required');
        break;
    }

    return errors;
  }

  /**
   * Get pricing breakdown text for display
   */
  static getPricingBreakdownText(calculation: PricingCalculation): string {
    if (!calculation.isValid) {
      return 'Invalid pricing configuration';
    }

    const { breakdown } = calculation;
    let text = '';

    if (breakdown.paperType) {
      text += `${breakdown.paperType} - ${breakdown.printingSide} sided`;
    }

    if (breakdown.quantity) {
      text += ` × ${breakdown.quantity} units`;
    }

    if (breakdown.meters) {
      text += ` × ${breakdown.meters} meters`;
    }

    if (breakdown.unitPrice) {
      text += ` @ KSh ${breakdown.unitPrice} each`;
    }

    return text || `Total: KSh ${calculation.totalPrice}`;
  }
}

export default PricingService; 