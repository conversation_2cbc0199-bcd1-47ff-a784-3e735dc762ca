import prisma from '@/lib/prisma';
import { z } from 'zod';
import { ErrorTracker } from '@/services/errorLogger';
import { CataloguePerformanceMonitor, CatalogueAuditLogger } from '@/utils/catalogueMonitoring';

// Enhanced validation schemas
const CatalogueBaseSchema = z.object({
  service: z.string()
    .min(1, 'Service name is required')
    .max(255, 'Service name too long')
    .regex(/^[a-zA-Z0-9\s\-&.,()×:/"'!@#$%^*+=|\\?<>[\]{}~`]+$/, 'Invalid characters in service name')
    .transform(str => str.trim()),
  price: z.number()
    .min(0, 'Price cannot be negative')
    .max(10000000, 'Price too high'),
  designFee: z.number()
    .min(0, 'Design fee cannot be negative')
    .max(10000000, 'Design fee too high')
    .optional()
    .default(0),
  description: z.string()
    .max(1000, 'Description too long')
    .transform(str => str?.trim() || '')
    .optional(),
  features: z.array(
    z.string()
      .max(100, 'Feature too long')
      .transform(str => str.trim())
  )
    .max(10, 'Too many features')
    .optional()
    .default([]),

  popular: z.boolean().optional().default(false),
  imageUrl: z.string()
    .url('Invalid image URL')
    .max(500, 'Image URL too long')
    .nullish()
    .transform(val => val || null),
  imageUrl2: z.string()
    .url('Invalid image URL')
    .max(500, 'Image URL too long')
    .nullish()
    .transform(val => val || null),
  imageUrl3: z.string()
    .url('Invalid image URL')
    .max(500, 'Image URL too long')
    .nullish()
    .transform(val => val || null),
  category: z.string()
    .max(100, 'Category name too long')
    .regex(/^[a-zA-Z0-9\s\-&.,()×:/"'!@#$%^*+=|\\?<>[\]{}~`]*$/, 'Invalid characters in category')
    .transform(str => str?.trim() || 'Other')
    .optional()
    .default('Other'),
  // Meter-based pricing fields
  pricingType: z.string()
    .max(50, 'Pricing type too long')
    .optional()
    .default('fixed'),
  unitType: z.string()
    .max(50, 'Unit type too long')
    .nullish()
    .transform(val => val || null),
  pricePerMeter: z.number()
    .min(0, 'Price per meter cannot be negative')
    .max(100000, 'Price per meter too high')
    .nullish()
    .transform(val => val || null),
  minMeters: z.number()
    .min(0, 'Minimum meters cannot be negative')
    .max(1000, 'Minimum meters too high')
    .nullish()
    .transform(val => val || null),
  maxMeters: z.number()
    .min(0, 'Maximum meters cannot be negative')
    .max(1000, 'Maximum meters too high')
    .nullish()
    .transform(val => val || null),
  minQuantity: z.number()
    .min(1, 'Minimum quantity must be at least 1')
    .max(100000, 'Minimum quantity too high')
    .int('Minimum quantity must be a whole number')
    .optional()
    .default(1),
  maxQuantity: z.number()
    .min(1, 'Maximum quantity must be at least 1')
    .max(1000000, 'Maximum quantity too high')
    .int('Maximum quantity must be a whole number')
    .nullish()
    .transform(val => val || null)
});

const CatalogueCreateSchema = CatalogueBaseSchema;
const CatalogueUpdateSchema = CatalogueBaseSchema.partial();

const CatalogueIdSchema = z.string()
  .regex(/^\d+$/, 'Invalid ID format')
  .transform(str => parseInt(str, 10))
  .refine(num => num > 0, 'ID must be positive');

// Enhanced interfaces
export interface Catalogue {
  id: string;
  service: string;
  price: number;
  designFee?: number;
  description?: string;
  features?: string[];
  popular?: boolean;
  imageUrl?: string | null;
  imageUrl2?: string | null;
  imageUrl3?: string | null;
  category?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CatalogueCreateData {
  service: string;
  price: number;
  designFee?: number;
  description?: string;
  features?: string[];
  popular?: boolean;
  imageUrl?: string | null;
  imageUrl2?: string | null;
  imageUrl3?: string | null;
  category?: string;
}

export interface CatalogueUpdateData {
  service?: string;
  price?: number;
  designFee?: number;
  description?: string;
  features?: string[];
  popular?: boolean;
  imageUrl?: string | null;
  imageUrl2?: string | null;
  imageUrl3?: string | null;
  category?: string;
}

// Custom error classes
export class CatalogueError extends Error {
  constructor(message: string, public code: string, public statusCode: number = 500) {
    super(message);
    this.name = 'CatalogueError';
  }
}

export class CatalogueValidationError extends CatalogueError {
  constructor(message: string, public field?: string) {
    super(message, 'VALIDATION_ERROR', 400);
    this.name = 'CatalogueValidationError';
  }
}

export class CatalogueNotFoundError extends CatalogueError {
  constructor(id: string) {
    super(`Catalogue item with ID ${id} not found`, 'NOT_FOUND', 404);
    this.name = 'CatalogueNotFoundError';
  }
}

export class CatalogueDuplicateError extends CatalogueError {
  constructor(service: string) {
    super(`Catalogue item with service name "${service}" already exists`, 'DUPLICATE', 409);
    this.name = 'CatalogueDuplicateError';
  }
}

// Enhanced CRUD operations
export class CatalogueCRUDService {
  // CREATE operation
  static async create(
    data: unknown,
    context?: { userId?: string; ipAddress?: string; userAgent?: string }
  ): Promise<Catalogue> {
    const timer = CataloguePerformanceMonitor.startTimer('create');

    try {
      // Validate input data
      const validatedData = CatalogueCreateSchema.parse(data);

      // Check for duplicate service names (case-insensitive)
      const existingCatalogue = await prisma.catalogue.findFirst({
        where: {
          service: {
            equals: validatedData.service,
            mode: 'insensitive'
          }
        }
      });

      if (existingCatalogue) {
        throw new CatalogueDuplicateError(validatedData.service);
      }

      // Create the catalogue item
      const createData: any = {
        service: validatedData.service,
        price: validatedData.price,
        description: validatedData.description || '',
        features: validatedData.features || [],
        popular: validatedData.popular || false,
        imageUrl: validatedData.imageUrl || null,
        imageUrl2: validatedData.imageUrl2 || null,
        imageUrl3: validatedData.imageUrl3 || null,
        category: validatedData.category || 'Other'
      };

      // Add optional fields if they exist
      if (validatedData.designFee !== undefined) createData.designFee = validatedData.designFee;
      if (validatedData.pricingType !== undefined) createData.pricingType = validatedData.pricingType;
      if (validatedData.unitType !== undefined) createData.unitType = validatedData.unitType;
      if (validatedData.pricePerMeter !== undefined) createData.pricePerMeter = validatedData.pricePerMeter;
      if (validatedData.minMeters !== undefined) createData.minMeters = validatedData.minMeters;
      if (validatedData.maxMeters !== undefined) createData.maxMeters = validatedData.maxMeters;
      if (validatedData.minQuantity !== undefined) createData.minQuantity = validatedData.minQuantity;
      if (validatedData.maxQuantity !== undefined) createData.maxQuantity = validatedData.maxQuantity;

      const newCatalogue = await prisma.catalogue.create({
        data: createData
      });

      const formattedCatalogue = this.formatCatalogue(newCatalogue);

      // Log the action
      await CatalogueAuditLogger.logAction(
        'CREATE',
        formattedCatalogue.id,
        context?.userId,
        context?.ipAddress,
        context?.userAgent,
        undefined,
        formattedCatalogue
      );

      timer();
      console.log(`Created catalogue item: ${newCatalogue.service}`);
      return formattedCatalogue;

    } catch (error) {
      timer();
      CataloguePerformanceMonitor.recordError('create');

      if (error instanceof z.ZodError) {
        const firstError = error.errors[0];
        if (firstError) {
            throw new CatalogueValidationError(firstError.message, firstError.path.join('.'));
        }
        throw new CatalogueValidationError('Validation failed with unknown details.');
      }

      if (error instanceof CatalogueDuplicateError) {
        throw error;
      }

      ErrorTracker.trackError(error as Error, 'CatalogueCRUDService.create', { data, context });
      throw new CatalogueError('Failed to create catalogue item', 'CREATE_FAILED');
    }
  }

  // READ operation (single item)
  static async findById(id: string): Promise<Catalogue | null> {
    const timer = CataloguePerformanceMonitor.startTimer('findById');

    try {
      const catalogueId = CatalogueIdSchema.parse(id);

      const catalogue = await prisma.catalogue.findUnique({
        where: { id: catalogueId }
      });

      timer();

      if (!catalogue) {
        return null;
      }

      return this.formatCatalogue(catalogue);

    } catch (error) {
      timer();
      CataloguePerformanceMonitor.recordError('findById');

      if (error instanceof z.ZodError) {
        throw new CatalogueValidationError('Invalid catalogue ID format');
      }

      ErrorTracker.trackError(error as Error, 'CatalogueCRUDService.findById', { id });
      throw new CatalogueError('Failed to fetch catalogue item', 'READ_FAILED');
    }
  }

  // READ operation (multiple items with filters)
  static async findMany(filters?: {
    category?: string;
    popular?: boolean;
    search?: string;
    minPrice?: number;
    maxPrice?: number;
    limit?: number;
    offset?: number;
    sortBy?: 'service' | 'price' | 'createdAt' | 'updatedAt';
    sortOrder?: 'asc' | 'desc';
  }): Promise<{ items: Catalogue[]; total: number }> {
    const timer = CataloguePerformanceMonitor.startTimer('findMany');

    try {
      const where: any = {};

      // Apply filters
      if (filters?.category) {
        where.category = filters.category;
      }

      if (filters?.popular !== undefined) {
        where.popular = filters.popular;
      }

      if (filters?.minPrice !== undefined || filters?.maxPrice !== undefined) {
        where.price = {};
        if (filters.minPrice !== undefined) where.price.gte = filters.minPrice;
        if (filters.maxPrice !== undefined) where.price.lte = filters.maxPrice;
      }

      if (filters?.search) {
        const searchTerm = filters.search.trim();
        if (searchTerm.length >= 2) {
          where.OR = [
            { service: { contains: searchTerm, mode: 'insensitive' } },
            { description: { contains: searchTerm, mode: 'insensitive' } },
            { category: { contains: searchTerm, mode: 'insensitive' } }
          ];
        }
      }

      // Build order by
      const orderBy: any = {};
      const sortBy = filters?.sortBy || 'service';
      const sortOrder = filters?.sortOrder || 'asc';
      orderBy[sortBy] = sortOrder;

      // Execute queries
      const [items, total] = await prisma.$transaction([
        prisma.catalogue.findMany({
          where,
          orderBy,
          take: Math.min(filters?.limit || 50, 100),
          skip: filters?.offset || 0
        }),
        prisma.catalogue.count({ where })
      ]);

      timer();

      return {
        items: items.map(this.formatCatalogue),
        total
      };

    } catch (error) {
      timer();
      CataloguePerformanceMonitor.recordError('findMany');

      ErrorTracker.trackError(error as Error, 'CatalogueCRUDService.findMany', { filters });
      throw new CatalogueError('Failed to fetch catalogue items', 'READ_FAILED');
    }
  }

  // UPDATE operation
  static async update(
    id: string,
    data: unknown,
    context?: { userId?: string; ipAddress?: string; userAgent?: string }
  ): Promise<Catalogue> {
    const timer = CataloguePerformanceMonitor.startTimer('update');

    try {
      const catalogueId = CatalogueIdSchema.parse(id);
      const validatedData = CatalogueUpdateSchema.parse(data);

      // Check if catalogue exists and get current data
      const existingCatalogue = await prisma.catalogue.findUnique({
        where: { id: catalogueId }
      });

      if (!existingCatalogue) {
        throw new CatalogueNotFoundError(id);
      }

      // Check for duplicate service names if service is being updated
      if (validatedData.service && validatedData.service !== existingCatalogue.service) {
        const duplicateCatalogue = await prisma.catalogue.findFirst({
          where: {
            service: {
              equals: validatedData.service,
              mode: 'insensitive'
            },
            id: { not: catalogueId }
          }
        });

        if (duplicateCatalogue) {
          throw new CatalogueDuplicateError(validatedData.service);
        }
      }

      // Transform validated data to match Prisma's expectations
      // Remove undefined fields and handle nullable fields properly
      const updateData: Record<string, any> = {};
      
      Object.entries(validatedData).forEach(([key, value]) => {
        if (value !== undefined) {
          updateData[key] = value;
        }
      });

      // Update the catalogue item
      const updatedCatalogue = await prisma.catalogue.update({
        where: { id: catalogueId },
        data: updateData
      });

      const formattedCatalogue = this.formatCatalogue(updatedCatalogue);

      // Log the action
      await CatalogueAuditLogger.logAction(
        'UPDATE',
        formattedCatalogue.id,
        context?.userId,
        context?.ipAddress,
        context?.userAgent,
        this.formatCatalogue(existingCatalogue),
        formattedCatalogue
      );

      timer();
      console.log(`Updated catalogue item: ${updatedCatalogue.service}`);
      return formattedCatalogue;

    } catch (error) {
      timer();
      CataloguePerformanceMonitor.recordError('update');

      if (error instanceof z.ZodError) {
        const firstError = error.errors[0];
        if (firstError) {
            throw new CatalogueValidationError(firstError.message, firstError.path.join('.'));
        }
        throw new CatalogueValidationError('Validation failed with unknown details.');
      }

      if (error instanceof CatalogueNotFoundError || error instanceof CatalogueDuplicateError) {
        throw error;
      }

      ErrorTracker.trackError(error as Error, 'CatalogueCRUDService.update', { id, data, context });
      throw new CatalogueError('Failed to update catalogue item', 'UPDATE_FAILED');
    }
  }

  // DELETE operation (single item)
  static async delete(
    id: string,
    context?: { userId?: string; ipAddress?: string; userAgent?: string }
  ): Promise<boolean> {
    const timer = CataloguePerformanceMonitor.startTimer('delete');

    try {
      const catalogueId = CatalogueIdSchema.parse(id);

      // Check if catalogue exists and get current data for audit
      const existingCatalogue = await prisma.catalogue.findUnique({
        where: { id: catalogueId }
      });

      if (!existingCatalogue) {
        throw new CatalogueNotFoundError(id);
      }

      // Check if catalogue item has orders (foreign key constraint)
      const ordersCount = await prisma.order.count({
        where: { productId: catalogueId }
      });

      if (ordersCount > 0) {
        throw new CatalogueError(
          `Cannot delete catalogue item "${existingCatalogue.service}" because it has ${ordersCount} existing order(s). Please handle or remove the related orders first.`,
          'DELETE_CONSTRAINT_VIOLATION',
          409
        );
      }

      // Delete the catalogue item
      await prisma.catalogue.delete({
        where: { id: catalogueId }
      });

      // Log the action
      await CatalogueAuditLogger.logAction(
        'DELETE',
        id,
        context?.userId,
        context?.ipAddress,
        context?.userAgent,
        this.formatCatalogue(existingCatalogue),
        undefined
      );

      timer();
      console.log(`Deleted catalogue item: ${existingCatalogue.service}`);
      return true;

    } catch (error) {
      timer();
      CataloguePerformanceMonitor.recordError('delete');

      if (error instanceof z.ZodError) {
        throw new CatalogueValidationError('Invalid catalogue ID format');
      }

      if (error instanceof CatalogueNotFoundError) {
        throw error;
      }

      ErrorTracker.trackError(error as Error, 'CatalogueCRUDService.delete', { id, context });
      throw new CatalogueError('Failed to delete catalogue item', 'DELETE_FAILED');
    }
  }

  // DELETE operation (bulk delete)
  static async bulkDelete(
    ids: string[],
    context?: { userId?: string; ipAddress?: string; userAgent?: string }
  ): Promise<{ success: boolean; deletedCount: number; errors: string[] }> {
    const timer = CataloguePerformanceMonitor.startTimer('bulkDelete');

    try {
      // Validate IDs
      if (!Array.isArray(ids) || ids.length === 0) {
        throw new CatalogueValidationError('No IDs provided for deletion');
      }

      if (ids.length > 50) {
        throw new CatalogueValidationError('Too many items to delete at once (max 50)');
      }

      const catalogueIds = ids.map(id => {
        try {
          return CatalogueIdSchema.parse(id);
        } catch {
          throw new CatalogueValidationError(`Invalid ID format: ${id}`);
        }
      });

      // Get existing items for audit logging
      const existingItems = await prisma.catalogue.findMany({
        where: { id: { in: catalogueIds } }
      });

      const existingIds = existingItems.map(item => item.id);
      const missingIds = catalogueIds.filter(id => !existingIds.includes(id));

      const errors: string[] = [];
      if (missingIds.length > 0) {
        errors.push(`Items not found: ${missingIds.join(', ')}`);
      }

      // Check which items have orders (foreign key constraint)
      const itemsWithOrders = await prisma.order.findMany({
        where: { productId: { in: existingIds } },
        select: {
          productId: true,
          product: { select: { service: true } }
        },
        distinct: ['productId']
      });

      const idsWithOrders = itemsWithOrders.map(order => order.productId);
      const itemsToDelete = existingIds.filter(id => !idsWithOrders.includes(id));

      // Record skipped items
      if (idsWithOrders.length > 0) {
        const skippedItems = itemsWithOrders.map(item => ({
          id: item.productId,
          name: item.product?.service || 'Unknown'
        }));
        errors.push(`Cannot delete items with existing orders: ${skippedItems.map(item => `${item.name} (ID: ${item.id})`).join(', ')}`);
      }

      let deleteResult = { count: 0 };

      // Delete items that don't have orders
      if (itemsToDelete.length > 0) {
        deleteResult = await prisma.catalogue.deleteMany({
          where: { id: { in: itemsToDelete } }
        });
      }

      // Log the action
      await CatalogueAuditLogger.logAction(
        'BULK_DELETE',
        existingIds.map(id => id.toString()),
        context?.userId,
        context?.ipAddress,
        context?.userAgent,
        existingItems.map(this.formatCatalogue),
        undefined
      );

      timer();
      console.log(`Bulk deleted ${deleteResult.count} catalogue items`);

      return {
        success: deleteResult.count > 0,
        deletedCount: deleteResult.count,
        errors
      };

    } catch (error) {
      timer();
      CataloguePerformanceMonitor.recordError('bulkDelete');

      if (error instanceof CatalogueValidationError) {
        throw error;
      }

      ErrorTracker.trackError(error as Error, 'CatalogueCRUDService.bulkDelete', { ids, context });
      throw new CatalogueError('Failed to bulk delete catalogue items', 'BULK_DELETE_FAILED');
    }
  }

  // UTILITY operations
  static async exists(id: string): Promise<boolean> {
    try {
      const catalogueId = CatalogueIdSchema.parse(id);
      const count = await prisma.catalogue.count({
        where: { id: catalogueId }
      });
      return count > 0;
    } catch {
      return false;
    }
  }

  static async count(filters?: { category?: string; popular?: boolean }): Promise<number> {
    try {
      const where: any = {};
      if (filters?.category) where.category = filters.category;
      if (filters?.popular !== undefined) where.popular = filters.popular;

      return await prisma.catalogue.count({ where });
    } catch (error) {
      ErrorTracker.trackError(error as Error, 'CatalogueCRUDService.count', { filters });
      throw new CatalogueError('Failed to count catalogue items', 'COUNT_FAILED');
    }
  }

  static async getCategories(): Promise<string[]> {
    try {
      const categories = await prisma.catalogue.groupBy({
        by: ['category'],
        _count: { category: true },
        orderBy: { _count: { category: 'desc' } }
      });

      return categories.map(cat => cat.category || 'Other');
    } catch (error) {
      ErrorTracker.trackError(error as Error, 'CatalogueCRUDService.getCategories');
      throw new CatalogueError('Failed to fetch categories', 'CATEGORIES_FAILED');
    }
  }

  // Helper method to format catalogue data
  private static formatCatalogue(catalogue: any): Catalogue {
    return {
      id: catalogue.id.toString(),
      service: catalogue.service,
      price: catalogue.price,
      designFee: catalogue.designFee || 0,
      description: catalogue.description || '',
      features: catalogue.features || [],
      popular: catalogue.popular || false,
      imageUrl: catalogue.imageUrl || undefined,
      imageUrl2: catalogue.imageUrl2 || undefined,
      imageUrl3: catalogue.imageUrl3 || undefined,
      category: catalogue.category || 'Other',
      createdAt: catalogue.createdAt.toISOString(),
      updatedAt: catalogue.updatedAt.toISOString()
    };
  }
}
