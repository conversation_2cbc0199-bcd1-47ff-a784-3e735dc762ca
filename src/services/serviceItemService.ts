/**
 * Service for managing service items
 */
import prisma from '@/lib/prisma';

export interface Service {
  id: string;
  name: string;
  description: string | null;
  price: number;
  category: string;
  createdAt: Date;
  updatedAt: Date;
}

/**
 * Get all services
 * @returns Array of all services
 */
export async function getAllServices(): Promise<Service[]> {
  try {
    const services = await prisma.service.findMany({
      orderBy: {
        name: 'asc'
      }
    });
    
    return services.map(formatService);
  } catch (error) {
    console.error('Error getting all services:', error);
    throw error;
  }
}

/**
 * Get services by category
 * @param category The service category
 * @returns Array of services in the category
 */
export async function getServicesByCategory(category: string): Promise<Service[]> {
  try {
    const services = await prisma.service.findMany({
      where: {
        category
      },
      orderBy: {
        name: 'asc'
      }
    });
    
    return services.map(formatService);
  } catch (error) {
    console.error(`Error getting services in category ${category}:`, error);
    throw error;
  }
}

/**
 * Get a service by ID
 * @param id The service ID
 * @returns The service or null if not found
 */
export async function getServiceById(id: string): Promise<Service | null> {
  try {
    const service = await prisma.service.findUnique({
      where: {
        id
      }
    });
    
    if (!service) {
      return null;
    }
    
    return formatService(service);
  } catch (error) {
    console.error(`Error getting service ${id}:`, error);
    throw error;
  }
}

/**
 * Create a new service
 * @param data The service data
 * @returns The created service
 */
export async function createService(data: Omit<Service, 'id' | 'createdAt' | 'updatedAt'>): Promise<Service> {
  try {
    const service = await prisma.service.create({
      data: {
        name: data.name,
        description: data.description,
        price: data.price,
        category: data.category
      }
    });
    
    return formatService(service);
  } catch (error) {
    console.error('Error creating service:', error);
    throw error;
  }
}

/**
 * Update a service
 * @param id The service ID
 * @param data The updated service data
 * @returns The updated service
 */
export async function updateService(id: string, data: Partial<Omit<Service, 'id' | 'createdAt' | 'updatedAt'>>): Promise<Service> {
  try {
    const updateData: any = {};
    
    if (data.name !== undefined) updateData.name = data.name;
    if (data.description !== undefined) updateData.description = data.description;
    if (data.price !== undefined) updateData.price = data.price;
    if (data.category !== undefined) updateData.category = data.category;
    
    const service = await prisma.service.update({
      where: {
        id
      },
      data: updateData
    });
    
    return formatService(service);
  } catch (error) {
    console.error(`Error updating service ${id}:`, error);
    throw error;
  }
}

/**
 * Delete a service
 * @param id The service ID
 * @returns Object with success status and error details
 */
export async function deleteService(id: string): Promise<{ success: boolean; error?: string }> {
  let existingService: any = null;
  
  try {
    console.log(`[ServiceItemService] Attempting to delete service: ${id}`);
    
    // First check if the service exists
    existingService = await prisma.service.findUnique({
      where: { id }
    });
    
    if (!existingService) {
      console.log(`[ServiceItemService] Service not found: ${id}`);
      return { success: false, error: `Service with ID ${id} not found` };
    }
    
    console.log(`[ServiceItemService] Service found: ${existingService.name}, proceeding with deletion`);
    
    // Check if service is referenced by quote items
    const referencingQuoteItems = await prisma.quoteItem.findMany({
      where: { serviceId: id },
      include: {
        quote: {
          select: {
            id: true,
            quoteNumber: true
          }
        }
      }
    });
    
    if (referencingQuoteItems.length > 0) {
      console.log(`[ServiceItemService] Service ${id} is referenced by ${referencingQuoteItems.length} quote item(s)`);
      const quoteNumbers = referencingQuoteItems.map(item => item.quote.quoteNumber).join(', ');
      return { 
        success: false, 
        error: `Cannot delete "${existingService.name}" because it is used in ${referencingQuoteItems.length} quote(s): ${quoteNumbers}. Please remove the service from these quotes first.`
      };
    }
    
    // Check if service is referenced by invoice items
    const referencingInvoiceItems = await prisma.invoiceItem.findMany({
      where: { serviceId: id },
      include: {
        invoice: {
          select: {
            id: true,
            invoiceNumber: true
          }
        }
      }
    });
    
    if (referencingInvoiceItems.length > 0) {
      console.log(`[ServiceItemService] Service ${id} is referenced by ${referencingInvoiceItems.length} invoice item(s)`);
      const invoiceNumbers = referencingInvoiceItems.map(item => item.invoice.invoiceNumber).join(', ');
      return { 
        success: false, 
        error: `Cannot delete "${existingService.name}" because it is used in ${referencingInvoiceItems.length} invoice(s): ${invoiceNumbers}. Please remove the service from these invoices first.`
      };
    }
    
    // Delete the service
    await prisma.service.delete({
      where: { id }
    });
    
    console.log(`[ServiceItemService] Successfully deleted service: ${id}`);
    return { success: true };
  } catch (error) {
    console.error(`[ServiceItemService] Error deleting service ${id}:`, error);
    
    // Handle specific Prisma errors
    if (error instanceof Error) {
      if (error.message.includes('Foreign key constraint failed') || error.message.includes('foreign key constraint')) {
        // Check which constraint is failing
        if (error.message.includes('quote_items_serviceId_fkey')) {
          return { 
            success: false, 
            error: `Cannot delete "${existingService?.name || 'this service'}" because it is being used in quotes. Please remove the service from all quotes first.` 
          };
        } else if (error.message.includes('invoice_items_serviceId_fkey')) {
          return { 
            success: false, 
            error: `Cannot delete "${existingService?.name || 'this service'}" because it is being used in invoices. Please remove the service from all invoices first.` 
          };
        } else {
          return { 
            success: false, 
            error: `Cannot delete "${existingService?.name || 'this service'}" because it is being used in other records. Please remove all references first.` 
          };
        }
      }
      if (error.message.includes('Record to delete does not exist')) {
        return { success: false, error: `Service with ID ${id} no longer exists` };
      }
      return { success: false, error: error.message };
    }
    
    return { success: false, error: 'Unknown error occurred during deletion' };
  }
}

/**
 * Format a service from the database
 * @param service The service from the database
 * @returns The formatted service
 */
function formatService(service: any): Service {
  return {
    id: service.id,
    name: service.name,
    description: service.description,
    price: Number(service.price),
    category: service.category,
    createdAt: new Date(service.createdAt),
    updatedAt: new Date(service.updatedAt)
  };
}
