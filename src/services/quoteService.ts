/**
 * Service for managing quotes
 */
import prisma from '@/lib/prisma';
import { generateQuotePDF, QuoteData } from '@/utils/pdfGenerator';
import { getServiceById } from './serviceItemService';
import { createInvoice } from './invoiceService';
import path from 'path';
import { promises as fs } from 'fs';
import { addDays } from 'date-fns';

export interface Quote {
  id: string;
  quoteNumber: string;
  totalAmount: number;
  subtotalAmount: number;
  discountType?: string | null;
  discountValue?: number | null;
  discountAmount?: number | null;
  customerName: string;
  phoneNumber: string;
  email: string | null;
  status: string;
  notes: string | null;
  createdAt: Date;
  updatedAt: Date;
  issuedAt: Date;
  validUntil: Date;
  pdfUrl?: string;
  items: QuoteItem[];
}

export interface QuoteItem {
  id: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  description: string | null;
  itemName?: string | null;
  itemType: string;
  serviceId?: string | null;
  serviceName?: string;
}

/**
 * Calculate discount amount based on type and value
 */
function calculateDiscount(subtotal: number, discountType?: string | null, discountValue?: number | null): number {
  if (!discountType || !discountValue) return 0;
  
  if (discountType === 'percentage') {
    return (subtotal * discountValue) / 100;
  } else if (discountType === 'fixed') {
    return Math.min(discountValue, subtotal); // Don't allow negative totals
  }
  
  return 0;
}

/**
 * Get all quotes
 * @returns All quotes
 */
export async function getAllQuotes(): Promise<Quote[]> {
  try {
    const quotes = await prisma.quote.findMany({
      include: {
        items: {
          include: {
            service: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    return quotes.map(formatQuote);
  } catch (error) {
    console.error('Error getting all quotes:', error);
    throw error;
  }
}

/**
 * Get a quote by ID
 * @param id The quote ID
 * @returns The quote or null if not found
 */
export async function getQuoteById(id: string): Promise<Quote | null> {
  try {
    console.log(`[QuoteService] Attempting to find quote with ID: "${id}"`);
    console.log(`[QuoteService] ID type: ${typeof id}, length: ${id?.length}`);

    const quote = await prisma.quote.findUnique({
      where: { id },
      include: {
        items: {
          include: {
            service: true
          }
        }
      }
    });

    console.log(`[QuoteService] Database query result:`, quote ? 'Found' : 'Not found');

    if (!quote) {
      console.log(`[QuoteService] Quote with ID "${id}" not found in database`);

      // Let's check what quotes exist
      const allQuotes = await prisma.quote.findMany({
        select: { id: true, quoteNumber: true }
      });
      console.log(`[QuoteService] Available quotes in database:`, allQuotes);

      return null;
    }

    console.log(`[QuoteService] Found quote: ${quote.quoteNumber} (ID: ${quote.id})`);
    return formatQuote(quote);
  } catch (error) {
    console.error(`[QuoteService] Error getting quote ${id}:`, error);
    console.error(`[QuoteService] Error stack:`, error instanceof Error ? error.stack : 'No stack trace');
    throw error;
  }
}

/**
 * Generate a unique quote number
 * @returns A unique quote number
 */
export async function generateQuoteNumber(): Promise<string> {
  const date = new Date();
  const year = date.getFullYear().toString().slice(-2);
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');

  let count = 0;
  try {
    // Get the count of quotes for today
    const todayStart = new Date(date.getFullYear(), date.getMonth(), date.getDate());
    const todayEnd = new Date(date.getFullYear(), date.getMonth(), date.getDate() + 1);

    // Try to count quotes, handle potential errors
    try {
      count = await prisma.quote.count({
        where: {
          createdAt: {
            gte: todayStart,
            lt: todayEnd
          }
        }
      });
    } catch (error) {
      console.error('Error counting quotes:', error);
      // If there's an error, assume count is 0
      count = 0;
    }
  } catch (error) {
    console.error('Error in generateQuoteNumber:', error);
    // If there's any error, use a fallback count
    count = 0;
  }

  // Format: QUO-YY-MM-DD-XXX where XXX is a sequential number
  const sequentialNumber = (count + 1).toString().padStart(3, '0');
  return `QUO-${year}${month}${day}-${sequentialNumber}`;
}

/**
 * Create a new quote
 * @param data The quote data
 * @returns The created quote
 */
export async function createQuote(data: {
  customerName: string;
  phoneNumber: string;
  email?: string;
  notes?: string;
  validUntil?: Date;
  discountType?: string;
  discountValue?: number;
  items: {
    serviceId?: string;
    quantity: number;
    unitPrice?: number;
    description?: string;
    itemName?: string;
    itemType?: string;
  }[];
}): Promise<Quote | null> {
  try {
    // Process items
    const quoteItems = [];
    let subtotalAmount = 0;

    for (const item of data.items) {
      const itemType = item.itemType || 'service';
      let unitPrice = item.unitPrice || 0;
      let itemName = item.itemName;
      let description = item.description;

      if (itemType === 'service' && item.serviceId) {
        // Service-based item
        const service = await getServiceById(item.serviceId);
        if (!service) {
          throw new Error(`Service ${item.serviceId} not found`);
        }
        
        // Use provided unit price or service price
        unitPrice = item.unitPrice !== undefined ? item.unitPrice : Number(service.price);
        itemName = service.name;
        description = item.description || service.description;
      } else if (itemType === 'custom') {
        // Custom item
        if (!item.itemName) {
          throw new Error('Custom items must have an item name');
        }
        unitPrice = item.unitPrice || 0;
        itemName = item.itemName;
        description = item.description;
      }

      const totalPrice = unitPrice * item.quantity;
      subtotalAmount += totalPrice;

      // Add to quote items
      quoteItems.push({
        serviceId: item.serviceId,
        quantity: item.quantity,
        unitPrice,
        totalPrice,
        description,
        itemName,
        itemType
      });
    }

    // Calculate discount
    const discountAmount = calculateDiscount(subtotalAmount, data.discountType, data.discountValue);
    const totalAmount = subtotalAmount - discountAmount;

    // Generate quote number
    const quoteNumber = await generateQuoteNumber();

    // Set valid until date (default to 30 days from now if not provided)
    const validUntil = data.validUntil || addDays(new Date(), 30);

    // Create the quote
    const quote = await prisma.quote.create({
      data: {
        quoteNumber,
        totalAmount,
        subtotalAmount,
        discountType: data.discountType || null,
        discountValue: data.discountValue || null,
        discountAmount,
        customerName: data.customerName,
        phoneNumber: data.phoneNumber,
        email: data.email,
        status: 'pending',
        notes: data.notes || null,
        issuedAt: new Date(),
        validUntil,
        items: {
          create: quoteItems
        }
      },
      include: {
        items: true
      }
    });

    // Format the quote
    const formattedQuote = formatQuote(quote);

    // Generate PDF
    try {
      await generateQuoteWithPDF(formattedQuote);
    } catch (pdfError) {
      console.error('Failed to generate PDF for quote:', pdfError);
      // Don't fail the quote creation if PDF generation fails
    }

    return formattedQuote;
  } catch (error) {
    console.error('Error creating quote:', error);
    throw error;
  }
}

/**
 * Delete a quote by ID
 * @param quoteId The quote ID to delete
 * @returns True if deleted successfully, false if not found
 */
export async function deleteQuote(quoteId: string): Promise<boolean> {
  try {
    console.log(`[Quote Service] Attempting to delete quote: ${quoteId}`);
    console.log(`[Quote Service] Quote ID type: ${typeof quoteId}, length: ${quoteId?.length}`);

    if (!quoteId || typeof quoteId !== 'string') {
      console.error(`[Quote Service] Invalid quote ID: ${quoteId}`);
      return false;
    }

    // First, check if the quote exists
    const existingQuote = await prisma.quote.findUnique({
      where: { id: quoteId },
      include: {
        items: true
      }
    });

    if (!existingQuote) {
      console.log(`[Quote Service] Quote not found: ${quoteId}`);
      // Let's check what quotes exist
      const allQuotes = await prisma.quote.findMany({
        select: { id: true, quoteNumber: true }
      });
      console.log(`[Quote Service] Available quotes:`, allQuotes);
      return false;
    }

    console.log(`[Quote Service] Found quote: ${existingQuote.quoteNumber} (ID: ${existingQuote.id})`);
    console.log(`[Quote Service] Quote items count: ${existingQuote.items?.length || 0}`);

    // Check if quote has an associated invoice
    const associatedInvoice = await prisma.invoice.findFirst({
      where: { quoteId: quoteId },
      select: { id: true, invoiceNumber: true }
    });

    if (associatedInvoice) {
      console.log(`[Quote Service] Quote has invoice: ${associatedInvoice.invoiceNumber}`);
      throw new Error('Cannot delete quote that has been converted to an invoice');
    }

    // Delete the quote and its items in a transaction
    console.log(`[Quote Service] Starting delete transaction`);
    await prisma.$transaction(async (tx) => {
      // Delete quote items first (due to foreign key constraints)
      console.log(`[Quote Service] Deleting quote items`);
      const deletedItems = await tx.quoteItem.deleteMany({
        where: { quoteId: quoteId }
      });
      console.log(`[Quote Service] Deleted ${deletedItems.count} quote items`);

      // Delete the quote
      console.log(`[Quote Service] Deleting quote`);
      await tx.quote.delete({
        where: { id: quoteId }
      });
      console.log(`[Quote Service] Quote deleted from database`);
    });

    // Clean up PDF file if it exists
    if (existingQuote.pdfUrl) {
      try {
        const pdfPath = path.join(process.cwd(), 'public', existingQuote.pdfUrl);
        console.log(`[Quote Service] Attempting to delete PDF file: ${pdfPath}`);
        await fs.unlink(pdfPath);
        console.log(`[Quote Service] PDF file deleted: ${pdfPath}`);
      } catch (fileError) {
        console.warn(`[Quote Service] Could not delete PDF file: ${existingQuote.pdfUrl}`, fileError);
        // Don't fail the deletion if PDF cleanup fails
      }
    }

    console.log(`[Quote Service] Quote deleted successfully: ${quoteId}`);
    return true;
  } catch (error) {
    console.error(`[Quote Service] Error deleting quote ${quoteId}:`, error);
    console.error(`[Quote Service] Error stack:`, error instanceof Error ? error.stack : 'No stack trace');
    throw error;
  }
}

/**
 * Update a quote's status
 * @param id The quote ID
 * @param status The new status
 * @returns The updated quote
 */
export async function updateQuoteStatus(id: string, status: string): Promise<Quote | null> {
  try {
    const quote = await prisma.quote.findUnique({
      where: { id },
      include: {
        items: true
      }
    });

    if (!quote) {
      return null;
    }

    // Update the quote
    const updatedQuote = await prisma.quote.update({
      where: { id },
      data: {
        status
      },
      include: {
        items: {
          include: {
            service: true
          }
        }
      }
    });

    return formatQuote(updatedQuote);
  } catch (error) {
    console.error(`Error updating quote status ${id}:`, error);
    throw error;
  }
}

/**
 * Convert a quote to an invoice
 * @param quoteId The quote ID
 * @returns The created invoice
 */
export async function convertQuoteToInvoice(quoteId: string): Promise<any> {
  try {
    const quote = await getQuoteById(quoteId);

    if (!quote) {
      throw new Error(`Quote ${quoteId} not found`);
    }

    // Check if quote is already converted to an invoice
    const existingInvoice = await prisma.invoice.findFirst({
      where: {
        quoteId
      }
    });

    if (existingInvoice) {
      throw new Error(`Quote ${quoteId} is already converted to invoice ${existingInvoice.invoiceNumber}`);
    }

    // Convert quote to invoice
    const invoice = await createInvoice({
      customerName: quote.customerName,
      phoneNumber: quote.phoneNumber,
      email: quote.email || undefined,
      notes: quote.notes || undefined,
      items: quote.items.map(item => ({
        serviceId: item.serviceId,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        description: item.description || undefined
      })),
      quoteId: quote.id
    });

    // Update quote status to 'accepted'
    await updateQuoteStatus(quoteId, 'accepted');

    return invoice;
  } catch (error) {
    console.error(`Error converting quote ${quoteId} to invoice:`, error);
    throw error;
  }
}

/**
 * Format a quote from the database
 * @param quote The quote from the database
 * @returns The formatted quote
 */
function formatQuote(quote: any): Quote {
  return {
    id: quote.id,
    quoteNumber: quote.quoteNumber,
    totalAmount: Number(quote.totalAmount),
    subtotalAmount: Number(quote.subtotalAmount || 0),
    discountType: quote.discountType,
    discountValue: quote.discountValue ? Number(quote.discountValue) : null,
    discountAmount: Number(quote.discountAmount || 0),
    customerName: quote.customerName,
    phoneNumber: quote.phoneNumber,
    email: quote.email,
    status: quote.status,
    notes: quote.notes,
    createdAt: new Date(quote.createdAt),
    updatedAt: new Date(quote.updatedAt),
    issuedAt: new Date(quote.issuedAt),
    validUntil: new Date(quote.validUntil),
    pdfUrl: quote.pdfUrl,
    items: quote.items.map((item: any) => ({
      id: item.id,
      quantity: item.quantity,
      unitPrice: Number(item.unitPrice),
      totalPrice: Number(item.totalPrice),
      description: item.description,
      itemName: item.itemName,
      itemType: item.itemType || 'service',
      serviceId: item.serviceId,
      serviceName: item.service?.name
    }))
  };
}

/**
 * Map a quote to quote data for PDF generation
 * @param quote The quote
 * @returns The quote data for PDF generation
 */
function mapToQuoteData(quote: Quote): QuoteData {
  return {
    quoteNumber: quote.quoteNumber,
    customerName: quote.customerName,
    phoneNumber: quote.phoneNumber,
    email: quote.email || '',
    totalAmount: quote.totalAmount,
    issuedAt: quote.issuedAt.toISOString(),
    validUntil: quote.validUntil.toISOString(),
    status: quote.status,
    notes: quote.notes || '',
    items: quote.items.map(item => ({
      description: item.description || '',
      quantity: item.quantity,
      unitPrice: item.unitPrice,
      totalPrice: item.totalPrice,
      serviceName: item.serviceName || ''
    }))
  };
}
