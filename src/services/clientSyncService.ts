import prisma from '@/lib/prisma';

/**
 * Sync clients from receipts
 * This function automatically creates client records based on customer data from receipts
 */
export async function syncClientsFromOrdersAndReceipts() {
  try {
    console.log('[Client Sync] Starting sync process...');

    // Get all receipts with customer data
    const receipts = await prisma.receipt.findMany({
      select: {
        id: true,
        receiptNumber: true,
        customerName: true,
        email: true,
        phoneNumber: true,
        createdAt: true,
      },
      where: {
        customerName: { not: null },
      },
    });

    console.log(`[Client Sync] Found ${receipts.length} receipts with customer data`);

    let created = 0;
    let updated = 0;
    let skipped = 0;

    for (const receipt of receipts) {
      if (!receipt.customerName?.trim()) continue;

      try {
        // Check if client already exists by exact name match
        const existingClient = await prisma.client.findFirst({
          where: {
            name: receipt.customerName.trim(),
          },
        });

        if (existingClient) {
          // Update existing client with any missing information
          const updateData: any = {};
          
          if (!existingClient.email && receipt.email) {
            updateData.email = receipt.email;
          }
          
          if (!existingClient.phone && receipt.phoneNumber) {
            updateData.phone = receipt.phoneNumber;
          }

          if (Object.keys(updateData).length > 0) {
            await prisma.client.update({
              where: { id: existingClient.id },
              data: {
                ...updateData,
                notes: existingClient.notes 
                  ? `${existingClient.notes}\n\nAuto-updated from receipt: ${receipt.receiptNumber}`
                  : `Auto-updated from receipt: ${receipt.receiptNumber}`,
              },
            });
            updated++;
            console.log(`[Client Sync] Updated client: ${receipt.customerName}`);
          } else {
            skipped++;
          }
        } else {
          // Create new client
          await prisma.client.create({
            data: {
              name: receipt.customerName.trim(),
              email: receipt.email || undefined,
              phone: receipt.phoneNumber || undefined,
              status: 'active',
              notes: `Auto-created from receipt: ${receipt.receiptNumber}`,
            },
          });
          created++;
          console.log(`[Client Sync] Created new client: ${receipt.customerName}`);
        }
      } catch (error) {
        console.error(`[Client Sync] Error processing customer ${receipt.customerName}:`, error);
      }
    }

    console.log(`[Client Sync] Completed - Created: ${created}, Updated: ${updated}, Skipped: ${skipped}`);

    return {
      success: true,
      stats: {
        totalCustomers: receipts.length,
        created,
        updated,
        skipped,
      },
    };
  } catch (error) {
    console.error('[Client Sync] Error during sync:', error);
    throw error;
  }
}

/**
 * Get client statistics
 */
export async function getClientStatistics() {
  try {
    const [
      totalClients,
      totalReceipts,
      uniqueReceiptCustomers,
    ] = await Promise.all([
      prisma.client.count(),
      prisma.receipt.count(),
      prisma.receipt.groupBy({
        by: ['customerName'],
        where: {
          customerName: { not: null },
        },
      }),
    ]);

    return {
      totalClients,
      totalOrders: 0,
      totalReceipts,
      uniqueOrderCustomers: 0,
      uniqueReceiptCustomers: uniqueReceiptCustomers.length,
    };
  } catch (error) {
    console.error('[Client Sync] Error getting statistics:', error);
    throw error;
  }
} 