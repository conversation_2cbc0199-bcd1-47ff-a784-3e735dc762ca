import { cacheService, CacheKeys, CacheTTL } from '@/lib/redis';
import { getRedisClient } from '@/lib/redis';

export interface AnalyticsMetric {
  id: string;
  metric: string;
  value: number;
  period: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}

export interface AnalyticsData {
  pageViews: number;
  uniqueVisitors: number;
  portfolioViews: number;
  blogViews: number;
  downloadCount: number;
  contactSubmissions: number;
  averageSessionDuration: number;
  bounceRate: number;
  topPages: { path: string; views: number }[];
  topReferrers: { source: string; visits: number }[];
  deviceTypes: { type: string; count: number }[];
  geographicData: { country: string; visits: number }[];
}

export interface PerformanceMetrics {
  apiResponseTimes: { endpoint: string; avgTime: number; requestCount: number }[];
  cacheHitRates: { service: string; hitRate: number }[];
  errorRates: { endpoint: string; errorRate: number }[];
  systemHealth: {
    cpuUsage: number;
    memoryUsage: number;
    diskUsage: number;
    uptime: number;
  };
}

class AnalyticsService {
  private redis = getRedisClient();

  // Track page views
  async trackPageView(path: string, userAgent?: string, ip?: string): Promise<void> {
    try {
      const today = new Date().toISOString().split('T')[0];
      const hour = new Date().getHours();
      
      // Increment daily page views
      await this.incrementMetric('page_views', today);
      
      // Increment hourly page views
      await this.incrementMetric('page_views_hourly', `${today}_${hour}`);
      
      // Track specific page views
      await this.incrementMetric('page_views', `${today}_${path}`);
      
      // Track unique visitors (based on IP if available)
      if (ip) {
        const uniqueKey = `unique_visitors:${today}`;
        if (this.redis) {
          await this.redis.sadd(uniqueKey, ip);
          await this.redis.expire(uniqueKey, 86400); // Expire after 1 day
        }
      }

      // Track device type based on user agent
      if (userAgent) {
        const deviceType = this.getDeviceType(userAgent);
        await this.incrementMetric('device_types', `${today}_${deviceType}`);
      }

      console.log(`Page view tracked: ${path}`);
    } catch (error) {
      console.error('Error tracking page view:', error);
    }
  }

  // Track portfolio item views
  async trackPortfolioView(itemId: string, category: string): Promise<void> {
    try {
      const today = new Date().toISOString().split('T')[0];
      
      // Track portfolio views
      await this.incrementMetric('portfolio_views', today);
      await this.incrementMetric('portfolio_views', `${today}_${category}`);
      await this.incrementMetric('portfolio_item_views', `${today}_${itemId}`);
      
      console.log(`Portfolio view tracked: ${itemId} (${category})`);
    } catch (error) {
      console.error('Error tracking portfolio view:', error);
    }
  }

  // Track blog views
  async trackBlogView(slug: string): Promise<void> {
    try {
      const today = new Date().toISOString().split('T')[0];
      
      // Track blog views
      await this.incrementMetric('blog_views', today);
      await this.incrementMetric('blog_post_views', `${today}_${slug}`);
      
      console.log(`Blog view tracked: ${slug}`);
    } catch (error) {
      console.error('Error tracking blog view:', error);
    }
  }

  // Track downloads
  async trackDownload(filename: string, category?: string): Promise<void> {
    try {
      const today = new Date().toISOString().split('T')[0];
      
      await this.incrementMetric('downloads', today);
      await this.incrementMetric('file_downloads', `${today}_${filename}`);
      
      if (category) {
        await this.incrementMetric('downloads_by_category', `${today}_${category}`);
      }
      
      console.log(`Download tracked: ${filename}`);
    } catch (error) {
      console.error('Error tracking download:', error);
    }
  }

  // Track contact form submissions
  async trackContactSubmission(type: string = 'general'): Promise<void> {
    try {
      const today = new Date().toISOString().split('T')[0];
      
      await this.incrementMetric('contact_submissions', today);
      await this.incrementMetric('contact_submissions_by_type', `${today}_${type}`);
      
      console.log(`Contact submission tracked: ${type}`);
    } catch (error) {
      console.error('Error tracking contact submission:', error);
    }
  }

  // Track API response times
  async trackApiResponseTime(endpoint: string, responseTime: number): Promise<void> {
    try {
      const today = new Date().toISOString().split('T')[0];
      const key = `api_response_times:${today}:${endpoint}`;
      
      if (this.redis) {
        // Store response time data as a list for calculating averages
        await this.redis.lpush(key, responseTime);
        await this.redis.ltrim(key, 0, 999); // Keep last 1000 entries
        await this.redis.expire(key, 86400); // Expire after 1 day
      }
    } catch (error) {
      console.error('Error tracking API response time:', error);
    }
  }

  // Track cache hit rates
  async trackCacheHit(service: string, hit: boolean): Promise<void> {
    try {
      const today = new Date().toISOString().split('T')[0];
      
      await this.incrementMetric('cache_requests', `${today}_${service}`);
      
      if (hit) {
        await this.incrementMetric('cache_hits', `${today}_${service}`);
      }
    } catch (error) {
      console.error('Error tracking cache hit:', error);
    }
  }

  // Get analytics data for a specific period
  async getAnalyticsData(period: string = 'today'): Promise<AnalyticsData> {
    try {
      const cacheKey = CacheKeys.analytics('dashboard', period);
      
      // Try to get from cache first
      const cachedData = await cacheService.get<AnalyticsData>(cacheKey);
      if (cachedData) {
        console.log(`Analytics data for ${period} retrieved from cache`);
        return cachedData;
      }

      console.log(`Calculating analytics data for ${period}...`);
      
      const dates = this.getDateRange(period);
      const data: AnalyticsData = {
        pageViews: 0,
        uniqueVisitors: 0,
        portfolioViews: 0,
        blogViews: 0,
        downloadCount: 0,
        contactSubmissions: 0,
        averageSessionDuration: 0,
        bounceRate: 0,
        topPages: [],
        topReferrers: [],
        deviceTypes: [],
        geographicData: [],
      };

      // Calculate metrics for each date in the range
      for (const date of dates) {
        data.pageViews += await this.getMetric('page_views', date) || 0;
        data.portfolioViews += await this.getMetric('portfolio_views', date) || 0;
        data.blogViews += await this.getMetric('blog_views', date) || 0;
        data.downloadCount += await this.getMetric('downloads', date) || 0;
        data.contactSubmissions += await this.getMetric('contact_submissions', date) || 0;

        // Count unique visitors
        if (this.redis) {
          const uniqueCount = await this.redis.scard(`unique_visitors:${date}`);
          data.uniqueVisitors += uniqueCount;
        }
      }

      // Calculate device type distribution
      const deviceTypes = ['desktop', 'mobile', 'tablet'];
      for (const deviceType of deviceTypes) {
        let count = 0;
        for (const date of dates) {
          count += await this.getMetric('device_types', `${date}_${deviceType}`) || 0;
        }
        if (count > 0) {
          data.deviceTypes.push({ type: deviceType, count });
        }
      }

      // Cache the calculated data
      await cacheService.set(cacheKey, data, CacheTTL.SHORT);
      console.log(`Analytics data for ${period} cached`);

      return data;
    } catch (error) {
      console.error('Error getting analytics data:', error);
      return this.getEmptyAnalyticsData();
    }
  }

  // Get performance metrics
  async getPerformanceMetrics(): Promise<PerformanceMetrics> {
    try {
      const cacheKey = CacheKeys.analytics('performance', 'current');
      
      // Try to get from cache first
      const cachedData = await cacheService.get<PerformanceMetrics>(cacheKey);
      if (cachedData) {
        console.log('Performance metrics retrieved from cache');
        return cachedData;
      }

      console.log('Calculating performance metrics...');
      
      const today = new Date().toISOString().split('T')[0];
      const metrics: PerformanceMetrics = {
        apiResponseTimes: [],
        cacheHitRates: [],
        errorRates: [],
        systemHealth: {
          cpuUsage: 0,
          memoryUsage: 0,
          diskUsage: 0,
          uptime: process.uptime(),
        },
      };

      // Calculate API response times
      if (this.redis) {
        const endpoints = [
          '/api/portfolio',
          '/api/admin/portfolio',
          '/api/blog',
          '/api/admin/blog',
          '/api/admin/stats',
        ];

        for (const endpoint of endpoints) {
          const key = `api_response_times:${today}:${endpoint}`;
          const times = await this.redis.lrange(key, 0, -1);
          
          if (times.length > 0) {
            const avgTime = times.reduce((sum, time) => sum + parseFloat(time), 0) / times.length;
            metrics.apiResponseTimes.push({
              endpoint,
              avgTime: Math.round(avgTime * 100) / 100,
              requestCount: times.length,
            });
          }
        }

        // Calculate cache hit rates
        const services = ['portfolio', 'blog', 'images', 'analytics'];
        for (const service of services) {
          const requests = await this.getMetric('cache_requests', `${today}_${service}`) || 0;
          const hits = await this.getMetric('cache_hits', `${today}_${service}`) || 0;
          
          if (requests > 0) {
            const hitRate = Math.round((hits / requests) * 100 * 100) / 100;
            metrics.cacheHitRates.push({ service, hitRate });
          }
        }
      }

      // Get system health (basic Node.js metrics)
      const memUsage = process.memoryUsage();
      metrics.systemHealth.memoryUsage = Math.round((memUsage.heapUsed / memUsage.heapTotal) * 100);

      // Cache the metrics for a shorter period
      await cacheService.set(cacheKey, metrics, CacheTTL.SHORT);
      console.log('Performance metrics cached');

      return metrics;
    } catch (error) {
      console.error('Error getting performance metrics:', error);
      return {
        apiResponseTimes: [],
        cacheHitRates: [],
        errorRates: [],
        systemHealth: {
          cpuUsage: 0,
          memoryUsage: 0,
          diskUsage: 0,
          uptime: 0,
        },
      };
    }
  }

  // Helper method to increment a metric
  private async incrementMetric(metric: string, key: string, amount: number = 1): Promise<void> {
    if (this.redis) {
      await this.redis.incrby(`metrics:${metric}:${key}`, amount);
      // Set expiration for metrics (30 days)
      await this.redis.expire(`metrics:${metric}:${key}`, 30 * 24 * 60 * 60);
    }
  }

  // Helper method to get a metric value
  private async getMetric(metric: string, key: string): Promise<number> {
    if (this.redis) {
      const value = await this.redis.get(`metrics:${metric}:${key}`);
      return value ? parseInt(value, 10) : 0;
    }
    return 0;
  }

  // Helper method to determine device type from user agent
  private getDeviceType(userAgent: string): string {
    const ua = userAgent.toLowerCase();
    
    if (ua.includes('mobile') || ua.includes('android') || ua.includes('iphone')) {
      return 'mobile';
    } else if (ua.includes('tablet') || ua.includes('ipad')) {
      return 'tablet';
    } else {
      return 'desktop';
    }
  }

  // Helper method to get date range for different periods
  private getDateRange(period: string): string[] {
    const dates: string[] = [];
    const today = new Date();
    
    switch (period) {
      case 'today':
        dates.push(today.toISOString().split('T')[0]);
        break;
      case 'week':
        for (let i = 6; i >= 0; i--) {
          const date = new Date(today);
          date.setDate(date.getDate() - i);
          dates.push(date.toISOString().split('T')[0]);
        }
        break;
      case 'month':
        for (let i = 29; i >= 0; i--) {
          const date = new Date(today);
          date.setDate(date.getDate() - i);
          dates.push(date.toISOString().split('T')[0]);
        }
        break;
      default:
        dates.push(today.toISOString().split('T')[0]);
    }
    
    return dates;
  }

  // Helper method to get empty analytics data
  private getEmptyAnalyticsData(): AnalyticsData {
    return {
      pageViews: 0,
      uniqueVisitors: 0,
      portfolioViews: 0,
      blogViews: 0,
      downloadCount: 0,
      contactSubmissions: 0,
      averageSessionDuration: 0,
      bounceRate: 0,
      topPages: [],
      topReferrers: [],
      deviceTypes: [],
      geographicData: [],
    };
  }

  // Clear analytics cache
  async clearCache(): Promise<void> {
    try {
      const periods = ['today', 'week', 'month'];
      const metrics = ['dashboard', 'performance'];
      
      for (const metric of metrics) {
        for (const period of periods) {
          await cacheService.del(CacheKeys.analytics(metric, period));
        }
      }
      
      console.log('Analytics cache cleared');
    } catch (error) {
      console.error('Error clearing analytics cache:', error);
    }
  }
}

// Export singleton instance
export const analyticsService = new AnalyticsService();

// Export middleware for tracking API calls
export function withAnalytics(
  handler: Function,
  endpoint: string
): Function {
  return async function(...args: any[]) {
    const startTime = Date.now();
    
    try {
      const result = await handler(...args);
      
      // Track successful API call
      const responseTime = Date.now() - startTime;
      await analyticsService.trackApiResponseTime(endpoint, responseTime);
      
      return result;
    } catch (error) {
      // Track API error
      const responseTime = Date.now() - startTime;
      await analyticsService.trackApiResponseTime(endpoint, responseTime);
      throw error;
    }
  };
} 