/**
 * Simple error logger service to replace the deleted tracking functionality
 * This just logs errors to the console instead of tracking them
 */
export class ErrorTracker {
  static trackError(error: Error, context?: string, metadata?: any): void {
    console.error(`[<PERSON>rrorLogger] ${context || 'Unknown context'}:`, error);
    if (metadata) {
      console.error('[ErrorLogger] Metadata:', metadata);
    }
  }
} 