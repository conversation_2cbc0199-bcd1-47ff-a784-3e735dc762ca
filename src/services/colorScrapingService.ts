import puppeteer from 'puppeteer';
import * as cheerio from 'cheerio';

export interface ColorPalette {
  id: string;
  name: string;
  colors: string[];
  likes?: number;
  tags?: string[];
  url?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface ScrapingResult {
  success: boolean;
  palettes: ColorPalette[];
  error?: string;
  timestamp: Date;
}

/**
 * Scrape trending color palettes from Coolors.co
 */
export async function scrapeTrendingPalettes(): Promise<ScrapingResult> {
  let browser;

  try {
    console.log('Starting color palette scraping...');

    // Launch browser with optimized settings for WSL/containerized environments
    const launchOptions: any = {
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--single-process',
        '--disable-gpu',
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor',
        '--disable-extensions',
        '--disable-plugins',
        '--disable-default-apps',
        '--disable-sync',
        '--disable-translate',
        '--hide-scrollbars',
        '--mute-audio',
        '--no-default-browser-check',
        '--no-pings',
        '--disable-background-timer-throttling',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding',
        '--disable-blink-features=AutomationControlled',
        '--disable-client-side-phishing-detection',
        '--disable-component-extensions-with-background-pages',
        '--disable-default-apps',
        '--disable-hang-monitor',
        '--disable-ipc-flooding-protection',
        '--disable-popup-blocking',
        '--disable-prompt-on-repost',
        '--disable-sync',
        '--disable-translate',
        '--metrics-recording-only',
        '--no-first-run',
        '--safebrowsing-disable-auto-update',
        '--enable-automation',
        '--password-store=basic',
        '--use-mock-keychain'
      ],
      timeout: 30000
    };

    // Only add executablePath if it's defined
    if (process.env.PUPPETEER_EXECUTABLE_PATH) {
      launchOptions.executablePath = process.env.PUPPETEER_EXECUTABLE_PATH;
    }

    browser = await puppeteer.launch(launchOptions);

    const page = await browser.newPage();

    // Set page timeouts and configurations
    await page.setDefaultTimeout(15000);
    await page.setDefaultNavigationTimeout(15000);

    // Set viewport and user agent
    await page.setViewport({ width: 1280, height: 720 });
    await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');

    // Block unnecessary resources to speed up loading and reduce memory usage
    await page.setRequestInterception(true);
    page.on('request', (req: any) => {
      const resourceType = req.resourceType();
      if (resourceType === 'image' || resourceType === 'media' || resourceType === 'font' || resourceType === 'stylesheet') {
        req.abort();
      } else {
        req.continue();
      }
    });

    // Navigate to trending palettes page with retry logic
    console.log('Navigating to Coolors.co trending page...');
    let retryCount = 0;
    const maxRetries = 2;
    
    while (retryCount < maxRetries) {
      try {
        // Try navigation
        await page.goto('https://coolors.co/palettes/trending', {
          waitUntil: 'domcontentloaded',
          timeout: 12000
        });
        
        // Wait for palettes to load with multiple possible selectors
        await page.waitForSelector('.palette-card, .palette, [data-testid="palette"], .color-palette, .palette-container', { 
          timeout: 8000 
        });
        
        // Small delay for any dynamic content
        await new Promise(resolve => setTimeout(resolve, 1500));
        break;
        
      } catch (error) {
        retryCount++;
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        console.log(`Navigation attempt ${retryCount} failed:`, errorMessage);
        
        if (retryCount >= maxRetries) {
          // Instead of throwing, let's try to proceed with what we have
          console.log('Navigation failed, attempting to extract any available content...');
          break;
        }
        
        // Wait before retry
        await new Promise(resolve => setTimeout(resolve, 1500));
      }
    }

    // Extract palettes with better error handling
    let palettes: any[] = [];
    
    try {
      // Check if page and browser are still alive before evaluation
      if (page.isClosed()) {
        console.log('Page was closed, cannot extract palettes');
        throw new Error('Page was closed during scraping');
      }
      
      // Try to get the page content first to ensure it's responsive
      await page.content();
      
      console.log('Attempting to extract color palettes from page...');
      palettes = await page.evaluate(() => {
        // Try multiple possible selectors for palette cards
        const possibleSelectors = [
          '.palette-card',
          '.palette',
          '[data-testid="palette"]',
          '.color-palette',
          '.palette-item'
        ];
        
        let paletteCards: NodeListOf<Element> | null = null;
        
        for (const selector of possibleSelectors) {
          paletteCards = document.querySelectorAll(selector);
          if (paletteCards.length > 0) {
            console.log(`Found ${paletteCards.length} palettes using selector: ${selector}`);
            break;
          }
        }
        
        if (!paletteCards || paletteCards.length === 0) {
          console.log('No palette cards found with any selector');
          return [];
        }
        
        const extractedPalettes: any[] = [];

      paletteCards.forEach((card, index) => {
        try {
          // Try to find color swatches within the card
          const colorElements = card.querySelectorAll('[style*="background-color"], .color-swatch, .swatch');
          const colors: string[] = [];

          colorElements.forEach((colorEl) => {
            const style = (colorEl as HTMLElement).style.backgroundColor;
            if (style) {
              // Convert rgb to hex if needed
              if (style.startsWith('rgb')) {
                const rgb = style.match(/\d+/g);
                if (rgb && rgb.length >= 3) {
                  const hex = '#' + rgb.slice(0, 3).map(x => {
                    const hex = parseInt(x).toString(16);
                    return hex.length === 1 ? '0' + hex : hex;
                  }).join('');
                  colors.push(hex);
                }
              } else if (style.startsWith('#')) {
                colors.push(style);
              }
            }
          });

          // Alternative: look for data attributes or other color indicators
          if (colors.length === 0) {
            const allElements = card.querySelectorAll('*');
            allElements.forEach((el) => {
              const bgColor = window.getComputedStyle(el).backgroundColor;
              if (bgColor && bgColor !== 'rgba(0, 0, 0, 0)' && bgColor !== 'transparent') {
                if (bgColor.startsWith('rgb')) {
                  const rgb = bgColor.match(/\d+/g);
                  if (rgb && rgb.length >= 3) {
                    const hex = '#' + rgb.slice(0, 3).map(x => {
                      const hex = parseInt(x).toString(16);
                      return hex.length === 1 ? '0' + hex : hex;
                    }).join('');
                    if (hex !== '#ffffff' && hex !== '#000000' && !colors.includes(hex)) {
                      colors.push(hex);
                    }
                  }
                }
              }
            });
          }

          // Get palette name
          const nameEl = card.querySelector('.palette-name, .title, h3, h4, [data-testid="palette-name"]');
          const name = nameEl ? nameEl.textContent?.trim() : `Trending Palette ${index + 1}`;

          // Get likes if available
          const likesEl = card.querySelector('.likes, .like-count, [data-testid="likes"]');
          const likes = likesEl ? parseInt(likesEl.textContent?.replace(/\D/g, '') || '0') : 0;

          if (colors.length >= 3) {
            extractedPalettes.push({
              id: `coolors-${Date.now()}-${index}`,
              name: name || `Trending Palette ${index + 1}`,
              colors: [...new Set(colors)].slice(0, 5), // Remove duplicates and limit to 5
              likes: likes,
              createdAt: new Date(),
              updatedAt: new Date()
            });
          }
        } catch (error) {
          console.error(`Error processing palette ${index}:`, error);
        }
      });

        return extractedPalettes;
      });
      
    } catch (evaluationError) {
      console.error('Error during page evaluation:', evaluationError);
      console.log('Evaluation failed, trying alternative extraction method...');
      
      // Try alternative method using existing JSON data if available
      try {
        const jsonData = await page.evaluate(() => {
          // Look for any JSON data in script tags that might contain palette info
          const scripts = document.querySelectorAll('script');
          for (let script of scripts) {
            if (script.textContent && script.textContent.includes('palette')) {
              return script.textContent;
            }
          }
          return null;
        });
        
        if (jsonData) {
          console.log('Found potential JSON data, but extraction failed');
        }
      } catch (altError) {
        console.log('Alternative extraction also failed');
      }
      
      palettes = []; // Use empty array if evaluation fails
    }

    console.log(`Successfully extracted ${palettes.length} color palettes`);

    // If we didn't get any palettes from scraping, return with fallback flag
    if (palettes.length === 0) {
      console.log('No palettes extracted from scraping, API will use fallback palettes');
      return {
        success: false,
        palettes: [],
        error: 'No palettes found on the page - will use fallback palettes',
        timestamp: new Date()
      };
    }

    return {
      success: true,
      palettes: palettes,
      timestamp: new Date()
    };

  } catch (error) {
    console.error('Error scraping color palettes:', error);

    // Log additional error details for debugging
    if (error instanceof Error) {
      console.error('Error stack:', error.stack);
      console.error('Error name:', error.name);
    }

    return {
      success: false,
      palettes: [],
      error: error instanceof Error ? error.message : 'Unknown error occurred',
      timestamp: new Date()
    };
  } finally {
    // Ensure proper cleanup
    if (browser) {
      try {
        // Close all pages first
        const pages = await browser.pages();
        await Promise.all(pages.map(page => page.close().catch(() => {})));
        
        // Then close the browser
        await browser.close();
        console.log('Browser closed successfully');
      } catch (closeError) {
        console.error('Error closing browser:', closeError);
        
        // Force kill browser process if normal close fails
        try {
          if (browser.process()) {
            browser.process()?.kill('SIGKILL');
          }
        } catch (killError) {
          console.error('Error force killing browser:', killError);
        }
      }
    }
  }
}

/**
 * Generate fallback palettes if scraping fails
 */
export function generateFallbackPalettes(): ColorPalette[] {
  const fallbackPalettes = [
    {
      id: 'fallback-1',
      name: 'Ocean Breeze',
      colors: ['#0077BE', '#00A8CC', '#7FB3D3', '#C5E4FD', '#E8F4FD'],
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'fallback-2',
      name: 'Sunset Vibes',
      colors: ['#FF6B35', '#F7931E', '#FFD23F', '#06FFA5', '#118AB2'],
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'fallback-3',
      name: 'Forest Dreams',
      colors: ['#2D5016', '#61A5C2', '#A9D6E5', '#E9C46A', '#F4A261'],
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'fallback-4',
      name: 'Purple Haze',
      colors: ['#6A4C93', '#C06C84', '#F67280', '#F8B195', '#C06C84'],
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'fallback-5',
      name: 'Minimal Modern',
      colors: ['#1A237E', '#FF5400', '#FFFFFF', '#F5F5F5', '#333333'],
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'fallback-6',
      name: 'Coral Blush',
      colors: ['#FF6B9D', '#FF9F43', '#FCD474', '#C7ECEE', '#A8E6CF'],
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'fallback-7',
      name: 'Midnight Gold',
      colors: ['#2C3E50', '#34495E', '#F39C12', '#F1C40F', '#ECF0F1'],
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'fallback-8',
      name: 'Berry Mint',
      colors: ['#8E44AD', '#3498DB', '#1ABC9C', '#F39C12', '#E74C3C'],
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ];

  return fallbackPalettes;
}
