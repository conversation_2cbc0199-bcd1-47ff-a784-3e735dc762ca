/**
 * Shared Upload Session Store
 * Manages upload sessions across different API routes
 */

interface UploadSession {
  uploadId: string;
  fileName: string;
  fileSize: number;
  fileType: string;
  metadata?: Record<string, any> | undefined;
  chunks: Set<number>;
  totalChunks: number;
  tempDir: string;
  createdAt: number;
}

// Global session store that persists across API routes
const uploadSessions = new Map<string, UploadSession>();

// Cleanup interval
let cleanupInterval: NodeJS.Timeout | null = null;

export class UploadSessionStore {
  static get(uploadId: string): UploadSession | undefined {
    return uploadSessions.get(uploadId);
  }

  static set(uploadId: string, session: UploadSession): void {
    uploadSessions.set(uploadId, session);
    
    // Start cleanup interval if not already running
    if (!cleanupInterval) {
      cleanupInterval = setInterval(() => {
        this.cleanupOldSessions();
      }, 60 * 60 * 1000); // Run every hour
    }
  }

  static has(uploadId: string): boolean {
    return uploadSessions.has(uploadId);
  }

  static delete(uploadId: string): boolean {
    return uploadSessions.delete(uploadId);
  }

  static size(): number {
    return uploadSessions.size;
  }

  static keys(): string[] {
    return Array.from(uploadSessions.keys());
  }

  static values(): UploadSession[] {
    return Array.from(uploadSessions.values());
  }

  static entries(): [string, UploadSession][] {
    return Array.from(uploadSessions.entries());
  }

  static clear(): void {
    uploadSessions.clear();
  }

  /**
   * Clean up old upload sessions
   */
  static cleanupOldSessions(): void {
    const now = Date.now();
    const maxAge = 24 * 60 * 60 * 1000; // 24 hours
    let cleanedCount = 0;

    for (const [uploadId, session] of uploadSessions.entries()) {
      if (now - session.createdAt > maxAge) {
        console.log(`[UploadSessionStore] Cleaning up old session: ${uploadId}`);
        uploadSessions.delete(uploadId);
        cleanedCount++;
        
        // Clean up temp files
        try {
          const fs = require('fs').promises;
          fs.rmdir(session.tempDir, { recursive: true }).catch((error: any) => {
            console.warn(`[UploadSessionStore] Failed to cleanup temp dir: ${error}`);
          });
        } catch (error) {
          console.warn(`[UploadSessionStore] Failed to cleanup temp dir: ${error}`);
        }
      }
    }

    if (cleanedCount > 0) {
      console.log(`[UploadSessionStore] Cleaned up ${cleanedCount} old sessions`);
    }
  }

  /**
   * Get session statistics
   */
  static getStats(): {
    totalSessions: number;
    oldestSession: number | null;
    newestSession: number | null;
    averageAge: number;
  } {
    const sessions = Array.from(uploadSessions.values());
    const now = Date.now();
    
    if (sessions.length === 0) {
      return {
        totalSessions: 0,
        oldestSession: null,
        newestSession: null,
        averageAge: 0
      };
    }

    const ages = sessions.map(s => now - s.createdAt);
    const oldest = Math.max(...ages);
    const newest = Math.min(...ages);
    const average = ages.reduce((a, b) => a + b, 0) / ages.length;

    return {
      totalSessions: sessions.length,
      oldestSession: oldest,
      newestSession: newest,
      averageAge: average
    };
  }
}

export type { UploadSession }; 