/**
 * Service for managing invoices
 */
import prisma from '@/lib/prisma';
import { generateInvoicePDF, InvoiceData } from '@/utils/pdfGenerator';
import { getServiceById } from './serviceItemService';
import { FinancialCalculator } from '@/utils/financialCalculator';
import path from 'path';
import { promises as fs } from 'fs';
import { addDays } from 'date-fns';

export interface Invoice {
  id: string;
  invoiceNumber: string;
  totalAmount: number;
  amountPaid: number;
  balance: number;
  customerName: string;
  phoneNumber: string;
  email: string | null;
  status: string;
  notes: string | null;
  createdAt: Date;
  updatedAt: Date;
  issuedAt: Date;
  dueDate: Date;
  paidAt: Date | null;
  pdfUrl?: string;
  items: InvoiceItem[];
  quoteId?: string | null;
}

export interface InvoiceItem {
  id: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  description: string | null;
  serviceId: string;
  serviceName?: string;
}

/**
 * Get all invoices
 * @returns All invoices
 */
export async function getAllInvoices(): Promise<Invoice[]> {
  try {
    const invoices = await prisma.invoice.findMany({
      include: {
        items: {
          include: {
            service: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    return invoices.map(formatInvoice);
  } catch (error) {
    console.error('Error getting all invoices:', error);
    throw error;
  }
}

/**
 * Get an invoice by ID
 * @param id The invoice ID
 * @returns The invoice or null if not found
 */
export async function getInvoiceById(id: string): Promise<Invoice | null> {
  try {
    const invoice = await prisma.invoice.findUnique({
      where: { id },
      include: {
        items: {
          include: {
            service: true
          }
        }
      }
    });

    if (!invoice) {
      return null;
    }

    return formatInvoice(invoice);
  } catch (error) {
    console.error(`Error getting invoice ${id}:`, error);
    throw error;
  }
}

/**
 * Get an invoice by quote ID
 * @param quoteId The quote ID
 * @returns The invoice or null if not found
 */
export async function getInvoiceByQuoteId(quoteId: string): Promise<Invoice | null> {
  try {
    const invoice = await prisma.invoice.findUnique({
      where: { quoteId },
      include: {
        items: {
          include: {
            service: true
          }
        }
      }
    });

    if (!invoice) {
      return null;
    }

    return formatInvoice(invoice);
  } catch (error) {
    console.error(`Error getting invoice by quote ID ${quoteId}:`, error);
    throw error;
  }
}

/**
 * Generate a unique invoice number
 * @returns A unique invoice number
 */
export async function generateInvoiceNumber(): Promise<string> {
  const date = new Date();
  const year = date.getFullYear().toString().slice(-2);
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const day = date.getDate().toString().padStart(2, '0');

  let count = 0;
  try {
    // Get the count of invoices for today
    const todayStart = new Date(date.getFullYear(), date.getMonth(), date.getDate());
    const todayEnd = new Date(date.getFullYear(), date.getMonth(), date.getDate() + 1);

    // Try to count invoices, handle potential errors
    try {
      count = await prisma.invoice.count({
        where: {
          createdAt: {
            gte: todayStart,
            lt: todayEnd
          }
        }
      });
    } catch (error) {
      console.error('Error counting invoices:', error);
      // If there's an error, assume count is 0
      count = 0;
    }
  } catch (error) {
    console.error('Error in generateInvoiceNumber:', error);
    // If there's any error, use a fallback count
    count = 0;
  }

  // Format: INV-YY-MM-DD-XXX where XXX is a sequential number
  const sequentialNumber = (count + 1).toString().padStart(3, '0');
  return `INV-${year}${month}${day}-${sequentialNumber}`;
}

/**
 * Create a new invoice
 * @param data The invoice data
 * @returns The created invoice
 */
export async function createInvoice(data: {
  customerName: string;
  phoneNumber: string;
  email?: string;
  notes?: string;
  dueDate?: Date;
  items: {
    serviceId: string;
    quantity: number;
    unitPrice?: number;
    description?: string;
  }[];
  quoteId?: string;
}): Promise<Invoice | null> {
  try {
    console.log('Creating invoice with data:', JSON.stringify(data, null, 2));

    // Validate input data
    if (!data.customerName || !data.phoneNumber) {
      throw new Error('Customer name and phone number are required');
    }

    if (!data.items || !Array.isArray(data.items) || data.items.length === 0) {
      throw new Error('At least one item is required');
    }

    // Check if an invoice already exists for this quote
    if (data.quoteId) {
      console.log(`Checking if invoice already exists for quote: ${data.quoteId}`);
      const existingInvoice = await prisma.invoice.findUnique({
        where: { quoteId: data.quoteId }
      });

      if (existingInvoice) {
        throw new Error(`An invoice (${existingInvoice.invoiceNumber}) has already been created for this quote. Each quote can only be converted to one invoice.`);
      }
    }

    // Process items with precise financial calculations
    const invoiceItems = [];
    let totalAmount = 0;

    for (const item of data.items) {
      console.log(`Processing item: ${JSON.stringify(item)}`);

      // Validate item data
      if (!item.serviceId) {
        throw new Error('Service ID is required for each item');
      }

      if (!item.quantity || item.quantity <= 0) {
        throw new Error('Quantity must be greater than 0 for each item');
      }

      // Get service details
      const service = await getServiceById(item.serviceId);
      if (!service) {
        throw new Error(`Service with ID ${item.serviceId} not found`);
      }

      console.log(`Found service: ${service.name} - ${service.price}`);

      // Use provided unit price or service price with validation
      const unitPrice = item.unitPrice !== undefined ? item.unitPrice : Number(service.price);
      FinancialCalculator.validateNonZeroAmount(unitPrice, 'Unit price');

      // Calculate precise item total using FinancialCalculator
      const totalPrice = FinancialCalculator.calculateItemTotal(unitPrice, item.quantity);

      // Add to total amount using precise calculation
      totalAmount = FinancialCalculator.add(totalAmount, totalPrice);

      // Add to invoice items
      invoiceItems.push({
        serviceId: service.id,
        quantity: item.quantity,
        unitPrice: FinancialCalculator.roundCurrency(unitPrice),
        totalPrice: FinancialCalculator.roundCurrency(totalPrice),
        description: item.description || service.description
      });
    }

    // Round total amount to currency precision
    totalAmount = FinancialCalculator.roundCurrency(totalAmount);

    console.log(`Total amount calculated: ${FinancialCalculator.formatCurrency(totalAmount)}`);
    console.log(`Invoice items: ${JSON.stringify(invoiceItems, null, 2)}`);

    FinancialCalculator.validateNonZeroAmount(totalAmount, 'Invoice total amount');

    // Generate invoice number
    console.log('Generating invoice number...');
    const invoiceNumber = await generateInvoiceNumber();
    console.log(`Generated invoice number: ${invoiceNumber}`);

    // Set due date (default to 14 days from now if not provided)
    const dueDate = data.dueDate || addDays(new Date(), 14);
    console.log(`Due date set to: ${dueDate}`);

    // Create the invoice
    console.log('Creating invoice in database...');
    const invoice = await prisma.invoice.create({
      data: {
        invoiceNumber,
        totalAmount,
        amountPaid: 0,
        balance: totalAmount,
        customerName: data.customerName,
        phoneNumber: data.phoneNumber,
        email: data.email || null,
        status: 'pending',
        notes: data.notes || null,
        issuedAt: new Date(),
        dueDate,
        quoteId: data.quoteId || null,
        items: {
          create: invoiceItems
        }
      },
      include: {
        items: {
          include: {
            service: true
          }
        }
      }
    });

    console.log(`Invoice created successfully with ID: ${invoice.id}`);

    // Format the invoice
    console.log('Formatting invoice...');
    const formattedInvoice = formatInvoice(invoice);

    // Generate PDF file
    try {
      console.log('Generating PDF...');
      // Convert invoice to invoice data for PDF generation
      const invoiceData = mapToInvoiceData(formattedInvoice);

      // Generate the PDF file
      const pdfUrl = await generateInvoicePDF(invoiceData);
      console.log(`PDF generated successfully: ${pdfUrl}`);

      // Update the invoice with the PDF URL
      await prisma.invoice.update({
        where: { id: invoice.id },
        data: { pdfUrl }
      });

      // Return the invoice with PDF URL
      return {
        ...formattedInvoice,
        pdfUrl
      };
    } catch (pdfError) {
      console.error('Error generating PDF:', pdfError);
      console.log('Returning invoice without PDF URL due to PDF generation error');
      // Return the invoice without PDF URL
      return formattedInvoice;
    }
  } catch (error) {
    console.error('Error creating invoice:', error);
    console.error('Error details:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      data: JSON.stringify(data, null, 2)
    });
    throw error;
  }
}

/**
 * Update an invoice by ID
 * @param invoiceId The invoice ID
 * @param data The invoice update data
 * @returns The updated invoice or null if not found
 */
export async function updateInvoice(invoiceId: string, data: {
  customerName?: string;
  phoneNumber?: string;
  email?: string;
  notes?: string;
  dueDate?: Date;
  status?: string;
  items?: {
    serviceId: string;
    quantity: number;
    unitPrice?: number;
    description?: string;
  }[];
}): Promise<Invoice | null> {
  try {
    console.log(`[Invoice Service] Updating invoice: ${invoiceId}`, JSON.stringify(data, null, 2));

    // Check if invoice exists
    const existingInvoice = await prisma.invoice.findUnique({
      where: { id: invoiceId },
      include: {
        items: {
          include: {
            service: true
          }
        }
      }
    });

    if (!existingInvoice) {
      console.log(`[Invoice Service] Invoice not found: ${invoiceId}`);
      return null;
    }

    // Validate basic fields if provided
    if (data.customerName && !data.customerName.trim()) {
      throw new Error('Customer name cannot be empty');
    }

    if (data.phoneNumber && !data.phoneNumber.trim()) {
      throw new Error('Phone number cannot be empty');
    }

    // Calculate new totals if items are being updated
    let totalAmount = Number(existingInvoice.totalAmount);
    let newItems: any[] = [];

    if (data.items) {
      console.log(`[Invoice Service] Updating items for invoice: ${invoiceId}`);

      // Validate items
      if (!Array.isArray(data.items) || data.items.length === 0) {
        throw new Error('At least one item is required');
      }

      // Prepare new items with calculations
      totalAmount = 0;
      for (const itemData of data.items) {
        if (!itemData.serviceId || itemData.quantity <= 0) {
          throw new Error('Invalid item data: serviceId and positive quantity are required');
        }

        // Get service details if unitPrice not provided
        let unitPrice = itemData.unitPrice;
        if (!unitPrice) {
          const service = await getServiceById(itemData.serviceId);
          if (!service) {
            throw new Error(`Service not found: ${itemData.serviceId}`);
          }
          unitPrice = Number(service.price);
        }

        const quantity = itemData.quantity;
        const calculatedTotalPrice = FinancialCalculator.roundCurrency(unitPrice * quantity);
        totalAmount += calculatedTotalPrice;

        newItems.push({
          serviceId: itemData.serviceId,
          quantity,
          unitPrice: FinancialCalculator.roundCurrency(unitPrice),
          totalPrice: calculatedTotalPrice,
          description: itemData.description || null
        });
      }

      totalAmount = FinancialCalculator.roundCurrency(totalAmount);
    }

    // Prepare update data
    const updateData: any = {
      updatedAt: new Date()
    };

    if (data.customerName !== undefined) updateData.customerName = data.customerName.trim();
    if (data.phoneNumber !== undefined) updateData.phoneNumber = data.phoneNumber.trim();
    if (data.email !== undefined) updateData.email = data.email?.trim() || null;
    if (data.notes !== undefined) updateData.notes = data.notes?.trim() || null;
    if (data.dueDate !== undefined) updateData.dueDate = data.dueDate;
    if (data.status !== undefined) updateData.status = data.status;

    // If items are updated, update totals and recalculate balance
    if (data.items) {
      updateData.totalAmount = totalAmount;
      const currentAmountPaid = Number(existingInvoice.amountPaid);
      updateData.balance = FinancialCalculator.calculateBalance(totalAmount, currentAmountPaid);
      
      // Update status based on payment
      if (!data.status) {
        updateData.status = FinancialCalculator.getPaymentStatus(totalAmount, currentAmountPaid);
      }
    }

    // Update invoice in a transaction
    const updatedInvoice = await prisma.$transaction(async (tx) => {
      // Update the invoice
      const updated = await tx.invoice.update({
        where: { id: invoiceId },
        data: updateData,
        include: {
          items: {
            include: {
              service: true
            }
          }
        }
      });

      // If items are updated, replace them
      if (data.items) {
        // Delete existing items
        await tx.invoiceItem.deleteMany({
          where: { invoiceId }
        });

        // Create new items
        for (const itemData of newItems) {
          await tx.invoiceItem.create({
            data: {
              invoiceId,
              ...itemData
            }
          });
        }

        // Fetch updated invoice with new items
        return await tx.invoice.findUnique({
          where: { id: invoiceId },
          include: {
            items: {
              include: {
                service: true
              }
            }
          }
        });
      }

      return updated;
    });

    if (!updatedInvoice) {
      throw new Error('Failed to update invoice');
    }

    console.log(`[Invoice Service] Invoice updated successfully: ${invoiceId}`);
    return formatInvoice(updatedInvoice);
  } catch (error) {
    console.error(`[Invoice Service] Error updating invoice ${invoiceId}:`, error);
    throw new Error(`Failed to update invoice: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Delete an invoice by ID
 * @param invoiceId The invoice ID to delete
 * @returns True if deleted successfully, false if not found
 */
export async function deleteInvoice(invoiceId: string): Promise<boolean> {
  try {
    console.log(`[Invoice Service] Attempting to delete invoice: ${invoiceId}`);

    // First, check if the invoice exists
    const existingInvoice = await prisma.invoice.findUnique({
      where: { id: invoiceId },
      include: {
        items: true,
        quote: true
      }
    });

    if (!existingInvoice) {
      console.log(`[Invoice Service] Invoice not found: ${invoiceId}`);
      return false;
    }

    console.log(`[Invoice Service] Found invoice: ${existingInvoice.invoiceNumber}`);

    // Delete the invoice and its items in a transaction
    await prisma.$transaction(async (tx) => {
      // Delete invoice items first (due to foreign key constraints)
      await tx.invoiceItem.deleteMany({
        where: { invoiceId: invoiceId }
      });

      // Delete the invoice
      await tx.invoice.delete({
        where: { id: invoiceId }
      });
    });

    // Clean up PDF file if it exists
    if (existingInvoice.pdfUrl) {
      try {
        const pdfPath = path.join(process.cwd(), 'public', existingInvoice.pdfUrl);
        await fs.unlink(pdfPath);
        console.log(`[Invoice Service] PDF file deleted: ${pdfPath}`);
      } catch (fileError) {
        console.warn(`[Invoice Service] Could not delete PDF file: ${existingInvoice.pdfUrl}`, fileError);
        // Don't fail the deletion if PDF cleanup fails
      }
    }

    console.log(`[Invoice Service] Invoice deleted successfully: ${invoiceId}`);
    return true;
  } catch (error) {
    console.error(`[Invoice Service] Error deleting invoice ${invoiceId}:`, error);
    throw new Error(`Failed to delete invoice: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Update invoice payment with precise calculations
 * @param invoiceId The invoice ID
 * @param amountPaid The amount paid
 * @returns The updated invoice
 */
export async function updateInvoicePayment(invoiceId: string, amountPaid: number): Promise<Invoice | null> {
  try {
    console.log(`[Invoice Service] Updating payment for invoice: ${invoiceId}, amount: ${amountPaid}`);

    // Validate amount
    FinancialCalculator.validatePositiveAmount(amountPaid, 'Amount paid');

    // Get the existing invoice
    const existingInvoice = await prisma.invoice.findUnique({
      where: { id: invoiceId },
      include: {
        items: {
          include: {
            service: true
          }
        }
      }
    });

    if (!existingInvoice) {
      console.log(`[Invoice Service] Invoice not found: ${invoiceId}`);
      return null;
    }

    // Calculate precise balance and status
    const totalAmount = Number(existingInvoice.totalAmount);
    const roundedAmountPaid = FinancialCalculator.roundCurrency(amountPaid);
    const balance = FinancialCalculator.calculateBalance(totalAmount, roundedAmountPaid);
    const status = FinancialCalculator.getPaymentStatus(totalAmount, roundedAmountPaid);

    console.log(`[Invoice Service] Payment calculation - Total: ${FinancialCalculator.formatCurrency(totalAmount)}, Paid: ${FinancialCalculator.formatCurrency(roundedAmountPaid)}, Balance: ${FinancialCalculator.formatCurrency(balance)}, Status: ${status}`);

    // Update the invoice
    const updatedInvoice = await prisma.invoice.update({
      where: { id: invoiceId },
      data: {
        amountPaid: roundedAmountPaid,
        balance: FinancialCalculator.roundCurrency(balance),
        status,
        paidAt: FinancialCalculator.isPaidInFull(totalAmount, roundedAmountPaid) ? new Date() : null
      },
      include: {
        items: {
          include: {
            service: true
          }
        }
      }
    });

    console.log(`[Invoice Service] Invoice payment updated successfully: ${invoiceId}`);
    return formatInvoice(updatedInvoice);
  } catch (error) {
    console.error(`[Invoice Service] Error updating invoice payment ${invoiceId}:`, error);
    throw new Error(`Failed to update invoice payment: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Format an invoice from the database
 * @param invoice The invoice from the database
 * @returns The formatted invoice
 */
function formatInvoice(invoice: any): Invoice {
  // Helper function to safely convert to number with fallback
  const safeNumber = (value: any, fallback: number = 0): number => {
    const num = Number(value);
    return isNaN(num) ? fallback : num;
  };

  return {
    id: invoice.id,
    invoiceNumber: invoice.invoiceNumber,
    totalAmount: safeNumber(invoice.totalAmount),
    amountPaid: safeNumber(invoice.amountPaid),
    balance: safeNumber(invoice.balance),
    customerName: invoice.customerName,
    phoneNumber: invoice.phoneNumber,
    email: invoice.email,
    status: invoice.status,
    notes: invoice.notes,
    createdAt: new Date(invoice.createdAt),
    updatedAt: new Date(invoice.updatedAt),
    issuedAt: new Date(invoice.issuedAt),
    dueDate: new Date(invoice.dueDate),
    paidAt: invoice.paidAt ? new Date(invoice.paidAt) : null,
    pdfUrl: invoice.pdfUrl,
    quoteId: invoice.quoteId,
    items: invoice.items.map((item: any) => ({
      id: item.id,
      quantity: safeNumber(item.quantity, 1),
      unitPrice: safeNumber(item.unitPrice),
      totalPrice: safeNumber(item.totalPrice),
      description: item.description,
      serviceId: item.serviceId,
      serviceName: item.service?.name
    }))
  };
}

/**
 * Map an invoice to invoice data for PDF generation
 * @param invoice The invoice
 * @returns The invoice data for PDF generation
 */
function mapToInvoiceData(invoice: Invoice): InvoiceData {
  return {
    invoiceNumber: invoice.invoiceNumber,
    customerName: invoice.customerName,
    phoneNumber: invoice.phoneNumber,
    email: invoice.email || '',
    totalAmount: invoice.totalAmount,
    amountPaid: invoice.amountPaid,
    balance: invoice.balance,
    issuedAt: invoice.issuedAt.toISOString(),
    dueDate: invoice.dueDate.toISOString(),
    status: invoice.status,
    notes: invoice.notes || '',
    items: invoice.items.map(item => ({
      description: item.description || '',
      quantity: item.quantity,
      unitPrice: item.unitPrice,
      totalPrice: item.totalPrice,
      serviceName: item.serviceName || ''
    }))
  };
}
