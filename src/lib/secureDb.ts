/**
 * Secure Database Instance
 * Ready-to-use secure database wrapper instance
 */

import { createSecureDb } from '@/utils/secureDb';
import { prisma } from '@/lib/prisma';

// Create a secure database instance
export const secureDb = createSecureDb(prisma, {
  sanitizeStrings: true,
  validateInput: true,
  logOperations: process.env.NODE_ENV === 'development'
});

// Export for convenience
export { prisma } from '@/lib/prisma'; 