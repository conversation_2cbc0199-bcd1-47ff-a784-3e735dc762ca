// Currency conversion and formatting utilities
export interface CurrencyInfo {
  code: string;
  symbol: string;
  name: string;
  rate: number; // Rate from KES (base currency)
}

export interface LocationInfo {
  country: string;
  countryCode: string;
  currency: string;
  timezone: string;
}

// Supported currencies with their symbols and names
export const SUPPORTED_CURRENCIES: Record<string, Omit<CurrencyInfo, 'rate'>> = {
  KES: { code: 'KES', symbol: 'KSH', name: 'Kenyan Shilling' },
  USD: { code: 'USD', symbol: '$', name: 'US Dollar' },
  EUR: { code: 'EUR', symbol: '€', name: 'Euro' },
  GBP: { code: 'GBP', symbol: '£', name: 'British Pound' },
  CAD: { code: 'CAD', symbol: 'C$', name: 'Canadian Dollar' },
  AUD: { code: 'AUD', symbol: 'A$', name: 'Australian Dollar' },
  ZAR: { code: 'ZAR', symbol: 'R', name: 'South African Rand' },
  NGN: { code: 'NGN', symbol: '₦', name: 'Nigerian Naira' },
  GHS: { code: 'GHS', symbol: '₵', name: 'Ghanaian <PERSON>' },
  UGX: { code: 'UGX', symbol: 'USh', name: 'Ugandan Shilling' },
  TZS: { code: 'TZS', symbol: 'TSh', name: 'Tanzanian Shilling' },
  RWF: { code: 'RWF', symbol: 'RF', name: 'Rwandan Franc' },
  ETB: { code: 'ETB', symbol: 'Br', name: 'Ethiopian Birr' },
  INR: { code: 'INR', symbol: '₹', name: 'Indian Rupee' },
  JPY: { code: 'JPY', symbol: '¥', name: 'Japanese Yen' },
  CNY: { code: 'CNY', symbol: '¥', name: 'Chinese Yuan' },
  AED: { code: 'AED', symbol: 'د.إ', name: 'UAE Dirham' },
  SAR: { code: 'SAR', symbol: 'ر.س', name: 'Saudi Riyal' },
};

// Country to currency mapping
export const COUNTRY_CURRENCY_MAP: Record<string, string> = {
  US: 'USD', CA: 'CAD', GB: 'GBP', AU: 'AUD', DE: 'EUR', FR: 'EUR', 
  IT: 'EUR', ES: 'EUR', NL: 'EUR', BE: 'EUR', AT: 'EUR', IE: 'EUR',
  PT: 'EUR', FI: 'EUR', GR: 'EUR', LU: 'EUR', MT: 'EUR', CY: 'EUR',
  SI: 'EUR', SK: 'EUR', EE: 'EUR', LV: 'EUR', LT: 'EUR',
  KE: 'KES', UG: 'UGX', TZ: 'TZS', RW: 'RWF', ET: 'ETB',
  NG: 'NGN', GH: 'GHS', ZA: 'ZAR', IN: 'INR', JP: 'JPY',
  CN: 'CNY', AE: 'AED', SA: 'SAR',
};

// Cache for exchange rates and location
let exchangeRatesCache: { rates: Record<string, number>; timestamp: number } | null = null;
let locationCache: LocationInfo | null = null;
const CACHE_DURATION = 60 * 60 * 1000; // 1 hour

// Get user's location information
export async function getUserLocation(): Promise<LocationInfo> {
  if (locationCache) {
    return locationCache;
  }

  try {
    // Try multiple IP geolocation services for reliability
    const services = [
      'https://ipapi.co/json/',
      'https://api.ipgeolocation.io/ipgeo?apiKey=free',
      'https://ipinfo.io/json',
    ];

    for (const service of services) {
      try {
        const response = await fetch(service);
        if (!response.ok) continue;
        
        const data = await response.json();
        
        // Normalize response based on service
        let locationInfo: LocationInfo;
        
        if (service.includes('ipapi.co')) {
          locationInfo = {
            country: data.country_name || 'Kenya',
            countryCode: data.country_code || 'KE',
            currency: data.currency || 'KES',
            timezone: data.timezone || 'Africa/Nairobi',
          };
        } else if (service.includes('ipgeolocation.io')) {
          locationInfo = {
            country: data.country_name || 'Kenya',
            countryCode: data.country_code2 || 'KE',
            currency: data.currency?.code || 'KES',
            timezone: data.time_zone?.name || 'Africa/Nairobi',
          };
        } else { // ipinfo.io
          locationInfo = {
            country: data.country || 'Kenya',
            countryCode: data.country || 'KE',
            currency: COUNTRY_CURRENCY_MAP[data.country] || 'KES',
            timezone: data.timezone || 'Africa/Nairobi',
          };
        }

        locationCache = locationInfo;
        return locationInfo;
      } catch (error) {
        console.warn(`Failed to get location from ${service}:`, error);
        continue;
      }
    }
  } catch (error) {
    console.warn('Failed to detect user location:', error);
  }

  // Fallback to Kenya
  const fallback: LocationInfo = {
    country: 'Kenya',
    countryCode: 'KE',
    currency: 'KES',
    timezone: 'Africa/Nairobi',
  };
  
  locationCache = fallback;
  return fallback;
}

// Get exchange rates from KES to other currencies
export async function getExchangeRates(): Promise<Record<string, number>> {
  // Check cache first
  if (exchangeRatesCache && Date.now() - exchangeRatesCache.timestamp < CACHE_DURATION) {
    return exchangeRatesCache.rates;
  }

  try {
    // Try multiple exchange rate APIs
    const apis = [
      'https://api.exchangerate-api.com/v4/latest/KES',
      'https://open.er-api.com/v6/latest/KES',
    ];

    for (const api of apis) {
      try {
        console.log(`Trying to fetch rates from: ${api}`);
        const response = await fetch(api);
        if (!response.ok) {
          console.warn(`API ${api} returned status: ${response.status}`);
          continue;
        }
        
        const data = await response.json();
        console.log(`API response from ${api}:`, data);
        
        if (data.rates) {
          const rates = data.rates;
          
          // Cache the rates
          exchangeRatesCache = {
            rates,
            timestamp: Date.now(),
          };
          
          console.log('Successfully fetched and cached exchange rates:', rates);
          return rates;
        }
      } catch (error) {
        console.warn(`Failed to get rates from ${api}:`, error);
        continue;
      }
    }
  } catch (error) {
    console.warn('Failed to fetch exchange rates:', error);
  }

  // Fallback rates (approximate, should be updated regularly)
  const fallbackRates: Record<string, number> = {
    KES: 1,
    USD: 0.0065, // 1 KES = 0.0065 USD (updated approximate rate)
    EUR: 0.0060, // 1 KES = 0.0060 EUR (updated approximate rate)
    GBP: 0.0051, // 1 KES = 0.0051 GBP (updated approximate rate)
    CAD: 0.0088, // 1 KES = 0.0088 CAD (updated approximate rate)
    AUD: 0.0098, // 1 KES = 0.0098 AUD (updated approximate rate)
    ZAR: 0.12,   // 1 KES = 0.12 ZAR (updated approximate rate)
    NGN: 10.5,   // 1 KES = 10.5 NGN (updated approximate rate)
    GHS: 0.078,  // 1 KES = 0.078 GHS (updated approximate rate)
    UGX: 24.2,   // 1 KES = 24.2 UGX (updated approximate rate)
    TZS: 16.3,   // 1 KES = 16.3 TZS (updated approximate rate)
    RWF: 8.6,    // 1 KES = 8.6 RWF (updated approximate rate)
    ETB: 0.37,   // 1 KES = 0.37 ETB (updated approximate rate)
    INR: 0.54,   // 1 KES = 0.54 INR (updated approximate rate)
    JPY: 0.97,   // 1 KES = 0.97 JPY (updated approximate rate)
    CNY: 0.047,  // 1 KES = 0.047 CNY (updated approximate rate)
    AED: 0.024,  // 1 KES = 0.024 AED (updated approximate rate)
    SAR: 0.024,  // 1 KES = 0.024 SAR (updated approximate rate)
  };

  exchangeRatesCache = {
    rates: fallbackRates,
    timestamp: Date.now(),
  };

  return fallbackRates;
}

// Convert price from KES to target currency
export async function convertPrice(kesAmount: number, targetCurrency: string): Promise<number> {
  console.log(`Converting ${kesAmount} KES to ${targetCurrency}`); // Debug log
  
  if (targetCurrency === 'KES') {
    return kesAmount;
  }

  const rates = await getExchangeRates();
  console.log(`Available rates:`, rates); // Debug log
  
  const rate = rates[targetCurrency];
  console.log(`Rate for ${targetCurrency}:`, rate); // Debug log
  
  if (!rate) {
    console.warn(`Exchange rate not found for ${targetCurrency}, using KES`);
    return kesAmount;
  }

  const convertedAmount = kesAmount * rate;
  console.log(`Converted ${kesAmount} KES to ${convertedAmount} ${targetCurrency}`); // Debug log
  
  return convertedAmount;
}

// Format price with currency symbol and proper formatting
export function formatPrice(amount: number, currencyCode: string): string {
  const currency = SUPPORTED_CURRENCIES[currencyCode];
  if (!currency) {
    return `${amount.toLocaleString()}`;
  }

  // Round to appropriate decimal places
  let roundedAmount: number;
  if (['JPY', 'KRW', 'VND', 'UGX', 'RWF', 'TZS'].includes(currencyCode)) {
    // Currencies that don't use decimal places
    roundedAmount = Math.round(amount);
  } else if (['USD', 'EUR', 'GBP', 'CAD', 'AUD'].includes(currencyCode)) {
    // Major currencies - 2 decimal places
    roundedAmount = Math.round(amount * 100) / 100;
  } else {
    // Other currencies - round to nearest whole number for simplicity
    roundedAmount = Math.round(amount);
  }

  // Format with proper separators
  const formattedNumber = roundedAmount.toLocaleString(undefined, {
    minimumFractionDigits: ['JPY', 'KRW', 'VND', 'UGX', 'RWF', 'TZS'].includes(currencyCode) ? 0 : 
                          ['USD', 'EUR', 'GBP', 'CAD', 'AUD'].includes(currencyCode) ? 2 : 0,
    maximumFractionDigits: ['JPY', 'KRW', 'VND', 'UGX', 'RWF', 'TZS'].includes(currencyCode) ? 0 : 
                          ['USD', 'EUR', 'GBP', 'CAD', 'AUD'].includes(currencyCode) ? 2 : 0,
  });

  return `${currency.symbol}${formattedNumber}`;
}

// Get user's preferred currency based on location
export async function getUserCurrency(): Promise<string> {
  try {
    const location = await getUserLocation();
    const currency = COUNTRY_CURRENCY_MAP[location.countryCode] || location.currency || 'KES';
    
    // Ensure the currency is supported
    if (SUPPORTED_CURRENCIES[currency]) {
      return currency;
    }
  } catch (error) {
    console.warn('Failed to get user currency:', error);
  }
  
  return 'KES'; // Fallback to KES
}

// Convert and format price in one function
export async function convertAndFormatPrice(kesAmount: number, targetCurrency?: string): Promise<string> {
  const currency = targetCurrency || await getUserCurrency();
  const convertedAmount = await convertPrice(kesAmount, currency);
  return formatPrice(convertedAmount, currency);
}

// Clear caches (useful for testing or manual refresh)
export function clearCaches(): void {
  exchangeRatesCache = null;
  locationCache = null;
} 