import { getRedisClient } from './redis';
import { NextRequest } from 'next/server';

export interface RateLimitConfig {
  windowMs: number;      // Time window in milliseconds
  maxRequests: number;   // Maximum requests per window
  skipOnError?: boolean; // Skip rate limiting if <PERSON><PERSON> is down
  keyGenerator?: (req: NextRequest) => string; // Custom key generator
  onLimitReached?: (req: NextRequest, key: string) => void; // Callback when limit reached
}

export interface RateLimitResult {
  allowed: boolean;
  remaining: number;
  resetTime: number;
  totalHits: number;
}

export interface RateLimitInfo {
  key: string;
  requests: number;
  remaining: number;
  resetTime: number;
  windowStart: number;
}

class RedisRateLimiter {
  private redis = getRedisClient();

  // Check rate limit for a specific key
  async checkRateLimit(
    key: string, 
    config: RateLimitConfig
  ): Promise<RateLimitResult> {
    try {
      if (!this.redis) {
        if (config.skipOnError) {
          // If Red<PERSON> is down and skipOnError is true, allow the request
          return {
            allowed: true,
            remaining: config.maxRequests - 1,
            resetTime: Date.now() + config.windowMs,
            totalHits: 1,
          };
        }
        throw new Error('Redis client not available');
      }

      const now = Date.now();
      const windowStart = Math.floor(now / config.windowMs) * config.windowMs;
      const windowKey = `rate_limit:${key}:${windowStart}`;

      // Use a Lua script for atomic operations
      const luaScript = `
        local windowKey = KEYS[1]
        local maxRequests = tonumber(ARGV[1])
        local windowMs = tonumber(ARGV[2])
        local now = tonumber(ARGV[3])
        
        local current = redis.call('GET', windowKey)
        if current == false then
          current = 0
        else
          current = tonumber(current)
        end
        
        if current < maxRequests then
          local newCount = redis.call('INCR', windowKey)
          redis.call('EXPIRE', windowKey, math.ceil(windowMs / 1000))
          return {1, newCount, maxRequests - newCount, now + windowMs}
        else
          return {0, current, 0, now + windowMs}
        end
      `;

      const result = await this.redis.eval(
        luaScript,
        1,
        windowKey,
        config.maxRequests.toString(),
        config.windowMs.toString(),
        now.toString()
      ) as number[];

      return {
        allowed: result[0] === 1,
        totalHits: result[1],
        remaining: Math.max(0, result[2]),
        resetTime: result[3],
      };
    } catch (error) {
      console.error('Rate limit check failed:', error);
      
      if (config.skipOnError) {
        return {
          allowed: true,
          remaining: config.maxRequests - 1,
          resetTime: Date.now() + config.windowMs,
          totalHits: 1,
        };
      }
      
      throw error;
    }
  }

  // Get current rate limit info for a key
  async getRateLimitInfo(key: string, windowMs: number): Promise<RateLimitInfo | null> {
    try {
      if (!this.redis) {
        return null;
      }

      const now = Date.now();
      const windowStart = Math.floor(now / windowMs) * windowMs;
      const windowKey = `rate_limit:${key}:${windowStart}`;

      const requests = await this.redis.get(windowKey);
      const requestCount = requests ? parseInt(requests, 10) : 0;

      return {
        key,
        requests: requestCount,
        remaining: Math.max(0, requestCount),
        resetTime: windowStart + windowMs,
        windowStart,
      };
    } catch (error) {
      console.error('Error getting rate limit info:', error);
      return null;
    }
  }

  // Clear rate limit for a specific key
  async clearRateLimit(key: string): Promise<void> {
    try {
      if (!this.redis) {
        return;
      }

      const pattern = `rate_limit:${key}:*`;
      const keys = await this.redis.keys(pattern);
      
      if (keys.length > 0) {
        await this.redis.del(...keys);
        console.log(`Cleared rate limit for key: ${key} (${keys.length} windows)`);
      }
    } catch (error) {
      console.error('Error clearing rate limit:', error);
    }
  }

  // Get rate limiting statistics
  async getRateLimitStats(): Promise<{
    totalActiveWindows: number;
    topLimitedKeys: { key: string; hits: number }[];
    currentRequests: number;
  }> {
    try {
      if (!this.redis) {
        return { totalActiveWindows: 0, topLimitedKeys: [], currentRequests: 0 };
      }

      const keys = await this.redis.keys('rate_limit:*');
      const keyData: { key: string; hits: number }[] = [];
      let totalCurrentRequests = 0;

      // Get hit counts for all active windows
      for (const key of keys) {
        const hits = await this.redis.get(key);
        if (hits) {
          const hitCount = parseInt(hits, 10);
          totalCurrentRequests += hitCount;
          
          // Extract the original key (remove rate_limit: prefix and window timestamp)
          const originalKey = key.replace(/^rate_limit:/, '').replace(/:\d+$/, '');
          keyData.push({ key: originalKey, hits: hitCount });
        }
      }

      // Sort by hits and get top limited keys
      const topLimitedKeys = keyData
        .sort((a, b) => b.hits - a.hits)
        .slice(0, 10);

      return {
        totalActiveWindows: keys.length,
        topLimitedKeys,
        currentRequests: totalCurrentRequests,
      };
    } catch (error) {
      console.error('Error getting rate limit stats:', error);
      return { totalActiveWindows: 0, topLimitedKeys: [], currentRequests: 0 };
    }
  }

  // Clean up expired rate limit windows
  async cleanupExpiredWindows(): Promise<number> {
    try {
      if (!this.redis) {
        return 0;
      }

      const keys = await this.redis.keys('rate_limit:*');
      let deletedCount = 0;

      for (const key of keys) {
        const ttl = await this.redis.ttl(key);
        if (ttl === -1) {
          // Key exists but has no expiration, set one based on current time
          await this.redis.expire(key, 3600); // 1 hour fallback
        } else if (ttl === -2) {
          // Key doesn't exist (should not happen with KEYS command)
          deletedCount++;
        }
      }

      console.log(`Rate limiter cleanup: checked ${keys.length} keys, ${deletedCount} were expired`);
      return deletedCount;
    } catch (error) {
      console.error('Error cleaning up rate limit windows:', error);
      return 0;
    }
  }
}

// Singleton instance
const redisRateLimiter = new RedisRateLimiter();

// Default rate limit configurations
export const RATE_LIMIT_CONFIGS = {
  // Public API - very lenient
  public: {
    windowMs: 60 * 1000,    // 1 minute
    maxRequests: 1000,      // 1000 requests per minute - very lenient
    skipOnError: true,
  },
  
  // General API - moderate
  general: {
    windowMs: 60 * 1000,    // 1 minute
    maxRequests: 500,       // 500 requests per minute - very lenient
    skipOnError: true,
  },
  
  // Admin API - restrictive
  admin: {
    windowMs: 60 * 1000,    // 1 minute
    maxRequests: 500,       // 500 requests per minute - very lenient
    skipOnError: false,     // Don't skip on Redis errors for admin routes
  },
  
  // Authentication - very restrictive
  auth: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 50,          // 50 attempts per 15 minutes - more lenient
    skipOnError: false,
  },
  
  // File uploads - restrictive
  upload: {
    windowMs: 60 * 1000,    // 1 minute
    maxRequests: 50,        // 50 uploads per minute - more lenient
    skipOnError: false,
  },
} as const;

// Helper function to generate rate limit key from request
export function generateRateLimitKey(req: NextRequest, prefix: string = ''): string {
  // Get client IP
  const forwarded = req.headers.get('x-forwarded-for');
  const realIp = req.headers.get('x-real-ip');
  const ip = forwarded ? forwarded.split(',')[0].trim() : realIp || 'unknown';
  
  // Get user agent for additional uniqueness
  const userAgent = req.headers.get('user-agent') || 'unknown';
  const userAgentHash = hashString(userAgent);
  
  return prefix ? `${prefix}:${ip}:${userAgentHash}` : `${ip}:${userAgentHash}`;
}

// Simple hash function for user agent
function hashString(str: string): string {
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return Math.abs(hash).toString(36).substring(0, 8);
}

// Main rate limiting function
export async function checkRateLimit(
  req: NextRequest,
  configName: keyof typeof RATE_LIMIT_CONFIGS,
  customKey?: string
): Promise<RateLimitResult> {
  const config = RATE_LIMIT_CONFIGS[configName];
  const key = customKey || generateRateLimitKey(req, configName);
  
  return await redisRateLimiter.checkRateLimit(key, config);
}

// Middleware wrapper for rate limiting
export function withRedisRateLimit(
  configName: keyof typeof RATE_LIMIT_CONFIGS,
  customKeyGenerator?: (req: NextRequest) => string
) {
  return async (req: NextRequest, handler: Function) => {
    try {
      const key = customKeyGenerator ? customKeyGenerator(req) : generateRateLimitKey(req, configName);
      const result = await checkRateLimit(req, configName, key);
      
      if (!result.allowed) {
        const resetDate = new Date(result.resetTime);
        
        return new Response(
          JSON.stringify({
            error: 'Too many requests',
            message: `Rate limit exceeded. Try again after ${resetDate.toISOString()}`,
            retryAfter: Math.ceil((result.resetTime - Date.now()) / 1000),
          }),
          {
            status: 429,
            headers: {
              'Content-Type': 'application/json',
              'X-RateLimit-Limit': RATE_LIMIT_CONFIGS[configName].maxRequests.toString(),
              'X-RateLimit-Remaining': result.remaining.toString(),
              'X-RateLimit-Reset': result.resetTime.toString(),
              'Retry-After': Math.ceil((result.resetTime - Date.now()) / 1000).toString(),
            },
          }
        );
      }
      
      // Call the original handler with rate limit headers
      const response = await handler(req);
      
      // Add rate limit headers to successful responses
      if (response instanceof Response) {
        response.headers.set('X-RateLimit-Limit', RATE_LIMIT_CONFIGS[configName].maxRequests.toString());
        response.headers.set('X-RateLimit-Remaining', result.remaining.toString());
        response.headers.set('X-RateLimit-Reset', result.resetTime.toString());
      }
      
      return response;
    } catch (error) {
      console.error('Rate limiting error:', error);
      
      // If rate limiting fails and skipOnError is true, continue with the request
      const config = RATE_LIMIT_CONFIGS[configName];
      if (config.skipOnError) {
        return await handler(req);
      }
      
      // Otherwise, return an error
      return new Response(
        JSON.stringify({
          error: 'Rate limiting service unavailable',
          message: 'Please try again later',
        }),
        {
          status: 503,
          headers: { 'Content-Type': 'application/json' },
        }
      );
    }
  };
}

// Administrative functions
export const rateLimitAdmin = {
  // Get current rate limit info for a key
  getInfo: async (key: string, windowMs: number = 60000) => {
    return await redisRateLimiter.getRateLimitInfo(key, windowMs);
  },
  
  // Clear rate limit for a key
  clear: async (key: string) => {
    await redisRateLimiter.clearRateLimit(key);
  },
  
  // Get overall statistics
  getStats: async () => {
    return await redisRateLimiter.getRateLimitStats();
  },
  
  // Clean up expired windows
  cleanup: async () => {
    return await redisRateLimiter.cleanupExpiredWindows();
  },
};

// Export the rate limiter instance for advanced usage
export { redisRateLimiter }; 