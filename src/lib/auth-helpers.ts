import { auth } from "../../auth"
import { redirect } from "next/navigation"
import { NextRequest, NextResponse } from "next/server"

/**
 * Get session in server components
 */
export async function getServerSession() {
  return await auth()
}

/**
 * Require authentication in server components
 * Redirects to login if not authenticated
 */
export async function requireAuth() {
  const session = await auth()
  
  if (!session?.user) {
    redirect('/admin/login')
  }
  
  return session
}

/**
 * Require admin role in server components
 * Redirects to unauthorized if not admin
 */
export async function requireAdmin() {
  const session = await requireAuth()
  
  // Normalize role comparison to handle case sensitivity
  const userRole = session.user.role?.toLowerCase() || '';
  if (userRole !== 'admin') {
    redirect('/unauthorized')
  }
  
  return session
}

/**
 * Require admin authentication in API routes
 * Returns user and error response if not authenticated/authorized
 */
export async function requireAdminAuth(request: NextRequest) {
  const session = await auth()
  
  if (!session?.user) {
    return {
      user: null,
      response: NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
  }
  
  // Normalize role comparison to handle case sensitivity
  const userRole = session.user.role?.toLowerCase() || '';
  if (userRole !== 'admin') {
    return {
      user: null,
      response: NextResponse.json({ error: 'Forbidden' }, { status: 403 })
    }
  }
  
  return {
    user: session.user,
    response: null
  }
}

/**
 * Check if user has specific permission
 */
export async function hasPermission(permission: string) {
  const session = await auth()
  
  if (!session?.user) {
    return false
  }
  
  // Admin users have all permissions - normalize role comparison
  const userRole = session.user.role?.toLowerCase() || '';
  if (userRole === 'admin') {
    return true
  }
  
  // Check specific permissions (this would need to be extended based on your role system)
  return false
} 