/**
 * Modern Authentication Configuration
 * Database-only authentication - no environment variables
 */

import { cookies } from 'next/headers';

export const AUTH_CONFIG = {
  // Session configuration
  SESSION: {
    COOKIE_NAME: 'mocky-session',
    MAX_AGE: 60 * 60 * 24 * 7, // 7 days
    REMEMBER_ME_MAX_AGE: 60 * 60 * 24 * 30, // 30 days
    SECURE: process.env.NODE_ENV === 'production',
    HTTP_ONLY: true,
    SAME_SITE: 'lax' as const,
    DOMAIN: process.env.NODE_ENV === 'production' ? 'mocky.co.ke' : undefined,
  },

  // Security configuration
  SECURITY: {
    BCRYPT_ROUNDS: 12,
    SESSION_TIMEOUT: 60 * 60 * 24 * 7, // 7 days
    CSRF_ENABLED: true,
    SECURE_HEADERS: true,
    RATE_LIMIT: {
      LOGIN_ATTEMPTS: 5,
      WINDOW_MINUTES: 15,
      LOCKOUT_MINUTES: 30,
    },
  },

  // JWT configuration - SECURITY HARDENED
  JWT: {
    SECRET: (() => {
      const secret = process.env.AUTH_SECRET || process.env.NEXTAUTH_SECRET;
      if (!secret) {
        throw new Error('AUTH_SECRET environment variable is required');
      }
      if (secret.length < 32) {
        throw new Error('AUTH_SECRET must be at least 32 characters long');
      }
      return secret;
    })(),
    ALGORITHM: 'HS256' as const,
    ISSUER: 'mocky-auth',
    AUDIENCE: 'mocky-users',
    EXPIRES_IN: '7d',
    REFRESH_EXPIRES_IN: '30d',
  },

  // Database configuration
  DATABASE: {
    CONNECTION_TIMEOUT: 10000,
    QUERY_TIMEOUT: 5000,
    MAX_CONNECTIONS: 10,
  },

  // API configuration
  API: {
    TIMEOUT: 10000,
    MAX_RETRIES: 3,
    RETRY_DELAY: 1000,
  },
} as const;

/**
 * Validate authentication environment
 */
export function validateAuthEnvironment(): void {
  if (process.env.NODE_ENV === 'production') {
    if (!process.env.AUTH_SECRET && !process.env.NEXTAUTH_SECRET) {
      console.warn('[AUTH CONFIG] Warning: No AUTH_SECRET set in production');
    }
    
    if (!process.env.DATABASE_URL) {
      console.error('[AUTH CONFIG] Error: DATABASE_URL is required in production');
    }
  }
}

/**
 * Get secure cookie configuration
 */
export function getCookieConfig() {
  return {
    name: AUTH_CONFIG.SESSION.COOKIE_NAME,
    maxAge: AUTH_CONFIG.SESSION.MAX_AGE,
    httpOnly: AUTH_CONFIG.SESSION.HTTP_ONLY,
    secure: AUTH_CONFIG.SESSION.SECURE,
    sameSite: AUTH_CONFIG.SESSION.SAME_SITE,
    domain: AUTH_CONFIG.SESSION.DOMAIN,
  };
}

/**
 * Set auth cookie
 */
export async function setAuthCookie(token: string, rememberMe: boolean = false) {
  const cookieStore = await cookies();
  const maxAge = rememberMe ? AUTH_CONFIG.SESSION.REMEMBER_ME_MAX_AGE : AUTH_CONFIG.SESSION.MAX_AGE;
  
  cookieStore.set(AUTH_CONFIG.SESSION.COOKIE_NAME, token, {
    httpOnly: true,
    secure: AUTH_CONFIG.SESSION.SECURE,
    sameSite: AUTH_CONFIG.SESSION.SAME_SITE,
    maxAge,
    domain: AUTH_CONFIG.SESSION.DOMAIN,
  });
}

/**
 * Clear auth cookie
 */
export async function clearAuthCookie() {
  const cookieStore = await cookies();
  cookieStore.delete(AUTH_CONFIG.SESSION.COOKIE_NAME);
}

/**
 * Get auth cookie value
 */
export async function getAuthCookie(): Promise<string | undefined> {
  const cookieStore = await cookies();
  return cookieStore.get(AUTH_CONFIG.SESSION.COOKIE_NAME)?.value;
} 