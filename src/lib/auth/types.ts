/**
 * Authentication Types for NextAuth.js
 */

export interface LoginCredentials {
  username: string;
  password: string;
  rememberMe?: boolean;
}

export interface Session {
  user: User;
  expires: string;
}

export interface User {
  id: string;
  username: string;
  email: string;
  name: string | null;
  active: boolean;
  roleId: string;
  lastLogin: Date | null;
  createdAt: Date;
  updatedAt: Date;
  role: {
    id: string;
    name: string;
    permissions: string[];
  };
}

export interface AuthContext {
  user: User | null;
  session: Session | null;
  isLoading: boolean;
} 