import { NextRequest, NextResponse } from 'next/server';
import { ErrorFactory } from '@/lib/error-handling/centralized-errors';

interface RateLimitConfig {
  windowMs: number;           // Time window in milliseconds
  maxRequests: number;        // Maximum requests per window
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
  keyGenerator?: (req: NextRequest) => string;
  onLimitReached?: (req: NextRequest, identifier: string) => void;
  whitelist?: string[];       // IPs to skip rate limiting
  blacklist?: string[];       // IPs to always block
}

interface RateLimitEntry {
  count: number;
  resetTime: number;
  blocked: boolean;
}

class RateLimiter {
  private static instances: Map<string, RateLimiter> = new Map();
  private store: Map<string, RateLimitEntry> = new Map();
  protected config: Required<RateLimitConfig>;
  private cleanupInterval: NodeJS.Timeout;

  protected constructor(config: RateLimitConfig, name: string) {
    this.config = {
      windowMs: config.windowMs,
      maxRequests: config.maxRequests,
      skipSuccessfulRequests: config.skipSuccessfulRequests ?? false,
      skipFailedRequests: config.skipFailedRequests ?? false,
      keyGenerator: config.keyGenerator ?? this.defaultKeyGenerator,
      onLimitReached: config.onLimitReached ?? this.defaultOnLimitReached,
      whitelist: config.whitelist ?? [],
      blacklist: config.blacklist ?? [],
    };

    // Clean up expired entries every minute
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, 60000);

    console.info(`[RATE_LIMITER] Initialized "${name}" - ${config.maxRequests} requests per ${config.windowMs}ms`);
  }

  public static getInstance(name: string, config: RateLimitConfig): RateLimiter {
    if (!RateLimiter.instances.has(name)) {
      RateLimiter.instances.set(name, new RateLimiter(config, name));
    }
    return RateLimiter.instances.get(name)!;
  }

  private defaultKeyGenerator(req: NextRequest): string {
    // Try to get real IP from headers (for proxy/load balancer environments)
    const forwardedFor = req.headers.get('x-forwarded-for');
    const realIP = req.headers.get('x-real-ip');
    const remoteAddr = req.headers.get('remote-addr');
    
    let clientIP = 'unknown';
    if (forwardedFor) {
      // x-forwarded-for can contain multiple IPs, get the first one
      clientIP = forwardedFor.split(',')[0].trim();
    } else if (realIP) {
      clientIP = realIP;
    } else if (remoteAddr) {
      clientIP = remoteAddr;
    }

    return clientIP;
  }

  private defaultOnLimitReached(req: NextRequest, identifier: string): void {
    console.warn(`[RATE_LIMITER] Rate limit exceeded for ${identifier} on ${req.nextUrl.pathname}`);
  }

  private cleanup(): void {
    const now = Date.now();
    const entriesRemoved = this.store.size;
    
    for (const [key, entry] of this.store.entries()) {
      if (now > entry.resetTime) {
        this.store.delete(key);
      }
    }
    
    const currentSize = this.store.size;
    if (entriesRemoved - currentSize > 0) {
      console.debug(`[RATE_LIMITER] Cleaned up ${entriesRemoved - currentSize} expired entries`);
    }
  }

  public async checkLimit(req: NextRequest): Promise<{
    allowed: boolean;
    limit: number;
    remaining: number;
    resetTime: number;
    retryAfter?: number;
  }> {
    const identifier = this.config.keyGenerator(req);
    const now = Date.now();

    // Check blacklist
    if (this.config.blacklist.includes(identifier)) {
      console.warn(`[RATE_LIMITER] Blocked blacklisted IP: ${identifier}`);
      return {
        allowed: false,
        limit: 0,
        remaining: 0,
        resetTime: now + this.config.windowMs,
        retryAfter: Math.ceil(this.config.windowMs / 1000),
      };
    }

    // Check whitelist
    if (this.config.whitelist.includes(identifier)) {
      return {
        allowed: true,
        limit: this.config.maxRequests,
        remaining: this.config.maxRequests,
        resetTime: now + this.config.windowMs,
      };
    }

    let entry = this.store.get(identifier);

    // Initialize new entry or reset if window expired
    if (!entry || now > entry.resetTime) {
      entry = {
        count: 0,
        resetTime: now + this.config.windowMs,
        blocked: false,
      };
      this.store.set(identifier, entry);
    }

    // If already blocked in this window
    if (entry.blocked) {
      const retryAfter = Math.ceil((entry.resetTime - now) / 1000);
      return {
        allowed: false,
        limit: this.config.maxRequests,
        remaining: 0,
        resetTime: entry.resetTime,
        retryAfter,
      };
    }

    // Check if limit would be exceeded
    if (entry.count >= this.config.maxRequests) {
      entry.blocked = true;
      this.config.onLimitReached(req, identifier);
      
      const retryAfter = Math.ceil((entry.resetTime - now) / 1000);
      return {
        allowed: false,
        limit: this.config.maxRequests,
        remaining: 0,
        resetTime: entry.resetTime,
        retryAfter,
      };
    }

    // Increment counter
    entry.count++;
    
    return {
      allowed: true,
      limit: this.config.maxRequests,
      remaining: Math.max(0, this.config.maxRequests - entry.count),
      resetTime: entry.resetTime,
    };
  }

  public recordRequest(req: NextRequest, wasSuccessful: boolean): void {
    // Optionally skip counting based on request outcome
    if ((wasSuccessful && this.config.skipSuccessfulRequests) ||
        (!wasSuccessful && this.config.skipFailedRequests)) {
      const identifier = this.config.keyGenerator(req);
      const entry = this.store.get(identifier);
      if (entry) {
        entry.count = Math.max(0, entry.count - 1);
      }
    }
  }

  public getStats(): {
    totalEntries: number;
    activeBlocks: number;
    memoryUsage: string;
  } {
    const activeBlocks = Array.from(this.store.values()).filter(entry => entry.blocked).length;
    const memoryUsage = `${Math.round(JSON.stringify(Array.from(this.store.entries())).length / 1024)}KB`;
    
    return {
      totalEntries: this.store.size,
      activeBlocks,
      memoryUsage,
    };
  }

  public destroy(): void {
    clearInterval(this.cleanupInterval);
    this.store.clear();
  }
}

// Pre-configured rate limiters for different use cases
export const RateLimiters = {
  // General API rate limiting
  api: RateLimiter.getInstance('api', {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 100,
  }),

  // Authentication endpoints (stricter)
  auth: RateLimiter.getInstance('auth', {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 10,
    onLimitReached: (req, identifier) => {
      console.error(`[SECURITY] Authentication rate limit exceeded for ${identifier} on ${req.nextUrl.pathname}`);
    },
  }),

  // Upload endpoints (very strict)
  upload: RateLimiter.getInstance('upload', {
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 20,
  }),

  // Admin endpoints (moderate)
  admin: RateLimiter.getInstance('admin', {
    windowMs: 5 * 60 * 1000, // 5 minutes
    maxRequests: 50,
  }),

  // Public content (lenient)
  public: RateLimiter.getInstance('public', {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 20,
  }),
};

// Middleware function factory
export function createRateLimitMiddleware(limiterName: keyof typeof RateLimiters) {
  const limiter = RateLimiters[limiterName];

  return async function rateLimitMiddleware(req: NextRequest): Promise<NextResponse | null> {
    try {
      const result = await limiter.checkLimit(req);
      
      // Add rate limit headers to all responses
      const headers = new Headers();
      headers.set('X-RateLimit-Limit', result.limit.toString());
      headers.set('X-RateLimit-Remaining', result.remaining.toString());
      headers.set('X-RateLimit-Reset', new Date(result.resetTime).toISOString());
      
      if (!result.allowed) {
        headers.set('Retry-After', result.retryAfter!.toString());
        
        return NextResponse.json(
          {
            success: false,
            error: 'Rate limit exceeded',
            code: 'RATE_LIMIT_EXCEEDED',
            retryAfter: result.retryAfter,
            resetTime: new Date(result.resetTime).toISOString(),
          },
          { 
            status: 429,
            headers,
          }
        );
      }

      // If allowed, return null to continue processing
      // The calling middleware should add the headers to the final response
      return null;
    } catch (error) {
      console.error('[RATE_LIMITER] Error in rate limiting middleware:', error);
      // On error, allow the request through but log the issue
      return null;
    }
  };
}

// Helper function to apply rate limit headers to any response
export function applyRateLimitHeaders(
  response: NextResponse,
  result: { limit: number; remaining: number; resetTime: number }
): NextResponse {
  response.headers.set('X-RateLimit-Limit', result.limit.toString());
  response.headers.set('X-RateLimit-Remaining', result.remaining.toString());
  response.headers.set('X-RateLimit-Reset', new Date(result.resetTime).toISOString());
  return response;
}

// Enhanced rate limiting with additional security features
export class SecurityEnhancedRateLimiter extends RateLimiter {
  private suspiciousIPs: Set<string> = new Set();
  private consecutiveFailures: Map<string, number> = new Map();

  constructor(config: RateLimitConfig, name: string) {
    super(config, name);
  }

  public async checkWithSecurityEnhancement(
    req: NextRequest,
    options: {
      recordFailure?: boolean;
      isSuspiciousRequest?: boolean;
    } = {}
  ): Promise<{
    allowed: boolean;
    limit: number;
    remaining: number;
    resetTime: number;
    retryAfter?: number;
    securityFlags?: string[];
  }> {
    const identifier = this.config.keyGenerator(req);
    const baseResult = await this.checkLimit(req);
    const securityFlags: string[] = [];

    // Track consecutive failures
    if (options.recordFailure) {
      const failures = this.consecutiveFailures.get(identifier) || 0;
      this.consecutiveFailures.set(identifier, failures + 1);
      
      if (failures >= 5) {
        this.suspiciousIPs.add(identifier);
        securityFlags.push('CONSECUTIVE_FAILURES');
      }
    } else {
      // Reset failure count on successful request
      this.consecutiveFailures.delete(identifier);
    }

    // Mark suspicious behavior
    if (options.isSuspiciousRequest) {
      this.suspiciousIPs.add(identifier);
      securityFlags.push('SUSPICIOUS_BEHAVIOR');
    }

    // Apply stricter limits for suspicious IPs
    if (this.suspiciousIPs.has(identifier)) {
      securityFlags.push('ENHANCED_MONITORING');
      // Reduce remaining requests for suspicious IPs
      const adjustedResult = {
        ...baseResult,
        remaining: Math.floor(baseResult.remaining / 2),
      };
      
      if (adjustedResult.remaining <= 0 && baseResult.allowed) {
        adjustedResult.allowed = false;
        adjustedResult.retryAfter = Math.ceil((baseResult.resetTime - Date.now()) / 1000);
      }
      
      return { ...adjustedResult, securityFlags };
    }

    return { ...baseResult, securityFlags };
  }
} 