import nodemailer from 'nodemailer';

// Create transporter (configure based on your email provider)
const transporter = nodemailer.createTransport({
  host: process.env.SMTP_HOST || 'smtp.gmail.com',
  port: parseInt(process.env.SMTP_PORT || '587'),
  secure: false, // true for 465, false for other ports
  auth: {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASS,
  },
});

export async function sendPasswordResetEmail(
  email: string, 
  resetToken: string, 
  clientName: string
) {
  const resetUrl = `${process.env.NEXTAUTH_URL}?mode=reset&token=${resetToken}`;
  
  const mailOptions = {
    from: `"${process.env.COMPANY_NAME || 'Mocky Digital'}" <${process.env.SMTP_FROM || process.env.SMTP_USER}>`,
    to: email,
    subject: 'Password Reset Request - Client Portal',
    html: `
      <div style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; max-width: 600px; margin: 0 auto; background: #ffffff; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 20px rgba(0,0,0,0.1);">
        <!-- Header with Logo and Brand Colors -->
        <div style="background: linear-gradient(135deg, #0A2647 0%, #**********%); padding: 40px 30px; text-align: center;">
          <img src="${process.env.NEXTAUTH_URL || 'https://mocky.co.ke'}/images/logo.png" alt="Mocky Digital Logo" style="width: 60px; height: 60px; margin-bottom: 20px; border-radius: 8px;">
          <h1 style="color: white; margin: 0; font-size: 28px; font-weight: 700; letter-spacing: -0.5px;">🔐 Password Reset Request</h1>
          <p style="color: rgba(255,255,255,0.9); margin: 10px 0 0 0; font-size: 16px;">Secure access to your client portal</p>
        </div>

        <!-- Content Body -->
        <div style="background: #ffffff; padding: 40px 30px;">
          <h2 style="color: #0A2647; margin: 0 0 20px 0; font-size: 24px; font-weight: 600;">Hello ${clientName}! 👋</h2>

          <p style="color: #4a5568; font-size: 16px; line-height: 1.6; margin-bottom: 25px;">
            We received a request to reset your password for your client portal account.
            If you didn't make this request, you can safely ignore this email.
          </p>

          <!-- CTA Button -->
          <div style="text-align: center; margin: 35px 0;">
            <a href="${resetUrl}"
               style="background: linear-gradient(135deg, #FF5400 0%, #ff7633 100%);
                      color: white;
                      padding: 16px 32px;
                      text-decoration: none;
                      border-radius: 8px;
                      font-weight: 600;
                      font-size: 16px;
                      display: inline-block;
                      box-shadow: 0 4px 15px rgba(255, 84, 0, 0.3);
                      transition: all 0.3s ease;">
              🔓 Reset My Password
            </a>
          </div>

          <!-- Security Notice -->
          <div style="background: #f7fafc; border-left: 4px solid #FF5400; padding: 15px 20px; margin: 25px 0; border-radius: 0 8px 8px 0;">
            <p style="color: #2d3748; font-size: 14px; margin: 0; font-weight: 500;">
              ⏰ This link will expire in 1 hour for security reasons.
            </p>
          </div>

          <hr style="border: none; border-top: 1px solid #e2e8f0; margin: 30px 0;">

          <!-- Fallback Link -->
          <p style="color: #718096; font-size: 13px; line-height: 1.5;">
            If the button doesn't work, copy and paste this link into your browser:<br>
            <a href="${resetUrl}" style="color: #205295; word-break: break-all;">${resetUrl}</a>
          </p>
        </div>

        <!-- Footer -->
        <div style="background: #f7fafc; padding: 25px 30px; text-align: center; border-top: 1px solid #e2e8f0;">
          <p style="color: #718096; font-size: 14px; margin: 0 0 10px 0;">
            Best regards,<br>
            <strong style="color: #0A2647;">${process.env.COMPANY_NAME || 'Mocky Digital'} Team</strong>
          </p>
          <p style="color: #a0aec0; font-size: 12px; margin: 0;">
            📧 <EMAIL> | 📱 +*********** 670<br>
            Professional Design & Digital Services
          </p>
        </div>
      </div>
    `,
  };

  try {
    await transporter.sendMail(mailOptions);
    return { success: true };
  } catch (error) {
    console.error('Failed to send password reset email:', error);
    return { success: false, error };
  }
}

export async function sendWelcomeEmail(
  email: string, 
  clientName: string, 
  verificationToken?: string
) {
  const verifyUrl = verificationToken 
          ? `${process.env.NEXTAUTH_URL}?mode=verify&token=${verificationToken}`
      : `${process.env.NEXTAUTH_URL}`;
  
  const mailOptions = {
    from: `"${process.env.COMPANY_NAME || 'Mocky Digital'}" <${process.env.SMTP_FROM || process.env.SMTP_USER}>`,
    to: email,
    subject: 'Welcome to Our Client Portal!',
    html: `
      <div style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; max-width: 600px; margin: 0 auto; background: #ffffff; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 20px rgba(0,0,0,0.1);">
        <!-- Header with Logo and Brand Colors -->
        <div style="background: linear-gradient(135deg, #0A2647 0%, #**********%); padding: 40px 30px; text-align: center;">
          <img src="${process.env.NEXTAUTH_URL || 'https://mocky.co.ke'}/images/logo.png" alt="Mocky Digital Logo" style="width: 60px; height: 60px; margin-bottom: 20px; border-radius: 8px;">
          <h1 style="color: white; margin: 0; font-size: 28px; font-weight: 700; letter-spacing: -0.5px;">🎉 Welcome to Our Client Portal!</h1>
          <p style="color: rgba(255,255,255,0.9); margin: 10px 0 0 0; font-size: 16px;">Your gateway to professional digital services</p>
        </div>
        
        <div style="background: #f8f9fa; padding: 30px; border-radius: 0 0 10px 10px;">
          <h2 style="color: #333; margin-top: 0;">Hello ${clientName},</h2>
          
          <p style="color: #666; font-size: 16px; line-height: 1.5;">
            Welcome to our client portal! Your account has been created successfully. 
            You now have access to track your projects, view documents, and collaborate with our team.
          </p>
          
          ${verificationToken ? `
            <div style="text-align: center; margin: 30px 0;">
              <a href="${verifyUrl}" 
                 style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
                        color: white; 
                        padding: 15px 30px; 
                        text-decoration: none; 
                        border-radius: 8px; 
                        font-weight: bold; 
                        display: inline-block;">
                Verify Email & Access Portal
              </a>
            </div>
          ` : `
            <div style="text-align: center; margin: 30px 0;">
              <a href="${verifyUrl}" 
                 style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
                        color: white; 
                        padding: 15px 30px; 
                        text-decoration: none; 
                        border-radius: 8px; 
                        font-weight: bold; 
                        display: inline-block;">
                Access Portal
              </a>
            </div>
          `}
          
          <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #333; margin-top: 0;">What you can do in the portal:</h3>
            <ul style="color: #666; line-height: 1.6;">
              <li>📊 View real-time project progress and milestones</li>
              <li>📁 Access and download project documents</li>
              <li>💬 Submit feedback and communicate with our team</li>
              <li>📋 Approve project milestones and deliverables</li>
              <li>💳 View invoices and payment information</li>
              <li>📤 Upload files and documents securely</li>
            </ul>
          </div>
          
          <p style="color: #666; font-size: 16px; line-height: 1.5;">
            If you have any questions or need assistance, please don't hesitate to contact our support team.
          </p>
          
          <p style="color: #999; font-size: 12px; margin-top: 30px;">
            Best regards,<br>
            ${process.env.COMPANY_NAME || 'Mocky Digital'} Team
          </p>
        </div>
      </div>
    `,
  };

  try {
    await transporter.sendMail(mailOptions);
    return { success: true };
  } catch (error) {
    console.error('Failed to send welcome email:', error);
    return { success: false, error };
  }
}

export async function sendNotificationEmail(
  email: string, 
  clientName: string, 
  title: string, 
  message: string, 
  actionUrl?: string
) {
  const mailOptions = {
    from: `"${process.env.COMPANY_NAME || 'Mocky Digital'}" <${process.env.SMTP_FROM || process.env.SMTP_USER}>`,
    to: email,
    subject: `${title} - Client Portal Notification`,
    html: `
      <div style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; max-width: 600px; margin: 0 auto; background: #ffffff; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 20px rgba(0,0,0,0.1);">
        <!-- Header with Logo and Brand Colors -->
        <div style="background: linear-gradient(135deg, #0A2647 0%, #**********%); padding: 40px 30px; text-align: center;">
          <img src="${process.env.NEXTAUTH_URL || 'https://mocky.co.ke'}/images/logo.png" alt="Mocky Digital Logo" style="width: 60px; height: 60px; margin-bottom: 20px; border-radius: 8px;">
          <h1 style="color: white; margin: 0; font-size: 26px; font-weight: 700; letter-spacing: -0.5px;">📢 ${title}</h1>
          <p style="color: rgba(255,255,255,0.9); margin: 10px 0 0 0; font-size: 16px;">Client Portal Notification</p>
        </div>

        <!-- Content Body -->
        <div style="background: #ffffff; padding: 40px 30px;">
          <h2 style="color: #0A2647; margin: 0 0 20px 0; font-size: 24px; font-weight: 600;">Hello ${clientName}! 👋</h2>

          <p style="color: #4a5568; font-size: 16px; line-height: 1.6; margin-bottom: 25px;">
            ${message}
          </p>

          ${actionUrl ? `
            <!-- CTA Button -->
            <div style="text-align: center; margin: 35px 0;">
              <a href="${actionUrl}"
                 style="background: linear-gradient(135deg, #FF5400 0%, #ff7633 100%);
                        color: white;
                        padding: 16px 32px;
                        text-decoration: none;
                        border-radius: 8px;
                        font-weight: 600;
                        font-size: 16px;
                        display: inline-block;
                        box-shadow: 0 4px 15px rgba(255, 84, 0, 0.3);
                        transition: all 0.3s ease;">
                🔗 View in Portal
              </a>
            </div>
          ` : ''}
        </div>

        <!-- Footer -->
        <div style="background: #f7fafc; padding: 25px 30px; text-align: center; border-top: 1px solid #e2e8f0;">
          <p style="color: #718096; font-size: 14px; margin: 0 0 10px 0;">
            Best regards,<br>
            <strong style="color: #0A2647;">${process.env.COMPANY_NAME || 'Mocky Digital'} Team</strong>
          </p>
          <p style="color: #a0aec0; font-size: 12px; margin: 0;">
            📧 <EMAIL> | 📱 +*********** 670<br>
            Professional Design & Digital Services
          </p>
        </div>
      </div>
    `,
  };

  try {
    await transporter.sendMail(mailOptions);
    return { success: true };
  } catch (error) {
    console.error('Failed to send notification email:', error);
    return { success: false, error };
  }
} 