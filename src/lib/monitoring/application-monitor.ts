import { NextRequest } from 'next/server';

export interface PerformanceMetric {
  name: string;
  value: number;
  timestamp: number;
  labels?: Record<string, string>;
  unit?: 'ms' | 'bytes' | 'count' | 'percent' | 'rate';
}

export interface SecurityEvent {
  type: 'authentication' | 'authorization' | 'validation' | 'rate_limit' | 'csrf' | 'suspicious';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  details: Record<string, any>;
  timestamp: number;
  requestId?: string;
  userAgent?: string;
  ipAddress?: string;
  userId?: string;
}

export interface ErrorEvent {
  error: Error;
  context: Record<string, any>;
  timestamp: number;
  requestId?: string;
  userId?: string;
  stackTrace?: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

export interface HealthCheckResult {
  status: 'healthy' | 'degraded' | 'unhealthy';
  checks: Record<string, {
    status: 'healthy' | 'degraded' | 'unhealthy';
    responseTime?: number;
    error?: string;
    lastChecked: number;
  }>;
  uptime: number;
  version: string;
}

interface MonitoringConfig {
  enableMetrics: boolean;
  enableSecurityMonitoring: boolean;
  enableErrorTracking: boolean;
  enableHealthChecks: boolean;
  metricsRetentionHours: number;
  alertThresholds: {
    errorRate: number;
    responseTime: number;
    memoryUsage: number;
    cpuUsage: number;
  };
}

const DEFAULT_CONFIG: MonitoringConfig = {
  enableMetrics: true,
  enableSecurityMonitoring: true,
  enableErrorTracking: true,
  enableHealthChecks: true,
  metricsRetentionHours: 24,
  alertThresholds: {
    errorRate: 5, // 5% error rate
    responseTime: 5000, // 5 seconds
    memoryUsage: 80, // 80% memory usage
    cpuUsage: 80, // 80% CPU usage
  },
};

export class ApplicationMonitor {
  private static instance: ApplicationMonitor;
  private config: MonitoringConfig;
  private metrics: Map<string, PerformanceMetric[]> = new Map();
  private securityEvents: SecurityEvent[] = [];
  private errorEvents: ErrorEvent[] = [];
  private healthChecks: Map<string, () => Promise<boolean>> = new Map();
  private startTime: number = Date.now();

  private constructor(config?: Partial<MonitoringConfig>) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.setupCleanupInterval();
  }

  public static getInstance(config?: Partial<MonitoringConfig>): ApplicationMonitor {
    if (!ApplicationMonitor.instance) {
      ApplicationMonitor.instance = new ApplicationMonitor(config);
    }
    return ApplicationMonitor.instance;
  }

  /**
   * Record a performance metric
   */
  public recordMetric(metric: PerformanceMetric): void {
    if (!this.config.enableMetrics) return;

    const key = `${metric.name}:${JSON.stringify(metric.labels || {})}`;
    if (!this.metrics.has(key)) {
      this.metrics.set(key, []);
    }

    const metrics = this.metrics.get(key)!;
    metrics.push(metric);

    // Keep only recent metrics
    const cutoff = Date.now() - (this.config.metricsRetentionHours * 60 * 60 * 1000);
    this.metrics.set(key, metrics.filter(m => m.timestamp > cutoff));

    // Check thresholds and trigger alerts if needed
    this.checkMetricThresholds(metric);
  }

  /**
   * Record a security event
   */
  public recordSecurityEvent(event: SecurityEvent): void {
    if (!this.config.enableSecurityMonitoring) return;

    this.securityEvents.push(event);

    // Log security events immediately
    const logLevel = event.severity === 'critical' ? 'error' : 
                    event.severity === 'high' ? 'warn' : 'info';
    
    console[logLevel](`[SECURITY:${event.type.toUpperCase()}] ${event.message}`, {
      ...event.details,
      requestId: event.requestId,
      timestamp: new Date(event.timestamp).toISOString(),
    });

    // Trigger immediate alerts for critical events
    if (event.severity === 'critical') {
      this.triggerSecurityAlert(event);
    }

    // Keep only recent security events (last 7 days)
    const cutoff = Date.now() - (7 * 24 * 60 * 60 * 1000);
    this.securityEvents = this.securityEvents.filter(e => e.timestamp > cutoff);
  }

  /**
   * Record an error event
   */
  public recordError(errorEvent: ErrorEvent): void {
    if (!this.config.enableErrorTracking) return;

    this.errorEvents.push(errorEvent);

    // Log error with full context
    console.error(`[ERROR] ${errorEvent.error.message}`, {
      ...errorEvent.context,
      requestId: errorEvent.requestId,
      userId: errorEvent.userId,
      stackTrace: errorEvent.stackTrace || errorEvent.error.stack,
      timestamp: new Date(errorEvent.timestamp).toISOString(),
    });

    // Keep only recent errors (last 24 hours)
    const cutoff = Date.now() - (24 * 60 * 60 * 1000);
    this.errorEvents = this.errorEvents.filter(e => e.timestamp > cutoff);

    // Check error rate threshold
    this.checkErrorRate();
  }

  /**
   * Register a health check
   */
  public registerHealthCheck(name: string, check: () => Promise<boolean>): void {
    this.healthChecks.set(name, check);
  }

  /**
   * Run all health checks
   */
  public async runHealthChecks(): Promise<HealthCheckResult> {
    const checks: HealthCheckResult['checks'] = {};
    let overallStatus: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';

    for (const [name, check] of this.healthChecks) {
      const startTime = Date.now();
      try {
        const result = await Promise.race([
          check(),
          new Promise<boolean>((_, reject) => 
            setTimeout(() => reject(new Error('Health check timeout')), 5000)
          )
        ]);

        const responseTime = Date.now() - startTime;
        checks[name] = {
          status: result ? 'healthy' : 'unhealthy',
          responseTime,
          lastChecked: Date.now(),
        };

        if (!result) {
          overallStatus = overallStatus === 'healthy' ? 'degraded' : 'unhealthy';
        }
      } catch (error) {
        checks[name] = {
          status: 'unhealthy',
          error: error instanceof Error ? error.message : 'Unknown error',
          lastChecked: Date.now(),
        };
        overallStatus = 'unhealthy';
      }
    }

    return {
      status: overallStatus,
      checks,
      uptime: Date.now() - this.startTime,
      version: process.env.npm_package_version || '1.0.0',
    };
  }

  /**
   * Get performance metrics
   */
  public getMetrics(name?: string, labels?: Record<string, string>): PerformanceMetric[] {
    if (name) {
      const key = `${name}:${JSON.stringify(labels || {})}`;
      return this.metrics.get(key) || [];
    }

    // Return all metrics
    const allMetrics: PerformanceMetric[] = [];
    for (const metrics of this.metrics.values()) {
      allMetrics.push(...metrics);
    }
    return allMetrics.sort((a, b) => b.timestamp - a.timestamp);
  }

  /**
   * Get security events
   */
  public getSecurityEvents(type?: SecurityEvent['type'], severity?: SecurityEvent['severity']): SecurityEvent[] {
    let events = [...this.securityEvents];

    if (type) {
      events = events.filter(e => e.type === type);
    }

    if (severity) {
      events = events.filter(e => e.severity === severity);
    }

    return events.sort((a, b) => b.timestamp - a.timestamp);
  }

  /**
   * Get error events
   */
  public getErrorEvents(): ErrorEvent[] {
    return [...this.errorEvents].sort((a, b) => b.timestamp - a.timestamp);
  }

  /**
   * Get monitoring statistics
   */
  public getStats(): {
    totalMetrics: number;
    totalSecurityEvents: number;
    totalErrors: number;
    errorRate: number;
    avgResponseTime: number;
    uptime: number;
  } {
    const now = Date.now();
    const lastHour = now - (60 * 60 * 1000);

    // Calculate error rate (last hour)
    const recentErrors = this.errorEvents.filter(e => e.timestamp > lastHour);
    const recentRequests = this.getMetrics('http_request_duration').filter(m => m.timestamp > lastHour);
    const errorRate = recentRequests.length > 0 ? (recentErrors.length / recentRequests.length) * 100 : 0;

    // Calculate average response time (last hour)
    const avgResponseTime = recentRequests.length > 0 
      ? recentRequests.reduce((sum, m) => sum + m.value, 0) / recentRequests.length 
      : 0;

    return {
      totalMetrics: Array.from(this.metrics.values()).reduce((sum, metrics) => sum + metrics.length, 0),
      totalSecurityEvents: this.securityEvents.length,
      totalErrors: this.errorEvents.length,
      errorRate,
      avgResponseTime,
      uptime: now - this.startTime,
    };
  }

  /**
   * Middleware to track HTTP requests
   */
  public trackRequest(request: NextRequest, startTime?: number): {
    finish: (statusCode: number, error?: Error) => void;
  } {
    const requestStartTime = startTime || Date.now();
    const requestId = crypto.randomUUID();
    const method = request.method;
    const path = new URL(request.url).pathname;
    const userAgent = request.headers.get('user-agent') || 'unknown';
    const ipAddress = request.headers.get('x-forwarded-for') || 
                     request.headers.get('x-real-ip') || 'unknown';

    return {
      finish: (statusCode: number, error?: Error) => {
        const duration = Date.now() - requestStartTime;

        // Record performance metric
        this.recordMetric({
          name: 'http_request_duration',
          value: duration,
          timestamp: Date.now(),
          labels: {
            method,
            path,
            status: statusCode.toString(),
          },
          unit: 'ms',
        });

        // Record error if present
        if (error) {
          this.recordError({
            error,
            context: {
              method,
              path,
              statusCode,
              userAgent,
              ipAddress,
            },
            timestamp: Date.now(),
            requestId,
            severity: statusCode >= 500 ? 'high' : 'medium',
          });
        }

        // Record security events for suspicious activity
        if (statusCode === 401 || statusCode === 403) {
          this.recordSecurityEvent({
            type: 'authorization',
            severity: 'medium',
            message: `Access denied: ${method} ${path}`,
            details: {
              method,
              path,
              statusCode,
              userAgent,
            },
            timestamp: Date.now(),
            requestId,
            ipAddress,
          });
        }
      },
    };
  }

  /**
   * Check metric thresholds and trigger alerts
   */
  private checkMetricThresholds(metric: PerformanceMetric): void {
    const { alertThresholds } = this.config;

    if (metric.name === 'http_request_duration' && metric.value > alertThresholds.responseTime) {
      console.warn(`[ALERT] High response time: ${metric.value}ms > ${alertThresholds.responseTime}ms`, {
        labels: metric.labels,
        timestamp: new Date(metric.timestamp).toISOString(),
      });
    }
  }

  /**
   * Check error rate and trigger alerts
   */
  private checkErrorRate(): void {
    const now = Date.now();
    const lastHour = now - (60 * 60 * 1000);
    
    const recentErrors = this.errorEvents.filter(e => e.timestamp > lastHour);
    const recentRequests = this.getMetrics('http_request_duration').filter(m => m.timestamp > lastHour);
    
    if (recentRequests.length > 10) { // Only check if we have enough data
      const errorRate = (recentErrors.length / recentRequests.length) * 100;
      
      if (errorRate > this.config.alertThresholds.errorRate) {
        console.error(`[ALERT] High error rate: ${errorRate.toFixed(2)}% > ${this.config.alertThresholds.errorRate}%`, {
          recentErrors: recentErrors.length,
          recentRequests: recentRequests.length,
          timestamp: new Date().toISOString(),
        });
      }
    }
  }

  /**
   * Trigger security alert
   */
  private triggerSecurityAlert(event: SecurityEvent): void {
    console.error(`[SECURITY ALERT] ${event.type}: ${event.message}`, {
      severity: event.severity,
      details: event.details,
      requestId: event.requestId,
      ipAddress: event.ipAddress,
      userAgent: event.userAgent,
      timestamp: new Date(event.timestamp).toISOString(),
    });

    // In production, this would integrate with alerting systems like:
    // - Email notifications
    // - Slack/Discord webhooks
    // - PagerDuty
    // - Datadog/NewRelic
  }

  /**
   * Setup cleanup interval to prevent memory leaks
   */
  private setupCleanupInterval(): void {
    setInterval(() => {
      const now = Date.now();
      const metricsRetentionMs = this.config.metricsRetentionHours * 60 * 60 * 1000;
      
      // Clean up old metrics
      for (const [key, metrics] of this.metrics) {
        const filteredMetrics = metrics.filter(m => now - m.timestamp < metricsRetentionMs);
        if (filteredMetrics.length === 0) {
          this.metrics.delete(key);
        } else {
          this.metrics.set(key, filteredMetrics);
        }
      }

      // Clean up old security events (7 days)
      const securityRetentionMs = 7 * 24 * 60 * 60 * 1000;
      this.securityEvents = this.securityEvents.filter(e => now - e.timestamp < securityRetentionMs);

      // Clean up old error events (24 hours)
      const errorRetentionMs = 24 * 60 * 60 * 1000;
      this.errorEvents = this.errorEvents.filter(e => now - e.timestamp < errorRetentionMs);
    }, 60 * 60 * 1000); // Run every hour
  }
}

// Export singleton instance
export const monitor = ApplicationMonitor.getInstance();

// Register default health checks
monitor.registerHealthCheck('memory', async () => {
  const memUsage = process.memoryUsage();
  const totalMem = memUsage.heapTotal;
  const usedMem = memUsage.heapUsed;
  const memoryUsagePercent = (usedMem / totalMem) * 100;
  
  return memoryUsagePercent < 90; // Healthy if memory usage < 90%
});

monitor.registerHealthCheck('uptime', async () => {
  return process.uptime() > 0; // Always healthy if process is running
});

// Utility functions for easy integration
export function trackPerformance(name: string, labels?: Record<string, string>) {
  const startTime = Date.now();
  return {
    finish: (additionalLabels?: Record<string, string>) => {
      monitor.recordMetric({
        name,
        value: Date.now() - startTime,
        timestamp: Date.now(),
        labels: { ...labels, ...additionalLabels },
        unit: 'ms',
      });
    },
  };
}

export function recordSecurityEvent(
  type: SecurityEvent['type'],
  severity: SecurityEvent['severity'],
  message: string,
  details: Record<string, any> = {},
  requestId?: string
): void {
  monitor.recordSecurityEvent({
    type,
    severity,
    message,
    details,
    timestamp: Date.now(),
    requestId,
  });
} 