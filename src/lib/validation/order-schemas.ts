import { z } from 'zod';

// Phone number validation for Kenya
const kenyanPhoneRegex = /^(\+254|254|0)?([17]\d{8}|[4]\d{8})$/;

// Customer information schema
export const CustomerInfoSchema = z.object({
  name: z.string()
    .min(2, 'Name must be at least 2 characters')
    .max(100, 'Name must not exceed 100 characters')
    .regex(/^[a-zA-Z\s\-'\.]+$/, 'Name contains invalid characters')
    .transform(str => str.trim()),
  
  phone: z.string()
    .regex(kenyanPhoneRegex, 'Please enter a valid Kenyan phone number (e.g., 0712345678)')
    .transform(phone => {
      // Normalize phone number to +254 format
      let normalized = phone.replace(/\s+/g, '');
      if (normalized.startsWith('0')) {
        normalized = '+254' + normalized.slice(1);
      } else if (normalized.startsWith('254')) {
        normalized = '+' + normalized;
      } else if (!normalized.startsWith('+254')) {
        normalized = '+254' + normalized;
      }
      return normalized;
    }),
  
  email: z.string()
    .email('Please enter a valid email address')
    .max(100, 'Email must not exceed 100 characters')
    .transform(str => str.toLowerCase().trim()),
  
  notes: z.string()
    .max(1000, 'Notes must not exceed 1000 characters')
    .optional()
    .default('')
});

// Order configuration schema
export const OrderConfigSchema = z.object({
  orderType: z.enum(['print', 'design'], {
    errorMap: () => ({ message: 'Please select either print or design service' })
  }),
  
  quantity: z.number()
    .min(1, 'Quantity must be at least 1')
    .max(10000, 'Quantity cannot exceed 10,000')
    .int('Quantity must be a whole number'),
  
  designBrief: z.string()
    .max(2000, 'Design brief must not exceed 2000 characters')
    .optional()
    .default(''),
  
  artworkFiles: z.array(z.object({
    name: z.string(),
    fileName: z.string().optional(),
    filePath: z.string().optional(),
    uploaded: z.boolean().optional(),
    size: z.number().optional(),
    type: z.string().optional()
  })).optional().default([])
});

// Complete order schema
export const OrderFormSchema = z.object({
  customerInfo: CustomerInfoSchema,
  orderConfig: OrderConfigSchema
}).refine((data) => {
  const { orderConfig } = data;
  
  // If it's a print order and no design service, require artwork files
  if (orderConfig.orderType === 'print' && 
      (!orderConfig.designBrief || orderConfig.designBrief.trim().length < 10) &&
      (!orderConfig.artworkFiles || orderConfig.artworkFiles.length === 0)) {
    return false;
  }
  
  return true;
}, {
  message: 'For print orders, either provide a design brief or upload artwork files',
  path: ['orderConfig', 'artworkFiles']
});

// Individual field validators
export const fieldValidators = {
  name: (value: string) => {
    const result = CustomerInfoSchema.shape.name.safeParse(value);
    return result.success ? null : result.error.errors[0]?.message || 'Invalid name';
  },
  
  phone: (value: string) => {
    const result = CustomerInfoSchema.shape.phone.safeParse(value);
    return result.success ? null : result.error.errors[0]?.message || 'Invalid phone number';
  },
  
  email: (value: string) => {
    const result = CustomerInfoSchema.shape.email.safeParse(value);
    return result.success ? null : result.error.errors[0]?.message || 'Invalid email';
  },
  
  quantity: (value: number) => {
    const result = OrderConfigSchema.shape.quantity.safeParse(value);
    return result.success ? null : result.error.errors[0]?.message || 'Invalid quantity';
  },
  
  designBrief: (value: string, orderType?: string) => {
    if (orderType === 'design' && (!value || value.trim().length < 10)) {
      return 'Design brief must be at least 10 characters for design services';
    }
    
    if (value && value.length > 2000) {
      return 'Design brief must not exceed 2000 characters';
    }
    
    return null;
  }
};

// Export types
export type CustomerInfo = z.infer<typeof CustomerInfoSchema>;
export type OrderConfig = z.infer<typeof OrderConfigSchema>;
export type OrderForm = z.infer<typeof OrderFormSchema>; 