import { z } from 'zod';
import { NextRequest } from 'next/server';

// Base validation schemas for common types
export const BaseSchemas = {
  uuid: z.string().uuid('Invalid UUID format'),
  email: z.string().email('Invalid email format'),
  phone: z.string().regex(/^[\+]?[1-9][\d]{0,15}$/, 'Invalid phone number'),
  url: z.string().url('Invalid URL format'),
  positiveNumber: z.number().positive('Must be a positive number'),
  nonEmptyString: z.string().min(1, 'Cannot be empty').max(1000, 'Text too long'),
  slug: z.string().regex(/^[a-z0-9]+(?:-[a-z0-9]+)*$/, 'Invalid slug format'),
  price: z.number().min(0, 'Price cannot be negative').max(1000000, 'Price too high'),
  safeText: z.string().max(5000, 'Text too long'),
};

// Security patterns to detect malicious input
const SECURITY_PATTERNS = [
  // SQL Injection patterns
  {
    pattern: /(\b(union|select|insert|update|delete|drop|create|alter|exec|execute)\b)/i,
    type: 'SQL_INJECTION',
    severity: 'HIGH'
  },
  {
    pattern: /('|(\\')|(;\s*--)|(;\s*\/\*))/i,
    type: 'SQL_INJECTION',
    severity: 'HIGH'
  },
  
  // XSS patterns
  {
    pattern: /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
    type: 'XSS',
    severity: 'HIGH'
  },
  {
    pattern: /javascript:/i,
    type: 'XSS',
    severity: 'MEDIUM'
  },
  {
    pattern: /on\w+\s*=/i,
    type: 'XSS',
    severity: 'MEDIUM'
  },
  
  // Path Traversal
  {
    pattern: /\.\.\//g,
    type: 'PATH_TRAVERSAL',
    severity: 'HIGH'
  },
  {
    pattern: /\.\.\\/g,
    type: 'PATH_TRAVERSAL',
    severity: 'HIGH'
  },
  
  // Command Injection
  {
    pattern: /[;&|`${}]/g,
    type: 'COMMAND_INJECTION',
    severity: 'HIGH'
  },
];

export class SecurityValidator {
  /**
   * Validate input against security patterns
   */
  static validateInput(input: string, fieldName: string): void {
    if (typeof input !== 'string') {
      return; // Only validate strings
    }

    for (const { pattern, type, severity } of SECURITY_PATTERNS) {
      if (pattern.test(input)) {
        console.error(`[SECURITY] ${severity} ${type} detected in field: ${fieldName}`);
        throw new Error(`Security violation detected in ${fieldName}: ${type}`);
      }
    }
  }

  /**
   * Validate HTTP headers for security issues
   */
  static validateHeaders(headers: Headers): void {
    const dangerousHeaders = [
      'x-forwarded-host',
      'x-forwarded-proto',
      'x-real-ip'
    ];

    for (const [name, value] of headers.entries()) {
      // Check for null bytes and control characters
      if (value.includes('\0') || /[\x00-\x1f\x7f-\x9f]/.test(value)) {
        throw new Error(`Security violation: Invalid characters in header ${name}`);
      }

      // Validate specific headers
      if (dangerousHeaders.includes(name.toLowerCase())) {
        this.validateInput(value, `header-${name}`);
      }

      // Check header value length
      if (value.length > 8192) { // 8KB limit
        throw new Error(`Security violation: Header ${name} too long`);
      }
    }
  }

  /**
   * Validate User-Agent header for suspicious patterns
   */
  static validateUserAgent(userAgent: string): void {
    if (!userAgent || userAgent.length === 0) {
      return; // Allow empty user agents
    }

    if (userAgent.length > 512) {
      throw new Error('Security violation: User agent too long');
    }

    // Check for suspicious patterns
    const suspiciousPatterns = [
      /sqlmap/i,
      /nikto/i,
      /nessus/i,
      /openvas/i,
      /burpsuite/i,
      /dirbuster/i,
      /gobuster/i,
      /ffuf/i
    ];

    if (suspiciousPatterns.some(pattern => pattern.test(userAgent))) {
      throw new Error('Security violation: Suspicious user agent detected');
    }
  }

  /**
   * Sanitize input by removing potentially dangerous characters
   */
  static sanitizeInput(input: string): string {
    if (typeof input !== 'string') {
      return input;
    }

    return input
      .trim()
      .replace(/[<>]/g, '') // Remove HTML brackets
      .replace(/['"]/g, '') // Remove quotes that could break SQL
      .replace(/[;&|`]/g, '') // Remove command injection chars
      .substring(0, 1000); // Limit length
  }

  /**
   * Deep validation and sanitization of objects
   */
  static validateAndSanitizeObject(obj: any, path: string = 'root'): any {
    if (typeof obj === 'string') {
      this.validateInput(obj, path);
      return this.sanitizeInput(obj);
    }
    
    if (Array.isArray(obj)) {
      return obj.map((item, index) => 
        this.validateAndSanitizeObject(item, `${path}[${index}]`)
      );
    }
    
    if (obj && typeof obj === 'object') {
      const result: any = {};
      for (const [key, value] of Object.entries(obj)) {
        // Validate the key itself
        this.validateInput(key, `${path}.key`);
        result[key] = this.validateAndSanitizeObject(value, `${path}.${key}`);
      }
      return result;
    }
    
    return obj;
  }
}

/**
 * Validate and sanitize request body with Zod schema
 */
export async function validateRequestBody<T>(
  request: NextRequest,
  schema: z.ZodSchema<T>
): Promise<T> {
  try {
    const body = await request.json();
    
    // Security validation first
    const sanitizedBody = SecurityValidator.validateAndSanitizeObject(body, 'request');
    
    // Then validate with Zod schema
    return schema.parse(sanitizedBody);
  } catch (error) {
    if (error instanceof z.ZodError) {
      const firstError = error.errors[0];
      if (firstError) {
        throw new Error(`Validation error in ${firstError.path.join('.')}: ${firstError.message}`);
      }
      throw new Error('Validation error occurred');
    }
    throw error;
  }
}

/**
 * Common validation schemas for API endpoints
 */
export const ApiSchemas = {
  // User authentication
  login: z.object({
    username: BaseSchemas.nonEmptyString,
    password: BaseSchemas.nonEmptyString,
    rememberMe: z.boolean().optional(),
  }),

  // User management
  createUser: z.object({
    username: z.string().min(3).max(50).regex(/^[a-zA-Z0-9_-]+$/, 'Invalid username format'),
    email: BaseSchemas.email,
    name: z.string().min(1).max(100).optional(),
    password: z.string().min(8).max(128),
    roleId: BaseSchemas.uuid,
  }),

  updateUser: z.object({
    username: z.string().min(3).max(50).regex(/^[a-zA-Z0-9_-]+$/, 'Invalid username format').optional(),
    email: BaseSchemas.email.optional(),
    name: z.string().min(1).max(100).optional(),
    active: z.boolean().optional(),
    roleId: BaseSchemas.uuid.optional(),
  }),

  // Catalogue management
  createCatalogueItem: z.object({
    service: BaseSchemas.nonEmptyString,
    description: BaseSchemas.safeText.optional(),
    price: BaseSchemas.price,
    category: BaseSchemas.nonEmptyString,
    icon: z.string().max(100).optional(),
    popular: z.boolean().default(false),
    features: z.array(z.string().max(200)).max(10).default([]),
  }),

  updateCatalogueItem: z.object({
    service: BaseSchemas.nonEmptyString.optional(),
    description: BaseSchemas.safeText.optional(),
    price: BaseSchemas.price.optional(),
    category: BaseSchemas.nonEmptyString.optional(),
    icon: z.string().max(100).optional(),
    popular: z.boolean().optional(),
    features: z.array(z.string().max(200)).max(10).optional(),
  }),

  // Search and filtering
  searchCatalogue: z.object({
    search: z.string().max(100).optional(),
    category: z.string().max(50).optional(),
    minPrice: z.number().min(0).optional(),
    maxPrice: z.number().min(0).optional(),
    limit: z.number().min(1).max(100).default(50),
    offset: z.number().min(0).default(0),
  }),
}; 