import Redis from 'ioredis';

// Redis configuration
const REDIS_CONFIG = {
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379'),
  password: process.env.REDIS_PASSWORD,
  db: parseInt(process.env.REDIS_DB || '0'),
  retryDelayOnFailover: 100,
  maxRetriesPerRequest: 3,
  lazyConnect: true,
  keepAlive: 30000,
  connectTimeout: 10000,
  commandTimeout: 5000,
};

// Create Redis client with error handling
let redis: Redis | null = null;

export function getRedisClient(): Redis | null {
  if (!redis) {
    try {
      redis = new Redis(REDIS_CONFIG);
      
      redis.on('connect', () => {
        console.log('✅ Redis connected successfully');
      });
      
      redis.on('error', (error) => {
        console.error('❌ Redis connection error:', error);
        redis = null;
      });
      
      redis.on('close', () => {
        console.log('🔌 Redis connection closed');
        redis = null;
      });
      
    } catch (error) {
      console.error('❌ Failed to create Redis client:', error);
      return null;
    }
  }
  
  return redis;
}

// Cache utilities with fallback to in-memory cache
const memoryCache = new Map<string, { data: any; expires: number }>();

export class CacheService {
  private redis: Redis | null;
  
  constructor() {
    this.redis = getRedisClient();
  }
  
  async get<T>(key: string): Promise<T | null> {
    try {
      // Try Redis first
      if (this.redis) {
        const data = await this.redis.get(key);
        if (data) {
          return JSON.parse(data);
        }
      }
      
      // Fallback to memory cache
      const memData = memoryCache.get(key);
      if (memData && memData.expires > Date.now()) {
        return memData.data;
      }
      
      return null;
    } catch (error) {
      console.error('Cache get error:', error);
      return null;
    }
  }
  
  async set(key: string, value: any, ttlSeconds: number = 3600): Promise<boolean> {
    try {
      const serialized = JSON.stringify(value);
      
      // Try Redis first
      if (this.redis) {
        await this.redis.setex(key, ttlSeconds, serialized);
        return true;
      }
      
      // Fallback to memory cache
      memoryCache.set(key, {
        data: value,
        expires: Date.now() + (ttlSeconds * 1000)
      });
      
      return true;
    } catch (error) {
      console.error('Cache set error:', error);
      return false;
    }
  }
  
  async del(key: string): Promise<boolean> {
    try {
      // Try Redis first
      if (this.redis) {
        await this.redis.del(key);
      }
      
      // Also remove from memory cache
      memoryCache.delete(key);
      
      return true;
    } catch (error) {
      console.error('Cache delete error:', error);
      return false;
    }
  }
  
  async flush(): Promise<boolean> {
    try {
      // Try Redis first
      if (this.redis) {
        await this.redis.flushdb();
      }
      
      // Also clear memory cache
      memoryCache.clear();
      
      return true;
    } catch (error) {
      console.error('Cache flush error:', error);
      return false;
    }
  }
  
  async exists(key: string): Promise<boolean> {
    try {
      // Try Redis first
      if (this.redis) {
        const exists = await this.redis.exists(key);
        return exists === 1;
      }
      
      // Fallback to memory cache
      const memData = memoryCache.get(key);
      return memData !== undefined && memData.expires > Date.now();
    } catch (error) {
      console.error('Cache exists error:', error);
      return false;
    }
  }

  async keys(pattern: string): Promise<string[]> {
    try {
      // Try Redis first
      if (this.redis) {
        const keys = await this.redis.keys(pattern);
        return keys;
      }
      
      // Fallback to memory cache pattern matching
      const now = Date.now();
      const matchingKeys: string[] = [];
      
      for (const [key, data] of memoryCache.entries()) {
        if (data.expires > now) {
          // Simple pattern matching for memory cache
          if (pattern === '*' || key.includes(pattern.replace('*', ''))) {
            matchingKeys.push(key);
          }
        }
      }
      
      return matchingKeys;
    } catch (error) {
      console.error('Cache keys error:', error);
      return [];
    }
  }

  async delMultiple(keys: string[]): Promise<boolean> {
    try {
      if (keys.length === 0) return true;
      
      // Try Redis first
      if (this.redis && keys.length > 0) {
        await this.redis.del(...keys);
      }
      
      // Also remove from memory cache
      for (const key of keys) {
        memoryCache.delete(key);
      }
      
      return true;
    } catch (error) {
      console.error('Cache delete multiple error:', error);
      return false;
    }
  }
  
  // Clean up expired memory cache entries
  private cleanupMemoryCache() {
    const now = Date.now();
    for (const [key, data] of memoryCache.entries()) {
      if (data.expires <= now) {
        memoryCache.delete(key);
      }
    }
  }
}

// Singleton cache service
export const cacheService = new CacheService();

// Cache key generators
export const CacheKeys = {
  images: (path: string) => `images:${path}`,
  portfolio: (category?: string) => category ? `portfolio:${category}` : 'portfolio:all',
  blog: (slug?: string) => slug ? `blog:${slug}` : 'blog:all',
  services: () => 'services:all',
  pricing: () => 'pricing:all',
  testimonials: () => 'testimonials:all',
  analytics: (metric: string, period: string) => `analytics:${metric}:${period}`,
  s3Config: () => 's3:config',
  userSession: (userId: string) => `session:${userId}`,
};

// Cache TTL constants (in seconds)
export const CacheTTL = {
  SHORT: 300,      // 5 minutes
  MEDIUM: 1800,    // 30 minutes
  LONG: 3600,      // 1 hour
  VERY_LONG: 86400, // 24 hours
  IMAGES: 604800,   // 1 week
};

// Clean up memory cache every 10 minutes
setInterval(() => {
  const cache = new CacheService();
  (cache as any).cleanupMemoryCache();
}, 10 * 60 * 1000);

export default cacheService;
