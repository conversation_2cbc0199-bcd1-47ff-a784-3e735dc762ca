import sharp from 'sharp';
import { cacheService, <PERSON>ache<PERSON>eys, CacheTTL } from './redis';

// Image optimization configuration
export interface ImageOptimizationConfig {
  quality: number;
  format: 'webp' | 'jpeg' | 'png' | 'avif';
  width?: number;
  height?: number;
  fit?: 'cover' | 'contain' | 'fill' | 'inside' | 'outside';
  progressive?: boolean;
  lossless?: boolean;
}

// Default optimization settings
const DEFAULT_CONFIG: ImageOptimizationConfig = {
  quality: 92,
  format: 'webp',
  progressive: true,
  fit: 'cover',
};

// Supported image formats
const SUPPORTED_FORMATS = ['jpeg', 'jpg', 'png', 'webp', 'avif', 'gif', 'svg'];

export class ImageOptimizationService {
  
  /**
   * Optimize an image buffer with the given configuration
   */
  async optimizeImage(
    buffer: Buffer,
    config: Partial<ImageOptimizationConfig> = {}
  ): Promise<{ buffer: Buffer; format: string; size: number }> {
    const finalConfig = { ...DEFAULT_CONFIG, ...config };
    
    try {
      let pipeline = sharp(buffer);
      
      // Get original metadata
      const metadata = await pipeline.metadata();
      
      // Resize if dimensions are specified
      if (finalConfig.width || finalConfig.height) {
        pipeline = pipeline.resize({
          width: finalConfig.width,
          height: finalConfig.height,
          fit: finalConfig.fit,
          withoutEnlargement: true,
        });
      }
      
      // Apply format-specific optimizations
      switch (finalConfig.format) {
        case 'webp':
          pipeline = pipeline.webp({
            quality: finalConfig.quality,
            lossless: finalConfig.lossless,
            effort: 4, // Reduced from 6 to 4 for better quality/speed balance
          });
          break;
          
        case 'avif':
          pipeline = pipeline.avif({
            quality: finalConfig.quality,
            lossless: finalConfig.lossless,
            effort: 6, // Reduced from 9 to 6 for better quality/speed balance
          });
          break;
          
        case 'jpeg':
          pipeline = pipeline.jpeg({
            quality: finalConfig.quality,
            progressive: finalConfig.progressive,
            mozjpeg: true, // Use mozjpeg encoder for better compression
          });
          break;
          
        case 'png':
          pipeline = pipeline.png({
            quality: finalConfig.quality,
            progressive: finalConfig.progressive,
            compressionLevel: 6, // Reduced from 9 to 6 for better quality
            adaptiveFiltering: true,
          });
          break;
      }
      
      const optimizedBuffer = await pipeline.toBuffer();
      
      return {
        buffer: optimizedBuffer,
        format: finalConfig.format,
        size: optimizedBuffer.length,
      };
      
    } catch (error) {
      console.error('Image optimization error:', error);
      throw new Error(`Failed to optimize image: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
  
  /**
   * Generate multiple sizes and formats for responsive images
   */
  async generateResponsiveImages(
    buffer: Buffer,
    sizes: number[] = [320, 640, 768, 1024, 1280, 1920]
  ): Promise<Array<{ buffer: Buffer; width: number; format: string; size: number }>> {
    const results = [];
    
    // Generate WebP versions for all sizes
    for (const width of sizes) {
      try {
        const webp = await this.optimizeImage(buffer, {
          width,
          format: 'webp',
          quality: 90,
        });
        
        results.push({
          buffer: webp.buffer,
          width,
          format: 'webp',
          size: webp.size,
        });
        
        // Also generate JPEG fallback for older browsers
        const jpeg = await this.optimizeImage(buffer, {
          width,
          format: 'jpeg',
          quality: 92,
        });
        
        results.push({
          buffer: jpeg.buffer,
          width,
          format: 'jpeg',
          size: jpeg.size,
        });
        
      } catch (error) {
        console.error(`Failed to generate responsive image for width ${width}:`, error);
      }
    }
    
    return results;
  }
  
  /**
   * Generate blur placeholder for progressive loading
   */
  async generateBlurPlaceholder(buffer: Buffer): Promise<string> {
    try {
      const placeholder = await sharp(buffer)
        .resize(20, 20, { fit: 'inside' })
        .blur(1)
        .jpeg({ quality: 20 })
        .toBuffer();
      
      return `data:image/jpeg;base64,${placeholder.toString('base64')}`;
    } catch (error) {
      console.error('Blur placeholder generation error:', error);
      return 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q==';
    }
  }
  
  /**
   * Check if image format is supported
   */
  isFormatSupported(format: string): boolean {
    return SUPPORTED_FORMATS.includes(format.toLowerCase());
  }
  
  /**
   * Get optimal format based on browser support
   */
  getOptimalFormat(acceptHeader: string = ''): 'avif' | 'webp' | 'jpeg' {
    if (acceptHeader.includes('image/avif')) {
      return 'avif';
    }
    if (acceptHeader.includes('image/webp')) {
      return 'webp';
    }
    return 'jpeg';
  }
  
  /**
   * Cache optimized image
   */
  async cacheOptimizedImage(
    key: string,
    buffer: Buffer,
    metadata: { format: string; width?: number; quality: number }
  ): Promise<void> {
    try {
      const cacheData = {
        buffer: buffer.toString('base64'),
        metadata,
        timestamp: Date.now(),
      };
      
      await cacheService.set(
        CacheKeys.images(`optimized:${key}`),
        cacheData,
        CacheTTL.IMAGES
      );
    } catch (error) {
      console.error('Failed to cache optimized image:', error);
    }
  }
  
  /**
   * Get cached optimized image
   */
  async getCachedOptimizedImage(key: string): Promise<{ buffer: Buffer; metadata: any } | null> {
    try {
      const cached = await cacheService.get(CacheKeys.images(`optimized:${key}`));
      
      if (cached && cached.buffer) {
        return {
          buffer: Buffer.from(cached.buffer, 'base64'),
          metadata: cached.metadata,
        };
      }
      
      return null;
    } catch (error) {
      console.error('Failed to get cached optimized image:', error);
      return null;
    }
  }
}

// Singleton instance
export const imageOptimizationService = new ImageOptimizationService();

// Utility functions
export function getImageDimensions(buffer: Buffer): Promise<{ width: number; height: number }> {
  return sharp(buffer).metadata().then(metadata => ({
    width: metadata.width || 0,
    height: metadata.height || 0,
  }));
}

export function calculateAspectRatio(width: number, height: number): number {
  return width / height;
}

export function getResponsiveSizes(maxWidth: number = 1920): number[] {
  const sizes = [320, 640, 768, 1024, 1280];
  return sizes.filter(size => size <= maxWidth).concat(maxWidth);
}

export default imageOptimizationService;
