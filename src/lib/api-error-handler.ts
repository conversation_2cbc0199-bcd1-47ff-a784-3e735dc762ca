import { NextResponse } from 'next/server';

// Standard error codes
export const ERROR_CODES = {
  // Authentication & Authorization
  AUTH_REQUIRED: 'AUTH_REQUIRED',
  AUTH_INVALID: 'AUTH_INVALID',
  PERMISSION_DENIED: 'PERMISSION_DENIED',
  
  // Validation
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  INVALID_INPUT: 'INVALID_INPUT',
  MISSING_REQUIRED_FIELD: 'MISSING_REQUIRED_FIELD',
  
  // Resource Management
  NOT_FOUND: 'NOT_FOUND',
  ALREADY_EXISTS: 'ALREADY_EXISTS',
  RESOURCE_CONFLICT: 'RESOURCE_CONFLICT',
  
  // Operations
  OPERATION_FAILED: 'OPERATION_FAILED',
  TIMEOUT: 'TIMEOUT',
  RATE_LIMITED: 'RATE_LIMITED',
  
  // File & Upload
  FILE_TOO_LARGE: 'FILE_TOO_LARGE',
  INVALID_FILE_TYPE: 'INVALID_FILE_TYPE',
  UPLOAD_FAILED: 'UPLOAD_FAILED',
  
  // Database
  DATABASE_ERROR: 'DATABASE_ERROR',
  CONNECTION_ERROR: 'CONNECTION_ERROR',
  
  // External Services
  EXTERNAL_SERVICE_ERROR: 'EXTERNAL_SERVICE_ERROR',
  
  // System
  INTERNAL_ERROR: 'INTERNAL_ERROR',
  SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE'
} as const;

export type ErrorCode = keyof typeof ERROR_CODES;

// Standard error response interface
export interface ApiErrorResponse {
  success: false;
  error: string;
  code: string;
  message: string;
  timestamp: string;
  details?: any;
  suggestions?: string[];
  requestId?: string;
}

// Standard success response interface
export interface ApiSuccessResponse<T = any> {
  success: true;
  data?: T;
  message?: string;
  timestamp: string;
  meta?: {
    totalCount?: number;
    page?: number;
    limit?: number;
    [key: string]: any;
  };
}

// Custom error class for API errors
export class ApiError extends Error {
  constructor(
    public code: ErrorCode,
    public message: string,
    public statusCode: number = 500,
    public details?: any,
    public suggestions?: string[]
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

// Standardized error handler
export function handleApiError(
  error: Error | ApiError | unknown,
  requestId?: string
): NextResponse<ApiErrorResponse> {
  console.error('[API Error]', { error, requestId });

  // Handle known ApiError instances
  if (error instanceof ApiError) {
    const response: ApiErrorResponse = {
      success: false,
      error: error.code,
      code: error.code,
      message: error.message,
      timestamp: new Date().toISOString(),
      ...(error.details && { details: error.details }),
      ...(error.suggestions && { suggestions: error.suggestions }),
      ...(requestId && { requestId })
    };
    return NextResponse.json(response, { status: error.statusCode });
  }

  // Handle standard Error instances
  if (error instanceof Error) {
    // Try to categorize common error patterns
    let code: ErrorCode = 'INTERNAL_ERROR';
    let statusCode = 500;
    let suggestions: string[] = [];

    if (error.message.includes('validation') || error.message.includes('invalid')) {
      code = 'VALIDATION_ERROR';
      statusCode = 400;
      suggestions = ['Check your input data', 'Ensure all required fields are provided'];
    } else if (error.message.includes('not found')) {
      code = 'NOT_FOUND';
      statusCode = 404;
      suggestions = ['Check the resource ID', 'Ensure the resource exists'];
    } else if (error.message.includes('already exists') || error.message.includes('duplicate')) {
      code = 'ALREADY_EXISTS';
      statusCode = 409;
      suggestions = ['Use a different identifier', 'Check for existing resources'];
    } else if (error.message.includes('timeout')) {
      code = 'TIMEOUT';
      statusCode = 408;
      suggestions = ['Try again in a moment', 'Check your connection'];
    } else if (error.message.includes('rate limit') || error.message.includes('too many')) {
      code = 'RATE_LIMITED';
      statusCode = 429;
      suggestions = ['Wait before trying again', 'Reduce request frequency'];
    }

    const response: ApiErrorResponse = {
      success: false,
      error: code,
      code: code,
      message: error.message,
      timestamp: new Date().toISOString(),
      suggestions,
      ...(requestId && { requestId })
    };
    return NextResponse.json(response, { status: statusCode });
  }

  // Handle unknown errors
  const response: ApiErrorResponse = {
    success: false,
    error: 'INTERNAL_ERROR',
    code: 'INTERNAL_ERROR',
    message: 'An unexpected error occurred',
    timestamp: new Date().toISOString(),
    suggestions: [
      'Try again in a moment',
      'Contact support if the issue persists'
    ],
    ...(requestId && { requestId })
  };
  return NextResponse.json(response, { status: 500 });
}

// Success response builder
export function createSuccessResponse<T>(
  data?: T,
  message?: string,
  meta?: any
): NextResponse<ApiSuccessResponse<T>> {
  const response: ApiSuccessResponse<T> = {
    success: true,
    timestamp: new Date().toISOString(),
    ...(data !== undefined && { data }),
    ...(message && { message }),
    ...(meta && { meta })
  };
  return NextResponse.json(response);
}

// Common error creators
export const createError = {
  validation: (message: string, details?: any) => 
    new ApiError('VALIDATION_ERROR', message, 400, details, [
      'Check your input data',
      'Ensure all required fields are provided'
    ]),
    
  notFound: (resource: string) => 
    new ApiError('NOT_FOUND', `${resource} not found`, 404, null, [
      'Check the resource ID',
      'Ensure the resource exists'
    ]),
    
  unauthorized: (message: string = 'Authentication required') =>
    new ApiError('AUTH_REQUIRED', message, 401, null, [
      'Please log in',
      'Check your authentication credentials'
    ]),
    
  forbidden: (message: string = 'Access denied') =>
    new ApiError('PERMISSION_DENIED', message, 403, null, [
      'Check your permissions',
      'Contact an administrator'
    ]),
    
  conflict: (message: string) =>
    new ApiError('ALREADY_EXISTS', message, 409, null, [
      'Use a different identifier',
      'Check for existing resources'
    ]),
    
  rateLimit: (message: string = 'Too many requests') =>
    new ApiError('RATE_LIMITED', message, 429, null, [
      'Wait before trying again',
      'Reduce request frequency'
    ]),
    
  internal: (message: string = 'Internal server error') =>
    new ApiError('INTERNAL_ERROR', message, 500, null, [
      'Try again in a moment',
      'Contact support if the issue persists'
    ])
};

// Middleware wrapper for standardized error handling
export function withErrorHandling<T extends any[], R>(
  handler: (...args: T) => Promise<NextResponse<R>>
) {
  return async (...args: T): Promise<NextResponse<R | ApiErrorResponse>> => {
    try {
      return await handler(...args);
    } catch (error) {
      return handleApiError(error);
    }
  };
} 