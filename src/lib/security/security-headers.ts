import { NextRequest, NextResponse } from 'next/server';

interface SecurityHeadersConfig {
  contentSecurityPolicy?: {
    directives: Record<string, string[]>;
    reportOnly?: boolean;
  };
  hsts?: {
    maxAge: number;
    includeSubDomains: boolean;
    preload: boolean;
  };
  frameOptions?: 'DENY' | 'SAMEORIGIN' | 'ALLOW-FROM';
  contentTypeOptions?: boolean;
  xssProtection?: boolean;
  referrerPolicy?: string;
  permissionsPolicy?: Record<string, string[]>;
  crossOriginEmbedderPolicy?: 'unsafe-none' | 'require-corp';
  crossOriginOpenerPolicy?: 'unsafe-none' | 'same-origin-allow-popups' | 'same-origin';
  crossOriginResourcePolicy?: 'same-site' | 'same-origin' | 'cross-origin';
}

const DEFAULT_CONFIG: SecurityHeadersConfig = {
  contentSecurityPolicy: {
    directives: {
      'default-src': ["'self'"],
      'script-src': [
        "'self'",
        "'unsafe-inline'", // Required for Next.js
        "'unsafe-eval'", // Required for development
        'https://www.googletagmanager.com',
        'https://www.google-analytics.com',
        'https://vercel.live',
      ],
      'style-src': [
        "'self'",
        "'unsafe-inline'", // Required for CSS-in-JS
        'https://fonts.googleapis.com',
      ],
      'img-src': [
        "'self'",
        'data:',
        'blob:',
        'https://*.amazonaws.com',
        'https://*.cloudinary.com',
        'https://*.unsplash.com',
        'https://www.google-analytics.com',
      ],
      'font-src': [
        "'self'",
        'https://fonts.gstatic.com',
      ],
      'connect-src': [
        "'self'",
        'https://www.google-analytics.com',
        'https://vitals.vercel-analytics.com',
        'wss://',
      ],
      'frame-src': [
        "'self'",
        'https://www.youtube.com',
        'https://player.vimeo.com',
      ],
      'object-src': ["'none'"],
      'base-uri': ["'self'"],
      'form-action': ["'self'"],
      'frame-ancestors': ["'none'"],
      'upgrade-insecure-requests': [],
    },
    reportOnly: process.env.NODE_ENV === 'development',
  },
  hsts: {
    maxAge: 31536000, // 1 year
    includeSubDomains: true,
    preload: true,
  },
  frameOptions: 'DENY',
  contentTypeOptions: true,
  xssProtection: true,
  referrerPolicy: 'strict-origin-when-cross-origin',
  permissionsPolicy: {
    camera: [],
    microphone: [],
    geolocation: [],
    'payment': [],
    'usb': [],
    'magnetometer': [],
    'gyroscope': [],
    'accelerometer': [],
  },
  crossOriginEmbedderPolicy: 'unsafe-none',
  crossOriginOpenerPolicy: 'same-origin-allow-popups',
  crossOriginResourcePolicy: 'cross-origin',
};

export class SecurityHeaders {
  private config: SecurityHeadersConfig;

  constructor(config?: Partial<SecurityHeadersConfig>) {
    this.config = this.mergeConfig(DEFAULT_CONFIG, config || {});
  }

  /**
   * Deep merge configuration objects
   */
  private mergeConfig(
    defaultConfig: SecurityHeadersConfig,
    userConfig: Partial<SecurityHeadersConfig>
  ): SecurityHeadersConfig {
    const merged = { ...defaultConfig };

    // Merge CSP directives
    if (userConfig.contentSecurityPolicy?.directives) {
      merged.contentSecurityPolicy = {
        ...defaultConfig.contentSecurityPolicy,
        directives: {
          ...defaultConfig.contentSecurityPolicy?.directives,
          ...userConfig.contentSecurityPolicy.directives,
        },
      };
    }

    // Merge other properties
    Object.keys(userConfig).forEach(key => {
      if (key !== 'contentSecurityPolicy') {
        (merged as any)[key] = { ...(defaultConfig as any)[key], ...(userConfig as any)[key] };
      }
    });

    return merged;
  }

  /**
   * Generate Content Security Policy header value
   */
  private generateCSP(): string {
    const { directives } = this.config.contentSecurityPolicy!;
    
    return Object.entries(directives)
      .map(([directive, sources]) => {
        if (sources.length === 0) {
          return directive;
        }
        return `${directive} ${sources.join(' ')}`;
      })
      .join('; ');
  }

  /**
   * Generate Permissions Policy header value
   */
  private generatePermissionsPolicy(): string {
    const { permissionsPolicy } = this.config;
    if (!permissionsPolicy) return '';

    return Object.entries(permissionsPolicy)
      .map(([directive, allowlist]) => {
        if (allowlist.length === 0) {
          return `${directive}=()`;
        }
        return `${directive}=(${allowlist.map(origin => `"${origin}"`).join(' ')})`;
      })
      .join(', ');
  }

  /**
   * Apply security headers to a response
   */
  public applyHeaders(response: NextResponse, request?: NextRequest): NextResponse {
    const isProduction = process.env.NODE_ENV === 'production';
    const url = request ? new URL(request.url) : null;

    // Content Security Policy
    if (this.config.contentSecurityPolicy) {
      const csp = this.generateCSP();
      const headerName = this.config.contentSecurityPolicy.reportOnly 
        ? 'Content-Security-Policy-Report-Only'
        : 'Content-Security-Policy';
      response.headers.set(headerName, csp);
    }

    // HTTP Strict Transport Security (HSTS)
    if (isProduction && this.config.hsts) {
      const { maxAge, includeSubDomains, preload } = this.config.hsts;
      let hstsValue = `max-age=${maxAge}`;
      if (includeSubDomains) hstsValue += '; includeSubDomains';
      if (preload) hstsValue += '; preload';
      response.headers.set('Strict-Transport-Security', hstsValue);
    }

    // X-Frame-Options
    if (this.config.frameOptions) {
      response.headers.set('X-Frame-Options', this.config.frameOptions);
    }

    // X-Content-Type-Options
    if (this.config.contentTypeOptions) {
      response.headers.set('X-Content-Type-Options', 'nosniff');
    }

    // X-XSS-Protection
    if (this.config.xssProtection) {
      response.headers.set('X-XSS-Protection', '1; mode=block');
    }

    // Referrer Policy
    if (this.config.referrerPolicy) {
      response.headers.set('Referrer-Policy', this.config.referrerPolicy);
    }

    // Permissions Policy
    if (this.config.permissionsPolicy) {
      const permissionsPolicy = this.generatePermissionsPolicy();
      if (permissionsPolicy) {
        response.headers.set('Permissions-Policy', permissionsPolicy);
      }
    }

    // Cross-Origin Embedder Policy
    if (this.config.crossOriginEmbedderPolicy) {
      response.headers.set('Cross-Origin-Embedder-Policy', this.config.crossOriginEmbedderPolicy);
    }

    // Cross-Origin Opener Policy
    if (this.config.crossOriginOpenerPolicy) {
      response.headers.set('Cross-Origin-Opener-Policy', this.config.crossOriginOpenerPolicy);
    }

    // Cross-Origin Resource Policy
    if (this.config.crossOriginResourcePolicy) {
      response.headers.set('Cross-Origin-Resource-Policy', this.config.crossOriginResourcePolicy);
    }

    // Additional security headers
    response.headers.set('X-DNS-Prefetch-Control', 'off');
    response.headers.set('X-Download-Options', 'noopen');
    response.headers.set('X-Permitted-Cross-Domain-Policies', 'none');

    // Remove potentially sensitive headers
    response.headers.delete('Server');
    response.headers.delete('X-Powered-By');

    return response;
  }

  /**
   * Middleware function for Next.js
   */
  public middleware(request: NextRequest): NextResponse {
    const response = NextResponse.next();
    return this.applyHeaders(response, request);
  }

  /**
   * Apply headers to API responses
   */
  public secureApiResponse(data: any, options: ResponseInit = {}): NextResponse {
    const response = NextResponse.json(data, options);
    return this.applyHeaders(response);
  }

  /**
   * Get CSP nonce for inline scripts/styles
   */
  public generateNonce(): string {
    return Buffer.from(crypto.getRandomValues(new Uint8Array(16))).toString('base64');
  }

  /**
   * Update CSP to include nonce
   */
  public withNonce(nonce: string): SecurityHeaders {
    const newConfig = { ...this.config };
    
    if (newConfig.contentSecurityPolicy?.directives) {
      // Add nonce to script-src and style-src
      const directives = { ...newConfig.contentSecurityPolicy.directives };
      
      if (directives['script-src']) {
        directives['script-src'] = [...directives['script-src'], `'nonce-${nonce}'`];
      }
      
      if (directives['style-src']) {
        directives['style-src'] = [...directives['style-src'], `'nonce-${nonce}'`];
      }
      
      newConfig.contentSecurityPolicy.directives = directives;
    }

    return new SecurityHeaders(newConfig);
  }

  /**
   * Create development-friendly configuration
   */
  public static development(): SecurityHeaders {
    return new SecurityHeaders({
      contentSecurityPolicy: {
        directives: {
          'default-src': ["'self'"],
          'script-src': [
            "'self'",
            "'unsafe-inline'",
            "'unsafe-eval'",
            'localhost:*',
            '127.0.0.1:*',
          ],
          'style-src': [
            "'self'",
            "'unsafe-inline'",
          ],
          'img-src': [
            "'self'",
            'data:',
            'blob:',
            'localhost:*',
            '127.0.0.1:*',
          ],
          'connect-src': [
            "'self'",
            'localhost:*',
            '127.0.0.1:*',
            'ws://localhost:*',
            'wss://localhost:*',
          ],
        },
        reportOnly: true,
      },
      hsts: undefined, // Disable HSTS in development
    });
  }

  /**
   * Create production-ready configuration
   */
  public static production(domain: string): SecurityHeaders {
    return new SecurityHeaders({
      contentSecurityPolicy: {
        directives: {
          'default-src': ["'self'"],
          'script-src': [
            "'self'",
            'https://www.googletagmanager.com',
            'https://www.google-analytics.com',
          ],
          'style-src': [
            "'self'",
            'https://fonts.googleapis.com',
          ],
          'img-src': [
            "'self'",
            'data:',
            'https://*.amazonaws.com',
            `https://${domain}`,
          ],
          'connect-src': [
            "'self'",
            'https://www.google-analytics.com',
          ],
          'frame-ancestors': ["'none'"],
        },
        reportOnly: false,
      },
      hsts: {
        maxAge: 31536000,
        includeSubDomains: true,
        preload: true,
      },
    });
  }
}

// Export default instance
export const securityHeaders = process.env.NODE_ENV === 'production'
  ? SecurityHeaders.production(process.env.NEXT_PUBLIC_DOMAIN || 'localhost')
  : SecurityHeaders.development();

// Utility function for API routes
export function withSecurityHeaders<T = any>(
  handler: (request: NextRequest, ...args: any[]) => Promise<NextResponse<T>>
) {
  return async (request: NextRequest, ...args: any[]): Promise<NextResponse<T>> => {
    const response = await handler(request, ...args);
    return securityHeaders.applyHeaders(response, request) as NextResponse<T>;
  };
} 