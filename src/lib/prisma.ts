/**
 * Prisma Client Configuration
 * Following Next.js best practices for database connections
 */

import { PrismaClient } from '@prisma/client';

const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined
}

export const prisma = globalForPrisma.prisma ?? new PrismaClient()

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma

// Graceful shutdown handling - only add listeners once
if (typeof window === 'undefined' && !globalForPrisma.prisma) {
  // Prevent multiple listeners by checking if already initialized
  let shutdownHandled = false;
  
  const gracefulShutdown = async () => {
    if (shutdownHandled) return;
    shutdownHandled = true;
    
    try {
      await prisma.$disconnect();
    } catch (error) {
      console.error('Error during Prisma disconnection:', error);
    }
  };

  process.on('beforeExit', gracefulShutdown);
  process.on('SIGINT', async () => {
    await gracefulShutdown();
    process.exit(0);
  });
  process.on('SIGTERM', async () => {
    await gracefulShutdown();
    process.exit(0);
  });
  
  // Increase max listeners to prevent warnings
  process.setMaxListeners(15);
}

export default prisma;
