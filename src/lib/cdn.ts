// CDN Configuration and URL generation utilities

export interface CDNConfig {
  provider: 'cloudflare' | 'aws' | 'custom';
  baseUrl: string;
  zone?: string;
  enabled: boolean;
  imageOptimization?: boolean;
  cacheControl?: string;
}

// CDN configuration
const CDN_CONFIG: CDNConfig = {
  provider: (process.env.CDN_PROVIDER as 'cloudflare' | 'aws' | 'custom') || 'custom',
  baseUrl: process.env.CDN_BASE_URL || '',
  zone: process.env.CDN_ZONE,
  enabled: process.env.CDN_ENABLED === 'true',
  imageOptimization: process.env.CDN_IMAGE_OPTIMIZATION === 'true',
  cacheControl: process.env.CDN_CACHE_CONTROL || 'public, max-age=31536000',
};

export class CDNService {
  private config: CDNConfig;

  constructor(config: CDNConfig = CDN_CONFIG) {
    this.config = config;
  }

  /**
   * Generate CDN URL for an asset
   */
  getAssetUrl(path: string, options: {
    width?: number;
    height?: number;
    quality?: number;
    format?: 'webp' | 'jpeg' | 'png' | 'avif';
    fit?: 'cover' | 'contain' | 'fill';
  } = {}): string {
    if (!this.config.enabled || !this.config.baseUrl) {
      return path;
    }

    // Clean the path
    const cleanPath = path.startsWith('/') ? path.slice(1) : path;
    let url = `${this.config.baseUrl}/${cleanPath}`;

    // Add image optimization parameters if supported
    if (this.config.imageOptimization && this.isImagePath(path)) {
      url = this.addImageOptimizationParams(url, options);
    }

    return url;
  }

  /**
   * Generate multiple CDN URLs for responsive images
   */
  getResponsiveImageUrls(
    path: string,
    sizes: number[] = [320, 640, 768, 1024, 1280, 1920],
    options: {
      quality?: number;
      format?: 'webp' | 'jpeg' | 'png' | 'avif';
      fit?: 'cover' | 'contain' | 'fill';
    } = {}
  ): Array<{ url: string; width: number }> {
    return sizes.map(width => ({
      url: this.getAssetUrl(path, { ...options, width }),
      width,
    }));
  }

  /**
   * Generate srcset string for responsive images
   */
  generateSrcSet(
    path: string,
    sizes: number[] = [320, 640, 768, 1024, 1280, 1920],
    options: {
      quality?: number;
      format?: 'webp' | 'jpeg' | 'png' | 'avif';
      fit?: 'cover' | 'contain' | 'fill';
    } = {}
  ): string {
    const urls = this.getResponsiveImageUrls(path, sizes, options);
    return urls.map(({ url, width }) => `${url} ${width}w`).join(', ');
  }

  /**
   * Generate picture element sources for modern image formats
   */
  generatePictureSources(
    path: string,
    sizes: number[] = [320, 640, 768, 1024, 1280, 1920],
    options: {
      quality?: number;
      fit?: 'cover' | 'contain' | 'fill';
    } = {}
  ): Array<{ srcset: string; type: string }> {
    const sources = [];

    // AVIF source (best compression)
    if (this.supportsFormat('avif')) {
      sources.push({
        srcset: this.generateSrcSet(path, sizes, { ...options, format: 'avif' }),
        type: 'image/avif',
      });
    }

    // WebP source (good compression, wide support)
    if (this.supportsFormat('webp')) {
      sources.push({
        srcset: this.generateSrcSet(path, sizes, { ...options, format: 'webp' }),
        type: 'image/webp',
      });
    }

    // JPEG fallback
    sources.push({
      srcset: this.generateSrcSet(path, sizes, { ...options, format: 'jpeg' }),
      type: 'image/jpeg',
    });

    return sources;
  }

  /**
   * Purge cache for specific URLs
   */
  async purgeCache(urls: string[]): Promise<boolean> {
    if (!this.config.enabled) {
      return true;
    }

    try {
      switch (this.config.provider) {
        case 'cloudflare':
          return await this.purgeCloudflareCache(urls);
        case 'aws':
          return await this.purgeAWSCache(urls);
        default:
          console.warn('Cache purging not implemented for custom CDN provider');
          return true;
      }
    } catch (error) {
      console.error('CDN cache purge failed:', error);
      return false;
    }
  }

  /**
   * Check if CDN is enabled and configured
   */
  isEnabled(): boolean {
    return this.config.enabled && !!this.config.baseUrl;
  }

  /**
   * Get CDN configuration
   */
  getConfig(): CDNConfig {
    return { ...this.config };
  }

  // Private methods

  private isImagePath(path: string): boolean {
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.webp', '.avif', '.gif'];
    return imageExtensions.some(ext => path.toLowerCase().includes(ext));
  }

  private addImageOptimizationParams(
    url: string,
    options: {
      width?: number;
      height?: number;
      quality?: number;
      format?: string;
      fit?: string;
    }
  ): string {
    const params = new URLSearchParams();

    if (options.width) params.set('w', options.width.toString());
    if (options.height) params.set('h', options.height.toString());
    if (options.quality) params.set('q', options.quality.toString());
    if (options.format) params.set('f', options.format);
    if (options.fit) params.set('fit', options.fit);

    const paramString = params.toString();
    return paramString ? `${url}?${paramString}` : url;
  }

  private supportsFormat(format: string): boolean {
    // Check if CDN supports the format
    switch (this.config.provider) {
      case 'cloudflare':
        return ['webp', 'avif', 'jpeg', 'png'].includes(format);
      case 'aws':
        return ['webp', 'jpeg', 'png'].includes(format);
      default:
        return ['webp', 'jpeg', 'png'].includes(format);
    }
  }

  private async purgeCloudflareCache(urls: string[]): Promise<boolean> {
    if (!process.env.CLOUDFLARE_API_TOKEN || !this.config.zone) {
      console.error('Cloudflare API token or zone not configured');
      return false;
    }

    const response = await fetch(
      `https://api.cloudflare.com/client/v4/zones/${this.config.zone}/purge_cache`,
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${process.env.CLOUDFLARE_API_TOKEN}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ files: urls }),
      }
    );

    return response.ok;
  }

  private async purgeAWSCache(urls: string[]): Promise<boolean> {
    // AWS CloudFront cache invalidation would go here
    console.warn('AWS CloudFront cache purging not implemented');
    return true;
  }
}

// Singleton CDN service
export const cdnService = new CDNService();

// Utility functions
export function getCDNUrl(path: string, options?: Parameters<CDNService['getAssetUrl']>[1]): string {
  return cdnService.getAssetUrl(path, options);
}

export function getResponsiveImageUrls(
  path: string,
  sizes?: number[],
  options?: Parameters<CDNService['getResponsiveImageUrls']>[2]
): ReturnType<CDNService['getResponsiveImageUrls']> {
  return cdnService.getResponsiveImageUrls(path, sizes, options);
}

export function generateSrcSet(
  path: string,
  sizes?: number[],
  options?: Parameters<CDNService['generateSrcSet']>[2]
): string {
  return cdnService.generateSrcSet(path, sizes, options);
}

export default cdnService;
