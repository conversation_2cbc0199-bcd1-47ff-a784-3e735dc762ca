import { NextResponse } from 'next/server';

export enum ErrorCode {
  // Authentication & Authorization
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  SESSION_EXPIRED = 'SESSION_EXPIRED',
  
  // Validation & Input
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  INVALID_INPUT = 'INVALID_INPUT',
  MISSING_REQUIRED_FIELD = 'MISSING_REQUIRED_FIELD',
  SECURITY_VIOLATION = 'SECURITY_VIOLATION',
  
  // Database & Resources
  RESOURCE_NOT_FOUND = 'RESOURCE_NOT_FOUND',
  DATABASE_ERROR = 'DATABASE_ERROR',
  FOREIGN_KEY_CONSTRAINT = 'FOREIGN_KEY_CONSTRAINT',
  UNIQUE_CONSTRAINT = 'UNIQUE_CONSTRAINT',
  
  // System & Infrastructure
  INTERNAL_SERVER_ERROR = 'INTERNAL_SERVER_ERROR',
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE',
  TIMEOUT = 'TIMEOUT',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  
  // Business Logic
  BUSINESS_LOGIC_ERROR = 'BUSINESS_LOGIC_ERROR',
  PAYMENT_REQUIRED = 'PAYMENT_REQUIRED',
  QUOTA_EXCEEDED = 'QUOTA_EXCEEDED',
}

export enum ErrorSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL',
}

export interface ErrorDetails {
  code: ErrorCode;
  message: string;
  severity: ErrorSeverity;
  statusCode: number;
  userMessage?: string;
  field?: string;
  metadata?: Record<string, any>;
}

export class AppError extends Error {
  public readonly code: ErrorCode;
  public readonly severity: ErrorSeverity;
  public readonly statusCode: number;
  public readonly userMessage: string;
  public readonly field?: string;
  public readonly metadata?: Record<string, any>;
  public readonly timestamp: Date;
  public readonly requestId?: string;

  constructor(details: ErrorDetails, originalError?: Error) {
    super(details.message);
    
    this.name = 'AppError';
    this.code = details.code;
    this.severity = details.severity;
    this.statusCode = details.statusCode;
    this.userMessage = details.userMessage || this.getDefaultUserMessage();
    this.field = details.field;
    this.metadata = details.metadata;
    this.timestamp = new Date();
    
    // Maintain stack trace
    if (originalError && originalError.stack) {
      this.stack = originalError.stack;
    } else if (Error.captureStackTrace) {
      Error.captureStackTrace(this, AppError);
    }
  }

  private getDefaultUserMessage(): string {
    switch (this.severity) {
      case ErrorSeverity.CRITICAL:
        return 'A critical system error occurred. Please contact support.';
      case ErrorSeverity.HIGH:
        return 'An error occurred while processing your request. Please try again later.';
      case ErrorSeverity.MEDIUM:
        return 'Unable to complete your request. Please check your input and try again.';
      case ErrorSeverity.LOW:
        return 'Your request could not be processed. Please verify your information.';
      default:
        return 'An unexpected error occurred.';
    }
  }

  public toJSON() {
    return {
      name: this.name,
      code: this.code,
      message: this.message,
      userMessage: this.userMessage,
      severity: this.severity,
      statusCode: this.statusCode,
      field: this.field,
      metadata: this.metadata,
      timestamp: this.timestamp.toISOString(),
      requestId: this.requestId,
    };
  }
}

export class ErrorLogger {
  private static logError(error: AppError, context?: Record<string, any>) {
    const logEntry = {
      timestamp: error.timestamp.toISOString(),
      level: this.getSeverityLogLevel(error.severity),
      code: error.code,
      message: error.message,
      userMessage: error.userMessage,
      statusCode: error.statusCode,
      field: error.field,
      metadata: error.metadata,
      context,
      stack: error.stack,
    };

    switch (error.severity) {
      case ErrorSeverity.CRITICAL:
        console.error('[CRITICAL ERROR]', JSON.stringify(logEntry, null, 2));
        break;
      case ErrorSeverity.HIGH:
        console.error('[HIGH ERROR]', JSON.stringify(logEntry, null, 2));
        break;
      case ErrorSeverity.MEDIUM:
        console.warn('[MEDIUM ERROR]', JSON.stringify(logEntry, null, 2));
        break;
      case ErrorSeverity.LOW:
        console.info('[LOW ERROR]', JSON.stringify(logEntry, null, 2));
        break;
    }
  }

  private static getSeverityLogLevel(severity: ErrorSeverity): string {
    switch (severity) {
      case ErrorSeverity.CRITICAL: return 'error';
      case ErrorSeverity.HIGH: return 'error';
      case ErrorSeverity.MEDIUM: return 'warn';
      case ErrorSeverity.LOW: return 'info';
      default: return 'error';
    }
  }

  public static logAndRespond(
    error: Error | AppError,
    context?: Record<string, any>
  ): NextResponse {
    const appError = error instanceof AppError 
      ? error 
      : this.convertToAppError(error);

    this.logError(appError, context);

    return NextResponse.json(
      {
        success: false,
        error: appError.userMessage,
        code: appError.code,
        field: appError.field,
        timestamp: appError.timestamp.toISOString(),
      },
      { 
        status: appError.statusCode,
        headers: {
          'Content-Type': 'application/json',
          'X-Error-Code': appError.code,
        }
      }
    );
  }

  private static convertToAppError(error: Error): AppError {
    // Check for known error patterns
    if (error.message.includes('Security violation')) {
      return new AppError({
        code: ErrorCode.SECURITY_VIOLATION,
        message: error.message,
        severity: ErrorSeverity.HIGH,
        statusCode: 400,
        userMessage: 'Invalid request format',
      }, error);
    }

    if (error.message.includes('timeout')) {
      return new AppError({
        code: ErrorCode.TIMEOUT,
        message: error.message,
        severity: ErrorSeverity.MEDIUM,
        statusCode: 503,
        userMessage: 'Service temporarily unavailable',
      }, error);
    }

    if (error.message.includes('not found')) {
      return new AppError({
        code: ErrorCode.RESOURCE_NOT_FOUND,
        message: error.message,
        severity: ErrorSeverity.LOW,
        statusCode: 404,
        userMessage: 'Requested resource not found',
      }, error);
    }

    // Default to internal server error
    return new AppError({
      code: ErrorCode.INTERNAL_SERVER_ERROR,
      message: error.message,
      severity: ErrorSeverity.HIGH,
      statusCode: 500,
    }, error);
  }
}

// Common error factory functions
export const ErrorFactory = {
  validation: (message: string, field?: string): AppError => 
    new AppError({
      code: ErrorCode.VALIDATION_ERROR,
      message,
      severity: ErrorSeverity.LOW,
      statusCode: 400,
      field,
      userMessage: `Invalid ${field || 'input'}: ${message}`,
    }),

  unauthorized: (message: string = 'Authentication required'): AppError =>
    new AppError({
      code: ErrorCode.UNAUTHORIZED,
      message,
      severity: ErrorSeverity.MEDIUM,
      statusCode: 401,
      userMessage: 'Please log in to access this resource',
    }),

  forbidden: (message: string = 'Access denied'): AppError =>
    new AppError({
      code: ErrorCode.FORBIDDEN,
      message,
      severity: ErrorSeverity.MEDIUM,
      statusCode: 403,
      userMessage: 'You do not have permission to access this resource',
    }),

  notFound: (resource: string = 'Resource'): AppError =>
    new AppError({
      code: ErrorCode.RESOURCE_NOT_FOUND,
      message: `${resource} not found`,
      severity: ErrorSeverity.LOW,
      statusCode: 404,
      userMessage: `The requested ${resource.toLowerCase()} could not be found`,
    }),

  database: (message: string, originalError?: Error): AppError =>
    new AppError({
      code: ErrorCode.DATABASE_ERROR,
      message,
      severity: ErrorSeverity.HIGH,
      statusCode: 500,
      userMessage: 'A database error occurred. Please try again later.',
    }, originalError),

  rateLimit: (): AppError =>
    new AppError({
      code: ErrorCode.RATE_LIMIT_EXCEEDED,
      message: 'Rate limit exceeded',
      severity: ErrorSeverity.MEDIUM,
      statusCode: 429,
      userMessage: 'Too many requests. Please try again later.',
    }),
}; 