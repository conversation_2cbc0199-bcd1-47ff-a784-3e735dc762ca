import { <PERSON>rismaClient, Prisma } from '@prisma/client';
import { <PERSON>rrorFactory, AppError, <PERSON><PERSON>r<PERSON><PERSON>, ErrorSeverity } from '@/lib/error-handling/centralized-errors';

// Database configuration
const DATABASE_CONFIG = {
  connectionTimeout: 10000,
  queryTimeout: 30000,
  retryAttempts: 3,
  retryDelay: 1000,
  maxConnections: 10,
  enableLogging: process.env.NODE_ENV === 'development',
};

class DatabaseService {
  private static instance: DatabaseService;
  private prisma: PrismaClient;
  private connectionCount = 0;
  private queryMetrics: Map<string, { count: number; totalTime: number }> = new Map();

  private constructor() {
    this.prisma = new PrismaClient({
      log: DATABASE_CONFIG.enableLogging 
        ? ['query', 'info', 'warn', 'error']
        : ['error'],
      datasources: {
        db: {
          url: process.env.DATABASE_URL || 'postgresql://localhost:5432/default',
        },
      },
    });

    // Enhanced connection lifecycle management
    this.setupConnectionHandlers();
    this.setupQueryMetrics();
  }

  public static getInstance(): DatabaseService {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService();
    }
    return DatabaseService.instance;
  }

  private setupConnectionHandlers(): void {
    // Connection monitoring - only add listeners for singleton instance
    // Event handlers are now managed by the prisma client setup
    if (DATABASE_CONFIG.enableLogging) {
      console.info('[DATABASE] DatabaseService initialized with connection monitoring');
    }
  }

  private setupQueryMetrics(): void {
    if (DATABASE_CONFIG.enableLogging) {
      this.prisma.$on('query' as never, (e: any) => {
        const operation = e.query.split(' ')[0].toUpperCase();
        const existing = this.queryMetrics.get(operation) || { count: 0, totalTime: 0 };
        this.queryMetrics.set(operation, {
          count: existing.count + 1,
          totalTime: existing.totalTime + e.duration,
        });
      });
    }
  }

  /**
   * Execute a database operation with proper error handling and retries
   */
  public async executeOperation<T>(
    operation: () => Promise<T>,
    operationName: string,
    retries: number = DATABASE_CONFIG.retryAttempts
  ): Promise<T> {
    const startTime = Date.now();
    
    for (let attempt = 1; attempt <= retries; attempt++) {
      try {
        this.connectionCount++;
        const result = await Promise.race([
          operation(),
          this.createTimeoutPromise(DATABASE_CONFIG.queryTimeout),
        ]);
        
        const duration = Date.now() - startTime;
        if (DATABASE_CONFIG.enableLogging && duration > 1000) {
          console.warn(`[DATABASE] Slow query detected: ${operationName} took ${duration}ms`);
        }
        
        return result as T;
        
             } catch (error: unknown) {
         const duration = Date.now() - startTime;
         const err = error as Error;
         
         // Handle different types of database errors
         if (this.isPrismaError(error)) {
           const appError = this.handlePrismaError(error, operationName);
           
           // Retry for transient errors
           if (this.isRetryableError(error) && attempt < retries) {
             console.warn(`[DATABASE] Retrying ${operationName} (attempt ${attempt + 1}/${retries}) after error:`, err.message);
             await this.delay(DATABASE_CONFIG.retryDelay * attempt);
             continue;
           }
           
           console.error(`[DATABASE] Operation failed: ${operationName} (${duration}ms)`, appError.toJSON());
           throw appError;
         }
         
         // Handle timeout errors
         if (err.message === 'Operation timeout') {
           const timeoutError = new AppError({
             code: ErrorCode.TIMEOUT,
             message: `Database operation timeout: ${operationName}`,
             severity: ErrorSeverity.HIGH,
             statusCode: 503,
             userMessage: 'The request is taking too long. Please try again.',
             metadata: { operationName, duration, attempt },
           });
           
           console.error(`[DATABASE] Timeout: ${operationName} (${duration}ms)`);
           throw timeoutError;
         }
         
         // Handle unknown errors
         const unknownError = new AppError({
           code: ErrorCode.DATABASE_ERROR,
           message: `Unknown database error in ${operationName}: ${err.message || 'Unknown error'}`,
           severity: ErrorSeverity.HIGH,
           statusCode: 500,
           metadata: { operationName, duration, attempt },
         });
         
         console.error(`[DATABASE] Unknown error: ${operationName}`, unknownError.toJSON());
         throw unknownError;
      } finally {
        this.connectionCount--;
      }
    }
    
    throw new AppError({
      code: ErrorCode.DATABASE_ERROR,
      message: `Max retries exceeded for ${operationName}`,
      severity: ErrorSeverity.HIGH,
      statusCode: 500,
    });
  }

  /**
   * Execute operations within a transaction
   */
  public async executeTransaction<T>(
    operations: (tx: Prisma.TransactionClient) => Promise<T>,
    operationName: string
  ): Promise<T> {
    return this.executeOperation(
      () => this.prisma.$transaction(operations, {
        maxWait: DATABASE_CONFIG.connectionTimeout,
        timeout: DATABASE_CONFIG.queryTimeout,
      }),
      `Transaction: ${operationName}`
    );
  }

  /**
   * Find unique record with proper error handling
   */
  public async findUnique<T>(
    model: any,
    args: any,
    operationName: string
  ): Promise<T | null> {
    return this.executeOperation(
      () => model.findUnique(args),
      `FindUnique: ${operationName}`
    );
  }

  /**
   * Find many records with pagination and limits
   */
  public async findMany<T>(
    model: any,
    args: any,
    operationName: string,
    maxResults: number = 1000
  ): Promise<T[]> {
    // Enforce maximum results limit
    const safeArgs = {
      ...args,
      take: Math.min(args.take || maxResults, maxResults),
    };

    return this.executeOperation(
      () => model.findMany(safeArgs),
      `FindMany: ${operationName}`
    );
  }

  /**
   * Create record with validation
   */
  public async create<T>(
    model: any,
    args: any,
    operationName: string
  ): Promise<T> {
    return this.executeOperation(
      () => model.create(args),
      `Create: ${operationName}`
    );
  }

  /**
   * Update record with optimistic locking check
   */
  public async update<T>(
    model: any,
    args: any,
    operationName: string
  ): Promise<T> {
    return this.executeOperation(
      () => model.update(args),
      `Update: ${operationName}`
    );
  }

  /**
   * Delete record with cascade checks
   */
  public async delete<T>(
    model: any,
    args: any,
    operationName: string
  ): Promise<T> {
    return this.executeOperation(
      () => model.delete(args),
      `Delete: ${operationName}`
    );
  }

  /**
   * Get database health status
   */
  public async getHealthStatus(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    connectionCount: number;
    queryMetrics: Record<string, { count: number; totalTime: number; avgTime: number }>;
    timestamp: string;
  }> {
    try {
      const startTime = Date.now();
      await this.prisma.$queryRaw`SELECT 1`;
      const responseTime = Date.now() - startTime;
      
      const metrics: Record<string, { count: number; totalTime: number; avgTime: number }> = {};
      for (const [operation, data] of this.queryMetrics.entries()) {
        metrics[operation] = {
          ...data,
          avgTime: data.count > 0 ? data.totalTime / data.count : 0,
        };
      }
      
      return {
        status: responseTime < 1000 ? 'healthy' : responseTime < 5000 ? 'degraded' : 'unhealthy',
        connectionCount: this.connectionCount,
        queryMetrics: metrics,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        connectionCount: this.connectionCount,
        queryMetrics: {},
        timestamp: new Date().toISOString(),
      };
    }
  }

  /**
   * Graceful shutdown
   */
  public async disconnect(): Promise<void> {
    console.info('[DATABASE] Disconnecting from database...');
    await this.prisma.$disconnect();
  }

  // Private helper methods
  private createTimeoutPromise(timeout: number): Promise<never> {
    return new Promise((_, reject) => {
      setTimeout(() => reject(new Error('Operation timeout')), timeout);
    });
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private isPrismaError(error: any): boolean {
    return error instanceof Prisma.PrismaClientKnownRequestError ||
           error instanceof Prisma.PrismaClientUnknownRequestError ||
           error instanceof Prisma.PrismaClientValidationError ||
           error instanceof Prisma.PrismaClientInitializationError;
  }

  private isRetryableError(error: any): boolean {
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      // Retry for connection and timeout errors
      return ['P1001', 'P1002', 'P1008', 'P1010'].includes(error.code);
    }
    return false;
  }

  private handlePrismaError(error: any, operationName: string): AppError {
    if (error instanceof Prisma.PrismaClientKnownRequestError) {
      switch (error.code) {
        case 'P2002':
          return ErrorFactory.validation(
            `Duplicate entry: ${error.meta?.field_name || 'unique constraint violation'}`,
            error.meta?.field_name as string
          );
        
        case 'P2025':
          return ErrorFactory.notFound(operationName);
        
        case 'P2003':
          return new AppError({
            code: ErrorCode.FOREIGN_KEY_CONSTRAINT,
            message: `Foreign key constraint failed: ${error.message}`,
            severity: ErrorSeverity.MEDIUM,
            statusCode: 400,
            userMessage: 'Cannot complete operation due to data dependencies',
          });
        
        case 'P1001':
        case 'P1002':
          return new AppError({
            code: ErrorCode.SERVICE_UNAVAILABLE,
            message: `Database connection error: ${error.message}`,
            severity: ErrorSeverity.HIGH,
            statusCode: 503,
            userMessage: 'Database service temporarily unavailable',
          });
        
        default:
          return ErrorFactory.database(`Prisma error ${error.code}: ${error.message}`);
      }
    }
    
    if (error instanceof Prisma.PrismaClientValidationError) {
      return ErrorFactory.validation(error.message);
    }
    
    return ErrorFactory.database(`Database error in ${operationName}: ${error.message}`, error);
  }

  // Expose prisma client for advanced operations (use sparingly)
  public get client(): PrismaClient {
    return this.prisma;
  }
}

// Export singleton instance
export const db = DatabaseService.getInstance();

// Export types for convenience
export type { Prisma };
export { PrismaClient }; 