import { APIErrorCode, API_ERROR_CODES } from '@/types/api';

/**
 * Base API Error class for all application errors
 */
export class APIError extends Error {
  public readonly statusCode: number;
  public readonly code: APIErrorCode;
  public readonly field?: string;
  public readonly details?: any;
  public readonly requestId?: string;

  constructor(
    message: string,
    statusCode: number = 500,
    code: APIErrorCode = API_ERROR_CODES.INTERNAL_ERROR,
    field?: string,
    details?: any,
    requestId?: string
  ) {
    super(message);
    this.name = 'APIError';
    this.statusCode = statusCode;
    this.code = code;
    this.field = field;
    this.details = details;
    this.requestId = requestId;

    // Maintains proper stack trace for where our error was thrown (only available on V8)
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, APIError);
    }
  }

  /**
   * Convert error to JSON response format
   */
  toJSON() {
    return {
      success: false,
      error: {
        code: this.code,
        message: this.message,
        field: this.field,
        details: this.details
      },
      meta: {
        timestamp: new Date().toISOString(),
        requestId: this.requestId
      }
    };
  }
}

/**
 * Authentication related errors
 */
export class AuthenticationError extends APIError {
  constructor(message: string = 'Authentication required', requestId?: string) {
    super(message, 401, API_ERROR_CODES.UNAUTHORIZED, undefined, undefined, requestId);
    this.name = 'AuthenticationError';
  }
}

export class AuthorizationError extends APIError {
  constructor(message: string = 'Insufficient permissions', requestId?: string) {
    super(message, 403, API_ERROR_CODES.FORBIDDEN, undefined, undefined, requestId);
    this.name = 'AuthorizationError';
  }
}

export class SessionExpiredError extends APIError {
  constructor(message: string = 'Session has expired', requestId?: string) {
    super(message, 401, API_ERROR_CODES.SESSION_EXPIRED, undefined, undefined, requestId);
    this.name = 'SessionExpiredError';
  }
}

/**
 * Validation related errors
 */
export class ValidationError extends APIError {
  constructor(message: string, field?: string, details?: any, requestId?: string) {
    super(message, 400, API_ERROR_CODES.VALIDATION_ERROR, field, details, requestId);
    this.name = 'ValidationError';
  }
}

export class InvalidInputError extends APIError {
  constructor(message: string, field?: string, requestId?: string) {
    super(message, 400, API_ERROR_CODES.INVALID_INPUT, field, undefined, requestId);
    this.name = 'InvalidInputError';
  }
}

export class MissingRequiredFieldError extends APIError {
  constructor(field: string, requestId?: string) {
    super(`Required field '${field}' is missing`, 400, API_ERROR_CODES.MISSING_REQUIRED_FIELD, field, undefined, requestId);
    this.name = 'MissingRequiredFieldError';
  }
}

/**
 * Resource related errors
 */
export class NotFoundError extends APIError {
  constructor(resource: string, id?: string, requestId?: string) {
    const message = id ? `${resource} with ID '${id}' not found` : `${resource} not found`;
    super(message, 404, API_ERROR_CODES.NOT_FOUND, undefined, { resource, id }, requestId);
    this.name = 'NotFoundError';
  }
}

export class AlreadyExistsError extends APIError {
  constructor(resource: string, field?: string, value?: string, requestId?: string) {
    const message = field && value 
      ? `${resource} with ${field} '${value}' already exists`
      : `${resource} already exists`;
    super(message, 409, API_ERROR_CODES.ALREADY_EXISTS, field, { resource, field, value }, requestId);
    this.name = 'AlreadyExistsError';
  }
}

export class ConflictError extends APIError {
  constructor(message: string, details?: any, requestId?: string) {
    super(message, 409, API_ERROR_CODES.CONFLICT, undefined, details, requestId);
    this.name = 'ConflictError';
  }
}

/**
 * Database related errors
 */
export class DatabaseError extends APIError {
  constructor(message: string = 'Database operation failed', details?: any, requestId?: string) {
    super(message, 500, API_ERROR_CODES.DATABASE_ERROR, undefined, details, requestId);
    this.name = 'DatabaseError';
  }
}

/**
 * Rate limiting errors
 */
export class RateLimitError extends APIError {
  constructor(
    message: string = 'Too many requests', 
    retryAfter?: number,
    requestId?: string
  ) {
    super(
      message, 
      429, 
      API_ERROR_CODES.RATE_LIMIT_EXCEEDED, 
      undefined, 
      { retryAfter }, 
      requestId
    );
    this.name = 'RateLimitError';
  }
}

/**
 * Business logic errors
 */
export class BusinessRuleError extends APIError {
  constructor(message: string, rule?: string, requestId?: string) {
    super(message, 400, API_ERROR_CODES.BUSINESS_RULE_VIOLATION, undefined, { rule }, requestId);
    this.name = 'BusinessRuleError';
  }
}

export class OperationNotAllowedError extends APIError {
  constructor(operation: string, reason?: string, requestId?: string) {
    const message = reason 
      ? `Operation '${operation}' not allowed: ${reason}`
      : `Operation '${operation}' not allowed`;
    super(message, 403, API_ERROR_CODES.OPERATION_NOT_ALLOWED, undefined, { operation, reason }, requestId);
    this.name = 'OperationNotAllowedError';
  }
}

/**
 * Service related errors
 */
export class ServiceUnavailableError extends APIError {
  constructor(service?: string, requestId?: string) {
    const message = service 
      ? `Service '${service}' is currently unavailable`
      : 'Service is currently unavailable';
    super(message, 503, API_ERROR_CODES.SERVICE_UNAVAILABLE, undefined, { service }, requestId);
    this.name = 'ServiceUnavailableError';
  }
}

/**
 * Error factory for creating errors from different sources
 */
export class ErrorFactory {
  static fromPrismaError(error: any, requestId?: string): APIError {
    // Handle specific Prisma error codes
    switch (error.code) {
      case 'P2002':
        // Unique constraint violation
        const target = error.meta?.target?.[0] || 'field';
        return new AlreadyExistsError('Resource', target, 'this value', requestId);
      
      case 'P2025':
        // Record not found
        return new NotFoundError('Record', undefined, requestId);
      
      case 'P2003':
        // Foreign key constraint violation
        return new ConflictError('Cannot delete record due to related data', error.meta, requestId);
      
      case 'P2016':
        // Query interpretation error
        return new InvalidInputError('Invalid query parameters', undefined, requestId);
      
      default:
        return new DatabaseError(error.message || 'Database operation failed', error, requestId);
    }
  }

  static fromZodError(error: any, requestId?: string): ValidationError {
    const firstError = error.errors?.[0];
    if (firstError) {
      return new ValidationError(
        firstError.message,
        firstError.path?.join('.'),
        error.errors,
        requestId
      );
    }
    return new ValidationError('Validation failed', undefined, error.errors, requestId);
  }

  static fromUnknownError(error: unknown, requestId?: string): APIError {
    if (error instanceof APIError) {
      return error;
    }
    
    if (error instanceof Error) {
      return new APIError(
        error.message,
        500,
        API_ERROR_CODES.INTERNAL_ERROR,
        undefined,
        { originalError: error.name },
        requestId
      );
    }
    
    return new APIError(
      'An unexpected error occurred',
      500,
      API_ERROR_CODES.INTERNAL_ERROR,
      undefined,
      { originalError: String(error) },
      requestId
    );
  }
}

/**
 * Type guard to check if error is an APIError
 */
export function isAPIError(error: unknown): error is APIError {
  return error instanceof APIError;
}

/**
 * Type guard to check if error is a specific type of APIError
 */
export function isValidationError(error: unknown): error is ValidationError {
  return error instanceof ValidationError;
}

export function isNotFoundError(error: unknown): error is NotFoundError {
  return error instanceof NotFoundError;
}

export function isAuthenticationError(error: unknown): error is AuthenticationError {
  return error instanceof AuthenticationError;
}

export function isRateLimitError(error: unknown): error is RateLimitError {
  return error instanceof RateLimitError;
} 