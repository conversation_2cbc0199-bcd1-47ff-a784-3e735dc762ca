/**
 * Security Configuration
 * Centralized security settings for the application
 */

export const SECURITY_CONFIG = {
  // Content Security Policy
  CSP: {
    'default-src': ["'self'"],
    'script-src': [
      "'self'",
      "'unsafe-inline'",
      "'unsafe-eval'",
      "https://fonts.googleapis.com",
      "https://cdnjs.cloudflare.com",
      "https://unpkg.com",
      "https://www.googletagmanager.com",
      "https://connect.facebook.net"
    ],
    'style-src': [
      "'self'",
      "'unsafe-inline'",
      "https://fonts.googleapis.com",
      "https://cdnjs.cloudflare.com",
      "https://unpkg.com"
    ],
    'img-src': [
      "'self'",
      "data:",
      "https:",
      "http:",
      "*.amazonaws.com",
      "*.linodeobjects.com",
      "mocky.co.ke",
      "api.dicebear.com"
    ],
    'font-src': [
      "'self'",
      "https://fonts.gstatic.com",
      "https://cdnjs.cloudflare.com"
    ],
    'connect-src': [
      "'self'",
      "https://api.mocky.co.ke",
      "wss://api.mocky.co.ke"
    ],
    'frame-ancestors': ["'none'"],
    'base-uri': ["'self'"],
    'form-action': ["'self'"]
  },

  // Security Headers
  HEADERS: {
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'Permissions-Policy': 'accelerometer=(), camera=(), geolocation=(), gyroscope=(), magnetometer=(), microphone=(), payment=(), usb=()',
    'Cross-Origin-Embedder-Policy': 'unsafe-none',
    'Cross-Origin-Opener-Policy': 'same-origin',
    'Cross-Origin-Resource-Policy': 'cross-origin'
  },

  // Rate Limiting (Enhanced)
  RATE_LIMITS: {
    LOGIN: {
      max: 5,
      window: 15 * 60 * 1000, // 15 minutes
      skipSuccessfulRequests: true
    },
    API_PUBLIC: {
      max: 200,
      window: 60 * 1000, // 1 minute
      skipSuccessfulRequests: false
    },
    API_ADMIN: {
      max: 50,
      window: 60 * 1000, // 1 minute
      skipSuccessfulRequests: false
    },
    API_GENERAL: {
      max: 100,
      window: 60 * 1000, // 1 minute
      skipSuccessfulRequests: false
    }
  },

  // Input Validation
  VALIDATION: {
    MAX_REQUEST_SIZE: 10 * 1024 * 1024, // 10MB
    MAX_FILE_SIZE: 5 * 1024 * 1024, // 5MB
    ALLOWED_FILE_TYPES: [
      'image/jpeg',
      'image/png',
      'image/webp',
      'image/gif',
      'application/pdf',
      'text/csv',
      'application/json'
    ],
    DANGEROUS_PATTERNS: [
      /union\s+select/i,
      /insert\s+into/i,
      /delete\s+from/i,
      /drop\s+table/i,
      /exec\s*\(/i,
      /<script/i,
      /javascript:/i,
      /on\w+\s*=/i,
      /\.\.\//g,
      /\.\.\\/g
    ]
  },

  // Password Policy
  PASSWORD_POLICY: {
    MIN_LENGTH: 12,
    REQUIRE_UPPERCASE: true,
    REQUIRE_LOWERCASE: true,
    REQUIRE_NUMBERS: true,
    REQUIRE_SYMBOLS: true,
    MAX_AGE_DAYS: 90,
    HISTORY_COUNT: 5
  },

  // Session Security
  SESSION: {
    MAX_AGE: 24 * 60 * 60, // 24 hours
    UPDATE_AGE: 60 * 60, // 1 hour
    SAME_SITE: 'strict' as const,
    SECURE: process.env.NODE_ENV === 'production',
    HTTP_ONLY: true
  },

  // Audit Logging
  AUDIT: {
    ENABLED: process.env.SECURITY_AUDIT_LOG === 'true',
    RETENTION_DAYS: 90,
    LOG_LEVELS: ['error', 'warn', 'security', 'admin_action']
  }
};

/**
 * Generate Content Security Policy string
 */
export function generateCSPString(): string {
  const csp = SECURITY_CONFIG.CSP;
  return Object.entries(csp)
    .map(([directive, values]) => `${directive} ${values.join(' ')}`)
    .join('; ');
}

/**
 * Validate password against security policy
 */
export function validatePassword(password: string): { valid: boolean; errors: string[] } {
  const errors: string[] = [];
  const policy = SECURITY_CONFIG.PASSWORD_POLICY;

  if (password.length < policy.MIN_LENGTH) {
    errors.push(`Password must be at least ${policy.MIN_LENGTH} characters long`);
  }

  if (policy.REQUIRE_UPPERCASE && !/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter');
  }

  if (policy.REQUIRE_LOWERCASE && !/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter');
  }

  if (policy.REQUIRE_NUMBERS && !/\d/.test(password)) {
    errors.push('Password must contain at least one number');
  }

  if (policy.REQUIRE_SYMBOLS && !/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push('Password must contain at least one special character');
  }

  return {
    valid: errors.length === 0,
    errors
  };
}

/**
 * Check if input contains dangerous patterns
 */
export function containsDangerousPatterns(input: string): boolean {
  return SECURITY_CONFIG.VALIDATION.DANGEROUS_PATTERNS.some(pattern => 
    pattern.test(input)
  );
}

/**
 * Sanitize input string
 */
export function sanitizeInput(input: string): string {
  // Remove potentially dangerous characters
  return input
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/javascript:/gi, '')
    .replace(/on\w+\s*=/gi, '')
    .trim();
} 