'use client';

import { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { 
  DashboardStats, 
  FamilyTransaction, 
  FamilyLoan,
  TransactionStats 
} from '@/types/family-savings';
import { 
  BanknotesIcon, 
  UsersIcon, 
  DocumentCheckIcon,
  ArrowTrendingUpIcon,
  CreditCardIcon,
  ClockIcon
} from '@heroicons/react/24/outline';
// Utility function for currency formatting
const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-KE', {
    style: 'currency',
    currency: 'KES'
  }).format(amount);
};
import toast from 'react-hot-toast';

interface DashboardData {
  stats: DashboardStats;
  recentTransactions: FamilyTransaction[];
  pendingLoans: FamilyLoan[];
  transactionStats: TransactionStats;
}

export default function FamilySavingsPage() {
  const [data, setData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      
      // Fetch dashboard stats
      const [dashboardRes, transactionsRes, loansRes] = await Promise.all([
        fetch('/api/family-savings/dashboard'),
        fetch('/api/family-savings/transactions?limit=5'),
        fetch('/api/family-savings/loans?status=PENDING&limit=5')
      ]);

      if (!dashboardRes.ok || !transactionsRes.ok || !loansRes.ok) {
        throw new Error('Failed to fetch dashboard data');
      }

      const [dashboardData, transactionsData, loansData] = await Promise.all([
        dashboardRes.json(),
        transactionsRes.json(),
        loansRes.json()
      ]);

      setData({
        stats: dashboardData.data,
        recentTransactions: transactionsData.data.transactions,
        pendingLoans: loansData.data.loans,
        transactionStats: transactionsData.data.stats
      });
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      toast.error('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Family Savings System</h1>
          <p className="text-gray-600">Unable to load dashboard data. Please try again.</p>
        </div>
      </div>
    );
  }

  const { stats, recentTransactions, pendingLoans, transactionStats } = data;

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Family Savings Dashboard</h1>
        <p className="text-gray-600">
          Manage family contributions, loans, and business transactions
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <StatCard
          title="Total Contributions"
          value={formatCurrency(stats.totalContributions)}
          icon={<BanknotesIcon className="h-6 w-6" />}
          color="bg-green-500"
          change={`+${stats.monthlyGrowth.toFixed(1)}%`}
        />
        <StatCard
          title="Active Loans"
          value={formatCurrency(stats.totalLoans)}
          icon={<CreditCardIcon className="h-6 w-6" />}
          color="bg-blue-500"
        />
        <StatCard
          title="Business Revenue"
          value={formatCurrency(stats.totalBusinessRevenue)}
          icon={<ArrowTrendingUpIcon className="h-6 w-6" />}
          color="bg-purple-500"
        />
        <StatCard
          title="Active Users"
          value={stats.activeUsers.toString()}
          icon={<UsersIcon className="h-6 w-6" />}
          color="bg-orange-500"
        />
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Quick Actions</h3>
          <div className="grid grid-cols-2 gap-4">
            <QuickActionButton
              title="New Transaction"
              description="Record a deposit/withdrawal"
              href="/family-savings/transactions/new"
              color="bg-green-50 hover:bg-green-100 text-green-700"
            />
            <QuickActionButton
              title="Apply for Loan"
              description="Submit loan application"
              href="/family-savings/loans/new"
              color="bg-blue-50 hover:bg-blue-100 text-blue-700"
            />
            <QuickActionButton
              title="Business Entry"
              description="Add business transaction"
              href="/family-savings/business/new"
              color="bg-purple-50 hover:bg-purple-100 text-purple-700"
            />
            <QuickActionButton
              title="Set Savings Goal"
              description="Create savings target"
              href="/family-savings/goals/new"
              color="bg-orange-50 hover:bg-orange-100 text-orange-700"
            />
          </div>
        </Card>

        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4">Transaction Overview</h3>
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Total Deposits</span>
              <span className="font-medium text-green-600">
                {formatCurrency(transactionStats.totalDeposits)}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Total Withdrawals</span>
              <span className="font-medium text-red-600">
                {formatCurrency(transactionStats.totalWithdrawals)}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Pending Approvals</span>
              <span className="font-medium text-yellow-600">
                {transactionStats.pendingApprovals}
              </span>
            </div>
            <div className="flex justify-between items-center border-t pt-2">
              <span className="text-sm font-medium">Monthly Volume</span>
              <span className="font-bold text-blue-600">
                {formatCurrency(transactionStats.monthlyVolume)}
              </span>
            </div>
          </div>
        </Card>
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Recent Transactions</h3>
            <a 
              href="/family-savings/transactions" 
              className="text-sm text-blue-600 hover:text-blue-800"
            >
              View All
            </a>
          </div>
          <div className="space-y-3">
            {recentTransactions.map((transaction) => (
              <div key={transaction.id} className="flex items-center justify-between py-2 border-b last:border-b-0">
                <div className="flex items-center space-x-3">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                    transaction.transactionType === 'DEPOSIT' ? 'bg-green-100' : 'bg-red-100'
                  }`}>
                    <BanknotesIcon className={`h-4 w-4 ${
                      transaction.transactionType === 'DEPOSIT' ? 'text-green-600' : 'text-red-600'
                    }`} />
                  </div>
                  <div>
                    <p className="text-sm font-medium">{transaction.transactionCode}</p>
                    <p className="text-xs text-gray-500">
                      {new Date(transaction.timestamp).toLocaleDateString()}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className={`text-sm font-medium ${
                    transaction.transactionType === 'DEPOSIT' ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {transaction.transactionType === 'DEPOSIT' ? '+' : '-'}
                    {formatCurrency(transaction.amount)}
                  </p>
                  <p className={`text-xs ${
                    transaction.status === 'COMPLETED' ? 'text-green-500' : 
                    transaction.status === 'PENDING' ? 'text-yellow-500' : 'text-red-500'
                  }`}>
                    {transaction.status}
                  </p>
                </div>
              </div>
            ))}
            {recentTransactions.length === 0 && (
              <p className="text-gray-500 text-center py-4">No recent transactions</p>
            )}
          </div>
        </Card>

        <Card className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">Pending Loan Applications</h3>
            <a 
              href="/family-savings/loans" 
              className="text-sm text-blue-600 hover:text-blue-800"
            >
              View All
            </a>
          </div>
          <div className="space-y-3">
            {pendingLoans.map((loan) => (
              <div key={loan.id} className="flex items-center justify-between py-2 border-b last:border-b-0">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 rounded-full bg-yellow-100 flex items-center justify-center">
                    <ClockIcon className="h-4 w-4 text-yellow-600" />
                  </div>
                  <div>
                    <p className="text-sm font-medium">{loan.borrower?.firstName} {loan.borrower?.lastName}</p>
                    <p className="text-xs text-gray-500">{loan.purpose}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium">{formatCurrency(loan.amount)}</p>
                  <p className="text-xs text-yellow-500">PENDING</p>
                </div>
              </div>
            ))}
            {pendingLoans.length === 0 && (
              <p className="text-gray-500 text-center py-4">No pending loan applications</p>
            )}
          </div>
        </Card>
      </div>

      {/* Admin Actions */}
      {stats.pendingTransactions > 0 && (
        <Card className="mt-6 p-6 bg-yellow-50 border-yellow-200">
          <div className="flex items-center space-x-3">
            <DocumentCheckIcon className="h-6 w-6 text-yellow-600" />
            <div>
              <h3 className="text-lg font-semibold text-yellow-800">Admin Action Required</h3>
              <p className="text-sm text-yellow-700">
                {stats.pendingTransactions} transaction{stats.pendingTransactions > 1 ? 's' : ''} pending approval
              </p>
            </div>
            <div className="ml-auto">
              <a 
                href="/family-savings/admin"
                className="bg-yellow-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-yellow-700"
              >
                Review Pending
              </a>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
}

interface StatCardProps {
  title: string;
  value: string;
  icon: React.ReactNode;
  color: string;
  change?: string;
}

function StatCard({ title, value, icon, color, change }: StatCardProps) {
  return (
    <Card className="p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900">{value}</p>
          {change && (
            <p className="text-sm text-green-600 mt-1">{change} from last month</p>
          )}
        </div>
        <div className={`${color} p-3 rounded-full text-white`}>
          {icon}
        </div>
      </div>
    </Card>
  );
}

interface QuickActionButtonProps {
  title: string;
  description: string;
  href: string;
  color: string;
}

function QuickActionButton({ title, description, href, color }: QuickActionButtonProps) {
  return (
    <a 
      href={href}
      className={`${color} p-4 rounded-lg transition-colors duration-200 block`}
    >
      <h4 className="font-medium mb-1">{title}</h4>
      <p className="text-sm opacity-75">{description}</p>
    </a>
  );
} 