'use client';

import { useState, useRef } from 'react';
import { ImageUploadService } from '@/utils/imageUploadUtils';
import { UploadDiagnostics } from '@/utils/uploadDiagnostics';

export default function UploadTestPage() {
  const [uploading, setUploading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [diagnostics, setDiagnostics] = useState<string | null>(null);
  const [statusMessages, setStatusMessages] = useState<string[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const addStatusMessage = (message: string, type: 'info' | 'warning' | 'error' = 'info') => {
    const timestamp = new Date().toLocaleTimeString();
    const prefix = type === 'error' ? '❌' : type === 'warning' ? '⚠️' : 'ℹ️';
    setStatusMessages(prev => [...prev, `${timestamp} ${prefix} ${message}`]);
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setUploading(true);
    setProgress(0);
    setResult(null);
    setError(null);
    setStatusMessages([]);

    addStatusMessage(`Starting upload of ${file.name} (${Math.round(file.size/1024)}KB)`);

    try {
      const uploadResult = await ImageUploadService.uploadImage(file, {
        category: 'test',
        maxRetries: 3,
        timeoutMs: 300000, // 5 minutes
        onProgress: (progressValue) => {
          setProgress(progressValue);
          addStatusMessage(`Progress: ${progressValue.toFixed(1)}%`);
        },
        onStatusUpdate: (message, type) => {
          addStatusMessage(message, type);
        }
      });

      setResult(uploadResult);
      addStatusMessage('Upload completed successfully!', 'info');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      setError(errorMessage);
      addStatusMessage(`Upload failed: ${errorMessage}`, 'error');
    } finally {
      setUploading(false);
    }
  };

  const runDiagnostics = async () => {
    addStatusMessage('Running diagnostics...');
    try {
      const report = await UploadDiagnostics.generateReport();
      setDiagnostics(report);
      addStatusMessage('Diagnostics completed');
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : String(err);
      addStatusMessage(`Diagnostics failed: ${errorMessage}`, 'error');
    }
  };

  const clearLogs = () => {
    setStatusMessages([]);
    setResult(null);
    setError(null);
    setDiagnostics(null);
    setProgress(0);
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-3xl font-bold mb-6">Upload Test & Diagnostics</h1>
        
        {/* File Upload Section */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">File Upload Test</h2>
          
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleFileUpload}
            disabled={uploading}
            className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
          />
          
          {uploading && (
            <div className="mt-4">
              <div className="flex justify-between text-sm text-gray-600 mb-1">
                <span>Uploading...</span>
                <span>{progress.toFixed(1)}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${progress}%` }}
                ></div>
              </div>
            </div>
          )}
          
          {result && (
            <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded">
              <h3 className="font-semibold text-green-800">Upload Successful!</h3>
              <p className="text-sm text-green-700 mt-1">URL: {result.url}</p>
              {result.warning && (
                <p className="text-sm text-yellow-700 mt-1">Warning: {result.warning}</p>
              )}
            </div>
          )}
          
          {error && (
            <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded">
              <h3 className="font-semibold text-red-800">Upload Failed</h3>
              <p className="text-sm text-red-700 mt-1">{error}</p>
            </div>
          )}
        </div>

        {/* Diagnostics Section */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">System Diagnostics</h2>
          
          <button
            onClick={runDiagnostics}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 mr-4"
          >
            Run Diagnostics
          </button>
          
          <button
            onClick={clearLogs}
            className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700"
          >
            Clear Logs
          </button>
          
          {diagnostics && (
            <div className="mt-4">
              <h3 className="font-semibold mb-2">Diagnostic Report:</h3>
              <pre className="bg-gray-100 p-4 rounded text-xs overflow-x-auto">
                {diagnostics}
              </pre>
            </div>
          )}
        </div>

        {/* Status Messages */}
        {statusMessages.length > 0 && (
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-xl font-semibold mb-4">Status Log</h2>
            <div className="bg-gray-900 text-green-400 p-4 rounded font-mono text-sm max-h-64 overflow-y-auto">
              {statusMessages.map((message, index) => (
                <div key={index} className="mb-1">{message}</div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
} 