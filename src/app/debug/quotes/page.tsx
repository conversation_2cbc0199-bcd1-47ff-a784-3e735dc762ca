'use client';
import React from 'react';

import { useState } from 'react';

export default function DebugQuotesPage() {
  const [quoteId, setQuoteId] = useState('20212cf0-ebcb-4de7-95b8-5f1d2ef35968');
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const testQuoteFetch = async (apiPath: string) => {
    setLoading(true);
    try {
      console.log(`Testing ${apiPath} with quote ID: ${quoteId}`);
      
      const response = await fetch(`${apiPath}/${quoteId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      });
      
      console.log('Response status:', response.status);
      console.log('Response headers:', Object.fromEntries(response.headers.entries()));
      
      const responseText = await response.text();
      console.log('Response text:', responseText);
      
      let data;
      try {
        data = JSON.parse(responseText);
      } catch (parseError) {
        data = { error: 'Could not parse response as JSON', responseText };
      }
      
      setResult({
        apiPath,
        status: response.status,
        ok: response.ok,
        data,
        headers: Object.fromEntries(response.headers.entries())
      });
      
    } catch (error) {
      console.error('Fetch error:', error);
      setResult({
        apiPath,
        error: error instanceof Error ? error.message : String(error)
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Quote Fetching Debug Tool</h1>
      
      <div className="mb-6">
        <label className="block text-sm font-medium mb-2">Quote ID:</label>
        <input
          type="text"
          value={quoteId}
          onChange={(e) => setQuoteId(e.target.value)}
          className="w-full p-2 border border-gray-300 rounded"
          placeholder="Enter quote ID"
        />
      </div>
      
      <div className="space-y-4 mb-6">
        <button
          onClick={() => testQuoteFetch('/api/debug/quotes')}
          disabled={loading}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
        >
          Test Debug API
        </button>
        
        <button
          onClick={() => testQuoteFetch('/api/admin/quotes')}
          disabled={loading}
          className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
        >
          Test Admin API
        </button>
        
        <button
          onClick={() => testQuoteFetch('/api/quotes')}
          disabled={loading}
          className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 disabled:opacity-50"
        >
          Test Public API
        </button>
      </div>
      
      {loading && (
        <div className="text-center py-4">
          <div className="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
          <p className="mt-2">Testing...</p>
        </div>
      )}
      
      {result && (
        <div className="bg-gray-100 p-4 rounded">
          <h2 className="text-lg font-semibold mb-2">Result:</h2>
          <pre className="text-sm overflow-auto">
            {JSON.stringify(result, null, 2)}
          </pre>
        </div>
      )}
      
      <div className="mt-8 bg-yellow-50 p-4 rounded">
        <h3 className="font-semibold mb-2">Available Quote IDs:</h3>
        <ul className="text-sm space-y-1">
          <li>20212cf0-ebcb-4de7-95b8-5f1d2ef35968 (QUO-250524-004)</li>
          <li>22dfd26f-f9fd-46a0-95aa-064fa2345dc9 (QUO-250524-003)</li>
          <li>e3464fd7-2a35-4af4-bef3-60a5737c91bc (QUO-250524-002)</li>
          <li>587b2ae2-ed83-4ef1-8469-d3ac9e0a9c37 (QUO-250524-001)</li>
          <li>c3274c74-64d4-4b62-ba82-85bd1b08fcc0 (QUO-250515-001)</li>
        </ul>
      </div>
    </div>
  );
}
