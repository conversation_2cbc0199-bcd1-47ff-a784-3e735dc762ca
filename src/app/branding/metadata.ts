import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Brand Identity Nairobi | Professional Branding Services Kenya',
  description: 'Complete brand identity solutions in Nairobi. Logo design, brand strategy, and comprehensive branding services to build a powerful brand that drives business growth.',
  keywords: 'brand identity nairobi, branding services kenya, brand strategy kenya, professional branding nairobi, corporate branding kenya, business branding nairobi, logo design nairobi',
  openGraph: {
    title: 'Brand Identity Nairobi | Professional Branding Services Kenya',
    description: 'Complete brand identity solutions in Nairobi. Logo design, brand strategy, and comprehensive branding services to build a powerful brand that drives business growth.',
    type: 'website',
    url: 'https://mocky.co.ke/branding',
    siteName: 'Mocky Digital',
    locale: 'en_KE',
    images: [
      {
        url: '/images/branding-og.jpg',
        width: 1200,
        height: 630,
        alt: 'Brand Identity Services in Nairobi - Mocky Digital',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Brand Identity Nairobi | Professional Branding Services Kenya',
    description: 'Complete brand identity solutions in Nairobi. Logo design, brand strategy, and comprehensive branding services.',
    images: ['/images/branding-og.jpg'],
    creator: '@mockydigital',
    site: '@mockydigital',
  },
  alternates: {
    canonical: '/branding',
  },
  robots: {
    index: true,
    follow: true,
  },
};