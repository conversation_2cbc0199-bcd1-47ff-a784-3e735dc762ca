'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import ModernFAQSection from '@/components/ModernFAQSection';
import { SparklesIcon } from '@heroicons/react/24/outline';

// Branding services data
const brandingServices = [
  {
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z" />
      </svg>
    ),
    title: 'Brand Identity Design',
    description: 'Complete visual identity systems including logos, color palettes, typography, and brand guidelines.',
    features: [
      'Logo Design & Variations',
      'Color Palette Development',
      'Typography Selection',
      'Brand Guidelines Document'
    ]
  },
  {
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
      </svg>
    ),
    title: 'Brand Strategy',
    description: 'Strategic planning to position your brand effectively in the market and connect with your target audience.',
    features: [
      'Market Research & Analysis',
      'Brand Positioning',
      'Target Audience Definition',
      'Competitive Analysis'
    ]
  },
  {
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
      </svg>
    ),
    title: 'Brand Collaterals',
    description: 'Professional business materials that maintain brand consistency across all touchpoints.',
    features: [
      'Business Cards & Letterheads',
      'Brochures & Flyers',
      'Company Profiles',
      'Marketing Materials'
    ]
  },
  {
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
      </svg>
    ),
    title: 'Corporate Branding',
    description: 'Complete corporate identity solutions for established businesses and organizations.',
    features: [
      'Office Branding & Signage',
      'Employee ID Cards',
      'Corporate Presentations',
      'Annual Reports'
    ]
  },
  {
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
      </svg>
    ),
    title: 'Product Branding',
    description: 'Packaging and product branding that makes your products stand out on shelves.',
    features: [
      'Packaging Design',
      'Product Labels',
      'Brand Merchandise',
      'Point of Sale Materials'
    ]
  },
  {
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
      </svg>
    ),
    title: 'Digital Branding',
    description: 'Consistent brand experience across all digital platforms and touchpoints.',
    features: [
      'Website Brand Integration',
      'Social Media Templates',
      'Email Signatures',
      'Digital Brand Assets'
    ]
  }
];



// Brand packages
const brandingPackages = [
  {
    name: 'Startup Branding',
    price: '25,000',
    description: 'Perfect for new businesses and startups',
    features: [
      'Logo Design (3 concepts)',
      'Business Card Design',
      'Letterhead Design',
      'Basic Brand Guidelines',
      'Social Media Profile Setup',
      '3 Revision Rounds'
    ],
    popular: false
  },
  {
    name: 'Business Branding',
    price: '45,000',
    description: 'Comprehensive branding for growing businesses',
    features: [
      'Complete Brand Identity',
      'Logo + 5 Variations',
      'Business Stationery Package',
      'Company Profile Design',
      'Brand Guidelines Document',
      'Social Media Templates',
      '5 Revision Rounds'
    ],
    popular: true
  },
  {
    name: 'Corporate Branding',
    price: '85,000',
    description: 'Full corporate identity for established businesses',
    features: [
      'Complete Corporate Identity',
      'Brand Strategy Development',
      'Full Stationery Suite',
      'Marketing Collaterals',
      'Office Branding Design',
      'Digital Brand Assets',
      'Unlimited Revisions'
    ],
    popular: false
  }
];

// FAQ data
const brandingFAQs = [
  {
    question: "What's included in a complete brand identity?",
    answer: "A complete brand identity includes logo design, color palette, typography selection, brand guidelines, business stationery (business cards, letterheads), and often additional materials like brochures, company profiles, and digital assets."
  },
  {
    question: "How long does the branding process take?",
    answer: "The timeline depends on the package: Startup Branding takes 5-7 days, Business Branding takes 7-10 days, and Corporate Branding takes 14-21 days. This includes research, design, revisions, and final delivery."
  },
  {
    question: "Do you provide brand guidelines?",
    answer: "Yes, all our packages include brand guidelines that specify how to use your logo, colors, fonts, and other brand elements correctly across different applications."
  },
  {
    question: "Can you rebrand an existing business?",
    answer: "Absolutely! We specialize in both new brand creation and rebranding existing businesses. We'll help you modernize your brand while maintaining any valuable brand equity you've built."
  },
  {
    question: "What file formats will I receive?",
    answer: "You'll receive your brand assets in multiple formats including vector files (AI, EPS, SVG), high-resolution images (PNG, JPG), and web-optimized files. We ensure you have everything needed for both print and digital use."
  }
  ];

export default function BrandingPage() {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const handlePackageSelect = (packageName: string, price: string) => {
    const whatsappMessage = `Hello Mocky Digital! I'm interested in the ${packageName} package (KES ${price}). Please provide more information.`;
    const whatsappUrl = `https://wa.me/254741590670?text=${encodeURIComponent(whatsappMessage)}`;
    window.open(whatsappUrl, '_blank');
  };

  if (!mounted) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-slate-50 via-white via-gray-50/30 to-orange-50/20 pt-16">
      {/* Hero Section - Matching Catalogue Design */}
      <div className="relative min-h-[60vh] sm:min-h-[70vh] lg:min-h-[80vh] bg-gradient-to-b from-slate-50 via-white to-gray-50/30 overflow-hidden flex items-center">
        {/* Dynamic Background Pattern */}
        <div className="absolute inset-0 opacity-[0.03]">
          <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
            <defs>
              <pattern id="branding-grid" width="15" height="15" patternUnits="userSpaceOnUse">
                <path d="M 15 0 L 0 0 0 15" fill="none" stroke="#FF5400" strokeWidth="0.3"/>
                <circle cx="7.5" cy="7.5" r="0.5" fill="#FF5400" opacity="0.1"/>
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#branding-grid)" />
          </svg>
        </div>

        {/* Enhanced Floating Elements - Responsive */}
        <div className="absolute top-10 sm:top-20 left-4 sm:left-10 w-20 sm:w-32 lg:w-40 h-20 sm:h-32 lg:h-40 bg-gradient-to-br from-orange-200/40 to-red-200/40 rounded-full blur-xl sm:blur-2xl animate-pulse"></div>
        <div className="absolute top-16 sm:top-32 right-4 sm:right-16 w-16 sm:w-24 lg:w-32 h-16 sm:h-24 lg:h-32 bg-gradient-to-br from-blue-200/30 to-purple-200/30 rounded-full blur-lg sm:blur-xl animate-pulse" style={{ animationDelay: '1.5s' }}></div>
        <div className="absolute bottom-20 sm:bottom-40 left-1/4 w-14 sm:w-20 lg:w-28 h-14 sm:h-20 lg:h-28 bg-gradient-to-br from-green-200/25 to-teal-200/25 rounded-full blur-lg sm:blur-xl animate-pulse" style={{ animationDelay: '2.5s' }}></div>
        <div className="absolute bottom-12 sm:bottom-24 right-1/4 sm:right-1/3 w-18 sm:w-28 lg:w-36 h-18 sm:h-28 lg:h-36 bg-gradient-to-br from-pink-200/30 to-rose-200/30 rounded-full blur-xl sm:blur-2xl animate-pulse" style={{ animationDelay: '0.8s' }}></div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 sm:py-16 lg:py-20">
          <div className="text-center space-y-8 sm:space-y-10 lg:space-y-12">
            {/* Premium Badge with Animation */}
            <div className="inline-flex items-center gap-2 sm:gap-3 bg-white/90 backdrop-blur-xl text-[#FF5400] px-4 sm:px-6 lg:px-8 py-3 sm:py-4 rounded-full text-xs sm:text-sm font-bold border border-orange-200/60 shadow-2xl hover:shadow-3xl transition-all duration-500 hover:scale-105 group">
              <SparklesIcon className="h-4 w-4 sm:h-5 sm:w-5 group-hover:rotate-12 transition-transform duration-300" />
              <span className="bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">
                PROFESSIONAL BRANDING SERVICES
              </span>
            </div>

            {/* Revolutionary Title */}
            <div className="space-y-4 sm:space-y-6">
              <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-black text-gray-900 leading-[0.9] tracking-tight px-2">
                Build Your{' '}
                <span className="relative inline-block">
                  <span className="bg-gradient-to-r from-[#FF5400] via-orange-500 to-red-500 bg-clip-text text-transparent">
                    Brand
                  </span>
                  <div className="absolute -bottom-1 sm:-bottom-2 left-0 right-0 h-0.5 sm:h-1 bg-gradient-to-r from-orange-400 to-red-400 rounded-full opacity-30"></div>
                </span>
              </h1>
              <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-black text-gray-800 leading-tight px-2">
                Identity & Strategy
              </h2>
            </div>

            <p className="text-base sm:text-lg md:text-xl lg:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed font-light px-4">
              Create a powerful brand identity that connects with your audience and drives business growth.
              <span className="font-semibold text-gray-800"> From strategy to implementation, we build brands that stand out</span>
            </p>

            {/* Action Buttons */}
            <div className="flex flex-wrap gap-4 sm:gap-6 justify-center px-4">
              <Link
                href="#services"
                className="inline-flex items-center gap-2 bg-[#FF5400] hover:bg-[#e64900] text-white px-6 sm:px-8 py-3 sm:py-4 rounded-full font-semibold text-sm sm:text-base transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl"
              >
                Explore Services
              </Link>
              <Link
                href="#pricing"
                className="inline-flex items-center gap-2 bg-white/90 backdrop-blur-xl hover:bg-white text-gray-900 px-6 sm:px-8 py-3 sm:py-4 rounded-full font-semibold text-sm sm:text-base transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl border border-gray-200/60"
              >
                View Pricing
              </Link>
            </div>
          </div>
        </div>
      </div>

    <main>

      {/* Value Proposition Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-[#FF5400]/10 border border-[#FF5400]/20 mb-6">
              <i className="fas fa-star text-[#FF5400]"></i>
              <span className="text-sm font-medium text-[#FF5400]">WHY CHOOSE US</span>
            </div>
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-black text-gray-900 mb-6">
              Why Your Brand <span className="text-[#FF5400]">Matters</span>
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Your brand is more than just a logo—it's the complete experience your customers have with your business. 
              We help you create a cohesive, memorable brand that builds trust and drives results.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                icon: (
                  <svg className="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0H8m8 0v2a2 2 0 01-2 2H10a2 2 0 01-2-2V6m8 0H8m0 0H5a2 2 0 00-2 2v6a2 2 0 002 2h2M7 7h10" />
                  </svg>
                ),
                title: 'Professional Credibility',
                description: 'A strong brand builds trust and credibility with your customers'
              },
              {
                icon: (
                  <svg className="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                ),
                title: 'Clear Differentiation',
                description: 'Stand out from competitors with a unique brand identity'
              },
              {
                icon: (
                  <svg className="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                  </svg>
                ),
                title: 'Customer Loyalty',
                description: 'Create emotional connections that drive customer loyalty'
              },
              {
                icon: (
                  <svg className="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                  </svg>
                ),
                title: 'Business Growth',
                description: 'Strong brands command premium pricing and grow faster'
              }
            ].map((benefit, index) => (
              <div
                key={index}
                className="text-center p-6 rounded-xl hover:shadow-lg transition-shadow"
              >
                <div className="flex justify-center mb-4 text-[#FF5400]">{benefit.icon}</div>
                <h3 className="text-xl font-bold mb-3 text-gray-900">{benefit.title}</h3>
                <p className="text-gray-600">{benefit.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section id="services" className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-[#FF5400]/10 border border-[#FF5400]/20 mb-6">
              <i className="fas fa-cogs text-[#FF5400]"></i>
              <span className="text-sm font-medium text-[#FF5400]">OUR SERVICES</span>
            </div>
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-black text-gray-900 mb-6">
              Comprehensive Branding <span className="text-[#FF5400]">Solutions</span>
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              From strategy to implementation, we provide end-to-end branding services that cover every aspect of your brand identity.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {brandingServices.map((service, index) => (
              <div
                key={index}
                className="bg-white p-8 rounded-xl shadow-sm hover:shadow-lg transition-all duration-300"
              >
                <div className="flex justify-center mb-6 text-[#FF5400]">{service.icon}</div>
                <h3 className="text-xl font-bold mb-4 text-gray-900">{service.title}</h3>
                <p className="text-gray-600 mb-6">{service.description}</p>
                <ul className="space-y-2">
                  {service.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center gap-3">
                      <div className="w-2 h-2 rounded-full bg-[#FF5400]"></div>
                      <span className="text-gray-600 text-sm">{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Brand Impact Section */}
      <section className="py-20 bg-gradient-to-br from-gray-50 via-white to-gray-50">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-16 items-center">
            {/* Left Content */}
            <div className="lg:pr-8">
              <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-[#FF5400]/10 border border-[#FF5400]/20 mb-6">
                <i className="fas fa-bolt text-[#FF5400]"></i>
                <span className="text-sm font-medium text-[#FF5400]">BRAND IMPACT</span>
              </div>
              <h2 className="text-3xl md:text-4xl lg:text-5xl font-black text-gray-900 mb-6">
                The Power of Strong <span className="text-[#FF5400]">Branding</span>
              </h2>
              <p className="text-lg text-gray-600 mb-10 leading-relaxed">
                Professional branding is not just about looking good—it's about creating a strategic advantage 
                that drives real business results and long-term success.
              </p>

              {/* Statistics */}
              <div className="space-y-8">
                {[
                  {
                    stat: '73%',
                    description: 'of consumers are willing to pay more for products from sustainable brands',
                    icon: (
                      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                      </svg>
                    )
                  },
                  {
                    stat: '80%',
                    description: 'of business leaders believe branding increases business value',
                    icon: (
                      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                      </svg>
                    )
                  },
                  {
                    stat: '3.5x',
                    description: 'higher revenue growth for companies with strong brand consistency',
                    icon: (
                      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                      </svg>
                    )
                  }
                ].map((stat, index) => (
                  <div key={index} className="flex items-start gap-6 p-6 bg-white rounded-xl shadow-sm border border-gray-100 hover:shadow-md transition-shadow">
                    <div className="flex-shrink-0">
                      <div className="w-12 h-12 bg-[#FF5400]/10 rounded-lg flex items-center justify-center text-[#FF5400]">
                        {stat.icon}
                      </div>
                    </div>
                    <div className="flex-1">
                      <div className="text-3xl font-bold text-[#FF5400] mb-2">{stat.stat}</div>
                      <div className="text-gray-600 leading-relaxed">{stat.description}</div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Right Visual Elements */}
            <div className="relative lg:pl-8">
              <div className="relative">
                {/* Background decorative elements */}
                <div className="absolute -top-4 -left-4 w-24 h-24 bg-[#FF5400]/10 rounded-full blur-xl"></div>
                <div className="absolute -bottom-8 -right-8 w-32 h-32 bg-[#0A2647]/10 rounded-full blur-xl"></div>
                
                {/* Main grid */}
                <div className="grid grid-cols-2 gap-6 relative z-10">
                  {[
                    { 
                      title: 'Logo Design', 
                      color: 'bg-[#FF5400]',
                      textColor: 'text-white',
                      description: 'Visual Identity',
                      icon: (
                        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z" />
                        </svg>
                      )
                    },
                    { 
                      title: 'Brand Colors', 
                      color: 'bg-[#0A2647]',
                      textColor: 'text-white',
                      highlightColor: 'text-[#FF5400]',
                      description: 'Color Psychology',
                      icon: (
                        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z" />
                        </svg>
                      )
                    },
                    { 
                      title: 'Typography', 
                      color: 'bg-gray-800',
                      textColor: 'text-white',
                      highlightColor: 'text-[#FF5400]',
                      description: 'Font Selection',
                      icon: (
                        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 18H18.5" />
                        </svg>
                      )
                    },
                    { 
                      title: 'Brand Voice', 
                      color: 'bg-gradient-to-br from-[#FF5400] to-[#e84a00]',
                      textColor: 'text-white',
                      description: 'Communication Style',
                      icon: (
                        <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z" />
                        </svg>
                      )
                    }
                  ].map((element, index) => (
                    <div
                      key={index}
                      className={`${element.color} ${element.textColor} p-8 rounded-2xl hover:scale-105 transition-all duration-300 cursor-pointer shadow-lg hover:shadow-xl`}
                    >
                      <div className="flex flex-col items-center text-center space-y-3">
                        <div className="text-white">{element.icon}</div>
                        <div>
                          <h3 className={`font-bold text-lg mb-1 ${element.highlightColor || 'text-white'}`}>{element.title}</h3>
                          <p className="text-sm text-white/90">{element.description}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section id="pricing" className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-[#FF5400]/10 border border-[#FF5400]/20 mb-6">
              <i className="fas fa-tag text-[#FF5400]"></i>
              <span className="text-sm font-medium text-[#FF5400]">PRICING</span>
            </div>
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-black text-gray-900 mb-6">
              Branding <span className="text-[#FF5400]">Packages</span>
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Choose the perfect branding package for your business needs and budget
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {brandingPackages.map((pkg, index) => (
              <div
                key={index}
                className={`relative bg-white rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 ${
                  pkg.popular ? 'ring-2 ring-[#FF5400] transform scale-105' : ''
                }`}
              >
                {pkg.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <span className="bg-[#FF5400] text-white px-4 py-1 rounded-full text-sm font-medium">
                      Most Popular
                    </span>
                  </div>
                )}
                
                <div className="p-8">
                  <h3 className="text-2xl font-bold mb-2 text-gray-900">{pkg.name}</h3>
                  <p className="text-gray-600 mb-6">{pkg.description}</p>
                  
                  <div className="mb-6">
                    <span className="text-4xl font-bold text-[#FF5400]">KES {pkg.price}</span>
                  </div>

                  <ul className="space-y-3 mb-8">
                    {pkg.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center gap-3">
                        <div className="w-5 h-5 rounded-full bg-green-100 flex items-center justify-center flex-shrink-0">
                          <svg className="w-3 h-3 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                        </div>
                        <span className="text-gray-600">{feature}</span>
                      </li>
                    ))}
                  </ul>

                  <button
                    onClick={() => handlePackageSelect(pkg.name, pkg.price)}
                    className={`w-full py-3 px-6 rounded-full font-medium transition-colors ${
                      pkg.popular
                        ? 'bg-[#FF5400] text-white hover:bg-[#E54D00]'
                        : 'bg-[#0A2647] text-white hover:bg-[#1a3a6d]'
                    }`}
                  >
                    Get Started
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>



      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-br from-[#0A2647] to-[#1a3a6d] text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-black mb-6">
            Ready to Build Your <span className="text-[#FF5400]">Brand?</span>
          </h2>
          <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
            Let's create a brand identity that tells your story and connects with your audience. 
            Start your branding journey today.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="https://wa.me/254741590670?text=Hello%20Mocky%20Digital!%20I'm%20interested%20in%20your%20branding%20services."
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center gap-2 px-8 py-4 bg-[#FF5400] text-white rounded-full hover:bg-[#E54D00] transition-colors font-medium"
            >
              Start Your Project
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </Link>
            <Link
              href="/contact"
              className="inline-flex items-center gap-2 px-8 py-4 border border-white/20 text-white rounded-full hover:bg-white/10 transition-colors font-medium"
            >
              Get Consultation
            </Link>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <ModernFAQSection faqs={brandingFAQs} />
    </main>
    </div>
  );
}