'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { getS3ImageUrl } from '@/utils/imageUtils';

export default function TestS3Images() {
  const [testResults, setTestResults] = useState<any[]>([]);
  const [s3Images, setS3Images] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function runTests() {
      setLoading(true);

      // Test various image key scenarios
      const testCases = [
        {
          name: 'Direct S3 Key',
          imageKey: 'images/portfolio/logos/1.jpg',
          expected: 'Should construct full S3 URL'
        },
        {
          name: 'Images Portfolio Websites',
          imageKey: 'images/portfolio/websites/1.jpg',
          expected: 'Should construct full S3 URL for websites'
        },
        {
          name: 'Without images prefix',
          imageKey: 'portfolio/logos/2.jpg',
          expected: 'Should still work'
        },
        {
          name: 'Leading slash',
          imageKey: '/images/portfolio/branding/3.jpg',
          expected: 'Should strip leading slash'
        },
        {
          name: 'Empty key',
          imageKey: '',
          expected: 'Should return placeholder'
        }
      ];

      const results = testCases.map(testCase => {
        const url = getS3ImageUrl(testCase.imageKey);
        return {
          ...testCase,
          result: url,
          isValidUrl: url.startsWith('http') || url.startsWith('/'),
        };
      });

      setTestResults(results);

      // Fetch actual S3 images from the API
      try {
        const response = await fetch('/api/admin/s3-images?category=all&t=' + Date.now());
        if (response.ok) {
          const images = await response.json();
          setS3Images(images.slice(0, 5)); // Show first 5 images
        }
      } catch (error) {
        console.error('Error fetching S3 images:', error);
      }

      setLoading(false);
    }

    runTests();
  }, []);

  if (loading) {
    return (
      <div className="container mx-auto py-8">
        <div className="text-center">Loading S3 image tests...</div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <h1 className="text-3xl font-bold mb-8">S3 Image URL Test</h1>

      {/* Environment Info */}
      <div className="mb-8 p-4 bg-gray-100 rounded">
        <h2 className="text-xl font-semibold mb-4">Environment Information</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <strong>S3 Endpoint:</strong> {process.env.NEXT_PUBLIC_S3_ENDPOINT || 'Not set'}
          </div>
          <div>
            <strong>S3 Bucket:</strong> {process.env.NEXT_PUBLIC_S3_BUCKET || 'Not set'}
          </div>
          <div>
            <strong>S3 Region:</strong> {process.env.NEXT_PUBLIC_S3_REGION || 'Not set'}
          </div>
          <div>
            <strong>NODE_ENV:</strong> {process.env.NODE_ENV || 'Not set'}
          </div>
        </div>
      </div>

      {/* Test Results */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">getS3ImageUrl() Test Results</h2>
        <div className="overflow-x-auto">
          <table className="min-w-full bg-white border border-gray-300">
            <thead>
              <tr className="bg-gray-50">
                <th className="px-4 py-2 text-left">Test Case</th>
                <th className="px-4 py-2 text-left">Input Key</th>
                <th className="px-4 py-2 text-left">Generated URL</th>
                <th className="px-4 py-2 text-left">Status</th>
              </tr>
            </thead>
            <tbody>
              {testResults.map((result, index) => (
                <tr key={index} className="border-t">
                  <td className="px-4 py-2 font-medium">{result.name}</td>
                  <td className="px-4 py-2 font-mono text-sm">{result.imageKey || '(empty)'}</td>
                  <td className="px-4 py-2 font-mono text-sm break-all">{result.result}</td>
                  <td className="px-4 py-2">
                    <span className={`px-2 py-1 rounded text-xs ${
                      result.isValidUrl ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {result.isValidUrl ? 'Valid' : 'Invalid'}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Real S3 Images */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Actual S3 Images from API</h2>
        {s3Images.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {s3Images.map((image, index) => (
              <div key={index} className="border rounded-lg p-4">
                <div className="mb-2">
                  <strong>Filename:</strong> {image.filename}
                </div>
                <div className="mb-2">
                  <strong>Category:</strong> {image.category}
                </div>
                <div className="mb-2">
                  <strong>URL:</strong> 
                  <div className="font-mono text-xs break-all">{image.url}</div>
                </div>
                <div className="relative aspect-square bg-gray-100 rounded">
                  <Image
                    src={image.url}
                    alt={image.filename}
                    fill
                    className="object-contain rounded"
                    onError={(e) => {
                      console.error('Image failed to load:', image.url);
                      const target = e.target as HTMLImageElement;
                      target.style.display = 'none';
                      const parent = target.parentElement;
                      if (parent) {
                        const errorDiv = document.createElement('div');
                        errorDiv.className = 'flex items-center justify-center h-full text-red-500';
                        errorDiv.textContent = 'Failed to load';
                        parent.appendChild(errorDiv);
                      }
                    }}
                  />
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-gray-500">No S3 images found or error loading images.</div>
        )}
      </div>

      {/* Manual URL Test */}
      <div className="mb-8">
        <h2 className="text-xl font-semibold mb-4">Manual URL Test</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="border rounded-lg p-4">
            <h3 className="font-medium mb-2">Direct S3 URL Test</h3>
            <div className="mb-2 font-mono text-xs break-all">
              https://fr-par-1.linodeobjects.com/mocky2/images/portfolio/logos/1.jpg
            </div>
            <div className="relative aspect-square bg-gray-100 rounded">
              <Image
                src="https://fr-par-1.linodeobjects.com/mocky2/images/portfolio/logos/1.jpg"
                alt="Direct S3 test"
                fill
                className="object-contain rounded"
                onError={(e) => {
                  console.error('Direct S3 URL failed to load');
                  const target = e.target as HTMLImageElement;
                  target.style.display = 'none';
                  const parent = target.parentElement;
                  if (parent) {
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'flex items-center justify-center h-full text-red-500';
                    errorDiv.textContent = 'Failed to load';
                    parent.appendChild(errorDiv);
                  }
                }}
              />
            </div>
          </div>

          <div className="border rounded-lg p-4">
            <h3 className="font-medium mb-2">getS3ImageUrl() Test</h3>
            <div className="mb-2 font-mono text-xs break-all">
              {getS3ImageUrl('images/portfolio/logos/1.jpg')}
            </div>
            <div className="relative aspect-square bg-gray-100 rounded">
              <Image
                src={getS3ImageUrl('images/portfolio/logos/1.jpg')}
                alt="getS3ImageUrl test"
                fill
                className="object-contain rounded"
                onError={(e) => {
                  console.error('getS3ImageUrl failed to load');
                  const target = e.target as HTMLImageElement;
                  target.style.display = 'none';
                  const parent = target.parentElement;
                  if (parent) {
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'flex items-center justify-center h-full text-red-500';
                    errorDiv.textContent = 'Failed to load';
                    parent.appendChild(errorDiv);
                  }
                }}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 