'use client';

import React, { useState } from 'react';
import EnhancedFileUpload from '../../components/EnhancedFileUpload';
import { Card } from '../../components/ui/card';

interface UploadResult {
  url: string;
  fileName: string;
  fileSize: number;
  timestamp: number;
}

export default function TestEnhancedUploadPage() {
  const [uploadResults, setUploadResults] = useState<UploadResult[]>([]);
  const [error, setError] = useState<string | null>(null);

  const handleUploadComplete = (result: { url: string; fileName: string; fileSize: number }) => {
    console.log('Upload completed:', result);
    
    setUploadResults(prev => [...prev, {
      ...result,
      timestamp: Date.now()
    }]);
    
    setError(null);
  };

  const handleUploadError = (error: string) => {
    console.error('Upload error:', error);
    setError(error);
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatTimestamp = (timestamp: number): string => {
    return new Date(timestamp).toLocaleString();
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Enhanced File Upload Test</h1>
        <p className="text-gray-600">
          Test the enhanced file upload system with chunked uploads, compression, and connection detection.
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Upload Section */}
        <div>
          <Card className="p-6">
            <h2 className="text-xl font-semibold mb-4">Upload File</h2>
            
            <EnhancedFileUpload
              onUploadComplete={handleUploadComplete}
              onUploadError={handleUploadError}
              accept="image/*,video/*,audio/*,.pdf,.doc,.docx,.txt"
              maxFileSize={200 * 1024 * 1024} // 200MB
              folder="test-uploads"
              className="mb-4"
            />

            {error && (
              <div className="mt-4 p-3 bg-red-100 border border-red-300 rounded text-red-700 text-sm">
                <strong>Error:</strong> {error}
              </div>
            )}
          </Card>

          {/* Features Info */}
          <Card className="p-6 mt-6">
            <h3 className="text-lg font-semibold mb-3">Features Included</h3>
            <ul className="space-y-2 text-sm text-gray-700">
              <li className="flex items-center">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                Automatic connection speed detection
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                Adaptive file compression based on connection
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                Chunked uploads for large files (&gt;10MB)
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                Resume capability for interrupted uploads
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                Real-time progress tracking with ETA
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                Automatic retry with exponential backoff
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-green-500 rounded-full mr-3"></span>
                Dynamic timeouts (up to 30 minutes)
              </li>
            </ul>
          </Card>
        </div>

        {/* Results Section */}
        <div>
          <Card className="p-6">
            <h2 className="text-xl font-semibold mb-4">Upload Results</h2>
            
            {uploadResults.length === 0 ? (
              <p className="text-gray-500 text-center py-8">
                No uploads yet. Try uploading a file to see the results here.
              </p>
            ) : (
              <div className="space-y-4 max-h-96 overflow-y-auto">
                {uploadResults.map((result, index) => (
                  <div key={index} className="border rounded-lg p-4 bg-gray-50">
                    <div className="flex justify-between items-start mb-2">
                      <h4 className="font-medium truncate flex-1 mr-2">{result.fileName}</h4>
                      <span className="text-xs text-gray-500 whitespace-nowrap">
                        {formatFileSize(result.fileSize)}
                      </span>
                    </div>
                    
                    <div className="text-sm text-gray-600 mb-2">
                      <div className="truncate">
                        <strong>URL:</strong> 
                        <a 
                          href={result.url} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:text-blue-800 ml-1"
                        >
                          {result.url}
                        </a>
                      </div>
                    </div>
                    
                    <div className="text-xs text-gray-500">
                      Uploaded: {formatTimestamp(result.timestamp)}
                    </div>
                    
                    {/* Preview for images */}
                    {result.fileName.match(/\.(jpg|jpeg|png|gif|webp)$/i) && (
                      <div className="mt-3">
                        <img 
                          src={result.url} 
                          alt={result.fileName}
                          className="max-w-full h-32 object-cover rounded border"
                          onError={(e) => {
                            (e.target as HTMLImageElement).style.display = 'none';
                          }}
                        />
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </Card>

          {/* Connection Test */}
          <Card className="p-6 mt-6">
            <h3 className="text-lg font-semibold mb-3">Connection Test</h3>
            <p className="text-sm text-gray-600 mb-3">
              The system automatically detects your connection speed and adjusts upload strategy accordingly.
            </p>
            <div className="text-xs text-gray-500">
              <div><strong>Fast Connection:</strong> Minimal compression, larger chunks</div>
              <div><strong>Slow Connection:</strong> Moderate compression, smaller chunks</div>
              <div><strong>Very Slow Connection:</strong> Aggressive compression, tiny chunks</div>
            </div>
          </Card>
        </div>
      </div>

      {/* Technical Details */}
      <Card className="p-6 mt-8">
        <h3 className="text-lg font-semibold mb-3">Technical Implementation</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-gray-700">
          <div>
            <h4 className="font-medium mb-2">Client-Side Features:</h4>
            <ul className="space-y-1">
              <li>• Connection speed detection using Network Information API</li>
              <li>• Client-side file chunking (2MB-10MB chunks)</li>
              <li>• Browser-based image compression</li>
              <li>• LocalStorage-based resume capability</li>
              <li>• Concurrent chunk uploads (max 3 parallel)</li>
              <li>• Real-time progress tracking</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium mb-2">Server-Side Features:</h4>
            <ul className="space-y-1">
              <li>• Temporary chunk storage in filesystem</li>
              <li>• Chunk validation and ordering</li>
              <li>• File reconstruction from chunks</li>
              <li>• S3 upload integration</li>
              <li>• Automatic cleanup of temporary files</li>
              <li>• Session management and recovery</li>
            </ul>
          </div>
        </div>
      </Card>
    </div>
  );
} 