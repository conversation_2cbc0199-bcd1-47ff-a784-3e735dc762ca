'use client';
import React from 'react';

import { useState, FormEvent } from 'react';
import PageHero from '@/components/PageHero';

interface FormData {
  name: string;
  email: string;
  phone: string;
  company?: string;
  serviceType: string;
  serviceCategory: string;
  budget: string;
  timeline: string;
  description: string;
}

const serviceCategories = {
  'Brand Identity': [
    'Logo Design',
    'Brand Identity Package',
    'Business/ID Card Design',
    'Letterhead Design',
    'Invoice Design',
    'Company Profile Design',
    'Product Package Design',
    'Label/Sticker Design'
  ],
  'Marketing Materials': [
    'Brochure Design',
    'Flyer Design',
    'Social Media Posts',
    'Rollup Banner Design',
    'Billboard Design',
    'Poster Design',
    'Menu Design',
    'Presentation Design'
  ],
  'Publication Design': [
    'Book Cover Design',
    'Ebook/PDF Layout',
    'Magazine Layout',
    'Newsletter Design',
    'Annual Report Design',
    'Catalog Design',
    'Editorial Design'
  ],
  'Digital Marketing': [
    'Social Media Management',
    'Content Creation',
    'Email Marketing',
    'SEO Services',
    'Digital Strategy',
    'Paid Advertising',
    'Brand Strategy',
    'Marketing Consultation'
  ]
};

// Add price ranges for reference (not displayed to users)
const servicePriceRanges = {
  'Logo Design': '10,000-50,000',
  'Brand Identity Package': '20,000-150,000',
  'Business/ID Card Design': '3,000',
  'Letterhead Design': '3,000',
  'Invoice Design': '3,000',
  'Company Profile Design': '30,000',
  'Brochure Design': '15,000',
  'Flyer Design': '3,000',
  'Social Media Posts': '2,500',
  'Rollup Banner Design': '5,000',
  'Billboard Design': '8,000',
  'Label/Sticker Design': '5,000',
  'Product Package Design': '20,000',
  'Book Cover Design': '15,000',
  'Ebook/PDF Layout': '25,000'
};

export default function ServiceRequest() {
  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    phone: '',
    company: '',
    serviceType: '',
    serviceCategory: '',
    budget: '',
    timeline: '',
    description: ''
  });

  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e: FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Format the message for WhatsApp
    const message = `*New Service Request*
Name: ${formData.name}
Email: ${formData.email}
Phone: ${formData.phone}
${formData.company ? `Company: ${formData.company}\n` : ''}
Category: ${formData.serviceType}
Service: ${formData.serviceCategory}
Budget: ${formData.budget}
Timeline: ${formData.timeline}
Description: ${formData.description}`;

    // WhatsApp number
    const whatsappNumber = '254741590670';
    const whatsappUrl = `https://wa.me/${whatsappNumber}?text=${encodeURIComponent(message)}`;
    
    window.open(whatsappUrl, '_blank');
    setIsSubmitting(false);
    
    // Reset form
    setFormData({
      name: '',
      email: '',
      phone: '',
      company: '',
      serviceType: '',
      serviceCategory: '',
      budget: '',
      timeline: '',
      description: ''
    });
  };

  return (
    <main className="min-h-screen pt-28 md:pt-32">
      <PageHero 
        title="Request Our Services"
        description="Let's bring your creative vision to life"
      />

      <section className="py-24 bg-gradient-to-b from-gray-50 to-white relative overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0 overflow-hidden opacity-[0.03] pointer-events-none">
          <div className="absolute -left-1/4 -top-1/4 w-1/2 h-1/2 bg-primary rounded-full blur-3xl" />
          <div className="absolute -right-1/4 -bottom-1/4 w-1/2 h-1/2 bg-primary rounded-full blur-3xl" />
        </div>

        <div className="container mx-auto px-4 relative">
          {/* Service Categories */}
          <div className="max-w-6xl mx-auto mb-20">
            <h2 className="text-3xl md:text-4xl font-bold text-center mb-4 text-gray-900">Our Design Services</h2>
            <p className="text-lg text-gray-600 text-center mb-12 max-w-2xl mx-auto">
              Choose from our comprehensive range of professional design services tailored to your needs
            </p>
            
            <div className="grid md:grid-cols-2 gap-8">
              {Object.entries(serviceCategories).map(([category, services], index) => (
                <div 
                  key={index} 
                  className="bg-white rounded-2xl shadow-sm border border-gray-100 p-8 hover:shadow-lg transition-all duration-300 group"
                >
                  <div className="flex items-start gap-4 mb-6">
                    <div className="p-3 rounded-xl bg-primary/5 text-primary group-hover:bg-primary group-hover:text-white transition-colors">
                      {category === 'Brand Identity' && (
                        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01" />
                        </svg>
                      )}
                      {category === 'Marketing Materials' && (
                        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z" />
                        </svg>
                      )}
                      {category === 'Publication Design' && (
                        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                        </svg>
                      )}
                      {category === 'Digital Marketing' && (
                        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M3 15a4 4 0 004 4h9a5 5 0 10-.1-9.999 5.002 5.002 0 10-9.78 2.096A4.001 4.001 0 003 15z" />
                        </svg>
                      )}
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-gray-900 group-hover:text-primary transition-colors">{category}</h3>
                      <p className="text-gray-500 mt-1">
                        {category === 'Brand Identity' && 'Create a strong, memorable brand identity'}
                        {category === 'Marketing Materials' && 'Engage your audience with compelling designs'}
                        {category === 'Publication Design' && 'Professional layouts for your content'}
                        {category === 'Digital Marketing' && 'Boost your online presence'}
                      </p>
                    </div>
                  </div>
                  <ul className="space-y-3 pl-4">
                    {services.map((service, serviceIndex) => (
                      <li key={serviceIndex} className="flex items-center text-gray-600 group-hover:text-gray-700">
                        <svg className="w-4 h-4 mr-3 text-primary flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                        </svg>
                        <span>{service}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>

          {/* Request Form Section */}
          <div className="max-w-4xl mx-auto relative">
            <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-8 md:p-10">
              <div className="text-center mb-10">
                <h2 className="text-3xl font-bold text-gray-900 mb-4">Start Your Project</h2>
                <p className="text-gray-600 max-w-2xl mx-auto">
                  Fill out the form below and we'll get back to you within 1-2 business hours to discuss your project in detail.
                </p>
              </div>
              
              <form onSubmit={handleSubmit} className="space-y-8">
                {/* Personal Information */}
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">Full Name *</label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      required
                      value={formData.name}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-primary focus:ring-2 focus:ring-primary/20 outline-none transition-all"
                      placeholder="Your full name"
                    />
                  </div>
                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">Email Address *</label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      required
                      value={formData.email}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-primary focus:ring-2 focus:ring-primary/20 outline-none transition-all"
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>

                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">Phone Number *</label>
                    <input
                      type="tel"
                      id="phone"
                      name="phone"
                      required
                      value={formData.phone}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-primary focus:ring-2 focus:ring-primary/20 outline-none transition-all"
                      placeholder="Your phone number"
                    />
                  </div>
                  <div>
                    <label htmlFor="company" className="block text-sm font-medium text-gray-700 mb-2">Company Name</label>
                    <input
                      type="text"
                      id="company"
                      name="company"
                      value={formData.company}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-primary focus:ring-2 focus:ring-primary/20 outline-none transition-all"
                      placeholder="Your company name (optional)"
                    />
                  </div>
                </div>

                {/* Service Details */}
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="serviceType" className="block text-sm font-medium text-gray-700 mb-2">Service Category *</label>
                    <select
                      id="serviceType"
                      name="serviceType"
                      required
                      value={formData.serviceType}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-primary focus:ring-2 focus:ring-primary/20 outline-none transition-all appearance-none bg-white"
                    >
                      <option value="">Select a category</option>
                      {Object.keys(serviceCategories).map((category, index) => (
                        <option key={index} value={category}>{category}</option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <label htmlFor="serviceCategory" className="block text-sm font-medium text-gray-700 mb-2">Specific Service *</label>
                    <select
                      id="serviceCategory"
                      name="serviceCategory"
                      required
                      value={formData.serviceCategory}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-primary focus:ring-2 focus:ring-primary/20 outline-none transition-all appearance-none bg-white"
                    >
                      <option value="">Select a service</option>
                      {formData.serviceType && serviceCategories[formData.serviceType as keyof typeof serviceCategories].map((service, index) => (
                        <option key={index} value={service}>{service}</option>
                      ))}
                    </select>
                  </div>
                </div>

                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="budget" className="block text-sm font-medium text-gray-700 mb-2">Budget Range (KSH) *</label>
                    <select
                      id="budget"
                      name="budget"
                      required
                      value={formData.budget}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-primary focus:ring-2 focus:ring-primary/20 outline-none transition-all appearance-none bg-white"
                    >
                      <option value="">Select your budget</option>
                      <option value="Below 5,000">Below 5,000</option>
                      <option value="5,000-15,000">5,000-15,000</option>
                      <option value="15,000-30,000">15,000-30,000</option>
                      <option value="30,000-50,000">30,000-50,000</option>
                      <option value="50,000-100,000">50,000-100,000</option>
                      <option value="Above 100,000">Above 100,000</option>
                    </select>
                  </div>
                  <div>
                    <label htmlFor="timeline" className="block text-sm font-medium text-gray-700 mb-2">Project Timeline *</label>
                    <select
                      id="timeline"
                      name="timeline"
                      required
                      value={formData.timeline}
                      onChange={handleInputChange}
                      className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-primary focus:ring-2 focus:ring-primary/20 outline-none transition-all appearance-none bg-white"
                    >
                      <option value="">Select timeline</option>
                      <option value="Urgent (1-2 days)">Urgent (1-2 days)</option>
                      <option value="Standard (3-5 days)">Standard (3-5 days)</option>
                      <option value="Flexible (1-2 weeks)">Flexible (1-2 weeks)</option>
                      <option value="Not urgent (2+ weeks)">Not urgent (2+ weeks)</option>
                    </select>
                  </div>
                </div>

                <div>
                  <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">Project Description *</label>
                  <textarea
                    id="description"
                    name="description"
                    required
                    value={formData.description}
                    onChange={handleInputChange}
                    rows={5}
                    className="w-full px-4 py-3 rounded-xl border border-gray-200 focus:border-primary focus:ring-2 focus:ring-primary/20 outline-none transition-all resize-none"
                    placeholder="Please describe your project requirements, goals, and any specific details that will help us understand your needs better..."
                  />
                </div>

                <div className="flex items-center justify-end pt-6">
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="inline-flex items-center px-8 py-4 rounded-xl bg-primary text-white font-medium hover:bg-primary/90 transition-all disabled:opacity-50 disabled:cursor-not-allowed shadow-lg shadow-primary/20 hover:shadow-xl hover:shadow-primary/30"
                  >
                    {isSubmitting ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Processing...
                      </>
                    ) : (
                      <>
                        Submit Request
                        <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                        </svg>
                      </>
                    )}
                  </button>
                </div>
              </form>
            </div>

            {/* What Happens Next */}
            <div className="mt-12 bg-gradient-to-br from-gray-50 to-white rounded-2xl p-8 border border-gray-100 shadow-sm">
              <div className="flex items-start space-x-6">
                <div className="text-primary bg-primary/5 p-3 rounded-xl">
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div className="flex-1">
                  <h4 className="text-xl font-bold text-gray-900 mb-4">What happens next?</h4>
                  <div className="space-y-4">
                    <div className="flex items-start">
                      <div className="flex-shrink-0 h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center text-primary font-semibold">
                        1
                      </div>
                      <p className="ml-4 text-gray-600">We'll review your request within 1-2 business hours</p>
                    </div>
                    <div className="flex items-start">
                      <div className="flex-shrink-0 h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center text-primary font-semibold">
                        2
                      </div>
                      <p className="ml-4 text-gray-600">Our team will contact you via WhatsApp to discuss details</p>
                    </div>
                    <div className="flex items-start">
                      <div className="flex-shrink-0 h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center text-primary font-semibold">
                        3
                      </div>
                      <p className="ml-4 text-gray-600">You'll receive a customized proposal based on your needs</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </main>
  );
} 