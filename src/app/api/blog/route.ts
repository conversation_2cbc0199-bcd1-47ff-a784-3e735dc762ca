import { NextRequest, NextResponse } from 'next/server';
import { BlogPost } from '@/types/blog';
import * as blogService from '@/services/blogService';

// GET handler - Get all published blog posts
export async function GET(request: NextRequest) {
  try {
    console.log('API route: Fetching blog posts from database');

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    const tag = searchParams.get('tag');
    const search = searchParams.get('search');
    const limit = searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : undefined;

    console.log(`API route debug - URL: ${request.url}, Headers: ${JSON.stringify(request.headers)}`);

    // Get all published blog posts from database
    let blogPosts = await blogService.getPublishedBlogPosts();
    
    console.log(`API route debug - Raw posts from database: ${blogPosts.length}`);
    console.log(`API route debug - Post dates: ${blogPosts.map(p => p.publishedAt).join(', ')}`);

    // Apply category filter if provided
    if (category) {
      blogPosts = blogPosts.filter(post => post.category === category);
    }

    // Apply tag filter if provided


    // Apply search filter if provided
    if (search) {
      const searchLower = search.toLowerCase();
      blogPosts = blogPosts.filter(post =>
        post.title.toLowerCase().includes(searchLower) ||
        post.content.toLowerCase().includes(searchLower) ||
        post.excerpt.toLowerCase().includes(searchLower) ||
        false // tags removed
      );
    }

    // Apply limit if provided
    if (limit && !isNaN(limit) && limit > 0) {
      blogPosts = blogPosts.slice(0, limit);
    }

    console.log(`API route: Successfully fetched ${blogPosts.length} blog posts from database`);

    // Add cache-busting header
    return NextResponse.json(blogPosts, {
      headers: {
        'Cache-Control': 'no-store, max-age=0, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      },
    });
  } catch (error) {
    console.error('Error in GET blog posts:', error);
    return NextResponse.json(
      { error: 'Failed to fetch blog posts' },
      { status: 500 }
    );
  }
}
