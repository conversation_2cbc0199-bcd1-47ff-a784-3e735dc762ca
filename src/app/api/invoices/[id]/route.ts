import { NextRequest, NextResponse } from 'next/server';
import { getInvoiceById } from '@/services/invoiceService';
import { promises as fs } from 'fs';
import path from 'path';

/**
 * Generate HTML for an invoice
 * @param invoice The invoice data
 * @returns HTML string
 */
function generateInvoiceHtml(invoice: any): string {
  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Invoice ${invoice.invoiceNumber}</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          line-height: 1.6;
          color: #333;
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
        }
        .header-container {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 30px;
          position: relative;
        }
        .logo-container {
          flex: 0 0 25%;
          text-align: left;
        }
        .company-info {
          flex: 0 0 25%;
          text-align: right;
        }
        .invoice-center {
          flex: 0 0 50%;
          text-align: center;
          padding: 0 15px;
        }
        .invoice-title {
          margin: 0 0 15px 0;
        }
        .invoice-details {
          text-align: center;
          margin-bottom: 10px;
        }
        .customer-info {
          margin-bottom: 20px;
        }
        .customer-info p {
          margin: 0 0 5px 0;
        }
        .section-title {
          font-weight: bold;
          margin-bottom: 10px;
          color: #0F2557;
          font-size: 16px;
        }
        table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 20px;
        }
        th, td {
          padding: 8px;
          text-align: left;
          border-bottom: 1px solid #ddd;
        }
        th {
          background-color: #f2f2f2;
        }
        .summary {
          margin-left: auto;
          width: 300px;
          text-align: right;
          margin-bottom: 20px;
        }
        .summary p {
          margin: 5px 0;
        }
        .summary p span {
          display: inline-block;
          width: 120px;
          text-align: right;
        }
        .total {
          font-weight: bold;
          font-size: 16px;
          color: #0F2557;
        }
        .notes {
          margin-bottom: 20px;
        }
        .footer {
          text-align: center;
          margin-top: 30px;
          color: #666;
        }
        .print-button {
          display: block;
          margin: 20px auto;
          padding: 10px 20px;
          background-color: #0F2557;
          color: white;
          border: none;
          border-radius: 5px;
          cursor: pointer;
          font-size: 16px;
        }
        /* Responsive scaling for different screen sizes */
        @media screen and (max-width: 600px) {
          body {
            padding: 10px;
            font-size: 14px;
          }
          .header-container {
            flex-direction: column;
            align-items: center;
          }
          .logo-container, .company-info, .invoice-center {
            flex: 0 0 100%;
            text-align: center;
            margin-bottom: 15px;
          }
          .summary {
            width: 100%;
          }
        }
        /* Print-specific styles with A4 optimization */
        @media print {
          @page {
            size: A4;
            margin: 15mm 10mm 15mm 10mm;
          }
          html {
            background-color: #FFFFFF;
            margin: 0;
            padding: 0;
          }
          body {
            padding: 0;
            margin: 0;
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
            width: 100%;
            max-width: 190mm; /* A4 width minus margins */
            box-sizing: border-box;
            font-size: 10pt;
            line-height: 1.3;
            font-family: Arial, sans-serif;
          }
          .print-button {
            display: none !important;
          }
          .header-container {
            display: flex !important;
            margin-bottom: 15mm;
            page-break-inside: avoid;
            height: auto;
            max-height: 40mm;
          }
          .logo-container {
            flex: 0 0 30%;
            text-align: left;
          }
          .logo-container img {
            display: block !important;
            visibility: visible !important;
            max-width: 25mm;
            max-height: 20mm;
            height: auto;
            width: auto;
          }
          .invoice-center {
            flex: 0 0 40%;
            text-align: center;
          }
          .invoice-center h1 {
            font-size: 14pt;
            margin: 2mm 0;
            font-weight: bold;
            color: #000 !important;
          }
          .invoice-details p {
            font-size: 8pt;
            margin: 1mm 0;
            line-height: 1.2;
          }
          .company-info {
            flex: 0 0 30%;
            text-align: right;
          }
          .company-info h2 {
            font-size: 10pt;
            margin: 0 0 2mm 0;
            font-weight: bold;
          }
          .company-info p {
            font-size: 8pt;
            margin: 0.5mm 0;
            line-height: 1.2;
          }
          .customer-info {
            page-break-inside: avoid;
            margin-bottom: 8mm;
          }
          .customer-info p {
            font-size: 8pt;
            margin: 1mm 0;
            line-height: 1.2;
          }
          .section-title {
            font-size: 10pt;
            margin: 5mm 0 3mm 0;
            font-weight: bold;
          }
          table {
            width: 100%;
            font-size: 8pt;
            page-break-inside: auto;
            border-collapse: collapse;
            margin-bottom: 8mm;
            table-layout: fixed;
          }
          table th:nth-child(1) { width: 50%; } /* Description */
          table th:nth-child(2) { width: 15%; } /* Quantity */
          table th:nth-child(3) { width: 17.5%; } /* Unit Price */
          table th:nth-child(4) { width: 17.5%; } /* Total */
          thead {
            display: table-header-group;
          }
          tbody {
            display: table-row-group;
          }
          tr {
            page-break-inside: avoid;
            page-break-after: auto;
          }
          th, td {
            padding: 2mm 1mm;
            border-bottom: 0.5pt solid #ddd;
            text-align: left;
            word-wrap: break-word;
          }
          th {
            background-color: #f5f5f5 !important;
            font-weight: bold;
            font-size: 8pt;
          }
          td:nth-child(2), td:nth-child(3), td:nth-child(4) {
            text-align: right;
          }
          th:nth-child(2), th:nth-child(3), th:nth-child(4) {
            text-align: right;
          }
          .summary {
            width: 70mm;
            margin-left: auto;
            margin-bottom: 8mm;
            page-break-inside: avoid;
            text-align: right;
          }
          .summary p {
            font-size: 9pt;
            margin: 1.5mm 0;
            line-height: 1.2;
          }
          .summary p span {
            display: inline-block;
            width: 35mm;
            text-align: right;
          }
          .total {
            font-weight: bold;
            font-size: 10pt;
            color: #000 !important;
            border-top: 1pt solid #000;
            padding-top: 2mm;
          }
          .notes {
            page-break-inside: avoid;
            margin: 8mm 0;
          }
          .notes p {
            font-size: 8pt;
            line-height: 1.3;
            margin: 1mm 0;
          }
          .footer {
            margin-top: 10mm;
            font-size: 7pt;
            page-break-inside: avoid;
            text-align: center;
            color: #666 !important;
          }
          /* Ensure content fits on one page for standard invoices */
          .page-break {
            page-break-before: always;
          }
        }
      </style>
    </head>
    <body>
      <div class="header-container">
        <div class="logo-container">
          <img src="/images/logo.png" alt="Mocky Digital Logo" style="max-width: 120px; height: auto;">
        </div>

        <div class="invoice-center">
          <div class="invoice-title">
            <h1 style="margin: 10px 0;">INVOICE</h1>
          </div>

          <div class="invoice-details">
            <p style="margin: 5px 0;">Invoice #: <strong>${invoice.invoiceNumber}</strong></p>
            <p style="margin: 5px 0;">Date: ${new Date(invoice.issuedAt).toLocaleDateString()}</p>
            <p style="margin: 5px 0;">Due Date: ${new Date(invoice.dueDate).toLocaleDateString()}</p>
            <p style="margin: 5px 0;">Status: ${invoice.status.toUpperCase()}</p>
          </div>
        </div>

        <div class="company-info">
          <h2 style="margin-top: 0;">Mocky Digital</h2>
          <p style="margin: 5px 0;">Nairobi, Kenya</p>
          <p style="margin: 5px 0;">Phone: +*********** 670</p>
          <p style="margin: 5px 0;">Email: <EMAIL></p>
          <p style="margin: 5px 0;">Tax PIN: P052373324V</p>
        </div>
      </div>

        <div class="customer-info">
          <div class="section-title">CUSTOMER INFORMATION</div>
          <p>Name: ${invoice.customerName}</p>
          <p>Phone: ${invoice.phoneNumber}</p>
          ${invoice.email ? `<p>Email: ${invoice.email}</p>` : ''}
        </div>

        <div class="section-title">ITEMS</div>
        <table>
          <thead>
            <tr>
              <th>Description</th>
              <th>Quantity</th>
              <th>Unit Price (KES)</th>
              <th>Total (KES)</th>
            </tr>
          </thead>
          <tbody>
            ${invoice.items.map((item: any) => `
              <tr>
                <td>${item.description || item.serviceName || ''}</td>
                <td>${item.quantity}</td>
                <td>KES ${item.unitPrice.toLocaleString()}</td>
                <td>KES ${item.totalPrice.toLocaleString()}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>

        <div class="summary">
          <p>Total Amount: <span>KES ${invoice.totalAmount.toLocaleString()}</span></p>
          <p>Amount Paid: <span>KES ${invoice.amountPaid.toLocaleString()}</span></p>
          <p class="total">Balance Due: <span>KES ${invoice.balance.toLocaleString()}</span></p>
        </div>

        ${invoice.notes ? `
          <div class="notes">
            <div class="section-title">NOTES</div>
            <p>${invoice.notes}</p>
          </div>
        ` : ''}

        <div class="footer">
          <p>Thank you for your business!</p>
          <p>This is a computer-generated invoice and does not require a signature.</p>
        </div>

        <button class="print-button">Print Invoice</button>
      </div>

      <script>
        function printInvoice() {
          window.print();
        }

        document.addEventListener('DOMContentLoaded', function() {
          setTimeout(function() {
            // Add click event to print button
            const printButton = document.querySelector('.print-button');
            if (printButton) {
              printButton.addEventListener('click', function(e) {
                e.preventDefault();
                printInvoice();
              });
            }
          }, 500);
        });
      </script>
    </body>
    </html>
  `;
}

/**
 * GET /api/invoices/[id]
 * Generate and serve an invoice as PDF or HTML
 */
export async function GET(
  req: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    // Await params for NextJS 15 compatibility
    const params = await context.params;
    const id = params.id;
    console.log(`Invoice download requested for ID: ${id}`);

    if (!id || id === 'undefined' || id === 'null') {
      console.error('Invalid invoice ID provided:', id);
      return NextResponse.json({ error: 'Invalid invoice ID' }, { status: 400 });
    }

    // Get the invoice
    const invoice = await getInvoiceById(id);

    if (!invoice) {
      console.error(`Invoice not found: ${id}`);
      return NextResponse.json({ error: 'Invoice not found' }, { status: 404 });
    }

    // Check if the invoice has a PDF URL
    if (invoice.pdfUrl) {
      console.log(`Invoice has PDF URL: ${invoice.pdfUrl}`);

      try {
        // Get the PDF file path
        const pdfPath = path.join(process.cwd(), 'public', invoice.pdfUrl);

        // Check if the file exists
        try {
          await fs.access(pdfPath);
          console.log(`PDF file exists: ${pdfPath}`);
        } catch (accessError) {
          console.error(`PDF file does not exist: ${pdfPath}`);

          // Generate HTML invoice
          const html = generateInvoiceHtml(invoice);

          // Return the HTML content
          return new NextResponse(html, {
            headers: {
              'Content-Type': 'text/html',
            },
          });
        }

        // Generate HTML invoice instead of serving PDF
        console.log('Returning HTML invoice instead of PDF');
        const html = generateInvoiceHtml(invoice);

        // Return the HTML content
        return new NextResponse(html, {
          headers: {
            'Content-Type': 'text/html',
          },
        });
      } catch (error) {
        console.error(`Error serving invoice PDF: ${error}`);

        // Generate HTML invoice as fallback
        const html = generateInvoiceHtml(invoice);

        // Return the HTML content
        return new NextResponse(html, {
          headers: {
            'Content-Type': 'text/html',
          },
        });
      }
    } else {
      console.log(`Invoice does not have a PDF URL, will return HTML version`);

      // Generate HTML invoice
      const html = generateInvoiceHtml(invoice);

      // Return the HTML content
      return new NextResponse(html, {
        headers: {
          'Content-Type': 'text/html',
        },
      });
    }
  } catch (error) {
    console.error('Error generating invoice:', error);
    return NextResponse.json({ error: 'Failed to generate invoice' }, { status: 500 });
  }
}
