import { NextRequest, NextResponse } from 'next/server';
import { imageOptimizationService } from '@/lib/imageOptimization';
import { cacheService, CacheKeys, CacheTTL } from '@/lib/redis';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const url = searchParams.get('url');
    const width = searchParams.get('w') ? parseInt(searchParams.get('w')!) : undefined;
    const height = searchParams.get('h') ? parseInt(searchParams.get('h')!) : undefined;
    const quality = searchParams.get('q') ? parseInt(searchParams.get('q')!) : 80;
    const format = searchParams.get('f') as 'webp' | 'jpeg' | 'png' | 'avif' || 'webp';
    const fit = searchParams.get('fit') as 'cover' | 'contain' | 'fill' | 'inside' | 'outside' || 'cover';

    if (!url) {
      return NextResponse.json({ error: 'URL parameter is required' }, { status: 400 });
    }

    // Generate cache key
    const cacheKey = `${url}:${width || 'auto'}x${height || 'auto'}:${quality}:${format}:${fit}`;
    
    // Check cache first
    const cached = await imageOptimizationService.getCachedOptimizedImage(cacheKey);
    if (cached) {
      return new NextResponse(cached.buffer, {
        headers: {
          'Content-Type': `image/${cached.metadata.format}`,
          'Cache-Control': 'public, max-age=31536000, immutable',
          'X-Cache': 'HIT',
        },
      });
    }

    // Fetch the original image
    const imageResponse = await fetch(url);
    if (!imageResponse.ok) {
      return NextResponse.json({ error: 'Failed to fetch image' }, { status: 404 });
    }

    const imageBuffer = Buffer.from(await imageResponse.arrayBuffer());

    // Optimize the image
    const optimized = await imageOptimizationService.optimizeImage(imageBuffer, {
      width,
      height,
      quality,
      format,
      fit,
    });

    // Cache the optimized image
    await imageOptimizationService.cacheOptimizedImage(cacheKey, optimized.buffer, {
      format: optimized.format,
      width,
      quality,
    });

    return new NextResponse(optimized.buffer, {
      headers: {
        'Content-Type': `image/${optimized.format}`,
        'Cache-Control': 'public, max-age=31536000, immutable',
        'X-Cache': 'MISS',
        'X-Original-Size': imageBuffer.length.toString(),
        'X-Optimized-Size': optimized.size.toString(),
        'X-Compression-Ratio': ((1 - optimized.size / imageBuffer.length) * 100).toFixed(2) + '%',
      },
    });

  } catch (error) {
    console.error('Image optimization error:', error);
    return NextResponse.json(
      { error: 'Failed to optimize image' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('image') as File;
    const width = formData.get('width') ? parseInt(formData.get('width') as string) : undefined;
    const height = formData.get('height') ? parseInt(formData.get('height') as string) : undefined;
    const quality = formData.get('quality') ? parseInt(formData.get('quality') as string) : 80;
    const format = (formData.get('format') as 'webp' | 'jpeg' | 'png' | 'avif') || 'webp';
    const fit = (formData.get('fit') as 'cover' | 'contain' | 'fill' | 'inside' | 'outside') || 'cover';
    const generateResponsive = formData.get('responsive') === 'true';

    if (!file) {
      return NextResponse.json({ error: 'Image file is required' }, { status: 400 });
    }

    const buffer = Buffer.from(await file.arrayBuffer());

    if (generateResponsive) {
      // Generate multiple sizes for responsive images
      const responsiveImages = await imageOptimizationService.generateResponsiveImages(buffer);
      
      return NextResponse.json({
        success: true,
        images: responsiveImages.map(img => ({
          width: img.width,
          format: img.format,
          size: img.size,
          data: img.buffer.toString('base64'),
        })),
      });
    } else {
      // Generate single optimized image
      const optimized = await imageOptimizationService.optimizeImage(buffer, {
        width,
        height,
        quality,
        format,
        fit,
      });

      // Generate blur placeholder
      const blurPlaceholder = await imageOptimizationService.generateBlurPlaceholder(buffer);

      return NextResponse.json({
        success: true,
        image: {
          format: optimized.format,
          size: optimized.size,
          data: optimized.buffer.toString('base64'),
          blurPlaceholder,
          compressionRatio: ((1 - optimized.size / buffer.length) * 100).toFixed(2) + '%',
        },
      });
    }

  } catch (error) {
    console.error('Image optimization error:', error);
    return NextResponse.json(
      { error: 'Failed to optimize image' },
      { status: 500 }
    );
  }
}
