import { NextRequest, NextResponse } from 'next/server';
import { auth } from '../../../../../auth';
import prisma from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Not authenticated',
          user: null
        },
        { status: 401 }
      );
    }

    // Get complete user data from database
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      include: {
        role: {
          select: {
            id: true,
            name: true,
            permissions: true
          }
        }
      }
    });

    if (!user) {
      return NextResponse.json(
        { 
          success: false,
          error: 'User not found',
          user: null
        },
        { status: 404 }
      );
    }

    // Return user data in the format expected by useAuth hook
    return NextResponse.json({
      success: true,
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        name: user.name,
        role: user.role?.name || 'user',
        permissions: user.role?.permissions || [],
        lastLogin: user.lastLogin,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
        active: user.active,
        roleId: user.roleId,
        roleObject: user.role // Add the full role object for useAuth hook
      }
    });

  } catch (error) {
    console.error('Error in /api/auth/me:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Internal server error',
        user: null
      },
      { status: 500 }
    );
  }
} 