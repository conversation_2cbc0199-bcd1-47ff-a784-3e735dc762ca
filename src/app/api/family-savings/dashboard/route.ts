import { NextRequest, NextResponse } from 'next/server';
import { familySavingsService } from '@/services/familySavingsService';

export async function GET(request: NextRequest) {
  try {
    const stats = await familySavingsService.getDashboardStats();
    
    return NextResponse.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch dashboard statistics' 
      },
      { status: 500 }
    );
  }
} 