import { NextRequest, NextResponse } from 'next/server';
import { familySavingsService } from '@/services/familySavingsService';
import { CreateTransactionData, TransactionFilters } from '@/types/family-savings';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Parse filters from query parameters
    const filters: TransactionFilters = {};
    
    if (searchParams.get('status')) {
      filters.status = searchParams.get('status') as 'PENDING' | 'COMPLETED' | 'FAILED';
    }
    
    if (searchParams.get('transactionType')) {
      filters.transactionType = searchParams.get('transactionType') as 'DEPOSIT' | 'WITHDRAWAL' | 'TRANSFER';
    }
    
    if (searchParams.get('userId')) {
      filters.userId = searchParams.get('userId')!;
    }
    
    if (searchParams.get('startDate')) {
      filters.startDate = new Date(searchParams.get('startDate')!);
    }
    
    if (searchParams.get('endDate')) {
      filters.endDate = new Date(searchParams.get('endDate')!);
    }

    const transactions = await familySavingsService.getTransactions(filters);
    const stats = await familySavingsService.getTransactionStats();
    
    return NextResponse.json({
      success: true,
      data: {
        transactions,
        stats
      }
    });
  } catch (error) {
    console.error('Error fetching transactions:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch transactions' 
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { userId, ...transactionData }: { userId: string } & CreateTransactionData = body;

    if (!userId) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'User ID is required' 
        },
        { status: 400 }
      );
    }

    // Validate transaction data
    if (!transactionData.amount || transactionData.amount <= 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Valid amount is required' 
        },
        { status: 400 }
      );
    }

    if (!transactionData.transactionType) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Transaction type is required' 
        },
        { status: 400 }
      );
    }

    const transaction = await familySavingsService.createTransaction(userId, transactionData);
    
    return NextResponse.json({
      success: true,
      data: transaction
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating transaction:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to create transaction' 
      },
      { status: 500 }
    );
  }
} 