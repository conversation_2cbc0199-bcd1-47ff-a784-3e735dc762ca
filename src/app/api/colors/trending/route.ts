import { NextRequest, NextResponse } from 'next/server';
import { scrapeTrendingPalettes, generateFallbackPalettes, ColorPalette } from '@/services/colorScrapingService';

// Cache for color palettes
let cachedPalettes: ColorPalette[] | null = null;
let cacheTime = 0;
const CACHE_DURATION = 1000 * 60 * 60 * 6; // 6 hours

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const forceRefresh = searchParams.get('refresh') === 'true';
    
    // Check if we have valid cached data and no force refresh
    const now = Date.now();
    if (!forceRefresh && cachedPalettes && now - cacheTime < CACHE_DURATION) {
      console.log('Returning cached color palettes');
      return NextResponse.json({
        success: true,
        palettes: cachedPalettes,
        cached: true,
        timestamp: new Date(cacheTime)
      }, {
        headers: {
          'Cache-Control': 'public, max-age=3600, s-maxage=7200',
        },
      });
    }

    console.log('Fetching fresh color palettes...');
    
    // Try to scrape fresh data
    const scrapingResult = await scrapeTrendingPalettes();
    
    if (scrapingResult.success && scrapingResult.palettes.length > 0) {
      // Update cache with fresh data
      cachedPalettes = scrapingResult.palettes;
      cacheTime = now;
      
      console.log(`Successfully fetched ${scrapingResult.palettes.length} fresh palettes`);
      
      return NextResponse.json({
        success: true,
        palettes: scrapingResult.palettes,
        cached: false,
        timestamp: scrapingResult.timestamp
      }, {
        headers: {
          'Cache-Control': 'public, max-age=3600, s-maxage=7200',
        },
      });
    } else {
      // Scraping failed, use fallback or cached data
      console.log('Scraping failed, using fallback palettes');
      
      const fallbackPalettes = generateFallbackPalettes();
      
      // If we have cached data, prefer it over fallback
      const palettesToReturn = cachedPalettes && cachedPalettes.length > 0 
        ? cachedPalettes 
        : fallbackPalettes;
      
      return NextResponse.json({
        success: true,
        palettes: palettesToReturn,
        cached: cachedPalettes && cachedPalettes.length > 0,
        fallback: !cachedPalettes || cachedPalettes.length === 0,
        error: scrapingResult.error,
        timestamp: new Date()
      }, {
        headers: {
          'Cache-Control': 'public, max-age=1800, s-maxage=3600', // Shorter cache for fallback
        },
      });
    }
    
  } catch (error) {
    console.error('Error in colors API route:', error);
    
    // Return fallback palettes on any error
    const fallbackPalettes = generateFallbackPalettes();
    
    return NextResponse.json({
      success: false,
      palettes: fallbackPalettes,
      fallback: true,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
      timestamp: new Date()
    }, {
      status: 500,
      headers: {
        'Cache-Control': 'public, max-age=900, s-maxage=1800', // Very short cache for errors
      },
    });
  }
}

export async function POST(request: NextRequest) {
  try {
    // Manual refresh endpoint for admin use
    console.log('Manual refresh of color palettes requested');
    
    const scrapingResult = await scrapeTrendingPalettes();
    
    if (scrapingResult.success) {
      // Update cache
      cachedPalettes = scrapingResult.palettes;
      cacheTime = Date.now();
      
      return NextResponse.json({
        success: true,
        message: `Successfully refreshed ${scrapingResult.palettes.length} color palettes`,
        palettes: scrapingResult.palettes,
        timestamp: scrapingResult.timestamp
      });
    } else {
      return NextResponse.json({
        success: false,
        message: 'Failed to refresh color palettes',
        error: scrapingResult.error,
        timestamp: scrapingResult.timestamp
      }, { status: 500 });
    }
    
  } catch (error) {
    console.error('Error in manual refresh:', error);
    
    return NextResponse.json({
      success: false,
      message: 'Error during manual refresh',
      error: error instanceof Error ? error.message : 'Unknown error occurred',
      timestamp: new Date()
    }, { status: 500 });
  }
}
