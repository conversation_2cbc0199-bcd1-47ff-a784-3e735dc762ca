import { NextRequest, NextResponse } from 'next/server';
import { requireAdminAuth } from '@/lib/auth-helpers';
import { successResponse, errorResponse, handleAPIError } from '@/utils/apiResponse';
import { logger } from '@/utils/logger';
import { withTransaction } from '@/utils/withTransaction';
import { ValidationError } from '@/errors/ApiErrors';
import { getQuoteById } from '@/services/quoteService';
import path from 'path';
import fs from 'fs/promises';

/**
 * Generate HTML for a quote
 * @param quote The quote data
 * @returns HTML string
 */
function generateQuoteHtml(quote: any): string {
  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Quote #${quote.quoteNumber}</title>
      <style>
        @page {
          size: A4;
          margin: 8mm 6mm;
        }
        
        @media print {
          body { margin: 0; padding: 0; }
          .print-button { display: none !important; }
        }
        
        body {
          font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
          line-height: 1.3;
          color: #333;
          font-size: 9pt;
          margin: 0;
          padding: 0;
          background: white;
        }
        
        .quote-container {
          max-width: 190mm;
          margin: 0 auto;
          padding: 4mm;
          background: white;
          min-height: calc(297mm - 16mm);
        }
        
        .header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 8mm;
          padding-bottom: 3mm;
          border-bottom: 1pt solid #0F2557;
        }
        
        .logo-container img {
          max-height: 25mm;
          max-width: 50mm;
          object-fit: contain;
        }
        
        .quote-info {
          text-align: right;
          flex-shrink: 0;
        }
        
        .quote-info h1 {
          font-size: 18pt;
          font-weight: bold;
          color: #0F2557;
          margin: 0 0 2mm 0;
        }
        
        .quote-info p {
          margin: 1mm 0;
          font-size: 8pt;
        }
        
        .client-info {
          margin-bottom: 4mm;
        }
        
        .client-info h3 {
          font-size: 10pt;
          font-weight: bold;
          color: #0F2557;
          margin: 0 0 2mm 0;
          border-bottom: 0.5pt solid #ddd;
          padding-bottom: 1mm;
        }
        
        .client-info p {
          margin: 0.5mm 0;
          font-size: 8pt;
          line-height: 1.2;
        }
        
        .items-table {
          width: 100%;
          border-collapse: collapse;
          margin: 4mm 0;
          font-size: 8pt;
        }
        
        .items-table th {
          background-color: #0F2557 !important;
          color: white !important;
          padding: 2mm;
          font-weight: bold;
          font-size: 8pt;
          text-align: left;
          border: none;
        }
        
        .items-table td {
          padding: 1.5mm 2mm;
          border-bottom: 0.5pt solid #eee;
          vertical-align: top;
          font-size: 8pt;
        }
        
        .items-table tbody tr:nth-child(even) {
          background-color: #f9f9f9 !important;
        }
        
        .summary {
          width: 100%;
          border-collapse: collapse;
          font-size: 8pt;
        }
        
        .summary td {
          padding: 1mm 2mm;
          text-align: right;
          font-size: 8pt;
        }
        
        .summary td:first-child {
          text-align: left;
        }
        
        .notes {
          margin: 3mm 0;
          padding: 2mm;
          background-color: #f8f9fa !important;
          border-radius: 2mm;
          page-break-inside: avoid;
        }
        
        .notes h3 {
          font-size: 9pt;
          font-weight: bold;
          color: #0F2557;
          margin: 0 0 1mm 0;
        }
        
        .notes p {
          font-size: 7pt;
          line-height: 1.2;
          margin: 0.5mm 0;
        }
        
        .notes div {
          font-size: 7pt;
          line-height: 1.2;
        }
        
        .footer {
          margin-top: 4mm;
          font-size: 6pt;
          page-break-inside: avoid;
          text-align: center;
          color: #666 !important;
          border-top: 0.5pt solid #eee;
          padding-top: 2mm;
        }
        
        .footer p {
          margin: 0.5mm 0;
          font-size: 6pt;
        }
        
        .print-button {
          position: fixed;
          bottom: 10mm;
          right: 10mm;
          background-color: #0F2557;
          color: white;
          border: none;
          padding: 3mm 6mm;
          border-radius: 2mm;
          cursor: pointer;
          font-size: 8pt;
          box-shadow: 0 1mm 3mm rgba(0,0,0,0.2);
          z-index: 1000;
        }
        
        .print-button:hover {
          background-color: #1a3a7a;
        }
        
        /* Compact layouts for sections */
        .company-section {
          display: flex;
          justify-content: space-between;
          margin-bottom: 4mm;
          gap: 4mm;
        }
        
        .company-section > div {
          flex: 1;
        }
        
        .summary-section {
          display: flex;
          justify-content: flex-end;
          margin: 3mm 0;
        }
        
        .summary-table {
          width: 60mm;
        }
        
        .payment-grid {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 3mm;
          font-size: 7pt;
        }
        
        .terms-list {
          font-size: 6pt;
          line-height: 1.2;
          margin: 1mm 0;
        }
        
        .terms-list p {
          margin: 0.5mm 0;
        }
        
        /* Responsive adjustments for smaller content */
        @media print {
          .quote-container {
            padding: 2mm;
          }
          
          .header {
            margin-bottom: 4mm;
          }
          
          .client-info {
            margin-bottom: 2mm;
          }
          
          .notes {
            margin: 2mm 0;
            padding: 1.5mm;
          }
          
          .footer {
            margin-top: 2mm;
            padding-top: 1mm;
          }
        }
      </style>
    </head>
    <body>
      <div class="quote-container">
        <div class="header">
          <div class="logo-container">
            <img src="/images/logo.png" alt="Mocky Digital Logo" onerror="this.style.display='none'">
          </div>
          <div class="quote-info">
            <h1>QUOTE</h1>
            <p><strong>Quote #:</strong> ${quote.quoteNumber}</p>
            <p><strong>Date:</strong> ${new Date(quote.createdAt).toLocaleDateString()}</p>
            <p><strong>Valid Until:</strong> ${new Date(quote.validUntil).toLocaleDateString()}</p>
            <p><strong>Status:</strong> ${quote.status ? quote.status.toUpperCase() : 'PENDING'}</p>
          </div>
        </div>

        <!-- Company Information Section -->
        <div class="company-section">
          <div>
            <div class="client-info">
              <h3>Quote For:</h3>
              ${quote.client?.firstName || quote.client?.lastName ? 
                `<p><strong>${quote.client?.firstName || ''} ${quote.client?.lastName || ''}</strong></p>` : 
                `<p><strong>Client Name:</strong> ${quote.clientName || 'Not specified'}</p>`
              }
              ${quote.client?.company ? `<p><strong>Company:</strong> ${quote.client.company}</p>` : ''}
              <p><strong>Email:</strong> ${quote.client?.email || quote.email || 'Not provided'}</p>
              ${quote.client?.phone || quote.phone ? `<p><strong>Phone:</strong> ${quote.client?.phone || quote.phone}</p>` : ''}
              ${quote.client?.address ? `<p><strong>Address:</strong> ${quote.client.address}</p>` : ''}
            </div>
          </div>
          
          <div>
            <div class="client-info">
              <h3>From:</h3>
              <p><strong>Mocky Digital</strong></p>
              <p>Professional Printing & Design Services</p>
              <p>Nairobi, Kenya | +*********** 670</p>
              <p><EMAIL> | www.mocky.co.ke</p>
              <p><strong>Tax PIN:</strong> P052373324V</p>
            </div>
          </div>
        </div>

        <!-- Services/Items Section -->
        <h3 style="color: #0F2557; margin: 2mm 0 1mm 0; font-size: 9pt;">Services & Items</h3>

        <table class="items-table">
          <thead>
            <tr>
              <th style="width: 50%;">Description</th>
              <th style="width: 15%; text-align: center;">Qty</th>
              <th style="width: 17.5%; text-align: right;">Unit Price</th>
              <th style="width: 17.5%; text-align: right;">Total</th>
            </tr>
          </thead>
          <tbody>
            ${quote.items && quote.items.length > 0 ? quote.items.map((item: any) => `
              <tr>
                <td>
                  <strong>${item.description || item.serviceName || item.name || 'Service'}</strong>
                  ${item.specifications ? `<br><small style="color: #666; font-size: 7pt;">${item.specifications}</small>` : ''}
                </td>
                <td style="text-align: center;">${item.quantity || 1}</td>
                <td style="text-align: right;">KES ${(item.unitPrice || item.price || 0).toLocaleString()}</td>
                <td style="text-align: right;"><strong>KES ${(item.totalPrice || item.total || (item.quantity || 1) * (item.unitPrice || item.price || 0)).toLocaleString()}</strong></td>
              </tr>
            `).join('') : `
              <tr>
                <td colspan="4" style="text-align: center; color: #666; font-style: italic;">No items specified</td>
              </tr>
            `}
          </tbody>
        </table>

        <!-- Summary Section -->
        <div class="summary-section">
          <table class="summary summary-table">
            ${quote.subtotal ? `
              <tr>
                <td><strong>Subtotal:</strong></td>
                <td><strong>KES ${quote.subtotal.toLocaleString()}</strong></td>
              </tr>
            ` : ''}
            ${quote.discount && quote.discount > 0 ? `
              <tr>
                <td><strong>Discount:</strong></td>
                <td><strong>- KES ${quote.discount.toLocaleString()}</strong></td>
              </tr>
            ` : ''}
            ${quote.tax && quote.tax > 0 ? `
              <tr>
                <td><strong>Tax (16% VAT):</strong></td>
                <td><strong>KES ${quote.tax.toLocaleString()}</strong></td>
              </tr>
            ` : ''}
            <tr style="border-top: 1pt solid #0F2557;">
              <td><strong style="font-size: 9pt;">Total Amount:</strong></td>
              <td><strong style="font-size: 9pt; color: #0F2557;">KES ${(quote.totalAmount || 0).toLocaleString()}</strong></td>
            </tr>
          </table>
        </div>

        <!-- Payment Information -->
        <div class="notes" style="background-color: #e8f4f8; border-left: 2mm solid #0F2557;">
          <h3 style="color: #0F2557;">Payment Information</h3>
          <div class="payment-grid">
            <div>
              <p><strong>Payment Terms:</strong> ${quote.paymentTerms || '50% deposit required'}</p>
              <p><strong>Delivery:</strong> ${quote.deliveryTimeline || '3-5 business days'}</p>
              <p><strong>Valid Until:</strong> ${new Date(quote.validUntil).toLocaleDateString()}</p>
            </div>
            <div>
              <p><strong>M-Pesa Payment:</strong></p>
              <p>Business: <strong>522533</strong> | Account: <strong>7934479</strong></p>
              <p>Amount: <strong>KES ${(quote.totalAmount || 0).toLocaleString()}</strong></p>
            </div>
          </div>
        </div>

        ${quote.notes || quote.description ? `
          <div class="notes">
            <h3>Additional Notes</h3>
            ${quote.description ? `<p><strong>Description:</strong> ${quote.description}</p>` : ''}
            ${quote.notes ? `<p><strong>Instructions:</strong> ${quote.notes}</p>` : ''}
          </div>
        ` : ''}

        <!-- Terms and Conditions -->
        <div class="notes" style="background-color: #f8f9fa;">
          <h3>Terms & Conditions</h3>
          <div class="terms-list">
            <p>• Designs remain Mocky Digital property until full payment • Client provides content in digital format</p>
            <p>• 3 revision rounds included, additional revisions may incur charges • Rush orders (< 48hrs) incur 50% surcharge</p>
            <p>• Delivery timelines start after deposit payment and content approval • Prices valid for 30 days</p>
          </div>
        </div>

        <div class="footer">
          <p><strong>Mocky Digital</strong> | +*********** 670 | <EMAIL> | www.mocky.co.ke | @mockydigital</p>
          <p style="margin-top: 1mm;">Thank you for choosing Mocky Digital - Your trusted partner for professional printing and design services</p>
        </div>

        <button class="print-button" onclick="window.print()">
          Print Quote
        </button>
      </div>

      <script>
        // Auto-trigger print dialog when the page loads (optional)
        window.addEventListener('load', function() {
          // Uncomment the line below to auto-print when page loads
          // setTimeout(() => window.print(), 1000);
        });
      </script>
    </body>
    </html>
  `;
}

/**
 * GET /api/quotes/[id]
 * Generate and serve a quote as HTML with enterprise patterns
 */
async function getQuote(
  request: NextRequest,
  session: any,
  params: Promise<{ id: string }>
) {
  const correlationId = request.headers.get('x-correlation-id') || 'unknown';
  const startTime = Date.now();
  
  try {
    const resolvedParams = await params;
    const { id } = resolvedParams;

    logger.info('Quote retrieval started', {
      requestId: correlationId,
      action: 'get_quote',
      userId: session?.user?.id || 'anonymous',
      metadata: { quoteId: id }
    });

    // Validate quote ID - cuid format (25 characters starting with 'c')
    if (!id || id === 'undefined' || id === 'null') {
      logger.warn('Quote ID validation failed: missing or invalid value', {
        requestId: correlationId,
        action: 'quote_validation',
        metadata: { 
          providedId: id,
          type: typeof id
        }
      });
      throw new ValidationError('Invalid quote ID provided', 'INVALID_ID_FORMAT');
    }
    
    // Validate cuid format (should be 25 characters starting with 'c')
    const cuidPattern = /^c[a-z0-9]{24}$/;
    if (!cuidPattern.test(id)) {
      logger.warn('Quote ID validation failed: invalid cuid format', {
        requestId: correlationId,
        action: 'quote_validation',
        metadata: {
          providedId: id,
          length: id.length,
          startsWithC: id.startsWith('c'),
          expectedPattern: 'cuid format (25 chars starting with c)'
        }
      });
      throw new ValidationError('Invalid quote ID format', 'INVALID_ID_FORMAT');
    }

    // Get the quote with transaction safety
    const quote = await withTransaction(async (tx) => {
      return await getQuoteById(id);
    });

    if (!quote) {
      throw new ValidationError('Quote not found', 'RESOURCE_NOT_FOUND');
    }

    const duration = Date.now() - startTime;

    logger.business('Quote retrieved successfully', {
      requestId: correlationId,
      userId: session?.user?.id || 'anonymous',
      duration,
      metadata: {
        quoteId: id,
        quoteNumber: quote.quoteNumber,
        clientId: (quote as any).client?.id || (quote as any).clientId,
        totalAmount: quote.totalAmount
      }
    });

    // Check if the quote has a PDF URL
    if (quote.pdfUrl) {
      try {
        // Get the PDF file path
        const pdfPath = path.join(process.cwd(), 'public', quote.pdfUrl);

        // Check if the file exists
        try {
          await fs.access(pdfPath);
          logger.info('PDF file exists, serving HTML instead', {
            requestId: correlationId,
            action: 'quote_pdf_check',
            metadata: { pdfPath: quote.pdfUrl }
          });
        } catch (accessError) {
          logger.warn('PDF file does not exist, generating HTML', {
            requestId: correlationId,
            action: 'quote_pdf_check',
            metadata: { pdfPath: quote.pdfUrl }
          });
        }

        // Generate HTML quote (preferred over PDF for better UX)
        const html = generateQuoteHtml(quote);

        return new NextResponse(html, {
          headers: {
            'Content-Type': 'text/html',
            'X-Request-ID': correlationId
          },
        });
      } catch (error) {
        logger.error('Error accessing PDF file', error as Error, {
          requestId: correlationId,
          action: 'quote_pdf_error',
          metadata: { quoteId: id }
        });
        
        // Fall back to HTML generation
        const html = generateQuoteHtml(quote);
        return new NextResponse(html, {
          headers: {
            'Content-Type': 'text/html',
            'X-Request-ID': correlationId
          },
        });
      }
    } else {
      logger.info('Quote does not have PDF URL, generating HTML', {
        requestId: correlationId,
        action: 'quote_html_generation',
        metadata: { quoteId: id }
      });

      // Generate HTML quote
      const html = generateQuoteHtml(quote);

      return new NextResponse(html, {
        headers: {
          'Content-Type': 'text/html',
          'X-Request-ID': correlationId
        },
      });
    }
  } catch (error) {
    const duration = Date.now() - startTime;
    
    logger.error('Failed to retrieve quote', error as Error, {
      requestId: correlationId,
      action: 'get_quote_error',
      userId: session?.user?.id || 'anonymous',
      duration
    });

    return handleAPIError(error);
  }
}

// Export handler with enterprise patterns (public access for quote viewing)
export const GET = async (
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) => {
  // Quotes can be viewed publicly, so no auth required
  return getQuote(request, null, context.params);
};
