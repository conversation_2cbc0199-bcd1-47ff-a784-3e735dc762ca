import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { getS3ImageUrl } from '@/utils/imageUtils';
import { requireAdminAuth } from '@/lib/auth-helpers';

export const dynamic = 'force-dynamic';
export const revalidate = 0; // Disable cache for now

export async function GET() {
  try {
    const teamMembers = await prisma.teamMember.findMany({
      orderBy: { order: 'asc' }
    });
    
    // Transform database team members to frontend format with proper S3 URLs
    const transformedMembers = teamMembers.map(member => ({
      id: member.id,
      name: member.name,
      role: member.role,
      bio: member.bio,
      imageSrc: member.imageKey ? getS3ImageUrl(member.imageKey) : '/images/placeholder.svg', // Use S3 URL utility
      order: member.order,
      linkedinUrl: member.linkedinUrl,
      twitterUrl: member.twitterUrl,
      githubUrl: member.githubUrl,
      emailAddress: member.emailAddress,
      createdAt: member.createdAt.toISOString(),
      updatedAt: member.updatedAt.toISOString()
    }));
    
    console.log(`[Public Team API] Retrieved ${transformedMembers.length} team members`);
    
    return NextResponse.json(transformedMembers);
  } catch (error) {
    console.error('Error in GET /api/team:', error);
    return NextResponse.json(
      { error: 'Failed to fetch team members' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;

    console.log(`[Team API] POST request from user: ${user!.username}`);
    
    const body = await request.json();
    const { name, role, email, phone, bio, imageUrl, linkedin, portfolio, github, skills } = body;

    if (!name || !role || !email) {
      console.log('[Team API] Missing required fields');
      return NextResponse.json({ 
        error: 'Missing required fields: name, role, email' 
      }, { status: 400 });
    }

    const teamMember = await prisma.teamMember.create({
      data: {
        name,
        role,
        email,
        phone,
        bio,
        imageUrl,
        linkedin,
        portfolio,
        github,
        skills: skills || []
      }
    });

    console.log(`[Team API] Created team member: ${teamMember.id}`);
    return NextResponse.json(teamMember);
  } catch (error) {
    console.error('[Team API] Error creating team member:', error);
    return NextResponse.json({ 
      error: 'Failed to create team member' 
    }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;

    console.log(`[Team API] PUT request from user: ${user!.username}`);
    
    const body = await request.json();
    const { id, name, role, email, phone, bio, imageUrl, linkedin, portfolio, github, skills } = body;

    if (!id) {
      console.log('[Team API] Missing team member ID');
      return NextResponse.json({ 
        error: 'Team member ID is required' 
      }, { status: 400 });
    }

    const teamMember = await prisma.teamMember.update({
      where: { id },
      data: {
        name,
        role,
        email,
        phone,
        bio,
        imageUrl,
        linkedin,
        portfolio,
        github,
        skills: skills || []
      }
    });

    console.log(`[Team API] Updated team member: ${teamMember.id}`);
    return NextResponse.json(teamMember);
  } catch (error) {
    console.error('[Team API] Error updating team member:', error);
    return NextResponse.json({ 
      error: 'Failed to update team member' 
    }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;

    console.log(`[Team API] DELETE request from user: ${user!.username}`);
    
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      console.log('[Team API] Missing team member ID');
      return NextResponse.json({ 
        error: 'Team member ID is required' 
      }, { status: 400 });
    }

    await prisma.teamMember.delete({
      where: { id }
    });

    console.log(`[Team API] Deleted team member: ${id}`);
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('[Team API] Error deleting team member:', error);
    return NextResponse.json({ 
      error: 'Failed to delete team member' 
    }, { status: 500 });
  }
}
