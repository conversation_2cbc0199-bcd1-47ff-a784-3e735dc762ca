import { NextRequest, NextResponse } from 'next/server';
import { CatalogueCRUDService } from '@/services/catalogueCRUDService';

export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category') || undefined;
    const search = searchParams.get('search') || undefined;
    const minPrice = searchParams.get('minPrice') ? Number(searchParams.get('minPrice')) : undefined;
    const maxPrice = searchParams.get('maxPrice') ? Number(searchParams.get('maxPrice')) : undefined;
    const popular = searchParams.get('popular') ? searchParams.get('popular') === 'true' : undefined;
    const limit = Math.min(Number(searchParams.get('limit')) || 50, 100);
    const offset = Math.max(Number(searchParams.get('offset')) || 0, 0);

    console.log('Public API: Fetching catalogue items from database...');

    // Use the advanced CRUD service for better performance
    const result = await CatalogueCRUDService.findMany({
      category,
      search,
      minPrice,
      maxPrice,
      popular,
      limit,
      offset,
      sortBy: 'service',
      sortOrder: 'asc'
    });

    console.log(`Public API: Retrieved ${result.items.length} of ${result.total} catalogue items`);

    // Filter by category if provided (additional client-side filter for compatibility)
    let filteredItems = result.items;
    if (category) {
      console.log(`Public API: Filtering by category: ${category}`);
      filteredItems = result.items.filter(item =>
        (item.category || 'Other') === category
      );
      console.log(`Public API: Found ${filteredItems.length} items in category "${category}"`);
    }

    // Add cache control headers - shorter cache time to ensure fresh data
    return NextResponse.json(filteredItems, {
      headers: {
        'Cache-Control': 'public, max-age=300, s-maxage=600', // 5 min client, 10 min CDN
        'X-Total-Count': result.total.toString(),
        'X-Has-More': (offset + filteredItems.length < result.total).toString()
      }
    });
  } catch (error) {
    console.error('Error fetching catalogue items:', error);
    return new NextResponse(JSON.stringify({
      error: 'Internal Server Error',
      message: error instanceof Error ? error.message : 'Unknown error'
    }), {
      status: 500,
      headers: {
        'Content-Type': 'application/json'
      }
    });
  }
}
