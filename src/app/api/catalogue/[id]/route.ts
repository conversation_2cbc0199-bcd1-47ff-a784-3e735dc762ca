import { NextRequest, NextResponse } from 'next/server';
import { CatalogueCRUDService } from '@/services/catalogueCRUDService';

// GET handler - Get a specific catalogue item by ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  let id: string = 'unknown';
  try {
    const resolvedParams = await params;
    id = resolvedParams.id;
    console.log(`Public API: Fetching catalogue item with ID: ${id}`);

    // Get the catalogue item using advanced CRUD service
    const catalogueItem = await CatalogueCRUDService.findById(id);

    if (!catalogueItem) {
      console.log(`Catalogue item not found: ${id}`);
      return NextResponse.json(
        { error: 'Catalogue item not found' },
        { status: 404 }
      );
    }

    console.log(`Successfully fetched catalogue item: ${catalogueItem.service}`);

    return NextResponse.json(catalogueItem, {
      headers: {
        'Cache-Control': 'public, max-age=300, s-maxage=600', // 5 min client, 10 min CDN
        'ETag': `"${catalogueItem.id}-${catalogueItem.updatedAt}"`
      },
    });
  } catch (error) {
    console.error(`Error fetching catalogue item ${id}:`, error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}
