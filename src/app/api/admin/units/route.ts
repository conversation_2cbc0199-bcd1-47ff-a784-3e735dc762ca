import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

// GET /api/admin/units - Fetch all units
export async function GET() {
  try {
    const units = await prisma.unit.findMany({
      where: { active: true },
      orderBy: [
        { order: 'asc' },
        { displayName: 'asc' }
      ]
    });

    return NextResponse.json(units);
  } catch (error) {
    console.error('Error fetching units:', error);
    return NextResponse.json(
      { error: 'Failed to fetch units' },
      { status: 500 }
    );
  }
}

// POST /api/admin/units - Create a new unit
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, displayName, plural, shortForm, category } = body;

    if (!name || !displayName || !plural) {
      return NextResponse.json(
        { error: 'Name, display name, and plural are required' },
        { status: 400 }
      );
    }

    const unit = await prisma.unit.create({
      data: {
        name: name.toLowerCase().replace(/\s+/g, '_'),
        displayName,
        plural,
        shortForm,
        category: category || 'general'
      }
    });

    return NextResponse.json(unit, { status: 201 });
  } catch (error) {
    console.error('Error creating unit:', error);
    return NextResponse.json(
      { error: 'Failed to create unit' },
      { status: 500 }
    );
  }
}

// PUT /api/admin/units - Update a unit
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { id, name, displayName, plural, shortForm, category, active } = body;

    if (!id) {
      return NextResponse.json(
        { error: 'Unit ID is required' },
        { status: 400 }
      );
    }

    const unit = await prisma.unit.update({
      where: { id },
      data: {
        ...(name && { name: name.toLowerCase().replace(/\s+/g, '_') }),
        ...(displayName && { displayName }),
        ...(plural && { plural }),
        ...(shortForm !== undefined && { shortForm }),
        ...(category && { category }),
        ...(active !== undefined && { active })
      }
    });

    return NextResponse.json(unit);
  } catch (error) {
    console.error('Error updating unit:', error);
    return NextResponse.json(
      { error: 'Failed to update unit' },
      { status: 500 }
    );
  }
} 