import { auth } from "../../../../../auth";;
import { requireAdminAuth } from '@/lib/auth-helpers';;
import { NextRequest, NextResponse } from 'next/server';

import { prisma } from '@/lib/prisma';

// GET handler - Health check
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    console.log(`[Health API] GET request from user: ${user.email}`);
    
    const healthChecks: any = {
      timestamp: new Date().toISOString(),
      status: 'healthy',
      checks: {
        database: { status: 'unknown', responseTime: 0 },
        api: { status: 'healthy', responseTime: 0 },
        storage: { status: 'unknown', responseTime: 0 },
        memory: { status: 'healthy', usage: 0 },
      },
      uptime: process.uptime(),
      version: process.env.npm_package_version || '1.0.0',
    };

    const startTime = Date.now();

    // Database health check
    try {
      const dbStart = Date.now();
      await prisma.$queryRaw`SELECT 1`;
      const dbTime = Date.now() - dbStart;
      
      healthChecks.checks.database = {
        status: 'healthy',
        responseTime: dbTime,
        message: 'Database connection successful'
      };
    } catch (dbError) {
      healthChecks.checks.database = {
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        error: dbError instanceof Error ? dbError.message : 'Database connection failed'
      };
      healthChecks.status = 'degraded';
    }

    // API health check
    healthChecks.checks.api = {
      status: 'healthy',
      responseTime: Date.now() - startTime,
      message: 'API is responding'
    };

    // Storage health check (placeholder)
    try {
      // In a real implementation, this would check S3/storage connectivity
      healthChecks.checks.storage = {
        status: 'healthy',
        responseTime: 5, // Simulated
        message: 'Storage is accessible'
      };
    } catch (storageError) {
      healthChecks.checks.storage = {
        status: 'unhealthy',
        responseTime: 0,
        error: 'Storage check failed'
      };
      healthChecks.status = 'degraded';
    }

    // Memory usage check
    try {
      const memUsage = process.memoryUsage();
      const memUsagePercent = (memUsage.heapUsed / memUsage.heapTotal) * 100;
      
      healthChecks.checks.memory = {
        status: memUsagePercent > 90 ? 'unhealthy' : memUsagePercent > 70 ? 'warning' : 'healthy',
        usage: Math.round(memUsagePercent),
        details: {
          heapUsed: `${Math.round(memUsage.heapUsed / 1024 / 1024)}MB`,
          heapTotal: `${Math.round(memUsage.heapTotal / 1024 / 1024)}MB`,
          external: `${Math.round(memUsage.external / 1024 / 1024)}MB`,
        }
      };

      if (memUsagePercent > 90) {
        healthChecks.status = 'unhealthy';
      } else if (memUsagePercent > 70 && healthChecks.status === 'healthy') {
        healthChecks.status = 'warning';
      }
    } catch (memError) {
      healthChecks.checks.memory = {
        status: 'unknown',
        usage: 0,
        error: 'Memory check failed'
      };
    }

    // Overall response time
    healthChecks.totalResponseTime = Date.now() - startTime;

    console.log(`[Health API] Health check completed in ${healthChecks.totalResponseTime}ms by user: ${user.email}`);

    // Set appropriate status code based on health
    const statusCode = healthChecks.status === 'healthy' ? 200 : 
                      healthChecks.status === 'warning' ? 200 : 503;

    return NextResponse.json(healthChecks, { 
      status: statusCode,
      headers: {
        'Cache-Control': 'no-store, max-age=0',
        'Pragma': 'no-cache',
      }
    });
    
  } catch (error) {
    console.error('Error in health check API:', error);
    return NextResponse.json({
      timestamp: new Date().toISOString(),
      status: 'unhealthy',
      error: 'Health check failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
} 
