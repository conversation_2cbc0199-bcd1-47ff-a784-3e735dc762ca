import { auth } from "../../../../../../auth";
import { NextRequest, NextResponse } from 'next/server';

import { requireAdminAuth } from '@/lib/auth-helpers';
import {
  performRedisHealthCheck,
  getRedisHealth,
  getRedisMonitoringStats,
  startRedisMonitoring,
  stopRedisMonitoring,
  redisMonitoring
} from '@/services/redisMonitoring';

async function handleHealthMonitoring(request: NextRequest, session: any) {
  try {
    console.log(`[Redis Health API] POST request from user: ${session.user.username}`);
    const body = await request.json();
    const { action, config } = body;
    if (!action) {
      return NextResponse.json({
        success: false,
        error: 'Action is required',
      }, { status: 400 });
    }
    let result: any = {};
    switch (action) {
      case 'start_monitoring':
        await startRedisMonitoring(config);
        result = { status: 'started', message: 'Redis monitoring started' };
        break;
      case 'stop_monitoring':
        await stopRedisMonitoring();
        result = { status: 'stopped', message: 'Redis monitoring stopped' };
        break;
      case 'health_check':
        result = await performRedisHealthCheck();
        break;
      case 'get_stats':
        result = await getRedisMonitoringStats();
        break;
      case 'update_config':
        if (!config) {
          return NextResponse.json({
            success: false,
            error: 'Configuration is required for update_config action',
          }, { status: 400 });
        }
        redisMonitoring.updateConfig(config);
        result = { status: 'updated', message: 'Configuration updated', config };
        break;
      default:
        return NextResponse.json({
          success: false,
          error: 'Invalid action. Use: start_monitoring, stop_monitoring, health_check, get_stats, update_config',
        }, { status: 400 });
    }
    console.log(`[Redis Health API] Action ${action} completed by user: ${session.user.username}`);
    
    return NextResponse.json({
      success: true,
      action,
      result,
      performedAt: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error in Redis health monitoring:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to perform Redis health monitoring operation',
      details: error instanceof Error ? error.message : 'Unknown error',
    }, { status: 500 });
  }
}

async function getHealthStatus(request: NextRequest, session: any) {
  try {
    console.log(`[Redis Health API] GET request from user: ${session.user.username}`);
    const { searchParams } = new URL(request.url);
    const includeHistory = searchParams.get('history') === 'true';
    const historyLimit = parseInt(searchParams.get('limit') || '20');
    // Get current health status
    const currentHealth = await getRedisHealth();
    // Get monitoring statistics
    const stats = await getRedisMonitoringStats();
    // Build response
    const response: any = {
      data: {
        currentHealth,
        statistics: stats,
      },
      generatedAt: new Date().toISOString(),
    };
    // Include history if requested
    if (includeHistory) {
      const history = redisMonitoring.getHealthHistory(historyLimit);
      response.data.history = history;
    }
    return NextResponse.json(response);
  } catch (error) {
    console.error('Error getting Redis health status:', error);
    return NextResponse.json({
      error: 'Failed to get Redis health status',
      details: error instanceof Error ? error.message : 'Unknown error',
    }, { status: 500 });
  }
}

// Export handlers with authentication
export async function GET(request: NextRequest, context?: any) {
  const { user, response } = await requireAdminAuth(request);
  if (response) return response;
  
  const session = { user };
  return getHealthStatus(request, session);
}

export async function POST(request: NextRequest, context?: any) {
  const { user, response } = await requireAdminAuth(request);
  if (response) return response;
  
  const session = { user };
  return handleHealthMonitoring(request, session);
} 
