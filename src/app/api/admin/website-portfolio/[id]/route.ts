import { auth } from "../../../../../../auth";
import { requireAdminAuth } from '@/lib/auth-helpers';
import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { getS3ImageUrl } from '@/utils/imageUtils';

// Schema for website portfolio updates - updated to match frontend data structure
const updateWebsitePortfolioSchema = z.object({
  title: z.string().min(1, 'Title is required').max(200, 'Title too long').optional(),
  description: z.string().max(1000, 'Description too long').optional(),
  url: z.string().url('Invalid URL format').optional(),
  imageSrc: z.string().optional(), // Frontend sends imageSrc
  imageUrl: z.string().optional(), // Keep backward compatibility
  imageKey: z.string().optional(),
  category: z.string().max(100, 'Category too long').optional(),
  featured: z.boolean().optional(),
});

interface RouteParams {
  params: {
    id: string;
  };
}

// GET - Retrieve a specific website portfolio item
export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const portfolioId = params.id;
    
    console.log(`[Website Portfolio API] GET request for portfolio ${portfolioId} from user: ${user.email}`);

    try {
      const portfolioItem = await prisma.websitePortfolio.findUnique({
        where: { id: portfolioId }
      });

      if (!portfolioItem) {
        return NextResponse.json(
          { error: 'Portfolio item not found' },
          { status: 404 }
        );
      }

      // Map database fields to frontend expected format
      const mappedItem = {
        id: portfolioItem.id,
        title: portfolioItem.title,
        description: portfolioItem.description || '',
        url: portfolioItem.projectUrl || portfolioItem.url || '', // Handle both fields
        category: portfolioItem.category || '',
        featured: portfolioItem.featured || false,
        imageSrc: portfolioItem.imageUrl || (portfolioItem.imageKey ? getS3ImageUrl(portfolioItem.imageKey) : ''),
        imageKey: portfolioItem.imageKey || '',
        createdAt: portfolioItem.createdAt.toISOString(),
        updatedAt: portfolioItem.updatedAt.toISOString(),
      };

      console.log(`[Website Portfolio API] Retrieved portfolio ${portfolioId} by user: ${user.email}`);

      // Return the item directly, not wrapped in an object
      return NextResponse.json(mappedItem);

    } catch (dbError) {
      console.error('Database error in website portfolio GET:', dbError);
      return NextResponse.json(
        { error: 'Failed to retrieve portfolio item' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error in website portfolio GET API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT - Update a website portfolio item
export async function PUT(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const portfolioId = params.id;
    
    console.log(`[Website Portfolio API] PUT request for portfolio ${portfolioId} from user: ${user.email}`);

    const body = await request.json();
    console.log('PUT request body:', body);
    
    // Validate input
    const validation = updateWebsitePortfolioSchema.safeParse(body);
    if (!validation.success) {
      console.error('Validation failed:', validation.error.errors);
      return NextResponse.json(
        { 
          error: 'Validation failed',
          details: validation.error.errors 
        },
        { status: 400 }
      );
    }

    const data = validation.data;

    try {
      // Check if portfolio item exists
      const existingItem = await prisma.websitePortfolio.findUnique({
        where: { id: portfolioId }
      });

      if (!existingItem) {
        return NextResponse.json(
          { error: 'Portfolio item not found' },
          { status: 404 }
        );
      }

      // Map frontend data to database fields
      const updateData: any = {
        updatedAt: new Date(),
        updatedBy: user.email,
      };

      if (data.title !== undefined) updateData.title = data.title;
      if (data.description !== undefined) updateData.description = data.description;
      if (data.url !== undefined) {
        updateData.projectUrl = data.url; // Primary URL field
        updateData.url = data.url; // Keep both for compatibility
      }
      if (data.category !== undefined) updateData.category = data.category;
      if (data.featured !== undefined) updateData.featured = data.featured;
      
      // Handle image data - priority: imageSrc > imageUrl > keep existing
      if (data.imageSrc !== undefined) {
        updateData.imageUrl = data.imageSrc;
      } else if (data.imageUrl !== undefined) {
        updateData.imageUrl = data.imageUrl;
      }
      
      // Update imageKey if provided
      if (data.imageKey !== undefined) {
        updateData.imageKey = data.imageKey;
      }

      console.log('Database update data:', updateData);

      // Update the portfolio item
      const updatedItem = await prisma.websitePortfolio.update({
        where: { id: portfolioId },
        data: updateData
      });

      console.log(`[Website Portfolio API] Updated portfolio ${portfolioId} by user: ${user.email}`);

      // Map the updated item to frontend format
      const mappedItem = {
        id: updatedItem.id,
        title: updatedItem.title,
        description: updatedItem.description || '',
        url: updatedItem.projectUrl || updatedItem.url || '',
        category: updatedItem.category || '',
        featured: updatedItem.featured || false,
        imageSrc: updatedItem.imageUrl || (updatedItem.imageKey ? getS3ImageUrl(updatedItem.imageKey) : ''),
        imageKey: updatedItem.imageKey || '',
        createdAt: updatedItem.createdAt.toISOString(),
        updatedAt: updatedItem.updatedAt.toISOString(),
      };

      return NextResponse.json(mappedItem);

    } catch (dbError) {
      console.error('Database error in website portfolio PUT:', dbError);
      return NextResponse.json(
        { error: 'Failed to update portfolio item' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error in website portfolio PUT API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE - Delete a website portfolio item
export async function DELETE(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const portfolioId = params.id;
    
    console.log(`[Website Portfolio API] DELETE request for portfolio ${portfolioId} from user: ${user.email}`);

    try {
      // Check if portfolio item exists
      const existingItem = await prisma.websitePortfolio.findUnique({
        where: { id: portfolioId }
      });

      if (!existingItem) {
        return NextResponse.json(
          { error: 'Portfolio item not found' },
          { status: 404 }
        );
      }

      // Soft delete by setting deletedAt
      const deletedItem = await prisma.websitePortfolio.update({
        where: { id: portfolioId },
        data: {
          deletedAt: new Date(),
          updatedBy: user.email,
        }
      });

      console.log(`[Website Portfolio API] Deleted portfolio ${portfolioId} by user: ${user.email}`);

      return NextResponse.json({
        success: true,
        message: 'Portfolio item deleted successfully'
      });

    } catch (dbError) {
      console.error('Database error in website portfolio DELETE:', dbError);
      return NextResponse.json(
        { error: 'Failed to delete portfolio item' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error in website portfolio DELETE API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
