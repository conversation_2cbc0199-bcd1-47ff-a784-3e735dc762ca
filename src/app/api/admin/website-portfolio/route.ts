import { auth } from "../../../../../auth";;
import { requireAdminAuth } from '@/lib/auth-helpers';;
import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import { z } from 'zod';

import { WebsitePortfolioItem } from '@/types/portfolio';
import { getS3ImageUrl, extractS3KeyFromUrl } from '@/utils/imageUtils';
import prisma from '@/lib/prisma';

// Schema for website portfolio creation
const createWebsitePortfolioSchema = z.object({
  title: z.string().min(1, 'Title is required').max(200, 'Title too long'),
  description: z.string().max(1000, 'Description too long').optional(),
  url: z.string().url('Invalid URL format'),
  imageSrc: z.string().optional(),
  imageKey: z.string().optional(),
  category: z.string().max(100, 'Category too long'),
  featured: z.boolean().default(false),
});

// GET handler - Fetch all website portfolio items
export async function GET(request: NextRequest) {
  try {
    // Check authentication for admin endpoints
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;

    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');

    console.log('Fetching website portfolio items with category filter:', category);

    // Build where clause
    const whereClause: any = {
      deletedAt: null // Only show non-deleted items
    };

    // Add category filter if specified and not 'all'
    if (category && category !== 'all') {
      whereClause.category = category;
    }

    const items = await prisma.websitePortfolio.findMany({
      where: whereClause,
      orderBy: [
        { featured: 'desc' }, // Featured items first
        { createdAt: 'desc' }
      ]
    });

    // Map database fields to frontend expected format
    const mappedItems = items.map(item => ({
      id: item.id,
      title: item.title,
      description: item.description || '',
      url: item.projectUrl || item.url || '', // Handle both fields
      category: item.category || '',
      featured: item.featured || false,
      imageSrc: item.imageUrl || (item.imageKey ? getS3ImageUrl(item.imageKey) : ''),
      imageKey: item.imageKey || '',
      createdAt: item.createdAt.toISOString(),
      updatedAt: item.updatedAt.toISOString(),
    }));

    console.log(`Found ${mappedItems.length} website portfolio items`);
    return NextResponse.json(mappedItems);

  } catch (error) {
    console.error('Error fetching website portfolio items:', error);
    return NextResponse.json(
      { error: 'Failed to fetch website portfolio items' },
      { status: 500 }
    );
  }
}

// POST handler - Create a new website portfolio item
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;

    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    console.log('POST request body:', body);

    // Validate input
    const validation = createWebsitePortfolioSchema.safeParse(body);
    if (!validation.success) {
      console.error('Validation failed:', validation.error.errors);
      return NextResponse.json(
        { 
          error: 'Validation failed',
          details: validation.error.errors 
        },
        { status: 400 }
      );
    }

    const data = validation.data;

    try {
      // Map frontend data to database fields
      const createData: any = {
        title: data.title,
        description: data.description || '',
        projectUrl: data.url, // Primary URL field
        url: data.url, // Keep both for compatibility
        category: data.category,
        featured: data.featured,
        createdBy: user.email,
      };

      // Handle image data
      if (data.imageSrc) {
        createData.imageUrl = data.imageSrc;
      }
      if (data.imageKey) {
        createData.imageKey = data.imageKey;
      }

      console.log('Creating website portfolio item with data:', createData);

      const createdItem = await prisma.websitePortfolio.create({
        data: createData
      });

      console.log(`Created website portfolio item: ${createdItem.id}`);

      // Map the created item to frontend format
      const mappedItem = {
        id: createdItem.id,
        title: createdItem.title,
        description: createdItem.description || '',
        url: createdItem.projectUrl || createdItem.url || '',
        category: createdItem.category || '',
        featured: createdItem.featured || false,
        imageSrc: createdItem.imageUrl || (createdItem.imageKey ? getS3ImageUrl(createdItem.imageKey) : ''),
        imageKey: createdItem.imageKey || '',
        createdAt: createdItem.createdAt.toISOString(),
        updatedAt: createdItem.updatedAt.toISOString(),
      };

      return NextResponse.json(mappedItem, { status: 201 });

    } catch (dbError) {
      console.error('Database error creating website portfolio item:', dbError);
      return NextResponse.json(
        { error: 'Failed to create website portfolio item' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error creating website portfolio item:', error);
    return NextResponse.json(
      { error: 'Failed to create website portfolio item' },
      { status: 500 }
    );
  }
}

// DELETE handler - Delete a website portfolio item (legacy support)
export async function DELETE(request: NextRequest) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;

    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'Portfolio item ID is required' },
        { status: 400 }
      );
    }

    // Get the portfolio item to delete
    const portfolioItem = await prisma.websitePortfolio.findUnique({
      where: { id }
    });

    if (!portfolioItem) {
      return NextResponse.json(
        { error: 'Portfolio item not found' },
        { status: 404 }
      );
    }

    // Soft delete by setting deletedAt
    await prisma.websitePortfolio.update({
      where: { id },
      data: {
        deletedAt: new Date(),
        updatedBy: user.email,
      }
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting website portfolio item:', error);
    return NextResponse.json(
      { error: 'Failed to delete website portfolio item' },
      { status: 500 }
    );
  }
}
