import { auth } from "../../../../../auth";;
import { requireAdminAuth } from '@/lib/auth-helpers';;
import { NextRequest, NextResponse } from 'next/server';

import { sendEmailWithAttachments } from '@/services/emailService';
import { z } from 'zod';

const SendEmailSchema = z.object({
  to: z.string().email('Invalid email address'),
  subject: z.string().min(1, 'Subject is required'),
  message: z.string().min(1, 'Message is required'),
  attachments: z.array(z.object({
    fileName: z.string(),
    originalName: z.string(),
    fileUrl: z.union([z.string().min(1), z.any()]).transform((val) => {
      // Convert any value to string for S3 URLs
      return typeof val === 'string' ? val : String(val);
    }),
    fileType: z.string(),
  })).optional(),
});

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    console.log(`[Send Email API] POST request from user: ${user.email}`);
    
    // Parse request body
    const data = await request.json();
    console.log('[Send Email API] Request data:', JSON.stringify(data, null, 2));
    
    // Validate the data
    const validationResult = SendEmailSchema.safeParse(data);
    if (!validationResult.success) {
      console.warn('[Send Email API] Invalid data:', validationResult.error.format());
      return NextResponse.json(
        { error: 'Invalid data', details: validationResult.error.format() },
        { status: 400 }
      );
    }

    const { to, subject, message, attachments } = validationResult.data;

    // Prepare email content
    const emailHtml = `
      <div style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; max-width: 600px; margin: 0 auto; background: #ffffff; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 20px rgba(0,0,0,0.1);">
        <!-- Header with Logo and Brand Colors -->
        <div style="background: linear-gradient(135deg, #0A2647 0%, #205295 100%); padding: 40px 30px; text-align: center;">
          <img src="${process.env.NEXTAUTH_URL || 'https://mocky.co.ke'}/images/logo.png" alt="Mocky Digital Logo" style="width: 60px; height: 60px; margin-bottom: 20px; border-radius: 8px;">
          <h1 style="color: white; margin: 0; font-size: 28px; font-weight: 700; letter-spacing: -0.5px;">🎨 Design Files Ready!</h1>
          <p style="color: rgba(255,255,255,0.9); margin: 10px 0 0 0; font-size: 16px;">Your project has been completed</p>
        </div>

        <!-- Content Body -->
        <div style="background: #ffffff; padding: 40px 30px;">
          <h2 style="color: #0A2647; margin: 0 0 20px 0; font-size: 24px; font-weight: 600;">Hello there! 👋</h2>

          <div style="color: #4a5568; font-size: 16px; line-height: 1.6; margin-bottom: 25px; white-space: pre-line;">
            ${message}
          </div>

          ${attachments && attachments.length > 0 ? `
            <!-- Attachments Card -->
            <div style="background: linear-gradient(135deg, #f7fafc 0%, #edf2f7 100%); border-left: 4px solid #FF5400; padding: 25px; border-radius: 8px; margin: 25px 0;">
              <h3 style="color: #0A2647; margin: 0 0 15px 0; font-size: 20px; font-weight: 600;">📎 Attached Files</h3>
              <ul style="color: #2d3748; padding-left: 20px; margin: 0;">
                ${attachments.map(att => `<li style="margin-bottom: 8px; color: #4a5568;"><strong style="color: #0A2647;">${att.originalName}</strong></li>`).join('')}
              </ul>
            </div>
          ` : ''}

          <!-- Support Message -->
          <div style="background: #f7fafc; border-left: 4px solid #205295; padding: 20px; border-radius: 0 8px 8px 0; margin: 25px 0;">
            <p style="color: #2d3748; font-size: 16px; line-height: 1.6; margin: 0;">
              💬 If you have any questions or need revisions, please don't hesitate to reach out!
            </p>
          </div>
        </div>

        <!-- Footer -->
        <div style="background: #f7fafc; padding: 25px 30px; text-align: center; border-top: 1px solid #e2e8f0;">
          <p style="color: #718096; font-size: 14px; margin: 0 0 10px 0;">
            <strong style="color: #0A2647;">Mocky Digital Team</strong>
          </p>
          <p style="color: #a0aec0; font-size: 12px; margin: 0;">
            📧 <EMAIL> | 📱 +254 741 590 670<br>
            Professional Design & Digital Services
          </p>
        </div>
      </div>
    `;

    // Prepare attachments for email
    const emailAttachments = attachments?.map(att => ({
      filename: att.originalName,
      path: att.fileUrl, // Nodemailer can handle URLs directly
      contentType: att.fileType,
    })) || [];

    // Send email
    const emailResult = await sendEmailWithAttachments({
      to,
      subject,
      html: emailHtml,
      text: message,
      attachments: emailAttachments,
      from: '<EMAIL>',
    });

    if (!emailResult.success) {
      return NextResponse.json(
        { error: 'Failed to send email', details: emailResult.error },
        { status: 500 }
      );
    }

    console.log(`[Send Email API] Email sent successfully to ${to} by user: ${user.email}`);

    return NextResponse.json({
      success: true,
      message: 'Email sent successfully',
      messageId: emailResult.messageId,
    });

  } catch (error) {
    console.error('Error in send email API:', error);
    return NextResponse.json(
      { error: 'Failed to send email' },
      { status: 500 }
    );
  }
} 