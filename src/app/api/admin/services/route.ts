import { auth } from "../../../../../auth";;
import { requireAdminAuth } from '@/lib/auth-helpers';;
import { NextRequest, NextResponse } from 'next/server';
import * as serviceItemService from '@/services/serviceItemService';


/**
 * GET /api/admin/services
 * Get all services or services by category
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(req);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    console.log(`[Services API] GET request from user: ${user.email}`);

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const category = searchParams.get('category');

    // Get services
    let services;
    if (category) {
      services = await serviceItemService.getServicesByCategory(category);
    } else {
      services = await serviceItemService.getAllServices();
    }

    return NextResponse.json({
      success: true,
      data: services,
      count: services.length
    });
  } catch (error) {
    console.error('Error getting services:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to get services',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * POST /api/admin/services
 * Create a new service
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(req);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    console.log(`[Services API] POST request from user: ${user.email}`);

    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.name || body.price === undefined || !body.category) {
      return NextResponse.json({
        success: false,
        error: 'Name, price, and category are required'
      }, { status: 400 });
    }

    // Create service
    const service = await serviceItemService.createService({
      name: body.name,
      description: body.description || null,
      price: body.price,
      category: body.category
    });

    return NextResponse.json({
      success: true,
      data: service,
      message: 'Service created successfully'
    });
  } catch (error) {
    console.error('Error creating service:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to create service',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * PATCH /api/admin/services
 * Update a service
 */
export async function PATCH(req: NextRequest) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(req);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    console.log(`[Services API] PATCH request from user: ${user.email}`);

    // Get request body
    const body = await req.json();

    // Check if id is provided
    if (!body.id) {
      return NextResponse.json({
        success: false,
        error: 'ID is required'
      }, { status: 400 });
    }

    // Update service
    const service = await serviceItemService.updateService(body.id, {
      name: body.name,
      description: body.description,
      price: body.price,
      category: body.category
    });

    return NextResponse.json({
      success: true,
      data: service,
      message: 'Service updated successfully'
    });
  } catch (error) {
    console.error('Error updating service:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to update service',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * DELETE /api/admin/services
 * Delete a service
 */
export async function DELETE(req: NextRequest) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(req);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    console.log(`[Services API] DELETE request from user: ${user.email}`);

    // Get service ID from the URL
    const searchParams = req.nextUrl.searchParams;
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json({
        success: false,
        error: 'Service ID is required'
      }, { status: 400 });
    }

    // Delete service
    const result = await serviceItemService.deleteService(id);

    if (!result.success) {
      return NextResponse.json({
        success: false,
        error: result.error || 'Failed to delete service'
      }, { status: 400 });
    }

    return NextResponse.json({
      success: true,
      message: 'Service deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting service:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to delete service',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
