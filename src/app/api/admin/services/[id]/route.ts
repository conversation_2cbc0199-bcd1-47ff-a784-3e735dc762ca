import { auth } from "../../../../../../auth";
import { requireAdminAuth } from '@/lib/auth-helpers';
import { NextRequest, NextResponse } from 'next/server';

import { prisma } from '@/lib/prisma';



/**
 * GET /api/admin/services/[id]
 * Get a service by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const serviceId = params.id;
    console.log(`[Service API] GET request for service ${serviceId} from user: ${user.username}`);

    const service = await prisma.service.findUnique({
      where: { id: serviceId }
    });

    if (!service) {
      return NextResponse.json({
        success: false,
        error: 'Service not found'
      }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      data: service
    });
  } catch (error) {
    console.error('Error getting service:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to get service',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * PUT /api/admin/services/[id]
 * Update a service
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const serviceId = params.id;
    console.log(`[Service API] PUT request for service ${serviceId} from user: ${user.username}`);

    const body = await request.json();

    const service = await prisma.service.update({
      where: { id: serviceId },
      data: {
        name: body.name,
        description: body.description,
        price: body.price,
        category: body.category
      }
    });

    return NextResponse.json({
      success: true,
      data: service,
      message: 'Service updated successfully'
    });
  } catch (error) {
    console.error('Error updating service:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to update service',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * DELETE /api/admin/services/[id]
 * Delete a service
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const serviceId = params.id;
    console.log(`[Services API] DELETE request from user: ${user.username} for service: ${serviceId}`);

    // Check if service exists
    const service = await prisma.service.findUnique({
      where: { id: serviceId }
    });

    if (!service) {
      return NextResponse.json({
        success: false,
        error: 'Service not found'
      }, { status: 404 });
    }

    // Check for foreign key constraints
    const [invoiceItemsCount, quoteItemsCount, receiptItemsCount] = await Promise.all([
      prisma.invoiceItem.count({ where: { serviceId } }),
      prisma.quoteItem.count({ where: { serviceId } }),
      prisma.receiptItem.count({ where: { serviceId } })
    ]);

    const totalReferences = invoiceItemsCount + quoteItemsCount + receiptItemsCount;

    if (totalReferences > 0) {
      const details = [];
      if (invoiceItemsCount > 0) details.push(`${invoiceItemsCount} invoice item(s)`);
      if (quoteItemsCount > 0) details.push(`${quoteItemsCount} quote item(s)`);
      if (receiptItemsCount > 0) details.push(`${receiptItemsCount} receipt item(s)`);

      return NextResponse.json({
        success: false,
        error: 'Cannot delete service',
        message: `Service "${service.name}" cannot be deleted because it is referenced by ${details.join(', ')}. Please remove these references first.`
      }, { status: 409 });
    }

    // Delete service
    await prisma.service.delete({
      where: { id: serviceId }
    });

    console.log(`[Services API] Successfully deleted service ${serviceId}`);
    return NextResponse.json({
      success: true,
      message: 'Service deleted successfully'
    });
  } catch (error) {
    console.error(`[Services API] Error deleting service:`, error);
    return NextResponse.json({
      success: false,
      error: 'Failed to delete service',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
