import { auth } from "../../../../../../auth";
import { requireAdminAuth } from '@/lib/auth-helpers';
import { NextRequest, NextResponse } from 'next/server';

import { prisma } from '@/lib/prisma';



interface StorageConfig {
  id: string;
  provider: string;
  region: string | null;
  endpoint: string | null;
  bucketName: string;
  accessKeyId: string;
  secretAccessKey: string;
  isDefault: boolean;
  created_at: Date;
  updated_at: Date;
}

/**
 * GET /api/admin/storage/config
 * Get storage configuration
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    console.log(`[Storage Config API] GET request from user: ${user.username}`);

    const configs = await prisma.storageConfig.findMany({
      orderBy: {
        createdAt: 'desc'
      }
    });

    return NextResponse.json({
      success: true,
      data: configs
    });
  } catch (error) {
    console.error('Error getting storage configs:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to get storage configs',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * POST /api/admin/storage/config
 * Create storage configuration
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    console.log(`[Storage Config API] POST request from user: ${user.username}`);

    const body = await request.json();

    // Validate required fields
    if (!body.provider || !body.bucketName || !body.accessKeyId || !body.secretAccessKey) {
      return NextResponse.json({
        success: false,
        error: 'Provider, bucket name, access key ID, and secret access key are required'
      }, { status: 400 });
    }

    // If this is set as default, unset other defaults
    if (body.isDefault) {
      await prisma.storageConfig.updateMany({
        where: { isDefault: true },
        data: { isDefault: false }
      });
    }

    const config = await prisma.storageConfig.create({
      data: {
        provider: body.provider,
        region: body.region || null,
        endpoint: body.endpoint || null,
        bucketName: body.bucketName,
        accessKeyId: body.accessKeyId,
        secretAccessKey: body.secretAccessKey,
        isDefault: body.isDefault || false
      }
    });

    return NextResponse.json({
      success: true,
      data: config,
      message: 'Storage configuration created successfully'
    });
  } catch (error) {
    console.error('Error creating storage config:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to create storage config',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * PUT /api/admin/storage/config
 * Update storage configuration
 */
export async function PUT(request: NextRequest) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    console.log(`[Storage Config API] PUT request from user: ${user.username}`);

    const body = await request.json();

    // Validate required fields
    if (!body.id) {
      return NextResponse.json({
        success: false,
        error: 'Configuration ID is required'
      }, { status: 400 });
    }

    // If this is set as default, unset other defaults
    if (body.isDefault) {
      await prisma.storageConfig.updateMany({
        where: { 
          isDefault: true,
          id: { not: body.id }
        },
        data: { isDefault: false }
      });
    }

    const config = await prisma.storageConfig.update({
      where: { id: body.id },
      data: {
        provider: body.provider,
        region: body.region || null,
        endpoint: body.endpoint || null,
        bucketName: body.bucketName,
        accessKeyId: body.accessKeyId,
        secretAccessKey: body.secretAccessKey,
        isDefault: body.isDefault || false
      }
    });

    return NextResponse.json({
      success: true,
      data: config,
      message: 'Storage configuration updated successfully'
    });
  } catch (error) {
    console.error('Error updating storage config:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to update storage config',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

 