import { auth } from "../../../../../auth";;
import { requireAdminAuth } from '@/lib/auth-helpers';;
import { NextRequest, NextResponse } from 'next/server';

import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import { getDefaultStorageConfig } from '@/lib/storageConfig';
import { v4 as uuidv4 } from 'uuid';

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    console.log(`[Upload Image API] POST request from user: ${user.email}`);

    const formData = await request.formData();
    const file = formData.get('file') as File;
    const category = formData.get('category') as string || 'general';
    const alt = formData.get('alt') as string || '';

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif', 'image/svg+xml', 'image/avif'];
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { error: 'Invalid file type. Only JPEG, PNG, WebP, GIF, SVG, and AVIF are allowed.' },
        { status: 400 }
      );
    }

    // Validate file size (10MB limit)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      return NextResponse.json(
        { error: 'File size too large. Maximum size is 10MB.' },
        { status: 400 }
      );
    }

    // Get storage configuration
    const storageConfig = await getDefaultStorageConfig();
    if (!storageConfig) {
      return NextResponse.json(
        { error: 'Storage configuration not found' },
        { status: 500 }
      );
    }

    // Create S3 client
    const s3Client = new S3Client({
      region: storageConfig.region,
      endpoint: storageConfig.endpoint,
      credentials: {
        accessKeyId: storageConfig.accessKey,
        secretAccessKey: storageConfig.secretKey,
      },
      forcePathStyle: true,
    });

    // Generate unique filename
    const fileExtension = file.name.split('.').pop();
    const uniqueId = uuidv4();
    const timestamp = Date.now();
    const filename = `${timestamp}-${uniqueId}.${fileExtension}`;
    
    // Create S3 key
    const s3Key = `images/${category}/${filename}`;

    try {
      // Convert file to buffer
      const fileBuffer = await file.arrayBuffer();

      // Upload to S3
      const uploadCommand = new PutObjectCommand({
        Bucket: storageConfig.bucketName,
        Key: s3Key,
        Body: Buffer.from(fileBuffer),
        ContentType: file.type,
        ACL: 'public-read',
        Metadata: {
          originalName: file.name,
          uploadedBy: user.email || 'unknown',
          category: category,
          alt: alt || '',
        },
      });

      await s3Client.send(uploadCommand);

      // Construct public URL
      const imageUrl = `${storageConfig.endpoint}/${storageConfig.bucketName}/${s3Key}`;

      console.log(`[Upload Image API] Image uploaded successfully by user: ${user.email}, file: ${filename}`);

      return NextResponse.json({
        success: true,
        url: imageUrl,
        key: s3Key,
        filename,
        originalName: file.name,
        size: file.size,
        type: file.type,
        category,
        alt,
        uploadedAt: new Date().toISOString(),
      }, {
        status: 201,
        headers: {
          'Cache-Control': 'no-store',
        },
      });

    } catch (s3Error) {
      console.error('S3 upload error:', s3Error);
      return NextResponse.json(
        { error: 'Failed to upload image to storage' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error in upload image API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
