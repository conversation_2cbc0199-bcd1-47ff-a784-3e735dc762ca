import { auth } from "../../../../../auth";;
import { requireAdminAuth } from '@/lib/auth-helpers';;
import { NextRequest, NextResponse } from 'next/server';

import prisma from '@/lib/prisma';
import { logActivity } from '@/utils/passwordUtils';
import { z } from 'zod';

// Validation schema for role creation/update
const RoleSchema = z.object({
  name: z.string().min(2).max(50),
  description: z.string().max(255).optional().nullable(),
  permissions: z.array(z.string()).default([]),
});

// GET handler - Get all roles
export async function GET(request: NextRequest) {
  try {
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;

    console.log(`[Roles API] GET request from user: ${user!.username}`);
    
    // Get roles from database
    const roles = await prisma.role.findMany({
      orderBy: { name: 'asc' }
    });

    console.log(`[Roles API] Retrieved ${roles.length} roles by user: ${user!.username}`);
    return NextResponse.json(roles);
  } catch (error) {
    console.error('Error fetching roles:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

// POST handler - Create a new role
export async function POST(request: NextRequest) {
  try {
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;

    console.log(`[Roles API] POST request from user: ${user!.username}`);
    
    // Parse request body
    const data = await request.json();
    
    // Validate input
    const validationResult = RoleSchema.safeParse(data);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Validation failed', details: validationResult.error.format() },
        { status: 400 }
      );
    }

    // Check if role name already exists
    const existingRole = await prisma.role.findFirst({
      where: { name: data.name }
    });

    if (existingRole) {
      return NextResponse.json(
        { error: 'Role name already exists' },
        { status: 409 }
      );
    }

    // Create role
    const role = await prisma.role.create({
      data: {
        name: data.name,
        description: data.description,
        permissions: data.permissions
      }
    });

    // Log activity
    await logActivity(
      user!.id,
      'role_created',
      `Role ${data.name} created`,
      request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
      request.headers.get('user-agent') || 'unknown',
      'role',
      role.id
    );

    console.log(`[Roles API] Created role: ${role.id} by user: ${user!.username}`);
    return NextResponse.json(role, { status: 201 });
  } catch (error) {
    console.error('Error creating role:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}
