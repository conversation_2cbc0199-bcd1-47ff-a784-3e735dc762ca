import { auth } from "../../../../../auth";;
import { requireAdminAuth } from '@/lib/auth-helpers';;
import { NextRequest, NextResponse } from 'next/server';
import * as comprehensiveReceiptService from '@/services/comprehensiveReceiptService';


/**
 * GET /api/admin/comprehensive-receipts
 * Get all comprehensive receipts
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    console.log(`[Comprehensive Receipts API] GET request from user: ${user.email}`);

    const receipts = await comprehensiveReceiptService.getAllComprehensiveReceipts();

    return NextResponse.json({
      success: true,
      data: receipts,
      count: receipts.length
    });
  } catch (error) {
    console.error('Error getting comprehensive receipts:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to get receipts',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * POST /api/admin/comprehensive-receipts
 * Create a comprehensive receipt directly from M-Pesa message
 * This replaces the transaction → receipt flow with a single step
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    console.log(`[Comprehensive Receipts API] POST request from user: ${user.email}`);

    const body = await request.json();
    console.log('Comprehensive receipt request body:', JSON.stringify(body, null, 2));

    // Validate required fields
    if (!body.mpesaMessage || !body.items || !Array.isArray(body.items) || body.items.length === 0) {
      console.log('Comprehensive receipt validation failed: Missing M-Pesa message or items');
      return NextResponse.json({ 
        success: false,
        error: 'M-Pesa message and items are required' 
      }, { status: 400 });
    }

    // Validate each item
    for (const item of body.items) {
      if (!item.quantity || item.quantity <= 0) {
        console.log('Comprehensive receipt validation failed: Invalid quantity', item);
        return NextResponse.json({ 
          success: false,
          error: 'Each item must have a valid quantity' 
        }, { status: 400 });
      }
      
      // For custom services, require description and unit price
      if (item.isCustomService) {
        if (!item.description?.trim()) {
          console.log('Comprehensive receipt validation failed: Custom service missing description', item);
          return NextResponse.json({ 
            success: false,
            error: 'Custom services must have a description' 
          }, { status: 400 });
        }
        if (!item.unitPrice || item.unitPrice <= 0) {
          console.log('Comprehensive receipt validation failed: Custom service missing unit price', item);
          return NextResponse.json({ 
            success: false,
            error: 'Custom services must have a valid unit price' 
          }, { status: 400 });
        }
      } else {
        // For regular services, require serviceId
        if (!item.serviceId) {
          console.log('Comprehensive receipt validation failed: Regular service missing serviceId', item);
          return NextResponse.json({ 
            success: false,
            error: 'Regular services must have a serviceId' 
          }, { status: 400 });
        }
      }
    }

    try {
      // Create comprehensive receipt
      console.log('Creating comprehensive receipt from M-Pesa message');
      const receipt = await comprehensiveReceiptService.createComprehensiveReceipt({
        mpesaMessage: body.mpesaMessage,
        customerName: body.customerName,
        phoneNumber: body.phoneNumber,
        email: body.email,
        notes: body.notes,
        items: body.items
      });

      if (!receipt) {
        console.log('Comprehensive receipt creation failed: No receipt returned from service');
        return NextResponse.json({ 
          success: false,
          error: 'Failed to create receipt' 
        }, { status: 500 });
      }

      console.log('Comprehensive receipt created successfully:', receipt.id);
      return NextResponse.json({
        success: true,
        data: receipt,
        message: 'Receipt created successfully'
      });
    } catch (serviceError: unknown) {
      console.error('Service error creating comprehensive receipt:', serviceError);
      const errorMessage = serviceError instanceof Error ? serviceError.message : 'Unknown service error';
      
      // Check for specific error types
      if (errorMessage.includes('already exists')) {
        return NextResponse.json({
          success: false,
          error: 'A receipt for this transaction already exists',
          details: errorMessage
        }, { status: 409 });
      }
      
      if (errorMessage.includes('Failed to parse')) {
        return NextResponse.json({
          success: false,
          error: 'Invalid M-Pesa message format',
          details: errorMessage
        }, { status: 400 });
      }
      
      throw new Error(errorMessage);
    }
  } catch (error: any) {
    console.error('Error creating comprehensive receipt:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal Server Error',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
} 