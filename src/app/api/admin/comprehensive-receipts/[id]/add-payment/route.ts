import { auth } from "../../../../../../../auth";
import { requireAdminAuth } from '@/lib/auth-helpers';
import * as comprehensiveReceiptService from '@/services/comprehensiveReceiptService';

import { NextRequest, NextResponse } from 'next/server';

/**
 * POST /api/admin/comprehensive-receipts/[id]/add-payment
 * Add additional payment to an existing receipt
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id: receiptId } = await params;
    console.log(`[Add Payment API] POST request for receipt ${receiptId} from user: ${user.username}`);

    if (!receiptId) {
      return NextResponse.json({
        success: false,
        error: 'Receipt ID is required'
      }, { status: 400 });
    }

    // Get request body
    const body = await request.json();
    console.log(`[Add Payment API] Request body:`, { mpesaMessage: body.mpesaMessage?.substring(0, 50) + '...', notes: body.notes });

    // Check if this is a manual payment or M-Pesa message
    if (body.mpesaMessage) {
      // M-Pesa message payment
      if (typeof body.mpesaMessage !== 'string' || body.mpesaMessage.trim().length === 0) {
        return NextResponse.json({
          success: false,
          error: 'M-Pesa message must be a non-empty string'
        }, { status: 400 });
      }

      // Add payment to receipt using M-Pesa message
      const result = await comprehensiveReceiptService.addPaymentToReceipt({
        receiptId,
        mpesaMessage: body.mpesaMessage.trim(),
        notes: body.notes?.trim() || undefined,
        allowOverpayment: body.allowOverpayment || false
      });

      if (!result) {
        return NextResponse.json({
          success: false,
          error: 'Receipt not found'
        }, { status: 404 });
      }

      console.log(`[Add Payment API] M-Pesa payment added successfully for receipt ${receiptId}`);

      return NextResponse.json({
        success: true,
        data: {
          receipt: result.receipt,
          paymentSummary: {
            previousAmountPaid: result.previousAmountPaid,
            newAmountPaid: result.newAmountPaid,
            additionalPayment: result.newAmountPaid - result.previousAmountPaid
          }
        },
        message: 'Payment added successfully'
      });
    } else if (body.amount) {
      // Manual amount payment
      if (typeof body.amount !== 'number' || body.amount <= 0) {
        return NextResponse.json({
          success: false,
          error: 'Payment amount must be a positive number'
        }, { status: 400 });
      }

      // Create a synthetic M-Pesa message for manual payments
      const syntheticMessage = `Manual payment of KES ${body.amount} added via admin panel on ${new Date().toLocaleString()}`;
      
      const result = await comprehensiveReceiptService.addPaymentToReceipt({
        receiptId,
        mpesaMessage: syntheticMessage,
        notes: body.notes?.trim() || undefined,
        allowOverpayment: body.allowOverpayment || false
      });

      if (!result) {
        return NextResponse.json({
          success: false,
          error: 'Receipt not found'
        }, { status: 404 });
      }

      console.log(`[Add Payment API] Manual payment added successfully for receipt ${receiptId}`);

      return NextResponse.json({
        success: true,
        data: {
          receipt: result.receipt,
          paymentSummary: {
            previousAmountPaid: result.previousAmountPaid,
            newAmountPaid: result.newAmountPaid,
            additionalPayment: result.newAmountPaid - result.previousAmountPaid
          }
        },
        message: 'Payment added successfully'
      });
    } else {
      return NextResponse.json({
        success: false,
        error: 'Either M-Pesa message or payment amount is required'
      }, { status: 400 });
    }
  } catch (error) {
    console.error('[Add Payment API] Error adding payment to receipt:', error);
    
    // Extract more specific error message
    let errorMessage = 'Failed to add payment';
    let statusCode = 500;
    
    if (error instanceof Error) {
      errorMessage = error.message;
      
      // Categorize different types of errors
      if (errorMessage.includes('overpayment') || errorMessage.includes('exceeds total amount')) {
        statusCode = 400; // Bad request for overpayment
      } else if (errorMessage.includes('already fully paid')) {
        statusCode = 400; // Bad request for already paid
      } else if (errorMessage.includes('no outstanding balance')) {
        statusCode = 400; // Bad request for no balance
      } else if (errorMessage.includes('Failed to parse M-Pesa message')) {
        statusCode = 400; // Bad request for invalid message format
      }
    }
    
    return NextResponse.json({
      success: false,
      error: errorMessage,
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: statusCode });
  }
} 