import { auth } from "../../../../../../auth";
import { requireAdminAuth } from '@/lib/auth-helpers';
import { NextRequest, NextResponse } from 'next/server';
import * as comprehensiveReceiptService from '@/services/comprehensiveReceiptService';


/**
 * GET /api/admin/comprehensive-receipts/[id]
 * Get a specific comprehensive receipt by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id: receiptId } = await params;
    console.log(`[Comprehensive Receipt API] GET request for receipt ${receiptId} from user: ${user.username}`);

    if (!receiptId) {
      return NextResponse.json({
        success: false,
        error: 'Receipt ID is required'
      }, { status: 400 });
    }

    const receipt = await comprehensiveReceiptService.getComprehensiveReceiptById(receiptId);

    if (!receipt) {
      return NextResponse.json({
        success: false,
        error: 'Receipt not found'
      }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      data: receipt
    });
  } catch (error) {
    console.error('Error getting comprehensive receipt:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to get receipt',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * PUT /api/admin/comprehensive-receipts/[id]
 * Update a comprehensive receipt status
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id: receiptId } = await params;
    console.log(`[Comprehensive Receipt API] PUT request for receipt ${receiptId} from user: ${user.username}`);

    if (!receiptId) {
      return NextResponse.json({
        success: false,
        error: 'Receipt ID is required'
      }, { status: 400 });
    }

    const body = await request.json();

    if (!body.status) {
      return NextResponse.json({
        success: false,
        error: 'Status is required'
      }, { status: 400 });
    }

    const validStatuses = ['issued', 'paid', 'cancelled', 'overdue'];
    if (!validStatuses.includes(body.status)) {
      return NextResponse.json({
        success: false,
        error: 'Invalid status. Must be one of: ' + validStatuses.join(', ')
      }, { status: 400 });
    }

    const receipt = await comprehensiveReceiptService.updateComprehensiveReceiptStatus(receiptId, body.status);

    if (!receipt) {
      return NextResponse.json({
        success: false,
        error: 'Receipt not found'
      }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      data: receipt,
      message: 'Receipt status updated successfully'
    });
  } catch (error) {
    console.error('Error updating comprehensive receipt:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to update receipt',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * DELETE /api/admin/comprehensive-receipts/[id]
 * Delete a comprehensive receipt
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id: receiptId } = await params;
    console.log(`[Comprehensive Receipt API] DELETE request for receipt ${receiptId} from user: ${user.username}`);

    if (!receiptId) {
      return NextResponse.json({
        success: false,
        error: 'Receipt ID is required'
      }, { status: 400 });
    }

    const success = await comprehensiveReceiptService.deleteComprehensiveReceipt(receiptId);

    if (!success) {
      return NextResponse.json({
        success: false,
        error: 'Receipt not found or could not be deleted'
      }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      message: 'Receipt deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting comprehensive receipt:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to delete receipt',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
} 