import { auth } from "../../../../../../auth";
import { requireAdminAuth } from '@/lib/auth-helpers';
import { NextRequest, NextResponse } from 'next/server';
import * as comprehensiveReceiptService from '@/services/comprehensiveReceiptService';


/**
 * POST /api/admin/comprehensive-receipts/check
 * Check if a comprehensive receipt exists for a given M-Pesa transaction ID
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    console.log(`[Comprehensive Receipts Check API] POST request from user: ${user.username}`);

    const body = await request.json();

    // Validate required fields
    if (!body.mpesaTransactionId) {
      return NextResponse.json({
        success: false,
        error: 'M-Pesa transaction ID is required'
      }, { status: 400 });
    }

    // Check if a comprehensive receipt exists for this M-Pesa transaction ID
    const result = await comprehensiveReceiptService.checkComprehensiveReceiptExists(body.mpesaTransactionId);

    return NextResponse.json({
      success: true,
      exists: result.exists,
      data: result.exists ? {
        receiptId: result.receiptId,
        receiptNumber: result.receiptNumber
      } : null
    });
  } catch (error) {
    console.error('Error checking comprehensive receipt:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to check receipt',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
} 