import { auth } from "../../../../../auth";;
import { requireAdminAuth } from '@/lib/auth-helpers';;
import { NextRequest, NextResponse } from 'next/server';

import { BlogPost } from '@/types/blog';
import slugify from 'slugify';
import * as blogService from '@/services/blogService';
import DOMPurify from 'isomorphic-dompurify';

// GET handler - Get all blog posts
export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    const tag = searchParams.get('tag');
    const status = searchParams.get('status');
    const author = searchParams.get('author');
    const search = searchParams.get('search');

    // Get all blog posts from the database
    let blogPosts = await blogService.getAllBlogPosts();

    // Apply filters if provided
    if (category) {
      blogPosts = blogPosts.filter(post => post.category === category);
    }

    if (tag) {
      blogPosts = blogPosts.filter(post => post.tags?.includes(tag));
    }

    if (status) {
      blogPosts = blogPosts.filter(post => post.status === status);
    }

    if (author) {
      blogPosts = blogPosts.filter(post => post.author?.toLowerCase().includes(author.toLowerCase()));
    }

    if (search) {
      const searchLower = search.toLowerCase();
      blogPosts = blogPosts.filter(post => 
        post.title.toLowerCase().includes(searchLower) ||
        post.content.toLowerCase().includes(searchLower) ||
        post.excerpt.toLowerCase().includes(searchLower) ||
        post.tags?.some(tag => tag.toLowerCase().includes(searchLower))
      );
    }

    // Sort by createdAt (newest first)
    blogPosts.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

    return NextResponse.json(blogPosts, {
      headers: {
        'Cache-Control': 'no-store',
      },
    });
  } catch (error) {
    console.error('Error in GET blog posts:', error);
    return NextResponse.json(
      { error: 'Failed to fetch blog posts' },
      { status: 500 }
    );
  }
}

// POST handler - Create a new blog post
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;

    // Parse request body
    const data = await request.json();

    // Validate required fields
    if (!data.title || !data.content) {
      return NextResponse.json(
        { error: 'Missing required fields: title or content' },
        { status: 400 }
      );
    }

    // Sanitize content to prevent XSS attacks
    const sanitizedContent = DOMPurify.sanitize(data.content, {
      ALLOWED_TAGS: ['p', 'br', 'strong', 'em', 'u', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'ul', 'ol', 'li', 'a', 'img', 'blockquote', 'pre', 'code', 'mark', 'sup', 'sub', 'div', 'span'],
      ALLOWED_ATTR: ['href', 'target', 'rel', 'src', 'alt', 'title', 'class', 'id', 'style'],
      ALLOW_DATA_ATTR: false,
      FORBID_TAGS: ['script', 'object', 'embed', 'form', 'input', 'button'],
      FORBID_ATTR: ['onclick', 'onload', 'onerror', 'onmouseover', 'onfocus', 'onblur', 'onchange', 'onsubmit'],
    });

    // Sanitize other text fields
    const sanitizedTitle = DOMPurify.sanitize(data.title, { ALLOWED_TAGS: [], ALLOWED_ATTR: [] });
    const sanitizedExcerpt = data.excerpt ? DOMPurify.sanitize(data.excerpt, { ALLOWED_TAGS: [], ALLOWED_ATTR: [] }) : '';
    const sanitizedSeoTitle = data.seoTitle ? DOMPurify.sanitize(data.seoTitle, { ALLOWED_TAGS: [], ALLOWED_ATTR: [] }) : '';
    const sanitizedSeoDescription = data.seoDescription ? DOMPurify.sanitize(data.seoDescription, { ALLOWED_TAGS: [], ALLOWED_ATTR: [] }) : '';
    const sanitizedAuthor = data.author ? DOMPurify.sanitize(data.author, { ALLOWED_TAGS: [], ALLOWED_ATTR: [] }) : 'Admin';

    // Generate slug from title
    let slug = slugify(data.title, {
      lower: true,
      strict: true,
      trim: true,
    });

    // Ensure slug is unique
    let uniqueSlug = slug;
    let counter = 1;
    while (await blogService.getBlogPostBySlug(uniqueSlug)) {
      uniqueSlug = `${slug}-${counter}`;
      counter++;
    }

    // Create new blog post
    const newPost = await blogService.createBlogPost({
      title: sanitizedTitle,
      slug: uniqueSlug,
      content: sanitizedContent,
      excerpt: sanitizedExcerpt || sanitizedContent.substring(0, 150) + '...',
      featuredImage: data.featuredImage,
      author: sanitizedAuthor,
      category: data.category || 'uncategorized',
      tags: data.tags || [],
      status: data.status || 'draft',
      seoTitle: sanitizedSeoTitle,
      seoDescription: sanitizedSeoDescription,
      seoKeywords: data.seoKeywords || [],
      publishedAt: data.status === 'published' ? new Date().toISOString() : null
    });

    return NextResponse.json(newPost, { status: 201 });
  } catch (error) {
    console.error('Error in POST blog post:', error);
    return NextResponse.json(
      { error: 'Failed to create blog post' },
      { status: 500 }
    );
  }
}
