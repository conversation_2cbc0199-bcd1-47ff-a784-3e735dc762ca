import { auth } from "../../../../../../auth";
import { requireAdminAuth } from '@/lib/auth-helpers';
import { NextRequest, NextResponse } from 'next/server';

import { BlogPost } from '@/types/blog';
import slugify from 'slugify';
import * as blogService from '@/services/blogService';
import DOMPurify from 'isomorphic-dompurify';

// GET handler - Get a specific blog post
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Await the params promise
    const resolvedParams = await params;
    const id = resolvedParams.id;

    // Get the blog post from the database by ID
    const blogPost = await blogService.getBlogPostById(id);

    if (!blogPost) {
      return NextResponse.json(
        { error: 'Blog post not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(blogPost);
  } catch (error) {
    console.error('Error in GET blog post:', error);
    return NextResponse.json(
      { error: 'Failed to fetch blog post' },
      { status: 500 }
    );
  }
}

// PUT handler - Update a blog post
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;

    // Await the params promise
    const resolvedParams = await params;
    const id = resolvedParams.id;
    const data = await request.json();

    // Validate required fields
    if (!data.title || !data.content) {
      return NextResponse.json(
        { error: 'Missing required fields: title or content' },
        { status: 400 }
      );
    }

    // Sanitize content to prevent XSS attacks
    const sanitizedContent = DOMPurify.sanitize(data.content, {
      ALLOWED_TAGS: ['p', 'br', 'strong', 'em', 'u', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'ul', 'ol', 'li', 'a', 'img', 'blockquote', 'pre', 'code', 'mark', 'sup', 'sub', 'div', 'span'],
      ALLOWED_ATTR: ['href', 'target', 'rel', 'src', 'alt', 'title', 'class', 'id', 'style'],
      ALLOW_DATA_ATTR: false,
      FORBID_TAGS: ['script', 'object', 'embed', 'form', 'input', 'button'],
      FORBID_ATTR: ['onclick', 'onload', 'onerror', 'onmouseover', 'onfocus', 'onblur', 'onchange', 'onsubmit'],
    });

    // Sanitize other text fields
    const sanitizedTitle = DOMPurify.sanitize(data.title, { ALLOWED_TAGS: [], ALLOWED_ATTR: [] });
    const sanitizedExcerpt = data.excerpt ? DOMPurify.sanitize(data.excerpt, { ALLOWED_TAGS: [], ALLOWED_ATTR: [] }) : '';
    const sanitizedSeoTitle = data.seoTitle ? DOMPurify.sanitize(data.seoTitle, { ALLOWED_TAGS: [], ALLOWED_ATTR: [] }) : '';
    const sanitizedSeoDescription = data.seoDescription ? DOMPurify.sanitize(data.seoDescription, { ALLOWED_TAGS: [], ALLOWED_ATTR: [] }) : '';
    const sanitizedAuthor = data.author ? DOMPurify.sanitize(data.author, { ALLOWED_TAGS: [], ALLOWED_ATTR: [] }) : 'Admin';

    // Get the existing blog post
    const existingPost = await blogService.getBlogPostById(id);

    if (!existingPost) {
      return NextResponse.json(
        { error: 'Blog post not found' },
        { status: 404 }
      );
    }

    // Generate slug from title if title has changed
    let slug = existingPost.slug;
    if (sanitizedTitle !== existingPost.title) {
      slug = slugify(sanitizedTitle, {
        lower: true,
        strict: true,
        trim: true,
      });
    }

    // Update the blog post in the database
    const updatedPost = await blogService.updateBlogPost(id, {
      title: sanitizedTitle,
      slug,
      content: sanitizedContent,
      excerpt: sanitizedExcerpt || sanitizedContent.substring(0, 150) + '...',
      category: data.category,
      status: data.status,
      author: sanitizedAuthor,
      featuredImage: data.featuredImage,
      tags: data.tags,
      seoTitle: sanitizedSeoTitle,
      seoDescription: sanitizedSeoDescription,
      seoKeywords: data.seoKeywords,
      scheduledDate: data.scheduledDate,
    });

    if (!updatedPost) {
      return NextResponse.json(
        { error: 'Failed to update blog post' },
        { status: 500 }
      );
    }

    return NextResponse.json(updatedPost);
  } catch (error) {
    console.error('Error in PUT blog post:', error);
    return NextResponse.json(
      { error: 'Failed to update blog post' },
      { status: 500 }
    );
  }
}

// DELETE handler - Delete a blog post
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;

    // Await the params promise
    const resolvedParams = await params;
    const id = resolvedParams.id;

    // Delete the blog post from the database
    const success = await blogService.deleteBlogPost(id);

    if (!success) {
      return NextResponse.json(
        { error: 'Blog post not found or could not be deleted' },
        { status: 404 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error in DELETE blog post:', error);
    return NextResponse.json(
      { error: 'Failed to delete blog post' },
      { status: 500 }
    );
  }
} 