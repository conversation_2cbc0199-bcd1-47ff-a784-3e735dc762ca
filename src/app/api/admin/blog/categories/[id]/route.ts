import { auth } from "../../../../../../../auth";
import { requireAdminAuth } from '@/lib/auth-helpers';
import { NextRequest, NextResponse } from 'next/server';

import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Validation schema for blog category updates
const BlogCategoryUpdateSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name too long').optional(),
  slug: z.string().min(1, 'Slug is required').max(100, 'Slug too long').optional(),
  description: z.string().max(500, 'Description too long').optional(),
  color: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid color format').optional(),
  parentId: z.string().optional(),
});

interface RouteParams {
  params: {
    id: string;
  };
}

// GET - Retrieve a specific blog category
export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const categoryId = params.id;
    
    if (!categoryId || categoryId === 'undefined' || categoryId === 'null') {
      return NextResponse.json(
        { error: 'Invalid category ID' },
        { status: 400 }
      );
    }

    console.log(`[Blog Category API] GET request for category ${categoryId} from user: ${user.email}`);

    try {
      const category = await prisma.blogCategory.findUnique({
        where: { id: categoryId },
        include: {
          parent: true,
          children: true,
          _count: {
            select: {
              posts: true
            }
          }
        }
      });

      if (!category) {
        return NextResponse.json(
          { error: 'Blog category not found' },
          { status: 404 }
        );
      }

      console.log(`[Blog Category API] Retrieved category ${categoryId} by user: ${user.email}`);

      return NextResponse.json({
        success: true,
        category
      });

    } catch (dbError) {
      console.error('Database error in blog category GET:', dbError);
      return NextResponse.json(
        { error: 'Failed to retrieve blog category' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error in blog category GET API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT - Update a blog category
export async function PUT(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const categoryId = params.id;
    
    if (!categoryId || categoryId === 'undefined' || categoryId === 'null') {
      return NextResponse.json(
        { error: 'Invalid category ID' },
        { status: 400 }
      );
    }

    console.log(`[Blog Category API] PUT request for category ${categoryId} from user: ${user.email}`);

    const body = await request.json();
    
    // Validate input
    const validation = BlogCategoryUpdateSchema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json(
        { 
          error: 'Validation failed',
          details: validation.error.errors 
        },
        { status: 400 }
      );
    }

    const data = validation.data;

    try {
      // Check if category exists
      const existingCategory = await prisma.blogCategory.findUnique({
        where: { id: categoryId }
      });

      if (!existingCategory) {
        return NextResponse.json(
          { error: 'Blog category not found' },
          { status: 404 }
        );
      }

      // Check for slug conflicts if slug is being updated
      if (data.slug && data.slug !== existingCategory.slug) {
        const slugConflict = await prisma.blogCategory.findFirst({
          where: { 
            slug: data.slug,
            id: { not: categoryId }
          }
        });

        if (slugConflict) {
          return NextResponse.json(
            { error: 'Slug already exists' },
            { status: 409 }
          );
        }
      }

      // Update the category
      const updateData: any = {
        ...data,
        updatedAt: new Date(),
      };
      
      const updatedCategory = await prisma.blogCategory.update({
        where: { id: categoryId },
        data: updateData,
        include: {
          parent: true,
          children: true,
          _count: {
            select: {
              posts: true
            }
          }
        }
      });

      console.log(`[Blog Category API] Updated category ${categoryId} by user: ${user.email}`);

      return NextResponse.json({
        success: true,
        message: 'Blog category updated successfully',
        category: updatedCategory
      });

    } catch (dbError) {
      console.error('Database error in blog category PUT:', dbError);
      return NextResponse.json(
        { error: 'Failed to update blog category' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error in blog category PUT API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE - Delete a blog category
export async function DELETE(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const categoryId = params.id;
    
    if (!categoryId || categoryId === 'undefined' || categoryId === 'null') {
      return NextResponse.json(
        { error: 'Invalid category ID' },
        { status: 400 }
      );
    }

    console.log(`[Blog Category API] DELETE request for category ${categoryId} from user: ${user.email}`);

    try {
      // Check if category exists and has posts
      const category = await prisma.blogCategory.findUnique({
        where: { id: categoryId },
        include: {
          _count: {
            select: {
              posts: true,
              children: true
            }
          }
        }
      });

      if (!category) {
        return NextResponse.json(
          { error: 'Blog category not found' },
          { status: 404 }
        );
      }

      // Prevent deletion if category has posts or child categories
      if (category._count.posts > 0) {
        return NextResponse.json(
          { error: 'Cannot delete category with existing posts' },
          { status: 400 }
        );
      }

      if (category._count.children > 0) {
        return NextResponse.json(
          { error: 'Cannot delete category with child categories' },
          { status: 400 }
        );
      }

      // Delete the category
      await prisma.blogCategory.delete({
        where: { id: categoryId }
      });

      console.log(`[Blog Category API] Deleted category ${categoryId} by user: ${user.email}`);

      return NextResponse.json({
        success: true,
        message: 'Blog category deleted successfully',
        deletedCategory: {
          id: category.id,
          name: category.name,
          slug: category.slug,
        }
      });

    } catch (dbError) {
      console.error('Database error in blog category DELETE:', dbError);
      return NextResponse.json(
        { error: 'Failed to delete blog category' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error in blog category DELETE API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 
