import { auth } from "../../../../../../auth";
import { requireAdminAuth } from '@/lib/auth-helpers';
import { NextResponse } from 'next/server';

import slugify from 'slugify';
import * as blogCategoryService from '@/services/blogCategoryService';

export async function GET(request: Request) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request); if (response) return response;
    if (!user) {
      return new NextResponse(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
      });
    }

    const categories = await blogCategoryService.getAllBlogCategories();
    
    // Add cache control headers to prevent caching
    return NextResponse.json(categories, {
      headers: {
        'Cache-Control': 'no-store, max-age=0, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });
  } catch (error) {
    console.error('Error fetching blog categories:', error);
    return new NextResponse(JSON.stringify({ error: 'Internal Server Error' }), {
      status: 500,
    });
  }
}

export async function POST(request: Request) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request); if (response) return response;
    if (!user) {
      return new NextResponse(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
      });
    }

    // Get request body
    const body = await request.json();
    const { name, description } = body;

    if (!name) {
      return new NextResponse(JSON.stringify({ error: 'Name is required' }), {
        status: 400,
      });
    }

    // Generate slug from name
    const slug = slugify(name, { lower: true, strict: true });

    // Check if category with same slug exists
    const existingCategory = await blogCategoryService.getBlogCategoryBySlug(slug);

    if (existingCategory) {
      return new NextResponse(JSON.stringify({ error: 'Category already exists' }), {
        status: 400,
      });
    }

    // Create category
    const newCategory = await blogCategoryService.createBlogCategory({
      name,
      slug,
      description
    });

    return NextResponse.json(newCategory);
  } catch (error) {
    console.error('Error creating blog category:', error);
    return new NextResponse(JSON.stringify({ error: 'Internal Server Error' }), {
      status: 500,
    });
  }
} 