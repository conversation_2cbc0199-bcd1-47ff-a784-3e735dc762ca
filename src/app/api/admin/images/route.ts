import { auth } from "../../../../../auth";;
import { requireAdminAuth } from '@/lib/auth-helpers';;
import { NextRequest, NextResponse } from 'next/server';

import { S3Client, ListObjectsV2Command } from '@aws-sdk/client-s3';
import { getDefaultStorageConfig } from '@/lib/storageConfig';

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    console.log(`[Images API] GET request from user: ${user.email}`);
    
    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category') || '';
    const limit = parseInt(searchParams.get('limit') || '50');
    const continuationToken = searchParams.get('continuationToken') || undefined;

    // Get storage configuration
    const storageConfig = await getDefaultStorageConfig();
    if (!storageConfig) {
      return NextResponse.json(
        { error: 'Storage configuration not found' },
        { status: 500 }
      );
    }

    // Create S3 client
    const s3Client = new S3Client({
      region: storageConfig.region,
      endpoint: storageConfig.endpoint,
      credentials: {
        accessKeyId: storageConfig.accessKey,
        secretAccessKey: storageConfig.secretKey,
      },
      forcePathStyle: true,
    });

    // Build prefix for filtering
    const prefix = category ? `images/${category}/` : 'images/';

    try {
      // List objects from S3
      const listCommand = new ListObjectsV2Command({
        Bucket: storageConfig.bucketName,
        Prefix: prefix,
        MaxKeys: limit,
        ContinuationToken: continuationToken,
      });

      const response = await s3Client.send(listCommand);

      // Process the results
      const images = (response.Contents || [])
        .filter(item => {
          // Filter out directories and non-image files
          const key = item.Key || '';
          return key.includes('.') && !key.endsWith('/');
        })
        .map(item => {
          const key = item.Key || '';
          const filename = key.split('/').pop() || '';
          const category = key.split('/')[1] || 'general';
          const url = `${storageConfig.endpoint}/${storageConfig.bucketName}/${key}`;
          
          return {
            key,
            filename,
            category,
            url,
            size: item.Size || 0,
            lastModified: item.LastModified?.toISOString() || '',
          };
        })
        .sort((a, b) => new Date(b.lastModified).getTime() - new Date(a.lastModified).getTime());

      console.log(`[Images API] Retrieved ${images.length} images for category: ${category || 'all'} by user: ${user.email}`);

      return NextResponse.json({
        success: true,
        images,
        hasMore: response.IsTruncated || false,
        nextContinuationToken: response.NextContinuationToken,
        count: images.length,
        category: category || 'all',
      }, {
        headers: {
          'Cache-Control': 'no-store, max-age=0',
          'Pragma': 'no-cache',
        },
      });

    } catch (s3Error) {
      console.error('S3 list error:', s3Error);
      return NextResponse.json(
        { error: 'Failed to list images from storage' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error in images API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 
