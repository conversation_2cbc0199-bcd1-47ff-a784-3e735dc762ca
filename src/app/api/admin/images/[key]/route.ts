import { auth } from "../../../../../../auth";
import { requireAdminAuth } from '@/lib/auth-helpers';
import { NextRequest, NextResponse } from 'next/server';

import { S3Client, DeleteObjectCommand, HeadObjectCommand } from '@aws-sdk/client-s3';
import { getDefaultStorageConfig } from '@/lib/storageConfig';

interface RouteParams {
  params: {
    key: string;
  };
}

// DELETE - Delete an image from S3
export async function DELETE(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Decode the S3 key (it might be URL encoded)
    const s3Key = decodeURIComponent(params.key);

    console.log(`[Delete Image API] DELETE request for key: ${s3Key} from user: ${user.email}`);

    // Basic validation - ensure it's an image path
    if (!s3Key.startsWith('images/')) {
      return NextResponse.json(
        { error: 'Invalid image path' },
        { status: 400 }
      );
    }

    // Get storage configuration
    const storageConfig = await getDefaultStorageConfig();
    if (!storageConfig) {
      return NextResponse.json(
        { error: 'Storage configuration not found' },
        { status: 500 }
      );
    }

    // Create S3 client
    const s3Client = new S3Client({
      region: storageConfig.region,
      endpoint: storageConfig.endpoint,
      credentials: {
        accessKeyId: storageConfig.accessKey,
        secretAccessKey: storageConfig.secretKey,
      },
      forcePathStyle: true,
    });

    try {
      // First, check if the object exists
      try {
        await s3Client.send(new HeadObjectCommand({
          Bucket: storageConfig.bucketName,
          Key: s3Key,
        }));
      } catch (headError: any) {
        if (headError.name === 'NotFound' || headError.$metadata?.httpStatusCode === 404) {
          return NextResponse.json(
            { error: 'Image not found' },
            { status: 404 }
          );
        }
        throw headError;
      }

      // Delete the object
      await s3Client.send(new DeleteObjectCommand({
        Bucket: storageConfig.bucketName,
        Key: s3Key,
      }));

      console.log(`[Delete Image API] Image deleted successfully: ${s3Key} by user: ${user.email}`);

      return NextResponse.json({
        success: true,
        message: 'Image deleted successfully',
        deletedKey: s3Key,
        deletedAt: new Date().toISOString(),
      });

    } catch (s3Error) {
      console.error('S3 delete error:', s3Error);
      return NextResponse.json(
        { error: 'Failed to delete image from storage' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error in delete image API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// GET - Get image metadata (optional, for consistency)
export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Decode the S3 key
    const s3Key = decodeURIComponent(params.key);

    console.log(`[Image Info API] GET request for key: ${s3Key} from user: ${user.email}`);

    // Basic validation
    if (!s3Key.startsWith('images/')) {
      return NextResponse.json(
        { error: 'Invalid image path' },
        { status: 400 }
      );
    }

    // Get storage configuration
    const storageConfig = await getDefaultStorageConfig();
    if (!storageConfig) {
      return NextResponse.json(
        { error: 'Storage configuration not found' },
        { status: 500 }
      );
    }

    // Create S3 client
    const s3Client = new S3Client({
      region: storageConfig.region,
      endpoint: storageConfig.endpoint,
      credentials: {
        accessKeyId: storageConfig.accessKey,
        secretAccessKey: storageConfig.secretKey,
      },
      forcePathStyle: true,
    });

    try {
      // Get object metadata
      const headResponse = await s3Client.send(new HeadObjectCommand({
        Bucket: storageConfig.bucketName,
        Key: s3Key,
      }));

      const filename = s3Key.split('/').pop() || '';
      const category = s3Key.split('/')[1] || 'general';
      const url = `${storageConfig.endpoint}/${storageConfig.bucketName}/${s3Key}`;

      console.log(`[Image Info API] Retrieved metadata for: ${s3Key} by user: ${user.email}`);

      return NextResponse.json({
        success: true,
        image: {
          key: s3Key,
          filename,
          category,
          url,
          size: headResponse.ContentLength || 0,
          contentType: headResponse.ContentType || '',
          lastModified: headResponse.LastModified?.toISOString() || '',
          metadata: headResponse.Metadata || {},
        }
      });

    } catch (s3Error: any) {
      if (s3Error.name === 'NotFound' || s3Error.$metadata?.httpStatusCode === 404) {
        return NextResponse.json(
          { error: 'Image not found' },
          { status: 404 }
        );
      }
      
      console.error('S3 head error:', s3Error);
      return NextResponse.json(
        { error: 'Failed to retrieve image metadata' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error in image info API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 
