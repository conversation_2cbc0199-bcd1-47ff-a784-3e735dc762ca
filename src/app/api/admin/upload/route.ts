import { auth } from "../../../../../auth";;
import { requireAdminAuth } from '@/lib/auth-helpers';;
import { NextRequest, NextResponse } from 'next/server';

import { uploadImageToS3 } from '@/utils/s3Utils';

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const formData = await request.formData();
    const file = formData.get('file') as File;
    const type = formData.get('type') as string || 'blog';

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      );
    }

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif', 'image/avif'];
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { error: 'Invalid file type. Only JPEG, PNG, WebP, GIF, and AVIF are allowed.' },
        { status: 400 }
      );
    }

    // Validate file size (15MB limit to match S3Utils)
    const maxSize = 15 * 1024 * 1024; // 15MB
    if (file.size > maxSize) {
      return NextResponse.json(
        { error: 'File size too large. Maximum size is 15MB.' },
        { status: 400 }
      );
    }

    // Generate unique filename with timestamp and random string
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2);
    const extension = file.name.split('.').pop();
    const filename = `${timestamp}-${randomString}.${extension}`;
    
    // Create S3 key based on type
    const s3Key = `images/${type}/${filename}`;

    try {
      // Upload to S3 using existing infrastructure
      const uploadResult = await uploadImageToS3(file, s3Key);

      if (!uploadResult.success) {
        return NextResponse.json(
          { error: uploadResult.error || 'Failed to upload image to S3' },
          { status: 500 }
        );
      }

      console.log(`[Admin Upload] File uploaded successfully by user: ${user.email}, file: ${filename}`);

      return NextResponse.json({
        success: true,
        url: uploadResult.url,
        filename,
        size: file.size,
        type: file.type,
        s3Key
      }, {
        headers: {
          'Cache-Control': 'no-store',
        },
      });

    } catch (s3Error) {
      console.error('S3 upload error:', s3Error);
      return NextResponse.json(
        { error: 'Failed to upload image to cloud storage' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error uploading file:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}