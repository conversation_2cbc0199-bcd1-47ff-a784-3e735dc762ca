import { auth } from "../../../../../auth";;
import { requireAdminAuth } from '@/lib/auth-helpers';;
import { NextRequest, NextResponse } from 'next/server';
import * as invoiceService from '@/services/invoiceService';


/**
 * GET /api/admin/invoices
 * Get all invoices
 */
export async function GET(req: NextRequest) {
  try {
    const { user, response } = await requireAdminAuth(req);
    if (response) return response;
    
    console.log(`[Invoices API] GET request from user: ${user!.username}`);

    // Get all invoices
    const invoices = await invoiceService.getAllInvoices();

    return NextResponse.json(invoices);
  } catch (error) {
    console.error('Error getting invoices:', error);
    return NextResponse.json({
      error: 'Failed to get invoices',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * POST /api/admin/invoices
 * Create a new invoice
 */
export async function POST(req: NextRequest) {
  try {
    const { user, response } = await requireAdminAuth(req);
    if (response) return response;
    
    console.log(`[Invoices API] POST request from user: ${user!.username}`);

    // Get request body
    const body = await req.json();
    console.log('Invoice request body:', JSON.stringify(body, null, 2));

    // Validate required fields
    if (!body.customerName || !body.phoneNumber || !body.items || !Array.isArray(body.items) || body.items.length === 0) {
      console.log('Invoice validation failed: Missing required fields');
      return NextResponse.json({
        error: 'Customer name, phone number, and items are required'
      }, { status: 400 });
    }

    // Validate items
    for (const item of body.items) {
      if (!item.serviceId || !item.quantity) {
        console.log('Invoice validation failed: Missing item fields');
        return NextResponse.json({ error: 'Each item must have a service ID and quantity' }, { status: 400 });
      }
    }

    try {
      // Create invoice
      console.log('Creating invoice for customer:', body.customerName);
      console.log('Invoice items:', JSON.stringify(body.items, null, 2));

      const invoice = await invoiceService.createInvoice({
        customerName: body.customerName,
        phoneNumber: body.phoneNumber,
        email: body.email,
        notes: body.notes,
        dueDate: body.dueDate ? new Date(body.dueDate) : undefined,
        items: body.items,
        quoteId: body.quoteId
      });

      if (!invoice) {
        console.log('Invoice creation failed: No invoice returned from service');
        return NextResponse.json({
          error: 'Failed to create invoice - no invoice returned from service'
        }, { status: 500 });
      }

      console.log('Invoice created successfully:', invoice.id);
      return NextResponse.json(invoice);
    } catch (serviceError) {
      console.error('Error in invoice service:', serviceError);
      console.error('Service error details:', {
        message: serviceError instanceof Error ? serviceError.message : 'Unknown error',
        stack: serviceError instanceof Error ? serviceError.stack : undefined,
        name: serviceError instanceof Error ? serviceError.name : undefined
      });

      return NextResponse.json({
        error: serviceError instanceof Error ? serviceError.message : 'Failed to create invoice',
        message: serviceError instanceof Error ? serviceError.message : 'Unknown error'
      }, { status: 500 });
    }
  } catch (error) {
    console.error('Error creating invoice:', error);
    return NextResponse.json({
      error: 'Failed to create invoice',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * PATCH /api/admin/invoices
 * Update an invoice's payment status
 */
export async function PATCH(req: NextRequest) {
  try {
    const { user, response } = await requireAdminAuth(req);
    if (response) return response;
    
    console.log(`[Invoices API] PATCH request from user: ${user!.username}`);

    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.id || body.amountPaid === undefined) {
      return NextResponse.json({
        error: 'Invoice ID and amount paid are required'
      }, { status: 400 });
    }

    // Update invoice payment
    const invoice = await invoiceService.updateInvoicePayment(body.id, body.amountPaid);

    if (!invoice) {
      return NextResponse.json({
        error: 'Invoice not found'
      }, { status: 404 });
    }

    return NextResponse.json(invoice);
  } catch (error) {
    console.error('Error updating invoice payment:', error);
    return NextResponse.json({
      error: 'Failed to update invoice payment',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
