import { auth } from "../../../../../../auth";
import { requireAdminAuth } from '@/lib/auth-helpers';
import { NextRequest, NextResponse } from 'next/server';
import * as invoiceService from '@/services/invoiceService';

import { prisma } from '@/lib/prisma';

/**
 * GET /api/admin/invoices/[id]
 * Get an invoice by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id: invoiceId } = await params;

    console.log(`[Invoice API] GET request for invoice ${invoiceId} from user: ${user.username}`);

    if (!invoiceId) {
      return NextResponse.json({
        success: false,
        error: 'Invoice ID is required'
      }, { status: 400 });
    }

    // Get invoice by ID
    const invoice = await prisma.invoice.findUnique({
      where: { id: invoiceId },
      include: {
        items: true
      }
    });

    if (!invoice) {
      return NextResponse.json({
        success: false,
        error: 'Invoice not found'
      }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      data: invoice
    });
  } catch (error) {
    console.error('Error getting invoice:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to get invoice',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * PUT /api/admin/invoices/[id]
 * Update an invoice by ID
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id: invoiceId } = await params;
    console.log(`[Invoice API] PUT request for invoice ${invoiceId} from user: ${user.username}`);

    if (!invoiceId) {
      return NextResponse.json({
        success: false,
        error: 'Invoice ID is required'
      }, { status: 400 });
    }

    const body = await request.json();

    const invoice = await prisma.invoice.update({
      where: { id: invoiceId },
      data: {
        customerName: body.customerName,
        phoneNumber: body.phoneNumber,
        email: body.email,
        status: body.status,
        notes: body.notes,
        totalAmount: body.totalAmount,
        dueDate: body.dueDate ? new Date(body.dueDate) : undefined,
        issuedAt: body.issuedAt ? new Date(body.issuedAt) : undefined
      },
      include: {
        items: true
      }
    });

    return NextResponse.json({
      success: true,
      data: invoice,
      message: 'Invoice updated successfully'
    });
  } catch (error) {
    console.error('Error updating invoice:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to update invoice',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * DELETE /api/admin/invoices/[id]
 * Delete an invoice by ID
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id: invoiceId } = await params;
    console.log(`[Invoices API] DELETE request from user: ${user.username} for invoice: ${invoiceId}`);

    // Delete invoice by ID
    const success = await invoiceService.deleteInvoice(invoiceId);

    if (!success) {
      return NextResponse.json({
        success: false,
        error: 'Invoice not found'
      }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      message: 'Invoice deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting invoice:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to delete invoice',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
