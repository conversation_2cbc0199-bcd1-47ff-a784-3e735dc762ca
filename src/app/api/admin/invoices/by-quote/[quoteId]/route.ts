import { auth } from "../../../../../../../auth";
import { requireAdminAuth } from '@/lib/auth-helpers';
import { NextRequest, NextResponse } from 'next/server';

import { prisma } from '@/lib/prisma';

/**
 * GET /api/admin/invoices/by-quote/[quoteId]
 * Get invoice by quote ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ quoteId: string }> }
) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { quoteId } = await params;
    console.log(`[Invoice by Quote API] GET request for quote ${quoteId} from user: ${user.username}`);

    if (!quoteId) {
      return NextResponse.json({
        success: false,
        error: 'Quote ID is required'
      }, { status: 400 });
    }

    // Find invoice by quote ID
    const invoice = await prisma.invoice.findFirst({
      where: { quoteId: quoteId },
      include: {
        items: true
      }
    });

    if (!invoice) {
      return NextResponse.json({
        success: false,
        error: 'Invoice not found for this quote'
      }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      data: invoice
    });
  } catch (error) {
    console.error('Error getting invoice by quote:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to get invoice',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
