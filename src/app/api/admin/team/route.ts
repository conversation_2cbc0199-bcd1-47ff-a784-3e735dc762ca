import { auth } from "../../../../../auth";;
import { requireAdminAuth } from '@/lib/auth-helpers';;
import { NextRequest, NextResponse } from 'next/server';

import prisma from '@/lib/prisma';
import { uploadImageToS3 } from '@/utils/s3Utils';
import { getS3ImageUrl } from '@/utils/imageUtils';
import crypto from 'crypto';

// GET handler - Get all team members
export async function GET(request: NextRequest) {
  try {
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    console.log(`[Team API] GET request from user: ${user.username}`);
    
    // Get team members ordered by order field
    const teamMembers = await prisma.teamMember.findMany({
      orderBy: { order: 'asc' },
      select: {
        id: true,
        name: true,
        role: true,
        bio: true,
        imageKey: true,
        order: true,
        linkedinUrl: true,
        twitterUrl: true,
        githubUrl: true,
        emailAddress: true,
        createdAt: true,
        updatedAt: true,
      }
    });

    // Transform the data to include proper image URLs
    const transformedMembers = teamMembers.map(member => ({
      ...member,
      createdAt: member.createdAt.toISOString(),
      updatedAt: member.updatedAt.toISOString(),
      imageSrc: member.imageKey ? getS3ImageUrl(member.imageKey) : '/images/placeholder.svg'
    }));

    console.log(`[Team API] Retrieved ${teamMembers.length} team members by user: ${user.username}`);
    return NextResponse.json(transformedMembers);
  } catch (error) {
    console.error('Error fetching team members:', error);
    return NextResponse.json(
      { error: 'Failed to fetch team members' },
      { status: 500 }
    );
  }
}

// POST handler - Create a new team member
export async function POST(request: NextRequest) {
  try {
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    console.log(`[Team API] POST request from user: ${user.username}`);
    
    const formData = await request.formData();
    
    const name = formData.get('name') as string;
    const role = formData.get('role') as string;
    const bio = formData.get('bio') as string;
    const order = parseInt(formData.get('order') as string) || 0;
    const linkedinUrl = formData.get('linkedinUrl') as string || null;
    const twitterUrl = formData.get('twitterUrl') as string || null;
    const githubUrl = formData.get('githubUrl') as string || null;
    const emailAddress = formData.get('emailAddress') as string || null;
    const imageFile = formData.get('image') as File | null;

    if (!name || !role || !bio) {
      return NextResponse.json(
        { error: 'Name, role, and bio are required' },
        { status: 400 }
      );
    }

    // Prepare create data
    const createData: any = {
      name,
      role,
      bio,
      order,
      linkedinUrl,
      twitterUrl,
      githubUrl,
      emailAddress,
    };

    // Handle image upload if provided
    let imageUploadWarning: string | null = null;
    if (imageFile && imageFile.size > 0) {
      try {
        console.log(`[Team API] Processing image upload for new team member: ${name}`);
        
        // Validate file type
        if (!imageFile.type.startsWith('image/')) {
          return NextResponse.json(
            { error: 'Please upload a valid image file' },
            { status: 400 }
          );
        }

        // Validate file size (15MB limit to match other parts of the system)
        const maxSize = 15 * 1024 * 1024; // 15MB
        if (imageFile.size > maxSize) {
          return NextResponse.json(
            { error: 'Image file size must be less than 15MB' },
            { status: 400 }
          );
        }

        // Generate unique filename for S3
        const buffer = await imageFile.arrayBuffer();
        const hash = crypto.createHash('sha256')
          .update(Buffer.from(buffer))
          .digest('hex')
          .slice(0, 8);
        const ext = imageFile.name.split('.').pop();
        const filename = `${hash}-${Date.now()}.${ext}`;
        const s3Key = `images/team/${filename}`;

        // Upload to S3
        const uploadResult = await uploadImageToS3(imageFile, s3Key);
        if (uploadResult.success) {
          createData.imageKey = s3Key;
          console.log(`[Team API] Image uploaded successfully: ${s3Key}`);
        } else {
          console.error('S3 upload failed:', uploadResult.error);
          imageUploadWarning = uploadResult.error || 'Image upload failed, but team member was created';
          // Continue with creation even if image upload fails
        }
      } catch (uploadError) {
        console.error('Error uploading image to S3:', uploadError);
        imageUploadWarning = 'Image upload failed, but team member was created';
        // Continue with creation even if image upload fails
      }
    }

    // Create the team member
    const newMember = await prisma.teamMember.create({
      data: createData,
    });

    // Get the image URL for the response
    let imageSrc = '/images/placeholder.svg';
    if (newMember.imageKey) {
      try {
        imageSrc = getS3ImageUrl(newMember.imageKey);
      } catch (error) {
        console.warn('Error getting S3 image URL:', error);
        // Keep placeholder if S3 URL generation fails
      }
    }

    // Transform the response
    const transformedMember = {
      ...newMember,
      createdAt: newMember.createdAt.toISOString(),
      updatedAt: newMember.updatedAt.toISOString(),
      imageSrc
    };

    console.log(`[Team API] Created team member: ${newMember.name} (ID: ${newMember.id}) by user: ${user.username}`);
    
    // Return response with optional warning
    const responseData = imageUploadWarning 
      ? { ...transformedMember, warning: imageUploadWarning }
      : transformedMember;
    
    return NextResponse.json(responseData, { status: 201 });
  } catch (error) {
    console.error('Error creating team member:', error);
    return NextResponse.json(
      { error: 'Failed to create team member' },
      { status: 500 }
    );
  }
}
