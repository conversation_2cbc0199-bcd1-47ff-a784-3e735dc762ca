import { auth } from "../../../../../../auth";
import { requireAdminAuth } from '@/lib/auth-helpers';
import { NextRequest, NextResponse } from 'next/server';

import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Schema for team member updates
const updateTeamMemberSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name too long').optional(),
  role: z.string().min(1, 'Role is required').max(100, 'Role too long').optional(),
  bio: z.string().max(500, 'Bio too long').optional(),
  email: z.string().email('Invalid email format').optional(),
  phone: z.string().max(20, 'Phone too long').optional(),
  imageUrl: z.string().url('Invalid image URL').optional(),
  socialLinks: z.object({
    linkedin: z.string().url().optional().or(z.literal('')),
    twitter: z.string().url().optional().or(z.literal('')),
    github: z.string().url().optional().or(z.literal('')),
    website: z.string().url().optional().or(z.literal('')),
  }).optional(),
  skills: z.array(z.string()).optional(),
  isActive: z.boolean().optional(),
});

interface RouteParams {
  params: {
    id: string;
  };
}

// GET - Retrieve a specific team member
export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const teamMemberId = params.id;
    
    // Validate CUID format
    if (!teamMemberId || typeof teamMemberId !== 'string' || teamMemberId.length < 20) {
      return NextResponse.json(
        { error: 'Invalid team member ID format' },
        { status: 400 }
      );
    }

    console.log(`[Team API] GET request for team member ${teamMemberId} from user: ${user.email}`);

    try {
      const teamMember = await prisma.teamMember.findUnique({
        where: { id: teamMemberId }
      });

      if (!teamMember) {
        return NextResponse.json(
          { error: 'Team member not found' },
          { status: 404 }
        );
      }

      console.log(`[Team API] Retrieved team member ${teamMemberId} by user: ${user.email}`);

      return NextResponse.json({
        success: true,
        teamMember
      });

    } catch (dbError) {
      console.error('Database error in team member GET:', dbError);
      return NextResponse.json(
        { error: 'Failed to retrieve team member' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error in team member GET API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT - Update a team member
export async function PUT(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const teamMemberId = params.id;
    
    // Validate CUID format
    if (!teamMemberId || typeof teamMemberId !== 'string' || teamMemberId.length < 20) {
      return NextResponse.json(
        { error: 'Invalid team member ID format' },
        { status: 400 }
      );
    }

    console.log(`[Team API] PUT request for team member ${teamMemberId} from user: ${user.email}`);

    const body = await request.json();
    
    // Validate the data
    const validation = updateTeamMemberSchema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json(
        { 
          error: 'Validation failed',
          details: validation.error.errors 
        },
        { status: 400 }
      );
    }

    const data = validation.data;

    try {
      // Check if team member exists
      const existingTeamMember = await prisma.teamMember.findUnique({
        where: { id: teamMemberId }
      });

      if (!existingTeamMember) {
        return NextResponse.json(
          { error: 'Team member not found' },
          { status: 404 }
        );
      }

      // Update the team member
      const updatedTeamMember = await prisma.teamMember.update({
        where: { id: teamMemberId },
        data: {
          ...data,
          updatedAt: new Date(),
        }
      });

      console.log(`[Team API] Updated team member ${teamMemberId} by user: ${user.email}`);

      return NextResponse.json({
        success: true,
        message: 'Team member updated successfully',
        teamMember: updatedTeamMember
      });

    } catch (dbError) {
      console.error('Database error in team member PUT:', dbError);
      return NextResponse.json(
        { error: 'Failed to update team member' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error in team member PUT API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE - Delete a team member
export async function DELETE(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const teamMemberId = params.id;
    
    // Validate CUID format - this was the main bug!
    if (!teamMemberId || typeof teamMemberId !== 'string' || teamMemberId.length < 20) {
      console.error(`[Team API] Invalid team member ID format: ${teamMemberId}`);
      return NextResponse.json(
        { error: 'Invalid team member ID format' },
        { status: 400 }
      );
    }

    console.log(`[Team API] DELETE request for team member ${teamMemberId} from user: ${user.username}`);

    try {
      // Check if team member exists
      const teamMember = await prisma.teamMember.findUnique({
        where: { id: teamMemberId }
      });

      if (!teamMember) {
        console.log(`[Team API] Team member not found: ${teamMemberId}`);
        return NextResponse.json(
          { error: 'Team member not found' },
          { status: 404 }
        );
      }

      // Delete the team member
      await prisma.teamMember.delete({
        where: { id: teamMemberId }
      });

      console.log(`[Team API] Successfully deleted team member ${teamMemberId} by user: ${user.username}`);

      return NextResponse.json({
        success: true,
        message: 'Team member deleted successfully',
        deletedTeamMember: {
          id: teamMember.id,
          name: teamMember.name,
          role: teamMember.role,
        }
      });

    } catch (dbError) {
      console.error('Database error in team member DELETE:', dbError);
      return NextResponse.json(
        { error: 'Failed to delete team member', details: (dbError as Error).message },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error in team member DELETE API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
