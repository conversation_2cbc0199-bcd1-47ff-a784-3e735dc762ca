import { auth } from "../../../../../../auth";
import { requireAdminAuth } from '@/lib/auth-helpers';
import { NextRequest, NextResponse } from 'next/server';

import prisma from '@/lib/prisma';
import { hashPassword, logActivity } from '@/utils/passwordUtils';
import { z } from 'zod';

// Validation schema for user updates
const UserUpdateSchema = z.object({
  username: z.string().min(3).max(50).optional(),
  email: z.string().email().optional(),
  name: z.string().min(1).max(100).optional(),
  password: z.string().min(6).optional(),
  roleId: z.string().uuid().optional(),
  active: z.boolean().optional()
});

/**
 * GET /api/admin/users/[id]
 * Get a specific user
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;

    const resolvedParams = await params;
    const { id } = resolvedParams;
    
    console.log(`[Users API] GET user ${id} request from user: ${user!.username}`);

    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(id)) {
      return NextResponse.json(
        { error: 'Invalid user ID format' },
        { status: 400 }
      );
    }

    const targetUser = await prisma.user.findUnique({
      where: { id },
      select: {
        id: true,
        username: true,
        email: true,
        name: true,
        active: true,
        lastLogin: true,
        createdAt: true,
        updatedAt: true,
        roleId: true,
        role: {
          select: {
            id: true,
            name: true,
            description: true,
            permissions: true,
          }
        }
      }
    });

    if (!targetUser) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    console.log(`[Users API] Retrieved user ${targetUser.username} by user: ${user!.username}`);
    return NextResponse.json(targetUser);
  } catch (error) {
    console.error('Error fetching user:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/admin/users/[id]
 * Update a user
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;

    const resolvedParams = await params;
    const { id } = resolvedParams;

    console.log(`[Users API] PUT user ${id} request from user: ${user!.username}`);

    // Parse and validate request body
    const data = await request.json();
    const validationResult = UserUpdateSchema.safeParse(data);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Validation failed', details: validationResult.error.format() },
        { status: 400 }
      );
    }

    const updateData = validationResult.data;

    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { id },
      include: { role: true }
    });

    if (!existingUser) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Check for duplicate username/email if changing
    if (updateData.username || updateData.email) {
      const duplicateUser = await prisma.user.findFirst({
        where: {
          OR: [
            updateData.username ? { username: updateData.username } : {},
            updateData.email ? { email: updateData.email } : {}
          ].filter(obj => Object.keys(obj).length > 0),
          NOT: { id }
        }
      });

      if (duplicateUser) {
        return NextResponse.json(
          { error: 'Username or email already exists' },
          { status: 409 }
        );
      }
    }

    // Check if role exists if changing
    if (updateData.roleId) {
      const role = await prisma.role.findUnique({
        where: { id: updateData.roleId }
      });

      if (!role) {
        return NextResponse.json(
          { error: 'Role not found' },
          { status: 404 }
        );
      }
    }

    // Prepare update data
    const finalUpdateData: any = {
      username: updateData.username,
      email: updateData.email,
      name: updateData.name,
      roleId: updateData.roleId,
      active: updateData.active
    };

    // Remove undefined fields
    Object.keys(finalUpdateData).forEach(key => {
      if (finalUpdateData[key] === undefined) {
        delete finalUpdateData[key];
      }
    });

    // Hash password if provided
    if (updateData.password) {
      finalUpdateData.passwordHash = await hashPassword(updateData.password);
    }

    // Update user
    const updatedUser = await prisma.user.update({
      where: { id },
      data: finalUpdateData,
      select: {
        id: true,
        username: true,
        email: true,
        name: true,
        active: true,
        lastLogin: true,
        createdAt: true,
        updatedAt: true,
        roleId: true,
        role: {
          select: {
            id: true,
            name: true,
            description: true,
            permissions: true
          }
        }
      }
    });

    // Log activity
    try {
      await logActivity(
        user!.id,
        'user_updated',
        `User ${updatedUser.username} updated`,
        request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
        request.headers.get('user-agent') || 'unknown',
        'user',
        updatedUser.id
      );
    } catch (logError) {
      console.error('Failed to log activity:', logError);
      // Continue execution even if logging fails
    }

    console.log(`[Users API] Updated user: ${updatedUser.username} by user: ${user!.username}`);
    return NextResponse.json(updatedUser);
  } catch (error) {
    console.error('Error updating user:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/admin/users/[id]
 * Delete a user
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;

    const resolvedParams = await params;
    const { id } = resolvedParams;

    console.log(`[Users API] DELETE user ${id} request from user: ${user!.username}`);

    // Check if user exists
    const existingUser = await prisma.user.findUnique({
      where: { id },
      include: { role: true }
    });

    if (!existingUser) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Prevent deleting the last admin user
    if (existingUser.role.name.toLowerCase() === 'admin') {
      const adminCount = await prisma.user.count({
        where: {
          role: { name: 'admin' },
          active: true
        }
      });

      if (adminCount <= 1) {
        return NextResponse.json(
          { error: 'Cannot delete the last admin user' },
          { status: 400 }
        );
      }
    }

    // Prevent self-deletion
    if (existingUser.id === user!.id) {
      return NextResponse.json(
        { error: 'Cannot delete your own account' },
        { status: 400 }
      );
    }

    // Check for foreign key constraints
    const [
      activityLogsCount,
      designRequestsCount
    ] = await Promise.all([
      prisma.activityLog.count({ where: { userId: id } }),
      prisma.designRequest.count({ where: { assignedTo: id } })
    ]);

    const totalReferences = activityLogsCount + designRequestsCount;

    if (totalReferences > 0) {
      const details = [];
      if (activityLogsCount > 0) details.push(`${activityLogsCount} activity log(s)`);
      if (designRequestsCount > 0) details.push(`${designRequestsCount} design request(s)`);

      return NextResponse.json({
        error: 'Cannot delete user',
        message: `User "${existingUser.username}" cannot be deleted because they have ${details.join(', ')}. Consider deactivating the user instead or reassigning their work.`,
        suggestion: 'You can deactivate this user to prevent login while preserving historical data.'
      }, { status: 409 });
    }

    // Delete the user
    await prisma.user.delete({
      where: { id }
    });

    // Log activity
    try {
      await logActivity(
        user!.id,
        'user_deleted',
        `User ${existingUser.username} deleted`,
        request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
        request.headers.get('user-agent') || 'unknown',
        'user',
        existingUser.id
      );
    } catch (logError) {
      console.error('Failed to log activity:', logError);
      // Continue execution even if logging fails
    }

    console.log(`[Users API] Deleted user: ${existingUser.username} by user: ${user!.username}`);
    return NextResponse.json({
      id: existingUser.id,
      username: existingUser.username,
      deletedAt: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error deleting user:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}
