import { auth } from "../../../../../auth";;
import { requireAdminAuth } from '@/lib/auth-helpers';;
import { NextRequest, NextResponse } from 'next/server';

import prisma from '@/lib/prisma';
import { hashPassword, logActivity } from '@/utils/passwordUtils';
import { z } from 'zod';

// Validation schema for user creation/update
const UserSchema = z.object({
  username: z.string().min(3).max(50),
  email: z.string().email().max(100),
  name: z.string().min(2).max(100).optional(),
  password: z.string().min(8).max(100).optional(),
  roleId: z.string().uuid(),
  active: z.boolean().default(true),
});

// GET handler - Get all users
export async function GET(request: NextRequest) {
  try {
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;

    console.log(`[Users API] GET request from user: ${user!.username}`);
    
    // Get users from database (exclude password hash)
    const users = await prisma.user.findMany({
      select: {
        id: true,
        username: true,
        email: true,
        name: true,
        active: true,
        lastLogin: true,
        createdAt: true,
        updatedAt: true,
        roleId: true,
        role: {
          select: {
            id: true,
            name: true,
            description: true,
          }
        }
      },
      orderBy: { username: 'asc' }
    });

    console.log(`[Users API] Retrieved ${users.length} users by user: ${user!.username}`);
    return NextResponse.json(users);
  } catch (error) {
    console.error('Error fetching users:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

// POST handler - Create a new user
export async function POST(request: NextRequest) {
  try {
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;

    console.log(`[Users API] POST request from user: ${user!.username}`);
    
    // Parse request body
    const data = await request.json();
    
    // Validate input
    const validationResult = UserSchema.safeParse(data);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Validation failed', details: validationResult.error.format() },
        { status: 400 }
      );
    }

    // Check if username or email already exists
    const existingUser = await prisma.user.findFirst({
      where: {
        OR: [
          { username: data.username },
          { email: data.email }
        ]
      }
    });

    if (existingUser) {
      return NextResponse.json(
        { error: 'Username or email already exists' },
        { status: 409 }
      );
    }

    // Check if role exists
    const role = await prisma.role.findUnique({
      where: { id: data.roleId }
    });

    if (!role) {
      return NextResponse.json(
        { error: 'Role not found' },
        { status: 404 }
      );
    }

    // Hash password
    if (!data.password) {
      return NextResponse.json(
        { error: 'Password is required' },
        { status: 400 }
      );
    }

    const passwordHash = await hashPassword(data.password);

    // Create user
    const newUser = await prisma.user.create({
      data: {
        username: data.username,
        email: data.email,
        name: data.name,
        passwordHash,
        roleId: data.roleId,
        active: data.active
      },
      select: {
        id: true,
        username: true,
        email: true,
        name: true,
        active: true,
        createdAt: true,
        updatedAt: true,
        roleId: true,
        role: {
          select: {
            id: true,
            name: true,
            description: true,
          }
        }
      }
    });

    // Log activity
    await logActivity(
      user!.id,
      'user_created',
      `User ${data.username} created`,
      request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
      request.headers.get('user-agent') || 'unknown',
      'user',
      newUser.id
    );

    console.log(`[Users API] Created user: ${newUser.id} by user: ${user!.username}`);
    return NextResponse.json(newUser, { status: 201 });
  } catch (error) {
    console.error('Error creating user:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}
