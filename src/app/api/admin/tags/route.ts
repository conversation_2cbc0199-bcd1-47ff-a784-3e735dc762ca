import { auth } from "../../../../../auth";;
import { requireAdminAuth } from '@/lib/auth-helpers';;
import { NextRequest, NextResponse } from 'next/server';

import prisma from '@/lib/prisma';

// GET handler - Get all tags with post counts
export async function GET(request: NextRequest) {
  try {
    // Get all blog posts to calculate tag counts
    const posts = await prisma.blogPost.findMany({
      select: {
        tags: true,
        status: true
      }
    });

    // Count tag occurrences
    const tagCounts: Record<string, { total: number; published: number }> = {};
    
    posts.forEach(post => {
      if (post.tags && Array.isArray(post.tags)) {
        post.tags.forEach(tag => {
          if (!tagCounts[tag]) {
            tagCounts[tag] = { total: 0, published: 0 };
          }
          tagCounts[tag].total++;
          if (post.status === 'published') {
            tagCounts[tag].published++;
          }
        });
      }
    });

    // Convert to array and sort by usage
    const tags = Object.entries(tagCounts)
      .map(([name, counts]) => ({
        id: name.toLowerCase().replace(/\s+/g, '-'),
        name,
        slug: name.toLowerCase().replace(/\s+/g, '-'),
        count: counts.total,
        publishedCount: counts.published,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }))
      .sort((a, b) => b.count - a.count);

    return NextResponse.json(tags);
  } catch (error) {
    console.error('Error fetching tags:', error);
    return NextResponse.json(
      { error: 'Failed to fetch tags' },
      { status: 500 }
    );
  }
}

// POST handler - Create a new tag (not typically needed as tags are created automatically)
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { name } = await request.json();

    if (!name || !name.trim()) {
      return NextResponse.json(
        { error: 'Tag name is required' },
        { status: 400 }
      );
    }

    // Tags are automatically created when used in blog posts
    // This endpoint is mainly for validation
    const tag = {
      id: name.toLowerCase().replace(/\s+/g, '-'),
      name: name.trim(),
      slug: name.toLowerCase().replace(/\s+/g, '-'),
      count: 0,
      publishedCount: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    return NextResponse.json(tag, { status: 201 });
  } catch (error) {
    console.error('Error creating tag:', error);
    return NextResponse.json(
      { error: 'Failed to create tag' },
      { status: 500 }
    );
  }
} 