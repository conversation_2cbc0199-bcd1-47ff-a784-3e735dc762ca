import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// GET - Fetch all logo packages (including inactive ones for admin)
export async function GET() {
  try {
    const packages = await prisma.logoPackage.findMany({
      orderBy: { sortOrder: 'asc' },
      include: {
        _count: {
          select: { logoOrders: true }
        }
      }
    });

    return NextResponse.json({
      success: true,
      data: packages
    });
  } catch (error) {
    console.error('Error fetching logo packages:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch logo packages' },
      { status: 500 }
    );
  }
}

// POST - Create new logo package
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    const {
      name,
      price,
      description,
      features,
      isActive = true,
      isPopular = false,
      sortOrder = 0,
      whatsappMessage
    } = body;

    // Validate required fields
    if (!name || !price) {
      return NextResponse.json(
        { success: false, error: 'Name and price are required' },
        { status: 400 }
      );
    }

    const newPackage = await prisma.logoPackage.create({
      data: {
        name,
        price: parseFloat(price),
        description,
        features: features || [],
        isActive,
        isPopular,
        sortOrder,
        whatsappMessage
      }
    });

    return NextResponse.json({
      success: true,
      data: newPackage
    });
  } catch (error) {
    console.error('Error creating logo package:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create logo package' },
      { status: 500 }
    );
  }
} 