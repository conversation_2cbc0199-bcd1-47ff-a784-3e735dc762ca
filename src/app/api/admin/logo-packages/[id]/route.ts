import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// GET - Fetch single logo package
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const pkg = await prisma.logoPackage.findUnique({
      where: { id }
    });

    if (!pkg) {
      return NextResponse.json(
        { success: false, error: 'Package not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: pkg
    });
  } catch (error) {
    console.error('Error fetching logo package:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch logo package' },
      { status: 500 }
    );
  }
}

// PUT - Update logo package
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const body = await request.json();

    // Check if package exists
    const existingPackage = await prisma.logoPackage.findUnique({
      where: { id }
    });

    if (!existingPackage) {
      return NextResponse.json(
        { success: false, error: 'Package not found' },
        { status: 404 }
      );
    }

    // Update the package
    const updatedPackage = await prisma.logoPackage.update({
      where: { id },
      data: body
    });

    return NextResponse.json({
      success: true,
      data: updatedPackage
    });
  } catch (error) {
    console.error('Error updating logo package:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update logo package' },
      { status: 500 }
    );
  }
}

// DELETE - Delete logo package
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    
    // Check if package exists
    const existingPackage = await prisma.logoPackage.findUnique({
      where: { id }
    });

    if (!existingPackage) {
      return NextResponse.json(
        { success: false, error: 'Package not found' },
        { status: 404 }
      );
    }

    // Delete the package
    await prisma.logoPackage.delete({
      where: { id }
    });

    return NextResponse.json({
      success: true,
      message: 'Package deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting logo package:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete logo package' },
      { status: 500 }
    );
  }
} 