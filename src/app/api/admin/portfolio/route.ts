import { auth } from "../../../../../auth";;
import { requireAdminAuth } from '@/lib/auth-helpers';;
import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';

import { S3Client, GetObjectCommand, PutObjectCommand } from '@aws-sdk/client-s3';
import { getDefaultStorageConfig } from '@/lib/storageConfig';
import { validatePortfolioItem, sanitizePortfolioItem } from '@/utils/portfolioUtils';

interface PortfolioMetadata {
  id: string;
  title: string;
  description?: string;
  category: string;
  imageSrc: string;
  alt?: string;
  featured?: boolean;
  createdAt: string;
  updatedAt: string;
  deletedAt?: string | null;
}

// S3 metadata file path
const PORTFOLIO_METADATA_KEY = 'portfolio-metadata.json';

// Get S3 client with the current configuration
async function getS3Client() {
  const config = await getDefaultStorageConfig();

  if (!config) {
    throw new Error('No storage configuration found');
  }

  console.log('Creating S3 client with config:', {
    region: config.region,
    endpoint: config.endpoint,
    bucketName: config.bucketName,
    // Don't log credentials
  });

  return new S3Client({
    region: config.region,
    endpoint: config.endpoint,
    credentials: {
      accessKeyId: config.accessKey,
      secretAccessKey: config.secretKey,
    },
    forcePathStyle: true // Required for some S3-compatible storage
  });
}

// Cache for portfolio metadata
let portfolioCache: PortfolioMetadata[] | null = null;
let portfolioCacheTimestamp: number = 0;
const PORTFOLIO_CACHE_DURATION = 3 * 60 * 1000; // 3 minutes

/**
 * Clear the portfolio metadata cache
 */
export function clearPortfolioCache(): void {
  portfolioCache = null;
  portfolioCacheTimestamp = 0;
}

// Function to read portfolio metadata from S3 - exported for use in other files
export async function readPortfolioMetadata(): Promise<PortfolioMetadata[]> {
  // Return cached data if still valid
  if (portfolioCache && Date.now() - portfolioCacheTimestamp < PORTFOLIO_CACHE_DURATION) {
    return portfolioCache;
  }

  try {
    const s3Client = await getS3Client();
    const config = await getDefaultStorageConfig();
    
    if (!config) {
      console.error('No storage configuration found');
      return [];
    }

    console.log('Reading portfolio metadata with config:', {
      bucket: config.bucketName,
      key: PORTFOLIO_METADATA_KEY
    });

    const command = new GetObjectCommand({
      Bucket: config.bucketName,
      Key: PORTFOLIO_METADATA_KEY,
    });

    const response = await s3Client.send(command);
    
    if (!response.Body) {
      console.log('No portfolio metadata found, returning empty array');
      portfolioCache = [];
      portfolioCacheTimestamp = Date.now();
      return [];
    }

    const bodyContents = await response.Body.transformToString();
    const portfolioItems: PortfolioMetadata[] = JSON.parse(bodyContents);
    
    const validItems = Array.isArray(portfolioItems) ? portfolioItems : [];
    
    // Cache the result
    portfolioCache = validItems;
    portfolioCacheTimestamp = Date.now();
    
    console.log(`Successfully read ${validItems.length} portfolio items`);
    return validItems;
  } catch (error) {
    if (error instanceof Error) {
      console.error('Error reading portfolio metadata from S3:', {
        name: error.name,
        message: error.message,
        stack: error.stack
      });
      
      if (error.name === 'NoSuchKey') {
        console.log('Portfolio metadata file does not exist, returning empty array');
        portfolioCache = [];
        portfolioCacheTimestamp = Date.now();
        return [];
      }
    } else {
      console.error('Unknown error reading portfolio metadata from S3:', error);
    }
    return [];
  }
}

// Helper function to write portfolio metadata to S3
async function writePortfolioMetadata(data: PortfolioMetadata[]): Promise<void> {
  try {
    const s3Client = await getS3Client();
    const config = await getDefaultStorageConfig();
    
    if (!config) {
      throw new Error('No storage configuration found');
    }

    const command = new PutObjectCommand({
      Bucket: config.bucketName,
      Key: PORTFOLIO_METADATA_KEY,
      Body: JSON.stringify(data, null, 2),
      ContentType: 'application/json',
      ACL: 'private', // Keep metadata private
    });

    await s3Client.send(command);
    
    // Clear cache after writing
    clearPortfolioCache();
    
    // Trigger automatic cache invalidation
    try {
      await invalidatePortfolioCache();
    } catch (cacheError) {
      console.warn('Failed to invalidate Next.js cache, but data was saved:', cacheError);
    }
  } catch (error) {
    console.error('Error writing portfolio metadata:', error);
    throw new Error('Failed to write portfolio metadata');
  }
}

// Helper function to invalidate Next.js cache
async function invalidatePortfolioCache(): Promise<void> {
  try {
    // Only import these in server environment
    const { revalidateTag, revalidatePath } = await import('next/cache');
    
    // Revalidate cache tags
    revalidateTag('portfolio-data');
    revalidateTag('portfolio-all');
    
    // Revalidate the portfolio page
    revalidatePath('/portfolio');
    
    console.log('Portfolio cache invalidated successfully');
  } catch (error) {
    console.error('Error invalidating portfolio cache:', error);
    // Don't throw here - cache invalidation failure shouldn't break the upload
  }
}

// GET handler for API endpoint
export async function GET(request: NextRequest) {
  try {
    // Check authentication using new system
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    console.log(`[Portfolio API] GET request from user: ${user.email}`);
    
    const items = await readPortfolioMetadata();
    
    // Filter out soft-deleted items for API response
    const activeItems = items.filter(item => !item.deletedAt);
    
    console.log(`[Portfolio API] Retrieved ${activeItems.length} portfolio items from S3`);
    return NextResponse.json(activeItems, {
      headers: {
        'Cache-Control': 'no-store, max-age=0',
        'Pragma': 'no-cache',
      },
    });
  } catch (error) {
    console.error('Error fetching portfolio items from S3:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

// POST handler - Create a new portfolio item
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    console.log(`[Portfolio API] POST request from user: ${user.email}`);

    // Parse request body
    let data;
    try {
      data = await request.json();
    } catch (parseError) {
      return NextResponse.json(
        { error: 'Invalid JSON in request body' },
        { status: 400 }
      );
    }

    // Create new portfolio item with required fields
    const newItem: PortfolioMetadata = {
      id: uuidv4(),
      title: data.title || '',
      description: data.description || '',
      category: data.category || '',
      imageSrc: data.imageSrc || '',
      alt: data.alt || data.title || '',
      featured: data.featured || false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    // Sanitize the new item
    const sanitizedItem = sanitizePortfolioItem(newItem);

    // Validate the new item
    const validation = validatePortfolioItem(sanitizedItem);
    if (!validation.isValid) {
      console.error('Portfolio item validation failed:', {
        errors: validation.errors,
        sanitizedItem: sanitizedItem
      });
      return NextResponse.json(
        {
          error: 'Invalid portfolio item data',
          details: validation.errors
        },
        { status: 400 }
      );
    }

    // Read existing portfolio metadata
    const portfolioItems = await readPortfolioMetadata();

    // Add the new item
    portfolioItems.push(sanitizedItem);

    // Write updated metadata to S3
    await writePortfolioMetadata(portfolioItems);

    console.log(`[Portfolio API] Created new portfolio item: ${sanitizedItem.id} (${sanitizedItem.title})`);

    return NextResponse.json(sanitizedItem, { status: 201 });
  } catch (error) {
    console.error('Error creating portfolio item:', error);

    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';

    return NextResponse.json(
      {
        error: 'Failed to create portfolio item',
        details: errorMessage,
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}