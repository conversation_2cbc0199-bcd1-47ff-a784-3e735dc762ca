import { auth } from "../../../../../../auth";
import { requireAdminAuth } from '@/lib/auth-helpers';
import { NextRequest, NextResponse } from 'next/server';

import { S3Client, GetObjectCommand, PutObjectCommand, DeleteObjectCommand } from '@aws-sdk/client-s3';
import { PortfolioItem } from '@/types/portfolio';
import { extractS3KeyFromUrl, validatePortfolioItem, sanitizePortfolioItem } from '@/utils/portfolioUtils';
import { getDefaultStorageConfig } from '@/lib/storageConfig';
import { revalidatePath } from 'next/cache';
import { PORTFOLIO_REVALIDATION_PATHS } from '@/config/revalidationPaths';
import { clearPortfolioCache } from '../route';

// S3 metadata file path
const PORTFOLIO_METADATA_KEY = 'portfolio-metadata.json';

// Get S3 client with the current configuration
async function getS3Client() {
  const config = await getDefaultStorageConfig();

  if (!config) {
    throw new Error('No storage configuration found');
  }

  return new S3Client({
    region: config.region,
    endpoint: config.endpoint,
    credentials: {
      accessKeyId: config.accessKey,
      secretAccessKey: config.secretKey,
    },
    forcePathStyle: true // Required for some S3-compatible storage
  });
}

// Get the current bucket name
async function getBucketName(): Promise<string> {
  const config = await getDefaultStorageConfig();
  return config?.bucketName || process.env.NEXT_PUBLIC_S3_BUCKET || 'mocky';
}

// Helper function to read portfolio metadata from S3
async function readPortfolioMetadata(): Promise<PortfolioItem[]> {
  try {
    const s3Client = await getS3Client();
    const bucketName = await getBucketName();

    const command = new GetObjectCommand({
      Bucket: bucketName,
      Key: PORTFOLIO_METADATA_KEY,
    });

    const response = await s3Client.send(command);
    const metadataContent = await response.Body?.transformToString();

    if (metadataContent) {
      return JSON.parse(metadataContent);
    }

    return [];
  } catch (error) {
    console.error('Error reading portfolio metadata:', error);
    return [];
  }
}

// Helper function to write portfolio metadata to S3
async function writePortfolioMetadata(data: PortfolioItem[]): Promise<void> {
  try {
    const s3Client = await getS3Client();
    const bucketName = await getBucketName();

    const command = new PutObjectCommand({
      Bucket: bucketName,
      Key: PORTFOLIO_METADATA_KEY,
      Body: JSON.stringify(data, null, 2),
      ContentType: 'application/json',
      ACL: 'private', // Keep metadata private
    });

    await s3Client.send(command);
    
    // Clear cache after writing - this was missing and causing the bug!
    clearPortfolioCache();
    console.log('Portfolio cache cleared after writing metadata');
  } catch (error) {
    console.error('Error writing portfolio metadata:', error);
    throw new Error('Failed to write portfolio metadata');
  }
}

// GET handler - Get a specific portfolio item by ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Await params before accessing properties
    const { id } = await params;

    // Read portfolio metadata
    const portfolioItems = await readPortfolioMetadata();

    // Find the item with the specified ID
    const item = portfolioItems.find(item => item.id === id);

    if (!item) {
      return NextResponse.json(
        { error: 'Portfolio item not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(item);
  } catch (error) {
    console.error('Error in GET portfolio item:', error);
    return NextResponse.json(
      { error: 'Failed to fetch portfolio item' },
      { status: 500 }
    );
  }
}

// PUT handler - Update a specific portfolio item by ID
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Await params before accessing properties
    const { id } = await params;

    if (!id || typeof id !== 'string') {
      return NextResponse.json(
        { error: 'Invalid portfolio item ID' },
        { status: 400 }
      );
    }

    // Parse request body
    let data;
    try {
      data = await request.json();
    } catch (parseError) {
      return NextResponse.json(
        { error: 'Invalid JSON in request body' },
        { status: 400 }
      );
    }

    console.log(`Attempting to update portfolio item with ID: ${id}`);

    // Read portfolio metadata
    const portfolioItems = await readPortfolioMetadata();

    // Find the item to update
    const itemIndex = portfolioItems.findIndex(item => item.id === id);
    if (itemIndex === -1) {
      return NextResponse.json(
        { error: 'Portfolio item not found' },
        { status: 404 }
      );
    }
    const existingItem = portfolioItems[itemIndex];
    
    if (!existingItem) {
      return NextResponse.json(
        { error: 'Portfolio item not found' },
        { status: 404 }
      );
    }

    // Optimistic locking: check updatedAt
    if (data.updatedAt && existingItem.updatedAt !== data.updatedAt) {
      return NextResponse.json(
        { error: 'Portfolio item was modified by another user. Please refresh and try again.' },
        { status: 409 }
      );
    }

    // Create updated item with validation
    const updatedItemData = {
      ...existingItem,
      title: data.title || existingItem.title,
      description: data.description !== undefined ? data.description : existingItem.description,
      category: data.category || existingItem.category,
      imageSrc: data.imageSrc || existingItem.imageSrc,
      alt: data.alt || existingItem.alt || data.title || existingItem.title,
      featured: data.featured !== undefined ? data.featured : existingItem.featured,
      updatedAt: new Date().toISOString(),
    };

    // Sanitize the updated data
    const sanitizedItem = sanitizePortfolioItem(updatedItemData);

    // Validate the updated item
    const validation = validatePortfolioItem(sanitizedItem);
    if (!validation.isValid) {
      return NextResponse.json(
        {
          error: 'Invalid portfolio item data',
          details: validation.errors
        },
        { status: 400 }
      );
    }

    // Replace the item in the array
    portfolioItems[itemIndex] = sanitizedItem;

    // Write updated metadata to S3
    await writePortfolioMetadata(portfolioItems);

    // Additional cache invalidation to ensure updates are reflected immediately
    try {
      const { revalidateTag } = await import('next/cache');
      revalidateTag('portfolio-data');
      revalidateTag('portfolio-all');
      console.log('Portfolio cache tags revalidated after update');
    } catch (cacheError) {
      console.warn('Failed to invalidate cache tags:', cacheError);
    }

    // Revalidate portfolio paths
    PORTFOLIO_REVALIDATION_PATHS.forEach(path => revalidatePath(path));
    console.log('Portfolio cache reset after update');

    console.log(`Successfully updated portfolio item: ${id} (${sanitizedItem.title})`);

    return NextResponse.json(sanitizedItem);
  } catch (error) {
    console.error('Error in PUT portfolio item:', error);

    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';

    return NextResponse.json(
      {
        error: 'Failed to update portfolio item',
        details: errorMessage,
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}

// DELETE handler - Delete a specific portfolio item by ID
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Await params before accessing properties
    const { id } = await params;

    if (!id || typeof id !== 'string') {
      return NextResponse.json(
        { error: 'Invalid portfolio item ID' },
        { status: 400 }
      );
    }

    console.log(`Attempting to delete portfolio item with ID: ${id}`);

    // Read portfolio metadata
    const portfolioItems = await readPortfolioMetadata();

    // Parse request body for updatedAt
    let clientUpdatedAt: string | undefined = undefined;
    try {
      const body = await request.json();
      clientUpdatedAt = body.updatedAt;
    } catch {}

    // Find the item to delete
    const itemToDelete = portfolioItems.find(item => item.id === id);
    if (!itemToDelete) {
      return NextResponse.json(
        { error: 'Portfolio item not found' },
        { status: 404 }
      );
    }

    // Optimistic locking: check updatedAt
    if (clientUpdatedAt && itemToDelete.updatedAt !== clientUpdatedAt) {
      return NextResponse.json(
        { error: 'Portfolio item was modified by another user. Please refresh and try again.' },
        { status: 409 }
      );
    }

    // Soft delete: set deletedAt timestamp instead of removing
    const updatedItems = portfolioItems.map(item =>
      item.id === id ? { ...item, deletedAt: new Date().toISOString() } : item
    );

    // Write updated metadata to S3
    await writePortfolioMetadata(updatedItems);

    // Additional cache invalidation to ensure deleted items don't show up
    try {
      const { revalidateTag } = await import('next/cache');
      revalidateTag('portfolio-data');
      revalidateTag('portfolio-all');
      console.log('Portfolio cache tags revalidated after deletion');
    } catch (cacheError) {
      console.warn('Failed to invalidate cache tags:', cacheError);
    }

    // Revalidate Next.js static pages
    PORTFOLIO_REVALIDATION_PATHS.forEach(path => revalidatePath(path));
    console.log('Next.js paths revalidated after deletion:', PORTFOLIO_REVALIDATION_PATHS);

    console.log(`Successfully deleted portfolio item: ${id}`);

    return NextResponse.json({
      success: true,
      message: 'Portfolio item deleted successfully',
      deletedItem: {
        id: id,
        title: portfolioItems.find(item => item.id === id)?.title || 'Unknown'
      }
    });
  } catch (error) {
    console.error('Error in DELETE portfolio item:', error);

    // Provide more detailed error information
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';

    return NextResponse.json(
      {
        error: 'Failed to delete portfolio item',
        details: errorMessage,
        timestamp: new Date().toISOString()
      },
      { status: 500 }
    );
  }
}
