import { auth } from "../../../../../auth";;;
import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function GET() {
  try {
    const paperTypes = await prisma.paperType.findMany({
      orderBy: { order: 'asc' }
    });
    
    return NextResponse.json(paperTypes);
  } catch (error) {
    console.error('Paper types fetch error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch paper types' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    const paperType = await prisma.paperType.create({
      data: {
        name: body.name,
        grammage: body.grammage || '',
        oneSidedPrice: parseFloat(body.oneSidedPrice),
        twoSidedPrice: parseFloat(body.twoSidedPrice),
        category: body.category || 'paper',
        active: body.active !== false,
        order: body.order || 0
      }
    });
    
    return NextResponse.json(paperType, { status: 201 });
  } catch (error) {
    console.error('Paper type creation error:', error);
    return NextResponse.json(
      { error: 'Failed to create paper type' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    
    if (!body.id) {
      return NextResponse.json(
        { error: 'Paper type ID is required' },
        { status: 400 }
      );
    }
    
    const paperType = await prisma.paperType.update({
      where: { id: body.id },
      data: {
        name: body.name,
        grammage: body.grammage,
        oneSidedPrice: parseFloat(body.oneSidedPrice),
        twoSidedPrice: parseFloat(body.twoSidedPrice),
        category: body.category,
        active: body.active,
        order: body.order
      }
    });
    
    return NextResponse.json(paperType);
  } catch (error) {
    console.error('Paper type update error:', error);
    return NextResponse.json(
      { error: 'Failed to update paper type' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');
    
    if (!id) {
      return NextResponse.json(
        { error: 'Paper type ID is required' },
        { status: 400 }
      );
    }
    
    await prisma.paperType.delete({
      where: { id }
    });
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Paper type deletion error:', error);
    return NextResponse.json(
      { error: 'Failed to delete paper type' },
      { status: 500 }
    );
  }
} 