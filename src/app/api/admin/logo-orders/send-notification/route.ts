import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { sendEmail, generateOrderNotificationEmail } from '@/utils/email';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { orderId } = body;

    if (!orderId) {
      return NextResponse.json(
        { success: false, error: 'Order ID is required' },
        { status: 400 }
      );
    }

    // Fetch the order with package details
    const order = await prisma.logoOrder.findUnique({
      where: { id: orderId },
      include: {
        package: true
      }
    });

    if (!order) {
      return NextResponse.json(
        { success: false, error: 'Order not found' },
        { status: 404 }
      );
    }

    // Send email notification
    try {
      const emailResult = await sendEmail(generateOrderNotificationEmail(order));
      console.log('Email notification result:', emailResult);
      
      // Update order to mark notification as sent
      await prisma.logoOrder.update({
        where: { id: orderId },
        data: { adminNotified: true }
      });

      return NextResponse.json({
        success: true,
        message: 'Email notification sent successfully'
      });
    } catch (emailError) {
      console.error('Failed to send email notification:', emailError);
      return NextResponse.json(
        { success: false, error: 'Failed to send email notification' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('Error sending notification:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
} 