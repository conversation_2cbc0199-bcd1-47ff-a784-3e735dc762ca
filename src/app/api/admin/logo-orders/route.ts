import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// GET - Fetch all logo orders (Admin)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const limit = searchParams.get('limit');
    const offset = searchParams.get('offset');

    const whereClause: any = {};
    if (status) {
      whereClause.status = status;
    }

    const orders = await prisma.logoOrder.findMany({
      where: whereClause,
      include: {
        package: true
      },
      orderBy: { createdAt: 'desc' },
      take: limit ? parseInt(limit) : undefined,
      skip: offset ? parseInt(offset) : undefined
    });

    const totalCount = await prisma.logoOrder.count({ where: whereClause });

    return NextResponse.json({
      success: true,
      data: orders,
      pagination: {
        total: totalCount,
        limit: limit ? parseInt(limit) : totalCount,
        offset: offset ? parseInt(offset) : 0
      }
    });
  } catch (error) {
    console.error('Error fetching logo orders:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch logo orders' },
      { status: 500 }
    );
  }
} 