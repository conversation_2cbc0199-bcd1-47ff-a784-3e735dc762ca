import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// POST - Handle bulk operations
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, orderIds, data } = body;

    if (!action || !orderIds || !Array.isArray(orderIds) || orderIds.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Invalid request data' },
        { status: 400 }
      );
    }

    let result;

    switch (action) {
      case 'delete':
        // Bulk delete orders
        result = await (prisma as any).logoOrder.deleteMany({
          where: {
            id: {
              in: orderIds
            }
          }
        });
        
        return NextResponse.json({
          success: true,
          message: `Successfully deleted ${result.count} orders`,
          deletedCount: result.count
        });

      case 'updateStatus':
        // Bulk update order status
        if (!data || !data.status) {
          return NextResponse.json(
            { success: false, error: 'Status is required for bulk status update' },
            { status: 400 }
          );
        }

        const updateData: any = { status: data.status };
        
        // If setting to completed, add completedAt timestamp
        if (data.status === 'completed') {
          updateData.completedAt = new Date();
        }

        result = await (prisma as any).logoOrder.updateMany({
          where: {
            id: {
              in: orderIds
            }
          },
          data: updateData
        });

        return NextResponse.json({
          success: true,
          message: `Successfully updated ${result.count} orders to ${data.status}`,
          updatedCount: result.count
        });

      case 'updatePaymentStatus':
        // Bulk update payment status
        if (!data || !data.paymentStatus) {
          return NextResponse.json(
            { success: false, error: 'Payment status is required for bulk payment status update' },
            { status: 400 }
          );
        }

        result = await (prisma as any).logoOrder.updateMany({
          where: {
            id: {
              in: orderIds
            }
          },
          data: {
            paymentStatus: data.paymentStatus
          }
        });

        return NextResponse.json({
          success: true,
          message: `Successfully updated payment status for ${result.count} orders`,
          updatedCount: result.count
        });

      case 'markNotified':
        // Bulk mark as admin notified
        result = await (prisma as any).logoOrder.updateMany({
          where: {
            id: {
              in: orderIds
            }
          },
          data: {
            adminNotified: true
          }
        });

        return NextResponse.json({
          success: true,
          message: `Successfully marked ${result.count} orders as notified`,
          updatedCount: result.count
        });

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid bulk action' },
          { status: 400 }
        );
    }

  } catch (error) {
    console.error('Error in bulk operation:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to execute bulk operation' },
      { status: 500 }
    );
  }
} 