import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// GET - Fetch single logo order
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const order = await (prisma as any).logoOrder.findUnique({
      where: { id },
      include: {
        package: true
      }
    });

    if (!order) {
      return NextResponse.json(
        { success: false, error: 'Order not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: order
    });
  } catch (error) {
    console.error('Error fetching logo order:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch logo order' },
      { status: 500 }
    );
  }
}

// PUT - Update logo order
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const body = await request.json();
    
    const updateData: any = {};
    
    // Only update fields that are provided
    if (body.status !== undefined) updateData.status = body.status;
    if (body.paymentStatus !== undefined) updateData.paymentStatus = body.paymentStatus;
    if (body.notes !== undefined) updateData.notes = body.notes;
    if (body.revisionCount !== undefined) updateData.revisionCount = body.revisionCount;
    if (body.designFiles !== undefined) updateData.designFiles = body.designFiles;
    if (body.adminNotified !== undefined) updateData.adminNotified = body.adminNotified;
    if (body.whatsappSent !== undefined) updateData.whatsappSent = body.whatsappSent;
    
    // Customer information fields (for design brief form updates)
    if (body.customerName !== undefined) updateData.customerName = body.customerName;
    if (body.email !== undefined) updateData.email = body.email;
    if (body.phone !== undefined) updateData.phone = body.phone;
    if (body.businessName !== undefined) updateData.businessName = body.businessName;
    if (body.industry !== undefined) updateData.industry = body.industry;
    if (body.logoType !== undefined) updateData.logoType = body.logoType;
    if (body.slogan !== undefined) updateData.slogan = body.slogan;
    if (body.additionalInfo !== undefined) updateData.additionalInfo = body.additionalInfo;
    
    // If status is being set to completed, set completedAt
    if (body.status === 'completed') {
      updateData.completedAt = new Date();
    }

    const updatedOrder = await (prisma as any).logoOrder.update({
      where: { id },
      data: updateData,
      include: {
        package: true
      }
    });

    return NextResponse.json({
      success: true,
      data: updatedOrder
    });
  } catch (error) {
    console.error('Error updating logo order:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update logo order' },
      { status: 500 }
    );
  }
}

// DELETE - Delete logo order
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    
    // Check if order exists
    const existingOrder = await (prisma as any).logoOrder.findUnique({
      where: { id },
    });

    if (!existingOrder) {
      return NextResponse.json(
        { success: false, error: 'Logo order not found' },
        { status: 404 }
      );
    }

    // Delete the order
    await (prisma as any).logoOrder.delete({
      where: { id },
    });

    return NextResponse.json({
      success: true,
      message: 'Logo order deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting logo order:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to delete logo order' },
      { status: 500 }
    );
  }
} 