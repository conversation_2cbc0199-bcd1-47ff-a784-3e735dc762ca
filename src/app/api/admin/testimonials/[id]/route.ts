import { auth } from "../../../../../../auth";
import { requireAdminAuth } from '@/lib/auth-helpers';
import { NextRequest, NextResponse } from 'next/server';

import { prisma } from '@/lib/prisma';

/**
 * GET /api/admin/testimonials/[id]
 * Get a specific testimonial by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const testimonialId = params.id;
    console.log(`[Testimonial API] GET request for testimonial ${testimonialId} from user: ${user.username}`);

    if (!testimonialId) {
      return NextResponse.json({
        success: false,
        error: 'Testimonial ID is required'
      }, { status: 400 });
    }

    const testimonial = await prisma.testimonial.findUnique({
      where: { id: testimonialId }
    });

    if (!testimonial) {
      return NextResponse.json({
        success: false,
        error: 'Testimonial not found'
      }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      data: testimonial
    });
  } catch (error) {
    console.error('Error getting testimonial:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to get testimonial',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * PUT /api/admin/testimonials/[id]
 * Update a testimonial
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const testimonialId = params.id;
    console.log(`[Testimonial API] PUT request for testimonial ${testimonialId} from user: ${user.username}`);

    if (!testimonialId) {
      return NextResponse.json({
        success: false,
        error: 'Testimonial ID is required'
      }, { status: 400 });
    }

    const body = await request.json();

    const testimonial = await prisma.testimonial.update({
      where: { id: testimonialId },
      data: {
        name: body.name,
        content: body.content,
        company: body.company || null,
        position: body.position || null,
        rating: body.rating || 5,
        isActive: body.isActive !== undefined ? body.isActive : true
      }
    });

    return NextResponse.json({
      success: true,
      data: testimonial,
      message: 'Testimonial updated successfully'
    });
  } catch (error) {
    console.error('Error updating testimonial:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to update testimonial',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * DELETE /api/admin/testimonials/[id]
 * Delete a testimonial
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const testimonialId = params.id;
    console.log(`[Testimonial API] DELETE request for testimonial ${testimonialId} from user: ${user.username}`);

    if (!testimonialId) {
      return NextResponse.json({
        success: false,
        error: 'Testimonial ID is required'
      }, { status: 400 });
    }

    await prisma.testimonial.delete({
      where: { id: testimonialId }
    });

    return NextResponse.json({
      success: true,
      message: 'Testimonial deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting testimonial:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to delete testimonial',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
