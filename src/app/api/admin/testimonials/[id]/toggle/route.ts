import { auth } from "../../../../../../../auth";
import { requireAdminAuth } from '@/lib/auth-helpers';
import { NextRequest, NextResponse } from 'next/server';

import { prisma } from '@/lib/prisma';

/**
 * POST /api/admin/testimonials/[id]/toggle
 * Toggle testimonial active status
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const testimonialId = params.id;
    console.log(`[Testimonial Toggle API] POST request for testimonial ${testimonialId} from user: ${user.username}`);

    if (!testimonialId) {
      return NextResponse.json({
        success: false,
        error: 'Testimonial ID is required'
      }, { status: 400 });
    }

    // Get current testimonial
    const currentTestimonial = await prisma.testimonial.findUnique({
      where: { id: testimonialId }
    });

    if (!currentTestimonial) {
      return NextResponse.json({
        success: false,
        error: 'Testimonial not found'
      }, { status: 404 });
    }

    // Toggle the active status
    const testimonial = await prisma.testimonial.update({
      where: { id: testimonialId },
      data: {
        isActive: !currentTestimonial.isActive
      }
    });

    return NextResponse.json({
      success: true,
      data: testimonial,
      message: `Testimonial ${testimonial.isActive ? 'activated' : 'deactivated'} successfully`
    });
  } catch (error) {
    console.error('Error toggling testimonial status:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to toggle testimonial status',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
