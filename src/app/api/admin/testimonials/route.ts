import { auth } from "../../../../../auth";;
import { requireAdminAuth } from '@/lib/auth-helpers';;
import { NextRequest, NextResponse } from 'next/server';

import { prisma } from '@/lib/prisma';

/**
 * GET /api/admin/testimonials
 * Get all testimonials
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    console.log(`[Testimonials API] GET request from user: ${user.username}`);

    const testimonials = await prisma.testimonial.findMany({
      orderBy: {
        createdAt: 'desc'
      }
    });

    return NextResponse.json({
      success: true,
      data: testimonials,
      count: testimonials.length
    });
  } catch (error) {
    console.error('Error getting testimonials:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to get testimonials',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * POST /api/admin/testimonials
 * Create a new testimonial
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    console.log(`[Testimonials API] POST request from user: ${user.username}`);

    const body = await request.json();

    // Validate required fields
    if (!body.name || !body.testimonial) {
      return NextResponse.json({
        success: false,
        error: 'Name and testimonial are required'
      }, { status: 400 });
    }

    const testimonial = await prisma.testimonial.create({
      data: {
        name: body.name,
        testimonial: body.testimonial,
        company: body.company || null,
        location: body.location || '',
        project: body.project || '',
        rating: body.rating || 5,
        active: body.active !== undefined ? body.active : true
      }
    });

    return NextResponse.json({
      success: true,
      data: testimonial,
      message: 'Testimonial created successfully'
    });
  } catch (error) {
    console.error('Error creating testimonial:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to create testimonial',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * PUT /api/admin/testimonials
 * Update a testimonial
 */
export async function PUT(request: NextRequest) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    console.log(`[Testimonials API] PUT request from user: ${user.username}`);

    const body = await request.json();

    // Validate required fields
    if (!body.id) {
      return NextResponse.json({
        success: false,
        error: 'Testimonial ID is required'
      }, { status: 400 });
    }

    const testimonial = await prisma.testimonial.update({
      where: { id: body.id },
      data: {
        name: body.name,
        testimonial: body.testimonial,
        company: body.company || null,
        location: body.location || '',
        project: body.project || '',
        rating: body.rating || 5,
        active: body.active !== undefined ? body.active : true
      }
    });

    return NextResponse.json({
      success: true,
      data: testimonial,
      message: 'Testimonial updated successfully'
    });
  } catch (error) {
    console.error('Error updating testimonial:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to update testimonial',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
