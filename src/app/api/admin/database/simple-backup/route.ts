import { NextRequest, NextResponse } from 'next/server';
import { exec } from 'child_process';
import { promisify } from 'util';
import { mkdir, readdir, stat, unlink } from 'fs/promises';
import { join } from 'path';
import { auth } from '../../../../../../auth';

const execAsync = promisify(exec);

const BACKUP_DIR = join(process.cwd(), 'backups');

// Ensure backup directory exists
async function ensureBackupDir() {
  try {
    await mkdir(BACKUP_DIR, { recursive: true });
  } catch (error) {
    // Directory might already exist
  }
}

// GET - List all backup files
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user || session.user.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await ensureBackupDir();

    // Read backup directory
    const files = await readdir(BACKUP_DIR);
    const backupFiles = files.filter(file => file.endsWith('.sql'));

    const backups = await Promise.all(
      backupFiles.map(async (filename) => {
        const filePath = join(BACKUP_DIR, filename);
        const stats = await stat(filePath);
        
        return {
          id: filename.replace('.sql', ''),
          filename,
          size: stats.size,
          createdAt: stats.birthtime.toISOString(),
          description: `Database backup created on ${stats.birthtime.toLocaleDateString()}`
        };
      })
    );

    // Sort by creation date (newest first)
    backups.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

    return NextResponse.json({
      success: true,
      backups
    });

  } catch (error) {
    console.error('Error fetching backups:', error);
    return NextResponse.json({ error: 'Failed to fetch backups' }, { status: 500 });
  }
}

// POST - Create a new backup
export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user || session.user.role !== 'admin') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { description } = await request.json();
    
    await ensureBackupDir();

    // Generate backup filename with timestamp
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `backup_${timestamp}.sql`;
    const filePath = join(BACKUP_DIR, filename);

    // Get database URL from environment
    const databaseUrl = process.env.DATABASE_URL;
    if (!databaseUrl) {
      return NextResponse.json({ error: 'Database URL not configured' }, { status: 500 });
    }

    // Create PostgreSQL dump
    const command = `pg_dump "${databaseUrl}" > "${filePath}"`;
    
    console.log('Creating database backup:', filename);
    await execAsync(command);

    // Verify backup was created
    const stats = await stat(filePath);
    
    return NextResponse.json({
      success: true,
      backup: {
        id: filename.replace('.sql', ''),
        filename,
        size: stats.size,
        createdAt: stats.birthtime.toISOString(),
        description: description || `Database backup created on ${new Date().toLocaleDateString()}`
      }
    });

  } catch (error) {
    console.error('Error creating backup:', error);
    return NextResponse.json({ error: 'Failed to create backup' }, { status: 500 });
  }
} 