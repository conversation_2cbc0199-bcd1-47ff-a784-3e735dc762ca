import { auth } from "../../../../../../auth";
import { requireAdminAuth } from '@/lib/auth-helpers';
import { NextRequest, NextResponse } from 'next/server';


import { createEnterpriseBackup, getBackupMetrics } from '@/services/enterpriseBackupService';

// Helper function to convert BigInt values to strings for JSON serialization
const serializeForJson = (obj: any): any => {
  if (obj === null || obj === undefined) {
    return obj;
  }
  
  if (typeof obj === 'bigint') {
    return obj.toString();
  }
  
  if (typeof obj === 'object') {
    if (Array.isArray(obj)) {
      return obj.map(serializeForJson);
    }
    
    const result: any = {};
    for (const key in obj) {
      result[key] = serializeForJson(obj[key]);
    }
    return result;
  }
  
  return obj;
};

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request); if (response) return response;
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { 
      description, 
      type = 'manual',
    } = body;

    console.log(`🚀 [Simple Backup API] Starting backup for user: ${user.email}`);

    const result = await createEnterpriseBackup(user.email || 'unknown', {
      description,
      type,
    });

    // Serialize the result to handle BigInt values
    const serializedResult = serializeForJson(result);

    return NextResponse.json({
      success: true,
      backup: serializedResult.backup,
      metrics: serializedResult.metrics,
      cloudPath: serializedResult.cloudPath,
      message: 'Simple backup created successfully'
    });

  } catch (error) {
    console.error('Enterprise backup error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to create simple backup',
        details: error instanceof Error ? error.message : 'Unknown error'
      }, 
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request); if (response) return response;
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const metrics = await getBackupMetrics();
    
    // Serialize metrics to handle any BigInt values
    const serializedMetrics = serializeForJson(metrics);

    return NextResponse.json({
      success: true,
      metrics: serializedMetrics
    });

  } catch (error) {
    console.error('Backup metrics error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to get backup metrics',
        details: error instanceof Error ? error.message : 'Unknown error'
      }, 
      { status: 500 }
    );
  }
} 