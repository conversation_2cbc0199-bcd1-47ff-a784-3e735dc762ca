import { auth } from "../../../../../../auth";
import { requireAdminAuth } from '@/lib/auth-helpers';
import { NextRequest, NextResponse } from 'next/server';


import { 
  listS3Backups, 
  getS3BackupMetadata, 
  restoreFromS3Backup 
} from '@/services/enterpriseBackupService';

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request); if (response) return response;
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const url = new URL(request.url);
    const cloudKey = url.searchParams.get('key');

    // If a specific key is requested, get metadata
    if (cloudKey) {
      try {
        const metadata = await getS3BackupMetadata(cloudKey);
        return NextResponse.json({
          success: true,
          metadata
        });
      } catch (error) {
        return NextResponse.json(
          { 
            error: 'Failed to get backup metadata',
            details: error instanceof Error ? error.message : 'Unknown error'
          }, 
          { status: 404 }
        );
      }
    }

    // Otherwise, list all S3 backups
    console.log(`☁️ [S3 Backups API] Listing S3 backups for user: ${user.email}`);
    
    const s3Backups = await listS3Backups();

    return NextResponse.json({
      success: true,
      backups: s3Backups,
      count: s3Backups.length
    });

  } catch (error) {
    console.error('S3 backups listing error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to list S3 backups',
        details: error instanceof Error ? error.message : 'Unknown error'
      }, 
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request); if (response) return response;
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { 
      cloudKey, 
      action = 'restore',
      createPreBackup = true,
      verifyIntegrity = true,
      description
    } = body;

    if (!cloudKey) {
      return NextResponse.json({ error: 'Cloud key is required' }, { status: 400 });
    }

    if (action !== 'restore') {
      return NextResponse.json({ error: 'Only restore action is supported' }, { status: 400 });
    }

    console.log(`☁️ [S3 Restore API] Starting restore from S3: ${cloudKey} for user: ${user.email}`);

    const result = await restoreFromS3Backup(
      cloudKey, 
      user.email || 'unknown',
      {
        createPreBackup,
        verifyIntegrity,
        description
      }
    );

    return NextResponse.json({
      success: true,
      message: `Database restored successfully from S3 backup: ${cloudKey}`,
      preBackupId: result.preBackupId,
      downloadedFile: result.downloadedFile
    });

  } catch (error) {
    console.error('S3 restore error:', error);
    return NextResponse.json(
      { 
        error: 'Failed to restore from S3 backup',
        details: error instanceof Error ? error.message : 'Unknown error'
      }, 
      { status: 500 }
    );
  }
} 