import { auth } from "../../../../../../../auth";
import { NextRequest, NextResponse } from 'next/server';

import { getDatabaseBackupById, deleteDatabaseBackup } from '@/services/databaseBackupService';
import { requireAdminAuth } from '@/lib/auth-helpers';

// Helper function to serialize BigInt values
function serializeBigInt(obj: any): any {
  if (obj === null || obj === undefined) {
    return obj;
  }
  
  if (typeof obj === 'bigint') {
    return obj.toString();
  }
  
  if (Array.isArray(obj)) {
    return obj.map(serializeBigInt);
  }
  
  if (typeof obj === 'object') {
    const serialized: any = {};
    for (const [key, value] of Object.entries(obj)) {
      serialized[key] = serializeBigInt(value);
    }
    return serialized;
  }
  
  return obj;
}

// GET handler
async function getDatabaseBackup(request: NextRequest, session: any, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params;
    console.log(`[Database Backup API] GET request for backup ${id} from user: ${session.user.username}`);
    const backup = await getDatabaseBackupById(id);
    if (!backup) {
      return NextResponse.json({ error: 'Backup not found' }, { status: 404 });
    }
    console.log(`[Database Backup API] Retrieved backup ${id} by user: ${session.user.username}`);
    return NextResponse.json(serializeBigInt(backup));
  } catch (error) {
    console.error('Error fetching database backup:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to fetch database backup' },
      { status: 500 }
    );
  }
}

// DELETE handler  
async function deleteDatabaseBackupHandler(request: NextRequest, session: any, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params;
    console.log(`[Database Backup API] DELETE request for backup ${id} from user: ${session.user.username}`);
    
    // Delete the backup
    const success = await deleteDatabaseBackup(id);
    if (!success) {
      return NextResponse.json({ error: 'Failed to delete backup or backup not found' }, { status: 404 });
    }
    
    console.log(`[Database Backup API] Deleted backup ${id} by user: ${session.user.username}`);
    return NextResponse.json({ 
      success: true,
      message: 'Backup deleted successfully' 
    });
  } catch (error) {
    console.error('Error deleting database backup:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Failed to delete database backup' },
      { status: 500 }
    );
  }
}

// Export handlers with enhanced authentication
export async function GET(request: NextRequest, context?: any) {
  const { user, response } = await requireAdminAuth(request);
  if (response) return response;
  
  const session = { user };
  return getDatabaseBackup(request, session, context);
}

export async function DELETE(request: NextRequest, context?: any) {
  const { user, response } = await requireAdminAuth(request);
  if (response) return response;
  
  const session = { user };
  return deleteDatabaseBackupHandler(request, session, context);
} 
