import { auth } from "../../../../../../../../auth";
import { NextRequest, NextResponse } from 'next/server';

import { successResponse, errorResponse, handleAPIError } from '@/utils/apiResponse';
import { logger } from '@/utils/logger';
import { withTransaction } from '@/utils/withTransaction';
import { ValidationError } from '@/errors/ApiErrors';
import prisma from '@/lib/prisma';
import fs from 'fs';
import path from 'path';
import { requireAdminAuth } from '@/lib/auth-helpers';

/**
 * GET /api/admin/database/backup/[id]/download
 * Enhanced backup download with comprehensive security and logging
 */
async function downloadBackup(
  request: NextRequest,
  session: any,
  params: Promise<{ id: string }>
) {
  const correlationId = request.headers.get('x-correlation-id') || 'unknown';
  const startTime = Date.now();
  
  try {
    const resolvedParams = await params;
    const { id: backupId } = resolvedParams;
    logger.info('Database backup download started', {
      requestId: correlationId,
      action: 'download_backup',
      userId: session.user.id,
      resourceId: backupId
    });

    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(backupId)) {
      throw new ValidationError('Invalid backup ID format', 'INVALID_ID_FORMAT');
    }

    // Get backup record with transaction safety
    const backup = await withTransaction(async (tx) => {
      return await tx.databaseBackup.findUnique({
        where: { id: backupId },
        select: {
          id: true,
          filename: true,
          filePath: true,
          fileSize: true,
          status: true,
          createdAt: true,
          createdBy: true
        }
      });
    });

    if (!backup) {
      logger.business('Database backup not found', {
        requestId: correlationId,
        action: 'backup_not_found',
        userId: session.user.id,
        resourceId: backupId
      });
      
      throw new ValidationError('Database backup not found', 'RESOURCE_NOT_FOUND');
    }

    // Security check: only allow downloading completed backups
    if (backup.status !== 'completed') {
      logger.security('suspicious_activity', {
        metadata: {
          action: 'attempted_incomplete_backup_download',
          userId: session.user.id,
          backupId,
          backupStatus: backup.status
        },
        ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || undefined,
        userAgent: request.headers.get('user-agent') || undefined
      });

      throw new ValidationError('Backup is not ready for download', 'BACKUP_NOT_READY');
    }

    // Construct file path safely
    const backupDir = process.env.BACKUP_DIR || path.join(process.cwd(), 'backups');
    const filePath = path.resolve(backupDir, backup.filename);
    
    // Security check: ensure file is within backup directory
    if (!filePath.startsWith(path.resolve(backupDir))) {
      logger.security('suspicious_activity', {
        metadata: {
          action: 'path_traversal_attempt',
          userId: session.user.id,
          backupId,
          attemptedPath: backup.filename
        },
        ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || undefined,
        userAgent: request.headers.get('user-agent') || undefined
      });

      throw new ValidationError('Invalid file path', 'INVALID_PATH');
    }

    // Check if file exists
    if (!fs.existsSync(filePath)) {
      logger.database('Database backup file missing', {
        requestId: correlationId,
        action: 'backup_file_missing',
        userId: session.user.id,
        backupId,
        expectedPath: filePath
      });

      throw new ValidationError('Backup file not found on disk', 'FILE_NOT_FOUND');
    }

    // Get file stats for additional validation
    const fileStats = fs.statSync(filePath);
    
    // Validate file size matches record
    if (backup.fileSize && fileStats.size !== backup.fileSize) {
      logger.database('Database backup file size mismatch', {
        requestId: correlationId,
        action: 'backup_file_size_mismatch',
        userId: session.user.id,
        backupId,
        expectedSize: backup.fileSize,
        actualSize: fileStats.size
      });

      throw new ValidationError('Backup file integrity check failed', 'FILE_INTEGRITY_ERROR');
    }

    // Log successful download
    const duration = Date.now() - startTime;
    logger.security('backup_downloaded', {
      metadata: {
        action: 'backup_downloaded',
        userId: session.user.id,
        backupId,
        filename: backup.filename,
        fileSize: fileStats.size,
        duration
      },
      ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || undefined,
      userAgent: request.headers.get('user-agent') || undefined
    });

    logger.business('Database backup downloaded successfully', {
      requestId: correlationId,
      action: 'backup_downloaded',
      duration,
      metadata: {
        backupId,
        filename: backup.filename,
        fileSize: fileStats.size
      }
    });

    // Read file and return as response
    const fileBuffer = fs.readFileSync(filePath);
    return new NextResponse(fileBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'application/octet-stream',
        'Content-Disposition': `attachment; filename="${backup.filename}"`,
        'Content-Length': fileStats.size.toString(),
        'X-Correlation-ID': correlationId,
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });
  } catch (error) {
    const duration = Date.now() - startTime;
    logger.error('Failed to download database backup', error as Error, {
      requestId: correlationId,
      action: 'download_backup_error',
      duration
    });
    return handleAPIError(error);
  }
}

// Enhanced export with comprehensive authentication and monitoring
export async function GET(request: NextRequest, context: { params: Promise<{ id: string }> }) {
  const { user, response } = await requireAdminAuth(request);
  if (response) return response;
  
  const session = { user };
  return downloadBackup(request, session, context.params);
} 
