import { auth } from "../../../../../../auth";
import { requireAdminAuth } from '@/lib/auth-helpers';
import { NextRequest, NextResponse } from 'next/server';
import { restoreDatabaseFromBackup } from '@/services/databaseBackupService';


// POST handler - Database restore functionality
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    console.log(`[Database Restore API] POST request from user: ${user.email}`);

    const body = await request.json();
    const { backupId, confirmRestore, verifyIntegrity = true } = body;

    if (!backupId) {
      return NextResponse.json(
        { error: 'Backup ID is required' },
        { status: 400 }
      );
    }

    if (!confirmRestore) {
      return NextResponse.json(
        { error: 'Restore confirmation is required' },
        { status: 400 }
      );
    }

    console.log(`[Database Restore API] CRITICAL: Starting restore operation for backup ${backupId} by user: ${user.email}`);

    // Perform actual database restore
    const restoreResult = await restoreDatabaseFromBackup(
      backupId,
      user.email,
      { 
        verifyIntegrity,
        targetDatabase: undefined // Use default database from DATABASE_URL
      }
    );

    console.log(`[Database Restore API] Restore completed successfully for backup ${backupId} by user: ${user.email}`);

    return NextResponse.json({
      success: true,
      message: restoreResult.message,
      preRestoreBackupId: restoreResult.preRestoreBackupId,
      backupId,
      restoredBy: user.email,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('[Database Restore API] Error:', error);
    return NextResponse.json(
      {
        error: 'Failed to perform database restore',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}
