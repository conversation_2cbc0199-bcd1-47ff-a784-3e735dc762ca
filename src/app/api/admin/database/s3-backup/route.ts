import { NextRequest, NextResponse } from 'next/server';
import s3BackupService from '@/services/s3BackupService';

export async function GET() {
  try {
    // Test S3 connection first
    const isConnected = await s3BackupService.testConnection();
    if (!isConnected) {
      return NextResponse.json({ 
        error: 'Unable to connect to Object Storage. Please check your credentials and bucket configuration.' 
      }, { status: 500 });
    }

    const backups = await s3BackupService.listBackups();
    
    return NextResponse.json({
      success: true,
      data: backups,
      total: backups.length,
      stats: {
        totalBackups: backups.length,
        totalSize: backups.reduce((sum, backup) => sum + backup.size, 0),
        lastBackup: backups.length > 0 ? backups[0]?.createdAt : null,
      }
    });

  } catch (error) {
    console.error('Error fetching S3 backups:', error);
    return NextResponse.json({ 
      error: 'Failed to fetch backups',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { description } = body;

    // Test S3 connection first
    const isConnected = await s3BackupService.testConnection();
    if (!isConnected) {
      return NextResponse.json({ 
        error: 'Unable to connect to Object Storage. Please check your credentials and bucket configuration.' 
      }, { status: 500 });
    }

    const backup = await s3BackupService.createBackup(description);
    
    return NextResponse.json({
      success: true,
      data: backup,
      message: 'Backup created successfully'
    });

  } catch (error) {
    console.error('Error creating S3 backup:', error);
    return NextResponse.json({ 
      error: 'Failed to create backup',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
} 