import { NextRequest, NextResponse } from 'next/server';
import s3BackupService from '@/services/s3BackupService';

export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params;
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');

    if (action === 'download') {
      const { url, filename } = await s3BackupService.downloadBackup(id);
      
      return NextResponse.json({
        success: true,
        downloadUrl: url,
        filename
      });
    }

    return NextResponse.json({ error: 'Invalid action' }, { status: 400 });

  } catch (error) {
    console.error('Error handling backup operation:', error);
    return NextResponse.json({ 
      error: 'Failed to process backup operation',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params;
    
    await s3BackupService.deleteBackup(id);
    
    return NextResponse.json({
      success: true,
      message: 'Backup deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting backup:', error);
    return NextResponse.json({ 
      error: 'Failed to delete backup',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id } = await params;
    const body = await request.json();
    const { action } = body;

    if (action === 'restore') {
      await s3BackupService.restoreBackup(id);
      
      return NextResponse.json({
        success: true,
        message: 'Database restored successfully'
      });
    }

    return NextResponse.json({ error: 'Invalid action' }, { status: 400 });

  } catch (error) {
    console.error('Error restoring backup:', error);
    return NextResponse.json({ 
      error: 'Failed to restore backup',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
} 