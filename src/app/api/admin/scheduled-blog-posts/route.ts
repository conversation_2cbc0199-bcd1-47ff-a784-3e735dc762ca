import { auth } from "../../../../../auth";;
import { requireAdminAuth } from '@/lib/auth-helpers';;
import { NextRequest, NextResponse } from 'next/server';

import prisma from '@/lib/prisma';
import { z } from 'zod';

// Validation schema for scheduled blog post
const ScheduledBlogPostSchema = z.object({
  category: z.string().optional(),
  tone: z.enum(['professional', 'casual', 'humorous', 'technical', 'conversational']).optional(),
  length: z.enum(['short', 'medium', 'long']).optional(),
  targetAudience: z.string().optional(),
  scheduledDate: z.string().refine((date) => !isNaN(Date.parse(date)), {
    message: 'Invalid date format',
  }),
});

// GET handler - Get all scheduled blog posts
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    console.log(`[Scheduled Blog Posts API] GET request from user: ${user.username}`);
    
    // Get query parameters
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const upcoming = searchParams.get('upcoming') === 'true';
    
    // Build the query
    const query: any = {};
    
    if (status) {
      query.status = status;
    }
    if (upcoming) {
      query.scheduledDate = {
        gte: new Date()
      };
    }

    // Get all scheduled blog posts
    const scheduledPosts = await prisma.scheduledBlogPost.findMany({
      where: query,
      orderBy: {
        scheduledDate: 'asc'
      }
    });
    
    console.log(`[Scheduled Blog Posts API] Retrieved ${scheduledPosts.length} scheduled posts by user: ${user.username}`);
    return NextResponse.json(scheduledPosts);
  } catch (error) {
    console.error('Error in GET scheduled blog posts:', error);
    return NextResponse.json(
      { error: 'Failed to fetch scheduled blog posts' },
      { status: 500 }
    );
  }
}

// POST handler - Create a new scheduled blog post
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    console.log(`[Scheduled Blog Posts API] POST request from user: ${user.username}`);
    
    // Parse request body
    const data = await request.json();
    
    // Validate the data
    const validationResult = ScheduledBlogPostSchema.safeParse(data);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid data', details: validationResult.error.format() },
        { status: 400 }
      );
    }

    // Create the scheduled blog post
    const scheduledPost = await prisma.scheduledBlogPost.create({
      data: {
        category: data.category,
        tone: data.tone,
        length: data.length,
        targetAudience: data.targetAudience,
        scheduledDate: new Date(data.scheduledDate),
        status: 'pending'
      }
    });
    
    console.log(`[Scheduled Blog Posts API] Created scheduled post: ${scheduledPost.id} by user: ${user.username}`);
    return NextResponse.json(scheduledPost, { status: 201 });
  } catch (error) {
    console.error('Error in POST scheduled blog post:', error);
    return NextResponse.json(
      { error: 'Failed to create scheduled blog post' },
      { status: 500 }
    );
  }
}

// DELETE handler - Delete all scheduled blog posts
export async function DELETE(request: NextRequest) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    console.log(`[Scheduled Blog Posts API] DELETE request from user: ${user.username}`);
    
    // Delete all scheduled blog posts
    const { count } = await prisma.scheduledBlogPost.deleteMany({
      where: {
        status: 'pending' // Only delete pending posts
      }
    });
    
    console.log(`[Scheduled Blog Posts API] Deleted ${count} scheduled posts by user: ${user.username}`);
    return NextResponse.json({ 
      message: `Deleted ${count} scheduled blog posts`,
      count
    });
  } catch (error) {
    console.error('Error in DELETE scheduled blog posts:', error);
    return NextResponse.json(
      { error: 'Failed to delete scheduled blog posts' },
      { status: 500 }
    );
  }
}
