import { auth } from "../../../../../../auth";
import { NextRequest, NextResponse } from 'next/server';

import { logActivity } from '@/utils/passwordUtils';
import { exec } from 'child_process';
import { promisify } from 'util';
import path from 'path';
import { requireAdminAuth } from '@/lib/auth-helpers';

const execAsync = promisify(exec);
// POST handler - Run the blog post generation script immediately
export async function POST(request: NextRequest) {
  return withAuth(
    async (req: NextRequest, session: any) => {
      const user = session.user;
      try {
        console.log(`[POST /api/admin/scheduled-blog-posts/run-now] User: ${user.email} (${user.id})`);
        // Parse request body
        const data = await request.json();
        const { runType = 'script' } = data;
        if (runType === 'script') {
          // Run the script directly using ts-node
          const scriptPath = path.join(process.cwd(), 'scripts', 'scheduled-blog-post.ts');
          
          try {
            console.log(`[POST /api/admin/scheduled-blog-posts/run-now] Executing script: ${scriptPath}`);
            
            const { stdout, stderr } = await execAsync(`npx ts-node ${scriptPath} --run-once`);
            if (stderr) {
              console.error('Script error:', stderr);
              return NextResponse.json({
                success: false,
                message: 'Error running script',
                error: stderr
              }, { status: 500 });
            }
            // Log the activity
            await logActivity(
              user.id,
              'execute',
              `Executed blog post generation script`,
              req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || undefined,
              req.headers.get('user-agent') || undefined,
              'blog_script',
              'run-now'
            );
            console.log(`[POST /api/admin/scheduled-blog-posts/run-now] Script executed successfully`);
            return NextResponse.json({
              success: true,
              message: 'Blog post generation script executed successfully',
              output: stdout
            });
          } catch (error) {
            console.error('Error executing script:', error);
            return NextResponse.json({
              success: false,
              message: 'Error executing script',
              error: error instanceof Error ? error.message : String(error)
            }, { status: 500 });
          }
        } else {
          console.warn(`[POST /api/admin/scheduled-blog-posts/run-now] Invalid runType: ${runType}`);
          return NextResponse.json({
            success: false,
            message: 'Blog automation API functionality has been removed. Please use script execution instead.'
          }, { status: 400 });
        }
      } catch (error) {
        console.error('Error in POST run-now:', error);
        return NextResponse.json({
          success: false,
          message: 'Failed to run blog post generation',
          error: error instanceof Error ? error.message : String(error)
        }, { status: 500 });
      }
    },
    'content:write',
    {
      logAction: 'execute_blog_script',
      resourceType: 'blog_script',
      rateLimitType: 'admin'
    }
  )(request);
}
