import { auth } from "../../../../../../auth";
import { NextRequest, NextResponse } from 'next/server';

import { successResponse, errorResponse, handleAPIError } from '@/utils/apiResponse';
import { logger } from '@/utils/logger';
import { withTransaction } from '@/utils/withTransaction';
import { validateRequestBody } from '@/utils/inputValidation';
import { APIError, ValidationError, DatabaseError } from '@/errors/ApiErrors';
import prisma from '@/lib/prisma';
import { z } from 'zod';
import { v4 as uuidv4 } from 'uuid';
import { requireAdminAuth } from '@/lib/auth-helpers';

// Enhanced validation schema for updating scheduled blog post
const UpdateScheduledBlogPostSchema = z.object({
  category: z.string().min(1).max(100).optional(),
  tone: z.enum(['professional', 'casual', 'humorous', 'technical', 'conversational']).optional(),
  length: z.enum(['short', 'medium', 'long']).optional(),
  targetAudience: z.string().min(1).max(200).optional(),
  scheduledDate: z.string().refine((date) => !isNaN(Date.parse(date)), {
    message: 'Invalid date format - must be ISO 8601',
  }).optional(),
  status: z.enum(['pending', 'completed', 'failed']).optional(),
  notes: z.string().max(1000).optional()
});
/**
 * GET /api/admin/scheduled-blog-posts/[id]
 * Enhanced scheduled blog post retrieval with comprehensive error handling
 */
async function getScheduledBlogPost(
  request: NextRequest, 
  { params }: { params: { id: string } }
) {
  const correlationId = request.headers.get('x-correlation-id') || uuidv4();
  const startTime = Date.now();
  
  try {
    const post = await prisma.scheduledBlogPost.findUnique({
      where: { id: params.id },
    });

    if (!post) {
      logger.warn('Scheduled blog post not found', {
        action: 'get_scheduled_post_not_found',
        requestId: correlationId
      });
      return NextResponse.json(
        { error: 'Scheduled blog post not found' },
        { status: 404 }
      );
    }

    const duration = Date.now() - startTime;
    logger.business('Scheduled blog post retrieved successfully', {
      duration,
      requestId: correlationId
    });

    return NextResponse.json({
      success: true,
      data: post,
      message: 'Scheduled blog post retrieved successfully',
      requestId: correlationId
    });
  } catch (error) {
    logger.error('Failed to retrieve scheduled blog post', error as Error, {
      action: 'get_scheduled_post_error',
      requestId: correlationId
    });
    return handleAPIError(error);
  }
}

/**
 * PUT /api/admin/scheduled-blog-posts/[id]
 * Enhanced scheduled blog post update with comprehensive validation
 */
async function updateScheduledBlogPost(
  request: NextRequest,
  session: any,
  params: Promise<{ id: string }>
) {
  const correlationId = request.headers.get('x-correlation-id') || uuidv4();
  const startTime = Date.now();
  try {
    const resolvedParams = await params;
    const { id } = resolvedParams;
    logger.info('Scheduled blog post update started', {
      action: 'update_scheduled_blog_post',
      requestId: correlationId,
      userId: session?.user?.id,
      resource: id
    });
    // Parse request body manually for better error handling
    let rawBody: string;
    try {
      rawBody = await request.text();
    } catch (error) {
      throw new ValidationError('Failed to read request body', 'BODY_READ_ERROR');
    }
    let parsedData: any;
    try {
      parsedData = JSON.parse(rawBody);
    } catch (error) {
      throw new ValidationError('Invalid JSON format', 'INVALID_JSON');
    }
    // Validate the data
    const validationResult = UpdateScheduledBlogPostSchema.safeParse(parsedData);
    if (!validationResult.success) {
      throw new ValidationError(
        `Validation failed: ${validationResult.error.errors.map(e => e.message).join(', ')}`,
        'VALIDATION_FAILED'
      );
    }
    const updateData = validationResult.data;
    // Update with transaction safety and optimistic locking
    const result = await withTransaction(async (tx) => {
      // Check if the scheduled blog post exists
      const existingPost = await tx.scheduledBlogPost.findUnique({
        where: { id }
      });
      if (!existingPost) {
        throw new ValidationError('Scheduled blog post not found', 'RESOURCE_NOT_FOUND');
      }
      // Prepare update data with only changed fields
      const finalUpdateData: any = {
        updatedAt: new Date()
      };
      // Only update provided fields
      Object.keys(updateData).forEach(key => {
        if (updateData[key as keyof typeof updateData] !== undefined) {
          const value = updateData[key as keyof typeof updateData];
          if (key === 'scheduledDate' && typeof value === 'string') {
            finalUpdateData[key] = new Date(value);
          } else {
            finalUpdateData[key] = value;
          }
        }
      });
      // Update the scheduled blog post
      const updatedPost = await tx.scheduledBlogPost.update({
        where: { id },
        data: finalUpdateData
      });
      return { existingPost, updatedPost };
    });
    logger.business('Scheduled blog post updated successfully', {
      action: 'scheduled_post_updated',
      duration: Date.now() - startTime,
      requestId: correlationId
    });
    return successResponse(result.updatedPost, {
      requestId: correlationId
    });
  } catch (error) {
    logger.error('Failed to update scheduled blog post', error as Error, {
      action: 'update_scheduled_post_error',
      requestId: correlationId
    });
    return handleAPIError(error);
  }
}

/**
 * DELETE /api/admin/scheduled-blog-posts/[id]
 * Enhanced scheduled blog post deletion with safety checks
 */
async function deleteScheduledBlogPost(
  request: NextRequest,
  session: any,
  params: Promise<{ id: string }>
) {
  const correlationId = request.headers.get('x-correlation-id') || uuidv4();
  const startTime = Date.now();
  try {
    const resolvedParams = await params;
    const { id } = resolvedParams;
    logger.info('Scheduled blog post deletion started', {
      action: 'delete_scheduled_blog_post',
      requestId: correlationId,
      userId: session?.user?.id,
      resource: id
    });
    // Delete with transaction safety
    const result = await withTransaction(async (tx) => {
      const existingPost = await tx.scheduledBlogPost.findUnique({
        where: { id },
        select: {
          id: true,
          category: true,
          status: true,
          scheduledDate: true
        }
      });
      // Prevent deletion of completed posts (business rule)
      if (existingPost?.status === 'completed') {
        throw new ValidationError(
          'Cannot delete completed scheduled blog posts',
          'DELETION_NOT_ALLOWED',
          { reason: 'Post has already been completed' }
        );
      }
      // Delete the scheduled blog post
      await tx.scheduledBlogPost.delete({
        where: { id }
      });
      return existingPost;
    });
    logger.business('Scheduled blog post deleted successfully', {
      action: 'scheduled_post_deleted',
      duration: Date.now() - startTime,
      requestId: correlationId
    });
    return successResponse(
      {
        id: result?.id,
        deletedAt: new Date().toISOString(),
        requestId: correlationId
      },
      {
        requestId: correlationId
      }
    );
  } catch (error) {
    logger.error('Failed to delete scheduled blog post', error as Error, {
      action: 'delete_scheduled_post_error',
      requestId: correlationId
    });
    return handleAPIError(error);
  }
}

// Enhanced exports with comprehensive authentication and monitoring
export async function GET(request: NextRequest, context: { params: Promise<{ id: string }> }) {
  const { user, response } = await requireAdminAuth(request);
  if (response) return response;
  
  const resolvedParams = await context.params;
  return getScheduledBlogPost(request, { params: resolvedParams });
}

export async function PUT(request: NextRequest, context: { params: Promise<{ id: string }> }) {
  const { user, response } = await requireAdminAuth(request);
  if (response) return response;
  
  const session = { user };
  return updateScheduledBlogPost(request, session, context.params);
}

export async function DELETE(request: NextRequest, context: { params: Promise<{ id: string }> }) {
  const { user, response } = await requireAdminAuth(request);
  if (response) return response;
  
  const session = { user };
  return deleteScheduledBlogPost(request, session, context.params);
}
