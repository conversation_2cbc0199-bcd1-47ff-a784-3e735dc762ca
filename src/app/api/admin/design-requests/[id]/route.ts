import { auth } from "../../../../../../auth";
import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    
    const designRequest = await (prisma as any).designRequest.findUnique({
      where: { id },
      include: {
        designer: {
          select: {
            name: true,
            email: true
          }
        },
        order: {
          select: {
            orderNumber: true,
            status: true
          }
        }
      }
    });

    if (!designRequest) {
      return NextResponse.json(
        { error: 'Design request not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      designRequest
    });
  } catch (error) {
    console.error('Design request fetch error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch design request' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const body = await request.json();
    
    const updateData: any = {};
    
    if (body.status) {
      updateData.status = body.status;
      
      // Update timestamps based on status
      if (body.status === 'completed') {
        updateData.completedAt = new Date();
      }
    }
    
    if (body.assignedTo) {
      updateData.assignedTo = body.assignedTo;
    }
    
    if (body.designFee !== undefined) {
      updateData.designFee = body.designFee;
      // Recalculate total cost
              const currentRequest = await (prisma as any).designRequest.findUnique({
          where: { id }
        });
      if (currentRequest) {
        updateData.totalCost = body.designFee + currentRequest.printCost;
      }
    }

    const updatedRequest = await (prisma as any).designRequest.update({
      where: { id },
      data: updateData,
      include: {
        designer: {
          select: {
            name: true,
            email: true
          }
        }
      }
    });

    return NextResponse.json(updatedRequest);
  } catch (error) {
    console.error('Design request update error:', error);
    return NextResponse.json(
      { error: 'Failed to update design request' },
      { status: 500 }
    );
  }
} 