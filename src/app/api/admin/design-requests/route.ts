import { auth } from "../../../../../auth";;;
import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const limit = parseInt(searchParams.get('limit') || '50');
    
    const where = status ? { status } : {};
    
    const designRequests = await prisma.designRequest.findMany({
      where,
      include: {
        designer: {
          select: {
            name: true,
            email: true
          }
        }
      },
      orderBy: {
        requestedAt: 'desc'
      },
      take: limit
    });

    return NextResponse.json(designRequests);
  } catch (error) {
    console.error('Design requests fetch error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch design requests' },
      { status: 500 }
    );
  }
} 