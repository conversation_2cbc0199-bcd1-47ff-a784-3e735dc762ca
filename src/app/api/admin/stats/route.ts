import { auth } from "../../../../../auth";;
import { requireAdminAuth } from '@/lib/auth-helpers';;
import { NextRequest, NextResponse } from 'next/server';

import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    console.log(`[Stats API] GET request from user: ${user.email}`);
    
    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const timeframe = searchParams.get('timeframe') || '30d';
    const detailed = searchParams.get('detailed') === 'true';

    // Calculate date range based on timeframe
    const now = new Date();
    let startDate: Date;
    
    switch (timeframe) {
      case '7d':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case '30d':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case '90d':
        startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
        break;
      case '1y':
        startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    }

    try {
      // Parallel database queries for better performance
      const [
        totalPosts,
        totalCategories,
        totalPortfolioItems,
        totalTeamMembers,
        recentPosts,
        recentPortfolioItems,
        categoryStats,
        activityLogs,
        websitePortfolioCount
      ] = await Promise.all([
        // Total posts
        prisma.blogPost.count(),
        
        // Total categories
        prisma.category.count(),
        
        // Total portfolio items (S3-based - this is an estimate)
        Promise.resolve(0), // We can't easily count S3 items from here
        
        // Total team members
        prisma.teamMember.count(),
        
        // Recent posts
        prisma.blogPost.findMany({
          where: {
            createdAt: { gte: startDate }
          },
          orderBy: { createdAt: 'desc' },
          take: detailed ? 20 : 5,
          select: {
            id: true,
            title: true,
            slug: true,
            createdAt: true,
            published: true,
            category: {
              select: {
                name: true,
                slug: true,
              }
            }
          }
        }),
        
        // Recent portfolio items (Website Portfolio - database-based)
        prisma.websitePortfolio.findMany({
          where: {
            createdAt: { gte: startDate }
          },
          orderBy: { createdAt: 'desc' },
          take: detailed ? 20 : 5,
          select: {
            id: true,
            title: true,
            description: true,
            createdAt: true,
            category: {
              select: {
                name: true,
                slug: true,
              }
            }
          }
        }),
        
        // Category statistics
        prisma.category.findMany({
          include: {
            _count: {
              select: {
                posts: true,
                websitePortfolio: true,
              }
            }
          }
        }),
        
        // Activity logs (if available)
        prisma.activityLog ? prisma.activityLog.findMany({
          where: {
            createdAt: { gte: startDate }
          },
          orderBy: { createdAt: 'desc' },
          take: detailed ? 50 : 10,
          select: {
            id: true,
            action: true,
            resourceType: true,
            resourceId: true,
            createdAt: true,
            userEmail: true,
          }
        }).catch(() => []) : Promise.resolve([]),
        
        // Website portfolio count
        prisma.websitePortfolio.count(),
      ]);

      // Calculate growth rates for the timeframe
      const previousStartDate = new Date(startDate.getTime() - (now.getTime() - startDate.getTime()));
      
      const [previousPosts, previousPortfolio] = await Promise.all([
        prisma.blogPost.count({
          where: {
            createdAt: {
              gte: previousStartDate,
              lt: startDate,
            }
          }
        }),
        prisma.websitePortfolio.count({
          where: {
            createdAt: {
              gte: previousStartDate,
              lt: startDate,
            }
          }
        }),
      ]);

      // Build response
      const stats = {
        overview: {
          totalPosts,
          totalCategories,
          totalPortfolioItems: totalPortfolioItems, // S3-based portfolio
          totalWebsitePortfolio: websitePortfolioCount, // Database-based portfolio
          totalTeamMembers,
        },
        growth: {
          postsThisPeriod: recentPosts.length,
          portfolioThisPeriod: recentPortfolioItems.length,
          postsGrowthRate: previousPosts > 0 ? ((recentPosts.length - previousPosts) / previousPosts * 100) : 0,
          portfolioGrowthRate: previousPortfolio > 0 ? ((recentPortfolioItems.length - previousPortfolio) / previousPortfolio * 100) : 0,
        },
        recent: {
          posts: recentPosts,
          portfolioItems: recentPortfolioItems,
        },
        categories: categoryStats.map(cat => ({
          id: cat.id,
          name: cat.name,
          slug: cat.slug,
          postsCount: cat._count.posts,
          portfolioCount: cat._count.websitePortfolio,
          totalContent: cat._count.posts + cat._count.websitePortfolio,
        })),
        timeframe,
        generatedAt: new Date().toISOString(),
      };

      // Add detailed information if requested
      if (detailed) {
        (stats as any).activity = activityLogs;
        (stats as any).dateRange = {
          start: startDate.toISOString(),
          end: now.toISOString(),
        };
      }

      console.log(`[Stats API] Generated stats for timeframe: ${timeframe} by user: ${user.email}`);

      return NextResponse.json({
        success: true,
        stats,
      }, {
        headers: {
          'Cache-Control': 'private, max-age=300', // 5 minutes cache
        },
      });

    } catch (dbError) {
      console.error('Database error in stats API:', dbError);
      return NextResponse.json(
        { error: 'Failed to retrieve statistics from database' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error in stats API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 