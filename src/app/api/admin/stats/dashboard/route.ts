import { auth } from "../../../../../../auth";
import { requireAdminAuth } from '@/lib/auth-helpers';
import { NextRequest, NextResponse } from 'next/server';

import { prisma } from '@/lib/prisma';

// Dashboard stats caching
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes
let statsCache: {
  data: any;
  expires: number;
} | null = null;

/**
 * GET /api/admin/stats/dashboard
 * Enhanced dashboard statistics with caching and performance monitoring
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    console.log(`[Dashboard Stats API] GET request from user: ${user.email}`);

    // Check cache first
    const now = Date.now();
    if (statsCache && statsCache.expires > now) {
      console.log(`[Dashboard Stats API] Served from cache for user: ${user.email}`);
      
      return NextResponse.json({
        success: true,
        ...statsCache.data
      });
    }

    try {
      // Fetch fresh data with parallel queries for better performance
      const [
        postsCount,
        categoriesCount,
        recentPosts,
        allPosts // Get all posts to analyze tags and authors
      ] = await Promise.all([
        prisma.blogPost.count(),
        prisma.category.count(),
        prisma.blogPost.findMany({
          take: 5,
          orderBy: { createdAt: 'desc' },
          select: {
            id: true,
            title: true,
            status: true,
            author: true,
            createdAt: true
          }
        }),
        // Get all posts to analyze tags and authors
        prisma.blogPost.findMany({
          select: {
            tags: true,
            author: true
          }
        })
      ]);

      // Calculate unique tags count
      const uniqueTags = new Set();
      allPosts.forEach(post => {
        if (post.tags && Array.isArray(post.tags)) {
          post.tags.forEach(tag => {
            if (tag && tag.trim()) {
              uniqueTags.add(tag.trim().toLowerCase());
            }
          });
        }
      });

      // Calculate unique authors count  
      const uniqueAuthors = new Set();
      allPosts.forEach(post => {
        if (post.author && post.author.trim()) {
          uniqueAuthors.add(post.author.trim());
        }
      });

      const stats = {
        posts: postsCount,
        categories: categoriesCount,
        tags: uniqueTags.size,
        authors: uniqueAuthors.size,
        recentPosts: recentPosts,
        system: { status: 'healthy' },
        performance: {
          generatedAt: new Date().toISOString()
        }
      };

      // Update cache
      statsCache = {
        data: stats,
        expires: now + CACHE_TTL
      };

      console.log(`[Dashboard Stats API] Generated fresh stats for user: ${user.email}`);

      return NextResponse.json({
        success: true,
        ...stats
      });

    } catch (dbError) {
      console.error('Database error in dashboard stats:', dbError);
      return NextResponse.json(
        { error: 'Failed to fetch dashboard statistics' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error in dashboard stats API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
