import { auth } from "../../../../../../auth";
import { requireAdminAuth } from '@/lib/auth-helpers';
import { NextRequest, NextResponse } from 'next/server';

import { prisma } from '@/lib/prisma';

// GET handler - Get category statistics
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    console.log(`[Category Stats API] GET request from user: ${user.email}`);

    try {
      // Get category statistics
      const [
        totalCategories,
        activeCategories,
        categoriesWithPosts,
        topCategories
      ] = await Promise.all([
        // Total categories count
        prisma.category.count(),
        
        // Active categories count (if you have an active field)
        prisma.category.count({
          where: { 
            // Add any active condition if applicable
          }
        }),
        
        // Categories that have blog posts
        prisma.category.count({
          where: {
            // This would need to be adjusted based on your actual schema
            // For now, just return total count
          }
        }),
        
        // Top categories by usage (mock data for now)
        prisma.category.findMany({
          take: 10,
          orderBy: {
            createdAt: 'desc'
          },
          select: {
            id: true,
            name: true,
            slug: true,
            createdAt: true
          }
        })
      ]);

      const stats = {
        overview: {
          total: totalCategories,
          active: activeCategories,
          withPosts: categoriesWithPosts,
          empty: totalCategories - categoriesWithPosts
        },
        topCategories,
        performance: {
          averagePostsPerCategory: categoriesWithPosts > 0 ? 
            Math.round((categoriesWithPosts / totalCategories) * 100) / 100 : 0,
          utilizationRate: totalCategories > 0 ? 
            Math.round((categoriesWithPosts / totalCategories) * 100) : 0
        },
        generatedAt: new Date().toISOString()
      };

      console.log(`[Category Stats API] Generated stats for user: ${user.email}`);

      return NextResponse.json({
        success: true,
        stats
      });

    } catch (dbError) {
      console.error('Database error in category stats:', dbError);
      return NextResponse.json(
        { error: 'Failed to fetch category statistics' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error in category stats API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 
