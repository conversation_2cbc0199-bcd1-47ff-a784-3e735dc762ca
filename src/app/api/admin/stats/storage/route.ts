import { auth } from "../../../../../../auth";
import { NextRequest, NextResponse } from 'next/server';

import { S3Client, ListObjectsV2Command } from '@aws-sdk/client-s3';
import { requireAdminAuth } from '@/lib/auth-helpers';

const s3Client = new S3Client({
  region: process.env.LINODE_REGION || 'us-east-1',
  endpoint: process.env.LINODE_ENDPOINT,
  credentials: {
    accessKeyId: process.env.LINODE_ACCESS_KEY_ID || '',
    secretAccessKey: process.env.LINODE_SECRET_ACCESS_KEY || '',
  }
});
async function getStorageStats(request: NextRequest, session: any) {
  try {
    console.log(`[Storage Stats API] GET request from user: ${session.user.username}`);
    const command = new ListObjectsV2Command({
      Bucket: process.env.LINODE_BUCKET_NAME,
    });
    const response = await s3Client.send(command);
    
    let totalSize = 0;
    const recentActivity = [];
    if (response.Contents) {
      // Calculate total size
      totalSize = response.Contents.reduce((acc, obj) => acc + (obj.Size || 0), 0);
      // Get recent activity (last 5 uploads)
      const sortedObjects = [...response.Contents].sort((a, b) => 
        (b.LastModified?.getTime() || 0) - (a.LastModified?.getTime() || 0)
      ).slice(0, 5);
      for (const obj of sortedObjects) {
        const timestamp = obj.LastModified?.toISOString() || '';
        const key = obj.Key || '';
        const category = key.split('/')[0];
        
        recentActivity.push({
          type: 'upload',
          message: `Uploaded ${key.split('/').pop()} to ${category}`,
          timestamp: new Date(timestamp).toLocaleString(),
          size: obj.Size
        });
      }
    }
    const stats = {
      used: totalSize,
      limit: 5 * 1024 * 1024 * 1024, // 5GB
      recentActivity
    };
    console.log(`[Storage Stats API] Retrieved storage stats (${totalSize} bytes used) by user: ${session.user.username}`);
    return NextResponse.json(stats);
  } catch (error) {
    console.error('Error fetching storage stats:', error);
    return NextResponse.json(
      { error: 'Failed to fetch storage statistics' },
      { status: 500 }
    );
}
}
// Export handler with enhanced authentication
export async function GET(request: NextRequest, context?: any) {
  const { user, response } = await requireAdminAuth(request);
  if (response) return response;
  
  const session = { user };
  return getStorageStats(request, session, context);
} 
