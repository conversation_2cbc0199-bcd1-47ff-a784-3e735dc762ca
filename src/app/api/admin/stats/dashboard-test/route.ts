import { auth } from "../../../../../../auth";
import { NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

export async function GET() {
  try {
    // Get blog posts count
    const postsCount = await prisma.blogPost.count();

    // Get categories count
    const categoriesCount = await prisma.category.count();

    // Get all posts to analyze tags and authors
    const allPosts = await prisma.blogPost.findMany({
      select: {
        tags: true,
        author: true
      }
    });

    // Calculate unique tags count
    const uniqueTags = new Set();
    allPosts.forEach(post => {
      if (post.tags && Array.isArray(post.tags)) {
        post.tags.forEach(tag => {
          if (tag && tag.trim()) {
            uniqueTags.add(tag.trim().toLowerCase());
          }
        });
      }
    });

    // Calculate unique authors count  
    const uniqueAuthors = new Set();
    allPosts.forEach(post => {
      if (post.author && post.author.trim()) {
        uniqueAuthors.add(post.author.trim());
      }
    });

    // Return the statistics
    return NextResponse.json({
      posts: postsCount,
      categories: categoriesCount,
      tags: uniqueTags.size,
      authors: uniqueAuthors.size,
      debug: {
        totalPostsAnalyzed: allPosts.length,
        sampleTags: Array.from(uniqueTags).slice(0, 5),
        sampleAuthors: <AUTHORS>
      }
    });
  } catch (error) {
    console.error('Error fetching dashboard statistics:', error);
    return NextResponse.json(
      { error: 'Failed to fetch dashboard statistics' },
      { status: 500 }
    );
  }
}
