import { auth } from "../../../../../../auth";
import { requireAdminAuth } from '@/lib/auth-helpers';
import { NextResponse } from 'next/server';


export async function GET() {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request); if (response) return response;
    if (!user) {
      return new NextResponse(JSON.stringify({ error: 'Unauthorized' }), {
        status: 401,
      });
    }

    // Return mock data since we no longer use a database
    return NextResponse.json({ 
      total: 0,
      recent: 0
    });
  } catch (error) {
    console.error('Error fetching image stats:', error);
    return new NextResponse(JSON.stringify({ error: 'Internal Server Error' }), {
      status: 500,
    });
  }
} 