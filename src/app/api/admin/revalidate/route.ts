import { auth } from "../../../../../auth";;
import { requireAdminAuth } from '@/lib/auth-helpers';;
import { NextRequest, NextResponse } from 'next/server';

import { revalidatePath, revalidateTag } from 'next/cache';

// POST handler - Revalidate content
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    console.log(`[Revalidate API] POST request from user: ${user.email}`);
    
    const body = await request.json();
    const { type, path, tag } = body;
    const results: { [key: string]: boolean } = {};
    
    switch (type) {
      case 'logos':
        // Revalidate logos page and related paths
        revalidatePath('/logos');
        revalidatePath('/portfolio');
        revalidatePath('/');
        results.logos = true;
        break;
        
      case 'portfolio':
        // Revalidate all portfolio-related pages
        revalidatePath('/portfolio');
        revalidatePath('/');
        results.portfolio = true;
        break;
        
      case 'images':
        // Revalidate image-related pages
        if (path) {
          revalidatePath(path);
        }
        results.images = true;
        break;
        
      case 'all':
        // Revalidate all major pages
        const paths = [
          '/',
          '/logos',
          '/portfolio',
          '/blog',
          '/about',
          '/contact',
          '/catalogue'
        ];
        
        paths.forEach(p => {
          try {
            revalidatePath(p);
          } catch (error) {
            console.warn(`Failed to revalidate path ${p}:`, error);
          }
        });
        results.all = true;
        break;
        
      case 'path':
        if (path) {
          revalidatePath(path);
          results.path = true;
        } else {
          return NextResponse.json(
            { error: 'Path is required for path revalidation' },
            { status: 400 }
          );
        }
        break;
        
      case 'tag':
        if (tag) {
          revalidateTag(tag);
          results.tag = true;
        } else {
          return NextResponse.json(
            { error: 'Tag is required for tag revalidation' },
            { status: 400 }
          );
        }
        break;
        
      default:
        return NextResponse.json(
          { error: 'Invalid revalidation type specified' },
          { status: 400 }
        );
    }
    
    const responseData = {
      success: true,
      message: `Revalidation completed for: ${type}`,
      results,
      timestamp: new Date().toISOString()
    };
    
    console.log(`[Revalidate API] Completed revalidation type: ${type} by user: ${user.email}`);
    
    return NextResponse.json(responseData, {
      headers: {
        'Cache-Control': 'no-store',
      },
    });
  } catch (error) {
    console.error('Error during revalidation:', error);
    return NextResponse.json(
      { 
        error: 'Revalidation failed', 
        details: error instanceof Error ? error.message : String(error) 
      },
      { status: 500 }
    );
  }
}

// GET handler - Get revalidation status
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    console.log(`[Revalidate API] GET request from user: ${user.email}`);
    
    return NextResponse.json({
      success: true,
      message: 'Revalidation API is available',
      supportedTypes: ['logos', 'portfolio', 'images', 'all', 'path', 'tag'],
      timestamp: new Date().toISOString()
    }, {
      headers: {
        'Cache-Control': 'no-store, max-age=0',
        'Pragma': 'no-cache',
      },
    });
  } catch (error) {
    console.error('Error in revalidation API:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to get revalidation status',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
