import { auth } from "../../../../../auth";;
import { requireAdminAuth } from '@/lib/auth-helpers';;
import { NextRequest, NextResponse } from 'next/server';

import { z } from 'zod';
import { getSiteSettings, upsertSiteSettings } from '@/services/siteSettingsService';

// Validation schema for site settings
const SiteSettingsSchema = z.object({
  siteName: z.string().min(1).max(100).optional(),
  siteDescription: z.string().max(255).optional(),
  contactEmail: z.string().email().max(100).optional(),
  phoneNumber: z.string().max(50).optional(),
  address: z.string().max(255).optional(),
  facebookUrl: z.string().url().max(255).optional().or(z.literal('')),
  twitterUrl: z.string().url().max(255).optional().or(z.literal('')),
  instagramUrl: z.string().url().max(255).optional().or(z.literal('')),
  tiktokUrl: z.string().url().max(255).optional().or(z.literal('')),
  linkedinUrl: z.string().url().max(255).optional().or(z.literal('')),
  metaTitle: z.string().max(255).optional(),
  metaDescription: z.string().optional(),
  googleAnalyticsId: z.string().max(50).optional(),
});

// GET - Fetch site settings
export async function GET(request: NextRequest) {
  try {
    // Check admin authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;

    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    console.log(`[Settings API] GET request from user: ${user.username}`);
    
    // Get site settings
    const settings = await getSiteSettings();
    
    // If no settings exist, return empty object
    if (!settings) {
      return NextResponse.json({});
    }
    
    return NextResponse.json(settings);
  } catch (error) {
    console.error('Error fetching site settings:', error);
    return NextResponse.json(
      { error: 'Failed to fetch site settings' },
      { status: 500 }
    );
  }
}

// PUT - Update site settings
export async function PUT(request: NextRequest) {
  try {
    // Check admin authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;

    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    console.log(`[Settings API] PUT request from user: ${user.username}`);
    
    // Parse and validate the request body
    const body = await request.json();
    const result = SiteSettingsSchema.safeParse(body);
    
    if (!result.success) {
      return NextResponse.json(
        { error: 'Invalid input', issues: result.error.issues },
        { status: 400 }
      );
    }

    // Update site settings
    const updatedSettings = await upsertSiteSettings(result.data);
    console.log(`[Settings API] Updated site settings by user: ${user.username}`);
    
    return NextResponse.json(updatedSettings);
  } catch (error) {
    console.error('Error updating site settings:', error);
    return NextResponse.json(
      { error: 'Failed to update site settings' },
      { status: 500 }
    );
  }
}
