import { auth } from "../../../../../../../auth";
import { NextRequest, NextResponse } from 'next/server';

import { successResponse, errorResponse, handleAPIError } from '@/utils/apiResponse';
import { logger } from '@/utils/logger';
import { withTransaction } from '@/utils/withTransaction';
import { validateRequestBody } from '@/utils/inputValidation';
import { ValidationError } from '@/errors/ApiErrors';
import { z } from 'zod';
import { 
  updateStorageConfig, 
  deleteStorageConfig, 
  StorageConfigInput
} from '@/lib/storageConfig';
import { v4 as uuidv4 } from 'uuid';
import { requireAdminAuth } from '@/lib/auth-helpers';

// Validation schema for storage configuration update
const StorageConfigUpdateSchema = z.object({
  provider: z.enum(['S3', 'LOCAL', 'LINODE', 'CLOUDINARY']).optional(),
  region: z.string().min(1).max(100).optional(),
  endpoint: z.string().url().max(255).optional(),
  bucketName: z.string().min(1).max(100).optional(),
  accessKeyId: z.string().min(1).max(255).optional(),
  secretAccessKey: z.string().min(1).max(255).optional(),
  isDefault: z.boolean().optional(),
});
/**
 * PUT /api/admin/settings/storage/[id]
 * Update storage configuration with enterprise patterns
 */
async function updateStorageConfig(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const correlationId = request.headers.get('x-correlation-id') || uuidv4();
  const startTime = Date.now();

  try {
    const updateData = await request.json();
    const { id } = params;

    // Log the update attempt
    logger.info('Storage config update started', {
      action: 'update_storage_config',
      configId: id,
      requestId: correlationId,
      ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || undefined,
      userAgent: request.headers.get('user-agent') || undefined
    });

    // Update the storage configuration
    const config = await prisma.storageConfig.update({
      where: { id },
      data: updateData
    });

    const duration = Date.now() - startTime;
    
    logger.business('Storage config updated successfully', {
      action: 'update_storage_config_success',
      configId: id,
      duration,
      requestId: correlationId
    });

    return NextResponse.json({
      success: true,
      data: config,
      message: 'Storage configuration updated successfully',
      requestId: correlationId
    });
  } catch (error) {
    logger.error('Failed to update storage config', error as Error, {
      action: 'update_storage_config_error',
      configId: params.id,
      requestId: correlationId
    });
    return handleAPIError(error);
  }
}

async function deleteStorageConfigHandler(
  request: NextRequest,
  session: any,
  params: Promise<{ id: string }>
) {
  const correlationId = request.headers.get('x-correlation-id') || 'unknown';
  const startTime = Date.now();
  try {
    const resolvedParams = await params;
    const { id } = resolvedParams;
    logger.info('Storage config deletion started', {
      action: 'delete_storage_config',
      configId: id
    });
    // Security logging for deletion
    logger.security('storage_config_deleted', {
      action: 'storage_config_deleted',
      configId: id
    });
    // Delete the storage configuration
    await deleteStorageConfig(id);
    logger.business('Storage config deleted successfully', {
      action: 'storage_config_deleted',
      configId: id,
      duration: Date.now() - startTime,
      requestId: correlationId
    });
    return successResponse(
      {
        id,
        deletedAt: new Date().toISOString(),
        requestId: correlationId
      },
      {
        message: 'Storage config deleted successfully',
        requestId: correlationId
      }
    );
  } catch (error: any) {
    // Handle specific business logic errors
    if (error.message === 'Cannot delete the default storage configuration') {
      logger.business('Attempted to delete default storage config', {
        requestId: correlationId,
        duration: Date.now() - startTime,
        metadata: {
          errorType: 'default_config_protection',
          configId: params.id
        }
      });
      throw new ValidationError(error.message, 'DEFAULT_CONFIG_PROTECTED');
    }
    logger.error('Failed to delete storage config', error as Error, {
      action: 'delete_storage_config_error',
      configId: params.id,
      requestId: correlationId
    });
    return handleAPIError(error);
  }
}

// Export handlers with comprehensive authentication and monitoring
export async function PUT(request: NextRequest, context?: any) {
  const { user, response } = await requireAdminAuth(request);
  if (response) return response;
  
  const session = { user };
  return updateStorageConfig(request, session, context);
}

export async function DELETE(request: NextRequest, context?: any) {
  const { user, response } = await requireAdminAuth(request);
  if (response) return response;
  
  const session = { user };
  return deleteStorageConfigHandler(request, session, context);
} 
