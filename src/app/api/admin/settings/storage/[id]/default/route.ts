import { auth } from "../../../../../../../../auth";
import { NextRequest, NextResponse } from 'next/server';

import { updateStorageConfig } from '@/lib/storageConfig';
import { requireAdminAuth } from '@/lib/auth-helpers';

// Get the ID from the URL context
function getIdFromContext(context: any): string {
  return context?.params?.id || '';
}
async function setDefaultStorageConfig(
  request: NextRequest,
  session: any,
  context: any
) {
  try {
    console.log(`[Storage Config API] PUT default request from user: ${session.user.username}`);
    // Get the configuration ID from the context
    const id = getIdFromContext(context);
    if (!id) {
      return NextResponse.json(
        { error: 'Storage configuration ID is required' },
        { status: 400 }
      );
    }
    
    // Update the storage configuration to be the default
    const config = await updateStorageConfig(id, { isDefault: true });
    console.log(`[Storage Config API] Set storage config ${id} as default by user: ${session.user.username}`);
    return NextResponse.json(config);
  } catch (error) {
    console.error('Error setting default storage configuration:', error);
    return NextResponse.json(
      { error: 'Failed to set default storage configuration' },
      { status: 500 }
    );
  }
}
// Export handler with enhanced authentication
export async function PUT(request: NextRequest, context?: any) {
  const { user, response } = await requireAdminAuth(request);
  if (response) return response;
  
  const session = { user };
  return setDefaultStorageConfig(request, session, context);
} 
