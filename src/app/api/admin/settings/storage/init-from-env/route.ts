import { auth } from "../../../../../../../auth";
import { NextRequest, NextResponse } from 'next/server';

import prisma from '@/lib/prisma';
import { v4 as uuidv4 } from 'uuid';
import { logger } from '@/utils/logger';
import { requireAdminAuth } from '@/lib/auth-helpers';

async function initStorageFromEnv(request: NextRequest) {
  const correlationId = request.headers.get('x-correlation-id') || uuidv4();
  const startTime = Date.now();

  try {
    // Check if a default config already exists
    const existingConfig = await prisma.storageConfig.findFirst({
      where: { isDefault: true }
    });

    let updatedConfig;
    if (existingConfig) {
      // Update existing config with environment variables
      updatedConfig = await prisma.storageConfig.update({
        where: { id: existingConfig.id },
        data: {
          provider: 's3',
          region: process.env.NEXT_PUBLIC_S3_REGION || 'fr-par-1',
          endpoint: process.env.NEXT_PUBLIC_S3_ENDPOINT,
          bucketName: process.env.NEXT_PUBLIC_S3_BUCKET,
          accessKeyId: process.env.S3_ACCESS_KEY_ID,
          secretAccessKey: process.env.S3_SECRET_ACCESS_KEY,
          isDefault: true
        }
      });
    } else {
      // Create new config from environment variables
      updatedConfig = await prisma.storageConfig.create({
        data: {
          provider: 's3',
          region: process.env.NEXT_PUBLIC_S3_REGION || 'fr-par-1',
          endpoint: process.env.NEXT_PUBLIC_S3_ENDPOINT,
          bucketName: process.env.NEXT_PUBLIC_S3_BUCKET,
          accessKeyId: process.env.S3_ACCESS_KEY_ID,
          secretAccessKey: process.env.S3_SECRET_ACCESS_KEY,
          isDefault: true
        }
      });
    }

    const duration = Date.now() - startTime;
    logger.business('Storage config initialized from environment', {
      action: 'init_storage_from_env_success',
      configId: updatedConfig.id,
      duration,
      requestId: correlationId
    });

      return NextResponse.json({
        success: true,
      data: {
          id: updatedConfig.id,
          provider: updatedConfig.provider,
          region: updatedConfig.region,
          endpoint: updatedConfig.endpoint,
          bucketName: updatedConfig.bucketName,
          isDefault: updatedConfig.isDefault
      },
      message: 'Storage configuration initialized successfully',
      requestId: correlationId
    });
  } catch (error) {
    logger.error('Failed to initialize storage from environment', error as Error, {
      action: 'init_storage_from_env_error',
      requestId: correlationId
    });
    return handleAPIError(error);
  }
}

// Export handler with enhanced authentication
export async function POST(request: NextRequest, context?: any) {
  const { user, response } = await requireAdminAuth(request);
  if (response) return response;
  
  const session = { user };
  return initStorageFromEnv(request, session, context);
}
