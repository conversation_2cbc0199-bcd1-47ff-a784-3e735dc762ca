import { auth } from "../../../../../../auth";
import { requireAdminAuth } from '@/lib/auth-helpers';
import { NextRequest, NextResponse } from 'next/server';

import { z } from 'zod';
import {
  getAllStorageConfigs,
  createStorageConfig,
  StorageConfigInput
} from '@/lib/storageConfig';
import prisma from '@/lib/prisma';

// Validation schema for storage configuration input
const StorageConfigSchema = z.object({
  provider: z.enum(['S3', 'LOCAL', 'LINODE', 'CLOUDINARY']),
  region: z.string().min(1).max(100),
  endpoint: z.string().url().max(255),
  bucketName: z.string().min(1).max(100),
  accessKeyId: z.string().min(1).max(255),
  secretAccessKey: z.string().min(1).max(255),
  isDefault: z.boolean().default(false),
});

// GET - Fetch storage configurations
export async function GET(request: NextRequest) {
  try {
    // Check admin authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;

    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    console.log(`[Storage Config API] GET request from user: ${user.username}`);
    
    // Check if this is a debug request with a special header
    const url = new URL(request.url);
    const isDebug = url.searchParams.get('debug') === 'true';
    
    try {
      // Check if prisma and storageConfig model exist
      if (!prisma || !prisma.storageConfig) {
        throw new Error('StorageConfig model not available in Prisma client');
      }
      
      // Get all storage configurations using direct Prisma call for reliability
      // @ts-ignore - Using the StorageConfig table that's defined in our schema
      let configs = await prisma.storageConfig.findMany({
        orderBy: { createdAt: 'desc' },
      });
      
      // Ensure configs is always an array
      configs = Array.isArray(configs) ? configs : [];
      console.log(`Successfully fetched ${configs.length} storage configurations`);
      return NextResponse.json(configs);
    } catch (dbError) {
      console.error('Database error fetching storage configurations:', dbError);
      
      // Fallback to environment variables if DB lookup fails
      if (process.env.NEXT_PUBLIC_S3_BUCKET) {
        console.log('Falling back to environment variables for storage configuration');
        const envConfig = {
          id: 'env-default',
          provider: 'S3',
          region: process.env.NEXT_PUBLIC_S3_REGION || 'fr-par-1',
          endpoint: process.env.NEXT_PUBLIC_S3_ENDPOINT || '',
          bucketName: process.env.NEXT_PUBLIC_S3_BUCKET || 'mocky',
          accessKeyId: process.env.NEXT_PUBLIC_S3_ACCESS_KEY || '',
          secretAccessKey: process.env.NEXT_PUBLIC_S3_SECRET_KEY || '',
          isDefault: true,
          createdAt: new Date(),
          updatedAt: new Date(),
        };
        return NextResponse.json([envConfig]);
      }
      
      // Return empty array instead of throwing to prevent forEach errors
      return NextResponse.json([]);
    }
  } catch (error) {
    console.error('Error fetching storage configurations:', error);
    return NextResponse.json(
      {
        error: 'Failed to fetch storage configurations',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  } finally {
    await prisma.$disconnect();
  }
}

// POST - Create storage configuration
export async function POST(request: NextRequest) {
  try {
    // Check admin authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;

    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    console.log(`[Storage Config API] POST request from user: ${user.username}`);
    
    // Parse and validate the request body
    const body = await request.json();
    const result = StorageConfigSchema.safeParse(body);
    
    if (!result.success) {
      return NextResponse.json(
        { error: 'Invalid input', issues: result.error.issues },
        { status: 400 }
      );
    }

    // Create the storage configuration
    const config = await createStorageConfig(result.data as StorageConfigInput);
    console.log(`[Storage Config API] Created storage config: ${config.id} by user: ${user.username}`);
    
    return NextResponse.json(config, { status: 201 });
  } catch (error) {
    console.error('Error creating storage configuration:', error);
    return NextResponse.json(
      { error: 'Failed to create storage configuration' },
      { status: 500 }
    );
  }
}
