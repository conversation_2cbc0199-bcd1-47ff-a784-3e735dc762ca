import { auth } from "../../../../../auth";;
import { requireAdminAuth } from '@/lib/auth-helpers';;
import { NextRequest, NextResponse } from 'next/server';

import { z } from 'zod';
import prisma from '@/lib/prisma';
import { logActivity } from '@/utils/passwordUtils';

// Validation schema for site scripts
const SiteScriptSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().optional(),
  scriptType: z.enum(['head', 'body', 'footer']),
  content: z.string().min(1),
  isActive: z.boolean().default(true),
  position: z.number().int().default(0),
});

// GET handler - Get all scripts
export async function GET(request: NextRequest) {
  try {
    // Check admin authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;

    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    console.log(`[Scripts API] GET request from user: ${user.username}`);
    
    // Get scripts from database
    const scripts = await prisma.siteScript.findMany({
      orderBy: [
        { scriptType: 'asc' },
        { position: 'asc' },
        { name: 'asc' }
      ],
    });

    console.log(`[Scripts API] Retrieved ${scripts.length} scripts by user: ${user.username}`);
    return NextResponse.json(scripts);
  } catch (error) {
    console.error('Error fetching scripts:', error);
    return NextResponse.json(
      { error: 'Failed to fetch scripts' },
      { status: 500 }
    );
  }
}

// POST handler - Create a new script
export async function POST(request: NextRequest) {
  try {
    // Check admin authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;

    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    console.log(`[Scripts API] POST request from user: ${user.username}`);
    
    // Parse request body
    const data = await request.json();
    
    // Validate data
    const validatedData = SiteScriptSchema.parse(data);
    
    // Create script in database
    const script = await prisma.siteScript.create({
      data: validatedData,
    });

    // Log activity
    await logActivity(
      user.id,
      'create',
      `Created script: ${script.name}`,
      request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
      request.headers.get('user-agent') || 'unknown',
      'script',
      script.id
    );

    console.log(`[Scripts API] Created script: ${script.name} by user: ${user.username}`);
    return NextResponse.json(script, { status: 201 });
  } catch (error) {
    console.error('Error creating script:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to create script' },
      { status: 500 }
    );
  }
}
