import { auth } from "../../../../../../auth";
import { NextRequest, NextResponse } from 'next/server';

import { successResponse, errorResponse, handleAPIError } from '@/utils/apiResponse';
import { logger } from '@/utils/logger';
import { withTransaction } from '@/utils/withTransaction';
import { validateRequestBody } from '@/utils/inputValidation';
import { ValidationError } from '@/errors/ApiErrors';
import { z } from 'zod';
import prisma from '@/lib/prisma';
import { logActivity } from '@/utils/passwordUtils';
import { requireAdminAuth } from '@/lib/auth-helpers';

// Validation schema for site scripts
const SiteScriptSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().max(500).optional(),
  scriptType: z.enum(['head', 'body', 'footer']),
  content: z.string().min(1),
  isActive: z.boolean().default(true),
  position: z.number().int().default(0),
});
/**
 * GET /api/admin/scripts/[id]
 * Get a specific script with enterprise patterns
 */
async function getScript(
  request: NextRequest,
  session: any,
  params: Promise<{ id: string }>
) {
  const correlationId = request.headers.get('x-correlation-id') || 'unknown';
  const startTime = Date.now();
  
  try {
    const resolvedParams = await params;
    const { id } = resolvedParams;
    logger.info('Script retrieval started', {
      requestId: correlationId,
      action: 'get_script',
      userId: session.user.id,
      scriptId: id
    });
    // Validate UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(id)) {
      throw new ValidationError('Invalid script ID format', 'INVALID_ID_FORMAT');
    }
    const script = await withTransaction(async (tx) => {
      return await tx.siteScript.findUnique({
        where: { id }
      });
    });
    if (!script) {
      throw new ValidationError('Script not found', 'RESOURCE_NOT_FOUND');
    }
    const duration = Date.now() - startTime;
    
    logger.business('Script retrieved successfully', {
      duration,
      metadata: {
        scriptId: id,
        scriptName: script.name,
        scriptType: script.scriptType
      }
    });
    return successResponse(script, {
      requestId: correlationId
    });
  } catch (error) {
    logger.error('Failed to retrieve script', error as Error, {
      action: 'get_script_error',
      duration: Date.now() - startTime
    });
    return handleAPIError(error);
  }
}

/**
 * PUT /api/admin/scripts/[id]
 * Update a script with enterprise patterns
 */
async function updateScript(
  request: NextRequest,
  session: any,
  params: Promise<{ id: string }>
) {
  const correlationId = request.headers.get('x-correlation-id') || 'unknown';
  const startTime = Date.now();
  
  try {
    const resolvedParams = await params;
    const { id } = resolvedParams;
    logger.info('Script update started', {
      action: 'update_script',
      requestId: correlationId,
      userId: session.user.id,
      scriptId: id
    });
    // Validate request body
    const validation = await validateRequestBody(request, SiteScriptSchema);
    if (!validation.success) {
      throw validation.error;
    }
    const updateData = validation.data;
    const result = await withTransaction(async (tx) => {
      // Check if script exists
      const existingScript = await tx.siteScript.findUnique({
        where: { id }
      });
      if (!existingScript) {
        throw new ValidationError('Script not found', 'RESOURCE_NOT_FOUND');
      }
      // Security check: Log script content changes for audit
      if (updateData.content !== existingScript.content) {
        logger.security('suspicious_activity', {
          requestId: correlationId,
          metadata: {
            action: 'script_content_modified',
            userId: session.user.id,
            scriptId: id,
            scriptName: existingScript.name,
            oldContentLength: existingScript.content.length,
            newContentLength: updateData.content.length
          },
          ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || undefined,
          userAgent: request.headers.get('user-agent') || undefined
        });
      }
      // Update script
      const updatedScript = await tx.siteScript.update({
        where: { id },
        data: updateData
      });
      return { existingScript, updatedScript };
    });
    // Log activity
    await logActivity(
      session.user.id,
      'script_updated',
      `Updated script: ${result.updatedScript.name}`,
      request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
      request.headers.get('user-agent') || 'unknown',
      'script',
      result.updatedScript.id
    );
    logger.business('Script updated successfully', {
      duration: Date.now() - startTime,
      metadata: {
        scriptId: id,
        scriptName: result.updatedScript.name,
        scriptType: result.updatedScript.scriptType
      }
    });
    return successResponse(result.updatedScript, {
      requestId: correlationId
    });
  } catch (error) {
    logger.error('Failed to update script', error as Error, {
      action: 'update_script_error',
      duration: Date.now() - startTime
    });
    return handleAPIError(error);
  }
}

/**
 * DELETE /api/admin/scripts/[id]
 * Delete a script with enterprise patterns
 */
async function deleteScript(
  request: NextRequest,
  session: any,
  params: Promise<{ id: string }>
) {
  const correlationId = request.headers.get('x-correlation-id') || 'unknown';
  const startTime = Date.now();
  
  try {
    const resolvedParams = await params;
    const { id } = resolvedParams;
    logger.info('Script deletion started', {
      action: 'delete_script',
      requestId: correlationId,
      userId: session.user.id,
      scriptId: id
    });
    const result = await withTransaction(async (tx) => {
      const existingScript = await tx.siteScript.findUnique({
        where: { id }
      });
      if (!existingScript) {
        throw new ValidationError('Script not found', 'RESOURCE_NOT_FOUND');
      }
      // Delete the script
      await tx.siteScript.delete({
        where: { id }
      });
      return existingScript;
    });
    // Log activity
    await logActivity(
      session.user.id,
      'script_deleted',
      `Deleted script: ${result.name}`,
      request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
      request.headers.get('user-agent') || 'unknown',
      'script',
      id
    );
    logger.security('suspicious_activity', {
        action: 'script_deleted',
      metadata: {
        userId: session.user.id,
        deletedScriptId: id,
        scriptName: result.name,
        scriptType: result.scriptType
      },
      ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || undefined,
      userAgent: request.headers.get('user-agent') || undefined
    });
    logger.business('Script deleted successfully', {
      duration: Date.now() - startTime,
      metadata: {
        scriptId: id,
        scriptName: result.name
      }
    });
    return successResponse(
      {
        id: result.id,
        name: result.name,
        deletedAt: new Date().toISOString()
      },
      {
        requestId: correlationId
      }
    );
  } catch (error) {
    logger.error('Failed to delete script', error as Error, {
      action: 'delete_script_error',
      duration: Date.now() - startTime
    });
    return handleAPIError(error);
  }
}

// Export handlers with comprehensive authentication and monitoring
export async function GET(request: NextRequest, context: { params: Promise<{ id: string }> }) {
  const { user, response } = await requireAdminAuth(request);
  if (response) return response;
  
  const session = { user };
  return getScript(request, session, context.params);
}

export async function PUT(request: NextRequest, context: { params: Promise<{ id: string }> }) {
  const { user, response } = await requireAdminAuth(request);
  if (response) return response;
  
  const session = { user };
  return updateScript(request, session, context.params);
}

export async function DELETE(request: NextRequest, context: { params: Promise<{ id: string }> }) {
  const { user, response } = await requireAdminAuth(request);
  if (response) return response;
  
  const session = { user };
  return deleteScript(request, session, context.params);
}
