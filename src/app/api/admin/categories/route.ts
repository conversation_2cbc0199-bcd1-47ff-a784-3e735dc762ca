import { auth } from "../../../../../auth";;
import { requireAdminAuth } from '@/lib/auth-helpers';;
import { NextRequest, NextResponse } from 'next/server';

import { db } from '@/lib/database/database-service';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ErrorFactory } from '@/lib/error-handling/centralized-errors';
import { validateRequestBody, ApiSchemas, BaseSchemas } from '@/lib/validation/security-validator';
import { createRateLimitMiddleware, RateLimiters } from '@/lib/middleware/rate-limiter';
import { z } from 'zod';
import slugify from 'slugify';

// Validation schemas
const CategorySchema = z.object({
  name: BaseSchemas.nonEmptyString.max(100),
  description: z.string().max(500).optional(),
  slug: z.string().max(100).regex(/^[a-z0-9-]+$/, 'Slug must contain only lowercase letters, numbers, and hyphens').optional(),
});

// Rate limiting middleware
const adminRateLimit = createRateLimitMiddleware('admin');

// GET handler - Get all categories
export async function GET(request: NextRequest) {
  const startTime = Date.now();
  const requestId = crypto.randomUUID();

  try {
    // Apply rate limiting
    const rateLimitResponse = await adminRateLimit(request);
    if (rateLimitResponse) return rateLimitResponse;

    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      throw ErrorFactory.unauthorized('Authentication required for categories access');
    }

    console.info(`[Categories API:${requestId}] GET request from user: ${user.email}`);
    
    // Fetch categories with database service
    const categories = await db.findMany(
      db.client.category,
      {
        orderBy: { name: 'asc' }
      },
      'categories-list'
    );

    const responseTime = Date.now() - startTime;
    console.info(`[Categories API:${requestId}] Retrieved ${categories.length} categories in ${responseTime}ms`);
    
    return NextResponse.json({
      success: true,
      data: categories,
      meta: {
        count: categories.length,
        timestamp: new Date().toISOString(),
      }
    }, {
      status: 200,
      headers: {
        'Cache-Control': 'no-store, max-age=0',
        'Pragma': 'no-cache',
        'X-Request-ID': requestId,
        'X-Response-Time': responseTime.toString(),
      },
    });
  } catch (error) {
    const responseTime = Date.now() - startTime;
    console.error(`[Categories API:${requestId}] Error in ${responseTime}ms:`, error);
    
    return ErrorLogger.logAndRespond(error as Error, {
      operation: 'GET categories',
      requestId,
      responseTime,
      user: request.headers.get('user-agent'),
    });
  }
}

// POST handler - Create a new category
export async function POST(request: NextRequest) {
  const startTime = Date.now();
  const requestId = crypto.randomUUID();

  try {
    // Apply rate limiting
    const rateLimitResponse = await adminRateLimit(request);
    if (rateLimitResponse) return rateLimitResponse;

    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      throw ErrorFactory.unauthorized('Authentication required for category creation');
    }

    console.info(`[Categories API:${requestId}] POST request from user: ${user.email}`);
    
    // Validate request body with security checks
    const validatedData = await validateRequestBody(request, CategorySchema);
    
    // Generate slug if not provided
    const categorySlug = validatedData.slug || 
      slugify(validatedData.name, { 
        lower: true, 
        strict: true,
        remove: /[*+~.()'"!:@]/g 
      });

    // Create category with database service
    const category = await db.create(
      db.client.category,
      {
        data: {
          name: validatedData.name.trim(),
          description: validatedData.description?.trim() || null,
          slug: categorySlug,
        },
      },
      'category-create'
    ) as any;

    const responseTime = Date.now() - startTime;
    console.info(`[Categories API:${requestId}] Created category: ${category.name} in ${responseTime}ms`);
    
    return NextResponse.json({
      success: true,
      data: category,
      message: 'Category created successfully',
    }, {
      status: 201,
      headers: {
        'Cache-Control': 'no-store',
        'X-Request-ID': requestId,
        'X-Response-Time': responseTime.toString(),
      },
    });
  } catch (error) {
    const responseTime = Date.now() - startTime;
    console.error(`[Categories API:${requestId}] Error in ${responseTime}ms:`, error);
    
    return ErrorLogger.logAndRespond(error as Error, {
      operation: 'POST categories',
      requestId,
      responseTime,
      user: request.headers.get('user-agent'),
    });
  }
}