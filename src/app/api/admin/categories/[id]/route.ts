import { auth } from "../../../../../../auth";
import { requireAdminAuth } from '@/lib/auth-helpers';
import { NextRequest, NextResponse } from 'next/server';

import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Schema for category updates
const updateCategorySchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name too long').optional(),
  slug: z.string().min(1, 'Slug is required').max(100, 'Slug too long').optional(),
  description: z.string().max(500, 'Description too long').optional(),
  color: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid color format').optional(),
});

interface RouteParams {
  params: {
    id: string;
  };
}

// GET - Retrieve a specific category
export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const categoryId = parseInt(params.id);
    
    if (isNaN(categoryId)) {
      return NextResponse.json(
        { error: 'Invalid category ID' },
        { status: 400 }
      );
    }

    console.log(`[Category API] GET request for category ${categoryId} from user: ${user.email}`);

    try {
      const category = await prisma.category.findUnique({
        where: { id: categoryId }
      });

      if (!category) {
        return NextResponse.json(
          { error: 'Category not found' },
          { status: 404 }
        );
      }

      console.log(`[Category API] Retrieved category ${categoryId} by user: ${user.email}`);

      return NextResponse.json({
        success: true,
        category
      });

    } catch (dbError) {
      console.error('Database error in category GET:', dbError);
      return NextResponse.json(
        { error: 'Failed to retrieve category' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error in category GET API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT - Update a category
export async function PUT(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const categoryId = parseInt(params.id);
    
    if (isNaN(categoryId)) {
      return NextResponse.json(
        { error: 'Invalid category ID' },
        { status: 400 }
      );
    }

    console.log(`[Category API] PUT request for category ${categoryId} from user: ${user.email}`);

    const body = await request.json();
    
    // Validate input
    const validation = updateCategorySchema.safeParse(body);
    if (!validation.success) {
      return NextResponse.json(
        { 
          error: 'Validation failed',
          details: validation.error.errors 
        },
        { status: 400 }
      );
    }

    const data = validation.data;

    try {
      // Check if category exists
      const existingCategory = await prisma.category.findUnique({
        where: { id: categoryId }
      });

      if (!existingCategory) {
        return NextResponse.json(
          { error: 'Category not found' },
          { status: 404 }
        );
      }

      // Check for slug conflicts if slug is being updated
      if (data.slug && data.slug !== existingCategory.slug) {
        const slugConflict = await prisma.category.findUnique({
          where: { slug: data.slug }
        });

        if (slugConflict) {
          return NextResponse.json(
            { error: 'Slug already exists' },
            { status: 409 }
          );
        }
      }

      // Update the category
      const updatedCategory = await prisma.category.update({
        where: { id: categoryId },
        data: {
          ...data,
          updatedAt: new Date(),
        }
      });

      console.log(`[Category API] Updated category ${categoryId} by user: ${user.email}`);

      return NextResponse.json({
        success: true,
        message: 'Category updated successfully',
        category: updatedCategory
      });

    } catch (dbError) {
      console.error('Database error in category PUT:', dbError);
      return NextResponse.json(
        { error: 'Failed to update category' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error in category PUT API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE - Delete a category
export async function DELETE(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const categoryId = parseInt(params.id);
    
    if (isNaN(categoryId)) {
      return NextResponse.json(
        { error: 'Invalid category ID' },
        { status: 400 }
      );
    }

    console.log(`[Category API] DELETE request for category ${categoryId} from user: ${user.email}`);

    try {
      // Check if category exists
      const category = await prisma.category.findUnique({
        where: { id: categoryId }
      });

      if (!category) {
        return NextResponse.json(
          { error: 'Category not found' },
          { status: 404 }
        );
      }

      // Delete the category
      await prisma.category.delete({
        where: { id: categoryId }
      });

      console.log(`[Category API] Deleted category ${categoryId} by user: ${user.email}`);

      return NextResponse.json({
        success: true,
        message: 'Category deleted successfully',
        deletedCategory: {
          id: category.id,
          name: category.name,
          slug: category.slug,
        }
      });

    } catch (dbError) {
      console.error('Database error in category DELETE:', dbError);
      return NextResponse.json(
        { error: 'Failed to delete category' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error in category DELETE API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
