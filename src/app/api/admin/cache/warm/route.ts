import { auth } from "../../../../../../auth";
import { requireAdminAuth } from '@/lib/auth-helpers';
import { NextRequest, NextResponse } from 'next/server';


// POST handler - Cache warming API for preloading critical data
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    console.log(`[Cache Warm API] POST request from user: ${user.email}`);

    const results: { [key: string]: boolean | string } = {};

    // Warm logos cache by preloading logo data
    try {
      const logoResponse = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/logos`, {
        headers: {
          'Cache-Control': 'no-cache',
          'User-Agent': 'Cache-Warmer/1.0'
        }
      });
      
      if (logoResponse.ok) {
        results.logos = true;
        console.log('✓ Logos cache warmed successfully');
      } else {
        results.logos = `Failed: ${logoResponse.status}`;
        console.warn('⚠ Failed to warm logos cache:', logoResponse.statusText);
      }
    } catch (error) {
      results.logos = `Error: ${error instanceof Error ? error.message : 'Unknown error'}`;
      console.error('✗ Error warming logos cache:', error);
    }

    // Warm portfolio cache by preloading portfolio data
    try {
      const portfolioResponse = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/portfolio`, {
        headers: {
          'Cache-Control': 'no-cache',
          'User-Agent': 'Cache-Warmer/1.0'
        }
      });
      
      if (portfolioResponse.ok) {
        results.portfolio = true;
        console.log('✓ Portfolio cache warmed successfully');
      } else {
        results.portfolio = `Failed: ${portfolioResponse.status}`;
        console.warn('⚠ Failed to warm portfolio cache:', portfolioResponse.statusText);
      }
    } catch (error) {
      results.portfolio = `Error: ${error instanceof Error ? error.message : 'Unknown error'}`;
      console.error('✗ Error warming portfolio cache:', error);
    }

    // Warm site settings cache
    try {
      const settingsResponse = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}/api/settings`, {
        headers: {
          'Cache-Control': 'no-cache',
          'User-Agent': 'Cache-Warmer/1.0'
        }
      });
      
      if (settingsResponse.ok) {
        results.settings = true;
        console.log('✓ Settings cache warmed successfully');
      } else {
        results.settings = `Failed: ${settingsResponse.status}`;
        console.warn('⚠ Failed to warm settings cache:', settingsResponse.statusText);
      }
    } catch (error) {
      results.settings = `Error: ${error instanceof Error ? error.message : 'Unknown error'}`;
      console.error('✗ Error warming settings cache:', error);
    }

    // Warm homepage and critical pages (if they exist as API endpoints)
    const criticalEndpoints = [
      '/api/public/logos',
      '/api/public/portfolio',
      '/api/public/homepage-data'
    ];

    for (const endpoint of criticalEndpoints) {
      try {
        const response = await fetch(`${process.env.NEXTAUTH_URL || 'http://localhost:3000'}${endpoint}`, {
          headers: {
            'Cache-Control': 'no-cache',
            'User-Agent': 'Cache-Warmer/1.0'
          }
        });
        
        if (response.ok) {
          results[endpoint.replace('/api/', '')] = true;
          console.log(`✓ ${endpoint} cache warmed successfully`);
        } else {
          results[endpoint.replace('/api/', '')] = `Failed: ${response.status}`;
          console.warn(`⚠ Failed to warm ${endpoint} cache:`, response.statusText);
        }
      } catch (error) {
        results[endpoint.replace('/api/', '')] = `Error: ${error instanceof Error ? error.message : 'Unknown error'}`;
        console.error(`✗ Error warming ${endpoint} cache:`, error);
      }
    }

    // Add artificial delay to show warming process (optional)
    await new Promise(resolve => setTimeout(resolve, 1000));

    const successCount = Object.values(results).filter(result => result === true).length;
    const totalCount = Object.keys(results).length;

    console.log(`[Cache Warm API] Completed cache warming. Success: ${successCount}/${totalCount} by user: ${user.email}`);

    return NextResponse.json({
      success: true,
      message: `Cache warming completed. ${successCount}/${totalCount} operations successful.`,
      results,
      summary: {
        total: totalCount,
        successful: successCount,
        failed: totalCount - successCount
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Cache warming error:', error);
    return NextResponse.json(
      {
        error: 'Failed to warm cache',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
} 