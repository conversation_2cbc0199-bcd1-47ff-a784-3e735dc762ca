import { auth } from "../../../../../auth";;
import { requireAdminAuth } from '@/lib/auth-helpers';;
import { NextRequest, NextResponse } from 'next/server';

import { revalidatePath, revalidateTag } from 'next/cache';

// POST handler - Clear cache
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    console.log(`[Cache API] POST request from user: ${user.email}`);
    
    const body = await request.json();
    const { type, paths, tags } = body;
    
    const results: { [key: string]: boolean } = {};
    
    try {
      switch (type) {
        case 'all':
          // Revalidate common paths
          const commonPaths = [
            '/',
            '/portfolio',
            '/logos',
            '/blog',
            '/about',
            '/contact',
            '/catalogue',
            '/services'
          ];
          
          commonPaths.forEach(path => {
            try {
              revalidatePath(path);
              results[path] = true;
            } catch (error) {
              console.warn(`Failed to revalidate path ${path}:`, error);
              results[path] = false;
            }
          });
          break;
          
        case 'paths':
          if (paths && Array.isArray(paths)) {
            paths.forEach((path: string) => {
              try {
                revalidatePath(path);
                results[path] = true;
              } catch (error) {
                console.warn(`Failed to revalidate path ${path}:`, error);
                results[path] = false;
              }
            });
          }
          break;
          
        case 'tags':
          if (tags && Array.isArray(tags)) {
            tags.forEach((tag: string) => {
              try {
                revalidateTag(tag);
                results[tag] = true;
              } catch (error) {
                console.warn(`Failed to revalidate tag ${tag}:`, error);
                results[tag] = false;
              }
            });
          }
          break;
          
        default:
          return NextResponse.json(
            { error: 'Invalid cache operation type' },
            { status: 400 }
          );
      }
      
      console.log(`[Cache API] Cache cleared successfully by user: ${user.email}`);
      
      return NextResponse.json({
        success: true,
        message: `Cache cleared for type: ${type}`,
        results,
        clearedAt: new Date().toISOString()
      });
      
    } catch (cacheError) {
      console.error('Cache operation error:', cacheError);
      return NextResponse.json(
        { error: 'Failed to clear cache' },
        { status: 500 }
      );
    }
    
  } catch (error) {
    console.error('Error in cache API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// GET handler - Get cache status
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    console.log(`[Cache API] GET request from user: ${user.email}`);
    
    return NextResponse.json({
      success: true,
      message: 'Cache API is available',
      supportedOperations: ['all', 'paths', 'tags'],
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('Error in cache status API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
