import { auth } from "../../../../../../auth";
import { requireAdminAuth } from '@/lib/auth-helpers';
import { NextRequest, NextResponse } from 'next/server';

import { getRedisClient, CacheService } from '@/lib/redis';
import { rateLimitAdmin } from '@/lib/rateLimiter';

// Create cache service instance
const cacheService = new CacheService();

// Types for Redis statistics
interface RedisStats {
  connection: {
    status: string;
    uptime: number;
    version: string;
    memory: {
      used: number;
      peak: number;
      fragmentation: number;
    };
  };
  keys: {
    total: number;
    byType: Record<string, number>;
    expired: number;
  };
  performance: {
    hitRate: number;
    avgResponseTime: number;
    totalOperations: number;
  };
  cache: {
    portfolio: number;
    blog: number;
    images: number;
    analytics: number;
    sessions: number;
    rateLimit: number;
  };
}

// Helper function to get session statistics
async function getSessionStatistics() {
  try {
    // Return basic stats since we don't have a specific session service
    return { activeSessions: 0 };
  } catch (error) {
    return { activeSessions: 0 };
  }
}

// GET handler - Get Redis stats
export async function GET(request: NextRequest) {
  try {
    // Check admin authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;

    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    console.log(`[Redis Cache API] GET stats request from user: ${user.username}`);
    const redis = getRedisClient();
    
    if (!redis) {
      return NextResponse.json({
        success: false,
        error: 'Redis client not available',
      }, { status: 503 });
    }

    // Get Redis server info
    const info = await redis.info();
    const infoLines = info.split('\r\n');
    const infoObj: Record<string, string> = {};
    
    for (const line of infoLines) {
      if (line.includes(':')) {
        const [key, value] = line.split(':');
        infoObj[key] = value;
      }
    }

    // Get key statistics
    const allKeys = await redis.keys('*');
    const keysByType: Record<string, number> = {
      'portfolio': 0,
      'blog': 0,
      'images': 0,
      'analytics': 0,
      'session': 0,
      'rate_limit': 0,
      'verification': 0,
      'metrics': 0,
      'other': 0,
    };

    // Count keys by type
    for (const key of allKeys) {
      if (key.startsWith('portfolio:')) keysByType.portfolio++;
      else if (key.startsWith('blog:')) keysByType.blog++;
      else if (key.startsWith('images:')) keysByType.images++;
      else if (key.startsWith('analytics:')) keysByType.analytics++;
      else if (key.startsWith('session:')) keysByType.session++;
      else if (key.startsWith('rate_limit:')) keysByType.rate_limit++;
      else if (key.startsWith('verification:')) keysByType.verification++;
      else if (key.startsWith('metrics:')) keysByType.metrics++;
      else keysByType.other++;
    }

    // Get performance metrics
    const [sessionStats, rateLimitStats] = await Promise.all([
      getSessionStatistics(),
      rateLimitAdmin.getStats(),
    ]);

    const stats: RedisStats = {
      connection: {
        status: 'connected',
        uptime: parseFloat(infoObj.uptime_in_seconds || '0'),
        version: infoObj.redis_version || 'unknown',
        memory: {
          used: parseInt(infoObj.used_memory || '0'),
          peak: parseInt(infoObj.used_memory_peak || '0'),
          fragmentation: parseFloat(infoObj.mem_fragmentation_ratio || '1'),
        },
      },
      keys: {
        total: allKeys.length,
        byType: keysByType,
        expired: 0, // Redis automatically removes expired keys
      },
      performance: {
        hitRate: 0, // Would need to track this separately
        avgResponseTime: 0, // Would need to track this separately
        totalOperations: parseInt(infoObj.total_commands_processed || '0'),
      },
      cache: {
        portfolio: keysByType.portfolio,
        blog: keysByType.blog,
        images: keysByType.images,
        analytics: keysByType.analytics,
        sessions: sessionStats.activeSessions,
        rateLimit: rateLimitStats.totalActiveWindows,
      },
    };

    console.log(`[Redis Cache API] Stats retrieved by user: ${user.username}`);
    return NextResponse.json({
      success: true,
      data: stats,
      generatedAt: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error getting Redis stats:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to get Redis statistics',
      details: error instanceof Error ? error.message : 'Unknown error',
    }, { status: 500 });
  }
}

// POST handler - Manage Redis cache
export async function POST(request: NextRequest) {
  try {
    // Check admin authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;

    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    console.log(`[Redis Cache API] POST management request from user: ${user.username}`);
    const body = await request.json();
    const { action, target, key } = body;

    if (!action) {
      return NextResponse.json({
        success: false,
        error: 'Action is required',
      }, { status: 400 });
    }

    let result: any = {};

    switch (action) {
      case 'clear':
        if (target) {
          await clearCacheByType(target);
          result.message = `Cleared ${target} cache`;
        } else if (key) {
          await cacheService.del(key);
          result.message = `Cleared key: ${key}`;
        } else {
          // Clear all cache
          const redis = getRedisClient();
          if (redis) {
            await redis.flushdb();
            result.message = 'Cleared all cache';
          }
        }
        break;

      case 'flush':
        await cacheService.flush();
        result.message = 'Flushed all cache';
        break;

      case 'cleanup':
        const rateLimitCleanup = await rateLimitAdmin.cleanup();
        result.message = `Cleaned up ${rateLimitCleanup} rate limit windows`;
        break;

      case 'get':
        if (!key) {
          return NextResponse.json({
            success: false,
            error: 'Key is required for get action',
          }, { status: 400 });
        }
        const value = await cacheService.get(key);
        result.key = key;
        result.value = value;
        result.exists = value !== null;
        break;

      case 'set':
        if (!key || !body.value) {
          return NextResponse.json({
            success: false,
            error: 'Key and value are required for set action',
          }, { status: 400 });
        }
        await cacheService.set(key, body.value, body.ttl);
        result.message = `Set key: ${key}`;
        break;

      case 'exists':
        if (!key) {
          return NextResponse.json({
            success: false,
            error: 'Key is required for exists action',
          }, { status: 400 });
        }
        const exists = await cacheService.exists(key);
        result.key = key;
        result.exists = exists;
        break;

      default:
        return NextResponse.json({
          success: false,
          error: 'Invalid action',
        }, { status: 400 });
    }

    console.log(`[Redis Cache API] Action ${action} performed by user: ${user.username}`);
    return NextResponse.json({
      success: true,
      action,
      target,
      key,
      result,
      performedAt: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error managing Redis cache:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to manage Redis cache',
      details: error instanceof Error ? error.message : 'Unknown error',
    }, { status: 500 });
  }
}

// Helper function to clear cache by type
async function clearCacheByType(type: string): Promise<void> {
  const redis = getRedisClient();
  if (!redis) {
    return;
  }

  let pattern: string;
  
  switch (type) {
    case 'portfolio':
      pattern = 'portfolio:*';
      break;
    case 'blog':
      pattern = 'blog:*';
      break;
    case 'images':
      pattern = 'images:*';
      break;
    case 'analytics':
      pattern = 'analytics:*';
      break;
    case 'sessions':
      pattern = 'session:*';
      break;
    case 'rate_limit':
      pattern = 'rate_limit:*';
      break;
    case 'verification':
      pattern = 'verification:*';
      break;
    case 'metrics':
      pattern = 'metrics:*';
      break;
    default:
      pattern = `${type}:*`;
  }

  const keys = await redis.keys(pattern);
  if (keys.length > 0) {
    await redis.del(...keys);
    console.log(`Cleared ${keys.length} keys matching pattern: ${pattern}`);
  }
} 
