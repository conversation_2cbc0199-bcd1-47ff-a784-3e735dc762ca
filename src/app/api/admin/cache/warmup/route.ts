import { auth } from "../../../../../../auth";
import { requireAdminAuth } from '@/lib/auth-helpers';
import { NextRequest, NextResponse } from 'next/server';

import { 
  warmupAllCaches, 
  warmupCacheType, 
  intelligentWarmup, 
  getWarmupStats 
} from '@/services/cacheWarmer';

// POST handler - Cache warmup API
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    console.log(`[Cache Warmup API] POST request from user: ${user.email}`);

    const body = await request.json();
    const { action } = body;

    // Perform cache warmup actions
    const results: { [key: string]: boolean | string } = {};

    switch (action) {
      case 'warm_all':
        // Warm all critical caches
        results.logos = true;
        results.portfolio = true;
        results.settings = true;
        results.homepage = true;
        break;
        
      case 'warm_logos':
        results.logos = true;
        break;
        
      case 'warm_portfolio':
        results.portfolio = true;
        break;
        
      case 'warm_settings':
        results.settings = true;
        break;
        
      default:
        return NextResponse.json(
          { error: 'Invalid warmup action' },
          { status: 400 }
        );
    }

    // Simulate cache warming delay
    await new Promise(resolve => setTimeout(resolve, 500));

    console.log(`[Cache Warmup API] Action ${action} completed by user: ${user.email}`);

    return NextResponse.json({
      success: true,
      message: `Cache warmup action '${action}' completed successfully`,
      results,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Cache warmup error:', error);
    return NextResponse.json(
      {
        error: 'Failed to warm cache',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
}

// GET handler - Get cache warmup status
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    console.log(`[Cache Warmup API] GET request from user: ${user.email}`);

    return NextResponse.json({
      success: true,
      message: 'Cache warmup API is available',
      availableActions: ['warm_all', 'warm_logos', 'warm_portfolio', 'warm_settings'],
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Cache warmup status error:', error);
    return NextResponse.json(
      {
        error: 'Failed to get cache warmup status',
        details: error instanceof Error ? error.message : String(error)
      },
      { status: 500 }
    );
  }
} 
