import { auth } from "../../../../../auth";;
import { requireAdminAuth } from '@/lib/auth-helpers';;
import { NextRequest, NextResponse } from 'next/server';

import { S3Client, ListObjectsV2Command } from '@aws-sdk/client-s3';
import { getDefaultStorageConfig } from '@/lib/storageConfig';
import { getS3ImageUrl } from '@/utils/imageUtils';

// Get S3 client with the current configuration
async function getS3Client() {
  const config = await getDefaultStorageConfig();

  if (!config) {
    throw new Error('No storage configuration found');
  }

  return new S3Client({
    region: config.region,
    endpoint: config.endpoint,
    credentials: {
      accessKeyId: config.accessKey,
      secretAccessKey: config.secretKey,
    },
    forcePathStyle: true // Required for some S3-compatible storage
  });
}

// Get the current bucket name
async function getBucketName(): Promise<string> {
  const config = await getDefaultStorageConfig();
  return config?.bucketName || process.env.NEXT_PUBLIC_S3_BUCKET || 'mocky';
}

// GET handler - Browse S3 images
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    const prefix = searchParams.get('prefix') || 'images/portfolio';

    const s3Client = await getS3Client();
    const bucketName = await getBucketName();

    // Construct the search prefix
    let searchPrefix = prefix;
    if (category && category !== 'all') {
      searchPrefix = `${prefix}/${category}`;
    }

    // Ensure prefix ends with / for directory listing
    if (!searchPrefix.endsWith('/')) {
      searchPrefix += '/';
    }

    console.log(`Browsing S3 images with prefix: ${searchPrefix}`);

    const command = new ListObjectsV2Command({
      Bucket: bucketName,
      Prefix: searchPrefix,
      MaxKeys: 1000,
    });

    const s3Response = await s3Client.send(command);

    if (!s3Response.Contents) {
      return NextResponse.json([]);
    }

    // Process and filter images
    const images = s3Response.Contents
      .filter(item => {
        if (!item.Key) return false;
        
        // Skip directories
        if (item.Key.endsWith('/')) return false;
        
        // Only include image files
        const ext = item.Key.split('.').pop()?.toLowerCase();
        return ['jpg', 'jpeg', 'png', 'webp', 'gif', 'svg'].includes(ext || '');
      })
      .map(item => {
        const key = item.Key!;
        const filename = key.split('/').pop() || '';
        const pathParts = key.split('/');
        const itemCategory = pathParts.length >= 3 ? pathParts[2] : 'uncategorized';
        
        // Construct the full URL using our utility function
        const imageUrl = getS3ImageUrl(key);

        return {
          key,
          filename,
          category: itemCategory,
          url: imageUrl,
          size: item.Size || 0,
          lastModified: item.LastModified?.toISOString() || new Date().toISOString(),
          displayName: filename
            .replace(/\.(jpg|jpeg|png|webp|gif|svg)$/i, '')
            .replace(/[-_]/g, ' ')
            .replace(/\b\w/g, (char) => char.toUpperCase())
        };
      })
      .sort((a, b) => {
        // Sort by category first, then by filename
        if (a.category !== b.category) {
          return (a.category || '').localeCompare(b.category || '');
        }
        return a.filename.localeCompare(b.filename);
      });

    console.log(`Found ${images.length} images in S3`);

    return NextResponse.json(images, {
      headers: {
        'Cache-Control': 'public, max-age=30, s-maxage=60',
      },
    });

  } catch (error) {
    console.error('Error browsing S3 images:', error);
    
    // Provide more specific error messages
    if (error instanceof Error) {
      if (error.message.includes('No storage configuration')) {
        return NextResponse.json(
          { error: 'Storage configuration not found. Please contact support.' },
          { status: 500 }
        );
      } else if (error.message.includes('AccessDenied')) {
        return NextResponse.json(
          { error: 'Access denied to storage. Please contact support.' },
          { status: 403 }
        );
      } else if (error.message.includes('NoSuchBucket')) {
        return NextResponse.json(
          { error: 'Storage bucket not found. Please contact support.' },
          { status: 404 }
        );
      }
    }
    
    return NextResponse.json(
      { error: 'Failed to browse S3 images' },
      { status: 500 }
    );
  }
}
