import { auth } from "../../../../../auth";;
import { requireAdminAuth } from '@/lib/auth-helpers';;
import { NextRequest, NextResponse } from 'next/server';

import { z } from 'zod';
import { TestType, testDescriptions } from '@/utils/testUtils';
import {
  testS3Connection,
  testS3Upload,
  testDatabaseConnection
} from '@/utils/testUtils.server';

// Validation schema for test request
const TestRequestSchema = z.object({
  type: z.nativeEnum(TestType),
  config: z.object({
    region: z.string().optional(),
    endpoint: z.string().optional(),
    bucketName: z.string().optional(),
    accessKeyId: z.string().optional(),
    secretAccessKey: z.string().optional(),
  }).optional(),
});

/**
 * POST handler for running tests
 */
export async function POST(request: NextRequest) {
  try {
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;

    console.log(`[Tests API] POST request from user: ${user!.username}`);
    
    // Parse and validate request body
    const body = await request.json();
    const validationResult = TestRequestSchema.safeParse(body);
    
    if (!validationResult.success) {
      return NextResponse.json(
        { error: 'Invalid request', details: validationResult.error.format() },
        { status: 400 }
      );
    }

    const { type, config } = validationResult.data;
    
    // Run the appropriate test
    let result;
    switch (type) {
      case TestType.S3_CONNECTION:
        result = await testS3Connection(config as any);
        break;
      case TestType.S3_UPLOAD:
        result = await testS3Upload(config as any);
        break;
      case TestType.DATABASE:
        result = await testDatabaseConnection();
        break;
      default:
        return NextResponse.json(
          { error: 'Invalid test type' },
          { status: 400 }
        );
    }

    console.log(`[Tests API] Executed test type: ${type} by user: ${user!.username}`);
    return NextResponse.json(result);
  } catch (error: any) {
    console.error('Error running test:', error);
    return NextResponse.json(
      { error: 'Internal Server Error', message: error.message },
      { status: 500 }
    );
  }
}

/**
 * GET handler for getting available test types
 */
export async function GET(request: NextRequest) {
  try {
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;

    console.log(`[Tests API] GET request from user: ${user!.username}`);
    
    // Return available test types
    const response_data = {
      testTypes: Object.values(TestType),
      descriptions: testDescriptions
    };

    console.log(`[Tests API] Retrieved test types by user: ${user!.username}`);
    return NextResponse.json(response_data);
  } catch (error: any) {
    console.error('Error getting test types:', error);
    return NextResponse.json(
      { error: 'Internal Server Error', message: error.message },
      { status: 500 }
    );
  }
}
