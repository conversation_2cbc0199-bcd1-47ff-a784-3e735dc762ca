import { auth } from "../../../../../auth";;;
import { NextRequest, NextResponse } from 'next/server';
import { requireAdminAuth } from '@/lib/auth-helpers';
import { analyticsService, withAnalytics } from '@/services/analyticsService';

// GET handler - Get analytics data
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    console.log(`[Analytics API] GET request from user: ${user.email}`);
    
    const { searchParams } = new URL(request.url);
    const period = searchParams.get('period') || 'today';
    const metric = searchParams.get('metric') || 'dashboard';

    let data;
    switch (metric) {
      case 'dashboard':
        data = await analyticsService.getAnalyticsData(period);
        break;
      
      case 'performance':
        data = await analyticsService.getPerformanceMetrics();
        break;

      default:
        return NextResponse.json(
          { error: 'Invalid metric type. Use "dashboard" or "performance"' },
          { status: 400 }
        );
    }

    console.log(`[Analytics API] Retrieved ${metric} data for ${period} by user: ${user.email}`);
    
    return NextResponse.json({
      success: true,
      data,
      period,
      metric,
      generatedAt: new Date().toISOString(),
    }, {
      headers: {
        'Cache-Control': 'no-store, max-age=0',
        'Pragma': 'no-cache',
      },
    });
  } catch (error) {
    console.error('Error fetching analytics:', error);
    return NextResponse.json(
      { error: 'Failed to fetch analytics data' },
      { status: 500 }
    );
  }
}

// POST handler - Track analytics events
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    console.log(`[Analytics API] POST request from user: ${user.email}`);
    
    const body = await request.json();
    const { event, data } = body;

    if (!event) {
      return NextResponse.json(
        { error: 'Event type is required' },
        { status: 400 }
      );
    }

    const userAgent = request.headers.get('user-agent') || undefined;
    const ip = request.headers.get('x-forwarded-for')?.split(',')[0] || 
               request.headers.get('x-real-ip') || undefined;

    switch (event) {
      case 'page_view':
        await analyticsService.trackPageView(data.path, userAgent, ip);
        break;
      case 'portfolio_view':
        await analyticsService.trackPortfolioView(data.itemId, data.category);
        break;
      case 'blog_view':
        await analyticsService.trackBlogView(data.slug);
        break;
      case 'download':
        await analyticsService.trackDownload(data.filename, data.category);
        break;
      case 'contact_submission':
        await analyticsService.trackContactSubmission(data.type);
        break;
      default:
        return NextResponse.json(
          { error: 'Invalid event type' },
          { status: 400 }
        );
    }

    console.log(`[Analytics API] Event tracked: ${event} by user: ${user.email}`);
    
    return NextResponse.json({
      success: true,
      message: 'Event tracked successfully',
      event,
      timestamp: new Date().toISOString(),
    }, {
      headers: {
        'Cache-Control': 'no-store',
      },
    });
  } catch (error) {
    console.error('Error tracking analytics event:', error);
    return NextResponse.json(
      { error: 'Failed to track analytics event' },
      { status: 500 }
    );
  }
}

// DELETE handler - Clear analytics cache
export async function DELETE(request: NextRequest) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    console.log(`[Analytics API] DELETE request from user: ${user.email}`);
    
    await analyticsService.clearCache();
    
    console.log(`[Analytics API] Cache cleared by user: ${user.email}`);
    
    return NextResponse.json({
      success: true,
      message: 'Analytics cache cleared successfully',
      clearedAt: new Date().toISOString(),
    }, {
      headers: {
        'Cache-Control': 'no-store',
      },
    });
  } catch (error) {
    console.error('Error clearing analytics cache:', error);
    return NextResponse.json(
      { error: 'Failed to clear analytics cache' },
      { status: 500 }
    );
  }
} 
