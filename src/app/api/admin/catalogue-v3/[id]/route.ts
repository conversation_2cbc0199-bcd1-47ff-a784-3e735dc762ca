import { auth } from "../../../../../../auth";
import { requireAdminAuth } from '@/lib/auth-helpers';
import { NextRequest, NextResponse } from 'next/server';

import { 
  CatalogueCRUDService, 
  CatalogueError, 
  CatalogueValidationError,
  CatalogueNotFoundError,
  CatalogueDuplicateError 
} from '@/services/catalogueCRUDService';

interface RouteParams {
  params: Promise<{ id: string }>;
}

// Enhanced GET handler for single item
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    // Check authentication
    const { user, response: authResponse } = await requireAdminAuth(request);
    if (authResponse) return authResponse;

    if (!user) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized',
        message: 'Authentication required',
        timestamp: new Date().toISOString()
      }, { status: 401 });
    }

    const { id } = await params;
    console.log(`API v3: Fetching catalogue item with ID: ${id}`);

    // Get catalogue item using enhanced CRUD service
    const catalogueItem = await CatalogueCRUDService.findById(id);

    if (!catalogueItem) {
      return NextResponse.json({
        success: false,
        error: 'Not Found',
        message: `Catalogue item with ID ${id} not found`,
        timestamp: new Date().toISOString()
      }, { status: 404 });
    }

    const responseData = {
      success: true,
      data: catalogueItem,
      timestamp: new Date().toISOString()
    };

    console.log(`API v3: Successfully fetched catalogue item: ${catalogueItem.service}`);

    return NextResponse.json(responseData, {
      status: 200,
      headers: {
        'Cache-Control': 'no-store, max-age=0, must-revalidate',
        'ETag': `"${catalogueItem.id}-${catalogueItem.updatedAt}"`
      }
    });

  } catch (error) {
    const { id } = await params;
    console.error(`API v3: Error in GET /admin/catalogue-v3/${id}:`, error);

    if (error instanceof CatalogueValidationError) {
      return NextResponse.json({
        success: false,
        error: 'Validation Error',
        message: error.message,
        field: error.field,
        timestamp: new Date().toISOString()
      }, { status: 400 });
    }

    if (error instanceof CatalogueError) {
      return NextResponse.json({
        success: false,
        error: error.code,
        message: error.message,
        timestamp: new Date().toISOString()
      }, { status: error.statusCode });
    }

    return NextResponse.json({
      success: false,
      error: 'Internal Server Error',
      message: 'Failed to fetch catalogue item',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

// Enhanced PUT handler for updating items
export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    // Check authentication
    const { user, response: authResponse } = await requireAdminAuth(request);
    if (authResponse) return authResponse;

    if (!user) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized',
        message: 'Authentication required',
        timestamp: new Date().toISOString()
      }, { status: 401 });
    }

    // Get client context
    const clientIp = request.headers.get('x-forwarded-for') || 'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';
    
    const { id } = await params;

    // Parse request body with error handling
    let body;
    try {
      body = await request.json();
    } catch (error) {
      return NextResponse.json({
        success: false,
        error: 'Invalid JSON',
        message: 'Request body must be valid JSON',
        timestamp: new Date().toISOString()
      }, { status: 400 });
    }

    console.log(`API v3: Updating catalogue item ${id}:`, body);

    // Update catalogue item using enhanced CRUD service
    const updatedCatalogue = await CatalogueCRUDService.update(id, body, {
      userId: user?.email || 'unknown',
      ipAddress: clientIp,
      userAgent: userAgent
    });

    const response = {
      success: true,
      data: updatedCatalogue,
      message: 'Catalogue item updated successfully',
      timestamp: new Date().toISOString()
    };

    console.log(`API v3: Updated catalogue item: ${updatedCatalogue.service}`);

    return NextResponse.json(response, {
      status: 200,
      headers: {
        'Cache-Control': 'no-store',
        'ETag': `"${updatedCatalogue.id}-${updatedCatalogue.updatedAt}"`
      }
    });

  } catch (error) {
    const { id } = await params;
    console.error(`API v3: Error in PUT /admin/catalogue-v3/${id}:`, error);

    if (error instanceof CatalogueValidationError) {
      return NextResponse.json({
        success: false,
        error: 'Validation Error',
        message: error.message,
        field: error.field,
        timestamp: new Date().toISOString()
      }, { status: 400 });
    }

    if (error instanceof CatalogueNotFoundError) {
      return NextResponse.json({
        success: false,
        error: 'Not Found',
        message: error.message,
        timestamp: new Date().toISOString()
      }, { status: 404 });
    }

    if (error instanceof CatalogueDuplicateError) {
      return NextResponse.json({
        success: false,
        error: 'Duplicate Error',
        message: error.message,
        timestamp: new Date().toISOString()
      }, { status: 409 });
    }

    if (error instanceof CatalogueError) {
      return NextResponse.json({
        success: false,
        error: error.code,
        message: error.message,
        timestamp: new Date().toISOString()
      }, { status: error.statusCode });
    }

    return NextResponse.json({
      success: false,
      error: 'Internal Server Error',
      message: 'Failed to update catalogue item',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

// Enhanced DELETE handler for single item
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    // Check authentication
    const { user, response: authResponse } = await requireAdminAuth(request);
    if (authResponse) return authResponse;

    if (!user) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized',
        message: 'Authentication required',
        timestamp: new Date().toISOString()
      }, { status: 401 });
    }

    // Get client context
    const clientIp = request.headers.get('x-forwarded-for') || 'unknown';
    const userAgent = request.headers.get('user-agent') || 'unknown';
    
    const { id } = await params;
    console.log(`API v3: Deleting catalogue item with ID: ${id}`);

    // Delete catalogue item using enhanced CRUD service
    const success = await CatalogueCRUDService.delete(id, {
      userId: user?.email || 'unknown',
      ipAddress: clientIp,
      userAgent: userAgent
    });

    const response = {
      success: true,
      data: { deleted: success },
      message: 'Catalogue item deleted successfully',
      timestamp: new Date().toISOString()
    };

    console.log(`API v3: Deleted catalogue item with ID: ${id}`);

    return NextResponse.json(response, {
      status: 200,
      headers: {
        'Cache-Control': 'no-store'
      }
    });

  } catch (error) {
    const { id } = await params;
    console.error(`API v3: Error in DELETE /admin/catalogue-v3/${id}:`, error);

    if (error instanceof CatalogueNotFoundError) {
      return NextResponse.json({
        success: false,
        error: 'Not Found',
        message: error.message,
        timestamp: new Date().toISOString()
      }, { status: 404 });
    }

    if (error instanceof CatalogueError) {
      return NextResponse.json({
        success: false,
        error: error.code,
        message: error.message,
        timestamp: new Date().toISOString()
      }, { status: error.statusCode });
    }

    return NextResponse.json({
      success: false,
      error: 'Internal Server Error',
      message: 'Failed to delete catalogue item',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}

// OPTIONS handler for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
} 