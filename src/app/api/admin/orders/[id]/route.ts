import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const body = await request.json();
    
    const updateData: any = {};
    
    // Handle status updates
    if (body.status) {
      updateData.status = body.status;
      
      // Update timestamps based on status
      if (body.status === 'confirmed') {
        updateData.confirmedAt = new Date();
      } else if (body.status === 'delivered') {
        updateData.completedAt = new Date();
      }
    }
    
    // Handle payment status updates
    if (body.paymentStatus) {
      updateData.paymentStatus = body.paymentStatus;
    }
    
    // Handle pricing updates (especially for design-only orders)
    if (body.designFee !== undefined) {
      updateData.designFee = parseFloat(body.designFee);
      
      // For design-only orders, update total amount to match design fee
      const currentOrder = await prisma.order.findUnique({
        where: { id }
      });
      
      if (currentOrder?.designOnly) {
        updateData.totalAmount = parseFloat(body.designFee);
      } else if (currentOrder) {
        // For regular orders with design, add design fee to subtotal
        updateData.totalAmount = Number(currentOrder.subtotal) + parseFloat(body.designFee);
      }
    }
    
    if (body.totalAmount !== undefined) {
      updateData.totalAmount = parseFloat(body.totalAmount);
    }
    
    if (body.unitPrice !== undefined) {
      updateData.unitPrice = parseFloat(body.unitPrice);
      
      // Recalculate subtotal if unit price changes
      const currentOrder = await prisma.order.findUnique({
        where: { id }
      });
      
      if (currentOrder) {
        const newSubtotal = parseFloat(body.unitPrice) * currentOrder.quantity;
        updateData.subtotal = newSubtotal;
        updateData.totalAmount = newSubtotal + (Number(currentOrder.designFee) || 0);
      }
    }

    const updatedOrder = await prisma.order.update({
      where: { id },
      data: updateData,
      include: {
        product: {
          select: {
            service: true,
            category: true
          }
        },
        designRequests: {
          select: {
            id: true,
            status: true,
            designType: true,
            urgency: true,
            specifications: true,
            designFee: true
          }
        }
      }
    });

    return NextResponse.json(updatedOrder);
  } catch (error) {
    console.error('Order update error:', error);
    return NextResponse.json(
      { error: 'Failed to update order' },
      { status: 500 }
    );
  }
} 