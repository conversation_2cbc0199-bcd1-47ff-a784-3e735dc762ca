import { auth } from "../../../../../auth";;
import { requireAdminAuth } from '@/lib/auth-helpers';;
import { NextRequest, NextResponse } from 'next/server';
import * as quoteService from '@/services/quoteService';


/**
 * GET /api/admin/quotes
 * Get all quotes
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(req);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    console.log(`[Quotes API] GET request from user: ${user.email}`);

    // Get all quotes
    const quotes = await quoteService.getAllQuotes();

    return NextResponse.json({
      success: true,
      data: quotes,
      count: quotes.length
    });
  } catch (error) {
    console.error('Error in quotes GET:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/admin/quotes
 * Create a new quote
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(req);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    console.log(`[Quotes API] POST request from user: ${user.email}`);

    // Get request body
    const body = await req.json();
    console.log('Quote request body:', JSON.stringify(body, null, 2));

    // Validate required fields
    if (!body.customerName || !body.phoneNumber || !body.items || !Array.isArray(body.items) || body.items.length === 0) {
      console.log('Quote validation failed: Missing required fields');
      return NextResponse.json({
        success: false,
        error: 'Customer name, phone number, and items are required'
      }, { status: 400 });
    }

    // Validate items
    for (const item of body.items) {
      const itemType = item.itemType || 'service';
      
      if (itemType === 'service') {
        if (!item.serviceId || !item.quantity) {
          console.log('Quote validation failed: Service items must have serviceId and quantity');
          return NextResponse.json({
            success: false,
            error: 'Service items must have a service ID and quantity'
          }, { status: 400 });
        }
      } else if (itemType === 'custom') {
        if (!item.itemName || !item.quantity || item.unitPrice === undefined) {
          console.log('Quote validation failed: Custom items must have itemName, quantity, and unitPrice');
          return NextResponse.json({
            success: false,
            error: 'Custom items must have an item name, quantity, and unit price'
          }, { status: 400 });
        }
      }
    }

    // Validate discount if provided
    if (body.discountType) {
      if (!['percentage', 'fixed'].includes(body.discountType)) {
        return NextResponse.json({
          success: false,
          error: 'Discount type must be either "percentage" or "fixed"'
        }, { status: 400 });
      }
      
      if (body.discountValue === undefined || body.discountValue < 0) {
        return NextResponse.json({
          success: false,
          error: 'Discount value must be a positive number'
        }, { status: 400 });
      }
      
      if (body.discountType === 'percentage' && body.discountValue > 100) {
        return NextResponse.json({
          success: false,
          error: 'Percentage discount cannot exceed 100%'
        }, { status: 400 });
      }
    }

    try {
      // Create quote
      console.log('Creating quote for customer:', body.customerName);
      const quote = await quoteService.createQuote({
        customerName: body.customerName,
        phoneNumber: body.phoneNumber,
        email: body.email,
        notes: body.notes,
        validUntil: body.validUntil ? new Date(body.validUntil) : undefined,
        discountType: body.discountType,
        discountValue: body.discountValue,
        items: body.items
      });

      if (!quote) {
        console.log('Quote creation failed: No quote returned from service');
        return NextResponse.json({
          success: false,
          error: 'Failed to create quote'
        }, { status: 500 });
      }

      console.log('Quote created successfully:', quote.id);
      return NextResponse.json({
        success: true,
        data: quote,
        message: 'Quote created successfully'
      });
    } catch (serviceError) {
      console.error('Error in quote service:', serviceError);
      return NextResponse.json({
        success: false,
        error: 'Failed to create quote',
        message: serviceError instanceof Error ? serviceError.message : 'Unknown error'
      }, { status: 500 });
    }
  } catch (error) {
    console.error('Error in quotes POST:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * PATCH /api/admin/quotes
 * Update a quote's status
 */
export async function PATCH(req: NextRequest) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(req);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    console.log(`[Quotes API] PATCH request from user: ${user.email}`);

    // Get request body
    const body = await req.json();

    // Validate required fields
    if (!body.id || !body.status) {
      return NextResponse.json({
        success: false,
        error: 'Quote ID and status are required'
      }, { status: 400 });
    }

    // Update quote status
    const quote = await quoteService.updateQuoteStatus(body.id, body.status);

    if (!quote) {
      return NextResponse.json({
        success: false,
        error: 'Quote not found'
      }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      data: quote,
      message: 'Quote status updated successfully'
    });
  } catch (error) {
    console.error('Error in quotes PATCH:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
