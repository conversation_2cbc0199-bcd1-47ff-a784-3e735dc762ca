import { auth } from "../../../../../../auth";
import { requireAdminAuth } from '@/lib/auth-helpers';
import { NextRequest, NextResponse } from 'next/server';
import * as quoteService from '@/services/quoteService';

import { prisma } from '@/lib/prisma';

/**
 * GET /api/admin/quotes/[id]
 * Get a quote by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id: quoteId } = await params;

    console.log(`[Quotes API] GET request from user: ${user.username} for quote: ${quoteId}`);
    console.log(`[Quotes API] Quote ID type: ${typeof quoteId}, value: "${quoteId}"`);
    console.log(`[Quotes API] Request URL: ${request.url}`);
    console.log(`[Quotes API] Request pathname: ${request.nextUrl.pathname}`);

    // Validate quote ID
    if (!quoteId || quoteId === 'undefined' || quoteId === 'null') {
      console.error(`[Quotes API] Invalid quote ID: ${quoteId}`);
      return NextResponse.json({ error: 'Invalid quote ID' }, { status: 400 });
    }

    // Get quote by ID
    console.log(`[Quotes API] Attempting to fetch quote with ID: ${quoteId}`);
    const quote = await quoteService.getQuoteById(quoteId);

    if (!quote) {
      console.log(`[Quotes API] Quote not found in database: ${quoteId}`);
      // Let's also try to get all quotes to see what's available
      const allQuotes = await quoteService.getAllQuotes();
      console.log(`[Quotes API] Available quotes: ${allQuotes.map(q => q.id).join(', ')}`);
      return NextResponse.json({ error: 'Quote not found' }, { status: 404 });
    }

    console.log(`[Quotes API] Quote found: ${quote.quoteNumber} (ID: ${quote.id})`);
    return NextResponse.json(quote);
  } catch (error) {
    console.error('Error getting quote:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to get quote',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * DELETE /api/admin/quotes/[id]
 * Delete a quote by ID
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) {
      console.log('[Quotes API] DELETE - Auth check failed:', response.status);
      return response;
    }
    
    if (!user) {
      console.log('[Quotes API] DELETE - No user found');
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id: quoteId } = await params;
    console.log(`[Quotes API] DELETE request from user: ${user.username} for quote: ${quoteId}`);

    if (!quoteId || typeof quoteId !== 'string') {
      console.log('[Quotes API] DELETE - Invalid quote ID:', quoteId);
      return NextResponse.json({
        success: false,
        error: 'Invalid quote ID'
      }, { status: 400 });
    }

    try {
      // Delete quote by ID
      const success = await quoteService.deleteQuote(quoteId);

      if (!success) {
        console.log('[Quotes API] DELETE - Quote not found:', quoteId);
        return NextResponse.json({
          success: false,
          error: 'Quote not found'
        }, { status: 404 });
      }

      console.log('[Quotes API] DELETE - Quote deleted successfully:', quoteId);
      return NextResponse.json({
        success: true,
        message: 'Quote deleted successfully'
      });
    } catch (serviceError) {
      console.error('[Quotes API] DELETE - Service error:', serviceError);
      // Check if this is a known error (like invoice exists)
      if (serviceError instanceof Error && serviceError.message.includes('invoice')) {
        return NextResponse.json({
          success: false,
          error: serviceError.message
        }, { status: 400 });
      }
      throw serviceError; // Re-throw unknown errors
    }
  } catch (error) {
    console.error('[Quotes API] DELETE - Unhandled error:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to delete quote',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/**
 * PUT /api/admin/quotes/[id]
 * Update a quote
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { id: quoteId } = await params;
    console.log(`[Quote API] PUT request for quote ${quoteId} from user: ${user.username}`);

    if (!quoteId) {
      return NextResponse.json({
        success: false,
        error: 'Quote ID is required'
      }, { status: 400 });
    }

    const body = await request.json();

    const quote = await prisma.quote.update({
      where: { id: quoteId },
      data: {
        customerName: body.customerName,
        phoneNumber: body.phoneNumber,
        email: body.email,
        status: body.status,
        notes: body.notes,
        totalAmount: body.totalAmount,
        discountAmount: body.discountAmount || 0,
        discountType: body.discountType || null,
        discountValue: body.discountValue || 0,
        subtotalAmount: body.subtotalAmount,
        validUntil: body.validUntil ? new Date(body.validUntil) : undefined,
        issuedAt: body.issuedAt ? new Date(body.issuedAt) : undefined
      },
      include: {
        items: {
          include: {
            service: true
          }
        }
      }
    });

    return NextResponse.json({
      success: true,
      data: quote,
      message: 'Quote updated successfully'
    });
  } catch (error) {
    console.error('Error updating quote:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to update quote',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
