import { auth } from "../../../../../auth";;
import { requireAdminAuth } from '@/lib/auth-helpers';;
import { NextRequest, NextResponse } from 'next/server';

import { S3Client, ListObjectsV2Command } from '@aws-sdk/client-s3';
import { getDefaultStorageConfig } from '@/lib/storageConfig';

interface S3Image {
  key: string;
  filename: string;
  category: string;
  url: string;
  size: number;
  lastModified: string;
  displayName: string;
}

// Get S3 client with the current configuration
async function getS3Client() {
  const config = await getDefaultStorageConfig();
  if (!config) {
    throw new Error('No storage configuration found');
  }
  return new S3Client({
    region: config.region,
    endpoint: config.endpoint,
    credentials: {
      accessKeyId: config.accessKey,
      secretAccessKey: config.secretKey,
    },
    forcePathStyle: true,
  });
}

async function getBucketName() {
  const config = await getDefaultStorageConfig();
  if (!config) {
    throw new Error('No storage configuration found');
  }
  return config.bucketName;
}

// Map catalogue categories to folder names
const CATALOGUE_CATEGORY_MAP: Record<string, string> = {
  'banners': 'banners',
  'drinkware': 'drinkware', 
  'office-essentials': 'office',
  'diaries-notebooks': 'diaries',
  'gift-sets': 'gifts',
  'printing-services': 'printing',
  'digital-services': 'digital',
  'awards': 'awards',
  'general': 'general',
  'all': 'catalogue' // For fetching all images
};

// GET handler - Fetch S3 catalogue images
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    console.log(`[S3 Catalogue Images API] GET request from user: ${user.email}`);

    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category') || 'all';
    const prefix = searchParams.get('prefix') || 'images/catalogue';
    
    console.log(`Fetching catalogue images for category: ${category}, prefix: ${prefix}`);
    
    const s3Client = await getS3Client();
    const bucketName = await getBucketName();
    const config = await getDefaultStorageConfig();
    
    // Construct the S3 prefix based on category
    let s3Prefix = prefix;
    if (category && category !== 'all' && CATALOGUE_CATEGORY_MAP[category]) {
      s3Prefix = `${prefix}/${CATALOGUE_CATEGORY_MAP[category]}/`;
    } else if (category === 'all') {
      s3Prefix = `${prefix}/`;
    }
    
    console.log(`Using S3 prefix: ${s3Prefix}`);
    
    const command = new ListObjectsV2Command({
      Bucket: bucketName,
      Prefix: s3Prefix,
      MaxKeys: 1000,
    });
    
    const s3Response = await s3Client.send(command);
    
    if (!s3Response.Contents || s3Response.Contents.length === 0) {
      console.log('No images found in S3 for catalogue');
      return NextResponse.json({
        success: true,
        images: []
      });
    }
    
    const images: S3Image[] = s3Response.Contents
      .filter(item => {
        const key = item.Key || '';
        // Filter out folders and non-image files
        const isFile = !key.endsWith('/');
        const isImage = /\.(jpg|jpeg|png|gif|webp|bmp)$/i.test(key);
        return isFile && isImage && item.Size && item.Size > 0;
      })
      .map(item => {
        const key = item.Key!;
        const filename = key.split('/').pop() || '';
        
        // Extract category from the path
        const pathParts = key.split('/');
        let itemCategory = 'general';
        if (pathParts.length >= 3) {
          // Structure: images/catalogue/category/filename
          const categoryFromPath = pathParts[2];
          // Map back to display category
          const categoryEntry = Object.entries(CATALOGUE_CATEGORY_MAP)
            .find(([_, folder]) => folder === categoryFromPath);
          itemCategory = categoryEntry ? categoryEntry[0] : categoryFromPath;
        }
        
        // Create display name from filename
        const displayName = filename
          .replace(/^product_\d+_[a-f0-9]+_/, '') // Remove scraped product prefix
          .replace(/\.(jpg|jpeg|png|gif|webp|bmp)$/i, '')
          .replace(/[-_]/g, ' ')
          .replace(/\b\w/g, char => char.toUpperCase())
          .trim();
        
        const imageUrl = `${config?.endpoint}/${bucketName}/${key}`;
        
        return {
          key,
          filename,
          category: itemCategory,
          url: imageUrl,
          size: item.Size || 0,
          lastModified: item.LastModified?.toISOString() || '',
          displayName: displayName || filename
        };
      })
      .sort((a, b) => {
        // Sort by last modified (newest first), then by display name
        const dateA = new Date(a.lastModified).getTime();
        const dateB = new Date(b.lastModified).getTime();
        if (dateB !== dateA) {
          return dateB - dateA;
        }
        return a.displayName.localeCompare(b.displayName);
      });
    
    console.log(`[S3 Catalogue Images API] Returning ${images.length} catalogue images to user: ${user.email}`);
    
    return NextResponse.json({
      success: true,
      images,
      count: images.length
    });
    
  } catch (error) {
    console.error('Error fetching catalogue images from S3:', error);
    return NextResponse.json(
      { error: 'Failed to fetch images from S3' },
      { status: 500 }
    );
  }
} 
