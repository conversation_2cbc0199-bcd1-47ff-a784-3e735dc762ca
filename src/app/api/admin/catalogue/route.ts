import { auth } from "../../../../../auth";;
import { requireAdminAuth } from '@/lib/auth-helpers';;
import { NextRequest, NextResponse } from 'next/server';

import { prisma } from '@/lib/prisma';

// GET handler for fetching catalogue items
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json({
        error: 'Unauthorized',
        message: 'Authentication required'
      }, { status: 401 });
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const filters = {
      category: searchParams.get('category') || undefined,
      search: searchParams.get('search') || undefined,
      minPrice: searchParams.get('minPrice') ? Number(searchParams.get('minPrice')) : undefined,
      maxPrice: searchParams.get('maxPrice') ? Number(searchParams.get('maxPrice')) : undefined,
      limit: Math.min(Number(searchParams.get('limit')) || 20, 100),
      offset: Math.max(Number(searchParams.get('offset')) || 0, 0),
      sortBy: (searchParams.get('sortBy') as any) || 'service',
      sortOrder: (searchParams.get('sortOrder') as any) || 'asc'
    };

    console.log('API: Fetching catalogue items with filters:', filters);

    // Build where clause
    const where: any = {};
    if (filters.category) where.category = filters.category;
    if (filters.search) {
      where.OR = [
        { service: { contains: filters.search, mode: 'insensitive' } },
        { description: { contains: filters.search, mode: 'insensitive' } }
      ];
    }
    if (filters.minPrice || filters.maxPrice) {
      where.price = {};
      if (filters.minPrice) where.price.gte = filters.minPrice;
      if (filters.maxPrice) where.price.lte = filters.maxPrice;
    }

    // Get catalogue items
    const [items, total] = await Promise.all([
      prisma.catalogue.findMany({
        where,
        skip: filters.offset,
        take: filters.limit,
        orderBy: { [filters.sortBy]: filters.sortOrder }
      }),
      prisma.catalogue.count({ where })
    ]);

    console.log(`API: Retrieved ${items.length} of ${total} catalogue items`);

    return NextResponse.json(items, {
      status: 200,
      headers: {
        'Cache-Control': 'no-store, max-age=0, must-revalidate',
        'Pragma': 'no-cache',
        'X-Total-Count': total.toString(),
        'X-Has-More': (filters.offset + items.length < total).toString()
      }
    });

  } catch (error) {
    console.error('API: Error in GET /admin/catalogue:', error);
    return NextResponse.json({
      error: 'Internal Server Error',
      message: 'Failed to fetch catalogue items'
    }, { status: 500 });
  }
}

// POST handler for creating catalogue items
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json({
        error: 'Unauthorized',
        message: 'Authentication required'
      }, { status: 401 });
    }

    // Parse request body
    let body;
    try {
      body = await request.json();
    } catch (error) {
      return NextResponse.json({
        error: 'Invalid JSON',
        message: 'Request body must be valid JSON'
      }, { status: 400 });
    }

    console.log('API: Creating catalogue item:', body);

    // Create catalogue item
    const newCatalogue = await prisma.catalogue.create({
      data: {
        service: body.service,
        description: body.description || '',
        price: body.price,
        designFee: body.designFee || 0,
        category: body.category || 'Other',

        popular: body.popular || false,
        features: body.features || [],
        imageUrl: body.imageUrl || null,
        imageUrl2: body.imageUrl2 || null,
        imageUrl3: body.imageUrl3 || null
      }
    });

    console.log(`API: Created catalogue item: ${newCatalogue.service}`);

    return NextResponse.json(newCatalogue, {
      status: 201,
      headers: {
        'Cache-Control': 'no-store',
        'Location': `/api/admin/catalogue/${newCatalogue.id}`
      }
    });

  } catch (error) {
    console.error('API: Error in POST /admin/catalogue:', error);
    return NextResponse.json({
      error: 'Internal Server Error',
      message: 'Failed to create catalogue item'
    }, { status: 500 });
  }
}

// DELETE handler for bulk operations
export async function DELETE(request: NextRequest) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json({
        error: 'Unauthorized',
        message: 'Authentication required'
      }, { status: 401 });
    }

    // Parse request body
    let body;
    try {
      body = await request.json();
    } catch (error) {
      return NextResponse.json({
        error: 'Invalid JSON',
        message: 'Request body must be valid JSON'
      }, { status: 400 });
    }

    const { ids } = body;

    if (!Array.isArray(ids) || ids.length === 0) {
      return NextResponse.json({
        error: 'Invalid Request',
        message: 'IDs array is required and must not be empty'
      }, { status: 400 });
    }

    console.log(`API: Bulk deleting ${ids.length} catalogue items:`, ids);

    // Convert string IDs to integers
    const numericIds = ids.map(id => {
      const numericId = parseInt(id, 10);
      if (isNaN(numericId)) {
        throw new Error(`Invalid ID format: ${id}`);
      }
      return numericId;
    });

    console.log(`API: Converted IDs to integers:`, numericIds);

    // Check which items have orders (foreign key constraint)
    const itemsWithOrders = await prisma.order.findMany({
      where: {
        productId: { in: numericIds }
      },
      select: {
        productId: true,
        product: {
          select: {
            service: true
          }
        }
      },
      distinct: ['productId']
    });

    const idsWithOrders = itemsWithOrders.map(order => order.productId);
    const itemsToDelete = numericIds.filter(id => !idsWithOrders.includes(id));

    console.log('API: Items with orders that cannot be deleted:', idsWithOrders);
    console.log('API: Items that can be safely deleted:', itemsToDelete);

    let deletedCount = 0;
    let skippedItems: { id: number; name: string; reason: string; }[] = [];

    // Delete items that don't have orders
    if (itemsToDelete.length > 0) {
      const deleteResult = await prisma.catalogue.deleteMany({
        where: {
          id: { in: itemsToDelete }
        }
      });
      deletedCount = deleteResult.count;
    }

    // Record skipped items
    if (idsWithOrders.length > 0) {
      skippedItems = itemsWithOrders.map(item => ({
        id: item.productId,
        name: item.product?.service || 'Unknown',
        reason: 'Has existing orders'
      }));
    }

    console.log(`API: Bulk operation completed - deleted ${deletedCount} items, skipped ${idsWithOrders.length} items`);

    return NextResponse.json({
      success: true,
      deletedCount,
      skippedCount: idsWithOrders.length,
      skippedItems,
      failedIds: idsWithOrders,
      message: deletedCount > 0 
        ? `Successfully deleted ${deletedCount} catalogue items${idsWithOrders.length > 0 ? `. Skipped ${idsWithOrders.length} items with existing orders` : ''}`
        : 'No items could be deleted. All selected items have existing orders.'
    }, {
      status: 200,
      headers: {
        'Cache-Control': 'no-store'
      }
    });

  } catch (error) {
    console.error('API: Error in DELETE /admin/catalogue:', error);
    
    // Handle specific error types
    if (error instanceof Error && error.message.includes('Invalid ID format')) {
      return NextResponse.json({
        error: 'Invalid Request',
        message: error.message
      }, { status: 400 });
    }

    return NextResponse.json({
      error: 'Internal Server Error',
      message: 'Failed to delete catalogue items'
    }, { status: 500 });
  }
}

// OPTIONS handler for CORS preflight
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Requested-With',
      'Access-Control-Max-Age': '86400',
    },
  });
} 