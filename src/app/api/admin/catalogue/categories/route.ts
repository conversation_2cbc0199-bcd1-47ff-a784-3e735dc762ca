import { auth } from "../../../../../../auth";
import { requireAdminAuth } from '@/lib/auth-helpers';
import { NextRequest, NextResponse } from 'next/server';

import { CatalogueCRUDService } from '@/services/catalogueCRUDService';
import { prisma } from '@/lib/prisma';

// GET handler - Get all catalogue categories
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    console.log(`[Catalogue Categories API] GET request from user: ${user.email}`);
    
    // Get categories from both the Category table and existing catalogue items
    const [dbCategories, catalogueCategories] = await Promise.all([
      prisma.category.findMany({
        orderBy: { name: 'asc' }
      }),
      CatalogueCRUDService.getCategories()
    ]);

    // Combine categories from both sources
    const dbCategoryNames = dbCategories.map(cat => cat.name);
    const allCategories = Array.from(new Set([...dbCategoryNames, ...catalogueCategories]));
    
    // Ensure default categories are available
    const defaultCategories = ['Web Development', 'Design', 'Marketing', 'Consulting', 'Other'];
    const finalCategories = Array.from(new Set([...allCategories, ...defaultCategories]));
    
    console.log(`[Catalogue Categories API] Retrieved ${finalCategories.length} categories`);
    
    return NextResponse.json({
      success: true,
      categories: finalCategories,
      count: finalCategories.length
    }, {
      headers: {
        'Cache-Control': 'no-store, max-age=0',
        'Pragma': 'no-cache',
      },
    });
  } catch (error) {
    console.error('Error fetching catalogue categories:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to fetch catalogue categories',
        categories: []
      },
      { status: 500 }
    );
  }
}

// POST handler - Create a new category
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { name } = body;

    if (!name || typeof name !== 'string' || !name.trim()) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Category name is required and must be a non-empty string' 
        },
        { status: 400 }
      );
    }

    const categoryName = name.trim();

    console.log(`[Catalogue Categories API] POST request from user: ${user.email} for category: ${categoryName}`);

    // Check if category already exists
    const existingCategory = await prisma.category.findFirst({
      where: {
        name: {
          equals: categoryName,
          mode: 'insensitive'
        }
      }
    });

    if (existingCategory) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Category already exists' 
        },
        { status: 409 }
      );
    }

    // Create slug from name
    const slug = categoryName
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '');

    // Create the category
    const newCategory = await prisma.category.create({
      data: {
        name: categoryName,
        slug: slug,
        description: `Category for ${categoryName} services`
      }
    });

    console.log(`[Catalogue Categories API] Created category: ${categoryName} with ID: ${newCategory.id}`);
    
    return NextResponse.json({
      success: true,
      category: newCategory.name,
      id: newCategory.id,
      message: 'Category created successfully'
    }, {
      status: 201,
      headers: {
        'Cache-Control': 'no-store',
      },
    });
  } catch (error) {
    console.error('Error creating catalogue category:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to create category'
      },
      { status: 500 }
    );
  }
} 