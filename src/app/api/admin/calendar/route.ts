import { auth } from "../../../../../auth";;
import { requireAdminAuth } from '@/lib/auth-helpers';;
import { NextRequest, NextResponse } from 'next/server';


import { prisma } from '@/lib/prisma';

interface CalendarEvent {
  id: string;
  title: string;
  description?: string;
  type: string;
  start: string;
  end?: string;
  status: string;
  priority: string;
  project?: {
    id: string;
    name: string;
  };
  client?: {
    id: string;
    name: string;
    company?: string;
  };
  allDay?: boolean;
}

export async function GET(request: NextRequest) {
  try {
    const { user, response } = await requireAdminAuth(request); if (response) return response;
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const view = searchParams.get('view') || 'month';
    const date = searchParams.get('date') || new Date().toISOString();
    const type = searchParams.get('type');
    const status = searchParams.get('status');

    const events: CalendarEvent[] = [];

    // For now, using sample data until database models are fully set up
    console.log('Calendar API - fetching events for view:', view, 'date:', date);

    // Add sample events for different types
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    const nextWeek = new Date(today);
    nextWeek.setDate(nextWeek.getDate() + 7);

    const sampleEvents: CalendarEvent[] = [
      {
        id: 'meeting-1',
        title: 'Team Stand-up',
        description: 'Daily team synchronization meeting',
        type: 'meeting',
        start: new Date(today.setHours(9, 0, 0, 0)).toISOString(),
        end: new Date(today.setHours(9, 30, 0, 0)).toISOString(),
        status: 'confirmed',
        priority: 'medium',
      },
      {
        id: 'meeting-2',
        title: 'Client Review Meeting',
        description: 'Review project progress with client',
        type: 'meeting',
        start: new Date(tomorrow.setHours(14, 0, 0, 0)).toISOString(),
        end: new Date(tomorrow.setHours(15, 30, 0, 0)).toISOString(),
        status: 'pending',
        priority: 'high',
      },
      {
        id: 'call-1',
        title: 'Weekly Progress Call',
        description: 'Check in with remote team members',
        type: 'call',
        start: new Date(today.setHours(16, 0, 0, 0)).toISOString(),
        end: new Date(today.setHours(16, 45, 0, 0)).toISOString(),
        status: 'confirmed',
        priority: 'medium',
      },
      {
        id: 'task-1',
        title: 'Code Review Deadline',
        description: 'Complete code review for new features',
        type: 'task',
        start: new Date(tomorrow.setHours(17, 0, 0, 0)).toISOString(),
        status: 'pending',
        priority: 'high',
        allDay: true,
      },
      {
        id: 'reminder-1',
        title: 'Follow up with Client',
        description: 'Send project update email',
        type: 'reminder',
        start: new Date(nextWeek.setHours(10, 0, 0, 0)).toISOString(),
        status: 'pending',
        priority: 'medium',
        allDay: true,
      },
    ];

    events.push(...sampleEvents);

    // Filter events by type and status if specified
    let filteredEvents = events;
    if (type) {
      filteredEvents = filteredEvents.filter(event => event.type === type);
    }
    if (status) {
      filteredEvents = filteredEvents.filter(event => event.status === status);
    }

    // Sort events by start date
    filteredEvents.sort((a, b) => new Date(a.start).getTime() - new Date(b.start).getTime());

    return NextResponse.json({
      events: filteredEvents,
      count: filteredEvents.length,
    });

  } catch (error) {
    console.error('Calendar API Error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch calendar events' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { user, response } = await requireAdminAuth(request); if (response) return response;
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { title, description, type, start, end, priority } = body;

    const newEvent: CalendarEvent = {
      id: `new-${Date.now()}`,
      title,
      description,
      type,
      start,
      end,
      status: 'pending',
      priority: priority || 'medium',
      allDay: !end || start === end,
    };

    return NextResponse.json({ event: newEvent }, { status: 201 });

  } catch (error) {
    console.error('Create event error:', error);
    return NextResponse.json(
      { error: 'Failed to create event' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const { user, response } = await requireAdminAuth(request); if (response) return response;
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { id, title, description, type, start, end, status, priority } = body;

    const updatedEvent: CalendarEvent = {
      id,
      title,
      description,
      type,
      start,
      end,
      status,
      priority,
      allDay: !end || start === end,
    };

    return NextResponse.json({ event: updatedEvent });

  } catch (error) {
    console.error('Update event error:', error);
    return NextResponse.json(
      { error: 'Failed to update event' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { user, response } = await requireAdminAuth(request); if (response) return response;
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json({ error: 'Event ID is required' }, { status: 400 });
    }

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('Delete event error:', error);
    return NextResponse.json(
      { error: 'Failed to delete event' },
      { status: 500 }
    );
  }
} 