import { auth } from "../../../../../../auth";
import { requireAdminAuth } from '@/lib/auth-helpers';
import { NextRequest, NextResponse } from 'next/server';


interface CalendarEvent {
  id: string;
  title: string;
  description?: string;
  startTime: string;
  endTime: string;
  type: 'meeting' | 'call' | 'deadline' | 'reminder' | 'appointment';
  status: 'scheduled' | 'confirmed' | 'cancelled' | 'completed';
  location?: string;
  isVirtual: boolean;
  meetingLink?: string;
  meetingPlatform?: 'zoom' | 'google-meet' | 'teams' | 'webex' | 'custom';
  meetingDetails?: {
    meetingId?: string;
    passcode?: string;
    dialInNumbers?: string[];
    recordingEnabled?: boolean;
    waitingRoom?: boolean;
    requireAuth?: boolean;
  };
  attendees: Array<{
    id: string;
    name: string;
    email: string;
    status: 'pending' | 'accepted' | 'declined' | 'tentative';
  }>;
  project?: {
    id: string;
    name: string;
    client?: {
      firstName: string;
      lastName: string;
      company?: string;
    };
  };
  client?: {
    id: string;
    firstName: string;
    lastName: string;
    company?: string;
    email?: string;
  };
  createdBy: {
    id: string;
    name: string;
    email: string;
  };
  createdAt: string;
  updatedAt: string;
}

// GET handler - Fetch calendar events
export async function GET(request: NextRequest) {
  try {
    const authResult = await requireAdminAuth(request);
    if (authResult.error || !authResult.user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    console.log(`[Calendar Events API] GET request from user: ${authResult.user.username}`);

    const { searchParams } = new URL(request.url);
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const type = searchParams.get('type');
    const status = searchParams.get('status');
    const search = searchParams.get('search');

    // Sample data - replace with database queries later
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    const nextWeek = new Date(today);
    nextWeek.setDate(nextWeek.getDate() + 7);

    const sampleEvents: CalendarEvent[] = [
      {
        id: 'meeting-1',
        title: 'Team Stand-up',
        description: 'Daily team synchronization meeting',
        type: 'meeting',
        startTime: new Date(today.setHours(9, 0, 0, 0)).toISOString(),
        endTime: new Date(today.setHours(9, 30, 0, 0)).toISOString(),
        status: 'confirmed',
        location: 'Conference Room A',
        isVirtual: true,
        meetingLink: 'https://zoom.us/j/123456789',
        meetingPlatform: 'zoom',
        meetingDetails: {
          meetingId: '123 456 789',
          passcode: 'standup24',
          dialInNumbers: ['+1 669 900 6833', '+1 253 215 8782'],
          recordingEnabled: true,
          waitingRoom: true,
          requireAuth: false
        },
        attendees: [
          {
            id: 'user-1',
            name: 'John Smith',
            email: '<EMAIL>',
            status: 'accepted'
          },
          {
            id: 'user-2',
            name: 'Jane Doe',
            email: '<EMAIL>',
            status: 'accepted'
          }
        ],
        client: {
          id: 'client-1',
          firstName: 'Alice',
          lastName: 'Johnson',
          company: 'Tech Corp',
          email: '<EMAIL>'
        },
        project: {
          id: 'project-1',
          name: 'Website Redesign',
          client: {
            firstName: 'Alice',
            lastName: 'Johnson',
            company: 'Tech Corp'
          }
        },
        createdBy: {
          id: authResult.user.id,
          name: authResult.user.name || 'Admin User',
          email: authResult.user.email
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: 'call-1',
        title: 'Weekly Progress Call',
        description: 'Check progress on current projects',
        type: 'call',
        startTime: new Date(today.setHours(16, 0, 0, 0)).toISOString(),
        endTime: new Date(today.setHours(16, 45, 0, 0)).toISOString(),
        status: 'scheduled',
        isVirtual: true,
        meetingLink: 'https://meet.google.com/abc-defg-hij',
        meetingPlatform: 'google-meet',
        meetingDetails: {
          meetingId: 'abc-defg-hij',
          recordingEnabled: false,
          waitingRoom: false,
          requireAuth: true
        },
        attendees: [
          {
            id: 'user-3',
            name: 'Bob Wilson',
            email: '<EMAIL>',
            status: 'pending'
          }
        ],
        client: {
          id: 'client-2',
          firstName: 'Bob',
          lastName: 'Wilson',
          company: 'Design Studio',
          email: '<EMAIL>'
        },
        createdBy: {
          id: authResult.user.id,
          name: authResult.user.name || 'Admin User',
          email: authResult.user.email
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      {
        id: 'deadline-1',
        title: 'Project Milestone Deadline',
        description: 'Complete phase 1 of the project',
        type: 'deadline',
        startTime: new Date(tomorrow.setHours(17, 0, 0, 0)).toISOString(),
        endTime: new Date(tomorrow.setHours(17, 0, 0, 0)).toISOString(),
        status: 'scheduled',
        isVirtual: false,
        attendees: [],
        project: {
          id: 'project-2',
          name: 'E-commerce Platform',
          client: {
            firstName: 'Carol',
            lastName: 'Davis',
            company: 'Retail Plus'
          }
        },
        client: {
          id: 'client-3',
          firstName: 'Carol',
          lastName: 'Davis',
          company: 'Retail Plus',
          email: '<EMAIL>'
        },
        createdBy: {
          id: authResult.user.id,
          name: authResult.user.name || 'Admin User',
          email: authResult.user.email
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
    ];

    // Filter events based on date range
    let filteredEvents = sampleEvents;
    
    if (startDate && endDate) {
      const start = new Date(startDate);
      const end = new Date(endDate);
      filteredEvents = filteredEvents.filter(event => {
        const eventDate = new Date(event.startTime);
        return eventDate >= start && eventDate <= end;
      });
    }

    // Filter by type
    if (type) {
      filteredEvents = filteredEvents.filter(event => event.type === type);
    }

    // Filter by status
    if (status) {
      filteredEvents = filteredEvents.filter(event => event.status === status);
    }

    // Filter by search term
    if (search) {
      const searchLower = search.toLowerCase();
      filteredEvents = filteredEvents.filter(event => 
        event.title.toLowerCase().includes(searchLower) ||
        event.description?.toLowerCase().includes(searchLower) ||
        event.client?.firstName.toLowerCase().includes(searchLower) ||
        event.client?.lastName.toLowerCase().includes(searchLower) ||
        event.client?.company?.toLowerCase().includes(searchLower)
      );
    }

    console.log(`[Calendar Events API] Retrieved ${filteredEvents.length} events by user: ${authResult.user.username}`);

    return NextResponse.json({
      events: filteredEvents,
      total: filteredEvents.length
    });
  } catch (error) {
    console.error('Error in GET calendar events:', error);
    return NextResponse.json(
      { error: 'Failed to fetch calendar events' },
      { status: 500 }
    );
  }
}

// POST handler - Create a new calendar event
export async function POST(request: NextRequest) {
  try {
    const authResult = await requireAdminAuth(request);
    if (authResult.error || !authResult.user) {
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }

    console.log(`[Calendar Events API] POST request from user: ${authResult.user.username}`);
    
    const data = await request.json();
    
    // Basic validation
    if (!data.title || !data.startTime || !data.endTime || !data.type) {
      return NextResponse.json(
        { error: 'Missing required fields: title, startTime, endTime, type' },
        { status: 400 }
      );
    }

    // Create new event (in real implementation, save to database)
    const newEvent: CalendarEvent = {
      id: `event-${Date.now()}`,
      title: data.title,
      description: data.description,
      startTime: data.startTime,
      endTime: data.endTime,
      type: data.type,
      status: data.status || 'scheduled',
      location: data.location,
      isVirtual: data.isVirtual || false,
      meetingLink: data.meetingLink,
      meetingPlatform: data.meetingPlatform,
      meetingDetails: data.meetingDetails,
      attendees: data.attendees || [],
      project: data.project,
      client: data.client,
      createdBy: {
        id: authResult.user.id,
        name: authResult.user.name || 'Admin User',
        email: authResult.user.email
      },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    console.log(`[Calendar Events API] Event created successfully: ${newEvent.id} by user: ${authResult.user.username}`);
    return NextResponse.json(newEvent, { status: 201 });
  } catch (error) {
    console.error('Error in POST calendar events:', error);
    return NextResponse.json(
      { error: 'Failed to create calendar event' },
      { status: 500 }
    );
  }
} 
