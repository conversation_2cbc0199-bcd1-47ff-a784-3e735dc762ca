import { NextRequest, NextResponse } from 'next/server';
import { monitor } from '@/lib/monitoring/application-monitor';
import { db } from '@/lib/database/database-service';
import { withSecurityHeaders } from '@/lib/security/security-headers';

/**
 * GET /api/health
 * Application health check endpoint
 * Returns comprehensive health status for monitoring systems
 */
async function healthHandler(request: NextRequest): Promise<NextResponse> {
  const startTime = Date.now();
  const requestTracker = monitor.trackRequest(request, startTime);

  try {
    // Run all registered health checks
    const healthResult = await monitor.runHealthChecks();
    
    // Get additional system metrics
    const stats = monitor.getStats();
    
    // Check database connectivity
    let databaseHealth = 'healthy';
    let databaseResponseTime = 0;
    try {
      const dbStartTime = Date.now();
      // Simple database connectivity test
      await db.client.$queryRaw`SELECT 1`;
      databaseResponseTime = Date.now() - dbStartTime;
    } catch (error) {
      databaseHealth = 'unhealthy';
      console.error('[Health Check] Database health check failed:', error);
    }

    // Determine overall status
    const isDatabaseHealthy = databaseHealth === 'healthy';
    const isApplicationHealthy = healthResult.status === 'healthy';
    const isErrorRateAcceptable = stats.errorRate < 10; // 10% threshold for health check
    
    const overallStatus = isDatabaseHealthy && isApplicationHealthy && isErrorRateAcceptable
      ? 'healthy'
      : isDatabaseHealthy && isApplicationHealthy && !isErrorRateAcceptable
      ? 'degraded'
      : 'unhealthy';

    const responseTime = Date.now() - startTime;
    
    // Comprehensive health data
    const healthData = {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      uptime: healthResult.uptime,
      version: healthResult.version,
      responseTime,
      
      // Application health checks
      checks: {
        ...healthResult.checks,
        database: {
          status: databaseHealth,
          responseTime: databaseResponseTime,
          lastChecked: Date.now(),
        },
      },
      
      // Performance metrics
      metrics: {
        errorRate: stats.errorRate,
        avgResponseTime: stats.avgResponseTime,
        totalErrors: stats.totalErrors,
        totalSecurityEvents: stats.totalSecurityEvents,
        totalMetrics: stats.totalMetrics,
      },
      
      // System information
      system: {
        nodeVersion: process.version,
        platform: process.platform,
        architecture: process.arch,
        memoryUsage: process.memoryUsage(),
        cpuUsage: process.cpuUsage(),
      },
      
      // Environment information (non-sensitive)
      environment: {
        nodeEnv: process.env.NODE_ENV,
        deployment: process.env.VERCEL_ENV || 'unknown',
      },
    };

    // Log health check
    console.info(`[Health Check] Status: ${overallStatus}, Response time: ${responseTime}ms`);
    
    // Record performance metric
    monitor.recordMetric({
      name: 'health_check_duration',
      value: responseTime,
      timestamp: Date.now(),
      labels: { status: overallStatus },
      unit: 'ms',
    });

    // Determine HTTP status code
    const statusCode = overallStatus === 'healthy' ? 200 : 
                      overallStatus === 'degraded' ? 200 : 503;

    // Complete request tracking
    requestTracker.finish(statusCode);

    return NextResponse.json(healthData, { 
      status: statusCode,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0',
      },
    });

  } catch (error) {
    const responseTime = Date.now() - startTime;
    console.error('[Health Check] Health check failed:', error);
    
    // Record error
    monitor.recordError({
      error: error as Error,
      context: {
        operation: 'health_check',
        responseTime,
      },
      timestamp: Date.now(),
      severity: 'high',
    });

    // Complete request tracking with error
    requestTracker.finish(500, error as Error);

    return NextResponse.json(
      {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: 'Health check failed',
        responseTime,
      },
      { 
        status: 500,
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
        },
      }
    );
  }
}

// Export with security headers
export const GET = withSecurityHeaders(healthHandler);

/**
 * GET /api/health/simple
 * Simple health check for load balancers
 */
export async function HEAD(request: NextRequest): Promise<NextResponse> {
  try {
    // Quick database connectivity check
    await db.client.$queryRaw`SELECT 1`;
    
    return new NextResponse(null, { 
      status: 200,
      headers: {
        'Cache-Control': 'no-cache',
      },
    });
  } catch (error) {
    return new NextResponse(null, { 
      status: 503,
      headers: {
        'Cache-Control': 'no-cache',
      },
    });
  }
}
