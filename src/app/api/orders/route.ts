import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { S3Client, CopyObjectCommand, DeleteObjectCommand, ListObjectsV2Command } from '@aws-sdk/client-s3';
import { getDefaultStorageConfig } from '@/lib/storageConfig';
import { sendEmail, generateCatalogueOrderNotificationEmail } from '@/utils/email';

// Function to relocate artwork files from temporary path to final order path
async function relocateArtworkFiles(artworkFiles: string[], newOrderNumber: string): Promise<string[]> {
  try {
    if (!artworkFiles || artworkFiles.length === 0) {
      return [];
    }

    const config = await getDefaultStorageConfig();
    if (!config) {
      console.error('No S3 configuration found for artwork file relocation');
      return artworkFiles; // Return original filenames if can't relocate
    }

    const s3Client = new S3Client({
      region: config.region,
      endpoint: config.endpoint,
      credentials: {
        accessKeyId: config.accessKey,
        secretAccessKey: config.secretKey,
      },
      forcePathStyle: true
    });

    const relocatedFiles: string[] = [];

    for (const fileName of artworkFiles) {
      try {
        console.log(`🔍 Looking for artwork file: ${fileName}`);

        // List objects in the artwork directory to find files that contain our filename
        // Check both 'artwork/' and 'artwork/TEMP-*' prefixes
        let listCommand = new ListObjectsV2Command({
          Bucket: config.bucketName,
          Prefix: 'artwork/',
        });

        let listResponse = await s3Client.send(listCommand);
        
        // Find the file that contains our filename
        let matchingFile = listResponse.Contents?.find(obj => 
          obj.Key && (obj.Key.includes(fileName) || obj.Key.endsWith(fileName))
        );

        // If not found in main artwork folder, search all files for this filename
        if (!matchingFile) {
          console.log(`🔍 File not found in main artwork folder, searching all artwork files...`);
          
          // List ALL files in artwork directory (including subdirectories)
          listCommand = new ListObjectsV2Command({
            Bucket: config.bucketName,
            Prefix: 'artwork/',
            MaxKeys: 1000 // Search more comprehensively
          });

          listResponse = await s3Client.send(listCommand);
          
          // Try multiple matching strategies
          console.log(`🔍 Searching through ${listResponse.Contents?.length || 0} files for: ${fileName}`);
          
          matchingFile = listResponse.Contents?.find(obj => {
            if (!obj.Key) return false;
            
            // Strategy 1: Exact filename match (end of path)
            if (obj.Key.endsWith(fileName)) {
              console.log(`✅ Found exact match: ${obj.Key}`);
              return true;
            }
            
            // Strategy 2: Contains filename
            if (obj.Key.includes(fileName)) {
              console.log(`✅ Found contains match: ${obj.Key}`);
              return true;
            }
            
            // Strategy 3: Extract the unique part after timestamp-random
            // Files are named like: 1750422266282-5bcejg-3.jpg
            // We need to match the "3.jpg" part
            const parts = fileName.split('-');
            if (parts.length >= 3) {
              const uniquePart = parts.slice(2).join('-'); // Get "3.jpg" part
              if (obj.Key.includes(uniquePart)) {
                console.log(`✅ Found unique part match: ${obj.Key} (${uniquePart})`);
                return true;
              }
            }
            
            return false;
          });
        }

        if (!matchingFile || !matchingFile.Key) {
          console.warn(`❌ Could not find temp artwork file for: ${fileName}`);
          relocatedFiles.push(fileName);
          continue;
        }

        const tempKey = matchingFile.Key;
        const newKey = `artwork/${newOrderNumber}/${fileName}`;

        console.log(`🔄 Relocating artwork: ${tempKey} → ${newKey}`);

        // Try to copy the file to the new location
        const copyCommand = new CopyObjectCommand({
          Bucket: config.bucketName,
          CopySource: `${config.bucketName}/${tempKey}`,
          Key: newKey,
          ACL: 'public-read'
        });

        await s3Client.send(copyCommand);

        // Delete the original file
        const deleteCommand = new DeleteObjectCommand({
          Bucket: config.bucketName,
          Key: tempKey
        });

        await s3Client.send(deleteCommand);

        relocatedFiles.push(fileName);
        console.log(`✅ Successfully relocated artwork file: ${tempKey} → ${newKey}`);

      } catch (error) {
        console.error(`❌ Failed to relocate artwork file: ${fileName}`, error);
        // Keep the original filename even if relocation fails
        relocatedFiles.push(fileName);
      }
    }

    return relocatedFiles;
  } catch (error) {
    console.error('Error in relocateArtworkFiles:', error);
    return artworkFiles; // Return original filenames if relocation fails
  }
}

// Function to relocate reference files from temporary path to final order path
async function relocateReferenceFiles(referenceFiles: string[], newOrderNumber: string): Promise<string[]> {
  try {
    const config = await getDefaultStorageConfig();
    if (!config) {
      console.error('No S3 configuration found for file relocation');
      return referenceFiles; // Return original filenames if can't relocate
    }

    const s3Client = new S3Client({
      region: config.region,
      endpoint: config.endpoint,
      credentials: {
        accessKeyId: config.accessKey,
        secretAccessKey: config.secretKey,
      },
      forcePathStyle: true
    });

    const relocatedFiles: string[] = [];

    for (const fileName of referenceFiles) {
      try {
        // The files are uploaded with temp order numbers like: artwork/TEMP-1234567890-abc/filename.jpg
        // We need to find the actual temp path and move it to: artwork/ORD-xxx/filename.jpg
        
        // Search for files that match this filename pattern in temp directories
        const listCommand = new ListObjectsV2Command({
          Bucket: config.bucketName,
          Prefix: 'artwork/TEMP-',
          MaxKeys: 1000
        });

        const listResponse = await s3Client.send(listCommand);
        
        // Find the file that contains our filename
        const matchingFile = listResponse.Contents?.find(obj => 
          obj.Key && obj.Key.includes(fileName)
        );

        if (!matchingFile || !matchingFile.Key) {
          console.warn(`❌ Could not find temp file for: ${fileName}`);
          relocatedFiles.push(fileName);
          continue;
        }

        const tempKey = matchingFile.Key;
        const newKey = `artwork/${newOrderNumber}/${fileName}`;

        console.log(`🔄 Relocating: ${tempKey} → ${newKey}`);

        // Try to copy the file to the new location
        const copyCommand = new CopyObjectCommand({
          Bucket: config.bucketName,
          CopySource: `${config.bucketName}/${tempKey}`,
          Key: newKey,
          ACL: 'public-read'
        });

        await s3Client.send(copyCommand);

        // Delete the original file
        const deleteCommand = new DeleteObjectCommand({
          Bucket: config.bucketName,
          Key: tempKey
        });

        await s3Client.send(deleteCommand);

        relocatedFiles.push(fileName);
        console.log(`✅ Successfully relocated reference file: ${tempKey} → ${newKey}`);

      } catch (error) {
        console.error(`❌ Failed to relocate reference file: ${fileName}`, error);
        // Keep the original filename even if relocation fails
        relocatedFiles.push(fileName);
      }
    }

    return relocatedFiles;
  } catch (error) {
    console.error('Error in relocateReferenceFiles:', error);
    return referenceFiles; // Return original filenames if relocation fails
  }
}

export async function POST(request: NextRequest) {
  try {
    // Rate limiting and CSRF protection would go here in production
    
    const body = await request.json();
    
    // Enhanced input validation and sanitization
    if (!body.customerName || typeof body.customerName !== 'string' || body.customerName.trim().length < 2) {
      return NextResponse.json(
        { error: 'Customer name is required and must be at least 2 characters' },
        { status: 400 }
      );
    }
    
    if (!body.phone || typeof body.phone !== 'string') {
      return NextResponse.json(
        { error: 'Phone number is required' },
        { status: 400 }
      );
    }
    
    if (!body.email || typeof body.email !== 'string' || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(body.email)) {
      return NextResponse.json(
        { error: 'Valid email address is required' },
        { status: 400 }
      );
    }
    
    if (!body.productId || isNaN(parseInt(body.productId))) {
      return NextResponse.json(
        { error: 'Valid product ID is required' },
        { status: 400 }
      );
    }
    
    // Generate order number
    const orderNumber = `ORD-${Date.now()}-${Math.random().toString(36).substr(2, 4).toUpperCase()}`;
    
    // Calculate actual quantity
    const actualQuantity = body.useCustomQuantity 
      ? parseInt(body.customQuantity) || 1 
      : body.quantity || 1;

    // Handle artwork file relocation if needed (for both regular and design-only orders)
    let finalArtworkFiles: string[] = [];
    let artworkUrls: string[] = [];
    if (body.artworkFiles && body.artworkFiles.length > 0) {
      // Convert array of objects to array of filenames for relocation
      // Priority: fileName (S3 key) > name (fallback for legacy data)
      const fileNames = body.artworkFiles.map((file: any) => {
        console.log(`🔍 Processing artwork file:`, file);
        return file.fileName || file.name;
      });
      console.log(`📋 Extracted filenames for relocation:`, fileNames);
      
      try {
        finalArtworkFiles = await relocateArtworkFiles(fileNames, orderNumber);
        
        // If relocation failed but we still have original filenames, use them
        if (finalArtworkFiles.length === 0 && fileNames.length > 0) {
          console.log(`⚠️ File relocation failed, using original filenames for database storage`);
          finalArtworkFiles = fileNames;
        }
        
        // Generate the full S3 URLs for the files
        const config = await getDefaultStorageConfig();
        if (config) {
          artworkUrls = finalArtworkFiles.map(fileName => 
            `${config.endpoint}/${config.bucketName}/artwork/${orderNumber}/${fileName}`
          );
        }
      } catch (error) {
        console.error(`❌ Error during file relocation:`, error);
        // Fallback: use original filenames so the order still gets created with file references
        finalArtworkFiles = fileNames;
        console.log(`⚠️ Using fallback filenames for order creation:`, finalArtworkFiles);
      }
    }

    // Debug logging for order creation
    console.log(`🔍 [OrderAPI] Creating order with data:`, {
      orderNumber,
      orderType: body.orderType,
      designOnly: body.designOnly,
      needsDesign: body.needsDesign,
      artworkFilesInput: body.artworkFiles,
      finalArtworkFiles,
      artworkFileCount: finalArtworkFiles.length
    });

    // Create the order
    const order = await (prisma as any).order.create({
      data: {
        orderNumber,
        customerName: body.customerName,
        email: body.email,
        phone: body.phone,
        productId: parseInt(body.productId),
        productName: body.productName,
        quantity: actualQuantity,
        customQuantity: body.useCustomQuantity || false,
        unitPrice: body.unitPrice || 0,
        designFee: body.designFee || 0, // Dynamic design fee from frontend
        subtotal: body.subtotal || 0,
        totalAmount: body.totalAmount || 0,
        paperType: body.paperType || null,
        printingSide: body.printingSide || null,
        meters: body.meters ? parseFloat(body.meters) : null,
        needsDesign: body.needsDesign || false,
        designOnly: body.designOnly || false,
        designBrief: body.designBrief || null,
        artworkFiles: finalArtworkFiles, // Use relocated artwork files
        artworkFileCount: finalArtworkFiles.length,
        deliveryMethod: body.deliveryMethod || 'pickup',
        deliveryAddress: body.deliveryAddress || null,
        notes: body.notes || null,
        status: 'pending',
        paymentStatus: 'pending'
      }
    });

    console.log(`✅ [OrderAPI] Order created successfully:`, {
      id: order.id,
      orderNumber: order.orderNumber,
      artworkFiles: order.artworkFiles,
      artworkFileCount: order.artworkFileCount,
      designOnly: order.designOnly,
      needsDesign: order.needsDesign
    });

    // Send email notification
    try {
      const emailResult = await sendEmail(generateCatalogueOrderNotificationEmail(order));
      console.log('Email notification result:', emailResult);
    } catch (emailError) {
      console.error('Failed to send email notification:', emailError);
      // Don't fail the order creation if email fails
    }

    // If design service is needed, create a design request
    if (body.needsDesign || body.designOnly) {
      // Handle reference file relocation if needed
      let finalReferenceFiles = body.referenceFiles || [];
      
      // If reference files were uploaded with temporary order numbers, relocate them
      if (body.referenceFiles && body.referenceFiles.length > 0) {
        finalReferenceFiles = await relocateReferenceFiles(body.referenceFiles, orderNumber);
      }
      
      await (prisma as any).designRequest.create({
        data: {
          customerName: body.customerName,
          email: body.email,
          phone: body.phone,
          productType: body.designOnly ? `${body.productName} (Design Only)` : body.productName,
          quantity: body.designOnly ? 1 : actualQuantity,
          specifications: body.designBrief,
          urgency: 'standard',
          designType: getDesignType(body.productName),
          designFee: body.designFee || 1000, // Use dynamic fee or default
          printCost: body.designOnly ? 0 : (body.subtotal || 0),
          totalCost: body.totalAmount || 0,
          orderId: order.id,
          referenceFiles: finalReferenceFiles,
          status: 'pending'
        }
      });
    }

    return NextResponse.json({
      success: true,
      order: {
        id: order.id,
        orderNumber: order.orderNumber,
        needsDesign: order.needsDesign,
        totalAmount: order.totalAmount,
        artworkUrls: artworkUrls // Include the S3 URLs for artwork files
      }
    }, { status: 201 });

  } catch (error) {
    console.error('Order creation error:', error);
    return NextResponse.json(
      { error: 'Failed to create order' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const limit = parseInt(searchParams.get('limit') || '50');
    
    const where = status ? { status } : {};
    
    const orders = await (prisma as any).order.findMany({
      where,
      include: {
        product: {
          select: {
            service: true,
            category: true
          }
        },
        designRequests: {
          select: {
            id: true,
            status: true,
            designType: true,
            urgency: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: limit
    });

    return NextResponse.json(orders);
  } catch (error) {
    console.error('Orders fetch error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch orders' },
      { status: 500 }
    );
  }
}

function getDesignType(productName: string): string {
  const name = productName.toLowerCase();
  
  if (name.includes('business card')) return 'business_card';
  if (name.includes('logo')) return 'logo';
  if (name.includes('flyer')) return 'flyer';
  if (name.includes('banner')) return 'banner';
  if (name.includes('brochure')) return 'brochure';
  if (name.includes('poster')) return 'poster';
  if (name.includes('sticker')) return 'sticker';
  
  return 'general';
} 