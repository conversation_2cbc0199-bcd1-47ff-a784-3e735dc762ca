import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    
    const order = await prisma.order.findUnique({
      where: { id },
      include: {
        product: {
          select: {
            service: true,
            category: true
          }
        },
        designRequests: {
          select: {
            id: true,
            status: true,
            designType: true,
            urgency: true,
            specifications: true,
            designFee: true
          }
        }
      }
    });

    if (!order) {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(order);
  } catch (error) {
    console.error('Order fetch error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch order' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const body = await request.json();
    
    const updateData: any = {};
    
    if (body.status) {
      updateData.status = body.status;
      
      // Update timestamps based on status
      if (body.status === 'confirmed') {
        updateData.confirmedAt = new Date();
      } else if (body.status === 'delivered') {
        updateData.completedAt = new Date();
      }
    }
    
    if (body.paymentStatus) {
      updateData.paymentStatus = body.paymentStatus;
    }
    
    if (body.notes !== undefined) {
      updateData.notes = body.notes;
    }

    const updatedOrder = await prisma.order.update({
      where: { id },
      data: updateData,
      include: {
        product: {
          select: {
            service: true,
            category: true
          }
        },
        designRequests: {
          select: {
            id: true,
            status: true,
            designType: true,
            urgency: true
          }
        }
      }
    });

    return NextResponse.json(updatedOrder);
  } catch (error) {
    console.error('Order update error:', error);
    return NextResponse.json(
      { error: 'Failed to update order' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    
    // Parse request body to check for permanent deletion flag
    let requestBody = {};
    try {
      const body = await request.text();
      if (body) {
        requestBody = JSON.parse(body);
      }
    } catch (error) {
      // If body parsing fails, continue with default behavior (cancellation)
      console.log('No request body or invalid JSON, proceeding with cancellation');
    }
    
    const isPermanentDelete = (requestBody as any).permanent === true;
    
    console.log(`🗑️ [OrderAPI] DELETE request for order ${id}, permanent: ${isPermanentDelete}`);
    
    // Check if order exists
    const existingOrder = await prisma.order.findUnique({
      where: { id },
      include: {
        designRequests: true
      }
    });

    if (!existingOrder) {
      return NextResponse.json(
        { error: 'Order not found' },
        { status: 404 }
      );
    }

    if (isPermanentDelete) {
      // Permanent deletion - remove the order completely
      console.log(`🗑️ [OrderAPI] Permanently deleting order ${id} (status: ${existingOrder.status})`);
      
      const result = await prisma.$transaction(async (tx) => {
        // Delete associated design requests first
        if (existingOrder.designRequests.length > 0) {
          await tx.designRequest.deleteMany({
            where: { orderId: id }
          });
        }

        // Delete the order
        await tx.order.delete({
          where: { id }
        });

        return { deleted: true };
      });

      console.log(`✅ [OrderAPI] Successfully deleted order ${id}`);

      return NextResponse.json({
        success: true,
        message: 'Order deleted permanently',
        deleted: true
      });
    } else {
      // Regular cancellation - only for pending or confirmed orders
      if (!['pending', 'confirmed'].includes(existingOrder.status)) {
        return NextResponse.json(
          { error: 'Order cannot be cancelled. Only pending or confirmed orders can be cancelled.' },
          { status: 400 }
        );
      }

      console.log(`❌ [OrderAPI] Cancelling order ${id} (status: ${existingOrder.status})`);

      // Use transaction to ensure data consistency
      const result = await prisma.$transaction(async (tx) => {
        // Cancel associated design requests
        if (existingOrder.designRequests.length > 0) {
          await tx.designRequest.updateMany({
            where: { orderId: id },
            data: { status: 'cancelled' }
          });
        }

        // Update order status to cancelled instead of deleting
        const cancelledOrder = await tx.order.update({
          where: { id },
          data: {
            status: 'cancelled',
            updatedAt: new Date()
          }
        });

        return cancelledOrder;
      });

      console.log(`✅ [OrderAPI] Successfully cancelled order ${id}`);

      return NextResponse.json({
        success: true,
        message: 'Order cancelled successfully',
        order: result
      });
    }

  } catch (error) {
    console.error('Order deletion/cancellation error:', error);
    return NextResponse.json(
      { error: 'Failed to process order deletion' },
      { status: 500 }
    );
  }
} 