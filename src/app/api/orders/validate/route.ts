import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { fieldValidators } from '@/lib/validation/order-schemas';
import { validateRequestBody } from '@/lib/validation/security-validator';
import DOMPurify from 'isomorphic-dompurify';

// Rate limiting map (in production, use Redis)
const rateLimitMap = new Map<string, { count: number; lastReset: number }>();
const RATE_LIMIT_WINDOW = 60 * 1000; // 1 minute
const RATE_LIMIT_MAX_REQUESTS = 30; // 30 requests per minute

function getRateLimitKey(request: NextRequest): string {
  return request.headers.get('x-forwarded-for') || 
         request.headers.get('x-real-ip') || 
         'unknown';
}

function isRateLimited(key: string): boolean {
  const now = Date.now();
  const limit = rateLimitMap.get(key);
  
  if (!limit) {
    rateLimitMap.set(key, { count: 1, lastReset: now });
    return false;
  }
  
  if (now - limit.lastReset > RATE_LIMIT_WINDOW) {
    rateLimitMap.set(key, { count: 1, lastReset: now });
    return false;
  }
  
  if (limit.count >= RATE_LIMIT_MAX_REQUESTS) {
    return true;
  }
  
  limit.count++;
  return false;
}

// Validation request schema
const ValidationRequestSchema = z.object({
  field: z.enum(['name', 'phone', 'email', 'quantity', 'designBrief']),
  value: z.union([z.string(), z.number()]),
  context: z.object({
    orderType: z.enum(['print', 'design']).optional()
  }).optional()
});

export async function POST(request: NextRequest) {
  try {
    // Rate limiting check
    const clientKey = getRateLimitKey(request);
    if (isRateLimited(clientKey)) {
      return NextResponse.json(
        { error: 'Too many validation requests. Please slow down.' },
        { status: 429, headers: { 'Retry-After': '60' } }
      );
    }

    // Parse request body
    const body = await request.json();
    
    // Validate with schema
    const parseResult = ValidationRequestSchema.safeParse(body);
    
    if (!parseResult.success) {
      return NextResponse.json(
        { error: 'Invalid request data' },
        { status: 400 }
      );
    }

    const { field, value, context } = parseResult.data;

    // Sanitize the value
    const sanitizedValue = typeof value === 'string' 
      ? DOMPurify.sanitize(value.trim())
      : value;

    // Validate the specific field
    let validationError: string | null = null;

    switch (field) {
      case 'name':
        validationError = fieldValidators.name(sanitizedValue as string);
        break;
      case 'phone':
        validationError = fieldValidators.phone(sanitizedValue as string);
        break;
      case 'email':
        validationError = fieldValidators.email(sanitizedValue as string);
        break;
      case 'quantity':
        validationError = fieldValidators.quantity(sanitizedValue as number);
        break;
      case 'designBrief':
        validationError = fieldValidators.designBrief(
          sanitizedValue as string, 
          context?.orderType
        );
        break;
      default:
        return NextResponse.json(
          { error: 'Unknown field type' },
          { status: 400 }
        );
    }

    return NextResponse.json({
      valid: !validationError,
      error: validationError,
      sanitizedValue: sanitizedValue
    });

  } catch (error) {
    console.error('Validation API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// Health check endpoint
export async function GET() {
  return NextResponse.json({ 
    status: 'healthy',
    timestamp: new Date().toISOString()
  });
} 