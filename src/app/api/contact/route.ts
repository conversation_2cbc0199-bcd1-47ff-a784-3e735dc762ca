import { NextRequest, NextResponse } from 'next/server';
import { sanitizeContactForm, validateRequired } from '@/utils/sanitization';
import { handleApiError, createSuccessResponse, createError } from '@/lib/api-error-handler';

export async function POST(request: Request) {
  try {
    // Get form data
    const formData = await request.formData();
    const rawData = {
      name: formData.get('name') as string,
      email: formData.get('email') as string,
      phone: formData.get('phone') as string,
      service: formData.get('service') as string,
      subject: formData.get('subject') as string,
      message: formData.get('message') as string,
      company: formData.get('company') as string,
    };
    
    const source = formData.get('source') as string || 'contact_form';
    const sessionId = formData.get('sessionId') as string;

    // Validate required fields
    try {
      validateRequired({
        name: rawData.name,
        email: rawData.email,
        message: rawData.message
      });
    } catch (validationError) {
      throw createError.validation(
        'Missing required fields',
        { missing: ['name', 'email', 'message'].filter(field => !rawData[field as keyof typeof rawData]) }
      );
    }

    // Sanitize all input data
    const sanitizedData = sanitizeContactForm(rawData);

    // Log the contact form submission (lead management removed)
    console.log('[Contact] Form submission received:', {
      name: sanitizedData.name,
      email: sanitizedData.email,
      phone: sanitizedData.phone,
      company: sanitizedData.company,
      source: source || 'website_contact',
      subject: sanitizedData.subject,
      message: sanitizedData.message,
    });

    // Return success
    return createSuccessResponse(
      { submitted: true },
      'Your message has been sent. We will get back to you soon!'
    );
  } catch (error) {
    console.error('Error processing contact form:', error);
    return handleApiError(error);
  }
}