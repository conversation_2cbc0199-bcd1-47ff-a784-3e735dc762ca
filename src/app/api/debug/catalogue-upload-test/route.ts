import { NextRequest, NextResponse } from 'next/server';
import { requireAdminAuth } from '@/lib/auth-helpers';
import { uploadImageToS3 } from '@/utils/s3Utils';
import { getDefaultStorageConfig } from '@/lib/storageConfig';
import { auditLogger } from '@/lib/audit-logger';

export async function POST(request: NextRequest) {
  const requestId = `test-upload-${Date.now()}-${Math.random().toString(36).substring(2)}`;
  
  try {
    console.log(`[Upload Debug] ${requestId} - Starting catalogue upload diagnostic`);
    
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json({
        success: false,
        error: 'Unauthorized',
        stage: 'authentication',
        progress: 0
      }, { status: 401 });
    }

    console.log(`[Upload Debug] ${requestId} - User authenticated: ${user.email}`);

    const formData = await request.formData();
    const file = formData.get('file') as File;
    const testMode = formData.get('testMode') === 'true';

    if (!file) {
      return NextResponse.json({
        success: false,
        error: 'No file provided',
        stage: 'validation',
        progress: 5
      }, { status: 400 });
    }

    console.log(`[Upload Debug] ${requestId} - File received: ${file.name} (${file.size} bytes)`);

    // Stage 1: File Validation (0-10%)
    const validationStart = Date.now();
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif', 'image/avif'];
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json({
        success: false,
        error: `Invalid file type: ${file.type}`,
        stage: 'validation',
        progress: 5
      }, { status: 400 });
    }

    const maxSize = 50 * 1024 * 1024; // 50MB
    if (file.size > maxSize) {
      return NextResponse.json({
        success: false,
        error: `File too large: ${Math.round(file.size / (1024 * 1024))}MB`,
        stage: 'validation',
        progress: 5
      }, { status: 400 });
    }
    
    const validationTime = Date.now() - validationStart;
    console.log(`[Upload Debug] ${requestId} - Validation complete in ${validationTime}ms`);

    // Stage 2: S3 Configuration Check (10-20%)
    const configStart = Date.now();
    const s3Config = await getDefaultStorageConfig();
    if (!s3Config) {
      return NextResponse.json({
        success: false,
        error: 'S3 configuration not found',
        stage: 'configuration',
        progress: 15
      }, { status: 500 });
    }

    console.log(`[Upload Debug] ${requestId} - S3 Config check: endpoint=${s3Config.endpoint}, bucket=${s3Config.bucketName}`);
    const configTime = Date.now() - configStart;

    // Stage 3: Generate S3 Key (20-30%)
    const keyGenStart = Date.now();
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2);
    const sanitizedName = file.name.replace(/[^a-zA-Z0-9.-]/g, '_');
    const s3Key = `images/catalogue/debug-test/${timestamp}-${randomString}-${sanitizedName}`;
    
    console.log(`[Upload Debug] ${requestId} - Generated S3 key: ${s3Key}`);
    const keyGenTime = Date.now() - keyGenStart;

    if (testMode) {
      // Test mode - just return diagnostics without actual upload
      return NextResponse.json({
        success: true,
        stage: 'test-complete',
        progress: 100,
        diagnostics: {
          requestId,
          fileInfo: {
            name: file.name,
            size: file.size,
            type: file.type
          },
          s3Config: {
            endpoint: s3Config.endpoint,
            bucket: s3Config.bucketName,
            region: s3Config.region
          },
          generatedKey: s3Key,
          timings: {
            validation: `${validationTime}ms`,
            configuration: `${configTime}ms`,
            keyGeneration: `${keyGenTime}ms`
          }
        }
      });
    }

    // Stage 4: Actual Upload (30-95%)
    const uploadStart = Date.now();
    console.log(`[Upload Debug] ${requestId} - Starting S3 upload...`);

    try {
      const uploadResult = await uploadImageToS3(file, s3Key, {
        maxRetries: 3,
        timeoutMs: 300000, // 5 minutes
        logPrefix: `[Upload Debug] ${requestId}`
      });

      const uploadTime = Date.now() - uploadStart;
      console.log(`[Upload Debug] ${requestId} - Upload completed in ${uploadTime}ms`);

      if (!uploadResult.success) {
        console.error(`[Upload Debug] ${requestId} - Upload failed: ${uploadResult.error}`);
        return NextResponse.json({
          success: false,
          error: uploadResult.error,
          stage: 'upload',
          progress: 85, // This is where failures typically occur
          diagnostics: {
            requestId,
            uploadTime: `${uploadTime}ms`,
            s3Key,
            uploadResult
          }
        }, { status: 500 });
      }

      // Stage 5: Post-upload validation (95-100%)
      const validationStart2 = Date.now();
      
      // Verify URL is accessible
      let urlValid = false;
      try {
        const urlTest = await fetch(uploadResult.url, { method: 'HEAD' });
        urlValid = urlTest.ok;
        console.log(`[Upload Debug] ${requestId} - URL validation: ${urlValid ? 'PASS' : 'FAIL'} (${urlTest.status})`);
      } catch (urlError) {
        console.warn(`[Upload Debug] ${requestId} - URL validation failed:`, urlError);
      }

      const postValidationTime = Date.now() - validationStart2;

      // Log success
      await auditLogger.info(
        user.email || 'unknown',
        'debug_catalogue_upload_test',
        s3Key,
        {
          filename: file.name,
          size: file.size,
          type: file.type,
          uploadTime,
          urlValid
        },
        request
      );

      const totalTime = Date.now() - validationStart;

      return NextResponse.json({
        success: true,
        stage: 'complete',
        progress: 100,
        url: uploadResult.url,
        diagnostics: {
          requestId,
          fileInfo: {
            name: file.name,
            size: file.size,
            type: file.type
          },
          s3Config: {
            endpoint: s3Config.endpoint,
            bucket: s3Config.bucketName,
            region: s3Config.region
          },
          uploadResult,
          urlValid,
          timings: {
            total: `${totalTime}ms`,
            validation: `${validationTime}ms`,
            configuration: `${configTime}ms`,
            keyGeneration: `${keyGenTime}ms`,
            upload: `${uploadTime}ms`,
            postValidation: `${postValidationTime}ms`
          },
          s3Key
        }
      });

    } catch (uploadError) {
      const uploadTime = Date.now() - uploadStart;
      console.error(`[Upload Debug] ${requestId} - Upload error after ${uploadTime}ms:`, uploadError);

      // Log the failure with detailed information
      await auditLogger.error(
        user.email || 'unknown',
        'debug_catalogue_upload_failed',
        s3Key,
        {
          filename: file.name,
          size: file.size,
          type: file.type,
          error: uploadError instanceof Error ? uploadError.message : String(uploadError),
          uploadTime,
          stage: 'upload'
        },
        request
      );

      return NextResponse.json({
        success: false,
        error: uploadError instanceof Error ? uploadError.message : 'Unknown upload error',
        stage: 'upload',
        progress: 85,
        diagnostics: {
          requestId,
          uploadTime: `${uploadTime}ms`,
          s3Key,
          errorDetails: {
            name: uploadError instanceof Error ? uploadError.name : 'UnknownError',
            message: uploadError instanceof Error ? uploadError.message : String(uploadError),
            stack: uploadError instanceof Error ? uploadError.stack : undefined
          }
        }
      }, { status: 500 });
    }

  } catch (error) {
    console.error(`[Upload Debug] ${requestId} - Unexpected error:`, error);

    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unexpected error',
      stage: 'unknown',
      progress: 0,
      diagnostics: {
        requestId,
        errorDetails: {
          name: error instanceof Error ? error.name : 'UnknownError',
          message: error instanceof Error ? error.message : String(error),
          stack: error instanceof Error ? error.stack : undefined
        }
      }
    }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;

    return NextResponse.json({
      message: 'Catalogue Upload Debug Endpoint',
      usage: {
        post: 'Upload a test file to diagnose issues',
        parameters: {
          file: 'Required - Image file to upload',
          testMode: 'Optional - Set to "true" for configuration test only (no actual upload)'
        }
      },
      endpoints: {
        test: 'POST /api/debug/catalogue-upload-test',
        testConfigOnly: 'POST /api/debug/catalogue-upload-test (with testMode=true)'
      }
    });

  } catch (error) {
    return NextResponse.json({
      error: 'Authentication required'
    }, { status: 401 });
  }
} 