import { NextRequest, NextResponse } from 'next/server';
import * as quoteService from '@/services/quoteService';

/**
 * Debug route for quote fetching - bypasses authentication for testing
 * This should be removed in production
 */
export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    console.log('=== Debug Quote Route ===');
    console.log('Request URL:', req.url);
    console.log('Request pathname:', req.nextUrl.pathname);
    
    const resolvedParams = await params;
    const quoteId = resolvedParams.id;
    
    console.log('Quote ID from params:', quoteId);
    console.log('Quote ID type:', typeof quoteId);
    console.log('Quote ID length:', quoteId?.length);
    
    // Validate quote ID
    if (!quoteId || quoteId === 'undefined' || quoteId === 'null') {
      console.error('Invalid quote ID:', quoteId);
      return NextResponse.json({ 
        error: 'Invalid quote ID',
        debug: {
          quoteId,
          type: typeof quoteId,
          length: quoteId?.length
        }
      }, { status: 400 });
    }
    
    // Get quote by ID (bypassing authentication for debug)
    console.log('Attempting to fetch quote with ID:', quoteId);
    const quote = await quoteService.getQuoteById(quoteId);
    
    if (!quote) {
      console.log('Quote not found in database:', quoteId);
      
      // Get all quotes for debugging
      const allQuotes = await quoteService.getAllQuotes();
      console.log('Available quotes:', allQuotes.map(q => ({ id: q.id, quoteNumber: q.quoteNumber })));
      
      return NextResponse.json({ 
        error: 'Quote not found',
        debug: {
          requestedId: quoteId,
          availableQuotes: allQuotes.map(q => ({ id: q.id, quoteNumber: q.quoteNumber }))
        }
      }, { status: 404 });
    }
    
    console.log('Quote found:', quote.quoteNumber);
    return NextResponse.json({
      success: true,
      quote,
      debug: {
        requestedId: quoteId,
        foundId: quote.id,
        quoteNumber: quote.quoteNumber
      }
    });
    
  } catch (error) {
    console.error('Error in debug quote route:', error);
    return NextResponse.json({
      error: 'Failed to fetch quote',
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 });
  }
}
