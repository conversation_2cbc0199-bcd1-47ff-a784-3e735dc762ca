import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// GET - Fetch single logo package
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const pkg = await prisma.logoPackage.findUnique({
      where: { id }
    });

    if (!pkg) {
      return NextResponse.json(
        { success: false, error: 'Package not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: pkg
    });
  } catch (error) {
    console.error('Error fetching logo package:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch logo package' },
      { status: 500 }
    );
  }
}
