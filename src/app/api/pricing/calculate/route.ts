import { NextRequest, NextResponse } from 'next/server';
import PricingService, { PricingInput } from '@/services/pricingService';

export async function POST(request: NextRequest) {
  try {
    const body: PricingInput = await request.json();
    
    // Validate required fields
    if (!body.productId) {
      return NextResponse.json(
        { error: 'Product ID is required' },
        { status: 400 }
      );
    }

    const calculation = await PricingService.calculatePrice(body);
    
    return NextResponse.json(calculation);
  } catch (error) {
    console.error('Price calculation API error:', error);
    return NextResponse.json(
      { error: 'Failed to calculate price' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const productId = searchParams.get('productId');
    
    if (!productId) {
      return NextResponse.json(
        { error: 'Product ID is required' },
        { status: 400 }
      );
    }

    const options = await PricingService.getProductPricingOptions(parseInt(productId));
    
    if (!options) {
      return NextResponse.json(
        { error: 'Product not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(options);
  } catch (error) {
    console.error('Pricing options API error:', error);
    return NextResponse.json(
      { error: 'Failed to get pricing options' },
      { status: 500 }
    );
  }
} 