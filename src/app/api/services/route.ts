import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';

export const dynamic = 'force-dynamic';
export const revalidate = 300; // Cache for 5 minutes

/**
 * GET /api/services
 * Public endpoint to get services (primarily for design services on product pages)
 */
export async function GET(req: NextRequest) {
  try {
    console.log('[Public Services API] GET request');

    // Get query parameters
    const searchParams = req.nextUrl.searchParams;
    const category = searchParams.get('category');

    // Build where clause
    let whereClause: any = {};
    if (category) {
      whereClause.category = category.toLowerCase();
    }

    // Get services
    const services = await prisma.service.findMany({
      where: whereClause,
      select: {
        id: true,
        name: true,
        description: true,
        price: true,
        category: true,
        createdAt: true,
        updatedAt: true
      },
      orderBy: {
        name: 'asc'
      }
    });

    console.log(`[Public Services API] Retrieved ${services.length} services${category ? ` in category "${category}"` : ''}`);

    return NextResponse.json({
      success: true,
      data: services,
      count: services.length
    });
  } catch (error) {
    console.error('[Public Services API] Error getting services:', error);
    return NextResponse.json({
      success: false,
      error: 'Failed to get services',
      message: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
