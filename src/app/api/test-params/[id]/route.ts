import { NextRequest, NextResponse } from 'next/server';

/**
 * Test route to debug parameter handling
 */
export async function GET(req: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    console.log('=== Test Params Route Debug ===');
    console.log('Request URL:', req.url);
    console.log('Request pathname:', req.nextUrl.pathname);
    console.log('Params type:', typeof params);
    console.log('Params value:', params);
    
    const resolvedParams = await params;
    console.log('Resolved params:', resolvedParams);
    console.log('ID from params:', resolvedParams.id);
    console.log('ID type:', typeof resolvedParams.id);
    console.log('ID length:', resolvedParams.id?.length);
    
    return NextResponse.json({
      success: true,
      debug: {
        url: req.url,
        pathname: req.nextUrl.pathname,
        paramsType: typeof params,
        resolvedParams,
        id: resolvedParams.id,
        idType: typeof resolvedParams.id,
        idLength: resolvedParams.id?.length
      }
    });
  } catch (error) {
    console.error('Error in test params route:', error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 });
  }
}
