import { NextRequest, NextResponse } from 'next/server';
import { revalidateTag, revalidatePath } from 'next/cache';
import { requireAdminAuth } from '@/lib/auth-helpers';
import { clearPortfolioCache } from '@/app/api/admin/portfolio/route';

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    console.log(`[Portfolio Revalidate] Request from user: ${user.email}`);

    // Parse request body for specific actions
    let body;
    try {
      body = await request.json();
    } catch {
      body = {};
    }

    const { type = 'all', category } = body;

    // Clear server-side cache first
    clearPortfolioCache();

    // Revalidate cache tags
    if (type === 'category' && category) {
      revalidateTag('portfolio-data');
      console.log(`[Portfolio Revalidate] Revalidated category: ${category}`);
    } else {
      // Revalidate all portfolio data
      revalidateTag('portfolio-data');
      revalidateTag('portfolio-all');
      console.log('[Portfolio Revalidate] Revalidated all portfolio data');
    }

    // Also revalidate the portfolio page itself
    revalidatePath('/portfolio');
    
    // Optionally revalidate specific category pages if they exist
    if (category) {
      revalidatePath(`/portfolio/${category}`);
    }

    return NextResponse.json({
      success: true,
      message: `Portfolio cache ${type === 'category' ? `for ${category}` : 'for all categories'} has been invalidated`,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('[Portfolio Revalidate] Error:', error);
    return NextResponse.json(
      { error: 'Failed to revalidate portfolio cache' },
      { status: 500 }
    );
  }
}

// GET endpoint for checking cache status
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    return NextResponse.json({
      cacheVersion: process.env.PORTFOLIO_CACHE_VERSION || 'v1',
      revalidateInterval: 300, // 5 minutes
      lastChecked: new Date().toISOString()
    });

  } catch (error) {
    console.error('[Portfolio Cache Status] Error:', error);
    return NextResponse.json(
      { error: 'Failed to get cache status' },
      { status: 500 }
    );
  }
} 