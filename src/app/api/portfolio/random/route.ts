import { NextResponse } from 'next/server';
import { S3Client, ListObjectsV2Command, GetObjectCommand } from '@aws-sdk/client-s3';
import { v4 as uuidv4 } from 'uuid';
import sharp from 'sharp';
import { getDefaultStorageConfig } from '@/lib/storageConfig';

// S3 Client - will be initialized from storage config
let s3Client: S3Client;
let s3Config: any;

// Initialize S3 client from storage configuration
async function initS3Client() {
  if (s3Client && s3Config) {
    return { s3Client, s3Config };
  }

  const config = await getDefaultStorageConfig();
  if (!config) {
    throw new Error('No storage configuration found');
  }

  s3Client = new S3Client({
    region: config.region,
    endpoint: config.endpoint,
    credentials: {
      accessKeyId: config.accessKey,
      secretAccessKey: config.secretKey,
    },
    forcePathStyle: true
  });

  s3Config = config;
  return { s3Client, s3Config };
}

const PORTFOLIO_BASE_PATH = 'images/portfolio';

// Portfolio categories to fetch from (only fliers and branding for homepage)
const PORTFOLIO_CATEGORIES = [
  'fliers',
  'branding'
];

interface PortfolioImage {
  id: string;
  src: string;
  alt: string;
  title: string;
  category: string;
  link: string;
}

// Helper function to get category display name
function getCategoryDisplayName(category: string): string {
  const categoryMap: Record<string, string> = {
    'branding': 'Branding',
    'fliers': 'Flyer Design'
  };
  return categoryMap[category] || category;
}

// Helper function to get category link
function getCategoryLink(category: string): string {
  const linkMap: Record<string, string> = {
    'branding': '/portfolio?category=branding',
    'fliers': '/portfolio?category=fliers'
  };
  return linkMap[category] || '/portfolio';
}

// Helper function to format title from filename
function formatTitleFromFilename(filename: string): string {
  return filename
    .replace(/\.(jpg|jpeg|png|webp|gif)$/i, '')
    .replace(/[-_]/g, ' ')
    .replace(/\b\w/g, l => l.toUpperCase());
}

// Simple cache for image aspect ratios to avoid repeated downloads
const aspectRatioCache = new Map<string, number>();

// Check if an image is likely square based on filename patterns and common square dimensions
function isLikelySquareImage(filename: string): boolean {
  // Common square image indicators in filenames
  const squarePatterns = [
    /1080x1080/i,
    /1000x1000/i,
    /800x800/i,
    /600x600/i,
    /500x500/i,
    /400x400/i,
    /300x300/i,
    /200x200/i,
    /square/i,
    /logo/i, // Logos are often square
  ];

  return squarePatterns.some(pattern => pattern.test(filename));
}

// Check if an image is square by reading image metadata (with better error handling)
async function isSquareImage(imageKey: string): Promise<boolean> {
  try {
    // Check cache first
    if (aspectRatioCache.has(imageKey)) {
      const aspectRatio = aspectRatioCache.get(imageKey)!;
      return aspectRatio >= 0.9 && aspectRatio <= 1.1;
    }

    // Quick check based on filename patterns
    const filename = imageKey.split('/').pop() || '';
    if (isLikelySquareImage(filename)) {
      aspectRatioCache.set(imageKey, 1.0); // Assume square
      return true;
    }

    // For logos category, assume most are square (common case)
    if (imageKey.includes('/logos/')) {
      aspectRatioCache.set(imageKey, 1.0);
      return true;
    }

    // Try to get image metadata with larger range for better header reading
    const { s3Client, s3Config } = await initS3Client();
    const getObjectCommand = new GetObjectCommand({
      Bucket: s3Config.bucketName,
      Key: imageKey,
      Range: 'bytes=0-8192', // Read first 8KB for better header coverage
    });

    const response = await s3Client.send(getObjectCommand);

    if (!response.Body) {
      return false;
    }

    // Convert stream to buffer
    const chunks: Uint8Array[] = [];
    const reader = response.Body.transformToWebStream().getReader();

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;
      chunks.push(value);
    }

    const buffer = Buffer.concat(chunks);

    // Get image metadata using Sharp
    const metadata = await sharp(buffer).metadata();

    if (!metadata.width || !metadata.height) {
      // If we can't determine dimensions, assume it's not square
      return false;
    }

    // Calculate aspect ratio
    const aspectRatio = metadata.width / metadata.height;

    // Cache the result
    aspectRatioCache.set(imageKey, aspectRatio);

    // Consider images with aspect ratio between 0.9 and 1.1 as square
    return aspectRatio >= 0.9 && aspectRatio <= 1.1;

  } catch (error) {
    console.error(`Error checking if image is square for ${imageKey}:`, error);
    // If there's an error, fall back to filename-based detection
    const filename = imageKey.split('/').pop() || '';
    return isLikelySquareImage(filename) || imageKey.includes('/logos/');
  }
}

// Fetch random square images from S3 across all categories
async function fetchRandomPortfolioImages(count: number = 12): Promise<PortfolioImage[]> {
  try {
    const { s3Client, s3Config } = await initS3Client();

    const allImages: PortfolioImage[] = [];

    // Fetch images from each category
    for (const category of PORTFOLIO_CATEGORIES) {
      try {
        const prefix = `${PORTFOLIO_BASE_PATH}/${category}/`;

        const command = new ListObjectsV2Command({
          Bucket: s3Config.bucketName,
          Prefix: prefix,
          MaxKeys: 20, // Reduced limit per category for faster processing
        });

        const response = await s3Client.send(command);

        if (response.Contents) {
          const imageItems = response.Contents.filter(item =>
            item.Key &&
            /\.(jpg|jpeg|png|webp|gif)$/i.test(item.Key) &&
            item.Key !== prefix
          );

          // Process images in smaller batches to avoid timeout
          let processedCount = 0;
          const maxProcessPerCategory = 10; // Limit processing per category

          for (const item of imageItems) {
            if (processedCount >= maxProcessPerCategory) break;

            if (!item.Key) continue;

            try {
              // Check if the image is square
              const isSquare = await isSquareImage(item.Key);
              if (!isSquare) {
                continue;
              }

              const filename = item.Key.split('/').pop() || '';
              const imageUrl = `${s3Config.endpoint}/${s3Config.bucketName}/${item.Key}`;

              allImages.push({
                id: uuidv4(),
                src: imageUrl,
                alt: formatTitleFromFilename(filename),
                title: formatTitleFromFilename(filename),
                category: getCategoryDisplayName(category),
                link: getCategoryLink(category)
              });

              processedCount++;
            } catch (imageError) {
              console.error(`Error processing image ${item.Key}:`, imageError);
              continue;
            }
          }
        }
      } catch (categoryError) {
        console.error(`Error fetching images from category ${category}:`, categoryError);
        continue;
      }
    }

    // Shuffle the array and return the requested count
    const shuffled = allImages.sort(() => 0.5 - Math.random());
    return shuffled.slice(0, count);

  } catch (error) {
    console.error('Error fetching random portfolio images:', error);
    return [];
  }
}

export async function GET() {
  try {
    const images = await fetchRandomPortfolioImages(12);

    // If no images from S3, return some fallback data
    if (images.length === 0) {
      console.log('No images found in S3, returning fallback data');
      const fallbackImages: PortfolioImage[] = [
        {
          id: 'fallback-1',
          src: '/images/hero/1.svg',
          alt: 'Digital Marketing Solutions',
          title: 'Digital Marketing Solutions',
          category: 'Digital Marketing',
          link: '/services'
        },
        {
          id: 'fallback-2',
          src: '/images/hero/2.svg',
          alt: 'Web Development',
          title: 'Web Development',
          category: 'Web Development',
          link: '/web-development'
        },
        {
          id: 'fallback-3',
          src: '/images/hero/3.svg',
          alt: 'Graphic Design',
          title: 'Graphic Design',
          category: 'Graphic Design',
          link: '/portfolio'
        }
      ];

      return NextResponse.json(fallbackImages, {
        headers: {
          'Cache-Control': 'public, max-age=60, s-maxage=120', // Shorter cache for fallback
        },
      });
    }

    return NextResponse.json(images, {
      headers: {
        'Cache-Control': 'public, max-age=300, s-maxage=600', // Cache for 5 minutes
      },
    });
  } catch (error) {
    console.error('Error in random portfolio API:', error);
    return NextResponse.json(
      { error: 'Failed to fetch random portfolio images' },
      { status: 500 }
    );
  }
}
