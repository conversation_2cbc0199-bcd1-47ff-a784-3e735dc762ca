import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { requireAdminAuth } from '@/lib/auth-helpers';
import { S3Client, PutObjectCommand, DeleteObjectCommand } from '@aws-sdk/client-s3';
import { getDefaultStorageConfig } from '@/lib/storageConfig';
import { WebsitePortfolioItem } from '@/types/portfolio';
import { getS3ImageUrl, normalizeImageUrl } from '@/utils/imageUtils';

// GET handler - Fetch all website portfolio items
export async function GET() {
  try {
    console.log('Public API: Fetching website portfolio items from database...');
    
    const items = await prisma.websitePortfolio.findMany({
      orderBy: [
        { featured: 'desc' }, // Featured items first
        { createdAt: 'desc' }
      ]
    });

    console.log(`Public API: Retrieved ${items.length} website portfolio items from database`);

     // Map database fields to frontend expected format with proper URL handling
     const mappedItems = items.map(item => {
       let imageSrc = '';
       
       try {
         // Try different approaches to get the image URL
         if (item.imageUrl) {
           // Use existing imageUrl if available
           imageSrc = normalizeImageUrl(item.imageUrl);
         } else if (item.imageKey) {
           // Generate URL from imageKey
           imageSrc = normalizeImageUrl(getS3ImageUrl(item.imageKey));
         } else {
           // Fallback to placeholder
           imageSrc = '/images/placeholder.jpg';
         }
         
         // Debug logging for image URL resolution
         console.log(`Portfolio item ${item.title}: imageUrl=${item.imageUrl}, imageKey=${item.imageKey}, resolved=${imageSrc}`);
         
       } catch (error) {
         console.error(`Error processing image URL for ${item.title}:`, error);
         imageSrc = '/images/placeholder.jpg';
       }

       return {
         id: item.id,
         title: item.title,
         description: item.description || '',
         url: item.projectUrl || item.url || '', // Handle both fields
         category: item.category || 'website',
         featured: item.featured || false,
         imageSrc: imageSrc,
         imageKey: item.imageKey || '',
         createdAt: item.createdAt.toISOString(),
         updatedAt: item.updatedAt.toISOString(),
       };
     });

    console.log(`Public API: Successfully mapped ${mappedItems.length} website portfolio items`);
    
    // Return with cache headers for better performance
    return NextResponse.json(mappedItems, {
      headers: {
        'Cache-Control': 'public, max-age=300, s-maxage=600',
      },
    });
  } catch (error) {
    console.error('Error fetching website portfolio items:', error);
    return NextResponse.json(
      { error: 'Failed to fetch website portfolio items' },
      { status: 500 }
    );
  }
}

// POST handler - Create a new website portfolio item
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;

    const formData = await request.formData();
    const title = formData.get('title') as string;
    const description = formData.get('description') as string;
    const url = formData.get('url') as string;
    const category = formData.get('category') as string;
    const featured = formData.get('featured') === 'true';
    const image = formData.get('image') as File;

    if (!title || !url || !category || !image) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Get S3 configuration
    const config = await getDefaultStorageConfig();
    if (!config) {
      throw new Error('Storage configuration not found');
    }

    // Create S3 client
    const s3Client = new S3Client({
      region: config.region,
      endpoint: config.endpoint,
      credentials: {
        accessKeyId: config.accessKey,
        secretAccessKey: config.secretKey
      }
    });

    // Generate unique image key
    const imageKey = `portfolio/${Date.now()}-${image.name}`;

    // Upload image to S3
    const imageBuffer = await image.arrayBuffer();
    await s3Client.send(new PutObjectCommand({
      Bucket: config.bucketName,
      Key: imageKey,
      Body: Buffer.from(imageBuffer),
      ContentType: image.type,
      ACL: 'public-read'
    }));

    // Create portfolio item in database - using type assertion for new fields
    const portfolioItem = await prisma.websitePortfolio.create({
      data: {
        title,
        description,
        projectUrl: url,
        imageKey: imageKey,
        imageUrl: getS3ImageUrl(imageKey), // Store full URL in imageUrl
        category,
        featured
      } as any // Temporary fix for TypeScript type cache issues
    });

    return NextResponse.json(portfolioItem, { status: 201 });
  } catch (error) {
    console.error('Error creating website portfolio item:', error);
    return NextResponse.json(
      { error: 'Failed to create website portfolio item' },
      { status: 500 }
    );
  }
}

// DELETE handler - Delete a website portfolio item
export async function DELETE(request: NextRequest) {
  try {
    // Check authentication
    const { user, response } = await requireAdminAuth(request);
    if (response) return response;

    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'Portfolio item ID is required' },
        { status: 400 }
      );
    }

    // Get the portfolio item to delete
    const portfolioItem = await prisma.websitePortfolio.findUnique({
      where: { id }
    });

    if (!portfolioItem) {
      return NextResponse.json(
        { error: 'Portfolio item not found' },
        { status: 404 }
      );
    }

    // Get S3 configuration
    const config = await getDefaultStorageConfig();
    if (!config) {
      throw new Error('Storage configuration not found');
    }

    // Create S3 client
    const s3Client = new S3Client({
      region: config.region,
      endpoint: config.endpoint,
      credentials: {
        accessKeyId: config.accessKey,
        secretAccessKey: config.secretKey
      }
    });

    // Delete image from S3 - handle imageKey access
    const imageKey = (portfolioItem as any).imageKey || portfolioItem.imageUrl?.split('/').pop();
    if (imageKey) {
      await s3Client.send(new DeleteObjectCommand({
        Bucket: config.bucketName,
        Key: imageKey
      }));
    }

    // Delete portfolio item from database
    await prisma.websitePortfolio.delete({
      where: { id }
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error deleting website portfolio item:', error);
    return NextResponse.json(
      { error: 'Failed to delete website portfolio item' },
      { status: 500 }
    );
  }
}
