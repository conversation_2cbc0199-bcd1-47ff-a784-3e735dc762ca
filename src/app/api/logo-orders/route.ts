import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { sendEmail, generateOrderNotificationEmail } from '@/utils/email';

// Generate unique order number
function generateOrderNumber(): string {
  const prefix = 'LO';
  const timestamp = Date.now().toString().slice(-6);
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
  return `${prefix}${timestamp}${random}`;
}

// GET - Fetch all logo orders (Admin)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const limit = searchParams.get('limit');
    const offset = searchParams.get('offset');

    const whereClause: any = {};
    if (status) {
      whereClause.status = status;
    }

    const orders = await prisma.logoOrder.findMany({
      where: whereClause,
      include: {
        package: true
      },
      orderBy: { createdAt: 'desc' },
      take: limit ? parseInt(limit) : undefined,
      skip: offset ? parseInt(offset) : undefined
    });

    const totalCount = await prisma.logoOrder.count({ where: whereClause });

    return NextResponse.json({
      success: true,
      data: orders,
      pagination: {
        total: totalCount,
        limit: limit ? parseInt(limit) : totalCount,
        offset: offset ? parseInt(offset) : 0
      }
    });
  } catch (error) {
    console.error('Error fetching logo orders:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to fetch logo orders' },
      { status: 500 }
    );
  }
}

// POST - Create new logo order
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    const {
      packageId,
      customerName,
      email,
      phone,
      businessName,
      industry,
      logoType,
      slogan,
      additionalInfo,
      totalAmount
    } = body;

    // Validate required fields
    if (!packageId || !customerName || !email || !businessName || !totalAmount) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Verify package exists
    const packageExists = await prisma.logoPackage.findUnique({
      where: { id: packageId }
    });

    if (!packageExists) {
      return NextResponse.json(
        { success: false, error: 'Package not found' },
        { status: 404 }
      );
    }

    // Generate unique order number
    const orderNumber = generateOrderNumber();

    // Create the order with the package relationship
    const order = await prisma.logoOrder.create({
      data: {
        orderNumber,
        customerName,
        email,
        phone,
        businessName,
        industry,
        logoType,
        slogan,
        additionalInfo,
        totalAmount,
        status: 'pending',
        packageId
      },
      include: {
        package: true // Include the package details in the response
      }
    });

    // Only send email notification if this is not a pending order
    if (customerName !== 'Pending Customer Info' && email !== '<EMAIL>' && businessName !== 'Pending Business Info') {
      try {
        const emailResult = await sendEmail(generateOrderNotificationEmail(order));
        console.log('Email notification result:', emailResult);
        
        // Update order to mark notification as sent
        await prisma.logoOrder.update({
          where: { id: order.id },
          data: { adminNotified: true }
        });
      } catch (emailError) {
        console.error('Failed to send email notification:', emailError);
        // Don't fail the order creation if email fails
      }
    }

    return NextResponse.json({ 
      success: true, 
      order 
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating logo order:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to create order' },
      { status: 500 }
    );
  }
} 