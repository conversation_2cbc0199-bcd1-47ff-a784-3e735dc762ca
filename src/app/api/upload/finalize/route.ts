import { NextRequest, NextResponse } from 'next/server';
import { readFile, readdir, unlink, rmdir } from 'fs/promises';
import { join } from 'path';
import { uploadToS3 } from '@/utils/s3';
import { UploadSessionStore } from '@/services/uploadSessionStore';
import { auth } from "../../../../../auth";

interface FinalizeRequest {
  uploadId: string;
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const userSession = await auth();
    if (!userSession?.user?.id) {
      console.log(`[ChunkedUpload] Finalize upload authentication failed: no valid session`);
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const body: FinalizeRequest = await request.json();
    const { uploadId } = body;

    console.log(`[ChunkedUpload] Finalizing upload: ${uploadId} by user: ${userSession.user.email}`);

    // Validate request
    if (!uploadId) {
      return NextResponse.json(
        { error: 'Missing uploadId' },
        { status: 400 }
      );
    }

    // Get upload session
    const session = UploadSessionStore.get(uploadId);
    if (!session) {
      return NextResponse.json(
        { error: 'Upload session not found' },
        { status: 404 }
      );
    }

    // Check if all chunks are uploaded
    if (session.chunks.size !== session.totalChunks) {
      return NextResponse.json(
        { 
          error: `Upload incomplete: ${session.chunks.size}/${session.totalChunks} chunks received`,
          completedChunks: session.chunks.size,
          totalChunks: session.totalChunks,
          missingChunks: Array.from(
            { length: session.totalChunks }, 
            (_, i) => i
          ).filter(i => !session.chunks.has(i))
        },
        { status: 400 }
      );
    }

    try {
      console.log(`[ChunkedUpload] Combining ${session.totalChunks} chunks...`);
      
      // Read and combine all chunks in order
      const chunkBuffers: Buffer[] = [];
      let totalSize = 0;

      // Read chunks in order
      for (let i = 0; i < session.totalChunks; i++) {
        const chunkPath = join(session.tempDir, `chunk_${i.toString().padStart(6, '0')}`);
        
        try {
          const chunkBuffer = await readFile(chunkPath);
          chunkBuffers.push(chunkBuffer);
          totalSize += chunkBuffer.length;
          
          console.log(`[ChunkedUpload] Read chunk ${i + 1}/${session.totalChunks} (${Math.round(chunkBuffer.length/1024)}KB)`);
        } catch (error) {
          console.error(`[ChunkedUpload] Failed to read chunk ${i}:`, error);
          throw new Error(`Failed to read chunk ${i}: ${error}`);
        }
      }

      // Combine all chunks into a single buffer
      const combinedBuffer = Buffer.concat(chunkBuffers);
      console.log(`[ChunkedUpload] Combined file size: ${Math.round(combinedBuffer.length/1024/1024)}MB`);

      // Verify file size matches expected
      if (combinedBuffer.length !== session.fileSize) {
        console.warn(`[ChunkedUpload] Size mismatch: expected ${session.fileSize}, got ${combinedBuffer.length}`);
      }

      console.log(`[ChunkedUpload] Uploading to S3...`);
      
      // Generate S3 key
      const folder = session.metadata?.folder || 'uploads';
      const timestamp = Date.now();
      const s3Key = `${folder}/${timestamp}_${session.fileName}`;
      
      // Upload to S3 using existing upload service
      const uploadUrl = await uploadToS3(combinedBuffer, s3Key, session.fileType);

      console.log(`[ChunkedUpload] S3 upload successful:`, uploadUrl);

      // Cleanup temporary files
      await cleanupTempFiles(session.tempDir);

      // Remove session
      UploadSessionStore.delete(uploadId);

      return NextResponse.json({
        success: true,
        uploadId,
        url: uploadUrl,
        fileSize: combinedBuffer.length,
        originalSize: session.fileSize,
        fileName: session.fileName,
        fileType: session.fileType,
        chunksProcessed: session.totalChunks,
        metadata: session.metadata
      });

    } catch (error) {
      console.error(`[ChunkedUpload] Finalization failed:`, error);
      
      // Try to cleanup temp files even on error
      try {
        await cleanupTempFiles(session.tempDir);
      } catch (cleanupError) {
        console.error(`[ChunkedUpload] Cleanup failed:`, cleanupError);
      }

      return NextResponse.json(
        { error: `Failed to finalize upload: ${error}` },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('[ChunkedUpload] Finalize error:', error);
    return NextResponse.json(
      { error: 'Failed to process finalize request' },
      { status: 500 }
    );
  }
}

/**
 * Clean up temporary files and directory
 */
async function cleanupTempFiles(tempDir: string): Promise<void> {
  try {
    console.log(`[ChunkedUpload] Cleaning up temp directory: ${tempDir}`);
    
    // Read all files in temp directory
    const files = await readdir(tempDir);
    
    // Delete all chunk files
    for (const file of files) {
      const filePath = join(tempDir, file);
      try {
        await unlink(filePath);
        console.log(`[ChunkedUpload] Deleted temp file: ${file}`);
      } catch (error) {
        console.warn(`[ChunkedUpload] Failed to delete ${file}:`, error);
      }
    }
    
    // Remove empty directory
    try {
      await rmdir(tempDir);
      console.log(`[ChunkedUpload] Removed temp directory: ${tempDir}`);
    } catch (error) {
      console.warn(`[ChunkedUpload] Failed to remove temp directory:`, error);
    }
    
  } catch (error) {
    console.error(`[ChunkedUpload] Cleanup error:`, error);
    throw error;
  }
} 