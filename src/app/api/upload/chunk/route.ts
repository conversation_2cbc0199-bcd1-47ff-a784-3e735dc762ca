import { NextRequest, NextResponse } from 'next/server';
import { writeFile } from 'fs/promises';
import { join } from 'path';
import { UploadSessionStore } from '@/services/uploadSessionStore';
import { auth } from "../../../../../auth";

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const userSession = await auth();
    if (!userSession?.user?.id) {
      console.log(`[ChunkedUpload] Chunk upload authentication failed: no valid session`);
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const formData = await request.formData();
    
    const uploadId = formData.get('uploadId') as string;
    const chunkNumber = parseInt(formData.get('chunkNumber') as string);
    const totalChunks = parseInt(formData.get('totalChunks') as string);
    const chunkFile = formData.get('chunk') as File;

    console.log(`[ChunkedUpload] Receiving chunk ${chunkNumber + 1}/${totalChunks} for upload ${uploadId} by user: ${userSession.user.email}`);

    // Validate request
    if (!uploadId || chunkNumber === undefined || !chunkFile) {
      return NextResponse.json(
        { error: 'Missing required fields: uploadId, chunkNumber, chunk' },
        { status: 400 }
      );
    }

    // Get upload session
    const session = UploadSessionStore.get(uploadId);
    if (!session) {
      return NextResponse.json(
        { error: 'Upload session not found. Please initialize upload first.' },
        { status: 404 }
      );
    }

    // Check if chunk already uploaded
    if (session.chunks.has(chunkNumber)) {
      console.log(`[ChunkedUpload] Chunk ${chunkNumber} already exists, skipping`);
      return NextResponse.json({
        success: true,
        chunkNumber,
        alreadyExists: true,
        completedChunks: session.chunks.size,
        totalChunks: session.totalChunks
      });
    }

    // Validate chunk number
    if (chunkNumber < 0 || chunkNumber >= session.totalChunks) {
      return NextResponse.json(
        { error: `Invalid chunk number: ${chunkNumber}. Expected 0-${session.totalChunks - 1}` },
        { status: 400 }
      );
    }

    try {
      // Convert chunk to buffer
      const chunkBuffer = Buffer.from(await chunkFile.arrayBuffer());
      
      // Save chunk to temporary file
      const chunkPath = join(session.tempDir, `chunk_${chunkNumber.toString().padStart(6, '0')}`);
      await writeFile(chunkPath, chunkBuffer);

      // Mark chunk as completed
      session.chunks.add(chunkNumber);

      console.log(`[ChunkedUpload] Chunk ${chunkNumber + 1}/${session.totalChunks} saved (${Math.round(chunkBuffer.length/1024)}KB)`);
      console.log(`[ChunkedUpload] Progress: ${session.chunks.size}/${session.totalChunks} chunks (${Math.round((session.chunks.size / session.totalChunks) * 100)}%)`);

      return NextResponse.json({
        success: true,
        chunkNumber,
        chunkSize: chunkBuffer.length,
        completedChunks: session.chunks.size,
        totalChunks: session.totalChunks,
        progress: Math.round((session.chunks.size / session.totalChunks) * 100)
      });

    } catch (error) {
      console.error(`[ChunkedUpload] Failed to save chunk ${chunkNumber}:`, error);
      return NextResponse.json(
        { error: `Failed to save chunk ${chunkNumber}: ${error}` },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('[ChunkedUpload] Chunk upload error:', error);
    return NextResponse.json(
      { error: 'Failed to process chunk upload' },
      { status: 500 }
    );
  }
} 