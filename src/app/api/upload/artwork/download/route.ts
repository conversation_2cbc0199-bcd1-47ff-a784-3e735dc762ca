import { NextRequest, NextResponse } from 'next/server';
import { S3Client, GetObjectCommand } from '@aws-sdk/client-s3';
import { getDefaultStorageConfig } from '@/lib/storageConfig';
import { requireAdminAuth } from '@/lib/auth-helpers';
import path from 'path';

// Get S3 client with the current configuration
async function getS3Client() {
  const config = await getDefaultStorageConfig();
  if (!config) {
    throw new Error('No storage configuration found');
  }

  return {
    client: new S3Client({
      region: config.region,
      endpoint: config.endpoint,
      credentials: {
        accessKeyId: config.accessKey,
        secretAccessKey: config.secretKey,
      },
      forcePathStyle: true
    }),
    config
  };
}

export async function GET(request: NextRequest) {
  try {
    // Check authentication for admin access
    const { user, response: authResponse } = await requireAdminAuth(request);
    if (authResponse) return authResponse;
    
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const orderNumber = searchParams.get('orderNumber');
    const fileName = searchParams.get('fileName');

    if (!orderNumber || !fileName) {
      return NextResponse.json(
        { error: 'Order number and file name are required' },
        { status: 400 }
      );
    }

    const { client: s3Client, config: s3Config } = await getS3Client();

    // Create S3 key
    const s3Key = `artwork/${orderNumber}/${fileName}`;

    console.log(`🔽 [Download] Attempting to download file:`, {
      orderNumber,
      fileName,
      s3Key,
      bucketName: s3Config.bucketName
    });

    try {
      // Get object from S3
      const getObjectCommand = new GetObjectCommand({
        Bucket: s3Config.bucketName,
        Key: s3Key,
      });

      const s3Response = await s3Client.send(getObjectCommand);
      
      if (!s3Response.Body) {
        return NextResponse.json(
          { error: 'File not found' },
          { status: 404 }
        );
      }

      // Convert the stream to buffer
      const chunks = [];
      const stream = s3Response.Body as any;
      
      for await (const chunk of stream) {
        chunks.push(chunk);
      }
      
      const fileBuffer = Buffer.concat(chunks);
      const fileExtension = path.extname(fileName).toLowerCase();
      
      // Determine content type
      let contentType = s3Response.ContentType || 'application/octet-stream';
      if (!s3Response.ContentType) {
        switch (fileExtension) {
          case '.jpg':
          case '.jpeg':
            contentType = 'image/jpeg';
            break;
          case '.png':
            contentType = 'image/png';
            break;
          case '.gif':
            contentType = 'image/gif';
            break;
          case '.webp':
            contentType = 'image/webp';
            break;
          case '.avif':
            contentType = 'image/avif';
            break;
          case '.pdf':
            contentType = 'application/pdf';
            break;
          case '.svg':
            contentType = 'image/svg+xml';
            break;
          case '.ai':
            contentType = 'application/postscript';
            break;
          case '.eps':
            contentType = 'application/eps';
            break;
          case '.psd':
            contentType = 'image/vnd.adobe.photoshop';
            break;
        }
      }

      // Get original filename from the stored filename or metadata
      const originalName = s3Response.Metadata?.originalName || fileName.split('-').slice(2).join('-');

      // Create response with file
      const response = new NextResponse(fileBuffer);
      response.headers.set('Content-Type', contentType);
      response.headers.set('Content-Disposition', `attachment; filename="${originalName}"`);
      response.headers.set('Cache-Control', 'private, no-cache');

      console.log(`✅ [Download] Successfully downloaded artwork file: ${fileName} for order ${orderNumber}`);

      return response;

    } catch (s3Error: any) {
      console.error(`❌ [Download] S3 error for ${s3Key}:`, {
        name: s3Error.name,
        message: s3Error.message,
        code: s3Error.code,
        statusCode: s3Error.$metadata?.httpStatusCode
      });
      
      if (s3Error.name === 'NoSuchKey' || s3Error.$metadata?.httpStatusCode === 404) {
        return NextResponse.json(
          { error: 'File not found' },
          { status: 404 }
        );
      }
      throw s3Error;
    }

  } catch (error) {
    console.error('Error downloading artwork file:', error);
    return NextResponse.json(
      { error: 'Failed to download file' },
      { status: 500 }
    );
  }
} 