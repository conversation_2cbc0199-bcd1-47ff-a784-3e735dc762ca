import { NextRequest, NextResponse } from 'next/server';
import { uploadImageToS3 } from '@/utils/s3Utils';
import { S3Client, ListObjectsV2Command, PutObjectCommand } from '@aws-sdk/client-s3';
import { getDefaultStorageConfig } from '@/lib/storageConfig';
import path from 'path';

// Allowed file types for artwork
const ALLOWED_TYPES = [
  'image/jpeg',
  'image/jpg', 
  'image/png',
  'image/gif',
  'image/webp',
  'image/avif',
  'application/pdf',
  'image/svg+xml',
  'application/postscript', // .ai files
  'application/eps', // .eps files
  'image/vnd.adobe.photoshop' // .psd files
];

// Maximum file size (10MB)
const MAX_FILE_SIZE = 10 * 1024 * 1024;

// Get S3 client with the current configuration
async function getS3Client() {
  const config = await getDefaultStorageConfig();
  if (!config) {
    throw new Error('No storage configuration found');
  }

  return {
    client: new S3Client({
      region: config.region,
      endpoint: config.endpoint,
      credentials: {
        accessKeyId: config.accessKey,
        secretAccessKey: config.secretKey,
      },
      forcePathStyle: true
    }),
    config
  };
}

export async function POST(request: NextRequest) {
  try {
    console.log('🔄 [ArtworkUpload] Starting artwork upload request');
    
    const formData = await request.formData();
    const files = formData.getAll('files') as File[];
    const orderNumber = formData.get('orderNumber') as string;

    console.log(`📋 [ArtworkUpload] Request details:`, {
      filesCount: files.length,
      orderNumber,
      fileNames: files.map(f => f.name),
      fileSizes: files.map(f => f.size),
      fileTypes: files.map(f => f.type)
    });

    if (!files || files.length === 0) {
      console.log('❌ [ArtworkUpload] No files provided');
      return NextResponse.json(
        { error: 'No files provided' },
        { status: 400 }
      );
    }

    if (!orderNumber) {
      console.log('❌ [ArtworkUpload] No order number provided');
      return NextResponse.json(
        { error: 'Order number is required' },
        { status: 400 }
      );
    }

    const uploadedFiles = [];
    const errors = [];

    for (const file of files) {
      try {
        console.log(`🔄 [ArtworkUpload] Processing file: ${file.name}`);
        
        // Validate file type
        if (!ALLOWED_TYPES.includes(file.type)) {
          const errorMsg = `${file.name}: Unsupported file type. Allowed: JPG, PNG, PDF, AI, EPS, PSD, SVG`;
          console.log(`❌ [ArtworkUpload] ${errorMsg}`);
          errors.push(errorMsg);
          continue;
        }

        // Validate file size
        if (file.size > MAX_FILE_SIZE) {
          const errorMsg = `${file.name}: File too large. Maximum size is 10MB`;
          console.log(`❌ [ArtworkUpload] ${errorMsg}`);
          errors.push(errorMsg);
          continue;
        }

        // Generate unique filename
        const timestamp = Date.now();
        const randomString = Math.random().toString(36).substring(2, 8);
        const fileExtension = path.extname(file.name);
        const safeFileName = file.name.replace(/[^a-zA-Z0-9.-]/g, '_');
        const uniqueFileName = `${timestamp}-${randomString}-${safeFileName}`;

        // Create S3 key using the same pattern as portfolio
        const s3Key = `artwork/${orderNumber}/${uniqueFileName}`;

        console.log(`🔄 [ArtworkUpload] Uploading to S3: ${file.name} → ${s3Key}`);

        // The uploadImageToS3 function has restrictive file type validation
        // We need to handle non-image files differently
        let uploadResult;
        
        if (file.type.startsWith('image/')) {
          // For image files, use the standard uploadImageToS3 function
          uploadResult = await uploadImageToS3(file, s3Key, {
            maxRetries: 5,
            timeoutMs: 300000, // 5 minutes
            logPrefix: '[ArtworkUpload]'
          });
        } else {
          // For non-image files (PDF, AI, EPS, PSD), use direct S3 upload
          console.log(`🔄 [ArtworkUpload] Non-image file detected, using direct S3 upload for: ${file.name}`);
          
          try {
            const config = await getDefaultStorageConfig();
            if (!config) {
              throw new Error('No storage configuration found');
            }

            const s3Client = new S3Client({
              region: config.region,
              endpoint: config.endpoint,
              credentials: {
                accessKeyId: config.accessKey,
                secretAccessKey: config.secretKey,
              },
              forcePathStyle: true
            });

            // Convert file to buffer
            const bytes = await file.arrayBuffer();
            const buffer = Buffer.from(bytes);

            // Upload to S3
            const uploadCommand = new PutObjectCommand({
              Bucket: config.bucketName,
              Key: s3Key,
              Body: buffer,
              ContentType: file.type,
              ACL: 'public-read',
              Metadata: {
                originalName: file.name,
                orderNumber: orderNumber,
                uploadedAt: new Date().toISOString()
              }
            });

            await s3Client.send(uploadCommand);

            // Generate public URL
            let endpoint = config.endpoint;
            if (!endpoint.startsWith('http')) {
              endpoint = `https://${endpoint}`;
            }
            endpoint = endpoint.replace(/\/+$/, '');
            const url = `${endpoint}/${config.bucketName}/${s3Key}`;

            uploadResult = {
              success: true,
              url: url,
              warnings: []
            };

            console.log(`✅ [ArtworkUpload] Direct S3 upload successful for: ${file.name}`);
          } catch (directUploadError) {
            console.error(`❌ [ArtworkUpload] Direct S3 upload failed:`, directUploadError);
            uploadResult = {
              success: false,
              url: '',
              error: directUploadError instanceof Error ? directUploadError.message : 'Direct upload failed'
            };
          }
        }

        console.log(`📦 [ArtworkUpload] S3 upload result for ${file.name}:`, {
          success: uploadResult.success,
          url: uploadResult.url,
          error: uploadResult.error,
          warnings: uploadResult.warnings
        });

        if (!uploadResult.success) {
          const errorMsg = `${file.name}: ${uploadResult.error || 'Upload failed'}`;
          console.log(`❌ [ArtworkUpload] ${errorMsg}`);
          errors.push(errorMsg);
          continue;
        }

        // Create file metadata
        const fileMetadata = {
          originalName: file.name,
          fileName: uniqueFileName,
          filePath: uploadResult.url, // S3 URL from uploadImageToS3
          s3Key: s3Key,
          fileSize: file.size,
          fileType: file.type,
          uploadedAt: new Date().toISOString()
        };

        uploadedFiles.push(fileMetadata);

        console.log(`✅ [ArtworkUpload] Successfully uploaded: ${file.name} → ${s3Key}`);

      } catch (fileError) {
        const errorMsg = `${file.name}: Upload failed - ${fileError instanceof Error ? fileError.message : 'Unknown error'}`;
        console.error(`❌ [ArtworkUpload] Exception:`, fileError);
        errors.push(errorMsg);
      }
    }

    const result = {
      success: true,
      uploadedFiles,
      errors: errors.length > 0 ? errors : undefined,
      message: `Successfully uploaded ${uploadedFiles.length} file(s) to cloud storage${errors.length > 0 ? ` with ${errors.length} error(s)` : ''}`
    };

    console.log(`🎯 [ArtworkUpload] Final result:`, {
      success: result.success,
      uploadedFilesCount: uploadedFiles.length,
      errorsCount: errors.length,
      uploadedFileNames: uploadedFiles.map(f => f.fileName)
    });

    return NextResponse.json(result);

  } catch (error) {
    console.error('❌ [ArtworkUpload] Fatal error:', error);
    return NextResponse.json(
      { error: 'Failed to upload artwork files' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const orderNumber = searchParams.get('orderNumber');

    console.log(`🔍 [ArtworkAPI] GET request for orderNumber: ${orderNumber}`);

    if (!orderNumber) {
      return NextResponse.json(
        { error: 'Order number is required' },
        { status: 400 }
      );
    }

    const { client: s3Client, config: s3Config } = await getS3Client();

    console.log(`🔍 [ArtworkAPI] Searching S3 bucket: ${s3Config.bucketName} with prefix: artwork/${orderNumber}/`);

    // List objects in S3 with the order number prefix
    const listCommand = new ListObjectsV2Command({
      Bucket: s3Config.bucketName,
      Prefix: `artwork/${orderNumber}/`
    });

    const response = await s3Client.send(listCommand);
    
    console.log(`📋 [ArtworkAPI] S3 ListObjects response:`, {
      isTruncated: response.IsTruncated,
      keyCount: response.KeyCount,
      contents: response.Contents?.map(obj => ({ key: obj.Key, size: obj.Size }))
    });
    
    const fileList = (response.Contents || []).map((object) => {
      const fileName = path.basename(object.Key || '');
      const publicUrl = `${s3Config.endpoint}/${s3Config.bucketName}/${object.Key}`;
      
      return {
        fileName,
        originalName: fileName.split('-').slice(2).join('-'), // Remove timestamp and random string
        filePath: publicUrl,
        s3Key: object.Key,
        fileSize: object.Size || 0,
        uploadedAt: object.LastModified?.toISOString() || new Date().toISOString(),
        fileType: getFileTypeFromExtension(fileName)
      };
    });

    console.log(`📁 [ArtworkAPI] Processed file list for ${orderNumber}:`, fileList.map(f => ({ 
      fileName: f.fileName, 
      originalName: f.originalName,
      s3Key: f.s3Key 
    })));

    // If no files found in S3, also check if database has artwork file info and search all S3 files
    if (fileList.length === 0) {
      console.log(`🔍 [ArtworkAPI] No files found in S3 for ${orderNumber}, checking database and searching all files...`);
      
      try {
        // Import prisma here to avoid circular dependency issues
        const { PrismaClient } = require('@prisma/client');
        const prisma = new PrismaClient();
        
        const order = await prisma.order.findUnique({
          where: { orderNumber },
          select: { artworkFiles: true }
        });
        
        if (order && order.artworkFiles.length > 0) {
          console.log(`📋 [ArtworkAPI] Database shows ${order.artworkFiles.length} artwork files:`, order.artworkFiles);
          
          // Try to find these files anywhere in S3 (including TEMP directories)
          console.log(`🔍 [ArtworkAPI] Searching all S3 files for missing artwork...`);
          
          const searchAllCommand = new ListObjectsV2Command({
            Bucket: s3Config.bucketName,
            Prefix: 'artwork/',
            MaxKeys: 1000
          });
          
          const allFilesResponse = await s3Client.send(searchAllCommand);
          console.log(`📋 [ArtworkAPI] Found ${allFilesResponse.Contents?.length || 0} total files in artwork/ prefix`);
          
          // Also search TEMP directories specifically
          console.log(`🔍 [ArtworkAPI] Also searching TEMP directories...`);
          const tempSearchCommand = new ListObjectsV2Command({
            Bucket: s3Config.bucketName,
            Prefix: 'artwork/TEMP-',
            MaxKeys: 1000
          });
          
          const tempFilesResponse = await s3Client.send(tempSearchCommand);
          console.log(`📋 [ArtworkAPI] Found ${tempFilesResponse.Contents?.length || 0} files in TEMP directories`);
          
          // Combine all search results
          const allSearchResults = [
            ...(allFilesResponse.Contents || []),
            ...(tempFilesResponse.Contents || [])
          ];
          
          // Try to match database files with S3 files
          const foundFiles = [];
          for (const dbFileName of order.artworkFiles) {
            console.log(`🔍 [ArtworkAPI] Searching for database file: ${dbFileName}`);
            
            const matchingS3File = allSearchResults.find(obj => {
              if (!obj.Key) return false;
              
              // Strategy 1: Exact filename match (end of path)
              if (obj.Key.endsWith(dbFileName)) {
                console.log(`✅ [ArtworkAPI] Exact match: ${obj.Key}`);
                return true;
              }
              
              // Strategy 2: Contains filename
              if (obj.Key.includes(dbFileName)) {
                console.log(`✅ [ArtworkAPI] Contains match: ${obj.Key}`);
                return true;
              }
              
              // Strategy 3: Extract the unique part after timestamp-random
              // Files are named like: 1750422266282-5bcejg-3.jpg
              // We need to match the "3.jpg" part
              const parts = dbFileName.split('-');
              if (parts.length >= 3) {
                const uniquePart = parts.slice(2).join('-'); // Get "3.jpg" part
                if (obj.Key.includes(uniquePart)) {
                  console.log(`✅ [ArtworkAPI] Unique part match: ${obj.Key} (${uniquePart})`);
                  return true;
                }
              }
              
              // Strategy 4: Match by timestamp and random string (for files missing extensions)
              if (parts.length >= 2) {
                const timestampPart = parts[0]; // timestamp
                const randomPart = parts[1]; // random string
                if (obj.Key.includes(timestampPart) && obj.Key.includes(randomPart)) {
                  console.log(`✅ [ArtworkAPI] Timestamp+random match: ${obj.Key} (${timestampPart}-${randomPart})`);
                  return true;
                }
              }
              
              // Strategy 5: Check if database filename is contained in S3 key
              if (dbFileName.includes(path.basename(obj.Key))) {
                console.log(`✅ [ArtworkAPI] Reverse contains match: ${obj.Key}`);
                return true;
              }
              
              return false;
            });
            
            if (matchingS3File) {
              console.log(`✅ [ArtworkAPI] Found missing file in S3: ${dbFileName} → ${matchingS3File.Key}`);
              
              const fileName = path.basename(matchingS3File.Key || '');
              const publicUrl = `${s3Config.endpoint}/${s3Config.bucketName}/${matchingS3File.Key}`;
              
              // Try to extract original name from database filename or S3 filename
              let originalName = fileName;
              
              // If the database filename has the pattern timestamp-random-originalname, extract it
              const dbParts = dbFileName.split('-');
              if (dbParts.length >= 3) {
                originalName = dbParts.slice(2).join('-');
              } else {
                // Fallback to S3 filename parsing
                const s3Parts = fileName.split('-');
                if (s3Parts.length >= 3) {
                  originalName = s3Parts.slice(2).join('-');
                } else if (s3Parts.length === 2) {
                  // Handle cases where extension was lost - add .jpg extension
                  originalName = s3Parts[1] + '.jpg';
                }
              }
              
              // If originalName is still just an extension, try to construct a better name
              if (originalName.length <= 4) { // .jpg, .png, etc.
                originalName = dbFileName.includes('.') ? dbFileName.split('/').pop() : dbFileName + '.jpg';
              }
              
              foundFiles.push({
                fileName,
                originalName,
                filePath: publicUrl,
                s3Key: matchingS3File.Key,
                fileSize: matchingS3File.Size || 0,
                uploadedAt: matchingS3File.LastModified?.toISOString() || new Date().toISOString(),
                fileType: getFileTypeFromExtension(originalName)
              });
            } else {
              console.log(`❌ [ArtworkAPI] Could not find file in S3: ${dbFileName}`);
            }
          }
          
          if (foundFiles.length > 0) {
            console.log(`🎯 [ArtworkAPI] Returning ${foundFiles.length} found files for ${orderNumber}`);
            await prisma.$disconnect();
            return NextResponse.json({
              success: true,
              files: foundFiles,
              count: foundFiles.length,
              note: `Files found through comprehensive search (${foundFiles.length}/${order.artworkFiles.length})`
            });
          }
        } else {
          console.log(`📋 [ArtworkAPI] Database shows no artwork files for ${orderNumber}`);
        }
        
        await prisma.$disconnect();
      } catch (dbError) {
        console.error(`❌ [ArtworkAPI] Database check failed:`, dbError);
      }
    }

    return NextResponse.json({
      success: true,
      files: fileList,
      count: fileList.length
    });

  } catch (error) {
    console.error('Error fetching artwork files:', error);
    return NextResponse.json(
      { error: 'Failed to fetch artwork files' },
      { status: 500 }
    );
  }
}

function getFileTypeFromExtension(fileName: string): string {
  const ext = path.extname(fileName).toLowerCase();
  switch (ext) {
    case '.jpg':
    case '.jpeg':
      return 'image/jpeg';
    case '.png':
      return 'image/png';
    case '.gif':
      return 'image/gif';
    case '.webp':
      return 'image/webp';
    case '.avif':
      return 'image/avif';
    case '.pdf':
      return 'application/pdf';
    case '.svg':
      return 'image/svg+xml';
    case '.ai':
      return 'application/postscript';
    case '.eps':
      return 'application/eps';
    case '.psd':
      return 'image/vnd.adobe.photoshop';
    default:
      return 'application/octet-stream';
  }
} 