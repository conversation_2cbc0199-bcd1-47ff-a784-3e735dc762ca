import { NextRequest, NextResponse } from 'next/server';
import { v4 as uuidv4 } from 'uuid';
import { mkdir } from 'fs/promises';
import { join } from 'path';
import { tmpdir } from 'os';
import { UploadSessionStore, UploadSession } from '@/services/uploadSessionStore';
import { auth } from "../../../../../auth";

interface InitUploadRequest {
  uploadId: string;
  fileName: string;
  fileSize: number;
  fileType: string;
  metadata?: Record<string, any>;
}

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const userSession = await auth();
    if (!userSession?.user?.id) {
      console.log(`[ChunkedUpload] Authentication failed: no valid session`);
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const body: InitUploadRequest = await request.json();
    const { uploadId, fileName, fileSize, fileType, metadata } = body;

    console.log(`[ChunkedUpload] Initializing upload session: ${uploadId}`);
    console.log(`[ChunkedUpload] File: ${fileName} (${Math.round(fileSize/1024/1024)}MB) by user: ${userSession.user.email}`);

    // Validate request
    if (!uploadId || !fileName || !fileSize) {
      return NextResponse.json(
        { error: 'Missing required fields: uploadId, fileName, fileSize' },
        { status: 400 }
      );
    }

    // Check if session already exists
    if (UploadSessionStore.has(uploadId)) {
      console.log(`[ChunkedUpload] Resume existing session: ${uploadId}`);
      const session = UploadSessionStore.get(uploadId)!;
      
      return NextResponse.json({
        success: true,
        uploadId,
        resuming: true,
        completedChunks: Array.from(session.chunks),
        totalChunks: session.totalChunks
      });
    }

    // Create temporary directory for chunks
    const tempDir = join(tmpdir(), 'mocky-uploads', uploadId);
    try {
      await mkdir(tempDir, { recursive: true });
    } catch (error) {
      console.error(`[ChunkedUpload] Failed to create temp directory: ${error}`);
      return NextResponse.json(
        { error: 'Failed to create upload directory' },
        { status: 500 }
      );
    }

    // Calculate expected number of chunks (assuming 5MB chunk size)
    const chunkSize = 5 * 1024 * 1024; // 5MB
    const totalChunks = Math.ceil(fileSize / chunkSize);

    // Create upload session
    const session: UploadSession = {
      uploadId,
      fileName,
      fileSize,
      fileType,
      metadata,
      chunks: new Set<number>(),
      totalChunks,
      tempDir,
      createdAt: Date.now()
    };

    UploadSessionStore.set(uploadId, session);

    console.log(`[ChunkedUpload] Session created: ${totalChunks} chunks expected`);

    return NextResponse.json({
      success: true,
      uploadId,
      totalChunks,
      chunkSize,
      tempDir: tempDir // For debugging, remove in production
    });

  } catch (error) {
    console.error('[ChunkedUpload] Init error:', error);
    return NextResponse.json(
      { error: 'Failed to initialize upload' },
      { status: 500 }
    );
  }
}

// Cleanup old sessions (run periodically)
export async function cleanupOldSessions() {
  const now = Date.now();
  const maxAge = 24 * 60 * 60 * 1000; // 24 hours

  for (const [uploadId, session] of UploadSessionStore.entries()) {
    if (now - session.createdAt > maxAge) {
      console.log(`[ChunkedUpload] Cleaning up old session: ${uploadId}`);
      UploadSessionStore.delete(uploadId);
      
      // Clean up temp files
      try {
        const fs = require('fs').promises;
        await fs.rmdir(session.tempDir, { recursive: true });
      } catch (error) {
        console.warn(`[ChunkedUpload] Failed to cleanup temp dir: ${error}`);
      }
    }
  }
}

// Export sessions for other endpoints to access
export { UploadSessionStore }; 