import { NextRequest, NextResponse } from 'next/server';
import { auth } from "../../../../auth";;
import { uploadImageToS3 } from '@/utils/s3Utils';
import { auditLogger } from '@/lib/audit-logger';
import crypto from 'crypto';

// Constants for upload configuration
const MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB
const MAX_FILES_PER_REQUEST = 20;
const MAX_TOTAL_SIZE = 200 * 1024 * 1024; // 200MB total
const UPLOAD_TIMEOUT = 600000; // 10 minutes (increased from 5 minutes)

const VALID_FILE_TYPES = [
  'image/jpeg',
  'image/jpg', 
  'image/png',
  'image/gif',
  'image/webp',
  'image/avif',
  'image/svg+xml'
];

const FILE_TYPE_CATEGORIES = {
  'image/jpeg': 'photo',
  'image/jpg': 'photo',
  'image/png': 'graphic',
  'image/gif': 'animation',
  'image/webp': 'photo',
  'image/avif': 'photo',
  'image/svg+xml': 'vector'
};

// Enhanced validation function
function validateUploadRequest(formData: FormData, userRole: string): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  files: File[];
  category: string;
} {
  const errors: string[] = [];
  const warnings: string[] = [];
  const files: File[] = [];
  const category = (formData.get('category') as string) || 'catalogue';

  // Extract all files
  for (const [key, value] of formData.entries()) {
    if (key.startsWith('file') && value instanceof File) {
      files.push(value);
    }
  }

  // Validate file count
  if (files.length === 0) {
    errors.push('At least one file is required');
  } else if (files.length > MAX_FILES_PER_REQUEST) {
    errors.push(`Maximum ${MAX_FILES_PER_REQUEST} files allowed per request`);
  }

  let totalSize = 0;
  files.forEach((file, index) => {
    // Individual file size validation
    if (file.size > MAX_FILE_SIZE) {
      errors.push(`File ${index + 1} (${file.name}) exceeds maximum size of ${MAX_FILE_SIZE / (1024 * 1024)}MB`);
    }

    // File type validation
    if (!VALID_FILE_TYPES.includes(file.type)) {
      errors.push(`File ${index + 1} (${file.name}) has invalid type: ${file.type}. Allowed types: ${VALID_FILE_TYPES.join(', ')}`);
    }

    // Warn about potentially problematic file types
    if (file.type === 'image/svg+xml' && file.size > 1024 * 1024) {
      warnings.push(`File ${index + 1} (${file.name}) is a large SVG file (${Math.round(file.size / 1024)}KB). Consider optimizing.`);
    }

    totalSize += file.size;
  });

  // Total size validation
  if (totalSize > MAX_TOTAL_SIZE) {
    errors.push(`Total file size (${Math.round(totalSize / (1024 * 1024))}MB) exceeds maximum allowed (${MAX_TOTAL_SIZE / (1024 * 1024)}MB)`);
  }

  // Category validation
  const validCategories = ['catalogue', 'portfolio', 'blog', 'team', 'general'];
  if (!validCategories.includes(category)) {
    warnings.push(`Invalid category "${category}". Using "catalogue" as default.`);
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    files,
    category: validCategories.includes(category) ? category : 'catalogue'
  };
}

export async function POST(request: NextRequest) {
  const requestId = crypto.randomUUID();
  const startTime = Date.now();
  
  console.log(`[Upload API] ${requestId} - Starting new upload request`);

  try {
    // Authentication check
    console.log(`[Upload API] ${requestId} - Checking authentication`);
    const session = await auth();
    
    if (!session?.user?.id) {
      console.log(`[Upload API] ${requestId} - Authentication failed: no valid session`);
      return NextResponse.json(
        { 
          error: 'Authentication required',
          code: 'AUTH_REQUIRED',
          message: 'Please log in to upload images'
        },
        { status: 401 }
      );
    }

    const user = session.user;
    console.log(`[Upload API] ${requestId} - Authenticated user: ${user.username} (${user.role})`);

    // Parse form data with timeout
    console.log(`[Upload API] ${requestId} - Parsing form data`);
    let formData: FormData;
    try {
      // Set up timeout for form parsing
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Form parsing timeout')), 30000); // 30 second timeout
      });
      
      const formDataPromise = request.formData();
      formData = await Promise.race([formDataPromise, timeoutPromise]);
    } catch (parseError) {
      console.error(`[Upload API] ${requestId} - Form parsing failed:`, parseError);
      return NextResponse.json(
        { 
          error: 'Failed to parse upload data',
          code: 'PARSE_ERROR',
          message: parseError instanceof Error ? parseError.message : 'Unknown parsing error'
        },
        { status: 400 }
      );
    }

    // Validate the upload request
    console.log(`[Upload API] ${requestId} - Validating upload request`);
    const validation = validateUploadRequest(formData, user.role);
    
    if (!validation.isValid) {
      console.error(`[Upload API] ${requestId} - Validation failed:`, validation.errors);
      return NextResponse.json(
        { 
          error: 'Upload validation failed',
          details: validation.errors,
          code: 'VALIDATION_ERROR'
        },
        { status: 400 }
      );
    }

    // Log warnings if any
    if (validation.warnings.length > 0) {
      console.warn(`[Upload API] ${requestId} - Warnings:`, validation.warnings);
    }

    console.log(`[Upload API] ${requestId} - Processing ${validation.files.length} files for category: ${validation.category}`);

    // Process uploads with enhanced error handling
    const uploadPromises = validation.files.map(async (file, index) => {
      const fileId = `${requestId}-${index}`;
      console.log(`[Upload API] ${fileId} - Starting upload: ${file.name} (${file.size} bytes)`);

      try {
        // Generate unique S3 key with better naming
        const timestamp = Date.now();
        const randomString = crypto.randomBytes(8).toString('hex');
        const fileExtension = file.name.split('.').pop()?.toLowerCase() || 'bin';
        const sanitizedName = file.name
          .replace(/[^a-zA-Z0-9.-]/g, '_')
          .replace(/_{2,}/g, '_')
          .replace(/^[._]+|[._]+$/g, '');
        
        // Use different path structure for catalogue images
        let s3Key: string;
        if (validation.category === 'catalogue') {
          s3Key = `images/catalogue/uploads/${timestamp}-${randomString}-${sanitizedName}`;
        } else {
          s3Key = `${validation.category}/${timestamp}-${randomString}-${sanitizedName}`;
        }

        // Upload to S3 with enhanced timeout and retries
        const uploadResult = await uploadImageToS3(file, s3Key, {
          maxRetries: 5,
          timeoutMs: UPLOAD_TIMEOUT,
          logPrefix: `[Upload API] ${fileId}`
        });

        if (!uploadResult.success) {
          throw new Error(uploadResult.error || 'S3 upload failed');
        }

        console.log(`[Upload API] ${fileId} - Upload successful: ${uploadResult.url}`);

        // Log successful upload
        await auditLogger.info(
          user.id,
          'image_upload',
          s3Key,
          {
            filename: file.name,
            size: file.size,
            type: file.type,
            category: validation.category
          },
          request
        );

        return {
          success: true,
          filename: file.name,
          originalName: file.name,
          size: file.size,
          type: file.type,
          category: FILE_TYPE_CATEGORIES[file.type as keyof typeof FILE_TYPE_CATEGORIES] || 'unknown',
          url: uploadResult.url,
          s3Key,
          warnings: uploadResult.warnings || [],
          uploadTime: Date.now() - startTime
        };

      } catch (uploadError) {
        console.error(`[Upload API] ${fileId} - Upload failed:`, uploadError);
        
        // Log failed upload
        await auditLogger.error(
          user.id,
          'image_upload_failed',
          file.name,
          {
            filename: file.name,
            error: uploadError instanceof Error ? uploadError.message : String(uploadError),
            category: validation.category
          },
          request
        );

        return {
          success: false,
          filename: file.name,
          error: uploadError instanceof Error ? uploadError.message : 'Upload failed',
          size: file.size,
          type: file.type,
          details: uploadError instanceof Error ? uploadError.stack : undefined
        };
      }
    });

    // Wait for all uploads to complete with timeout
    console.log(`[Upload API] ${requestId} - Waiting for ${uploadPromises.length} uploads to complete`);
    
    let uploadResults;
    try {
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Upload batch timeout')), UPLOAD_TIMEOUT + 60000); // Extra 1 minute buffer
      });
      
      uploadResults = await Promise.race([
        Promise.all(uploadPromises),
        timeoutPromise
      ]);
    } catch (timeoutError) {
      console.error(`[Upload API] ${requestId} - Batch upload timeout:`, timeoutError);
      return NextResponse.json(
        {
          success: false,
          error: 'Upload timeout',
          message: 'The upload process took too long and was cancelled. Please try uploading fewer or smaller files.',
          code: 'UPLOAD_TIMEOUT'
        },
        { status: 408 }
      );
    }
    
    // Separate successful and failed uploads
    const successful = uploadResults.filter(result => result.success);
    const failed = uploadResults.filter(result => !result.success);

    const totalTime = Date.now() - startTime;
    console.log(`[Upload API] ${requestId} - Completed in ${totalTime}ms. Success: ${successful.length}, Failed: ${failed.length}`);

    // Determine response based on results
    if (successful.length === 0) {
      // All uploads failed
      return NextResponse.json(
        {
          success: false,
          error: 'All file uploads failed',
          details: failed.map(f => ({ filename: f.filename, error: f.error })),
          code: 'ALL_UPLOADS_FAILED',
          suggestions: [
            'Check your internet connection',
            'Try uploading smaller files',
            'Contact support if the issue persists'
          ]
        },
        { status: 500 }
      );
    } else if (failed.length > 0) {
      // Partial success
      return NextResponse.json(
        {
          success: true,
          message: `${successful.length} of ${uploadResults.length} files uploaded successfully`,
          results: uploadResults,
          successful,
          failed,
          warnings: validation.warnings,
          stats: {
            totalFiles: uploadResults.length,
            successfulFiles: successful.length,
            failedFiles: failed.length,
            totalTime,
            totalSize: validation.files.reduce((sum, file) => sum + file.size, 0)
          },
          // For backward compatibility, return the first successful upload URL
          url: successful[0]?.url,
          filename: successful[0]?.filename
        },
        { status: 207 } // Multi-status
      );
    } else {
      // All uploads successful
      return NextResponse.json(
        {
          success: true,
          message: `Successfully uploaded ${successful.length} file${successful.length > 1 ? 's' : ''}`,
          results: successful,
          warnings: validation.warnings,
          stats: {
            totalFiles: successful.length,
            totalTime,
            totalSize: validation.files.reduce((sum, file) => sum + file.size, 0),
            averageUploadTime: totalTime / successful.length
          },
          // For backward compatibility, return the first upload URL
          url: successful[0]?.url,
          filename: successful[0]?.filename
        }
      );
    }

  } catch (error) {
    console.error(`[Upload API] ${requestId} - Unexpected error:`, error);
    
    // Log the error (skip if no user context)
    try {
      const session = await auth();
      if (session?.user?.id) {
        await auditLogger.error(
          session.user.id,
          'upload_error',
          requestId,
          {
            error: error instanceof Error ? error.message : String(error),
            requestId
          },
          request
        );
      }
    } catch (logError) {
      console.error(`[Upload API] ${requestId} - Failed to log error:`, logError);
    }

    return NextResponse.json(
      {
        success: false,
        error: 'An unexpected error occurred during upload',
        message: error instanceof Error ? error.message : 'Unknown error',
        code: 'UNEXPECTED_ERROR',
        requestId,
        suggestions: [
          'Try refreshing the page and uploading again',
          'Check if the files are valid image formats',
          'Contact support if the problem continues'
        ]
      },
      { status: 500 }
    );
  }
}

// Export configuration for Next.js
export const runtime = 'nodejs';
export const maxDuration = 600; // 10 minutes