'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeftIcon, CheckIcon, ClipboardDocumentIcon } from '@heroicons/react/24/outline';
import SmartPrice from '@/components/SmartPrice';

interface LogoPackage {
  id: string;
  name: string;
  price: number;
  description: string | null;
  features: string[];
  isActive: boolean;
  isPopular: boolean;
  sortOrder: number;
  whatsappMessage: string | null;
}

interface LogoOrderPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default function LogoOrderPage({ params }: LogoOrderPageProps) {
  const router = useRouter();
  const [packageData, setPackageData] = useState<LogoPackage | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [notification, setNotification] = useState<{
    type: 'success' | 'error';
    message: string;
  } | null>(null);
  const [copiedField, setCopiedField] = useState<string | null>(null);

  const [formData, setFormData] = useState({
    customerName: '',
    email: '',
    phone: '',
    businessName: '',
    industry: '',
    logoType: 'wordmark',
    slogan: '',
    additionalInfo: ''
  });

  useEffect(() => {
    const fetchPackage = async () => {
      try {
        const resolvedParams = await params;
        const response = await fetch(`/api/logo-packages/${resolvedParams.id}`);
        const result = await response.json();

        if (result.success) {
          setPackageData(result.data);
        } else {
          setError(result.error || 'Package not found');
        }
      } catch (err) {
        console.error('Error fetching package:', err);
        setError('Failed to load package details');
      } finally {
        setLoading(false);
      }
    };

    fetchPackage();
  }, [params]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!packageData) return;

    // Validate required fields
    if (!formData.customerName || !formData.email || !formData.businessName) {
      setNotification({
        type: 'error',
        message: 'Please fill in all required fields.'
      });
      return;
    }

    setIsSubmitting(true);
    try {
      const orderResponse = await fetch('/api/logo-orders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          packageId: packageData.id,
          customerName: formData.customerName,
          email: formData.email,
          phone: formData.phone,
          businessName: formData.businessName,
          industry: formData.industry,
          logoType: formData.logoType,
          slogan: formData.slogan,
          additionalInfo: formData.additionalInfo,
          totalAmount: packageData.price,
        }),
      });

      if (!orderResponse.ok) {
        throw new Error('Failed to create order');
      }

      const orderResult = await orderResponse.json();
      if (!orderResult.success) {
        throw new Error(orderResult.error || 'Failed to create order');
      }

      setNotification({
        type: 'success',
        message: 'Order submitted successfully! Redirecting to WhatsApp...'
      });

      // Generate WhatsApp message
      const whatsappMessage = generateWhatsAppMessage(packageData, formData, orderResult.order);
      
      // Redirect to WhatsApp after a short delay
      setTimeout(() => {
        window.open(`https://wa.me/254741590670?text=${encodeURIComponent(whatsappMessage)}`, '_blank');
        router.push('/logos?success=true');
      }, 2000);

    } catch (error) {
      console.error('Error submitting order:', error);
      setNotification({
        type: 'error',
        message: 'Failed to submit order. Please try again.'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const generateWhatsAppMessage = (pkg: LogoPackage, form: typeof formData, order: any) => {
    return `Hello Mocky Digital! 👋

I've just submitted a logo design order through your website:

*Order Details:*
Order Number: ${order.orderNumber}
Package: ${pkg.name}
Price: KSh ${pkg.price.toLocaleString()}

*Customer Information:*
Name: ${form.customerName}
Email: ${form.email}
${form.phone ? `Phone: ${form.phone}` : ''}

*Logo Design Brief:*
Business Name: ${form.businessName}
Industry/Niche: ${form.industry}
Logo Type: ${form.logoType}
${form.slogan ? `Slogan: ${form.slogan}` : ''}
${form.additionalInfo ? `Additional Information: ${form.additionalInfo}` : ''}

*Package Features:*
${pkg.features.map(feature => `• ${feature}`).join('\n')}

I'm ready to proceed with the M-PESA payment of KSh ${pkg.price.toLocaleString()}. Please confirm the payment details and project timeline. Thank you!`;
  };

  const copyToClipboard = async (text: string, fieldName: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedField(fieldName);
      setTimeout(() => setCopiedField(null), 2000);
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = text;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      setCopiedField(fieldName);
      setTimeout(() => setCopiedField(null), 2000);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 py-12">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto">
            <div className="animate-pulse space-y-4">
              <div className="h-8 bg-gray-200 rounded w-1/2" />
              <div className="h-4 bg-gray-200 rounded w-3/4" />
              <div className="space-y-3">
                <div className="h-4 bg-gray-200 rounded" />
                <div className="h-4 bg-gray-200 rounded" />
                <div className="h-4 bg-gray-200 rounded" />
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !packageData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Package Not Found</h1>
          <p className="text-gray-600 mb-6">The logo package you're looking for doesn't exist.</p>
          <button
            onClick={() => router.push('/logos')}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
          >
            <ArrowLeftIcon className="h-5 w-5 mr-2" />
            Back to Logo Packages
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Top Navigation Bar */}
      <div className="bg-white border-b border-gray-200">
        <div className="container mx-auto px-4 py-4">
          <button
            onClick={() => router.push('/logos')}
            className="inline-flex items-center text-sm text-gray-600 hover:text-orange-600 transition-colors"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Back to Logo Packages
          </button>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-3xl font-bold text-gray-900 mb-3">Complete Your Logo Order</h1>
            <p className="text-gray-600 max-w-xl mx-auto">
              Fill out the form below to get started with your custom logo design project.
            </p>
          </div>

          <div className="grid grid-cols-12 gap-8">
            {/* Main Form Content */}
            <div className="col-span-12 lg:col-span-8">
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Package Summary */}
                <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h2 className="text-xl font-semibold text-gray-900">{packageData.name}</h2>
                        {packageData.isPopular && (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                            Most Popular
                          </span>
                        )}
                      </div>
                      <p className="text-gray-600 text-sm mb-4">{packageData.description}</p>
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                        {packageData.features.map((feature, index) => (
                          <div key={index} className="flex items-center text-sm text-gray-700">
                            <CheckIcon className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                            {feature}
                          </div>
                        ))}
                      </div>
                    </div>
                    <div className="text-right ml-6">
                      <div className="text-2xl font-bold text-orange-600">
                        <SmartPrice kesAmount={packageData.price} />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Customer Information */}
                <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Customer Information</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Your Name <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        required
                        value={formData.customerName}
                        onChange={(e) => setFormData({ ...formData, customerName: e.target.value })}
                        className="w-full border border-gray-300 rounded-lg px-4 py-2.5 text-gray-900 placeholder-gray-400 focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-colors"
                        placeholder="Enter your full name"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Email Address <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="email"
                        required
                        value={formData.email}
                        onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                        className="w-full border border-gray-300 rounded-lg px-4 py-2.5 text-gray-900 placeholder-gray-400 focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-colors"
                        placeholder="<EMAIL>"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Phone Number (Optional)
                      </label>
                      <input
                        type="tel"
                        value={formData.phone}
                        onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                        className="w-full border border-gray-300 rounded-lg px-4 py-2.5 text-gray-900 placeholder-gray-400 focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-colors"
                        placeholder="+254 700 000 000"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Business Name <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        required
                        value={formData.businessName}
                        onChange={(e) => setFormData({ ...formData, businessName: e.target.value })}
                        className="w-full border border-gray-300 rounded-lg px-4 py-2.5 text-gray-900 placeholder-gray-400 focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-colors"
                        placeholder="Enter your business or brand name"
                      />
                    </div>
                  </div>
                </div>

                {/* Logo Design Brief */}
                <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Logo Design Brief</h3>
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Industry/Niche (Optional)
                        </label>
                        <input
                          type="text"
                          value={formData.industry}
                          onChange={(e) => setFormData({ ...formData, industry: e.target.value })}
                          className="w-full border border-gray-300 rounded-lg px-4 py-2.5 text-gray-900 placeholder-gray-400 focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-colors"
                          placeholder="e.g. Restaurant, Technology, Fashion"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Logo Type <span className="text-red-500">*</span>
                        </label>
                        <select
                          required
                          value={formData.logoType}
                          onChange={(e) => setFormData({ ...formData, logoType: e.target.value })}
                          className="w-full border border-gray-300 rounded-lg px-4 py-2.5 text-gray-900 focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-colors"
                        >
                          <option value="wordmark">Wordmark (Text-based logo like Coca-Cola)</option>
                          <option value="lettermark">Lettermark (Initials like IBM, HBO)</option>
                          <option value="symbol">Symbol (Icon-based like Apple, Nike)</option>
                          <option value="combination">Combination (Text + Symbol like Amazon)</option>
                          <option value="emblem">Emblem (Text inside symbol like Starbucks)</option>
                        </select>
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Slogan/Tagline (Optional)
                      </label>
                      <input
                        type="text"
                        value={formData.slogan}
                        onChange={(e) => setFormData({ ...formData, slogan: e.target.value })}
                        className="w-full border border-gray-300 rounded-lg px-4 py-2.5 text-gray-900 placeholder-gray-400 focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-colors"
                        placeholder="Your business slogan or tagline if you have one"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        Additional Information (Optional)
                      </label>
                      <textarea
                        value={formData.additionalInfo}
                        onChange={(e) => setFormData({ ...formData, additionalInfo: e.target.value })}
                        className="w-full border border-gray-300 rounded-lg px-4 py-3 text-gray-900 placeholder-gray-400 focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-colors"
                        placeholder="Tell us about your brand personality, color preferences, style preferences, or any specific requirements..."
                        rows={4}
                      />
                    </div>
                  </div>
                </div>
              </form>
            </div>

            {/* Order Summary Sidebar */}
            <div className="col-span-12 lg:col-span-4">
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 sticky top-8">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Order Summary</h3>
                <div className="space-y-3">
                  <div className="flex justify-between py-2 border-b border-gray-100">
                    <span className="text-gray-600">Package</span>
                    <span className="font-medium text-gray-900">{packageData.name}</span>
                  </div>
                  <div className="flex justify-between py-2 border-b border-gray-100">
                    <span className="text-gray-600">Price</span>
                    <span className="font-medium text-gray-900">
                      <SmartPrice kesAmount={packageData.price} />
                    </span>
                  </div>
                  <div className="flex justify-between py-3 text-lg">
                    <span className="font-semibold text-gray-900">Total</span>
                    <span className="font-bold text-orange-600">
                      <SmartPrice kesAmount={packageData.price} />
                    </span>
                  </div>

                  <button
                    onClick={handleSubmit}
                    disabled={isSubmitting}
                    className={`w-full mt-6 py-3 px-4 rounded-lg text-white font-medium transition-all duration-200 ${
                      isSubmitting
                        ? 'bg-gray-400 cursor-not-allowed'
                        : 'bg-orange-600 hover:bg-orange-700 shadow-lg hover:shadow-xl'
                    }`}
                  >
                    {isSubmitting ? (
                      <span className="flex items-center justify-center">
                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Processing...
                      </span>
                    ) : 'Submit Order'}
                  </button>

                  <div className="mt-4 text-center">
                    <p className="text-sm text-gray-500">
                      By submitting, you'll be redirected to WhatsApp to complete your order
                    </p>
                  </div>
                </div>

                {/* Payment Information */}
                <div className="mt-6 p-4 bg-gray-50 rounded-lg border border-gray-200">
                  <h4 className="font-semibold text-gray-900 mb-3">M-PESA Payment Details</h4>
                  <div className="space-y-3 text-sm">
                    <div className="flex items-center justify-between p-2 bg-white rounded border">
                      <div className="flex flex-col">
                        <span className="text-gray-600 text-xs">Business Number</span>
                        <span className="font-medium text-gray-900">522533</span>
                      </div>
                      <button
                        onClick={() => copyToClipboard('522533', 'business')}
                        className="flex items-center gap-1 px-2 py-1 text-xs text-gray-600 hover:text-orange-600 hover:bg-orange-50 rounded transition-colors"
                        title="Copy business number"
                      >
                        <ClipboardDocumentIcon className="w-4 h-4" />
                        {copiedField === 'business' ? 'Copied!' : 'Copy'}
                      </button>
                    </div>
                    <div className="flex items-center justify-between p-2 bg-white rounded border">
                      <div className="flex flex-col">
                        <span className="text-gray-600 text-xs">Account Number</span>
                        <span className="font-medium text-gray-900">7934479</span>
                      </div>
                      <button
                        onClick={() => copyToClipboard('7934479', 'account')}
                        className="flex items-center gap-1 px-2 py-1 text-xs text-gray-600 hover:text-orange-600 hover:bg-orange-50 rounded transition-colors"
                        title="Copy account number"
                      >
                        <ClipboardDocumentIcon className="w-4 h-4" />
                        {copiedField === 'account' ? 'Copied!' : 'Copy'}
                      </button>
                    </div>
                  </div>
                  <div className="mt-3 p-2 bg-blue-50 rounded text-xs text-blue-700">
                    <strong>Instructions:</strong> Send payment to the business number above using your account number as the reference.
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Notification */}
      {notification && (
        <div
          className={`fixed bottom-4 right-4 z-50 p-4 rounded-lg shadow-lg transform transition-all duration-300 ${
            notification.type === 'success'
              ? 'bg-green-500 text-white'
              : 'bg-red-500 text-white'
          }`}
        >
          {notification.message}
        </div>
      )}
    </div>
  );
}
