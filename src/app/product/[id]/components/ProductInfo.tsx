import { ShareIcon, TagIcon, StarIcon } from '@heroicons/react/24/outline';

interface Product {
  service: string;
  description?: string;  // Make description optional
  category?: string;
  tags?: string[];
  popular?: boolean;
  features?: string[];
}

interface ProductInfoProps {
  product: Product;
  onShare: () => void;
  loading?: boolean;
}

/**
 * Product information display component
 * Shows title, description, category and share functionality
 */
export default function ProductInfo({ product, onShare, loading = false }: ProductInfoProps) {
  if (loading) {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-start">
          <div className="flex-1 space-y-3">
            <div className="h-8 bg-gray-200 rounded animate-pulse" />
            <div className="h-4 bg-gray-200 rounded w-24 animate-pulse" />
          </div>
          <div className="w-10 h-10 bg-gray-200 rounded-full animate-pulse" />
        </div>
        <div className="space-y-2">
          <div className="h-4 bg-gray-200 rounded animate-pulse" />
          <div className="h-4 bg-gray-200 rounded animate-pulse" />
          <div className="h-4 bg-gray-200 rounded w-3/4 animate-pulse" />
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Enhanced Title Section */}
      <div className="space-y-4">
        <div className="flex justify-between items-start gap-4">
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-3">
              {product.popular && (
                <span className="inline-flex items-center px-3 py-1 bg-gradient-to-r from-orange-100 to-red-100 text-orange-800 text-sm font-semibold rounded-full border border-orange-200">
                  <StarIcon className="w-4 h-4 mr-1" />
                  Popular Choice
                </span>
              )}
              {product.category && (
                <span className="inline-flex items-center px-3 py-1 bg-gray-100 text-gray-700 text-sm font-medium rounded-full">
                  <TagIcon className="w-4 h-4 mr-1" />
                  {product.category}
                </span>
              )}
            </div>

            <h1 className="text-4xl lg:text-5xl font-black text-gray-900 mb-4 leading-tight">
              {product.service}
            </h1>
          </div>

          <button
            onClick={onShare}
            className="p-3 bg-white hover:bg-gray-50 border border-gray-200 hover:border-gray-300 rounded-xl shadow-sm hover:shadow-md transition-all duration-200 group"
            aria-label="Share product"
          >
            <ShareIcon className="w-5 h-5 text-gray-600 group-hover:text-orange-600" />
          </button>
        </div>
      </div>

      {/* Enhanced Description Section */}
      {product.description && (
        <div className="bg-white rounded-2xl p-6 lg:p-8 shadow-sm border border-gray-100">
          <div className="space-y-4">
            <h2 className="text-xl font-bold text-gray-900 flex items-center">
              <div className="w-1 h-6 bg-gradient-to-b from-orange-500 to-red-500 rounded-full mr-3"></div>
              About This Service
            </h2>
            <div className="prose prose-gray max-w-none">
              <p className="text-gray-700 leading-relaxed text-lg">
                {product.description}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Enhanced Features Section */}
      {product.features && product.features.length > 0 && (
        <div className="bg-gradient-to-br from-orange-50 to-red-50 rounded-2xl p-6 lg:p-8 border border-orange-100">
          <div className="space-y-4">
            <h3 className="text-xl font-bold text-gray-900 flex items-center">
              <div className="w-1 h-6 bg-gradient-to-b from-orange-500 to-red-500 rounded-full mr-3"></div>
              What's Included
            </h3>
            <ul className="grid grid-cols-1 gap-3">
              {product.features.map((feature, index) => (
                <li key={index} className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center mt-0.5">
                    <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                  <span className="text-gray-700 font-medium leading-relaxed">{feature}</span>
                </li>
              ))}
            </ul>
          </div>
        </div>
      )}

      {/* Enhanced Tags Section */}
      {product.tags && product.tags.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-bold text-gray-900 flex items-center">
            <div className="w-1 h-5 bg-gradient-to-b from-orange-500 to-red-500 rounded-full mr-3"></div>
            Related Tags
          </h3>
          <div className="flex flex-wrap gap-3">
            {product.tags.map((tag, index) => (
              <span
                key={index}
                className="px-4 py-2 bg-white hover:bg-gray-50 border border-gray-200 hover:border-orange-300 text-gray-700 hover:text-orange-700 text-sm font-medium rounded-xl transition-all duration-200 cursor-pointer shadow-sm hover:shadow-md"
              >
                #{tag}
              </span>
            ))}
          </div>
        </div>
      )}
    </div>
  );
} 