import { useState, useEffect, useRef } from 'react';
import { use } from 'react';
import type { PricingItem } from '@/utils/pricing';

export interface UseProductDataResult {
  product: PricingItem | null;
  relatedProducts: PricingItem[];
  loading: boolean;
  error: string | null;
  productId: string;
}

/**
 * Custom hook to handle product data fetching
 * Extracted from product page for better separation of concerns
 */
export function useProductData(params: Promise<{ id: string }>): UseProductDataResult {
  const [product, setProduct] = useState<PricingItem | null>(null);
  const [relatedProducts, setRelatedProducts] = useState<PricingItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [productId, setProductId] = useState<string>('');

  // Unwrap the params Promise using React.use()
  const unwrappedParams = use(params);

  // Use a ref to track the latest request to prevent race conditions
  const latestRequestId = useRef<string>('');

  useEffect(() => {
    let isMounted = true;

    const fetchProduct = async () => {
      try {
        const id = unwrappedParams.id;
        
        // Store this request's ID
        latestRequestId.current = id;
        
        // Only proceed if component is still mounted
        if (!isMounted) return;
        
        setLoading(true);
        setProductId(id);

        const response = await fetch(`/api/catalogue/${id}`);
        
        // Check if this is still the latest request
        if (!isMounted || latestRequestId.current !== id) return;
        
        if (!response.ok) {
          throw new Error('Product not found');
        }

        const data = await response.json();
        
        // Check again before setting state
        if (!isMounted || latestRequestId.current !== id) return;
        
        setProduct(data);

        // Fetch related products if category exists
        if (data.category) {
          const relatedResponse = await fetch(`/api/catalogue?category=${data.category}`);
          
          // Final check before setting related products
          if (!isMounted || latestRequestId.current !== id) return;
          
          if (relatedResponse.ok) {
            const relatedData = await relatedResponse.json();
            setRelatedProducts(
              relatedData
                .filter((item: PricingItem) => item.id !== data.id)
                .slice(0, 4)
            );
          }
        }
        
        setError(null);
      } catch (err: any) {
        // Only set error if this is still the latest request
        if (isMounted) {
          console.error('Error fetching product:', err);
          setError(err.message || 'Failed to load product information');
          setProduct(null);
          setRelatedProducts([]);
        }
      } finally {
        // Only update loading state if still mounted
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    fetchProduct();

    // Cleanup function to prevent updates after unmount
    return () => {
      isMounted = false;
    };
  }, [unwrappedParams.id]); // Only re-run if id changes

  return {
    product,
    relatedProducts,
    loading,
    error,
    productId,
  };
} 