import { useState, useEffect, useCallback, useMemo } from 'react';
import { getOrderFormData, saveOrderFormData } from '@/utils/orderFormUtils';
import type { PricingItem } from '@/utils/pricing';

export interface CustomerInfo {
  name: string;
  phone: string;
  email: string;
  notes: string;
}

export interface UseOrderFormResult {
  // Form state
  quantity: string;
  setQuantity: (value: string) => void;
  meters: string;
  setMeters: (value: string) => void;
  orderType: 'design' | 'print' | 'both';
  setOrderType: (value: 'design' | 'print' | 'both') => void;
  designBrief: string;
  setDesignBrief: (value: string) => void;
  customerInfo: CustomerInfo;
  setCustomerInfo: (value: CustomerInfo) => void;
  
  // Artwork handling
  uploadedArtwork: Array<{
    name: string;
    fileName?: string;
    filePath?: string;
    uploaded?: boolean;
    size?: number;
    type?: string;
  }>;
  setUploadedArtwork: (value: any) => void;
  removeArtwork: (index: number) => void;
  
  // UI state
  showOrderForm: boolean;
  setShowOrderForm: (value: boolean) => void;
  isSubmitting: boolean;
  setIsSubmitting: (value: boolean) => void;
  
  // Notification state
  notification: {
    type: 'success' | 'error';
    message: string;
  } | null;
  setNotification: (value: any) => void;
}

/**
 * Custom hook to handle order form state management and persistence
 * Extracted from product page for better separation of concerns
 */
export function useOrderForm(product: PricingItem | null): UseOrderFormResult {
  // Form state
  const [quantity, setQuantity] = useState('1');
  const [meters, setMeters] = useState('1');
  const [orderType, setOrderType] = useState<'design' | 'print' | 'both'>('print');  // Default to 'print' instead of 'both'
  const [designBrief, setDesignBrief] = useState('');
  const [customerInfo, setCustomerInfo] = useState<CustomerInfo>({
    name: '',
    phone: '',
    email: '',
    notes: ''
  });
  
  // Artwork state
  const [uploadedArtwork, setUploadedArtwork] = useState<Array<{
    name: string;
    fileName?: string;
    filePath?: string;
    uploaded?: boolean;
    size?: number;
    type?: string;
  }>>([]);
  
  // UI state
  const [showOrderForm, setShowOrderForm] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [notification, setNotification] = useState<{
    type: 'success' | 'error';
    message: string;
  } | null>(null);

  // Enhanced notification handling with auto-dismiss
  const showNotification = useCallback((type: 'success' | 'error', message: string, duration: number = 3000) => {
    setNotification({ type, message });
    
    // Auto-dismiss notification after duration
    setTimeout(() => {
      setNotification(null);
    }, duration);
  }, []);

  // Override setNotification to use the enhanced version
  const enhancedSetNotification = useCallback((value: any) => {
    if (!value) {
      setNotification(null);
      return;
    }

    showNotification(value.type, value.message);
  }, [showNotification]);

  // Debounced form data saving
  const debouncedSave = useCallback((formData: any) => {
    const timeoutId = setTimeout(() => {
      saveOrderFormData(formData);
    }, 1000); // Save after 1 second of no changes
    return () => clearTimeout(timeoutId);
  }, []);

  // Memoize product ID to prevent unnecessary effect triggers
  const productId = useMemo(() => product?.id, [product?.id]);

  // Load saved form data on mount or product change
  useEffect(() => {
    if (!productId) return;
    
    const savedData = getOrderFormData();
    // Only restore data if it's for the same product
    if (savedData && savedData.productId === productId) {
      if (savedData.quantity) setQuantity(String(savedData.quantity));
      if (savedData.meters) setMeters(String(savedData.meters));
      if (savedData.designBrief) setDesignBrief(savedData.designBrief);
      if (savedData.artworkFiles) setUploadedArtwork(savedData.artworkFiles);
      if (savedData.customerInfo) setCustomerInfo(savedData.customerInfo);
      if (savedData.orderType) setOrderType(savedData.orderType);
    }
  }, [productId]);

  // Save form data when it changes
  useEffect(() => {
    if (!productId) return;

    const formData = {
      productId,
      productName: product?.service,
      quantity: parseInt(quantity) || 1,
      meters: parseFloat(meters) || 1,
      orderType,
      designBrief,
      artworkFiles: uploadedArtwork,
      customerInfo
    };

    const cleanup = debouncedSave(formData);
    return () => cleanup();
  }, [
    productId,
    quantity,
    meters,
    orderType,
    designBrief,
    uploadedArtwork,
    customerInfo,
    debouncedSave
  ]);

  const removeArtwork = (index: number) => {
    setUploadedArtwork(prev => prev.filter((_, i) => i !== index));
  };

  return {
    // Form state
    quantity,
    setQuantity,
    meters,
    setMeters,
    orderType,
    setOrderType,
    designBrief,
    setDesignBrief,
    customerInfo,
    setCustomerInfo,
    
    // Artwork handling
    uploadedArtwork,
    setUploadedArtwork,
    removeArtwork,
    
    // UI state
    showOrderForm,
    setShowOrderForm,
    isSubmitting,
    setIsSubmitting,
    
    // Notification state
    notification,
    setNotification: enhancedSetNotification,
  };
} 