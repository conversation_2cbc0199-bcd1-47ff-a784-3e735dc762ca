'use client';

import { useNotification } from '@/contexts/NotificationContext';

export default function TestNotificationsPage() {
  const { showNotification } = useNotification();

  const testNotifications = [
    {
      type: 'success' as const,
      title: 'Success!',
      message: 'Your catalogue item has been updated successfully.',
      action: {
        label: 'View Item',
        onClick: () => console.log('View item clicked')
      }
    },
    {
      type: 'error' as const,
      title: 'Validation Error',
      message: 'Invalid characters in service name. Please check your input and try again.',
      action: {
        label: 'Fix Now',
        onClick: () => console.log('Fix now clicked')
      }
    },
    {
      type: 'warning' as const,
      title: 'Warning',
      message: 'This action cannot be undone. Please review your changes carefully.',
      action: {
        label: 'Continue',
        onClick: () => console.log('Continue clicked')
      }
    },
    {
      type: 'info' as const,
      title: 'Information',
      message: 'New features are available in the admin panel. Check them out!',
      action: {
        label: 'Learn More',
        onClick: () => console.log('Learn more clicked')
      }
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white rounded-2xl shadow-xl p-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-8 text-center">
            🎨 Modern Notification System
          </h1>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {testNotifications.map((notification, index) => (
              <div key={index} className="space-y-4">
                <div className="p-6 border-2 border-dashed border-gray-200 rounded-xl hover:border-gray-300 transition-colors">
                  <h3 className="text-lg font-semibold text-gray-900 mb-2 capitalize">
                    {notification.type} Notification
                  </h3>
                  <p className="text-gray-600 mb-4 text-sm">
                    {notification.message}
                  </p>
                  
                  <div className="space-y-2">
                    <button
                      onClick={() => showNotification(
                        notification.type,
                        notification.title,
                        notification.message,
                        5000,
                        notification.action
                      )}
                      className={`w-full px-4 py-2 rounded-lg font-medium transition-colors ${
                        notification.type === 'success' 
                          ? 'bg-green-600 hover:bg-green-700 text-white'
                          : notification.type === 'error'
                          ? 'bg-red-600 hover:bg-red-700 text-white'
                          : notification.type === 'warning'
                          ? 'bg-orange-600 hover:bg-orange-700 text-white'
                          : 'bg-blue-600 hover:bg-blue-700 text-white'
                      }`}
                    >
                      Show with Action
                    </button>
                    
                    <button
                      onClick={() => showNotification(
                        notification.type,
                        notification.title,
                        notification.message
                      )}
                      className="w-full px-4 py-2 rounded-lg font-medium bg-gray-600 hover:bg-gray-700 text-white transition-colors"
                    >
                      Show Simple
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
          
          <div className="mt-12 p-6 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-200">
            <h2 className="text-xl font-semibold text-blue-900 mb-4">
              ✨ Features
            </h2>
            <ul className="space-y-2 text-blue-800">
              <li className="flex items-center">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                Modern gradient backgrounds with brand colors
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                Animated progress bar for auto-close timing
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                Optional action buttons for interactive notifications
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                Smooth animations with backdrop blur effects
              </li>
              <li className="flex items-center">
                <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                Support for success, error, warning, and info types
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
