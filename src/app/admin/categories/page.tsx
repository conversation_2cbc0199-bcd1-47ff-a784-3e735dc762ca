'use client';

import React, { useState, useEffect } from 'react';
import { ArrowPathIcon, PlusIcon } from '@heroicons/react/24/outline';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';

export default function CategoriesPage() {
  const [loading, setLoading] = useState(false);
  const [categories, setCategories] = useState<Array<{
    id: string;
    name: string;
    slug: string;
    description: string;
    createdAt: string;
    updatedAt: string;
  }>>([]);

  // Get search params to detect when coming back from edit page
  const searchParams = useSearchParams();
  const refreshParam = searchParams?.get('refresh') || null;

  const fetchCategories = async () => {
    setLoading(true);
    try {
      // Add a timestamp to bust the cache
      const timestamp = new Date().getTime();
      const response = await fetch(`/api/admin/categories?t=${timestamp}`);
      if (response.ok) {
        const data = await response.json();
        // API returns { success: true, data: categories[] }, so use data.data
        setCategories(Array.isArray(data.data) ? data.data : []);
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
      setCategories([]); // Reset to empty array on error
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchCategories();
  }, [refreshParam]);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-slate-800">Categories</h1>
        <div className="flex space-x-2">
          <button
            onClick={fetchCategories}
            className="p-2 rounded-lg text-slate-500 hover:text-slate-700 hover:bg-slate-100"
            disabled={loading}
          >
            <ArrowPathIcon className={`h-5 w-5 ${loading ? 'animate-spin' : ''}`} />
          </button>
          <Link
            href="/admin/categories/new"
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-500 hover:bg-orange-600"
          >
            <PlusIcon className="h-4 w-4 mr-1" />
            Add Category
          </Link>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-sm overflow-hidden">
        {categories.length === 0 ? (
          <div className="p-8 text-center">
            <div className="mx-auto h-12 w-12 text-slate-400 mb-4">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={1.5}
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M3.75 9.776c.112-.017.227-.026.344-.026h15.812c.117 0 .232.009.344.026m-16.5 0a2.25 2.25 0 00-1.883 2.542l.857 6a2.25 2.25 0 002.227 1.932H19.05a2.25 2.25 0 002.227-1.932l.857-6a2.25 2.25 0 00-1.883-2.542m-16.5 0V6A2.25 2.25 0 016 3.75h3.879a1.5 1.5 0 011.06.44l2.122 2.12a1.5 1.5 0 001.06.44H18A2.25 2.25 0 0120.25 9v.776"
                />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-slate-800 mb-1">No categories found</h3>
            <p className="text-slate-500 mb-4">Get started by creating your first category</p>
            <Link
              href="/admin/categories/new"
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-500 hover:bg-orange-600"
            >
              Create your first category
            </Link>
          </div>
        ) : (
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Name
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Slug
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Description
                </th>
                <th
                  scope="col"
                  className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
                >
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {(categories || []).map((category) => (
                <tr key={category.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">{category.name}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-500">{category.slug}</div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-500 truncate max-w-xs">
                      {category.description || '-'}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <Link
                      href={`/admin/categories/${category.id}/edit`}
                      className="text-orange-500 hover:text-orange-700 mr-4"
                    >
                      Edit
                    </Link>
                    <button
                      className="text-red-500 hover:text-red-700"
                      onClick={async () => {
                        if (window.confirm(`Are you sure you want to delete the category "${category.name}"?`)) {
                          setLoading(true);
                          try {
                            const response = await fetch(`/api/admin/categories/${category.id}`, {
                              method: 'DELETE',
                              headers: { 'Content-Type': 'application/json' },
                            });

                            if (response.ok) {
                              setCategories(prev => prev.filter(c => c.id !== category.id));
                              alert('Category deleted successfully');
                            } else {
                              alert('Failed to delete category');
                            }
                          } catch (error) {
                            console.error('Error deleting category:', error);
                            alert('An error occurred while deleting the category');
                          } finally {
                            setLoading(false);
                          }
                        }
                      }}
                    >
                      Delete
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        )}
      </div>
    </div>
  );
}
