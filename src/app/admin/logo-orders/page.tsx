'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import SmartPrice from '@/components/SmartPrice';
import Link from 'next/link';
import {
  ArrowPathIcon,
  EyeIcon,
  TrashIcon,
  PlusIcon,
  XMarkIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon,
  BanknotesIcon,
  CreditCardIcon
} from '@heroicons/react/24/outline';

interface LogoPackage {
  id: string;
  name: string;
  price: number;
}

interface LogoOrder {
  id: string;
  orderNumber: string;
  packageId: string;
  customerName: string;
  email: string;
  phone?: string;
  businessName: string;
  industry?: string;
  logoType?: string;
  slogan?: string;
  additionalInfo?: string;
  totalAmount: number;
  status: string;
  paymentStatus: string;
  deliveryMethod: string;
  whatsappSent: boolean;
  adminNotified: boolean;
  designFiles: string[];
  revisionCount: number;
  notes?: string;
  createdAt: string;
  updatedAt: string;
  completedAt?: string;
  package: LogoPackage;
}

const statusColors = {
  pending: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  in_progress: 'bg-blue-100 text-blue-800 border-blue-200',
  completed: 'bg-green-100 text-green-800 border-green-200',
  cancelled: 'bg-red-100 text-red-800 border-red-200',
  on_hold: 'bg-gray-100 text-gray-800 border-gray-200'
};

const paymentStatusColors = {
  pending: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  paid: 'bg-green-100 text-green-800 border-green-200',
  overdue: 'bg-red-100 text-red-800 border-red-200',
  refunded: 'bg-purple-100 text-purple-800 border-purple-200'
};

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'completed':
      return <CheckCircleIcon className="h-3 w-3" />;
    case 'in_progress':
      return <ClockIcon className="h-3 w-3" />;
    case 'cancelled':
      return <XMarkIcon className="h-3 w-3" />;
    default:
      return <ClockIcon className="h-3 w-3" />;
  }
};

const getPaymentIcon = (paymentStatus: string) => {
  switch (paymentStatus) {
    case 'paid':
      return <CheckCircleIcon className="h-3 w-3" />;
    case 'overdue':
      return <ExclamationTriangleIcon className="h-3 w-3" />;
    default:
      return <CreditCardIcon className="h-3 w-3" />;
  }
};

export default function LogoOrdersAdmin() {
  const [orders, setOrders] = useState<LogoOrder[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  
  // Bulk operations state
  const [selectedOrders, setSelectedOrders] = useState<Set<string>>(new Set());
  const [bulkActionType, setBulkActionType] = useState('');
  const [showBulkActionModal, setShowBulkActionModal] = useState(false);
  const [bulkLoading, setBulkLoading] = useState(false);
  
  // Delete confirmation state
  const [deleteConfirmation, setDeleteConfirmation] = useState<{
    show: boolean;
    orderId?: string;
    orderNumber?: string;
    type: 'single' | 'bulk';
  }>({ show: false, type: 'single' });

  // Fetch orders
  const fetchOrders = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/logo-orders');
      const result = await response.json();
      if (result.success) {
        setOrders(result.data);
        setSelectedOrders(new Set()); // Clear selections on refresh
      }
    } catch (error) {
      console.error('Error fetching orders:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchOrders();
  }, []);

  // Individual delete order
  const deleteOrder = async (orderId: string) => {
    try {
      const response = await fetch(`/api/admin/logo-orders/${orderId}`, {
        method: 'DELETE',
      });

      const result = await response.json();
      
      if (result.success) {
        await fetchOrders();
        alert('Order deleted successfully');
      } else {
        alert('Error deleting order: ' + result.error);
      }
    } catch (error) {
      console.error('Error deleting order:', error);
      alert('Error deleting order');
    }
  };

  // Bulk operations
  const handleBulkAction = async (action: string, data?: any) => {
    if (selectedOrders.size === 0) {
      alert('Please select orders first');
      return;
    }

    setBulkLoading(true);
    try {
      const response = await fetch('/api/admin/logo-orders/bulk', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action,
          orderIds: Array.from(selectedOrders),
          data
        }),
      });

      const result = await response.json();
      
      if (result.success) {
        await fetchOrders();
        setSelectedOrders(new Set());
        setShowBulkActionModal(false);
        alert(result.message);
      } else {
        alert('Error: ' + result.error);
      }
    } catch (error) {
      console.error('Error in bulk action:', error);
      alert('Error executing bulk action');
    } finally {
      setBulkLoading(false);
    }
  };

  // Selection handlers
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedOrders(new Set(filteredOrders.map(order => order.id)));
    } else {
      setSelectedOrders(new Set());
    }
  };

  const handleSelectOrder = (orderId: string, checked: boolean) => {
    const newSelected = new Set(selectedOrders);
    if (checked) {
      newSelected.add(orderId);
    } else {
      newSelected.delete(orderId);
    }
    setSelectedOrders(newSelected);
  };

  // Filter orders
  const filteredOrders = orders.filter(order => {
    const matchesFilter = filter === 'all' || order.status === filter;
    const matchesSearch = 
      order.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.businessName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.orderNumber.toLowerCase().includes(searchTerm.toLowerCase());
    
    return matchesFilter && matchesSearch;
  });

  // Get order stats
  const orderStats = {
    total: orders.length,
    pending: orders.filter(o => o.status === 'pending').length,
    in_progress: orders.filter(o => o.status === 'in_progress').length,
    completed: orders.filter(o => o.status === 'completed').length,
    revenue: orders
      .filter(o => o.paymentStatus === 'paid')
      .reduce((sum, o) => sum + o.totalAmount, 0)
  };

  // Format date consistently
  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString('en-GB', {
        day: '2-digit',
        month: '2-digit', 
        year: 'numeric'
      });
    } catch {
      return 'Invalid Date';
    }
  };

  // Bulk action modal component
  const BulkActionModal = () => (
    <AnimatePresence>
      {showBulkActionModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-md w-full p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Bulk Action: {bulkActionType.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
            </h3>
            <p className="text-gray-600 mb-4">
              Selected {selectedOrders.size} order{selectedOrders.size !== 1 ? 's' : ''}
            </p>
            
            {bulkActionType === 'updateStatus' && (
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  New Status
                </label>
                <select
                  id="bulkStatus"
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="pending">Pending</option>
                  <option value="in_progress">In Progress</option>
                  <option value="completed">Completed</option>
                  <option value="cancelled">Cancelled</option>
                  <option value="on_hold">On Hold</option>
                </select>
              </div>
            )}
            
            {bulkActionType === 'updatePaymentStatus' && (
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  New Payment Status
                </label>
                <select
                  id="bulkPaymentStatus"
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="pending">Pending</option>
                  <option value="paid">Paid</option>
                  <option value="overdue">Overdue</option>
                  <option value="refunded">Refunded</option>
                </select>
              </div>
            )}

            <div className="flex gap-3 justify-end">
              <button
                onClick={() => setShowBulkActionModal(false)}
                className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50"
                disabled={bulkLoading}
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  if (bulkActionType === 'updateStatus') {
                    const status = (document.getElementById('bulkStatus') as HTMLSelectElement).value;
                    handleBulkAction('updateStatus', { status });
                  } else if (bulkActionType === 'updatePaymentStatus') {
                    const paymentStatus = (document.getElementById('bulkPaymentStatus') as HTMLSelectElement).value;
                    handleBulkAction('updatePaymentStatus', { paymentStatus });
                  } else {
                    handleBulkAction(bulkActionType);
                  }
                }}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
                disabled={bulkLoading}
              >
                {bulkLoading ? 'Processing...' : 'Execute'}
              </button>
            </div>
          </div>
        </div>
      )}
    </AnimatePresence>
  );

  // Delete confirmation modal
  const DeleteConfirmationModal = () => (
    <AnimatePresence>
      {deleteConfirmation.show && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-md w-full p-6">
            <div className="flex items-center gap-3 mb-4">
              <div className="flex-shrink-0 w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                <ExclamationTriangleIcon className="w-6 h-6 text-red-600" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">
                  {deleteConfirmation.type === 'single' ? 'Delete Order' : 'Delete Multiple Orders'}
                </h3>
                <p className="text-sm text-gray-500">This action cannot be undone</p>
              </div>
            </div>
            
            <div className="mb-6">
              {deleteConfirmation.type === 'single' ? (
                <p className="text-gray-700">
                  Are you sure you want to delete order <strong>{deleteConfirmation.orderNumber}</strong>?
                </p>
              ) : (
                <p className="text-gray-700">
                  Are you sure you want to delete <strong>{selectedOrders.size}</strong> selected order{selectedOrders.size !== 1 ? 's' : ''}?
                </p>
              )}
            </div>

            <div className="flex gap-3 justify-end">
              <button
                onClick={() => setDeleteConfirmation({ show: false, type: 'single' })}
                className="px-4 py-2 text-gray-700 border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={async () => {
                  if (deleteConfirmation.type === 'single' && deleteConfirmation.orderId) {
                    await deleteOrder(deleteConfirmation.orderId);
                  } else {
                    await handleBulkAction('delete');
                  }
                  setDeleteConfirmation({ show: false, type: 'single' });
                }}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </AnimatePresence>
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <ArrowPathIcon className="h-8 w-8 animate-spin text-blue-600" />
      </div>
    );
  }

  return (
    <div className="space-y-4 sm:space-y-6">
      {/* Page Header */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
            <div>
              <h1 className="text-xl sm:text-2xl font-bold text-gray-900">Logo Orders</h1>
              <p className="text-sm sm:text-base text-gray-600 mt-1">
                Manage logo design orders and track their progress
              </p>
            </div>
            <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
              <button
                onClick={fetchOrders}
                disabled={loading}
                className="inline-flex items-center justify-center px-3 sm:px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <ArrowPathIcon className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                Refresh
              </button>
            </div>
          </div>
        </div>

        {/* Summary Stats */}
        <div className="px-4 sm:px-6 py-6 bg-gradient-to-r from-gray-50 to-white border-b border-gray-200">
          <div className="flex flex-col sm:flex-row sm:flex-wrap sm:justify-start sm:gap-8">
            {/* Total Orders */}
            <div className="w-full sm:flex-1 sm:min-w-[120px] sm:max-w-[160px] bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200 mb-3 sm:mb-0">
              <div className="flex sm:flex-col items-center justify-between p-4 border-l-4 border-blue-600">
                <div className="text-sm font-medium text-gray-600">Total Orders</div>
                <div className="text-2xl sm:text-3xl font-bold text-blue-600 sm:mt-1">{orderStats.total}</div>
              </div>
            </div>

            {/* Pending */}
            <div className="w-full sm:flex-1 sm:min-w-[120px] sm:max-w-[160px] bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200 mb-3 sm:mb-0">
              <div className="flex sm:flex-col items-center justify-between p-4 border-l-4 border-yellow-600">
                <div className="text-sm font-medium text-gray-600">Pending</div>
                <div className="text-2xl sm:text-3xl font-bold text-yellow-600 sm:mt-1">{orderStats.pending}</div>
              </div>
            </div>

            {/* In Progress */}
            <div className="w-full sm:flex-1 sm:min-w-[120px] sm:max-w-[160px] bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200 mb-3 sm:mb-0">
              <div className="flex sm:flex-col items-center justify-between p-4 border-l-4 border-blue-600">
                <div className="text-sm font-medium text-gray-600">In Progress</div>
                <div className="text-2xl sm:text-3xl font-bold text-blue-600 sm:mt-1">{orderStats.in_progress}</div>
              </div>
            </div>

            {/* Completed */}
            <div className="w-full sm:flex-1 sm:min-w-[120px] sm:max-w-[160px] bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200 mb-3 sm:mb-0">
              <div className="flex sm:flex-col items-center justify-between p-4 border-l-4 border-green-600">
                <div className="text-sm font-medium text-gray-600">Completed</div>
                <div className="text-2xl sm:text-3xl font-bold text-green-600 sm:mt-1">{orderStats.completed}</div>
              </div>
            </div>

            {/* Revenue */}
            <div className="w-full sm:flex-1 sm:min-w-[120px] sm:max-w-[160px] bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200">
              <div className="flex sm:flex-col items-center justify-between p-4 border-l-4 border-gray-900">
                <div className="text-sm font-medium text-gray-600">Revenue</div>
                <div className="text-2xl sm:text-3xl font-bold text-gray-900 sm:mt-1">
                  <SmartPrice kesAmount={orderStats.revenue} size="sm" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Controls */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 sm:px-6 py-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <input
              type="text"
              placeholder="Search orders..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="flex-1 border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            />
            <select
              value={filter}
              onChange={(e) => setFilter(e.target.value)}
              className="border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            >
              <option value="all">All Status</option>
              <option value="pending">Pending</option>
              <option value="in_progress">In Progress</option>
              <option value="completed">Completed</option>
              <option value="cancelled">Cancelled</option>
              <option value="on_hold">On Hold</option>
            </select>
            
            {/* Bulk Actions Dropdown */}
            {selectedOrders.size > 0 && (
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-600 whitespace-nowrap">
                  {selectedOrders.size} selected
                </span>
                <select
                  value=""
                  onChange={(e) => {
                    if (e.target.value) {
                      if (e.target.value === 'delete') {
                        setDeleteConfirmation({ show: true, type: 'bulk' });
                      } else {
                        setBulkActionType(e.target.value);
                        setShowBulkActionModal(true);
                      }
                    }
                  }}
                  className="border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
                >
                  <option value="">Bulk Actions</option>
                  <option value="updateStatus">Update Status</option>
                  <option value="updatePaymentStatus">Update Payment Status</option>
                  <option value="markNotified">Mark as Notified</option>
                  <option value="delete">Delete Selected</option>
                </select>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Orders Table */}
      <div className="bg-white shadow rounded-lg overflow-hidden">
        {filteredOrders.length === 0 ? (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-4">
              <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No orders found</h3>
            <p className="text-gray-500">
              {searchTerm || filter !== 'all' 
                ? 'Try adjusting your search or filters' 
                : 'No logo orders have been placed yet'
              }
            </p>
          </div>
        ) : (
          <div className="divide-y divide-gray-100">
            {/* Table Headers - visible only on desktop */}
            <div className="hidden sm:grid sm:grid-cols-8 px-4 py-3 bg-gray-50 text-xs font-medium text-gray-500 uppercase tracking-wider">
              <div className="col-span-1">
                <input
                  type="checkbox"
                  checked={selectedOrders.size === filteredOrders.length && filteredOrders.length > 0}
                  onChange={(e) => handleSelectAll(e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
              </div>
              <div className="col-span-2">Order</div>
              <div className="col-span-1">Customer</div>
              <div className="col-span-1">Package</div>
              <div className="col-span-1">Amount</div>
              <div className="col-span-1">Status</div>
              <div className="col-span-1">Actions</div>
            </div>

            {/* Order Cards */}
            {filteredOrders.map((order) => (
              <div key={order.id} className="bg-white hover:bg-gray-50 transition-colors duration-150">
                {/* Mobile Card Layout */}
                <div className="sm:hidden">
                  <div className="p-4 space-y-4">
                    <div className="flex items-start justify-between">
                      <div>
                        <div className="font-medium text-gray-900">{order.orderNumber}</div>
                        <div className="text-sm text-gray-500">{order.businessName}</div>
                      </div>
                      <input
                        type="checkbox"
                        checked={selectedOrders.has(order.id)}
                        onChange={(e) => handleSelectOrder(order.id, e.target.checked)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                    </div>
                    
                    <div className="flex justify-between items-center text-sm">
                      <div className="text-gray-500">Customer</div>
                      <div className="font-medium text-gray-900">{order.customerName}</div>
                    </div>

                    <div className="flex justify-between items-center text-sm">
                      <div className="text-gray-500">Package</div>
                      <div className="font-medium text-gray-900">{order.package.name}</div>
                    </div>

                    <div className="flex justify-between items-center text-sm">
                      <div className="text-gray-500">Amount</div>
                      <div className="font-medium text-gray-900">
                        <SmartPrice kesAmount={order.totalAmount} size="sm" />
                      </div>
                    </div>

                    <div className="flex justify-between items-center text-sm">
                      <div className="text-gray-500">Status</div>
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                        statusColors[order.status as keyof typeof statusColors] || 'bg-gray-100 text-gray-800'
                      }`}>
                        {getStatusIcon(order.status)}
                        <span className="ml-1">{order.status.replace('_', ' ')}</span>
                      </span>
                    </div>

                    <div className="flex justify-between items-center text-sm">
                      <div className="text-gray-500">Payment</div>
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                        paymentStatusColors[order.paymentStatus as keyof typeof paymentStatusColors] || 'bg-gray-100 text-gray-800'
                      }`}>
                        {getPaymentIcon(order.paymentStatus)}
                        <span className="ml-1">{order.paymentStatus}</span>
                      </span>
                    </div>

                    <div className="flex justify-between items-center text-sm">
                      <div className="text-gray-500">Date</div>
                      <div className="font-medium text-gray-900">{formatDate(order.createdAt)}</div>
                    </div>

                    <div className="flex justify-end gap-2 mt-4">
                      <Link
                        href={`/admin/logo-orders/${order.id}`}
                        className="inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                      >
                        <EyeIcon className="h-3 w-3 mr-1" />
                        View
                      </Link>
                      <button
                        onClick={() => setDeleteConfirmation({
                          show: true,
                          orderId: order.id,
                          orderNumber: order.orderNumber,
                          type: 'single'
                        })}
                        className="inline-flex items-center px-3 py-1.5 border border-red-300 shadow-sm text-xs font-medium rounded text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                      >
                        <TrashIcon className="h-3 w-3 mr-1" />
                        Delete
                      </button>
                    </div>
                  </div>
                </div>

                {/* Desktop Table Layout */}
                <div className="hidden sm:grid sm:grid-cols-8 sm:px-4 sm:py-3">
                  <div className="col-span-1 flex items-center">
                    <input
                      type="checkbox"
                      checked={selectedOrders.has(order.id)}
                      onChange={(e) => handleSelectOrder(order.id, e.target.checked)}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                  </div>
                  <div className="col-span-2">
                    <div className="font-medium text-gray-900">{order.orderNumber}</div>
                    <div className="text-sm text-gray-500 truncate">{order.businessName}</div>
                  </div>
                  <div className="col-span-1 truncate">
                    <div className="font-medium text-gray-900">{order.customerName}</div>
                    <div className="text-sm text-gray-500 truncate">{order.email}</div>
                  </div>
                  <div className="col-span-1 font-medium text-gray-900">{order.package.name}</div>
                  <div className="col-span-1 font-medium text-gray-900">
                    <SmartPrice kesAmount={order.totalAmount} size="sm" />
                  </div>
                  <div className="col-span-1">
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      statusColors[order.status as keyof typeof statusColors] || 'bg-gray-100 text-gray-800'
                    }`}>
                      {getStatusIcon(order.status)}
                      <span className="ml-1">{order.status.replace('_', ' ')}</span>
                    </span>
                  </div>
                  <div className="col-span-1 flex items-center space-x-2">
                    <Link
                      href={`/admin/logo-orders/${order.id}`}
                      className="inline-flex items-center p-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                    >
                      <EyeIcon className="h-3 w-3" />
                    </Link>
                    <button
                      onClick={() => setDeleteConfirmation({
                        show: true,
                        orderId: order.id,
                        orderNumber: order.orderNumber,
                        type: 'single'
                      })}
                      className="inline-flex items-center p-1 border border-red-300 shadow-sm text-xs font-medium rounded text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                    >
                      <TrashIcon className="h-3 w-3" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Modals */}
      <BulkActionModal />
      <DeleteConfirmationModal />
    </div>
  );
} 