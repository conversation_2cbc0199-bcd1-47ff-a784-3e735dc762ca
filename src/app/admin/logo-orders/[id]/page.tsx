'use client';

import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import Link from 'next/link';
import SmartPrice from '@/components/SmartPrice';
import {
  ArrowLeftIcon,
  CheckCircleIcon,
  ClockIcon,
  XMarkIcon,
  ExclamationTriangleIcon,
  CreditCardIcon,
  TrashIcon,
  PencilIcon
} from '@heroicons/react/24/outline';

interface LogoPackage {
  id: string;
  name: string;
  price: number;
}

interface LogoOrder {
  id: string;
  orderNumber: string;
  packageId: string;
  customerName: string;
  email: string;
  phone?: string;
  businessName: string;
  industry?: string;
  logoType?: string;
  slogan?: string;
  additionalInfo?: string;
  totalAmount: number;
  status: string;
  paymentStatus: string;
  deliveryMethod: string;
  whatsappSent: boolean;
  adminNotified: boolean;
  designFiles: string[];
  revisionCount: number;
  notes?: string;
  createdAt: string;
  updatedAt: string;
  completedAt?: string;
  package: LogoPackage;
}

const statusColors = {
  pending: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  in_progress: 'bg-blue-100 text-blue-800 border-blue-200',
  completed: 'bg-green-100 text-green-800 border-green-200',
  cancelled: 'bg-red-100 text-red-800 border-red-200',
  on_hold: 'bg-gray-100 text-gray-800 border-gray-200'
};

const paymentStatusColors = {
  pending: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  paid: 'bg-green-100 text-green-800 border-green-200',
  overdue: 'bg-red-100 text-red-800 border-red-200',
  refunded: 'bg-purple-100 text-purple-800 border-purple-200'
};

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'completed':
      return <CheckCircleIcon className="h-4 w-4" />;
    case 'in_progress':
      return <ClockIcon className="h-4 w-4" />;
    case 'cancelled':
      return <XMarkIcon className="h-4 w-4" />;
    default:
      return <ClockIcon className="h-4 w-4" />;
  }
};

const getPaymentIcon = (paymentStatus: string) => {
  switch (paymentStatus) {
    case 'paid':
      return <CheckCircleIcon className="h-4 w-4" />;
    case 'overdue':
      return <ExclamationTriangleIcon className="h-4 w-4" />;
    default:
      return <CreditCardIcon className="h-4 w-4" />;
  }
};

export default function LogoOrderDetailPage() {
  const params = useParams();
  const router = useRouter();
  const [order, setOrder] = useState<LogoOrder | null>(null);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);
  const [deleting, setDeleting] = useState(false);

  const orderId = params.id as string;

  // Fetch order details
  const fetchOrder = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/admin/logo-orders/${orderId}`);
      const result = await response.json();
      if (result.success) {
        setOrder(result.data);
      } else {
        console.error('Error fetching order:', result.error);
        router.push('/admin/logo-orders');
      }
    } catch (error) {
      console.error('Error fetching order:', error);
      router.push('/admin/logo-orders');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (orderId) {
      fetchOrder();
    }
  }, [orderId]);

  // Update order status
  const updateOrderStatus = async (status: string) => {
    try {
      setUpdating(true);
      const response = await fetch(`/api/admin/logo-orders/${orderId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status }),
      });

      const result = await response.json();
      
      if (result.success) {
        setOrder(result.data);
        alert('Order status updated successfully');
      } else {
        alert('Error updating order: ' + result.error);
      }
    } catch (error) {
      console.error('Error updating order:', error);
      alert('Error updating order');
    } finally {
      setUpdating(false);
    }
  };

  // Update payment status
  const updatePaymentStatus = async (paymentStatus: string) => {
    try {
      setUpdating(true);
      const response = await fetch(`/api/admin/logo-orders/${orderId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ paymentStatus }),
      });

      const result = await response.json();
      
      if (result.success) {
        setOrder(result.data);
        alert('Payment status updated successfully');
      } else {
        alert('Error updating payment status: ' + result.error);
      }
    } catch (error) {
      console.error('Error updating payment status:', error);
      alert('Error updating payment status');
    } finally {
      setUpdating(false);
    }
  };

  // Delete order
  const deleteOrder = async () => {
    if (!order) return;

    if (!confirm(`Are you sure you want to delete order ${order.orderNumber}? This action cannot be undone.`)) {
      return;
    }

    try {
      setDeleting(true);
      const response = await fetch(`/api/admin/logo-orders/${orderId}`, {
        method: 'DELETE',
      });

      const result = await response.json();
      
      if (result.success) {
        alert('Order deleted successfully');
        router.push('/admin/logo-orders');
      } else {
        alert('Error deleting order: ' + result.error);
      }
    } catch (error) {
      console.error('Error deleting order:', error);
      alert('Error deleting order');
    } finally {
      setDeleting(false);
    }
  };

  // Format date consistently
  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString('en-GB', {
        day: '2-digit',
        month: '2-digit', 
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch {
      return 'Invalid Date';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!order) {
    return (
      <div className="text-center py-12">
        <h3 className="text-lg font-medium text-gray-900 mb-2">Order not found</h3>
        <p className="text-gray-500 mb-4">The order you're looking for doesn't exist or has been deleted.</p>
        <Link
          href="/admin/logo-orders"
          className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
        >
          <ArrowLeftIcon className="h-4 w-4 mr-2" />
          Back to Orders
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-4 sm:space-y-6">
      {/* Header */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
            <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-4 space-y-2 sm:space-y-0">
              <Link
                href="/admin/logo-orders"
                className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 self-start"
              >
                <ArrowLeftIcon className="h-4 w-4 mr-1" />
                Back to Orders
              </Link>
              <div>
                <h1 className="text-xl sm:text-2xl font-bold text-gray-900">Order {order.orderNumber}</h1>
                <p className="text-sm sm:text-base text-gray-600">Created on {formatDate(order.createdAt)}</p>
              </div>
            </div>
            <div className="flex flex-wrap gap-2">
              <button
                onClick={deleteOrder}
                disabled={deleting}
                className="inline-flex items-center px-3 py-1.5 border border-red-300 rounded text-xs font-medium text-red-700 bg-white hover:bg-red-50 disabled:opacity-50"
              >
                <TrashIcon className="h-3 w-3 mr-1" />
                {deleting ? 'Deleting...' : 'Delete Order'}
              </button>
            </div>
          </div>
        </div>

        {/* Status Bar */}
        <div className="px-4 sm:px-6 py-4 bg-gray-50 border-b border-gray-200">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-500">Status:</span>
              <span className={`inline-flex items-center px-2.5 py-1.5 text-xs font-medium rounded-full border ${
                statusColors[order.status as keyof typeof statusColors] || 'bg-gray-100 text-gray-800 border-gray-200'
              }`}>
                {getStatusIcon(order.status)}
                <span className="ml-1">{order.status.replace('_', ' ')}</span>
              </span>
              <span className="text-sm text-gray-500">Payment:</span>
              <span className={`inline-flex items-center px-2.5 py-1.5 text-xs font-medium rounded-full border ${
                paymentStatusColors[order.paymentStatus as keyof typeof paymentStatusColors] || 'bg-gray-100 text-gray-800 border-gray-200'
              }`}>
                {getPaymentIcon(order.paymentStatus)}
                <span className="ml-1">{order.paymentStatus}</span>
              </span>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 xl:grid-cols-3 gap-4 sm:gap-6">
        {/* Order Details */}
        <div className="xl:col-span-2 space-y-4 sm:space-y-6">
          {/* Customer Information */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
              <h2 className="text-base sm:text-lg font-medium text-gray-900">Customer Information</h2>
            </div>
            <div className="px-4 sm:px-6 py-4">
              <dl className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                <div>
                  <dt className="text-sm font-medium text-gray-500">Name</dt>
                  <dd className="mt-1 text-sm text-gray-900 break-words">{order.customerName}</dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">Email</dt>
                  <dd className="mt-1 text-sm text-gray-900 break-all">{order.email}</dd>
                </div>
                {order.phone && (
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Phone Number</dt>
                    <dd className="mt-1 text-sm text-gray-900">{order.phone}</dd>
                  </div>
                )}
                <div>
                  <dt className="text-sm font-medium text-gray-500">Business Name</dt>
                  <dd className="mt-1 text-sm text-gray-900 break-words">{order.businessName}</dd>
                </div>
                {order.industry && (
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Industry</dt>
                    <dd className="mt-1 text-sm text-gray-900">{order.industry}</dd>
                  </div>
                )}
              </dl>
            </div>
          </div>

          {/* Order Information */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
              <h2 className="text-base sm:text-lg font-medium text-gray-900">Order Information</h2>
            </div>
            <div className="px-4 sm:px-6 py-4">
              <dl className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                <div>
                  <dt className="text-sm font-medium text-gray-500">Package</dt>
                  <dd className="mt-1 text-sm text-gray-900">{order.package.name}</dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">Amount</dt>
                  <dd className="mt-1 text-sm text-gray-900">
                    <SmartPrice kesAmount={order.totalAmount} size="sm" />
                  </dd>
                </div>
                {order.logoType && (
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Logo Type</dt>
                    <dd className="mt-1 text-sm text-gray-900 capitalize">{order.logoType}</dd>
                  </div>
                )}
                {order.slogan && (
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Slogan</dt>
                    <dd className="mt-1 text-sm text-gray-900">{order.slogan}</dd>
                  </div>
                )}
                <div>
                  <dt className="text-sm font-medium text-gray-500">Delivery Method</dt>
                  <dd className="mt-1 text-sm text-gray-900 capitalize">{order.deliveryMethod}</dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">Revisions</dt>
                  <dd className="mt-1 text-sm text-gray-900">{order.revisionCount}</dd>
                </div>
              </dl>
            </div>
          </div>

          {/* Design Brief */}
          {order.additionalInfo && (
            <div className="bg-white shadow rounded-lg">
              <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
                <h2 className="text-base sm:text-lg font-medium text-gray-900">Design Brief</h2>
              </div>
              <div className="px-4 sm:px-6 py-4">
                <p className="text-sm text-gray-700 whitespace-pre-wrap">{order.additionalInfo}</p>
              </div>
            </div>
          )}

          {/* Notes */}
          {order.notes && (
            <div className="bg-white shadow rounded-lg">
              <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
                <h2 className="text-base sm:text-lg font-medium text-gray-900">Notes</h2>
              </div>
              <div className="px-4 sm:px-6 py-4">
                <p className="text-sm text-gray-700 whitespace-pre-wrap">{order.notes}</p>
              </div>
            </div>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-4 sm:space-y-6">
          {/* Status Management */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
              <h2 className="text-base sm:text-lg font-medium text-gray-900">Status Management</h2>
            </div>
            <div className="px-4 sm:px-6 py-4 space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Order Status</label>
                <select
                  value={order.status}
                  onChange={(e) => updateOrderStatus(e.target.value)}
                  disabled={updating}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"
                >
                  <option value="pending">Pending</option>
                  <option value="in_progress">In Progress</option>
                  <option value="completed">Completed</option>
                  <option value="cancelled">Cancelled</option>
                  <option value="on_hold">On Hold</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Payment Status</label>
                <select
                  value={order.paymentStatus}
                  onChange={(e) => updatePaymentStatus(e.target.value)}
                  disabled={updating}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50"
                >
                  <option value="pending">Pending</option>
                  <option value="paid">Paid</option>
                  <option value="overdue">Overdue</option>
                  <option value="refunded">Refunded</option>
                </select>
              </div>
            </div>
          </div>

          {/* Order Information */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
              <h2 className="text-base sm:text-lg font-medium text-gray-900">Order Tracking</h2>
            </div>
            <div className="px-4 sm:px-6 py-4">
              <dl className="space-y-3">
                <div>
                  <dt className="text-sm font-medium text-gray-500">Order Number</dt>
                  <dd className="mt-1 text-sm text-gray-900 break-all">{order.orderNumber}</dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">WhatsApp Sent</dt>
                  <dd className="mt-1 text-sm text-gray-900">{order.whatsappSent ? 'Yes' : 'No'}</dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">Admin Notified</dt>
                  <dd className="mt-1 text-sm text-gray-900">{order.adminNotified ? 'Yes' : 'No'}</dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">Created Date</dt>
                  <dd className="mt-1 text-sm text-gray-900">{formatDate(order.createdAt)}</dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">Last Updated</dt>
                  <dd className="mt-1 text-sm text-gray-900">{formatDate(order.updatedAt)}</dd>
                </div>
                {order.completedAt && (
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Completed Date</dt>
                    <dd className="mt-1 text-sm text-gray-900">{formatDate(order.completedAt)}</dd>
                  </div>
                )}
              </dl>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 