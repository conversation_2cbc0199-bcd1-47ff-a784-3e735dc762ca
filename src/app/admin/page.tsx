'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';

export default function AdminPage() {
  const { user, isLoading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading) {
      if (user) {
        // User is authenticated, redirect to dashboard
        router.replace('/admin/dashboard');
      } else {
        // User is not authenticated, redirect to login
        router.replace('/admin/login');
      }
    }
  }, [user, isLoading, router]);

  // Show loading while determining auth status
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Loading Admin Panel</h3>
          <p className="text-sm text-gray-600">
            Checking authentication status...
          </p>
        </div>
      </div>
    );
  }

  // This should not be reached due to redirects above, but just in case
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">Redirecting...</h3>
        <p className="text-sm text-gray-600">
          Please wait while we redirect you...
        </p>
      </div>
    </div>
  );
} 