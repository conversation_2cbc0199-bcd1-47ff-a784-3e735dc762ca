'use client';

import React, { useState, useEffect } from 'react';
import { 
  BeakerIcon, 
  ServerIcon, 
  CloudIcon, 
  ArrowPathIcon
} from '@heroicons/react/24/outline';
import { CheckCircleIcon, XCircleIcon } from '@heroicons/react/24/solid';
import { useNotification } from '@/contexts/NotificationContext';
import { TestType } from '@/utils/testUtils';

interface TestTypeInfo {
  id: TestType;
  name: string;
  description: string;
  icon: React.ReactNode;
}

interface TestResult {
  success: boolean;
  message: string;
  details?: any;
  duration?: number;
}

export default function TestsPage() {
  const [testTypes, setTestTypes] = useState<TestTypeInfo[]>([]);
  const [selectedTest, setSelectedTest] = useState<TestType | null>(null);
  const [testResult, setTestResult] = useState<TestResult | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingTypes, setIsLoadingTypes] = useState(true);
  const { showNotification } = useNotification();

  // Fetch available test types
  useEffect(() => {
    const fetchTestTypes = async () => {
      try {
        setIsLoadingTypes(true);
        const response = await fetch('/api/admin/tests');
        
        if (!response.ok) {
          throw new Error('Failed to fetch test types');
        }
        
        const data = await response.json();
        
        // Map test types to UI-friendly format with icons
        const mappedTypes = data.testTypes.map((type: TestType) => {
          let icon;
          switch (type) {
            case TestType.S3_CONNECTION:
              icon = <CloudIcon className="h-6 w-6" />;
              break;
            case TestType.S3_UPLOAD:
              icon = <CloudIcon className="h-6 w-6" />;
              break;
            case TestType.DATABASE:
              icon = <ServerIcon className="h-6 w-6" />;
              break;
            default:
              icon = <BeakerIcon className="h-6 w-6" />;
          }
          
          return {
            id: type,
            name: type.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()).join(' '),
            description: data.descriptions[type] || 'No description available',
            icon
          };
        });
        
        setTestTypes(mappedTypes);
      } catch (error) {
        console.error('Error fetching test types:', error);
        showNotification('error', 'Error', 'Failed to fetch test types');
      } finally {
        setIsLoadingTypes(false);
      }
    };
    
    fetchTestTypes();
  }, [showNotification]);

  // Run the selected test
  const runTest = async () => {
    if (!selectedTest) {
      showNotification('error', 'Error', 'Please select a test to run');
      return;
    }
    
    try {
      setIsLoading(true);
      setTestResult(null);
      
      const response = await fetch('/api/admin/tests', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: selectedTest,
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to run test');
      }
      
      const result = await response.json();
      setTestResult(result);
      
      showNotification(
        result.success ? 'success' : 'error',
        result.success ? 'Test Passed' : 'Test Failed',
        result.message
      );
    } catch (error) {
      console.error('Error running test:', error);
      showNotification('error', 'Error', error instanceof Error ? error.message : 'Failed to run test');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-8">
      <div className="flex items-center">
        <div className="p-2 bg-blue-50 text-blue-500 rounded-lg mr-3">
          <BeakerIcon className="h-6 w-6" />
        </div>
        <h1 className="text-2xl font-semibold text-slate-800">System Tests</h1>
      </div>

      {/* Test Selection */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h2 className="text-lg font-medium text-slate-800 mb-4">Select a Test to Run</h2>
        
        {isLoadingTypes ? (
          <div className="flex justify-center items-center h-40">
            <ArrowPathIcon className="h-8 w-8 text-blue-500 animate-spin" />
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {testTypes.map((test) => (
              <div
                key={test.id}
                className={`border rounded-lg p-4 cursor-pointer transition-all ${
                  selectedTest === test.id
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-blue-300 hover:bg-blue-50/50'
                }`}
                onClick={() => setSelectedTest(test.id)}
              >
                <div className="flex items-center mb-2">
                  <div className="p-2 bg-blue-100 text-blue-500 rounded-lg mr-2">
                    {test.icon}
                  </div>
                  <h3 className="font-medium text-slate-800">{test.name}</h3>
                </div>
                <p className="text-sm text-slate-600">{test.description}</p>
              </div>
            ))}
          </div>
        )}
        
        <div className="mt-6 flex justify-end">
          <button
            onClick={runTest}
            disabled={isLoading || !selectedTest}
            className={`px-4 py-2 rounded-lg font-medium flex items-center ${
              isLoading || !selectedTest
                ? 'bg-gray-200 text-gray-500 cursor-not-allowed'
                : 'bg-blue-500 text-white hover:bg-blue-600'
            }`}
          >
            {isLoading ? (
              <>
                <ArrowPathIcon className="h-5 w-5 mr-2 animate-spin" />
                Running Test...
              </>
            ) : (
              <>
                <BeakerIcon className="h-5 w-5 mr-2" />
                Run Test
              </>
            )}
          </button>
        </div>
      </div>

      {/* Test Results */}
      {testResult && (
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-lg font-medium text-slate-800 mb-4">Test Results</h2>
          
          <div className={`p-4 rounded-lg mb-4 ${
            testResult.success ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'
          }`}>
            <div className="flex items-center">
              {testResult.success ? (
                <CheckCircleIcon className="h-6 w-6 text-green-500 mr-2" />
              ) : (
                <XCircleIcon className="h-6 w-6 text-red-500 mr-2" />
              )}
              <span className={`font-medium ${testResult.success ? 'text-green-700' : 'text-red-700'}`}>
                {testResult.success ? 'Test Passed' : 'Test Failed'}
              </span>
              {testResult.duration && (
                <span className="ml-auto text-sm text-slate-500">
                  Duration: {(testResult.duration / 1000).toFixed(2)}s
                </span>
              )}
            </div>
            <p className="mt-2 text-slate-700">{testResult.message}</p>
          </div>
          
          {testResult.details && (
            <div className="mt-4">
              <h3 className="font-medium text-slate-800 mb-2">Details</h3>
              <pre className="bg-slate-50 p-4 rounded-lg overflow-auto text-sm text-slate-700 max-h-96">
                {JSON.stringify(testResult.details, null, 2)}
              </pre>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
