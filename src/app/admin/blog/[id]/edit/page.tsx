'use client';

import React, { useState, FormEvent, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { ArrowLeftIcon, SparklesIcon } from '@heroicons/react/24/outline';
import { BlogPost, BlogPostStatus } from '@/types/blog';
import { useNotification } from '@/contexts/NotificationContext';
import AdvancedRichTextEditor from '@/components/admin/AdvancedRichTextEditor';
import BlogPostGenerator from '@/components/admin/BlogPostGenerator';
import ImageUpload from '@/components/admin/ImageUpload';
import TagsInput from '@/components/admin/TagsInput';

interface EditBlogPostPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default function EditBlogPostPage({ params }: EditBlogPostPageProps) {
  const router = useRouter();
  const { showNotification } = useNotification();

  const [blogId, setBlogId] = useState<string>('');
  const [blogPost, setBlogPost] = useState<BlogPost | null>(null);
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [excerpt, setExcerpt] = useState('');
  const [category, setCategory] = useState('uncategorized');
  const [status, setStatus] = useState<BlogPostStatus>('draft');
  const [author, setAuthor] = useState('');
  const [featuredImage, setFeaturedImage] = useState<string | undefined>('');
  const [tags, setTags] = useState<string[]>([]);
  const [seoTitle, setSeoTitle] = useState('');
  const [seoDescription, setSeoDescription] = useState('');
  const [seoKeywords, setSeoKeywords] = useState<string[]>([]);
  const [scheduledDate, setScheduledDate] = useState('');
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(false);
  const [fetchLoading, setFetchLoading] = useState(true);
  const [categories, setCategories] = useState<Array<{
    id: string;
    name: string;
    slug: string;
  }>>([]);
  const [tagSuggestions, setTagSuggestions] = useState<string[]>([]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setFetchLoading(true);

        // Resolve params Promise
        const resolvedParams = await params;
        const id = resolvedParams.id;
        setBlogId(id);

        // Fetch blog post
        const blogResponse = await fetch(`/api/admin/blog/${id}`, {
          cache: 'no-store',
          headers: {
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache'
          },
          next: { revalidate: 0 }
        });

        if (!blogResponse.ok) {
          throw new Error(`Failed to fetch blog post: ${blogResponse.statusText}`);
        }

        const blogData = await blogResponse.json();
        setBlogPost(blogData);

        // Set form fields
        setTitle(blogData.title);
        setContent(blogData.content);
        setExcerpt(blogData.excerpt || '');
        setCategory(blogData.category);
        setStatus(blogData.status);
        setAuthor(blogData.author || '');
        setFeaturedImage(blogData.featuredImage || '');
        setTags(blogData.tags || []);
        setSeoTitle(blogData.seoTitle || '');
        setSeoDescription(blogData.seoDescription || '');
        setSeoKeywords(blogData.seoKeywords || []);
        
        // Handle scheduled date
        if (blogData.scheduledAt) {
          const scheduledDate = new Date(blogData.scheduledAt);
          const localDateTime = new Date(scheduledDate.getTime() - scheduledDate.getTimezoneOffset() * 60000)
            .toISOString()
            .slice(0, 16);
          setScheduledDate(localDateTime);
        }

        // Fetch blog categories
        const categoriesResponse = await fetch('/api/admin/blog/categories', {
          cache: 'no-store',
          headers: {
            'Cache-Control': 'no-cache, no-store, must-revalidate',
            'Pragma': 'no-cache'
          },
          next: { revalidate: 0 }
        });
        if (categoriesResponse.ok) {
          const categoriesData = await categoriesResponse.json();
          setCategories(categoriesData);
        }

        // Set tag suggestions
        setTagSuggestions([
          'web development', 'design', 'marketing', 'seo', 'branding',
          'ui/ux', 'mobile app', 'e-commerce', 'social media', 'content strategy',
          'digital marketing', 'graphic design', 'wordpress', 'react', 'nextjs'
        ]);
      } catch (err) {
        console.error('Error fetching data:', err);
        showNotification('error', 'Error', 'Failed to load blog post');
        router.push('/admin/blog');
      } finally {
        setFetchLoading(false);
      }
    };

    fetchData();
  }, [params, router, showNotification]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!title.trim()) {
      newErrors.title = 'Title is required';
    }

    if (!content.trim()) {
      newErrors.content = 'Content is required';
    }

    if (!category) {
      newErrors.category = 'Category is required';
    }

    if (!author.trim()) {
      newErrors.author = 'Author is required';
    }

    if (status === 'scheduled' && !scheduledDate) {
      newErrors.scheduledDate = 'Scheduled date is required for scheduled posts';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();

    if (!validateForm() || !blogPost) {
      return;
    }

    setLoading(true);

    try {
      // Update the blog post
      const blogPostData = {
        title,
        content,
        excerpt: excerpt || content.substring(0, 150) + '...',
        category,
        status,
        author,
        featuredImage: featuredImage || undefined,
        tags,
        seoTitle: seoTitle || undefined,
        seoDescription: seoDescription || undefined,
        seoKeywords: seoKeywords.length > 0 ? seoKeywords : undefined,
        scheduledDate: status === 'scheduled' ? scheduledDate : undefined,
      };

      const updateResponse = await fetch(`/api/admin/blog/${blogId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache'
        },
        body: JSON.stringify(blogPostData),
      });

      if (!updateResponse.ok) {
        const errorData = await updateResponse.json();
        throw new Error(errorData.error || 'Failed to update blog post');
      }

      showNotification('success', 'Success', 'Blog post updated successfully');
      router.push('/admin/blog');
      router.refresh(); // Force a refresh of the page
    } catch (error) {
      console.error('Error updating blog post:', error);
      showNotification('error', 'Error', error instanceof Error ? error.message : 'Failed to update blog post');
    } finally {
      setLoading(false);
    }
  };

  if (fetchLoading) {
    return (
      <div className="space-y-8">
        <div className="flex items-center mb-6 mt-2">
          <Link
            href="/admin/blog"
            className="mr-4 p-2 rounded-lg text-slate-500 hover:text-slate-700 hover:bg-slate-100 transition-colors"
          >
            <ArrowLeftIcon className="h-5 w-5" />
          </Link>
          <h1 className="text-2xl font-bold text-slate-800">Edit Blog Post</h1>
        </div>
        <div className="bg-white shadow-sm rounded-lg overflow-hidden border border-gray-200 p-6">
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 rounded w-1/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!blogPost) {
    return (
      <div className="space-y-8">
        <div className="flex items-center mb-6 mt-2">
          <Link
            href="/admin/blog"
            className="mr-4 p-2 rounded-lg text-slate-500 hover:text-slate-700 hover:bg-slate-100 transition-colors"
          >
            <ArrowLeftIcon className="h-5 w-5" />
          </Link>
          <h1 className="text-2xl font-bold text-slate-800">Edit Blog Post</h1>
        </div>
        <div className="bg-white shadow-sm rounded-lg overflow-hidden border border-gray-200 p-6">
          <p className="text-red-500">Blog post not found.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4 sm:space-y-6">
      {/* Page Header */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
            <div className="flex items-center">
              <Link
                href="/admin/blog"
                className="mr-3 sm:mr-4 p-2 rounded-lg text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-colors"
              >
                <ArrowLeftIcon className="h-5 w-5" />
              </Link>
              <div>
                <h1 className="text-xl sm:text-2xl font-bold text-gray-900">Edit Blog Post</h1>
                <p className="text-sm sm:text-base text-gray-600 mt-1">
                  Update your blog article
                </p>
              </div>
            </div>
            <div className="flex-shrink-0">
              <BlogPostGenerator
                onGenerated={(newTitle, newContent, newExcerpt) => {
                  setTitle(newTitle);
                  setContent(newContent);
                  setExcerpt(newExcerpt || newContent.substring(0, 150) + '...');
                }}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6">
          {/* Main Content - Left Column */}
          <div className="lg:col-span-2 space-y-4 sm:space-y-6">
            {/* Basic Information */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900">Basic Information</h2>
              </div>
              <div className="px-4 sm:px-6 py-4 space-y-4">
                {/* Title */}
                <div>
                  <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
                    Title *
                  </label>
                  <input
                    type="text"
                    id="title"
                    name="title"
                    value={title}
                    onChange={(e) => setTitle(e.target.value)}
                    className={`block w-full rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 sm:text-sm ${
                      errors.title ? 'border-red-300' : 'border-gray-300'
                    }`}
                    placeholder="Enter blog post title..."
                  />
                  {errors.title && (
                    <p className="mt-1 text-sm text-red-600">{errors.title}</p>
                  )}
                </div>

                {/* Excerpt */}
                <div>
                  <label htmlFor="excerpt" className="block text-sm font-medium text-gray-700 mb-2">
                    Excerpt
                  </label>
                  <textarea
                    id="excerpt"
                    name="excerpt"
                    rows={3}
                    value={excerpt}
                    onChange={(e) => setExcerpt(e.target.value)}
                    className="block w-full rounded-md border-gray-300 shadow-sm py-2 px-3 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
                    placeholder="Brief description of the post (optional)..."
                  />
                  <p className="mt-1 text-xs text-gray-500">
                    Leave empty to auto-generate from content
                  </p>
                </div>
              </div>
            </div>

            {/* Content */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900">Content</h2>
              </div>
              <div className="px-4 sm:px-6 py-4">
                <AdvancedRichTextEditor
                  content={content}
                  onChange={setContent}
                  placeholder="Edit your blog post content... Use the toolbar for formatting and drag & drop images directly into the editor."
                />
                {errors.content && (
                  <p className="mt-2 text-sm text-red-600">{errors.content}</p>
                )}
              </div>
            </div>

            {/* SEO Settings */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900">SEO Settings</h2>
              </div>
              <div className="px-4 sm:px-6 py-4 space-y-4">
                {/* SEO Title */}
                <div>
                  <label htmlFor="seoTitle" className="block text-sm font-medium text-gray-700 mb-2">
                    SEO Title
                  </label>
                  <input
                    type="text"
                    id="seoTitle"
                    name="seoTitle"
                    value={seoTitle}
                    onChange={(e) => setSeoTitle(e.target.value)}
                    placeholder="Leave empty to use post title"
                    className="block w-full rounded-md border-gray-300 shadow-sm py-2 px-3 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
                  />
                </div>

                {/* SEO Description */}
                <div>
                  <label htmlFor="seoDescription" className="block text-sm font-medium text-gray-700 mb-2">
                    SEO Description
                  </label>
                  <textarea
                    id="seoDescription"
                    name="seoDescription"
                    rows={2}
                    value={seoDescription}
                    onChange={(e) => setSeoDescription(e.target.value)}
                    placeholder="Leave empty to use post excerpt"
                    className="block w-full rounded-md border-gray-300 shadow-sm py-2 px-3 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
                  />
                </div>

                {/* SEO Keywords */}
                <TagsInput
                  value={seoKeywords}
                  onChange={(keywords) => setSeoKeywords(keywords)}
                  label="SEO Keywords"
                  placeholder="Add SEO keywords..."
                  maxTags={15}
                />
              </div>
            </div>
          </div>

          {/* Sidebar - Right Column */}
          <div className="space-y-4 sm:space-y-6">
            {/* Publishing Options */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900">Publishing</h2>
              </div>
              <div className="px-4 sm:px-6 py-4 space-y-4">
                {/* Status */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    Status
                  </label>
                  <div className="space-y-2">
                    <div className="flex items-center">
                      <input
                        id="draft"
                        name="status"
                        type="radio"
                        value="draft"
                        checked={status === 'draft'}
                        onChange={() => setStatus('draft')}
                        className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300"
                      />
                      <label htmlFor="draft" className="ml-2 block text-sm text-gray-700">
                        Draft
                      </label>
                    </div>
                    <div className="flex items-center">
                      <input
                        id="published"
                        name="status"
                        type="radio"
                        value="published"
                        checked={status === 'published'}
                        onChange={() => setStatus('published')}
                        className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300"
                      />
                      <label htmlFor="published" className="ml-2 block text-sm text-gray-700">
                        Published
                      </label>
                    </div>
                    <div className="flex items-center">
                      <input
                        id="scheduled"
                        name="status"
                        type="radio"
                        value="scheduled"
                        checked={status === 'scheduled'}
                        onChange={() => setStatus('scheduled')}
                        className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300"
                      />
                      <label htmlFor="scheduled" className="ml-2 block text-sm text-gray-700">
                        Scheduled
                      </label>
                    </div>
                  </div>
                </div>

                {/* Scheduled Date */}
                {status === 'scheduled' && (
                  <div>
                    <label htmlFor="scheduledDate" className="block text-sm font-medium text-gray-700 mb-2">
                      Scheduled Date
                    </label>
                    <input
                      type="datetime-local"
                      id="scheduledDate"
                      name="scheduledDate"
                      value={scheduledDate}
                      onChange={(e) => setScheduledDate(e.target.value)}
                      className="block w-full rounded-md border-gray-300 shadow-sm py-2 px-3 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
                    />
                  </div>
                )}

                {/* Category */}
                <div>
                  <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-2">
                    Category
                  </label>
                  <select
                    id="category"
                    name="category"
                    value={category}
                    onChange={(e) => setCategory(e.target.value)}
                    className="block w-full rounded-md border-gray-300 shadow-sm py-2 px-3 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
                  >
                    {categories.map((cat) => (
                      <option key={cat.id} value={cat.slug}>
                        {cat.name}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Author */}
                <div>
                  <label htmlFor="author" className="block text-sm font-medium text-gray-700 mb-2">
                    Author
                  </label>
                  <input
                    type="text"
                    id="author"
                    name="author"
                    value={author}
                    onChange={(e) => setAuthor(e.target.value)}
                    className="block w-full rounded-md border-gray-300 shadow-sm py-2 px-3 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
                  />
                </div>
              </div>
            </div>

            {/* Featured Image */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900">Featured Image</h2>
              </div>
              <div className="px-4 sm:px-6 py-4">
                <ImageUpload
                  value={featuredImage || ''}
                  onChange={setFeaturedImage}
                  label=""
                />
              </div>
            </div>

            {/* Tags */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900">Tags</h2>
              </div>
              <div className="px-4 sm:px-6 py-4">
                <TagsInput
                  value={tags}
                  onChange={setTags}
                  suggestions={tagSuggestions}
                  label=""
                  placeholder="Add tags..."
                  maxTags={10}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Form Actions */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 sm:px-6 py-4">
            <div className="flex flex-col sm:flex-row justify-between items-center space-y-3 sm:space-y-0">
              <Link
                href="/admin/blog"
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
              >
                Cancel
              </Link>
              <div className="flex space-x-3">
                <button
                  type="button"
                  onClick={() => setStatus('draft')}
                  disabled={loading}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:opacity-50"
                >
                  Save as Draft
                </button>
                <button
                  type="submit"
                  disabled={loading}
                  className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:opacity-50"
                >
                  {loading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Updating...
                    </>
                  ) : (
                    <>
                      {status === 'published' ? 'Update & Publish' : status === 'scheduled' ? 'Update & Schedule' : 'Update Draft'}
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  );
}
