'use client';
import React from 'react';

import { useState, FormEvent, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { ArrowLeftIcon, SparklesIcon } from '@heroicons/react/24/outline';
import { BlogPostFormData } from '@/types/blog';
import { useNotification } from '@/contexts/NotificationContext';
import AdvancedRichTextEditor from '@/components/admin/AdvancedRichTextEditor';
import BlogPostGenerator from '@/components/admin/BlogPostGenerator';
import ImageUpload from '@/components/admin/ImageUpload';
import TagsInput from '@/components/admin/TagsInput';

export default function NewBlogPostPage() {
  const router = useRouter();
  const { showNotification } = useNotification();

  const [formData, setFormData] = useState<BlogPostFormData>({
    title: '',
    content: '',
    excerpt: '',
    category: 'uncategorized',
    status: 'draft',
    author: 'Admin',
    featuredImage: '',
    tags: [],
    seoTitle: '',
    seoDescription: '',
    seoKeywords: [],
    scheduledDate: ''
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [loading, setLoading] = useState(false);
  const [categories, setCategories] = useState<Array<{
    id: string;
    name: string;
    slug: string;
  }>>([]);
  const [tagSuggestions, setTagSuggestions] = useState<string[]>([]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch blog categories
        const categoriesResponse = await fetch('/api/admin/blog/categories');
        if (categoriesResponse.ok) {
          const categoriesData = await categoriesResponse.json();
          setCategories(categoriesData);

          // If we have categories and no category is selected yet, select the first one
          if (categoriesData.length > 0 && !formData.category) {
            setFormData(prev => ({ ...prev, category: categoriesData[0].slug }));
          }
        }

        // Fetch popular tags for suggestions
        const tagsResponse = await fetch('/api/admin/tags');
        if (tagsResponse.ok) {
          const tagsData = await tagsResponse.json();
          setTagSuggestions(tagsData.map((tag: any) => tag.name));
        }
      } catch (error) {
        console.error('Error fetching data:', error);
      }
    };

    fetchData();
  }, []);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear error for this field if it exists
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  const handleContentChange = (content: string) => {
    setFormData(prev => ({ ...prev, content }));

    // Clear error for content if it exists
    if (errors.content) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors.content;
        return newErrors;
      });
    }
  };

  const handleStatusChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({ ...prev, status: e.target.value as 'draft' | 'published' | 'scheduled' }));
  };

  const handleImageChange = (url: string | undefined) => {
    setFormData(prev => ({ ...prev, featuredImage: url || '' }));
  };

  const handleTagsChange = (tags: string[]) => {
    setFormData(prev => ({ ...prev, tags }));
  };

  const handleSeoKeywordsChange = (keywords: string[]) => {
    setFormData(prev => ({ ...prev, seoKeywords: keywords }));
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Title is required';
    }

    if (!formData.content.trim()) {
      newErrors.content = 'Content is required';
    }

    if (!formData.category) {
      newErrors.category = 'Category is required';
    }

    if (!formData.author.trim()) {
      newErrors.author = 'Author is required';
    }

    if (formData.status === 'scheduled' && !formData.scheduledDate) {
      newErrors.scheduledDate = 'Scheduled date is required for scheduled posts';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      // Create the blog post
      const blogPostData = {
        title: formData.title,
        content: formData.content,
        excerpt: formData.excerpt || formData.content.substring(0, 150) + '...',
        category: formData.category,
        status: formData.status,
        author: formData.author,
        featuredImage: formData.featuredImage,
        tags: formData.tags,
        seoTitle: formData.seoTitle,
        seoDescription: formData.seoDescription,
        seoKeywords: formData.seoKeywords,
        scheduledDate: formData.scheduledDate
      };

      const createResponse = await fetch('/api/admin/blog', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(blogPostData),
      });

      if (!createResponse.ok) {
        const errorData = await createResponse.json();
        throw new Error(errorData.error || 'Failed to create blog post');
      }

      showNotification('success', 'Success', 'Blog post created successfully');
      router.push('/admin/blog');
    } catch (error) {
      console.error('Error creating blog post:', error);
      showNotification('error', 'Error', error instanceof Error ? error.message : 'Failed to create blog post');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-4 sm:space-y-6">
      {/* Page Header */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
            <div className="flex items-center">
              <Link
                href="/admin/blog"
                className="mr-3 sm:mr-4 p-2 rounded-lg text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-colors"
              >
                <ArrowLeftIcon className="h-5 w-5" />
              </Link>
              <div>
                <h1 className="text-xl sm:text-2xl font-bold text-gray-900">Create Blog Post</h1>
                <p className="text-sm sm:text-base text-gray-600 mt-1">
                  Write and publish a new blog article
                </p>
              </div>
            </div>
            <div className="flex-shrink-0">
              <BlogPostGenerator
                onGenerated={(title, content, excerpt) => {
                  setFormData(prev => ({
                    ...prev,
                    title,
                    content,
                    excerpt: excerpt || content.substring(0, 150) + '...'
                  }));
                }}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Form */}
      <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6">
          {/* Main Content - Left Column */}
          <div className="lg:col-span-2 space-y-4 sm:space-y-6">
            {/* Basic Information */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900">Basic Information</h2>
              </div>
              <div className="px-4 sm:px-6 py-4 space-y-4">
                {/* Title */}
                <div>
                  <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
                    Title *
                  </label>
                  <input
                    type="text"
                    id="title"
                    name="title"
                    value={formData.title}
                    onChange={handleInputChange}
                    className={`block w-full rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 sm:text-sm ${
                      errors.title ? 'border-red-300' : 'border-gray-300'
                    }`}
                    placeholder="Enter blog post title..."
                  />
                  {errors.title && (
                    <p className="mt-1 text-sm text-red-600">{errors.title}</p>
                  )}
                </div>

                {/* Excerpt */}
                <div>
                  <label htmlFor="excerpt" className="block text-sm font-medium text-gray-700 mb-2">
                    Excerpt
                  </label>
                  <textarea
                    id="excerpt"
                    name="excerpt"
                    rows={3}
                    value={formData.excerpt}
                    onChange={handleInputChange}
                    className="block w-full rounded-md border-gray-300 shadow-sm py-2 px-3 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
                    placeholder="Brief description of the post (optional)..."
                  />
                  <p className="mt-1 text-xs text-gray-500">
                    Leave empty to auto-generate from content
                  </p>
                </div>
              </div>
            </div>

            {/* Content */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900">Content</h2>
              </div>
              <div className="px-4 sm:px-6 py-4">
                                                 <AdvancedRichTextEditor
                  content={formData.content}
                  onChange={handleContentChange}
                  placeholder="Write your awesome blog post... Use the toolbar above for formatting and drag & drop images directly into the editor."
                />
                {errors.content && (
                  <p className="mt-2 text-sm text-red-600">{errors.content}</p>
                )}
              </div>
            </div>

            {/* SEO Settings */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900">SEO Settings</h2>
              </div>
              <div className="px-4 sm:px-6 py-4 space-y-4">
                {/* SEO Title */}
                <div>
                  <label htmlFor="seoTitle" className="block text-sm font-medium text-gray-700 mb-2">
                    SEO Title
                  </label>
                  <input
                    type="text"
                    id="seoTitle"
                    name="seoTitle"
                    value={formData.seoTitle || ''}
                    onChange={handleInputChange}
                    placeholder="Leave empty to use post title"
                    className="block w-full rounded-md border-gray-300 shadow-sm py-2 px-3 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
                  />
                </div>

                {/* SEO Description */}
                <div>
                  <label htmlFor="seoDescription" className="block text-sm font-medium text-gray-700 mb-2">
                    SEO Description
                  </label>
                  <textarea
                    id="seoDescription"
                    name="seoDescription"
                    rows={2}
                    value={formData.seoDescription || ''}
                    onChange={handleInputChange}
                    placeholder="Leave empty to use post excerpt"
                    className="block w-full rounded-md border-gray-300 shadow-sm py-2 px-3 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
                  />
                </div>

                {/* SEO Keywords */}
                <TagsInput
                  value={formData.seoKeywords || []}
                  onChange={handleSeoKeywordsChange}
                  label="SEO Keywords"
                  placeholder="Add SEO keywords..."
                  maxTags={15}
                />
              </div>
            </div>
          </div>

          {/* Sidebar - Right Column */}
          <div className="space-y-4 sm:space-y-6">
            {/* Publishing Options */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900">Publishing</h2>
              </div>
              <div className="px-4 sm:px-6 py-4 space-y-4">
                {/* Status */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    Status
                  </label>
                  <div className="space-y-2">
                    <div className="flex items-center">
                      <input
                        id="draft"
                        name="status"
                        type="radio"
                        value="draft"
                        checked={formData.status === 'draft'}
                        onChange={handleStatusChange}
                        className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300"
                      />
                      <label htmlFor="draft" className="ml-2 block text-sm text-gray-700">
                        Draft
                      </label>
                    </div>
                    <div className="flex items-center">
                      <input
                        id="published"
                        name="status"
                        type="radio"
                        value="published"
                        checked={formData.status === 'published'}
                        onChange={handleStatusChange}
                        className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300"
                      />
                      <label htmlFor="published" className="ml-2 block text-sm text-gray-700">
                        Published
                      </label>
                    </div>
                    <div className="flex items-center">
                      <input
                        id="scheduled"
                        name="status"
                        type="radio"
                        value="scheduled"
                        checked={formData.status === 'scheduled'}
                        onChange={handleStatusChange}
                        className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300"
                      />
                      <label htmlFor="scheduled" className="ml-2 block text-sm text-gray-700">
                        Scheduled
                      </label>
                    </div>
                  </div>
                </div>

                {/* Scheduled Date */}
                {formData.status === 'scheduled' && (
                  <div>
                    <label htmlFor="scheduledDate" className="block text-sm font-medium text-gray-700 mb-2">
                      Scheduled Date
                    </label>
                    <input
                      type="datetime-local"
                      id="scheduledDate"
                      name="scheduledDate"
                      value={formData.scheduledDate || ''}
                      onChange={handleInputChange}
                      className="block w-full rounded-md border-gray-300 shadow-sm py-2 px-3 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
                    />
                  </div>
                )}

                {/* Category */}
                <div>
                  <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-2">
                    Category
                  </label>
                  <select
                    id="category"
                    name="category"
                    value={formData.category}
                    onChange={handleInputChange}
                    className="block w-full rounded-md border-gray-300 shadow-sm py-2 px-3 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
                  >
                    {categories.map((cat) => (
                      <option key={cat.id} value={cat.slug}>
                        {cat.name}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Author */}
                <div>
                  <label htmlFor="author" className="block text-sm font-medium text-gray-700 mb-2">
                    Author
                  </label>
                  <input
                    type="text"
                    id="author"
                    name="author"
                    value={formData.author}
                    onChange={handleInputChange}
                    className="block w-full rounded-md border-gray-300 shadow-sm py-2 px-3 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
                  />
                </div>
              </div>
            </div>

            {/* Featured Image */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900">Featured Image</h2>
              </div>
              <div className="px-4 sm:px-6 py-4">
                                                 <ImageUpload
                  value={formData.featuredImage || ''}
                  onChange={handleImageChange}
                  label=""
                />
              </div>
            </div>

            {/* Tags */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900">Tags</h2>
              </div>
              <div className="px-4 sm:px-6 py-4">
                <TagsInput
                  value={formData.tags}
                  onChange={handleTagsChange}
                  suggestions={tagSuggestions}
                  label=""
                  placeholder="Add tags..."
                  maxTags={10}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Form Actions */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 sm:px-6 py-4">
            <div className="flex flex-col sm:flex-row justify-between items-center space-y-3 sm:space-y-0">
              <Link
                href="/admin/blog"
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
              >
                Cancel
              </Link>
              <div className="flex space-x-3">
                <button
                  type="button"
                  onClick={() => setFormData(prev => ({ ...prev, status: 'draft' }))}
                  disabled={loading}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:opacity-50"
                >
                  Save as Draft
                </button>
                <button
                  type="submit"
                  disabled={loading}
                  className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:opacity-50"
                >
                  {loading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Creating...
                    </>
                  ) : (
                    <>
                      {formData.status === 'published' ? 'Publish' : formData.status === 'scheduled' ? 'Schedule' : 'Save Draft'}
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  );
}
