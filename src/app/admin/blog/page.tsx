'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import {
  PlusIcon,
  ArrowPathIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  CheckCircleIcon,
  ClockIcon,
  DocumentTextIcon,
  XCircleIcon,
} from '@heroicons/react/24/outline';
import { BlogPost } from '@/types/blog';
import { useNotification } from '@/contexts/NotificationContext';
import AdminCard, { AdminCardList } from '@/components/admin/AdminCard';
import ConfirmDialog from '@/components/admin/ConfirmDialog';
import Image from 'next/image';

export default function BlogPage() {
  const [blogPosts, setBlogPosts] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [deletePostId, setDeletePostId] = useState<string | null>(null);
  const [loadingOperations, setLoadingOperations] = useState<Set<string>>(new Set());
  const { showNotification } = useNotification();

  // Helper to manage loading states
  const setOperationLoading = (operation: string, isLoading: boolean) => {
    setLoadingOperations(prev => {
      const newSet = new Set(prev);
      if (isLoading) {
        newSet.add(operation);
      } else {
        newSet.delete(operation);
      }
      return newSet;
    });
  };

  const fetchBlogPosts = async () => {
    try {
      setLoading(true);
      setError('');

      const response = await fetch('/api/admin/blog', {
        cache: 'no-store',
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache'
        },
        next: { revalidate: 0 }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch blog posts: ${response.statusText}`);
      }

      const data = await response.json();
      setBlogPosts(data);
    } catch (err) {
      console.error('Error fetching blog posts:', err);
      setError('Failed to load blog posts. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const initializePage = async () => {
      // Fetch CSRF token first
      // Then fetch blog posts
      await fetchBlogPosts();
    };
    
    initializePage();
  }, []);

  // Add a refresh interval
  useEffect(() => {
    const interval = setInterval(() => {
      fetchBlogPosts();
    }, 30000); // Refresh every 30 seconds

    return () => clearInterval(interval);
  }, []);

  const handleDeleteClick = (id: string) => {
    setDeletePostId(id);
  };

  const handleDeleteConfirm = async () => {
    if (!deletePostId) return;

    // Store the original post for potential rollback
    const postToDelete = blogPosts.find(p => p.id === deletePostId);
    if (!postToDelete) return;

    try {
      // Set loading state
      setOperationLoading(`delete-${deletePostId}`, true);

      // Clear any existing errors
      setError('');

      const response = await fetch(`/api/admin/blog/${deletePostId}`, {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
      });

      if (!response.ok) {
        throw new Error(`Failed to delete blog post: ${response.statusText}`);
      }

      // Success - remove the post from the UI immediately
      setBlogPosts(prevPosts => prevPosts.filter(post => post.id !== deletePostId));
      showNotification('success', 'Success', 'Blog post deleted successfully');
    } catch (err) {
      console.error('Error deleting blog post:', err);
      showNotification('error', 'Error', 'Failed to delete blog post. Please try again.');
    } finally {
      // Clear loading state
      setOperationLoading(`delete-${deletePostId}`, false);
      setDeletePostId(null);
    }
  };

  const handleDeleteCancel = () => {
    setDeletePostId(null);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  return (
    <div className="space-y-4 sm:space-y-6">
      {/* Page Header */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
            <div>
              <h1 className="text-xl sm:text-2xl font-bold text-gray-900">Blog Posts</h1>
              <p className="text-sm sm:text-base text-gray-600 mt-1">
                Manage your blog content and articles
              </p>
            </div>
            <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
              <button
                onClick={fetchBlogPosts}
                disabled={loading}
                className="inline-flex items-center justify-center px-3 sm:px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
              >
                <ArrowPathIcon className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                Refresh
              </button>
              <Link
                href="/admin/blog/new"
                className="inline-flex items-center justify-center px-3 sm:px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
              >
                <PlusIcon className="h-4 w-4 mr-2" />
                New Post
              </Link>
            </div>
          </div>
        </div>

        {/* Summary Stats */}
        <div className="px-4 sm:px-6 py-4 bg-gray-50 border-b border-gray-200">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3 sm:gap-4">
            <div className="text-center">
              <div className="text-lg sm:text-2xl font-bold text-orange-600">{blogPosts.length}</div>
              <div className="text-xs sm:text-sm text-gray-600">Total Posts</div>
            </div>
            <div className="text-center">
              <div className="text-lg sm:text-2xl font-bold text-green-600">
                {blogPosts.filter(p => p.status === 'published').length}
              </div>
              <div className="text-xs sm:text-sm text-gray-600">Published</div>
            </div>
            <div className="text-center">
              <div className="text-lg sm:text-2xl font-bold text-yellow-600">
                {blogPosts.filter(p => p.status === 'draft').length}
              </div>
              <div className="text-xs sm:text-sm text-gray-600">Drafts</div>
            </div>
            <div className="text-center">
              <div className="text-lg sm:text-2xl font-bold text-gray-900">
                {new Set(blogPosts.map(p => p.category)).size}
              </div>
              <div className="text-xs sm:text-sm text-gray-600">Categories</div>
            </div>
          </div>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <XCircleIcon className="h-5 w-5 text-red-400" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error Loading Posts</h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{error}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Blog Posts Display */}
      <div className="bg-white shadow rounded-lg overflow-hidden">
        {loading && blogPosts.length === 0 ? (
          <div className="px-4 sm:px-6 py-12 text-center text-gray-500">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-600 mx-auto mb-4"></div>
            <p className="text-base font-medium">Loading blog posts...</p>
          </div>
        ) : blogPosts.length === 0 ? (
          <div className="px-4 sm:px-6 py-12 text-center text-gray-500">
            <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-300 mb-4" />
            <p className="text-base font-medium">No blog posts found</p>
            <p className="text-sm mt-2">
              <Link
                href="/admin/blog/new"
                className="text-orange-600 hover:text-orange-500"
              >
                Create your first blog post
              </Link>
            </p>
          </div>
        ) : (
          <>
            {/* Mobile Card View */}
            <div className="block sm:hidden divide-y divide-gray-200">
              {blogPosts.map((post) => (
                <div key={post.id} className="px-4 py-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {post.title}
                      </p>
                      <p className="text-xs text-gray-500">
                        {post.category} • {formatDate(post.createdAt)}
                      </p>
                    </div>
                    <div className="ml-3">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        post.status === 'published'
                          ? 'bg-green-100 text-green-800'
                          : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {post.status === 'published' ? 'Published' : 'Draft'}
                      </span>
                    </div>
                  </div>
                  
                  {post.excerpt && (
                    <div className="mb-3">
                      <p className="text-xs text-gray-500 line-clamp-2">{post.excerpt}</p>
                    </div>
                  )}
                  
                  <div className="flex flex-wrap gap-2">
                    <Link
                      href={`/blog/${post.slug}`}
                      target="_blank"
                      className="inline-flex items-center px-2.5 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
                    >
                      <EyeIcon className="h-3 w-3 mr-1" />
                      View
                    </Link>
                    <Link
                      href={`/admin/blog/${post.id}/edit`}
                      className="inline-flex items-center px-2.5 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
                    >
                      <PencilIcon className="h-3 w-3 mr-1" />
                      Edit
                    </Link>
                    <button
                      onClick={() => handleDeleteClick(post.id)}
                      disabled={loadingOperations.has(`delete-${post.id}`)}
                      className="inline-flex items-center px-2.5 py-1.5 border border-red-300 shadow-sm text-xs font-medium rounded text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
                    >
                      {loadingOperations.has(`delete-${post.id}`) ? (
                        <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-red-600 mr-1"></div>
                      ) : (
                        <TrashIcon className="h-3 w-3 mr-1" />
                      )}
                      {loadingOperations.has(`delete-${post.id}`) ? 'Deleting...' : 'Delete'}
                    </button>
                  </div>
                </div>
              ))}
            </div>

            {/* Desktop Table View */}
            <div className="hidden sm:block overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Title
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Category
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Date
                    </th>
                    <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {blogPosts.map((post) => (
                    <tr key={post.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10">
                            <div className="h-10 w-10 rounded-lg bg-gray-200 flex items-center justify-center">
                              <DocumentTextIcon className="h-5 w-5 text-gray-400" />
                            </div>
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900 max-w-xs truncate">
                              {post.title}
                            </div>
                            {post.excerpt && (
                              <div className="text-sm text-gray-500 max-w-xs truncate">
                                {post.excerpt}
                              </div>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          post.status === 'published'
                            ? 'bg-green-100 text-green-800'
                            : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {post.status === 'published' ? 'Published' : 'Draft'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {post.category}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatDate(post.createdAt)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex justify-end gap-2">
                          <Link
                            href={`/blog/${post.slug}`}
                            target="_blank"
                            className="inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
                          >
                            <EyeIcon className="h-3 w-3 mr-1" />
                            View
                          </Link>
                          <Link
                            href={`/admin/blog/${post.id}/edit`}
                            className="inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
                          >
                            <PencilIcon className="h-3 w-3 mr-1" />
                            Edit
                          </Link>
                          <button
                            onClick={() => handleDeleteClick(post.id)}
                            disabled={loadingOperations.has(`delete-${post.id}`)}
                            className="inline-flex items-center px-3 py-1.5 border border-red-300 shadow-sm text-xs font-medium rounded text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
                          >
                            {loadingOperations.has(`delete-${post.id}`) ? (
                              <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-red-600 mr-1"></div>
                            ) : (
                              <TrashIcon className="h-3 w-3 mr-1" />
                            )}
                            {loadingOperations.has(`delete-${post.id}`) ? 'Deleting...' : 'Delete'}
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </>
        )}
      </div>

      {/* Delete Confirmation Modal */}
      <ConfirmDialog
        isOpen={deletePostId !== null}
        onConfirm={handleDeleteConfirm}
        onClose={handleDeleteCancel}
        title="Delete Blog Post"
        message="Are you sure you want to delete this blog post? This action cannot be undone."
        confirmText="Delete"
        cancelText="Cancel"
      />
    </div>
  );
}
