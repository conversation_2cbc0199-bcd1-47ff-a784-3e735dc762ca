'use client';

import { useState, useEffect } from 'react';
import { PlusIcon, PencilIcon, TrashIcon } from '@heroicons/react/24/outline';

interface PaperType {
  id: string;
  name: string;
  grammage: string;
  oneSidedPrice: number;
  twoSidedPrice: number;
  category: string;
  active: boolean;
  order: number;
}

interface Product {
  id: number;
  service: string;
  price: number;
  pricingType: string;
  unitType?: string;
  pricePerMeter?: number;
  minQuantity?: number;
  maxQuantity?: number;
  minMeters?: number;
  maxMeters?: number;
}

export default function PricingManagementPage() {
  const [activeTab, setActiveTab] = useState<'paper-types' | 'products'>('paper-types');
  const [paperTypes, setPaperTypes] = useState<PaperType[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [showPaperTypeForm, setShowPaperTypeForm] = useState(false);
  const [editingPaperType, setEditingPaperType] = useState<PaperType | null>(null);
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);

  // Paper type form state
  const [paperTypeForm, setPaperTypeForm] = useState({
    name: '',
    grammage: '',
    oneSidedPrice: '',
    twoSidedPrice: '',
    category: 'paper',
    active: true,
    order: 0
  });

  // Product form state
  const [productForm, setProductForm] = useState({
    pricingType: 'fixed',
    unitType: '',
    pricePerMeter: '',
    minQuantity: '',
    maxQuantity: '',
    minMeters: '',
    maxMeters: ''
  });

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      
      // Fetch paper types
      const paperTypesResponse = await fetch('/api/admin/paper-types');
      if (paperTypesResponse.ok) {
        const paperTypesData = await paperTypesResponse.json();
        setPaperTypes(paperTypesData);
      }

      // Fetch products
      const productsResponse = await fetch('/api/catalogue');
      if (productsResponse.ok) {
        const productsData = await productsResponse.json();
        setProducts(productsData);
      }
    } catch (error) {
      console.error('Error fetching data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handlePaperTypeSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      const method = editingPaperType ? 'PUT' : 'POST';
      const body = editingPaperType 
        ? { ...paperTypeForm, id: editingPaperType.id }
        : paperTypeForm;

      const response = await fetch('/api/admin/paper-types', {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(body)
      });

      if (response.ok) {
        await fetchData();
        resetPaperTypeForm();
      }
    } catch (error) {
      console.error('Error saving paper type:', error);
    }
  };

  const handleDeletePaperType = async (id: string) => {
    if (!confirm('Are you sure you want to delete this paper type?')) return;

    try {
      const response = await fetch(`/api/admin/paper-types?id=${id}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        await fetchData();
      }
    } catch (error) {
      console.error('Error deleting paper type:', error);
    }
  };

  const handleProductPricingUpdate = async (productId: number) => {
    try {
      const response = await fetch(`/api/catalogue/${productId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          pricingType: productForm.pricingType,
          unitType: productForm.unitType || null,
          pricePerMeter: productForm.pricePerMeter ? parseFloat(productForm.pricePerMeter) : null,
          minQuantity: productForm.minQuantity ? parseInt(productForm.minQuantity) : null,
          maxQuantity: productForm.maxQuantity ? parseInt(productForm.maxQuantity) : null,
          minMeters: productForm.minMeters ? parseFloat(productForm.minMeters) : null,
          maxMeters: productForm.maxMeters ? parseFloat(productForm.maxMeters) : null
        })
      });

      if (response.ok) {
        await fetchData();
        setEditingProduct(null);
      }
    } catch (error) {
      console.error('Error updating product pricing:', error);
    }
  };

  const resetPaperTypeForm = () => {
    setPaperTypeForm({
      name: '',
      grammage: '',
      oneSidedPrice: '',
      twoSidedPrice: '',
      category: 'paper',
      active: true,
      order: 0
    });
    setEditingPaperType(null);
    setShowPaperTypeForm(false);
  };

  const editPaperType = (paperType: PaperType) => {
    setPaperTypeForm({
      name: paperType.name,
      grammage: paperType.grammage,
      oneSidedPrice: paperType.oneSidedPrice.toString(),
      twoSidedPrice: paperType.twoSidedPrice.toString(),
      category: paperType.category,
      active: paperType.active,
      order: paperType.order
    });
    setEditingPaperType(paperType);
    setShowPaperTypeForm(true);
  };

  const editProduct = (product: Product) => {
    setProductForm({
      pricingType: product.pricingType || 'fixed',
      unitType: product.unitType || '',
      pricePerMeter: product.pricePerMeter?.toString() || '',
      minQuantity: product.minQuantity?.toString() || '',
      maxQuantity: product.maxQuantity?.toString() || '',
      minMeters: product.minMeters?.toString() || '',
      maxMeters: product.maxMeters?.toString() || ''
    });
    setEditingProduct(product);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Pricing Management</h1>
        <p className="text-gray-600">Manage paper types and product pricing configurations</p>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('paper-types')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'paper-types'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Paper Types
          </button>
          <button
            onClick={() => setActiveTab('products')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'products'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Product Pricing
          </button>
        </nav>
      </div>

      {/* Paper Types Tab */}
      {activeTab === 'paper-types' && (
        <div>
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-lg font-semibold">Paper Types</h2>
            <button
              onClick={() => setShowPaperTypeForm(true)}
              className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center gap-2"
            >
              <PlusIcon className="h-4 w-4" />
              Add Paper Type
            </button>
          </div>

          {/* Paper Types List */}
          <div className="bg-white rounded-lg shadow overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Paper Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Grammage
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    1-Sided Price
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    2-Sided Price
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {paperTypes.map((paperType) => (
                  <tr key={paperType.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {paperType.name}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {paperType.grammage}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      KSh {paperType.oneSidedPrice}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      KSh {paperType.twoSidedPrice}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        paperType.active 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {paperType.active ? 'Active' : 'Inactive'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button
                        onClick={() => editPaperType(paperType)}
                        className="text-blue-600 hover:text-blue-900 mr-3"
                      >
                        <PencilIcon className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleDeletePaperType(paperType.id)}
                        className="text-red-600 hover:text-red-900"
                      >
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Paper Type Form Modal */}
          {showPaperTypeForm && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
              <div className="bg-white rounded-lg p-6 w-full max-w-md">
                <h3 className="text-lg font-semibold mb-4">
                  {editingPaperType ? 'Edit Paper Type' : 'Add Paper Type'}
                </h3>
                <form onSubmit={handlePaperTypeSubmit} className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Paper Type Name
                    </label>
                    <input
                      type="text"
                      value={paperTypeForm.name}
                      onChange={(e) => setPaperTypeForm(prev => ({ ...prev, name: e.target.value }))}
                      className="w-full border border-gray-300 rounded px-3 py-2"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Grammage
                    </label>
                    <input
                      type="text"
                      value={paperTypeForm.grammage}
                      onChange={(e) => setPaperTypeForm(prev => ({ ...prev, grammage: e.target.value }))}
                      className="w-full border border-gray-300 rounded px-3 py-2"
                      placeholder="e.g., 130G"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      1-Sided Price (KSh)
                    </label>
                    <input
                      type="number"
                      value={paperTypeForm.oneSidedPrice}
                      onChange={(e) => setPaperTypeForm(prev => ({ ...prev, oneSidedPrice: e.target.value }))}
                      className="w-full border border-gray-300 rounded px-3 py-2"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      2-Sided Price (KSh)
                    </label>
                    <input
                      type="number"
                      value={paperTypeForm.twoSidedPrice}
                      onChange={(e) => setPaperTypeForm(prev => ({ ...prev, twoSidedPrice: e.target.value }))}
                      className="w-full border border-gray-300 rounded px-3 py-2"
                      required
                    />
                  </div>
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="active"
                      checked={paperTypeForm.active}
                      onChange={(e) => setPaperTypeForm(prev => ({ ...prev, active: e.target.checked }))}
                      className="mr-2"
                    />
                    <label htmlFor="active" className="text-sm text-gray-700">
                      Active
                    </label>
                  </div>
                  <div className="flex gap-3 pt-4">
                    <button
                      type="submit"
                      className="flex-1 bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded"
                    >
                      {editingPaperType ? 'Update' : 'Create'}
                    </button>
                    <button
                      type="button"
                      onClick={resetPaperTypeForm}
                      className="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 py-2 px-4 rounded"
                    >
                      Cancel
                    </button>
                  </div>
                </form>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Products Tab */}
      {activeTab === 'products' && (
        <div>
          <div className="mb-6">
            <h2 className="text-lg font-semibold">Product Pricing Configuration</h2>
            <p className="text-sm text-gray-600">Configure pricing types for each product</p>
          </div>

          <div className="bg-white rounded-lg shadow overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Product
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Base Price
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Pricing Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Configuration
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {products.map((product) => (
                  <tr key={product.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {product.service}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      KSh {product.price.toLocaleString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                        product.pricingType === 'paper_print' ? 'bg-blue-100 text-blue-800' :
                        product.pricingType === 'banner_meter' ? 'bg-green-100 text-green-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {product.pricingType === 'paper_print' ? 'Paper Print' :
                         product.pricingType === 'banner_meter' ? 'Banner/Meter' :
                         'Fixed Price'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {product.pricingType === 'banner_meter' && product.pricePerMeter && (
                        <span>KSh {product.pricePerMeter}/meter</span>
                      )}
                      {product.minQuantity && (
                        <span>Min: {product.minQuantity}</span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button
                        onClick={() => editProduct(product)}
                        className="text-blue-600 hover:text-blue-900"
                      >
                        <PencilIcon className="h-4 w-4" />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Product Pricing Form Modal */}
          {editingProduct && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
              <div className="bg-white rounded-lg p-6 w-full max-w-lg">
                <h3 className="text-lg font-semibold mb-4">
                  Configure Pricing: {editingProduct.service}
                </h3>
                <form onSubmit={(e) => { e.preventDefault(); handleProductPricingUpdate(editingProduct.id); }} className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Pricing Type
                    </label>
                    <select
                      value={productForm.pricingType}
                      onChange={(e) => setProductForm(prev => ({ ...prev, pricingType: e.target.value }))}
                      className="w-full border border-gray-300 rounded px-3 py-2"
                    >
                      <option value="fixed">Fixed Price</option>
                      <option value="paper_print">Paper Print</option>
                      <option value="banner_meter">Banner/Meter Based</option>
                      <option value="custom">Custom</option>
                    </select>
                  </div>

                  {productForm.pricingType === 'banner_meter' && (
                    <>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Price per Meter (KSh)
                        </label>
                        <input
                          type="number"
                          value={productForm.pricePerMeter}
                          onChange={(e) => setProductForm(prev => ({ ...prev, pricePerMeter: e.target.value }))}
                          className="w-full border border-gray-300 rounded px-3 py-2"
                          placeholder="e.g., 100"
                        />
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Min Meters
                          </label>
                          <input
                            type="number"
                            step="0.1"
                            value={productForm.minMeters}
                            onChange={(e) => setProductForm(prev => ({ ...prev, minMeters: e.target.value }))}
                            className="w-full border border-gray-300 rounded px-3 py-2"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-1">
                            Max Meters
                          </label>
                          <input
                            type="number"
                            step="0.1"
                            value={productForm.maxMeters}
                            onChange={(e) => setProductForm(prev => ({ ...prev, maxMeters: e.target.value }))}
                            className="w-full border border-gray-300 rounded px-3 py-2"
                          />
                        </div>
                      </div>
                    </>
                  )}

                  {(productForm.pricingType === 'fixed' || productForm.pricingType === 'paper_print') && (
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Min Quantity
                        </label>
                        <input
                          type="number"
                          value={productForm.minQuantity}
                          onChange={(e) => setProductForm(prev => ({ ...prev, minQuantity: e.target.value }))}
                          className="w-full border border-gray-300 rounded px-3 py-2"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Max Quantity
                        </label>
                        <input
                          type="number"
                          value={productForm.maxQuantity}
                          onChange={(e) => setProductForm(prev => ({ ...prev, maxQuantity: e.target.value }))}
                          className="w-full border border-gray-300 rounded px-3 py-2"
                        />
                      </div>
                    </div>
                  )}

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Unit Type
                    </label>
                    <input
                      type="text"
                      value={productForm.unitType}
                      onChange={(e) => setProductForm(prev => ({ ...prev, unitType: e.target.value }))}
                      className="w-full border border-gray-300 rounded px-3 py-2"
                      placeholder="e.g., piece, meter, sqm"
                    />
                  </div>

                  <div className="flex gap-3 pt-4">
                    <button
                      type="submit"
                      className="flex-1 bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded"
                    >
                      Update Configuration
                    </button>
                    <button
                      type="button"
                      onClick={() => setEditingProduct(null)}
                      className="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 py-2 px-4 rounded"
                    >
                      Cancel
                    </button>
                  </div>
                </form>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
} 