'use client';

import { useState, useEffect } from 'react';
import { requireAdmin } from '@/lib/auth-helpers';
import { useSession } from 'next-auth/react';
import Link from 'next/link';
import DashboardCharts from '@/components/admin/DashboardCharts';
import DashboardAlerts from '@/components/admin/DashboardAlerts';
import {
  ChartBarIcon,
  ShoppingBagIcon,
  UsersIcon,
  DocumentTextIcon,
  CurrencyDollarIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  ArrowRightIcon,
  EyeIcon,
  ChatBubbleLeftRightIcon,
  PhotoIcon,
  ServerIcon,
  ShieldCheckIcon,
  NewspaperIcon,
  PaintBrushIcon,
  BanknotesIcon,
  CalendarDaysIcon,
  ArrowPathIcon,
} from '@heroicons/react/24/outline';

// Types for dashboard data
interface DashboardStats {
  overview: {
    totalRevenue: number;
    totalOrders: number;
    totalUsers: number;
    totalProjects: number;
    revenueGrowth: number;
    ordersGrowth: number;
    usersGrowth: number;
    projectsGrowth: number;
  };
  orders: {
    pending: number;
    processing: number;
    completed: number;
    cancelled: number;
    totalValue: number;
    avgOrderValue: number;
  };
  content: {
    catalogueItems: number;
    blogPosts: number;
    portfolio: number;
    testimonials: number;
  };
  system: {
    serverStatus: 'online' | 'maintenance' | 'offline';
    lastBackup: string;
    storageUsed: number;
    totalStorage: number;
    activeUsers: number;
  };
  serverStats?: {
    cpu: {
      model: string;
      cores: number;
      usage: number;
      loadAverage: number[];
    };
    memory: {
      totalGB: number;
      usedGB: number;
      freeGB: number;
      usage: number;
      process: {
        rssGB: number;
        heapUsedGB: number;
        heapTotalGB: number;
      };
    };
    storage: {
      totalGB: number;
      usedGB: number;
      freeGB: number;
      usage: number;
    };
    system: {
      platform: string;
      arch: string;
      hostname: string;
      uptime: {
        seconds: number;
        hours: number;
        days: number;
        formatted: string;
      };
      nodeVersion: string;
      pid: number;
    };
  };
  recentActivity: Array<{
    id: string;
    type: 'order' | 'user' | 'blog' | 'system';
    message: string;
    timestamp: string;
    status: 'success' | 'warning' | 'error';
  }>;
  quickStats: {
    todayOrders: number;
    todayRevenue: number;
    weeklyOrders: number;
    weeklyRevenue: number;
    monthlyOrders: number;
    monthlyRevenue: number;
  };
}

// Default empty data structure for loading state
const defaultDashboardData: DashboardStats = {
  overview: {
    totalRevenue: 0,
    totalOrders: 0,
    totalUsers: 0,
    totalProjects: 0,
    revenueGrowth: 0,
    ordersGrowth: 0,
    usersGrowth: 0,
    projectsGrowth: 0,
  },
  orders: {
    pending: 0,
    processing: 0,
    completed: 0,
    cancelled: 0,
    totalValue: 0,
    avgOrderValue: 0,
  },
  content: {
    catalogueItems: 0,
    blogPosts: 0,
    portfolio: 0,
    testimonials: 0,
  },
  system: {
    serverStatus: 'offline',
    lastBackup: new Date().toISOString(),
    storageUsed: 0,
    totalStorage: 100,
    activeUsers: 0,
  },
  recentActivity: [],
  quickStats: {
    todayOrders: 0,
    todayRevenue: 0,
    weeklyOrders: 0,
    weeklyRevenue: 0,
    monthlyOrders: 0,
    monthlyRevenue: 0,
  },
};

export default function AdminDashboard() {
  const { data: session } = useSession();
  const [dashboardData, setDashboardData] = useState<DashboardStats>(defaultDashboardData);
  const [loading, setLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState(new Date());

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  // Format percentage
  const formatPercentage = (value: number, showSign: boolean = true) => {
    const formatted = Math.abs(value).toFixed(1);
    if (showSign) {
      return value >= 0 ? `+${formatted}%` : `-${formatted}%`;
    }
    return `${formatted}%`;
  };

  // Get trend icon and color
  const getTrendInfo = (value: number) => {
    if (value > 0) {
      return {
        icon: ArrowTrendingUpIcon,
        color: 'text-green-600',
        bgColor: 'bg-green-50',
      };
    } else if (value < 0) {
      return {
        icon: ArrowTrendingDownIcon,
        color: 'text-red-600',
        bgColor: 'bg-red-50',
      };
    } else {
      return {
        icon: ArrowTrendingUpIcon,
        color: 'text-gray-600',
        bgColor: 'bg-gray-50',
      };
    }
  };

  // Refresh dashboard data
  const refreshData = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/admin/dashboard', {
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
        throw new Error(errorData.error || `HTTP ${response.status}`);
      }
      
      const data = await response.json();
      setDashboardData(data);
      setLastUpdated(new Date());
    } catch (error) {
      console.error('Failed to fetch dashboard data:', error);
      // Show error notification to user
      if (typeof window !== 'undefined') {
        alert('Failed to load dashboard data. Please refresh the page or check your connection.');
      }
    } finally {
      setLoading(false);
    }
  };

  // Initial data fetch
  useEffect(() => {
    refreshData();
  }, []);

  // Auto-refresh every 5 minutes
  useEffect(() => {
    const interval = setInterval(() => {
      refreshData();
    }, 5 * 60 * 1000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div className="flex-1 min-w-0">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                    <ChartBarIcon className="h-6 w-6 text-white" />
                  </div>
                </div>
                <div className="ml-4">
                  <h1 className="text-2xl lg:text-3xl font-bold text-gray-900">
                    Admin Dashboard
                  </h1>
                  <p className="text-sm lg:text-base text-gray-600">
                    Welcome back, {session?.user?.username || 'Admin'}! Here's your business overview.
                  </p>
                </div>
              </div>
            </div>
            
            <div className="mt-4 sm:mt-0 flex items-center space-x-3">
              <div className="text-sm text-gray-500">
                Last updated: {lastUpdated.toLocaleTimeString()}
              </div>
              <button
                onClick={refreshData}
                disabled={loading}
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
              >
                <ArrowPathIcon className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                Refresh
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="px-4 sm:px-6 lg:px-8 py-8 space-y-8">
        {/* Main Stats Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* Total Revenue */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-lg transition-shadow duration-200">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                  <CurrencyDollarIcon className="h-6 w-6 text-green-600" />
                </div>
              </div>
              <div className="ml-4 flex-1">
                <p className="text-sm font-medium text-gray-600">Total Revenue</p>
                <p className="text-2xl font-bold text-gray-900">
                  {formatCurrency(dashboardData.overview.totalRevenue)}
                </p>
                <div className="flex items-center mt-1">
                  {(() => {
                    const trend = getTrendInfo(dashboardData.overview.revenueGrowth);
                    return (
                      <>
                        <trend.icon className={`h-4 w-4 ${trend.color}`} />
                        <span className={`text-sm font-medium ml-1 ${trend.color}`}>
                          {formatPercentage(dashboardData.overview.revenueGrowth)}
                        </span>
                        <span className="text-sm text-gray-500 ml-1">this month</span>
                      </>
                    );
                  })()}
                </div>
              </div>
            </div>
          </div>

          {/* Total Orders */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-lg transition-shadow duration-200">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                  <ShoppingBagIcon className="h-6 w-6 text-blue-600" />
                </div>
              </div>
              <div className="ml-4 flex-1">
                <p className="text-sm font-medium text-gray-600">Total Orders</p>
                <p className="text-2xl font-bold text-gray-900">
                  {dashboardData.overview.totalOrders.toLocaleString()}
                </p>
                <div className="flex items-center mt-1">
                  {(() => {
                    const trend = getTrendInfo(dashboardData.overview.ordersGrowth);
                    return (
                      <>
                        <trend.icon className={`h-4 w-4 ${trend.color}`} />
                        <span className={`text-sm font-medium ml-1 ${trend.color}`}>
                          {formatPercentage(dashboardData.overview.ordersGrowth)}
                        </span>
                        <span className="text-sm text-gray-500 ml-1">this month</span>
                      </>
                    );
                  })()}
                </div>
              </div>
            </div>
          </div>

          {/* Total Users */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-lg transition-shadow duration-200">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                  <UsersIcon className="h-6 w-6 text-purple-600" />
                </div>
              </div>
              <div className="ml-4 flex-1">
                <p className="text-sm font-medium text-gray-600">Total Users</p>
                <p className="text-2xl font-bold text-gray-900">
                  {dashboardData.overview.totalUsers.toLocaleString()}
                </p>
                <div className="flex items-center mt-1">
                  {(() => {
                    const trend = getTrendInfo(dashboardData.overview.usersGrowth);
                    return (
                      <>
                        <trend.icon className={`h-4 w-4 ${trend.color}`} />
                        <span className={`text-sm font-medium ml-1 ${trend.color}`}>
                          {formatPercentage(dashboardData.overview.usersGrowth)}
                        </span>
                        <span className="text-sm text-gray-500 ml-1">this month</span>
                      </>
                    );
                  })()}
                </div>
              </div>
            </div>
          </div>

          {/* Total Projects */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-lg transition-shadow duration-200">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                  <DocumentTextIcon className="h-6 w-6 text-orange-600" />
                </div>
              </div>
              <div className="ml-4 flex-1">
                <p className="text-sm font-medium text-gray-600">Active Projects</p>
                <p className="text-2xl font-bold text-gray-900">
                  {dashboardData.overview.totalProjects.toLocaleString()}
                </p>
                <div className="flex items-center mt-1">
                  {(() => {
                    const trend = getTrendInfo(dashboardData.overview.projectsGrowth);
                    return (
                      <>
                        <trend.icon className={`h-4 w-4 ${trend.color}`} />
                        <span className={`text-sm font-medium ml-1 ${trend.color}`}>
                          {formatPercentage(dashboardData.overview.projectsGrowth)}
                        </span>
                        <span className="text-sm text-gray-500 ml-1">this month</span>
                      </>
                    );
                  })()}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Stats Row */}
        <div className="grid grid-cols-1 sm:grid-cols-3 lg:grid-cols-6 gap-4">
          <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-4">
            <div className="flex items-center">
              <CalendarDaysIcon className="h-5 w-5 text-blue-500" />
              <div className="ml-3">
                <p className="text-xs font-medium text-gray-600">Today</p>
                <p className="text-lg font-bold text-gray-900">{dashboardData.quickStats.todayOrders}</p>
                <p className="text-xs text-gray-500">{formatCurrency(dashboardData.quickStats.todayRevenue)}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-4">
            <div className="flex items-center">
              <CalendarDaysIcon className="h-5 w-5 text-green-500" />
              <div className="ml-3">
                <p className="text-xs font-medium text-gray-600">This Week</p>
                <p className="text-lg font-bold text-gray-900">{dashboardData.quickStats.weeklyOrders}</p>
                <p className="text-xs text-gray-500">{formatCurrency(dashboardData.quickStats.weeklyRevenue)}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-4">
            <div className="flex items-center">
              <CalendarDaysIcon className="h-5 w-5 text-purple-500" />
              <div className="ml-3">
                <p className="text-xs font-medium text-gray-600">This Month</p>
                <p className="text-lg font-bold text-gray-900">{dashboardData.quickStats.monthlyOrders}</p>
                <p className="text-xs text-gray-500">{formatCurrency(dashboardData.quickStats.monthlyRevenue)}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-4">
            <div className="flex items-center">
              <BanknotesIcon className="h-5 w-5 text-green-500" />
              <div className="ml-3">
                <p className="text-xs font-medium text-gray-600">Avg Order</p>
                <p className="text-lg font-bold text-gray-900">{formatCurrency(dashboardData.orders.avgOrderValue)}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-4">
            <div className="flex items-center">
              <EyeIcon className="h-5 w-5 text-blue-500" />
              <div className="ml-3">
                <p className="text-xs font-medium text-gray-600">Active Users</p>
                <p className="text-lg font-bold text-gray-900">{dashboardData.system.activeUsers}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-100 p-4">
            <div className="flex items-center">
              <ServerIcon className="h-5 w-5 text-green-500" />
              <div className="ml-3">
                <p className="text-xs font-medium text-gray-600">Server Status</p>
                <p className="text-sm font-bold text-green-600 capitalize">{dashboardData.system.serverStatus}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Orders and Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Orders Overview */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900">Orders Overview</h3>
              <Link 
                href="/admin/orders"
                className="text-sm text-blue-600 hover:text-blue-700 font-medium flex items-center"
              >
                View All <ArrowRightIcon className="h-4 w-4 ml-1" />
              </Link>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-4 bg-yellow-50 rounded-lg">
                <ClockIcon className="h-8 w-8 text-yellow-600 mx-auto mb-2" />
                <p className="text-2xl font-bold text-yellow-700">{dashboardData.orders.pending}</p>
                <p className="text-sm text-yellow-600">Pending</p>
              </div>
              
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <DocumentTextIcon className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                <p className="text-2xl font-bold text-blue-700">{dashboardData.orders.processing}</p>
                <p className="text-sm text-blue-600">Processing</p>
              </div>
              
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <CheckCircleIcon className="h-8 w-8 text-green-600 mx-auto mb-2" />
                <p className="text-2xl font-bold text-green-700">{dashboardData.orders.completed}</p>
                <p className="text-sm text-green-600">Completed</p>
              </div>
              
              <div className="text-center p-4 bg-red-50 rounded-lg">
                <XCircleIcon className="h-8 w-8 text-red-600 mx-auto mb-2" />
                <p className="text-2xl font-bold text-red-700">{dashboardData.orders.cancelled}</p>
                <p className="text-sm text-red-600">Cancelled</p>
              </div>
            </div>
            
            <div className="mt-6 pt-6 border-t border-gray-200">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium text-gray-600">Total Orders Value</span>
                <span className="text-lg font-bold text-gray-900">
                  {formatCurrency(dashboardData.orders.totalValue)}
                </span>
              </div>
            </div>
          </div>

          {/* Content Overview */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900">Content Overview</h3>
              <Link 
                href="/admin/catalogue"
                className="text-sm text-blue-600 hover:text-blue-700 font-medium flex items-center"
              >
                Manage <ArrowRightIcon className="h-4 w-4 ml-1" />
              </Link>
            </div>
            
            <div className="space-y-4">
              <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                <div className="flex items-center">
                  <PhotoIcon className="h-6 w-6 text-blue-600 mr-3" />
                  <span className="text-sm font-medium text-gray-900">Catalogue Items</span>
                </div>
                <span className="text-lg font-bold text-blue-700">{dashboardData.content.catalogueItems}</span>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                <div className="flex items-center">
                  <NewspaperIcon className="h-6 w-6 text-green-600 mr-3" />
                  <span className="text-sm font-medium text-gray-900">Blog Posts</span>
                </div>
                <span className="text-lg font-bold text-green-700">{dashboardData.content.blogPosts}</span>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
                <div className="flex items-center">
                  <PaintBrushIcon className="h-6 w-6 text-purple-600 mr-3" />
                  <span className="text-sm font-medium text-gray-900">Portfolio Items</span>
                </div>
                <span className="text-lg font-bold text-purple-700">{dashboardData.content.portfolio}</span>
              </div>
              
              <div className="flex items-center justify-between p-3 bg-orange-50 rounded-lg">
                <div className="flex items-center">
                  <ChatBubbleLeftRightIcon className="h-6 w-6 text-orange-600 mr-3" />
                  <span className="text-sm font-medium text-gray-900">Testimonials</span>
                </div>
                <span className="text-lg font-bold text-orange-700">{dashboardData.content.testimonials}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions and Recent Activity */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Quick Actions */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-6">Quick Actions</h3>
            
            <div className="grid grid-cols-2 gap-4">
              <Link
                href="/admin/catalogue/new"
                className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 hover:border-blue-300 transition-colors"
              >
                <PhotoIcon className="h-8 w-8 text-blue-600 mb-2" />
                <span className="text-sm font-medium text-gray-900">Add Product</span>
              </Link>
              
              <Link
                href="/admin/blog/new"
                className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 hover:border-green-300 transition-colors"
              >
                <NewspaperIcon className="h-8 w-8 text-green-600 mb-2" />
                <span className="text-sm font-medium text-gray-900">New Blog Post</span>
              </Link>
              
              <Link
                href="/admin/users"
                className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 hover:border-purple-300 transition-colors"
              >
                <UsersIcon className="h-8 w-8 text-purple-600 mb-2" />
                <span className="text-sm font-medium text-gray-900">Manage Users</span>
              </Link>
              
              <Link
                href="/admin/orders"
                className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 hover:border-orange-300 transition-colors"
              >
                <ShoppingBagIcon className="h-8 w-8 text-orange-600 mb-2" />
                <span className="text-sm font-medium text-gray-900">View Orders</span>
              </Link>
            </div>
          </div>

          {/* Recent Activity */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-6">Recent Activity</h3>
            
            <div className="space-y-4">
              {dashboardData.recentActivity.map((activity) => (
                <div key={activity.id} className="flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50">
                  <div className={`flex-shrink-0 w-2 h-2 rounded-full mt-2 ${
                    activity.status === 'success' ? 'bg-green-500' :
                    activity.status === 'warning' ? 'bg-yellow-500' : 'bg-red-500'
                  }`} />
                  <div className="flex-1 min-w-0">
                    <p className="text-sm text-gray-900">{activity.message}</p>
                    <p className="text-xs text-gray-500 mt-1">
                      {new Date(activity.timestamp).toLocaleString()}
                    </p>
                  </div>
                </div>
              ))}
            </div>
            
            <div className="mt-6 pt-4 border-t border-gray-200">
              <Link
                href="/admin/activity-logs"
                className="text-sm text-blue-600 hover:text-blue-700 font-medium flex items-center justify-center"
              >
                View All Activity <ArrowRightIcon className="h-4 w-4 ml-1" />
              </Link>
            </div>
          </div>
        </div>

        {/* System Status */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-6">System Status</h3>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className={`w-3 h-3 rounded-full mr-3 ${
                  dashboardData.system.serverStatus === 'online' ? 'bg-green-500' :
                  dashboardData.system.serverStatus === 'maintenance' ? 'bg-yellow-500' : 'bg-red-500'
                }`} />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-900">Server Status</p>
                <p className={`text-sm capitalize ${
                  dashboardData.system.serverStatus === 'online' ? 'text-green-600' :
                  dashboardData.system.serverStatus === 'maintenance' ? 'text-yellow-600' : 'text-red-600'
                }`}>
                  {dashboardData.system.serverStatus}
                </p>
              </div>
            </div>
            
            <div>
              <p className="text-sm font-medium text-gray-900">Last Backup</p>
              <p className="text-sm text-gray-600">
                {new Date(dashboardData.system.lastBackup).toLocaleDateString()}
              </p>
            </div>
            
            <div>
              <p className="text-sm font-medium text-gray-900">Storage Used</p>
              <div className="flex items-center mt-1">
                <div className="flex-1 bg-gray-200 rounded-full h-2 mr-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full" 
                    style={{ width: `${dashboardData.system.storageUsed}%` }}
                  />
                </div>
                <span className="text-sm text-gray-600">{dashboardData.system.storageUsed}%</span>
              </div>
            </div>
            
            <div>
              <p className="text-sm font-medium text-gray-900">Security Status</p>
              <div className="flex items-center mt-1">
                <ShieldCheckIcon className="h-4 w-4 text-green-500 mr-1" />
                <span className="text-sm text-green-600">Protected</span>
              </div>
            </div>
          </div>
        </div>

        {/* Server Statistics */}
        {dashboardData.serverStats && (
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                <ServerIcon className="h-5 w-5 text-blue-500" />
                Server Statistics
              </h3>
              <div className="flex items-center text-sm text-gray-500">
                <span className="mr-2">Live Data</span>
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* CPU Stats */}
              <div className="bg-gradient-to-br from-blue-50 to-blue-100 rounded-lg p-4 border border-blue-200">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-medium text-blue-900">CPU Usage</h4>
                  <div className="text-2xl font-bold text-blue-600">
                    {dashboardData.serverStats.cpu.usage.toFixed(1)}%
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-blue-700">Cores:</span>
                    <span className="font-medium text-blue-900">{dashboardData.serverStats.cpu.cores}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-blue-700">Load Avg:</span>
                    <span className="font-medium text-blue-900">
                      {dashboardData.serverStats.cpu.loadAverage.slice(0, 3).map(avg => avg.toFixed(2)).join(', ')}
                    </span>
                  </div>
                  <div className="w-full bg-blue-200 rounded-full h-2 mt-3">
                    <div 
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300" 
                      style={{ width: `${Math.min(dashboardData.serverStats.cpu.usage, 100)}%` }}
                    />
                  </div>
                </div>
              </div>

              {/* Memory Stats */}
              <div className="bg-gradient-to-br from-green-50 to-green-100 rounded-lg p-4 border border-green-200">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-medium text-green-900">Memory Usage</h4>
                  <div className="text-2xl font-bold text-green-600">
                    {dashboardData.serverStats.memory.usage.toFixed(1)}%
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-green-700">Total:</span>
                    <span className="font-medium text-green-900">{dashboardData.serverStats.memory.totalGB.toFixed(1)} GB</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-green-700">Used:</span>
                    <span className="font-medium text-green-900">{dashboardData.serverStats.memory.usedGB.toFixed(1)} GB</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-green-700">Free:</span>
                    <span className="font-medium text-green-900">{dashboardData.serverStats.memory.freeGB.toFixed(1)} GB</span>
                  </div>
                  <div className="w-full bg-green-200 rounded-full h-2 mt-3">
                    <div 
                      className="bg-green-600 h-2 rounded-full transition-all duration-300" 
                      style={{ width: `${Math.min(dashboardData.serverStats.memory.usage, 100)}%` }}
                    />
                  </div>
                </div>
              </div>

              {/* Storage Stats */}
              <div className="bg-gradient-to-br from-purple-50 to-purple-100 rounded-lg p-4 border border-purple-200">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="font-medium text-purple-900">Storage Usage</h4>
                  <div className="text-2xl font-bold text-purple-600">
                    {dashboardData.serverStats.storage.usage.toFixed(1)}%
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-purple-700">Total:</span>
                    <span className="font-medium text-purple-900">{dashboardData.serverStats.storage.totalGB.toFixed(1)} GB</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-purple-700">Used:</span>
                    <span className="font-medium text-purple-900">{dashboardData.serverStats.storage.usedGB.toFixed(1)} GB</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-purple-700">Free:</span>
                    <span className="font-medium text-purple-900">{dashboardData.serverStats.storage.freeGB.toFixed(1)} GB</span>
                  </div>
                  <div className="w-full bg-purple-200 rounded-full h-2 mt-3">
                    <div 
                      className="bg-purple-600 h-2 rounded-full transition-all duration-300" 
                      style={{ width: `${Math.min(dashboardData.serverStats.storage.usage, 100)}%` }}
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* System Information */}
            <div className="mt-6 pt-6 border-t border-gray-200">
              <h4 className="font-medium text-gray-900 mb-4">System Information</h4>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="bg-gray-50 rounded-lg p-3">
                  <div className="text-xs text-gray-500 uppercase tracking-wide mb-1">Platform</div>
                  <div className="font-medium text-gray-900 capitalize">{dashboardData.serverStats.system.platform}</div>
                </div>
                <div className="bg-gray-50 rounded-lg p-3">
                  <div className="text-xs text-gray-500 uppercase tracking-wide mb-1">Architecture</div>
                  <div className="font-medium text-gray-900">{dashboardData.serverStats.system.arch}</div>
                </div>
                <div className="bg-gray-50 rounded-lg p-3">
                  <div className="text-xs text-gray-500 uppercase tracking-wide mb-1">Uptime</div>
                  <div className="font-medium text-gray-900">{dashboardData.serverStats.system.uptime.formatted}</div>
                </div>
                <div className="bg-gray-50 rounded-lg p-3">
                  <div className="text-xs text-gray-500 uppercase tracking-wide mb-1">Node.js</div>
                  <div className="font-medium text-gray-900">{dashboardData.serverStats.system.nodeVersion}</div>
                </div>
              </div>
            </div>

            {/* Process Memory Details */}
            <div className="mt-6 pt-6 border-t border-gray-200">
              <h4 className="font-medium text-gray-900 mb-4">Node.js Process Memory</h4>
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                <div className="bg-orange-50 rounded-lg p-3 border border-orange-200">
                  <div className="text-xs text-orange-600 uppercase tracking-wide mb-1">RSS Memory</div>
                  <div className="font-medium text-orange-900">{dashboardData.serverStats.memory.process.rssGB.toFixed(1)} MB</div>
                </div>
                <div className="bg-orange-50 rounded-lg p-3 border border-orange-200">
                  <div className="text-xs text-orange-600 uppercase tracking-wide mb-1">Heap Used</div>
                  <div className="font-medium text-orange-900">{dashboardData.serverStats.memory.process.heapUsedGB.toFixed(1)} MB</div>
                </div>
                <div className="bg-orange-50 rounded-lg p-3 border border-orange-200">
                  <div className="text-xs text-orange-600 uppercase tracking-wide mb-1">Heap Total</div>
                  <div className="font-medium text-orange-900">{dashboardData.serverStats.memory.process.heapTotalGB.toFixed(1)} MB</div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Analytics Charts */}
        <DashboardCharts />

        {/* Alerts and Notifications */}
        <DashboardAlerts />
      </div>
    </div>
  );
}
