'use client';

import { useState, useEffect } from 'react';
import { useUnits } from '@/hooks/useUnits';
import { PlusIcon, PencilIcon, TrashIcon } from '@heroicons/react/24/outline';

interface Unit {
  id: string;
  name: string;
  displayName: string;
  plural: string;
  shortForm?: string;
  category: string;
}

interface UnitFormData {
  name: string;
  displayName: string;
  plural: string;
  shortForm: string;
  category: string;
}

const UNIT_CATEGORIES = [
  'Printing',
  'Digital',
  'Physical',
  'Time',
  'Measurement',
  'Other'
];

export default function UnitsManagementPage() {
  const { units, loading, error, refetch } = useUnits();
  const [showModal, setShowModal] = useState(false);
  const [editingUnit, setEditingUnit] = useState<Unit | null>(null);
  const [formData, setFormData] = useState<UnitFormData>({
    name: '',
    displayName: '',
    plural: '',
    shortForm: '',
    category: 'Printing'
  });
  const [submitting, setSubmitting] = useState(false);
  const [notification, setNotification] = useState<{
    type: 'success' | 'error';
    message: string;
  } | null>(null);

  const resetForm = () => {
    setFormData({
      name: '',
      displayName: '',
      plural: '',
      shortForm: '',
      category: 'Printing'
    });
    setEditingUnit(null);
  };

  const openCreateModal = () => {
    resetForm();
    setShowModal(true);
  };

  const openEditModal = (unit: Unit) => {
    setFormData({
      name: unit.name,
      displayName: unit.displayName,
      plural: unit.plural,
      shortForm: unit.shortForm || '',
      category: unit.category
    });
    setEditingUnit(unit);
    setShowModal(true);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const showNotification = (type: 'success' | 'error', message: string) => {
    setNotification({ type, message });
    setTimeout(() => setNotification(null), 5000);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);

    try {
      const url = editingUnit 
        ? `/api/admin/units/${editingUnit.id}`
        : '/api/admin/units';
      
      const method = editingUnit ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        throw new Error(`Failed to ${editingUnit ? 'update' : 'create'} unit`);
      }

      const result = await response.json();
      
      showNotification('success', 
        editingUnit 
          ? 'Unit updated successfully!'
          : 'Unit created successfully!'
      );
      
      setShowModal(false);
      resetForm();
      refetch();
    } catch (err: any) {
      console.error('Error submitting unit:', err);
      showNotification('error', err.message || 'Failed to save unit');
    } finally {
      setSubmitting(false);
    }
  };

  const handleDelete = async (unit: Unit) => {
    if (!confirm(`Are you sure you want to delete the unit "${unit.displayName}"? This action cannot be undone.`)) {
      return;
    }

    try {
      const response = await fetch(`/api/admin/units/${unit.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete unit');
      }

      showNotification('success', 'Unit deleted successfully!');
      refetch();
    } catch (err: any) {
      console.error('Error deleting unit:', err);
      showNotification('error', err.message || 'Failed to delete unit');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-md p-4">
        <div className="text-red-800">
          <strong>Error loading units:</strong> {error}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Units Management</h1>
          <p className="text-gray-600">Manage pricing units for your services</p>
        </div>
        <button
          onClick={openCreateModal}
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center gap-2"
        >
          <PlusIcon className="h-5 w-5" />
          Add Unit
        </button>
      </div>

      {/* Notification */}
      {notification && (
        <div className={`p-4 rounded-md ${
          notification.type === 'success' 
            ? 'bg-green-50 border border-green-200 text-green-800' 
            : 'bg-red-50 border border-red-200 text-red-800'
        }`}>
          {notification.message}
        </div>
      )}

      {/* Units List */}
      <div className="bg-white shadow rounded-lg overflow-hidden">
        <div className="px-4 py-5 sm:p-6">
          <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
            {units.map((unit) => (
              <div
                key={unit.id}
                className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
              >
                <div className="flex justify-between items-start mb-3">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900">
                      {unit.displayName}
                    </h3>
                    <p className="text-sm text-gray-500">
                      {unit.name} • {unit.category}
                    </p>
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => openEditModal(unit)}
                      className="text-blue-600 hover:text-blue-800"
                    >
                      <PencilIcon className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => handleDelete(unit)}
                      className="text-red-600 hover:text-red-800"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </button>
                  </div>
                </div>
                
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-500">Plural:</span>
                    <span className="text-gray-900">{unit.plural}</span>
                  </div>
                  {unit.shortForm && (
                    <div className="flex justify-between">
                      <span className="text-gray-500">Short form:</span>
                      <span className="text-gray-900">{unit.shortForm}</span>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>

          {units.length === 0 && (
            <div className="text-center py-12">
              <div className="text-gray-500 mb-4">No units found</div>
              <button
                onClick={openCreateModal}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg"
              >
                Create your first unit
              </button>
            </div>
          )}
        </div>
      </div>

      {/* Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-full max-w-md shadow-lg rounded-md bg-white">
            <div className="mt-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                {editingUnit ? 'Edit Unit' : 'Create New Unit'}
              </h3>
              
              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Unit Name *
                  </label>
                  <input
                    type="text"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="e.g., copy, meter, page"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Display Name *
                  </label>
                  <input
                    type="text"
                    name="displayName"
                    value={formData.displayName}
                    onChange={handleInputChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="e.g., Copy, Square Meter, Page"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Plural Form *
                  </label>
                  <input
                    type="text"
                    name="plural"
                    value={formData.plural}
                    onChange={handleInputChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="e.g., Copies, Square Meters, Pages"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Short Form
                  </label>
                  <input
                    type="text"
                    name="shortForm"
                    value={formData.shortForm}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="e.g., pc, m², pg"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Category *
                  </label>
                  <select
                    name="category"
                    value={formData.category}
                    onChange={handleInputChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    {UNIT_CATEGORIES.map((category) => (
                      <option key={category} value={category}>
                        {category}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="flex justify-end space-x-3 pt-4">
                  <button
                    type="button"
                    onClick={() => setShowModal(false)}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-md"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={submitting}
                    className="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md disabled:opacity-50"
                  >
                    {submitting 
                      ? (editingUnit ? 'Updating...' : 'Creating...') 
                      : (editingUnit ? 'Update Unit' : 'Create Unit')
                    }
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 