'use client';
import React from 'react';

import { useState, useRef, FormEvent } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { ArrowLeftIcon, PhotoIcon, GlobeAltIcon, FolderOpenIcon } from '@heroicons/react/24/outline';
import { WebsitePortfolioFormData, WEBSITE_CATEGORIES } from '@/types/portfolio';
import { useNotification } from '@/contexts/NotificationContext';
import S3ImageBrowser from '@/components/admin/S3ImageBrowser';

export default function NewWebsitePortfolioItemPage() {
  const router = useRouter();
  const { showNotification } = useNotification();
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [formData, setFormData] = useState<WebsitePortfolioFormData>({
    title: '',
    description: '',
    category: 'e-commerce',
    image: null,
    url: '',
    featured: false
  });

  const [preview, setPreview] = useState<string | null>(null);
  const [selectedImageUrl, setSelectedImageUrl] = useState<string | null>(null);
  const [imageSource, setImageSource] = useState<'upload' | 's3'>('upload');
  const [showS3Browser, setShowS3Browser] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [recentUpload, setRecentUpload] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear error when field is edited
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFormData(prev => ({ ...prev, [name]: checked }));
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setFormData(prev => ({ ...prev, image: file }));

      // Create preview
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreview(reader.result as string);
      };
      reader.readAsDataURL(file);

      // Clear S3 selection and set source to upload
      setSelectedImageUrl(null);
      setImageSource('upload');
      setRecentUpload(true);

      // Clear error
      if (errors.image) {
        setErrors(prev => {
          const newErrors = { ...prev };
          delete newErrors.image;
          return newErrors;
        });
      }
    }
  };

  const handleS3ImageSelect = (imageUrl: string, filename: string) => {
    setSelectedImageUrl(imageUrl);
    setPreview(imageUrl);
    setImageSource('s3');

    // Clear file upload
    setFormData(prev => ({ ...prev, image: null }));
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }

    // Auto-fill title if empty
    if (!formData.title && filename) {
      const titleFromFilename = filename.replace(/\.[^/.]+$/, '').replace(/[-_]/g, ' ');
      setFormData(prev => ({ ...prev, title: titleFromFilename }));
    }

    // Clear error
    if (errors.image) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors.image;
        return newErrors;
      });
    }
  };

  const handleImageSourceChange = (source: 'upload' | 's3') => {
    setImageSource(source);

    if (source === 's3') {
      // Clear upload data
      setFormData(prev => ({ ...prev, image: null }));
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
      if (!selectedImageUrl) {
        setPreview(null);
      }
    } else if (source === 'upload') {
      // Clear S3 selection
      setSelectedImageUrl(null);
      if (!formData.image) {
        setPreview(null);
      }
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Title is required';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    }

    if (!formData.url.trim()) {
      newErrors.url = 'Website URL is required';
    } else if (!/^https?:\/\/.+\..+/.test(formData.url)) {
      newErrors.url = 'Please enter a valid URL (starting with http:// or https://)';
    }

    if (!formData.image && !selectedImageUrl) {
      newErrors.image = 'Screenshot image is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      let imageSrc: string;

      // Determine image source
      if (imageSource === 's3' && selectedImageUrl) {
        // Use the selected S3 image URL directly
        imageSrc = selectedImageUrl;
      } else if (imageSource === 'upload' && formData.image) {
        // Upload the new image
        const imageFormData = new FormData();
        imageFormData.append('file', formData.image as File);
        imageFormData.append('category', 'websites');

        const uploadResponse = await fetch('/api/admin/upload', {
          method: 'POST',
          body: imageFormData,
        });

        if (!uploadResponse.ok) {
          const errorData = await uploadResponse.json();
          throw new Error(errorData.error || 'Failed to upload image');
        }

        const uploadResult = await uploadResponse.json();
        imageSrc = uploadResult.url;
      } else {
        throw new Error('No image selected');
      }

      // Then, create the website portfolio item
      const websitePortfolioItemData = {
        title: formData.title,
        description: formData.description,
        category: formData.category,
        imageSrc,
        url: formData.url,
        featured: formData.featured,
      };

      const createResponse = await fetch('/api/admin/website-portfolio', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(websitePortfolioItemData),
      });

      if (!createResponse.ok) {
        const errorData = await createResponse.json();
        throw new Error(errorData.error || 'Failed to create website portfolio item');
      }

      showNotification('success', 'Success', 'Website portfolio item created successfully');
      router.push('/admin/website-portfolio');
    } catch (error) {
      console.error('Error creating website portfolio item:', error);
      showNotification('error', 'Error', error instanceof Error ? error.message : 'Failed to create website portfolio item');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <Link
            href="/admin/website-portfolio"
            className="mr-4 p-2 rounded-full text-slate-500 hover:text-slate-700 hover:bg-slate-100"
          >
            <ArrowLeftIcon className="h-5 w-5" />
          </Link>
          <h1 className="text-2xl font-bold text-slate-800">Add New Website Portfolio Item</h1>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="bg-white shadow-sm rounded-lg p-6">
        <div className="space-y-6">
          {/* Title */}
          <div>
            <label htmlFor="title" className="block text-sm font-medium text-gray-700">
              Website Title <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="title"
              name="title"
              value={formData.title}
              onChange={handleInputChange}
              className={`mt-1 block w-full rounded-md border ${errors.title ? 'border-red-300' : 'border-gray-300'} shadow-sm p-2 focus:border-orange-500 focus:ring-orange-500`}
              placeholder="E.g., Homestore.co.ke"
            />
            {errors.title && <p className="mt-1 text-sm text-red-600">{errors.title}</p>}
          </div>

          {/* Description */}
          <div>
            <label htmlFor="description" className="block text-sm font-medium text-gray-700">
              Description <span className="text-red-500">*</span>
            </label>
            <textarea
              id="description"
              name="description"
              rows={3}
              value={formData.description}
              onChange={handleInputChange}
              className={`mt-1 block w-full rounded-md border ${errors.description ? 'border-red-300' : 'border-gray-300'} shadow-sm p-2 focus:border-orange-500 focus:ring-orange-500`}
              placeholder="E.g., Modern E-commerce Platform for Household Items"
            />
            {errors.description && <p className="mt-1 text-sm text-red-600">{errors.description}</p>}
          </div>

          {/* URL */}
          <div>
            <label htmlFor="url" className="block text-sm font-medium text-gray-700">
              Website URL <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="url"
              name="url"
              value={formData.url}
              onChange={handleInputChange}
              className={`mt-1 block w-full rounded-md border ${errors.url ? 'border-red-300' : 'border-gray-300'} shadow-sm p-2 focus:border-orange-500 focus:ring-orange-500`}
              placeholder="https://example.com"
            />
            {errors.url && <p className="mt-1 text-sm text-red-600">{errors.url}</p>}
          </div>

          {/* Category */}
          <div>
            <label htmlFor="category" className="block text-sm font-medium text-gray-700">
              Category <span className="text-red-500">*</span>
            </label>
            <select
              id="category"
              name="category"
              value={formData.category}
              onChange={handleInputChange}
              className="mt-1 block w-full rounded-md border border-gray-300 shadow-sm p-2 focus:border-orange-500 focus:ring-orange-500"
            >
              {WEBSITE_CATEGORIES.map(category => (
                <option key={category.value} value={category.value}>
                  {category.label}
                </option>
              ))}
            </select>
          </div>

          {/* Image Selection */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Website Screenshot <span className="text-red-500">*</span>
            </label>

            {/* Image Source Toggle */}
            <div className="mb-4">
              <div className="flex rounded-md shadow-sm">
                <button
                  type="button"
                  onClick={() => handleImageSourceChange('upload')}
                  className={`flex-1 px-4 py-2 text-sm font-medium rounded-l-md border ${
                    imageSource === 'upload'
                      ? 'bg-orange-50 border-orange-500 text-orange-700'
                      : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  <PhotoIcon className="h-4 w-4 inline mr-2" />
                  Upload New
                </button>
                <button
                  type="button"
                  onClick={() => handleImageSourceChange('s3')}
                  className={`flex-1 px-4 py-2 text-sm font-medium rounded-r-md border-t border-r border-b ${
                    imageSource === 's3'
                      ? 'bg-orange-50 border-orange-500 text-orange-700'
                      : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  <FolderOpenIcon className="h-4 w-4 inline mr-2" />
                  Select from S3
                </button>
              </div>
            </div>

            {/* Image Preview/Upload Area */}
            <div
              className={`flex justify-center px-6 pt-5 pb-6 border-2 border-dashed rounded-md ${
                errors.image ? 'border-red-300 bg-red-50' : 'border-gray-300'
              } transition-colors`}
            >
              <div className="space-y-1 text-center">
                {preview ? (
                  <div className="relative w-full h-48 mx-auto">
                    <Image
                      src={preview}
                      alt="Preview"
                      fill
                      className="object-contain rounded-md"
                    />
                    {imageSource === 's3' && (
                      <div className="absolute top-2 right-2 bg-blue-500 text-white text-xs px-2 py-1 rounded">
                        From S3
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="mx-auto h-12 w-12 text-gray-400">
                    <GlobeAltIcon className="h-12 w-12" aria-hidden="true" />
                  </div>
                )}

                {imageSource === 'upload' ? (
                  <div>
                    <div className="flex text-sm text-gray-600">
                      <label
                        htmlFor="image"
                        className="relative cursor-pointer rounded-md font-medium text-orange-600 focus-within:outline-none focus-within:ring-2 focus-within:ring-orange-500 focus-within:ring-offset-2 hover:text-orange-500"
                      >
                        <span>{preview ? 'Change image' : 'Upload an image'}</span>
                        <input
                          id="image"
                          name="image"
                          type="file"
                          className="sr-only"
                          accept="image/jpeg,image/png,image/gif,image/webp,image/avif"
                          ref={fileInputRef}
                          onChange={handleImageChange}
                        />
                      </label>
                      <p className="pl-1">or drag and drop</p>
                    </div>
                    <p className="text-xs text-gray-500">PNG, JPG, GIF up to 10MB</p>
                  </div>
                ) : (
                  <div>
                    <button
                      type="button"
                      onClick={() => setShowS3Browser(true)}
                      className="text-orange-600 hover:text-orange-700 font-medium text-sm"
                    >
                      {selectedImageUrl ? 'Change S3 image' : 'Browse S3 images'}
                    </button>
                    <p className="text-xs text-gray-500">
                      Select from existing images in S3 storage
                    </p>
                  </div>
                )}
              </div>
            </div>
            {errors.image && <p className="mt-1 text-sm text-red-600">{errors.image}</p>}
          </div>

          {/* Featured Checkbox */}
          <div className="flex items-start">
            <div className="flex items-center h-5">
              <input
                id="featured"
                name="featured"
                type="checkbox"
                checked={formData.featured}
                onChange={handleCheckboxChange}
                className="h-4 w-4 rounded border-gray-300 text-orange-600 focus:ring-orange-500"
              />
            </div>
            <div className="ml-3 text-sm">
              <label htmlFor="featured" className="font-medium text-gray-700">
                Featured
              </label>
              <p className="text-gray-500">Mark this website as featured to highlight it on the portfolio page</p>
            </div>
          </div>
        </div>

        <div className="mt-6 flex justify-end space-x-3">
          <Link
            href="/admin/website-portfolio"
            className="inline-flex justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2"
          >
            Cancel
          </Link>
          <button
            type="submit"
            disabled={isSubmitting}
            className="inline-flex justify-center rounded-md border border-transparent bg-orange-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 disabled:bg-orange-300 disabled:cursor-not-allowed"
          >
            {isSubmitting ? 'Creating...' : 'Create Website Portfolio Item'}
          </button>
        </div>
      </form>

      {/* S3 Image Browser Modal */}
      <S3ImageBrowser
        isOpen={showS3Browser}
        onClose={() => setShowS3Browser(false)}
        onSelectImage={handleS3ImageSelect}
        selectedCategory={formData.category}
        forceRefresh={recentUpload}
      />
    </div>
  );
}
