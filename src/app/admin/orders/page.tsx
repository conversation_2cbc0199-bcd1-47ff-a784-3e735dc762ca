'use client';

import { useState, useEffect } from 'react';
import { 
  EyeIcon, 
  CheckIcon, 
  XMarkIcon, 
  PaintBrushIcon, 
  PrinterIcon, 
  ArrowDownTrayIcon, 
  FolderOpenIcon,
  ArrowLeftIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  CreditCardIcon,
  DocumentArrowDownIcon,
  TrashIcon,
  Square3Stack3DIcon
} from '@heroicons/react/24/outline';
import Link from 'next/link';
import { format } from 'date-fns';
import ArtworkManager from '@/components/admin/ArtworkManager';

interface Order {
  id: string;
  orderNumber: string;
  customerName: string;
  email: string;
  phone: string;
  productName: string;
  quantity: number;
  customQuantity: boolean;
  unitPrice: number;
  designFee: number;
  subtotal: number;
  totalAmount: number;
  paperType?: string;
  printingSide?: string;
  meters?: number;
  needsDesign: boolean;
  designOnly: boolean;
  designBrief?: string;
  artworkFiles: string[];
  referenceFiles?: string[];
  status: string;
  paymentStatus: string;
  createdAt: string;
  updatedAt?: string;
  confirmedAt?: string;
  completedAt?: string;
  designRequests: {
    id: string;
    status: string;
    designType: string;
    urgency: string;
  }[];
}

export default function OrdersPage() {
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [downloadingFiles, setDownloadingFiles] = useState<Set<string>>(new Set());
  const [orderFiles, setOrderFiles] = useState<Record<string, any[]>>({});
  const [notification, setNotification] = useState<{ message: string; type: 'success' | 'error' } | null>(null);
  const [updating, setUpdating] = useState(false);
  const [selectedOrders, setSelectedOrders] = useState<Set<string>>(new Set());
  const [showBulkActions, setShowBulkActions] = useState(false);

  useEffect(() => {
    fetchOrders();
  }, [statusFilter]);

  const fetchOrders = async () => {
    try {
      setLoading(true);
      const url = statusFilter === 'all' 
        ? '/api/orders' 
        : `/api/orders?status=${statusFilter}`;
      
      const response = await fetch(url);
      if (response.ok) {
        const data = await response.json();
        setOrders(data);
      }
    } catch (error) {
      console.error('Error fetching orders:', error);
    } finally {
      setLoading(false);
    }
  };

  const updateOrderStatus = async (id: string, status: string) => {
    try {
      setUpdating(true);
      
      // Optimistically update the UI immediately
      setOrders(prevOrders => 
        prevOrders.map(order => 
          order.id === id 
            ? { ...order, status, updatedAt: new Date().toISOString() }
            : order
        )
      );
      
      if (selectedOrder?.id === id) {
        setSelectedOrder({ ...selectedOrder, status, updatedAt: new Date().toISOString() });
      }

      const response = await fetch(`/api/orders/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status })
      });

      if (response.ok) {
        const updatedOrder = await response.json();
        
        // Update with the actual server response
        setOrders(prevOrders => 
          prevOrders.map(order => 
            order.id === id ? updatedOrder : order
          )
        );
        
        if (selectedOrder?.id === id) {
          setSelectedOrder(updatedOrder);
        }
        
        setNotification({ message: `Order status updated to ${status.replace('_', ' ')}`, type: 'success' });
        setTimeout(() => setNotification(null), 3000);
      } else {
        // Revert optimistic update on error
        await fetchOrders();
        setNotification({ message: 'Failed to update order status', type: 'error' });
        setTimeout(() => setNotification(null), 3000);
      }
    } catch (error) {
      console.error('Error updating order status:', error);
      // Revert optimistic update on error
      await fetchOrders();
      setNotification({ message: 'Failed to update order status', type: 'error' });
      setTimeout(() => setNotification(null), 3000);
    } finally {
      setUpdating(false);
    }
  };

  // Cancel order function
  const cancelOrder = async (id: string) => {
    if (!confirm('Are you sure you want to cancel this order? This action cannot be undone.')) {
      return;
    }

    try {
      setUpdating(true);
      
      // Optimistically update the UI immediately
      setOrders(prevOrders => 
        prevOrders.map(order => 
          order.id === id 
            ? { ...order, status: 'cancelled', updatedAt: new Date().toISOString() }
            : order
        )
      );
      
      if (selectedOrder?.id === id) {
        setSelectedOrder({ ...selectedOrder, status: 'cancelled', updatedAt: new Date().toISOString() });
      }

      const response = await fetch(`/api/orders/${id}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        const result = await response.json();
        
        // Update with the actual server response
        setOrders(prevOrders => 
          prevOrders.map(order => 
            order.id === id ? result.order : order
          )
        );
        
        if (selectedOrder?.id === id) {
          setSelectedOrder(result.order);
        }
        
        setNotification({ message: 'Order cancelled successfully', type: 'success' });
        setTimeout(() => setNotification(null), 3000);
      } else {
        const error = await response.json();
        // Revert optimistic update on error
        await fetchOrders();
        setNotification({ message: error.error || 'Failed to cancel order', type: 'error' });
        setTimeout(() => setNotification(null), 3000);
      }
    } catch (error) {
      console.error('Error cancelling order:', error);
      // Revert optimistic update on error
      await fetchOrders();
      setNotification({ message: 'Failed to cancel order', type: 'error' });
      setTimeout(() => setNotification(null), 3000);
    } finally {
      setUpdating(false);
    }
  };

  // Delete order function (permanent delete)
  const deleteOrder = async (id: string) => {
    if (!confirm('Are you sure you want to permanently delete this order? This action cannot be undone.')) {
      return;
    }

    try {
      setUpdating(true);
      
      const response = await fetch(`/api/orders/${id}`, {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ permanent: true })
      });

      if (response.ok) {
        // Remove from orders list
        setOrders(prevOrders => prevOrders.filter(order => order.id !== id));
        
        // Close detail view if this order was selected
        if (selectedOrder?.id === id) {
          setSelectedOrder(null);
        }
        
        // Remove from selected orders if it was selected
        setSelectedOrders(prev => {
          const newSet = new Set(prev);
          newSet.delete(id);
          return newSet;
        });
        
        setNotification({ message: 'Order deleted successfully', type: 'success' });
        setTimeout(() => setNotification(null), 3000);
      } else {
        const error = await response.json();
        setNotification({ message: error.error || 'Failed to delete order', type: 'error' });
        setTimeout(() => setNotification(null), 3000);
      }
    } catch (error) {
      console.error('Error deleting order:', error);
      setNotification({ message: 'Failed to delete order', type: 'error' });
      setTimeout(() => setNotification(null), 3000);
    } finally {
      setUpdating(false);
    }
  };

  // Bulk actions
  const toggleOrderSelection = (orderId: string) => {
    setSelectedOrders(prev => {
      const newSet = new Set(prev);
      if (newSet.has(orderId)) {
        newSet.delete(orderId);
      } else {
        newSet.add(orderId);
      }
      return newSet;
    });
  };

  const toggleAllOrders = () => {
    if (selectedOrders.size === orders.length) {
      setSelectedOrders(new Set());
    } else {
      setSelectedOrders(new Set(orders.map(order => order.id)));
    }
  };

  const bulkUpdateStatus = async (status: string) => {
    if (selectedOrders.size === 0) return;
    
    if (!confirm(`Are you sure you want to update ${selectedOrders.size} orders to ${status.replace('_', ' ')}?`)) {
      return;
    }

    try {
      setUpdating(true);
      
      const promises = Array.from(selectedOrders).map(orderId =>
        fetch(`/api/orders/${orderId}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ status })
        })
      );

      const results = await Promise.allSettled(promises);
      const successful = results.filter(result => result.status === 'fulfilled').length;
      const failed = results.length - successful;

      if (successful > 0) {
        await fetchOrders(); // Refresh the list
        setSelectedOrders(new Set()); // Clear selection
        setNotification({ 
          message: `${successful} orders updated successfully${failed > 0 ? `, ${failed} failed` : ''}`, 
          type: successful === results.length ? 'success' : 'error' 
        });
      } else {
        setNotification({ message: 'Failed to update orders', type: 'error' });
      }
      
      setTimeout(() => setNotification(null), 3000);
    } catch (error) {
      console.error('Error updating orders:', error);
      setNotification({ message: 'Failed to update orders', type: 'error' });
      setTimeout(() => setNotification(null), 3000);
    } finally {
      setUpdating(false);
    }
  };

  const bulkDelete = async () => {
    if (selectedOrders.size === 0) return;
    
    if (!confirm(`Are you sure you want to permanently delete ${selectedOrders.size} orders? This action cannot be undone.`)) {
      return;
    }

    try {
      setUpdating(true);
      
      const promises = Array.from(selectedOrders).map(async (orderId) => {
        try {
          const response = await fetch(`/api/orders/${orderId}`, {
            method: 'DELETE',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ permanent: true })
          });

          console.log(`🗑️ [BulkDelete] Order ${orderId} - Status: ${response.status} ${response.statusText}`);

          if (response.ok) {
            const result = await response.json();
            console.log(`✅ [BulkDelete] Order ${orderId} deleted successfully:`, result);
            return { success: true, orderId, error: null };
          } else {
            const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
            console.error(`❌ [BulkDelete] Order ${orderId} failed:`, errorData);
            return { success: false, orderId, error: errorData.error || `HTTP ${response.status}` };
          }
        } catch (error) {
          console.error(`❌ [BulkDelete] Network error for order ${orderId}:`, error);
          return { success: false, orderId, error: error instanceof Error ? error.message : 'Network error' };
        }
      });

      const results = await Promise.all(promises);
      const successful = results.filter(result => result.success);
      const failed = results.filter(result => !result.success);

      console.log(`🎯 [BulkDelete] Results: ${successful.length} successful, ${failed.length} failed`);

      if (failed.length > 0) {
        console.error(`❌ [BulkDelete] Failed deletions:`, failed);
      }

      if (successful.length > 0) {
        // Remove successfully deleted orders from the list
        const deletedIds = new Set(successful.map(result => result.orderId));
        setOrders(prevOrders => 
          prevOrders.filter(order => !deletedIds.has(order.id))
        );
        setSelectedOrders(new Set()); // Clear selection
        
        let message = `${successful.length} orders deleted successfully`;
        if (failed.length > 0) {
          message += `. ${failed.length} failed: ${failed.map(f => f.error).join(', ')}`;
        }
        
        setNotification({ 
          message, 
          type: failed.length === 0 ? 'success' : 'error' 
        });
      } else {
        setNotification({ 
          message: `Failed to delete all orders: ${failed.map(f => f.error).join(', ')}`, 
          type: 'error' 
        });
      }
      
      setTimeout(() => setNotification(null), 5000); // Show notification longer for detailed messages
    } catch (error) {
      console.error('Error in bulk delete operation:', error);
      setNotification({ message: 'Failed to delete orders', type: 'error' });
      setTimeout(() => setNotification(null), 3000);
    } finally {
      setUpdating(false);
    }
  };

  // Update payment status function
  const updatePaymentStatus = async (id: string, paymentStatus: string) => {
    try {
      setUpdating(true);
      
      // Optimistically update the UI immediately
      setOrders(prevOrders => 
        prevOrders.map(order => 
          order.id === id 
            ? { ...order, paymentStatus, updatedAt: new Date().toISOString() }
            : order
        )
      );
      
      if (selectedOrder?.id === id) {
        setSelectedOrder({ ...selectedOrder, paymentStatus, updatedAt: new Date().toISOString() });
      }

      const response = await fetch(`/api/orders/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ paymentStatus })
      });

      if (response.ok) {
        const updatedOrder = await response.json();
        
        // Update with the actual server response
        setOrders(prevOrders => 
          prevOrders.map(order => 
            order.id === id ? updatedOrder : order
          )
        );
        
        if (selectedOrder?.id === id) {
          setSelectedOrder(updatedOrder);
        }
        
        setNotification({ message: `Payment status updated to ${paymentStatus}`, type: 'success' });
        setTimeout(() => setNotification(null), 3000);
      } else {
        // Revert optimistic update on error
        await fetchOrders();
        setNotification({ message: 'Failed to update payment status', type: 'error' });
        setTimeout(() => setNotification(null), 3000);
      }
    } catch (error) {
      console.error('Error updating payment status:', error);
      // Revert optimistic update on error
      await fetchOrders();
      setNotification({ message: 'Failed to update payment status', type: 'error' });
      setTimeout(() => setNotification(null), 3000);
    } finally {
      setUpdating(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'confirmed': return 'bg-blue-100 text-blue-800';
      case 'in_production': return 'bg-purple-100 text-purple-800';
      case 'ready': return 'bg-green-100 text-green-800';
      case 'delivered': return 'bg-gray-100 text-gray-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-red-100 text-red-800';
      case 'paid': return 'bg-green-100 text-green-800';
      case 'partial': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusBadge = (status: string) => {
    const baseClass = "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium";
    return (
      <span className={`${baseClass} ${getStatusColor(status)}`}>
        {status.replace('_', ' ').toUpperCase()}
      </span>
    );
  };

  const getPaymentStatusBadge = (status: string) => {
    const baseClass = "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium";
    return (
      <span className={`${baseClass} ${getPaymentStatusColor(status)}`}>
        {status.toUpperCase()}
      </span>
    );
  };

  // Fetch files for an order (both artwork and reference files)
  const fetchOrderFiles = async (orderNumber: string) => {
    try {
      console.log(`🔍 [Admin] Fetching files for order: ${orderNumber}`);
      
      // Get files from S3 using the unified artwork API
      const response = await fetch(`/api/upload/artwork?orderNumber=${orderNumber}`);
      const result = await response.json();
      
      console.log(`📋 [Admin] API response for ${orderNumber}:`, result);
      
      let files = result.success ? (result.files || []) : [];
      
      console.log(`📁 [Admin] Found ${files.length} files for ${orderNumber}:`, files);
      
      // The unified system stores all files (artwork and reference) in the same S3 structure
      // Files are automatically moved from temporary paths to final order paths
      
      setOrderFiles(prev => ({
        ...prev,
        [orderNumber]: files
      }));
      return files;
    } catch (error) {
      console.error('Error fetching order files:', error);
      setOrderFiles(prev => ({
        ...prev,
        [orderNumber]: []
      }));
      return [];
    }
  };

  // Helper function to determine file type from filename
  const getFileTypeFromName = (fileName: string): string => {
    const ext = fileName.split('.').pop()?.toLowerCase();
    switch (ext) {
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'gif':
        return 'image/gif';
      case 'webp':
        return 'image/webp';
      case 'pdf':
        return 'application/pdf';
      case 'svg':
        return 'image/svg+xml';
      case 'ai':
        return 'application/postscript';
      case 'eps':
        return 'application/eps';
      case 'psd':
        return 'image/vnd.adobe.photoshop';
      default:
        return 'application/octet-stream';
    }
  };

  // Download a single file
  const downloadFile = async (orderNumber: string, fileName: string, originalName: string) => {
    const downloadKey = `${orderNumber}-${fileName}`;
    
    try {
      setDownloadingFiles(prev => new Set(prev).add(downloadKey));
      
      const response = await fetch(
        `/api/upload/artwork/download?orderNumber=${orderNumber}&fileName=${fileName}`
      );

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = originalName;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        
        setNotification({ message: `Downloaded: ${originalName}`, type: 'success' });
        setTimeout(() => setNotification(null), 3000);
      } else {
        const error = await response.json();
        setNotification({ message: `Download failed: ${error.error || 'Unknown error'}`, type: 'error' });
        setTimeout(() => setNotification(null), 3000);
      }
    } catch (error) {
      console.error('Download error:', error);
      setNotification({ message: 'Download failed. Please try again.', type: 'error' });
      setTimeout(() => setNotification(null), 3000);
    } finally {
      setDownloadingFiles(prev => {
        const newSet = new Set(prev);
        newSet.delete(downloadKey);
        return newSet;
      });
    }
  };

  // Download all files for an order
  const downloadAllFiles = async (orderNumber: string) => {
    try {
      setDownloadingFiles(prev => new Set(prev).add(`${orderNumber}-all`));
      
      let files = orderFiles[orderNumber];
      if (!files) {
        files = await fetchOrderFiles(orderNumber);
      }

      if (!files || files.length === 0) {
        setNotification({ message: 'No artwork files found for this order', type: 'error' });
        setTimeout(() => setNotification(null), 3000);
        return;
      }

      // Download files sequentially to avoid overwhelming the browser
      for (const file of files) {
        await downloadFile(orderNumber, file.fileName, file.originalName);
        // Small delay between downloads
        await new Promise(resolve => setTimeout(resolve, 500));
      }
      
      setNotification({ message: `Downloaded ${files.length} files successfully`, type: 'success' });
      setTimeout(() => setNotification(null), 3000);
    } catch (error) {
      console.error('Error downloading all files:', error);
      setNotification({ message: 'Failed to download files. Please try again.', type: 'error' });
      setTimeout(() => setNotification(null), 3000);
    } finally {
      setDownloadingFiles(prev => {
        const newSet = new Set(prev);
        newSet.delete(`${orderNumber}-all`);
        return newSet;
      });
    }
  };

  // Show order details
  const showOrderDetails = async (order: Order) => {
    if (!orderFiles[order.orderNumber]) {
      await fetchOrderFiles(order.orderNumber);
    }
    setSelectedOrder(order);
  };

  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return 'N/A';
    try {
      return format(new Date(dateString), 'dd/MM/yyyy HH:mm');
    } catch {
      return 'Invalid Date';
    }
  };

  const formatAmount = (amount: number) => {
    // Ensure amount is a valid number
    if (isNaN(amount) || !isFinite(amount)) {
      return '0.00';
    }
    
    // Format as currency with 2 decimal places
    return amount.toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  // Order Detail View (matching receipt design)
  if (selectedOrder) {
    return (
      <div className="space-y-4 sm:space-y-6">
        {/* Notification */}
        {notification && (
          <div className={`fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg ${
            notification.type === 'success' 
              ? 'bg-green-100 border border-green-400 text-green-700' 
              : 'bg-red-100 border border-red-400 text-red-700'
          }`}>
            <div className="flex items-center">
              {notification.type === 'success' ? (
                <CheckIcon className="h-5 w-5 mr-2" />
              ) : (
                <XMarkIcon className="h-5 w-5 mr-2" />
              )}
              <span className="text-sm font-medium">{notification.message}</span>
            </div>
          </div>
        )}

        {/* Header */}
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
              <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-4 space-y-2 sm:space-y-0">
                <button
                  onClick={() => setSelectedOrder(null)}
                  className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 self-start"
                >
                  <ArrowLeftIcon className="h-4 w-4 mr-1" />
                  Back to Orders
                </button>
                <div>
                  <h1 className="text-xl sm:text-2xl font-bold text-gray-900">Order {selectedOrder.orderNumber}</h1>
                  <p className="text-sm sm:text-base text-gray-600">Created on {formatDate(selectedOrder.createdAt)}</p>
                </div>
              </div>
              <div className="flex flex-wrap gap-2">
                <button
                  onClick={() => downloadAllFiles(selectedOrder.orderNumber)}
                  disabled={downloadingFiles.has(`${selectedOrder.orderNumber}-all`)}
                  className="inline-flex items-center px-3 py-1.5 border border-blue-300 rounded text-xs font-medium text-blue-700 bg-white hover:bg-blue-50 disabled:opacity-50"
                >
                  {downloadingFiles.has(`${selectedOrder.orderNumber}-all`) ? (
                    <div className="w-3 h-3 border border-blue-600 border-t-transparent rounded-full animate-spin mr-1"></div>
                  ) : (
                    <DocumentArrowDownIcon className="h-3 w-3 mr-1" />
                  )}
                  Download Files
                </button>
              </div>
            </div>
          </div>

          {/* Status Section */}
          <div className="px-4 sm:px-6 py-4 bg-gray-50 border-b border-gray-200">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
              <div className="flex items-center space-x-4">
                <span className="text-sm text-gray-500">Status:</span>
                {getStatusBadge(selectedOrder.status)}
                {getPaymentStatusBadge(selectedOrder.paymentStatus)}
              </div>
              <div className="flex flex-wrap gap-2">
                {/* Order Status Buttons */}
                {selectedOrder.status === 'pending' && (
                  <button
                    onClick={() => updateOrderStatus(selectedOrder.id, 'confirmed')}
                    disabled={updating}
                    className="inline-flex items-center px-3 py-1.5 border border-green-300 rounded text-xs font-medium text-green-700 bg-white hover:bg-green-50 disabled:opacity-50"
                  >
                    <CheckIcon className="h-3 w-3 mr-1" />
                    Confirm Order
                  </button>
                )}
                {selectedOrder.status === 'confirmed' && (
                  <button
                    onClick={() => updateOrderStatus(selectedOrder.id, 'in_production')}
                    disabled={updating}
                    className="inline-flex items-center px-3 py-1.5 border border-purple-300 rounded text-xs font-medium text-purple-700 bg-white hover:bg-purple-50 disabled:opacity-50"
                  >
                    Start Production
                  </button>
                )}
                {selectedOrder.status === 'in_production' && (
                  <button
                    onClick={() => updateOrderStatus(selectedOrder.id, 'ready')}
                    disabled={updating}
                    className="inline-flex items-center px-3 py-1.5 border border-green-300 rounded text-xs font-medium text-green-700 bg-white hover:bg-green-50 disabled:opacity-50"
                  >
                    Mark Ready
                  </button>
                )}
                {selectedOrder.status === 'ready' && (
                  <button
                    onClick={() => updateOrderStatus(selectedOrder.id, 'delivered')}
                    disabled={updating}
                    className="inline-flex items-center px-3 py-1.5 border border-blue-300 rounded text-xs font-medium text-blue-700 bg-white hover:bg-blue-50 disabled:opacity-50"
                  >
                    Mark Delivered
                  </button>
                )}
                {['pending', 'confirmed'].includes(selectedOrder.status) && (
                  <button
                    onClick={() => cancelOrder(selectedOrder.id)}
                    disabled={updating}
                    className="inline-flex items-center px-3 py-1.5 border border-red-300 rounded text-xs font-medium text-red-700 bg-white hover:bg-red-50 disabled:opacity-50"
                  >
                    <XMarkIcon className="h-3 w-3 mr-1" />
                    Cancel Order
                  </button>
                )}
                
                {/* Delete Order Button */}
                <button
                  onClick={() => deleteOrder(selectedOrder.id)}
                  disabled={updating}
                  className="inline-flex items-center px-3 py-1.5 border border-red-300 rounded text-xs font-medium text-red-700 bg-white hover:bg-red-50 disabled:opacity-50"
                >
                  <TrashIcon className="h-3 w-3 mr-1" />
                  Delete Order
                </button>
                
                {/* Payment Status Buttons */}
                {selectedOrder.paymentStatus === 'pending' && (
                  <button
                    onClick={() => updatePaymentStatus(selectedOrder.id, 'paid')}
                    disabled={updating}
                    className="inline-flex items-center px-3 py-1.5 border border-green-300 rounded text-xs font-medium text-green-700 bg-white hover:bg-green-50 disabled:opacity-50"
                  >
                    <CreditCardIcon className="h-3 w-3 mr-1" />
                    Mark Paid
                  </button>
                )}
                {selectedOrder.paymentStatus === 'paid' && (
                  <button
                    onClick={() => updatePaymentStatus(selectedOrder.id, 'pending')}
                    disabled={updating}
                    className="inline-flex items-center px-3 py-1.5 border border-yellow-300 rounded text-xs font-medium text-yellow-700 bg-white hover:bg-yellow-50 disabled:opacity-50"
                  >
                    Mark Unpaid
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 xl:grid-cols-3 gap-4 sm:gap-6">
          {/* Order Details */}
          <div className="xl:col-span-2 space-y-4 sm:space-y-6">
            {/* Customer Information */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
                <h2 className="text-base sm:text-lg font-medium text-gray-900">Customer Information</h2>
              </div>
              <div className="px-4 sm:px-6 py-4">
                <dl className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Name</dt>
                    <dd className="mt-1 text-sm text-gray-900 break-words">{selectedOrder.customerName}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Phone Number</dt>
                    <dd className="mt-1 text-sm text-gray-900">{selectedOrder.phone}</dd>
                  </div>
                  <div className="sm:col-span-2">
                    <dt className="text-sm font-medium text-gray-500">Email</dt>
                    <dd className="mt-1 text-sm text-gray-900 break-all">{selectedOrder.email}</dd>
                  </div>
                </dl>
              </div>
            </div>

            {/* Product Details */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
                <h2 className="text-base sm:text-lg font-medium text-gray-900">Product Details</h2>
              </div>
              <div className="px-4 sm:px-6 py-4">
                <dl className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Product</dt>
                    <dd className="mt-1 text-sm text-gray-900">{selectedOrder.productName}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Quantity</dt>
                    <dd className="mt-1 text-sm text-gray-900">
                      {selectedOrder.quantity.toLocaleString()}{selectedOrder.customQuantity && ' (Custom)'}
                    </dd>
                  </div>
                  {selectedOrder.paperType && (
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Paper Type</dt>
                      <dd className="mt-1 text-sm text-gray-900">{selectedOrder.paperType}</dd>
                    </div>
                  )}
                  {selectedOrder.printingSide && (
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Printing Side</dt>
                      <dd className="mt-1 text-sm text-gray-900">{selectedOrder.printingSide}</dd>
                    </div>
                  )}
                  {selectedOrder.meters && (
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Length</dt>
                      <dd className="mt-1 text-sm text-gray-900">{selectedOrder.meters}m</dd>
                    </div>
                  )}
                </dl>
              </div>
            </div>

            {/* Design Brief */}
            {selectedOrder.needsDesign && selectedOrder.designBrief && (
              <div className="bg-white shadow rounded-lg">
                <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
                  <h2 className="text-base sm:text-lg font-medium text-gray-900">Design Brief</h2>
                </div>
                <div className="px-4 sm:px-6 py-4">
                  <p className="text-sm text-gray-700 break-words whitespace-pre-wrap">{selectedOrder.designBrief}</p>
                </div>
              </div>
            )}

            {/* Files Section - Show for all order types */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <h2 className="text-base sm:text-lg font-medium text-gray-900">
                    {selectedOrder.designOnly ? 'Reference Files' : 
                     selectedOrder.needsDesign ? 'Reference Files' : 'Artwork Files'}
                  </h2>
                  {orderFiles[selectedOrder.orderNumber]?.length > 0 && (
                    <button
                      onClick={() => downloadAllFiles(selectedOrder.orderNumber)}
                      disabled={downloadingFiles.has(`${selectedOrder.orderNumber}-all`)}
                      className="inline-flex items-center px-3 py-1.5 border border-blue-300 rounded text-xs font-medium text-blue-700 bg-white hover:bg-blue-50 disabled:opacity-50"
                    >
                      {downloadingFiles.has(`${selectedOrder.orderNumber}-all`) ? (
                        <div className="w-3 h-3 border border-blue-600 border-t-transparent rounded-full animate-spin mr-1"></div>
                      ) : (
                        <ArrowDownTrayIcon className="h-3 w-3 mr-1" />
                      )}
                      Download All
                    </button>
                  )}
                </div>
              </div>
              <div className="px-4 sm:px-6 py-4">
                {orderFiles[selectedOrder.orderNumber]?.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {(orderFiles[selectedOrder.orderNumber] || []).map((file, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border">
                        <div className="flex items-center space-x-3">
                          <div className="w-8 h-8 bg-blue-100 rounded flex items-center justify-center">
                            {file.fileType.startsWith('image/') ? (
                              <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                              </svg>
                            ) : (
                              <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                              </svg>
                            )}
                          </div>
                          <div>
                            <p className="text-sm font-medium text-gray-900 truncate max-w-40" title={file.originalName}>
                              {file.originalName}
                            </p>
                            <p className="text-xs text-gray-500">
                              {(file.fileSize / 1024 / 1024).toFixed(2)} MB
                            </p>
                          </div>
                        </div>
                        <button
                          onClick={() => downloadFile(selectedOrder.orderNumber, file.fileName, file.originalName)}
                          disabled={downloadingFiles.has(`${selectedOrder.orderNumber}-${file.fileName}`)}
                          className="text-blue-600 hover:text-blue-800 disabled:text-gray-400 p-2"
                          title="Download file"
                        >
                          {downloadingFiles.has(`${selectedOrder.orderNumber}-${file.fileName}`) ? (
                            <div className="w-4 h-4 border border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                          ) : (
                            <ArrowDownTrayIcon className="h-4 w-4" />
                          )}
                        </button>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <p>No {selectedOrder.designOnly ? 'reference' : selectedOrder.needsDesign ? 'reference' : 'artwork'} files found for this order.</p>
                    <p className="text-xs mt-1">Files are automatically fetched when viewing order details.</p>
                    {/* Show database info for debugging */}
                    {selectedOrder.artworkFiles && selectedOrder.artworkFiles.length > 0 && (
                      <div className="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded text-left">
                        <p className="text-xs font-medium text-yellow-800 mb-1">Database shows {selectedOrder.artworkFiles.length} file(s):</p>
                        <ul className="text-xs text-yellow-700 space-y-1">
                          {selectedOrder.artworkFiles.slice(0, 3).map((file, index) => (
                            <li key={index} className="font-mono truncate">{file}</li>
                          ))}
                          {selectedOrder.artworkFiles.length > 3 && (
                            <li className="text-yellow-600">...and {selectedOrder.artworkFiles.length - 3} more</li>
                          )}
                        </ul>
                        <p className="text-xs text-yellow-600 mt-2">Files may be in a different S3 location. Contact technical support if needed.</p>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-4 sm:space-y-6">
            {/* Pricing Summary */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
                <h2 className="text-base sm:text-lg font-medium text-gray-900">Pricing Summary</h2>
              </div>
              <div className="px-4 sm:px-6 py-4">
                <dl className="space-y-3">
                  {/* Show different pricing layout for design-only vs regular orders */}
                  {selectedOrder.designOnly ? (
                    <>
                      <div className="flex justify-between">
                        <dt className="text-sm text-gray-500">Service Type</dt>
                        <dd className="text-sm font-medium text-blue-600">Design Only</dd>
                      </div>
                      <div className="flex justify-between">
                        <dt className="text-sm text-gray-500">Design Fee</dt>
                        <dd className="text-sm font-medium text-gray-900">
                          KSh {formatAmount(selectedOrder.designFee > 0 ? selectedOrder.designFee : selectedOrder.totalAmount)}
                        </dd>
                      </div>
                      <div className="border-t border-gray-200 pt-3">
                        <div className="flex justify-between">
                          <dt className="text-base font-medium text-gray-900">Total Amount</dt>
                          <dd className="text-base font-medium text-gray-900">
                            KSh {formatAmount(selectedOrder.totalAmount > 0 ? selectedOrder.totalAmount : selectedOrder.designFee)}
                          </dd>
                        </div>
                      </div>
                    </>
                  ) : (
                    <>
                      <div className="flex justify-between">
                        <dt className="text-sm text-gray-500">Unit Price</dt>
                        <dd className="text-sm font-medium text-gray-900">KSh {formatAmount(selectedOrder.unitPrice)}</dd>
                      </div>
                      <div className="flex justify-between">
                        <dt className="text-sm text-gray-500">Quantity</dt>
                        <dd className="text-sm font-medium text-gray-900">{selectedOrder.quantity.toLocaleString()}</dd>
                      </div>
                      <div className="flex justify-between">
                        <dt className="text-sm text-gray-500">Subtotal</dt>
                        <dd className="text-sm font-medium text-gray-900">KSh {formatAmount(selectedOrder.subtotal)}</dd>
                      </div>
                      {(selectedOrder.designFee > 0 || selectedOrder.needsDesign) && (
                        <div className="flex justify-between">
                          <dt className="text-sm text-gray-500">Design Fee</dt>
                          <dd className="text-sm font-medium text-blue-600">KSh {formatAmount(selectedOrder.designFee)}</dd>
                        </div>
                      )}
                      <div className="border-t border-gray-200 pt-3">
                        <div className="flex justify-between">
                          <dt className="text-base font-medium text-gray-900">Total Amount</dt>
                          <dd className="text-base font-medium text-gray-900">KSh {formatAmount(selectedOrder.totalAmount)}</dd>
                        </div>
                      </div>
                    </>
                  )}
                </dl>
              </div>
            </div>

            {/* Order Information */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
                <h2 className="text-base sm:text-lg font-medium text-gray-900">Order Information</h2>
              </div>
              <div className="px-4 sm:px-6 py-4">
                <dl className="space-y-3">
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Order Number</dt>
                    <dd className="mt-1 text-sm text-gray-900 break-all">{selectedOrder.orderNumber}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Order Date</dt>
                    <dd className="mt-1 text-sm text-gray-900">{formatDate(selectedOrder.createdAt)}</dd>
                  </div>
                  {selectedOrder.confirmedAt && (
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Confirmed Date</dt>
                      <dd className="mt-1 text-sm text-gray-900">{formatDate(selectedOrder.confirmedAt)}</dd>
                    </div>
                  )}
                  {selectedOrder.completedAt && (
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Completed Date</dt>
                      <dd className="mt-1 text-sm text-gray-900">{formatDate(selectedOrder.completedAt)}</dd>
                    </div>
                  )}
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Service Type</dt>
                    <dd className="mt-1 text-sm text-gray-900">
                      {selectedOrder.designOnly ? 'Design Only' : selectedOrder.needsDesign ? 'Design + Print' : 'Print Only'}
                    </dd>
                  </div>
                </dl>
              </div>
            </div>

            {/* Design Requests */}
            {(selectedOrder.designRequests || []).length > 0 && (
              <div className="bg-white shadow rounded-lg">
                <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
                  <h2 className="text-base sm:text-lg font-medium text-gray-900">Design Requests</h2>
                </div>
                <div className="px-4 sm:px-6 py-4">
                  <div className="space-y-3">
                    {(selectedOrder.designRequests || []).map((request) => (
                      <div key={request.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div>
                          <p className="text-sm font-medium text-gray-900">{request.designType}</p>
                          <p className="text-xs text-gray-500">Urgency: {request.urgency}</p>
                        </div>
                        <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(request.status)}`}>
                          {request.status}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }

  // Orders List View (improved with receipt-style cards)
  return (
    <div className="space-y-4 sm:space-y-6">
      {/* Notification */}
      {notification && (
        <div className={`fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg ${
          notification.type === 'success' 
            ? 'bg-green-100 border border-green-400 text-green-700' 
            : 'bg-red-100 border border-red-400 text-red-700'
        }`}>
          <div className="flex items-center">
            {notification.type === 'success' ? (
              <CheckIcon className="h-5 w-5 mr-2" />
            ) : (
              <XMarkIcon className="h-5 w-5 mr-2" />
            )}
            <span className="text-sm font-medium">{notification.message}</span>
          </div>
        </div>
      )}

      {/* Header */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
            <div>
              <h1 className="text-xl sm:text-2xl font-bold text-gray-900">Orders Management</h1>
              <p className="text-sm sm:text-base text-gray-600">Manage customer orders and design requests</p>
            </div>
          </div>
        </div>

        {/* Stats Summary */}
        <div className="px-4 sm:px-6 py-4 bg-gray-50 border-b border-gray-200">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3 sm:gap-4">
            <div className="text-center">
              <div className="text-lg sm:text-2xl font-bold text-blue-600">{orders.length}</div>
              <div className="text-xs sm:text-sm text-gray-600">Total Orders</div>
            </div>
            <div className="text-center">
              <div className="text-lg sm:text-2xl font-bold text-yellow-600">
                {orders.filter(o => o.status === 'pending').length}
              </div>
              <div className="text-xs sm:text-sm text-gray-600">Pending</div>
            </div>
            <div className="text-center">
              <div className="text-lg sm:text-2xl font-bold text-purple-600">
                {orders.filter(o => o.status === 'in_production').length}
              </div>
              <div className="text-xs sm:text-sm text-gray-600">In Production</div>
            </div>
            <div className="text-center">
              <div className="text-lg sm:text-2xl font-bold text-gray-900">
                KSh {formatAmount(orders.reduce((sum, o) => {
                  const amount = Number(o.totalAmount) || 0;
                  return sum + amount;
                }, 0))}
              </div>
              <div className="text-xs sm:text-sm text-gray-600">Total Value</div>
            </div>
          </div>
        </div>
      </div>

      {/* Bulk Actions Bar */}
      {selectedOrders.size > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg">
          <div className="px-4 sm:px-6 py-3">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
              <div className="flex items-center">
                <Square3Stack3DIcon className="h-5 w-5 text-blue-600 mr-2" />
                <span className="text-sm font-medium text-blue-900">
                  {selectedOrders.size} order{selectedOrders.size !== 1 ? 's' : ''} selected
                </span>
              </div>
              <div className="flex flex-wrap gap-2">
                <button
                  onClick={() => bulkUpdateStatus('confirmed')}
                  disabled={updating}
                  className="inline-flex items-center px-3 py-1.5 border border-green-300 rounded text-xs font-medium text-green-700 bg-white hover:bg-green-50 disabled:opacity-50"
                >
                  <CheckIcon className="h-3 w-3 mr-1" />
                  Confirm All
                </button>
                <button
                  onClick={() => bulkUpdateStatus('in_production')}
                  disabled={updating}
                  className="inline-flex items-center px-3 py-1.5 border border-purple-300 rounded text-xs font-medium text-purple-700 bg-white hover:bg-purple-50 disabled:opacity-50"
                >
                  Start Production
                </button>
                <button
                  onClick={() => bulkUpdateStatus('ready')}
                  disabled={updating}
                  className="inline-flex items-center px-3 py-1.5 border border-blue-300 rounded text-xs font-medium text-blue-700 bg-white hover:bg-blue-50 disabled:opacity-50"
                >
                  Mark Ready
                </button>
                <button
                  onClick={() => bulkUpdateStatus('delivered')}
                  disabled={updating}
                  className="inline-flex items-center px-3 py-1.5 border border-green-300 rounded text-xs font-medium text-green-700 bg-white hover:bg-green-50 disabled:opacity-50"
                >
                  Mark Delivered
                </button>
                <button
                  onClick={bulkDelete}
                  disabled={updating}
                  className="inline-flex items-center px-3 py-1.5 border border-red-300 rounded text-xs font-medium text-red-700 bg-white hover:bg-red-50 disabled:opacity-50"
                >
                  <TrashIcon className="h-3 w-3 mr-1" />
                  Delete All
                </button>
                <button
                  onClick={() => setSelectedOrders(new Set())}
                  className="inline-flex items-center px-3 py-1.5 border border-gray-300 rounded text-xs font-medium text-gray-700 bg-white hover:bg-gray-50"
                >
                  <XMarkIcon className="h-3 w-3 mr-1" />
                  Clear
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Filters */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 sm:px-6 py-4">
          <div className="flex flex-wrap gap-4">
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="border border-gray-300 rounded px-3 py-2 text-sm focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="all">All Orders</option>
              <option value="pending">Pending</option>
              <option value="confirmed">Confirmed</option>
              <option value="in_production">In Production</option>
              <option value="ready">Ready</option>
              <option value="delivered">Delivered</option>
            </select>
          </div>
        </div>
      </div>

      {/* Orders List - Mobile Cards & Desktop Table */}
      <div className="bg-white shadow rounded-lg">
        {/* Mobile Card View */}
        <div className="block sm:hidden">
          {orders.length === 0 ? (
            <div className="px-4 py-8 text-center text-gray-500">
              <p>No orders found for the selected filter.</p>
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {orders.map((order) => (
                <div key={order.id} className="px-4 py-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {order.orderNumber}
                      </p>
                      <p className="text-xs text-gray-500">
                        {order.productName}
                      </p>
                    </div>
                    <div className="ml-3 flex flex-col gap-1">
                      {getStatusBadge(order.status)}
                      {getPaymentStatusBadge(order.paymentStatus)}
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-3 mb-3">
                    <div>
                      <p className="text-xs text-gray-500">Customer</p>
                      <p className="text-sm font-medium text-gray-900 truncate">{order.customerName}</p>
                      <p className="text-xs text-gray-500">{order.phone}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-xs text-gray-500">Amount</p>
                      <p className="text-sm font-medium text-gray-900">KSh {formatAmount(Number(order.totalAmount) || 0)}</p>
                      <p className="text-xs text-gray-500">Qty: {order.quantity.toLocaleString()}</p>
                    </div>
                  </div>
                  
                  <div className="mb-3">
                    <p className="text-xs text-gray-500">Date</p>
                    <p className="text-sm text-gray-900">{formatDate(order.createdAt)}</p>
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-2 text-xs text-gray-500">
                      {order.designOnly ? (
                        <div className="flex items-center text-purple-600">
                          <PaintBrushIcon className="h-3 w-3 mr-1" />
                          Design Only
                        </div>
                      ) : order.needsDesign ? (
                        <div className="flex items-center text-blue-600">
                          <PaintBrushIcon className="h-3 w-3 mr-1" />
                          Design + Print
                        </div>
                      ) : (
                        <div className="flex items-center text-green-600">
                          <PrinterIcon className="h-3 w-3 mr-1" />
                          Files: {order.artworkFiles.length}
                        </div>
                      )}
                    </div>
                    <button
                      onClick={() => showOrderDetails(order)}
                      className="inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
                    >
                      <EyeIcon className="h-3 w-3 mr-1" />
                      View
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Desktop Table View */}
        <div className="hidden sm:block overflow-x-auto">
          {orders.length === 0 ? (
            <div className="px-6 py-8 text-center text-gray-500">
              <p>No orders found for the selected filter.</p>
            </div>
          ) : (
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-3 py-3 text-left">
                    <input
                      type="checkbox"
                      checked={selectedOrders.size === orders.length && orders.length > 0}
                      onChange={toggleAllOrders}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Order
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Customer
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Product
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Amount
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {orders.map((order) => (
                  <tr key={order.id} className="hover:bg-gray-50">
                    <td className="px-3 py-4 whitespace-nowrap">
                      <input
                        type="checkbox"
                        checked={selectedOrders.has(order.id)}
                        onChange={() => toggleOrderSelection(order.id)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{order.orderNumber}</div>
                      <div className="text-xs text-gray-500 flex items-center space-x-2">
                        {order.designOnly ? (
                          <span className="inline-flex items-center text-purple-600">
                            <PaintBrushIcon className="h-3 w-3 mr-1" />
                            Design Only
                          </span>
                        ) : order.needsDesign ? (
                          <span className="inline-flex items-center text-blue-600">
                            <PaintBrushIcon className="h-3 w-3 mr-1" />
                            Design + Print
                          </span>
                        ) : (
                          <span className="inline-flex items-center text-green-600">
                            <PrinterIcon className="h-3 w-3 mr-1" />
                            {order.artworkFiles.length} files
                          </span>
                        )}
                        <div className="flex space-x-1">
                          {getStatusBadge(order.status)}
                          {getPaymentStatusBadge(order.paymentStatus)}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{order.customerName}</div>
                      <div className="text-sm text-gray-500">{order.phone}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{order.productName}</div>
                      <div className="text-sm text-gray-500">Qty: {order.quantity.toLocaleString()}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      KSh {formatAmount(Number(order.totalAmount) || 0)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">{formatDate(order.createdAt)}</div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex flex-wrap justify-end gap-1">
                        <button
                          onClick={() => showOrderDetails(order)}
                          className="inline-flex items-center px-2 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
                        >
                          <EyeIcon className="h-3 w-3 mr-1" />
                          View
                        </button>
                        {((!order.needsDesign || order.designOnly) && order.artworkFiles.length > 0) && (
                          <button
                            onClick={() => downloadAllFiles(order.orderNumber)}
                            disabled={downloadingFiles.has(`${order.orderNumber}-all`)}
                            className="inline-flex items-center px-2 py-1 border border-blue-300 shadow-sm text-xs font-medium rounded text-blue-700 bg-white hover:bg-blue-50 disabled:opacity-50"
                          >
                            {downloadingFiles.has(`${order.orderNumber}-all`) ? (
                              <div className="w-3 h-3 border border-blue-600 border-t-transparent rounded-full animate-spin mr-1"></div>
                            ) : (
                              <ArrowDownTrayIcon className="h-3 w-3 mr-1" />
                            )}
                            Files
                          </button>
                        )}
                        <button
                          onClick={() => deleteOrder(order.id)}
                          disabled={updating}
                          className="inline-flex items-center px-2 py-1 border border-red-300 shadow-sm text-xs font-medium rounded text-red-700 bg-white hover:bg-red-50 disabled:opacity-50"
                        >
                          <TrashIcon className="h-3 w-3 mr-1" />
                          Delete
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          )}
        </div>
      </div>
    </div>
  );
} 