'use client';

import { useState, useEffect } from 'react';
import { 
  EyeIcon, 
  CheckIcon, 
  XMarkIcon, 
  UserIcon, 
  ClockIcon,
  PaintBrushIcon,
  DocumentTextIcon,
  CurrencyDollarIcon,
  FunnelIcon,
  MagnifyingGlassIcon,
  CalendarIcon,
  PhoneIcon,
  EnvelopeIcon,
  SparklesIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  PlayIcon,
  PauseIcon,
  ArrowPathIcon,
  ChartBarIcon,
  UsersIcon,
  ClipboardDocumentListIcon,
  AdjustmentsHorizontalIcon,
  CloudArrowUpIcon,
  PaperAirplaneIcon,
  DocumentArrowUpIcon
} from '@heroicons/react/24/outline';
import { 
  CheckCircleIcon as CheckCircleIconSolid,
  ClockIcon as ClockIconSolid,
  ExclamationTriangleIcon as ExclamationTriangleIconSolid,
  XCircleIcon as XCircleIconSolid
} from '@heroicons/react/24/solid';

interface DesignRequest {
  id: string;
  customerName: string;
  email: string;
  phone: string;
  productType: string;
  quantity: number;
  specifications: string;
  urgency: string;
  designType: string;
  status: string;
  designFee: number;
  printCost: number;
  totalCost: number;
  requestedAt: string;
  assignedTo?: string;
  designer?: {
    name: string;
    email: string;
  };
}

interface UploadedFile {
  fileName: string;
  originalName: string;
  fileSize: number;
  fileType: string;
  filePath: string;
  uploaded: boolean;
}

export default function DesignRequestsPage() {
  const [requests, setRequests] = useState<DesignRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedRequest, setSelectedRequest] = useState<DesignRequest | null>(null);
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [urgencyFilter, setUrgencyFilter] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState<'date' | 'amount' | 'urgency'>('date');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  
  // Communication modal state
  const [showSubmitModal, setShowSubmitModal] = useState(false);
  const [submitRequest, setSubmitRequest] = useState<DesignRequest | null>(null);
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitMessage, setSubmitMessage] = useState('');
  const [communicationSubject, setCommunicationSubject] = useState('');

  useEffect(() => {
    fetchDesignRequests();
  }, [statusFilter, urgencyFilter]);

  const fetchDesignRequests = async () => {
    try {
      setLoading(true);
      let url = '/api/admin/design-requests';
      const params = new URLSearchParams();
      
      if (statusFilter !== 'all') params.append('status', statusFilter);
      if (urgencyFilter !== 'all') params.append('urgency', urgencyFilter);
      
      if (params.toString()) {
        url += `?${params.toString()}`;
      }
      
      const response = await fetch(url);
      if (response.ok) {
        const data = await response.json();
        setRequests(data);
      }
    } catch (error) {
      console.error('Error fetching design requests:', error);
    } finally {
      setLoading(false);
    }
  };

  const updateRequestStatus = async (id: string, status: string) => {
    try {
      const response = await fetch(`/api/admin/design-requests/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status })
      });

      if (response.ok) {
        await fetchDesignRequests();
        setSelectedRequest(null);
      }
    } catch (error) {
      console.error('Error updating request status:', error);
    }
  };

  // Handle Submit Job with Communication
  const handleSubmitJob = (request: DesignRequest) => {
    setSubmitRequest(request);
    setCommunicationSubject(`Design Completed: ${request.productType} for ${request.customerName}`);
    setSubmitMessage(`Hi ${request.customerName},\n\nGreat news! Your ${request.productType} design is ready!\n\nWe've attached the completed design files for your review. The design includes:\n- High-resolution files ready for print\n- Source files for future edits\n- Multiple format options\n\nPlease review the attached files and let us know if you need any adjustments.\n\nThank you for choosing our design services!\n\nBest regards,\nMocky Digital Team`);
    setShowSubmitModal(true);
  };

  // Handle file upload
  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files || files.length === 0) return;

    setIsUploading(true);
    const newUploadedFiles: UploadedFile[] = [];

    try {
      const formData = new FormData();
      
      // Add all files to FormData with the correct field name 'files'
      for (const file of Array.from(files)) {
        // Validate file size before uploading
        if (file.size > 10 * 1024 * 1024) {
          alert(`File ${file.name} is too large. Maximum size is 10MB.`);
          continue;
        }
        formData.append('files', file);
      }
      
      // Add order number
      formData.append('orderNumber', `DESIGN-${submitRequest?.id || Date.now()}`);

      const response = await fetch('/api/upload/artwork', {
        method: 'POST',
        body: formData,
      });

      if (response.ok) {
        const result = await response.json();
        
        // Process each uploaded file from the response
        if (result.uploadedFiles && Array.isArray(result.uploadedFiles)) {
          for (const uploadedFile of result.uploadedFiles) {
            newUploadedFiles.push({
              fileName: uploadedFile.fileName,
              originalName: uploadedFile.originalName,
              fileSize: uploadedFile.fileSize,
              fileType: uploadedFile.fileType,
              filePath: uploadedFile.url,
              uploaded: true,
            });
          }
        }
        
        setUploadedFiles([...uploadedFiles, ...newUploadedFiles]);
      } else {
        const errorData = await response.json();
        console.error('Upload failed:', errorData);
        alert(`Upload failed: ${errorData.error || 'Unknown error'}`);
      }
    } catch (error) {
      console.error('Error uploading files:', error);
      alert('Error uploading files');
    } finally {
      setIsUploading(false);
    }
  };

  // Submit the job with communication
  const handleSubmitWithCommunication = async () => {
    if (!submitRequest || uploadedFiles.length === 0) {
      alert('Please upload at least one design file before submitting.');
      return;
    }

    setIsSubmitting(true);

    try {
      // First, find or create the client
      let clientId = null;
      
      // Try to find existing client by email using search parameter
      const clientResponse = await fetch(`/api/admin/clients?search=${encodeURIComponent(submitRequest.email)}`);
      if (clientResponse.ok) {
        const clientData = await clientResponse.json();
        if (clientData.clients && clientData.clients.length > 0) {
          // Find exact email match
          const exactMatch = clientData.clients.find((client: any) => client.email === submitRequest.email);
          if (exactMatch) {
            clientId = exactMatch.id;
          }
        }
      }

      // If no client found, create one
      if (!clientId) {
        const createClientResponse = await fetch('/api/admin/clients', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            name: submitRequest.customerName,
            email: submitRequest.email,
            phone: submitRequest.phone,
            status: 'active',
            notes: `Created from design request: ${submitRequest.productType}`,
          }),
        });

        if (createClientResponse.ok) {
          const newClient = await createClientResponse.json();
          clientId = newClient.client.id;
        } else if (createClientResponse.status === 409) {
          // Client already exists, try to find them again with search
          const retryClientResponse = await fetch(`/api/admin/clients?search=${encodeURIComponent(submitRequest.email)}`);
          if (retryClientResponse.ok) {
            const retryClientData = await retryClientResponse.json();
            if (retryClientData.clients && retryClientData.clients.length > 0) {
              // Find exact email match
              const exactMatch = retryClientData.clients.find((client: any) => client.email === submitRequest.email);
              if (exactMatch) {
                clientId = exactMatch.id;
              }
            }
          }
          if (!clientId) {
            throw new Error('Failed to find or create client');
          }
        } else {
          const errorData = await createClientResponse.json();
          throw new Error(`Failed to create client: ${errorData.error || 'Unknown error'}`);
        }
      }

      // Create communication record
      const communicationData = {
        clientId,
        type: 'message',
        subject: communicationSubject,
        message: submitMessage,
        direction: 'outbound',
        status: 'sent',
        priority: 'medium',
        attachments: uploadedFiles.map(file => ({
          fileName: file.fileName,
          originalName: file.originalName,
          fileSize: file.fileSize,
          fileType: file.fileType,
          fileUrl: file.filePath,
        })),
      };

      const communicationResponse = await fetch('/api/admin/communications', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(communicationData),
      });

      if (!communicationResponse.ok) {
        throw new Error('Failed to create communication');
      }

      // Update design request status to completed
      await updateRequestStatus(submitRequest.id, 'completed');

      // Generate WhatsApp message
      const whatsappMessage = `Hi ${submitRequest.customerName}! 🎨\n\nYour ${submitRequest.productType} design is ready! \n\nWe've sent the files to your email (${submitRequest.email}). Please check your inbox for the completed design files.\n\nNeed any changes? Just reply to this message!\n\nThanks for choosing Mocky Digital! ✨`;
      
      // Use business WhatsApp number instead of client's phone
      const businessWhatsAppNumber = '254741590670';
      const whatsappUrl = `https://wa.me/${businessWhatsAppNumber}?text=${encodeURIComponent(whatsappMessage)}`;
      
      // Send email with attachments automatically
      const emailSubject = `Design Completed: ${submitRequest.productType}`;
      const emailMessage = `${submitMessage}\n\nFor any questions, please contact <NAME_EMAIL> or call us at +254 741 590 670.`;
      
      // Debug: Log the attachments data
      const attachmentsData = uploadedFiles.map(file => ({
        fileName: file.fileName,
        originalName: file.originalName,
        fileUrl: file.filePath,
        fileType: file.fileType,
      }));
      console.log('Sending email with attachments:', attachmentsData);
      
      // Send email with attachments
      const emailResponse = await fetch('/api/admin/send-email', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          to: submitRequest.email,
          subject: emailSubject,
          message: emailMessage,
          attachments: attachmentsData,
        }),
      });

      const emailResult = await emailResponse.json();
      
      // Show success message
      if (emailResult.success) {
        alert('Design submitted successfully! Email sent with attachments and communication record created.');
      } else {
        alert(`Design submitted successfully! Communication record created.\n\nNote: Email sending failed - ${emailResult.error || 'Unknown error'}`);
      }
      
      // Open WhatsApp
      if (confirm('Open WhatsApp to send message to client?')) {
        window.open(whatsappUrl, '_blank');
      }

      // Reset modal state
      setShowSubmitModal(false);
      setSubmitRequest(null);
      setUploadedFiles([]);
      setSubmitMessage('');
      setCommunicationSubject('');

    } catch (error) {
      console.error('Error submitting job:', error);
      alert('Error submitting job. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Remove uploaded file
  const removeUploadedFile = (index: number) => {
    const newFiles = uploadedFiles.filter((_, i) => i !== index);
    setUploadedFiles(newFiles);
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    if (isNaN(amount) || !isFinite(amount)) {
      return 'KSh 0.00';
    }
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
      minimumFractionDigits: 2,
    }).format(Number(amount) || 0);
  };

  // Utility functions for status and urgency
  const getStatusConfig = (status: string) => {
    switch (status) {
      case 'pending': 
        return { 
          color: 'bg-amber-50 text-amber-700 border-amber-200', 
          icon: ClockIconSolid,
          iconColor: 'text-amber-500',
          label: 'Pending Review'
        };
      case 'in_progress': 
        return { 
          color: 'bg-blue-50 text-blue-700 border-blue-200', 
          icon: PlayIcon,
          iconColor: 'text-blue-500',
          label: 'In Progress'
        };
      case 'completed': 
        return { 
          color: 'bg-emerald-50 text-emerald-700 border-emerald-200', 
          icon: CheckCircleIconSolid,
          iconColor: 'text-emerald-500',
          label: 'Completed'
        };
      case 'cancelled': 
        return { 
          color: 'bg-red-50 text-red-700 border-red-200', 
          icon: XCircleIconSolid,
          iconColor: 'text-red-500',
          label: 'Cancelled'
        };
      default: 
        return { 
          color: 'bg-gray-50 text-gray-700 border-gray-200', 
          icon: ClockIconSolid,
          iconColor: 'text-gray-500',
          label: status
        };
    }
  };

  const getUrgencyConfig = (urgency: string) => {
    switch (urgency) {
      case 'urgent': 
        return { 
          color: 'text-red-600 bg-red-50 border-red-200', 
          icon: ExclamationTriangleIconSolid,
          label: 'Urgent'
        };
      case 'standard': 
        return { 
          color: 'text-blue-600 bg-blue-50 border-blue-200', 
          icon: ClockIconSolid,
          label: 'Standard'
        };
      case 'flexible': 
        return { 
          color: 'text-green-600 bg-green-50 border-green-200', 
          icon: CheckCircleIconSolid,
          label: 'Flexible'
        };
      default: 
        return { 
          color: 'text-gray-600 bg-gray-50 border-gray-200', 
          icon: ClockIconSolid,
          label: urgency
        };
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getTimeAgo = (dateString: string) => {
    const now = new Date();
    const date = new Date(dateString);
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d ago`;
    return formatDate(dateString);
  };

  // Calculate statistics
  const stats = {
    total: requests.length,
    pending: requests.filter(r => r.status === 'pending').length,
    inProgress: requests.filter(r => r.status === 'in_progress').length,
    completed: requests.filter(r => r.status === 'completed').length,
    totalValue: requests.reduce((sum, r) => sum + (Number(r.totalCost) || 0), 0),
    avgValue: requests.length > 0 ? requests.reduce((sum, r) => sum + (Number(r.totalCost) || 0), 0) / requests.length : 0,
  };

  // Filter and sort requests
  const filteredRequests = requests
    .filter(request => {
      const matchesStatus = statusFilter === 'all' || request.status === statusFilter;
      const matchesUrgency = urgencyFilter === 'all' || request.urgency === urgencyFilter;
      const matchesSearch = searchTerm === '' || 
        request.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        request.productType.toLowerCase().includes(searchTerm.toLowerCase()) ||
        request.email.toLowerCase().includes(searchTerm.toLowerCase());
      
      return matchesStatus && matchesUrgency && matchesSearch;
    })
    .sort((a, b) => {
      let comparison = 0;
      
      switch (sortBy) {
        case 'date':
          comparison = new Date(a.requestedAt).getTime() - new Date(b.requestedAt).getTime();
          break;
        case 'amount':
          comparison = (Number(a.totalCost) || 0) - (Number(b.totalCost) || 0);
          break;
        case 'urgency':
          const urgencyOrder = { 'urgent': 3, 'high': 2, 'standard': 1, 'flexible': 0 };
          comparison = (urgencyOrder[a.urgency as keyof typeof urgencyOrder] || 0) - 
                      (urgencyOrder[b.urgency as keyof typeof urgencyOrder] || 0);
          break;
      }
      
      return sortOrder === 'desc' ? -comparison : comparison;
    });

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white p-8 rounded-lg shadow-sm">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 text-center">Loading design requests...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header Section */}
      <div className="bg-white border-b border-gray-200">
        <div className="px-4 sm:px-6 py-4 sm:py-6">
          <div className="flex flex-col space-y-4 sm:space-y-0 sm:flex-row sm:items-center sm:justify-between">
            <div className="flex-1 min-w-0">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-purple-500 to-blue-600 rounded-lg sm:rounded-xl flex items-center justify-center">
                    <PaintBrushIcon className="h-5 w-5 sm:h-6 sm:w-6 text-white" />
                  </div>
                </div>
                <div className="ml-3 sm:ml-4">
                  <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900">Design Requests</h1>
                  <p className="text-sm sm:text-base lg:text-lg text-gray-600">Manage and track customer design service requests</p>
                </div>
              </div>
            </div>
            
            <div className="flex items-center justify-end">
              <button
                onClick={() => fetchDesignRequests()}
                className="inline-flex items-center px-3 py-2 sm:px-4 sm:py-2 border border-gray-300 rounded-lg shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <ArrowPathIcon className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">Refresh</span>
                <span className="sm:hidden">Refresh</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Dashboard */}
      <div className="px-4 sm:px-6 py-3 sm:py-4">
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-3 sm:gap-4">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-3 sm:p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ClipboardDocumentListIcon className="h-5 w-5 sm:h-6 sm:w-6 text-gray-600" />
              </div>
              <div className="ml-2 sm:ml-3 min-w-0">
                <p className="text-xs font-medium text-gray-600 truncate">Total</p>
                <p className="text-base sm:text-lg font-bold text-gray-900">{stats.total}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-3 sm:p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ClockIcon className="h-5 w-5 sm:h-6 sm:w-6 text-amber-500" />
              </div>
              <div className="ml-2 sm:ml-3 min-w-0">
                <p className="text-xs font-medium text-gray-600 truncate">Pending</p>
                <p className="text-base sm:text-lg font-bold text-amber-600">{stats.pending}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-3 sm:p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <PlayIcon className="h-5 w-5 sm:h-6 sm:w-6 text-blue-500" />
              </div>
              <div className="ml-2 sm:ml-3 min-w-0">
                <p className="text-xs font-medium text-gray-600 truncate">Progress</p>
                <p className="text-base sm:text-lg font-bold text-blue-600">{stats.inProgress}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-3 sm:p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CheckCircleIcon className="h-5 w-5 sm:h-6 sm:w-6 text-emerald-500" />
              </div>
              <div className="ml-2 sm:ml-3 min-w-0">
                <p className="text-xs font-medium text-gray-600 truncate">Done</p>
                <p className="text-base sm:text-lg font-bold text-emerald-600">{stats.completed}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-3 sm:p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CurrencyDollarIcon className="h-5 w-5 sm:h-6 sm:w-6 text-green-500" />
              </div>
              <div className="ml-2 sm:ml-3 min-w-0">
                <p className="text-xs font-medium text-gray-600 truncate">Total</p>
                <p className="text-sm sm:text-lg font-bold text-green-600 truncate">{formatCurrency(stats.totalValue)}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-3 sm:p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ChartBarIcon className="h-5 w-5 sm:h-6 sm:w-6 text-purple-500" />
              </div>
              <div className="ml-2 sm:ml-3 min-w-0">
                <p className="text-xs font-medium text-gray-600 truncate">Average</p>
                <p className="text-sm sm:text-lg font-bold text-purple-600 truncate">{formatCurrency(stats.avgValue)}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Controls */}
      <div className="px-4 sm:px-6 pb-4 sm:pb-6">
        <div className="bg-white rounded-lg sm:rounded-xl shadow-sm border border-gray-200 p-4 sm:p-6">
          <div className="space-y-4">
            {/* Search */}
            <div className="w-full">
              <div className="relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder="Search requests..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="block w-full pl-10 pr-3 py-2.5 sm:py-3 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm sm:text-base"
                />
              </div>
            </div>

            {/* Filters */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0 sm:space-x-3">
              <div className="flex flex-wrap items-center gap-2 sm:gap-3">
                <div className="flex items-center space-x-2">
                  <FunnelIcon className="h-4 w-4 sm:h-5 sm:w-5 text-gray-400" />
                  <select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                    className="border border-gray-300 rounded-lg pl-2 pr-8 py-1.5 sm:pl-3 sm:pr-10 sm:py-2 text-xs sm:text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 bg-white min-w-0"
                  >
                    <option value="all">All Status</option>
                    <option value="pending">Pending</option>
                    <option value="in_progress">In Progress</option>
                    <option value="completed">Completed</option>
                    <option value="cancelled">Cancelled</option>
                  </select>
                </div>

                <select
                  value={urgencyFilter}
                  onChange={(e) => setUrgencyFilter(e.target.value)}
                  className="border border-gray-300 rounded-lg pl-2 pr-8 py-1.5 sm:pl-3 sm:pr-10 sm:py-2 text-xs sm:text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 bg-white min-w-0"
                >
                  <option value="all">All Urgency</option>
                  <option value="urgent">Urgent</option>
                  <option value="standard">Standard</option>
                  <option value="flexible">Flexible</option>
                </select>
              </div>

              <div className="flex items-center space-x-2 sm:space-x-3">
                <div className="flex items-center space-x-2">
                  <AdjustmentsHorizontalIcon className="h-4 w-4 sm:h-5 sm:w-5 text-gray-400" />
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value as 'date' | 'amount' | 'urgency')}
                    className="border border-gray-300 rounded-lg pl-2 pr-8 py-1.5 sm:pl-3 sm:pr-10 sm:py-2 text-xs sm:text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 bg-white min-w-0"
                  >
                    <option value="date">Date</option>
                    <option value="amount">Amount</option>
                    <option value="urgency">Urgency</option>
                  </select>
                </div>

                <button
                  onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                  className="p-1.5 sm:p-2 border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-1 focus:ring-blue-500 flex-shrink-0"
                  title={`Sort ${sortOrder === 'asc' ? 'Descending' : 'Ascending'}`}
                >
                  <ArrowPathIcon className={`h-3 w-3 sm:h-4 sm:w-4 text-gray-600 transition-transform duration-200 ${sortOrder === 'desc' ? 'rotate-180' : ''}`} />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Requests Grid */}
      <div className="px-4 sm:px-6 pb-6 sm:pb-8">
        {filteredRequests.length === 0 ? (
          <div className="bg-white rounded-lg sm:rounded-xl shadow-sm border border-gray-200 p-8 sm:p-12 text-center">
            <div className="w-12 h-12 sm:w-16 sm:h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <PaintBrushIcon className="h-6 w-6 sm:h-8 sm:w-8 text-gray-400" />
            </div>
            <h3 className="text-base sm:text-lg font-semibold text-gray-900 mb-2">No Design Requests Found</h3>
            <p className="text-sm sm:text-base text-gray-600 max-w-sm mx-auto">
              {searchTerm || statusFilter !== 'all' || urgencyFilter !== 'all' 
                ? 'No requests match your current filters. Try adjusting your search criteria.'
                : 'No design requests have been submitted yet. They will appear here when customers submit requests.'
              }
            </p>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4 sm:gap-6">
            {filteredRequests.map((request) => {
              const statusConfig = getStatusConfig(request.status);
              const urgencyConfig = getUrgencyConfig(request.urgency);
              const StatusIcon = statusConfig.icon;
              const UrgencyIcon = urgencyConfig.icon;

              return (
                <div key={request.id} className="bg-white rounded-lg sm:rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200">
                  {/* Card Header */}
                  <div className="p-4 sm:p-6 pb-3 sm:pb-4">
                    <div className="flex items-start justify-between mb-3 sm:mb-4">
                      <div className="flex items-center space-x-2 sm:space-x-3 min-w-0 flex-1">
                        <div className="w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center flex-shrink-0">
                          <UserIcon className="h-4 w-4 sm:h-5 sm:w-5 text-white" />
                        </div>
                        <div className="min-w-0 flex-1">
                          <h3 className="text-base sm:text-lg font-semibold text-gray-900 truncate">{request.customerName}</h3>
                          <p className="text-xs sm:text-sm text-gray-600">{getTimeAgo(request.requestedAt)}</p>
                        </div>
                      </div>
                      
                      <div className="flex flex-col items-end space-y-1 sm:space-y-2 flex-shrink-0">
                        <div className={`inline-flex items-center px-2 py-0.5 sm:px-2.5 sm:py-1 rounded-full text-xs font-medium border ${statusConfig.color}`}>
                          <StatusIcon className={`w-2.5 h-2.5 sm:w-3 sm:h-3 mr-1 ${statusConfig.iconColor}`} />
                          <span className="hidden sm:inline">{statusConfig.label}</span>
                          <span className="sm:hidden">{statusConfig.label.split(' ')[0]}</span>
                        </div>
                        <div className={`inline-flex items-center px-2 py-0.5 sm:px-2.5 sm:py-1 rounded-full text-xs font-medium border ${urgencyConfig.color}`}>
                          <UrgencyIcon className="w-2.5 h-2.5 sm:w-3 sm:h-3 mr-1" />
                          <span>{urgencyConfig.label}</span>
                        </div>
                      </div>
                    </div>

                    {/* Contact Info */}
                    <div className="space-y-1 sm:space-y-2 mb-3 sm:mb-4">
                      <div className="flex items-center text-xs sm:text-sm text-gray-600">
                        <EnvelopeIcon className="h-3 w-3 sm:h-4 sm:w-4 mr-2 text-gray-400 flex-shrink-0" />
                        <span className="truncate">{request.email}</span>
                      </div>
                      <div className="flex items-center text-xs sm:text-sm text-gray-600">
                        <PhoneIcon className="h-3 w-3 sm:h-4 sm:w-4 mr-2 text-gray-400 flex-shrink-0" />
                        <span>{request.phone}</span>
                      </div>
                    </div>

                    {/* Project Details */}
                    <div className="bg-gray-50 rounded-lg p-3 sm:p-4 mb-3 sm:mb-4">
                      <div className="grid grid-cols-2 gap-2 sm:gap-4 text-xs sm:text-sm">
                        <div>
                          <p className="text-gray-600 mb-1">Product</p>
                          <p className="font-medium text-gray-900 truncate">{request.productType}</p>
                        </div>
                        <div>
                          <p className="text-gray-600 mb-1">Quantity</p>
                          <p className="font-medium text-gray-900">{request.quantity.toLocaleString()}</p>
                        </div>
                        <div>
                          <p className="text-gray-600 mb-1">Design Type</p>
                          <p className="font-medium text-gray-900 capitalize truncate">{request.designType.replace('_', ' ')}</p>
                        </div>
                        <div>
                          <p className="text-gray-600 mb-1">Total Cost</p>
                          <p className="font-bold text-green-600 text-xs sm:text-sm">{formatCurrency(Number(request.totalCost) || 0)}</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Card Footer */}
                  <div className="px-4 sm:px-6 py-3 sm:py-4 bg-gray-50 rounded-b-lg sm:rounded-b-xl border-t border-gray-100">
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
                      <div className="flex items-center justify-center space-x-2 sm:justify-end">
                        {request.status === 'pending' && (
                          <button
                            onClick={() => handleSubmitJob(request)}
                            className="inline-flex items-center px-2 py-1 sm:px-3 sm:py-1.5 bg-green-600 hover:bg-green-700 text-white text-xs font-medium rounded-md transition-colors duration-200"
                            title="Submit Job"
                          >
                            <CheckCircleIcon className="h-3 w-3 sm:h-4 sm:w-4" />
                            <span className="ml-1 sm:ml-1.5">Submit Job</span>
                          </button>
                        )}

                        {(request.status === 'completed' || request.status === 'cancelled') && (
                          <button
                            onClick={() => updateRequestStatus(request.id, 'pending')}
                            className="inline-flex items-center px-2 py-1 sm:px-3 sm:py-1.5 bg-gray-500 hover:bg-gray-600 text-white text-xs font-medium rounded-md transition-colors duration-200"
                            title="Reopen Request"
                          >
                            <ArrowPathIcon className="h-3 w-3 sm:h-4 sm:w-4" />
                            <span className="ml-1 sm:ml-1.5 hidden sm:inline">Reopen</span>
                          </button>
                        )}

                        <button
                          onClick={() => setSelectedRequest(request)}
                          className="inline-flex items-center px-2 py-1 sm:px-3 sm:py-1.5 bg-blue-500 hover:bg-blue-600 text-white text-xs font-medium rounded-md transition-colors duration-200"
                          title="View Details"
                        >
                          <EyeIcon className="h-3 w-3 sm:h-4 sm:w-4" />
                          <span className="ml-1 sm:ml-1.5 hidden sm:inline">Details</span>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* Enhanced Request Detail Modal */}
      {selectedRequest && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg w-full max-w-4xl max-h-[90vh] overflow-hidden shadow-lg">
            {/* Modal Header */}
            <div className="px-4 sm:px-6 py-4 bg-gray-50 border-b border-gray-200">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
                <div>
                  <h3 className="text-lg font-medium text-gray-900">Design Request Details</h3>
                  <p className="text-sm text-gray-500">Request ID: {selectedRequest.id.slice(-8).toUpperCase()}</p>
                </div>
                <button
                  onClick={() => setSelectedRequest(null)}
                  className="text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <XMarkIcon className="h-6 w-6" />
                </button>
              </div>
            </div>

            {/* Modal Content */}
            <div className="overflow-y-auto max-h-[calc(90vh-120px)]">
              <div className="grid grid-cols-1 xl:grid-cols-3 gap-4 sm:gap-6 p-4 sm:p-6">
                {/* Main Content */}
                <div className="xl:col-span-2 space-y-4 sm:space-y-6">
                  {/* Customer Information */}
                  <div className="bg-white shadow rounded-lg">
                    <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
                      <h2 className="text-base sm:text-lg font-medium text-gray-900">Customer Information</h2>
                    </div>
                    <div className="px-4 sm:px-6 py-4">
                      <dl className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                        <div>
                          <dt className="text-sm font-medium text-gray-500">Name</dt>
                          <dd className="mt-1 text-sm text-gray-900 break-words">{selectedRequest.customerName}</dd>
                        </div>
                        <div>
                          <dt className="text-sm font-medium text-gray-500">Phone Number</dt>
                          <dd className="mt-1 text-sm text-gray-900">{selectedRequest.phone}</dd>
                        </div>
                        <div className="sm:col-span-2">
                          <dt className="text-sm font-medium text-gray-500">Email</dt>
                          <dd className="mt-1 text-sm text-gray-900 break-all">{selectedRequest.email}</dd>
                        </div>
                      </dl>
                    </div>
                  </div>

                  {/* Project Details */}
                  <div className="bg-white shadow rounded-lg">
                    <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
                      <h2 className="text-base sm:text-lg font-medium text-gray-900">Project Details</h2>
                    </div>
                    <div className="px-4 sm:px-6 py-4">
                      <dl className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                        <div>
                          <dt className="text-sm font-medium text-gray-500">Product Type</dt>
                          <dd className="mt-1 text-sm text-gray-900">{selectedRequest.productType}</dd>
                        </div>
                        <div>
                          <dt className="text-sm font-medium text-gray-500">Quantity</dt>
                          <dd className="mt-1 text-sm text-gray-900">{selectedRequest.quantity.toLocaleString()} units</dd>
                        </div>
                        <div>
                          <dt className="text-sm font-medium text-gray-500">Design Type</dt>
                          <dd className="mt-1 text-sm text-gray-900 capitalize">{selectedRequest.designType.replace('_', ' ')}</dd>
                        </div>
                        <div>
                          <dt className="text-sm font-medium text-gray-500">Urgency</dt>
                          <dd className="mt-1 text-sm text-gray-900 capitalize">{selectedRequest.urgency}</dd>
                        </div>
                        {selectedRequest.designer && (
                          <div className="sm:col-span-2">
                            <dt className="text-sm font-medium text-gray-500">Assigned Designer</dt>
                            <dd className="mt-1 text-sm text-gray-900">{selectedRequest.designer.name}</dd>
                          </div>
                        )}
                      </dl>
                    </div>
                  </div>

                  {/* Design Brief */}
                  <div className="bg-white shadow rounded-lg">
                    <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
                      <h2 className="text-base sm:text-lg font-medium text-gray-900">Design Brief & Specifications</h2>
                    </div>
                    <div className="px-4 sm:px-6 py-4">
                      <p className="text-sm text-gray-700 break-words whitespace-pre-wrap">{selectedRequest.specifications}</p>
                    </div>
                  </div>
                </div>

                {/* Sidebar */}
                <div className="space-y-4 sm:space-y-6">
                  {/* Status Information */}
                  <div className="bg-white shadow rounded-lg">
                    <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
                      <h2 className="text-base sm:text-lg font-medium text-gray-900">Status Information</h2>
                    </div>
                    <div className="px-4 sm:px-6 py-4">
                      <dl className="space-y-3">
                        <div>
                          <dt className="text-sm font-medium text-gray-500">Current Status</dt>
                          <dd className="mt-1">
                            {(() => {
                              const statusConfig = getStatusConfig(selectedRequest.status);
                              return (
                                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusConfig.color}`}>
                                  {statusConfig.label}
                                </span>
                              );
                            })()}
                          </dd>
                        </div>
                        <div>
                          <dt className="text-sm font-medium text-gray-500">Request Date</dt>
                          <dd className="mt-1 text-sm text-gray-900">{formatDate(selectedRequest.requestedAt)}</dd>
                        </div>
                      </dl>
                    </div>
                  </div>

                  {/* Pricing Summary */}
                  <div className="bg-white shadow rounded-lg">
                    <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
                      <h2 className="text-base sm:text-lg font-medium text-gray-900">Pricing Summary</h2>
                    </div>
                    <div className="px-4 sm:px-6 py-4">
                      <dl className="space-y-3">
                        <div className="flex justify-between">
                          <dt className="text-sm text-gray-500">Design Fee</dt>
                          <dd className="text-sm font-medium text-gray-900">{formatCurrency(Number(selectedRequest.designFee) || 0)}</dd>
                        </div>
                        <div className="flex justify-between">
                          <dt className="text-sm text-gray-500">Print Cost</dt>
                          <dd className="text-sm font-medium text-gray-900">{formatCurrency(Number(selectedRequest.printCost) || 0)}</dd>
                        </div>
                        <div className="border-t border-gray-200 pt-3">
                          <div className="flex justify-between">
                            <dt className="text-base font-medium text-gray-900">Total Project Value</dt>
                            <dd className="text-base font-medium text-green-600">{formatCurrency(Number(selectedRequest.totalCost) || 0)}</dd>
                          </div>
                        </div>
                      </dl>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="bg-white shadow rounded-lg">
                    <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
                      <h2 className="text-base sm:text-lg font-medium text-gray-900">Actions</h2>
                    </div>
                    <div className="px-4 sm:px-6 py-4">
                      <div className="space-y-3">
                        <button
                          onClick={() => handleSubmitJob(selectedRequest)}
                          className="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                        >
                          <CheckCircleIcon className="h-4 w-4 mr-2" />
                          Submit Job
                        </button>
                        
                        {selectedRequest.status === 'in_progress' && (
                          <button
                            onClick={() => updateRequestStatus(selectedRequest.id, 'completed')}
                            className="w-full inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                          >
                            <CheckCircleIcon className="h-4 w-4 mr-2" />
                            Mark Complete
                          </button>
                        )}
                        
                        {selectedRequest.status !== 'cancelled' && selectedRequest.status !== 'completed' && (
                          <button
                            onClick={() => updateRequestStatus(selectedRequest.id, 'cancelled')}
                            className="w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                          >
                            <XMarkIcon className="h-4 w-4 mr-2" />
                            Cancel Request
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Communication Modal */}
      {showSubmitModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl w-full max-w-4xl max-h-[90vh] overflow-hidden shadow-xl">
            {/* Modal Header */}
            <div className="px-4 py-3 bg-gradient-to-r from-navy-800 to-navy-600">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-white bg-opacity-20 rounded-lg flex items-center justify-center">
                    <PaperAirplaneIcon className="h-4 w-4 text-white" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-white">Submit Design Work</h3>
                    <p className="text-navy-100 text-xs">Upload files and notify client</p>
                  </div>
                </div>
                <button
                  onClick={() => setShowSubmitModal(false)}
                  className="w-8 h-8 bg-white bg-opacity-20 hover:bg-opacity-30 rounded-lg flex items-center justify-center transition-all duration-200"
                >
                  <XMarkIcon className="h-4 w-4 text-white" />
                </button>
              </div>
              
              {/* Client Info */}
              {submitRequest && (
                <div className="mt-3 p-2 bg-white bg-opacity-10 rounded-lg">
                  <div className="flex items-center space-x-2">
                    <UserIcon className="h-3 w-3 text-white" />
                    <span className="text-white text-sm font-medium">{submitRequest.customerName}</span>
                    <span className="text-navy-100 text-xs">• {submitRequest.productType}</span>
                  </div>
                </div>
              )}
            </div>

            {/* Modal Content */}
            <div className="overflow-y-auto max-h-[calc(90vh-120px)]">
              <div className="p-4">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
                  {/* Main Content */}
                  <div className="lg:col-span-2 space-y-4">
                    {/* File Upload Section */}
                    <div className="bg-gray-50 rounded-lg border border-gray-200 p-4">
                      <div className="flex items-center space-x-2 mb-3">
                        <CloudArrowUpIcon className="h-4 w-4 text-navy-600" />
                        <h3 className="text-sm font-semibold text-gray-900">Upload Files</h3>
                      </div>
                      
                      {/* File Upload Area */}
                      <div className="relative">
                        <input
                          type="file"
                          multiple
                          onChange={handleFileUpload}
                          disabled={isUploading}
                          className="absolute inset-0 w-full h-full opacity-0 cursor-pointer disabled:cursor-not-allowed"
                          accept=".jpg,.jpeg,.png,.pdf,.ai,.eps,.psd,.svg"
                        />
                        <div className={`border-2 border-dashed rounded-lg p-6 text-center transition-all duration-200 ${
                          isUploading 
                            ? 'border-navy-300 bg-navy-50' 
                            : 'border-gray-300 hover:border-navy-400 hover:bg-navy-50'
                        }`}>
                          {isUploading ? (
                            <div className="flex flex-col items-center space-y-2">
                              <ArrowPathIcon className="h-5 w-5 text-navy-600 animate-spin" />
                              <p className="text-navy-600 text-sm">Uploading...</p>
                            </div>
                          ) : (
                            <div className="flex flex-col items-center space-y-2">
                              <DocumentArrowUpIcon className="h-5 w-5 text-gray-600" />
                              <p className="text-gray-700 text-sm">Click to upload</p>
                              <p className="text-xs text-gray-500">Max 10MB each</p>
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Uploaded Files List */}
                      {uploadedFiles.length > 0 && (
                        <div className="mt-3 space-y-1">
                          <p className="text-xs font-medium text-gray-700">Files ({uploadedFiles.length})</p>
                          <div className="space-y-1 max-h-20 overflow-y-auto">
                            {uploadedFiles.map((file, index) => (
                              <div key={index} className="flex items-center justify-between p-2 bg-white rounded border">
                                <div className="flex items-center space-x-2 min-w-0">
                                  <CheckCircleIcon className="h-3 w-3 text-green-600 flex-shrink-0" />
                                  <span className="text-xs text-gray-900 truncate">{file.originalName}</span>
                                </div>
                                <button
                                  onClick={() => removeUploadedFile(index)}
                                  className="w-4 h-4 bg-red-100 hover:bg-red-200 rounded flex items-center justify-center transition-colors flex-shrink-0"
                                >
                                  <XMarkIcon className="h-2 w-2 text-red-600" />
                                </button>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Message Composition */}
                    <div className="bg-gray-50 rounded-lg border border-gray-200 p-4">
                      <div className="flex items-center space-x-2 mb-3">
                        <EnvelopeIcon className="h-4 w-4 text-navy-600" />
                        <h3 className="text-sm font-semibold text-gray-900">Message</h3>
                      </div>
                      
                      {/* Subject */}
                      <div className="mb-3">
                        <input
                          type="text"
                          value={communicationSubject}
                          onChange={(e) => setCommunicationSubject(e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-1 focus:ring-navy-500 focus:border-transparent"
                          placeholder="Design Completed"
                        />
                      </div>
                      
                      {/* Message */}
                      <textarea
                        value={submitMessage}
                        onChange={(e) => setSubmitMessage(e.target.value)}
                        rows={4}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-1 focus:ring-navy-500 focus:border-transparent resize-none"
                        placeholder="Hi there! Your design is ready..."
                      />
                    </div>
                  </div>

                  {/* Sidebar */}
                  <div className="space-y-4">
                    {/* Project Summary */}
                    {submitRequest && (
                      <div className="bg-white rounded-lg border border-gray-200 p-3">
                        <div className="flex items-center space-x-2 mb-3">
                          <SparklesIcon className="h-4 w-4 text-navy-600" />
                          <h3 className="text-sm font-semibold text-gray-900">Summary</h3>
                        </div>
                        
                        <div className="space-y-2 text-xs">
                          <div className="flex justify-between">
                            <span className="text-gray-600">Product</span>
                            <span className="font-medium text-gray-900">{submitRequest.productType}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Quantity</span>
                            <span className="font-medium text-gray-900">{submitRequest.quantity}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Fee</span>
                            <span className="font-medium text-green-600">{formatCurrency(Number(submitRequest.designFee) || 0)}</span>
                          </div>
                          <div className="border-t pt-2">
                            <div className="flex justify-between">
                              <span className="font-medium text-gray-900">Total</span>
                              <span className="font-bold text-green-600">{formatCurrency(Number(submitRequest.totalCost) || 0)}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Delivery Channels */}
                    <div className="bg-white rounded-lg border border-gray-200 p-3">
                      <div className="flex items-center space-x-2 mb-3">
                        <PaperAirplaneIcon className="h-4 w-4 text-navy-600" />
                        <h3 className="text-sm font-semibold text-gray-900">Delivery</h3>
                      </div>
                      
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2 p-2 bg-green-50 rounded border border-green-200">
                          <EnvelopeIcon className="h-3 w-3 text-green-600" />
                          <div>
                            <span className="text-xs font-medium text-green-900">Auto Email</span>
                            <p className="text-xs text-green-700">Files attached automatically</p>
                          </div>
                        </div>
                        
                        <div className="flex items-center space-x-2 p-2 bg-navy-50 rounded border border-navy-200">
                          <PhoneIcon className="h-3 w-3 text-navy-600" />
                          <div>
                            <span className="text-xs font-medium text-navy-900">WhatsApp</span>
                            <p className="text-xs text-navy-700">+254 741 590 670</p>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Submit Button */}
                    <button
                      onClick={handleSubmitWithCommunication}
                      disabled={isSubmitting || uploadedFiles.length === 0}
                      className="w-full bg-gradient-to-r from-navy-600 to-navy-800 hover:from-navy-700 hover:to-navy-900 disabled:from-gray-400 disabled:to-gray-500 text-white font-semibold py-3 px-4 rounded-lg transition-all duration-200 disabled:cursor-not-allowed text-sm"
                    >
                      {isSubmitting ? (
                        <div className="flex items-center justify-center space-x-2">
                          <ArrowPathIcon className="h-4 w-4 animate-spin" />
                          <span>Submitting...</span>
                        </div>
                      ) : (
                        <div className="flex items-center justify-center space-x-2">
                          <PaperAirplaneIcon className="h-4 w-4" />
                          <span>Submit & Send</span>
                        </div>
                      )}
                    </button>
                    
                    {uploadedFiles.length === 0 && (
                      <p className="text-xs text-gray-500 text-center">
                        Upload files to continue
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 