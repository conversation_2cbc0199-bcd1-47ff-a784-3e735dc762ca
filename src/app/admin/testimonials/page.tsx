'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import {
  PlusIcon,
  ArrowPathIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  StarIcon,
  CheckCircleIcon,
  XCircleIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';
import { formatDate } from '@/lib/utils';
import { useNotification } from '@/contexts/NotificationContext';
import AdminCard, { AdminCardList } from '@/components/admin/AdminCard';
import ConfirmDialog from '@/components/admin/ConfirmDialog';
import Image from 'next/image';
import { UserIcon } from '@heroicons/react/24/outline';

interface Testimonial {
  id: number;
  name: string;
  location: string;
  project: string;
  testimonial: string;
  rating: number;
  company?: string;
  active: boolean;
  order: number;
  createdAt: string;
  updatedAt: string;
  clientImage?: string;
  clientName: string;
  projectType: string;
  status: string;
}

export default function TestimonialsPage() {
  const [testimonials, setTestimonials] = useState<Testimonial[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterActive, setFilterActive] = useState<string>('all');
  const [deleteId, setDeleteId] = useState<number | null>(null);
  const { showNotification } = useNotification();


  const fetchTestimonials = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();

      if (filterActive !== 'all') {
        params.append('active', filterActive);
      }

      if (searchTerm) {
        params.append('search', searchTerm);
      }

      const response = await fetch(`/api/admin/testimonials?${params}`);
      const data = await response.json();

      if (data.success) {
        setTestimonials(data.data);
      } else {
        showNotification('error', 'Failed to fetch testimonials');
      }
    } catch (error) {
      console.error('Error fetching testimonials:', error);
      showNotification('error', 'Error fetching testimonials');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTestimonials();
  }, [searchTerm, filterActive]);

  const handleDeleteClick = (id: number) => {
    setDeleteId(id);
  };

  const handleDeleteConfirm = async () => {
    if (!deleteId) return;

    // Optimistic update: immediately remove from UI
    const originalTestimonials = [...testimonials];
    setTestimonials(prev => prev.filter(t => t.id !== deleteId));

    try {
      const response = await fetch(`/api/admin/testimonials/${deleteId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (data.success) {
        showNotification('success', 'Testimonial deleted successfully');
        // Keep the optimistic update - item already removed
      } else {
        // Revert optimistic update on failure
        setTestimonials(originalTestimonials);
        showNotification('error', data.error || 'Failed to delete testimonial');
      }
    } catch (error) {
      console.error('Error deleting testimonial:', error);
      // Revert optimistic update on error
      setTestimonials(originalTestimonials);
      showNotification('error', 'Error deleting testimonial');
    } finally {
      setDeleteId(null);
    }
  };

  const handleToggleStatus = async (id: number) => {
    // Optimistic update: immediately toggle status in UI
    const originalTestimonials = [...testimonials];
    setTestimonials(prev => prev.map(t => 
      t.id === id ? { ...t, active: !t.active } : t
    ));

    try {
      const response = await fetch(`/api/admin/testimonials/${id}/toggle`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const data = await response.json();

      if (data.success) {
        showNotification('success', data.message);
        // Keep the optimistic update - status already toggled
      } else {
        // Revert optimistic update on failure
        setTestimonials(originalTestimonials);
        showNotification('error', data.error || 'Failed to toggle testimonial status');
      }
    } catch (error) {
      console.error('Error toggling testimonial status:', error);
      // Revert optimistic update on error
      setTestimonials(originalTestimonials);
      showNotification('error', 'Error toggling testimonial status');
    }
  };

  const renderStars = (rating: number) => {
    return (
      <div className="flex">
        {[1, 2, 3, 4, 5].map((star) => (
          <StarIconSolid
            key={star}
            className={`h-4 w-4 ${
              star <= rating ? 'text-yellow-400' : 'text-gray-300'
            }`}
          />
        ))}
      </div>
    );
  };

  const filteredTestimonials = testimonials.filter(testimonial => {
    const matchesSearch = !searchTerm ||
      testimonial.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      testimonial.company?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      testimonial.location.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesFilter = filterActive === 'all' ||
      (filterActive === 'true' && testimonial.active) ||
      (filterActive === 'false' && !testimonial.active);

    return matchesSearch && matchesFilter;
  });

  return (
    <div className="space-y-8">
      <div className="flex justify-between items-center mb-6 mt-2">
        <h1 className="text-2xl font-bold text-slate-800">Testimonials</h1>
        <div className="flex space-x-3">
          <button
            onClick={fetchTestimonials}
            className="p-2 rounded-lg text-slate-500 hover:text-slate-700 hover:bg-slate-100 transition-colors"
            disabled={loading}
            aria-label="Refresh testimonials"
          >
            <ArrowPathIcon className={`h-5 w-5 ${loading ? 'animate-spin' : ''}`} />
          </button>
          <Link
            href="/admin/testimonials/new"
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-500 hover:bg-orange-600 transition-colors"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            New Testimonial
          </Link>
        </div>
      </div>

      {/* Search and Filter */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="flex-1">
          <input
            type="text"
            placeholder="Search testimonials..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
          />
        </div>
        <select
          value={filterActive}
          onChange={(e) => setFilterActive(e.target.value)}
          className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
        >
          <option value="all">All Status</option>
          <option value="true">Active</option>
          <option value="false">Inactive</option>
        </select>
      </div>

      {loading ? (
        <div className="flex justify-center items-center py-12">
          <ArrowPathIcon className="h-8 w-8 animate-spin text-orange-500" />
        </div>
      ) : filteredTestimonials.length === 0 ? (
        <div className="text-center py-12">
          <StarIcon className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-4 text-lg font-medium text-slate-800">No testimonials found</h3>
          <p className="mt-2 text-slate-500 max-w-md mx-auto">
            Get started by adding your first testimonial.
          </p>
          <div className="mt-8">
            <Link
              href="/admin/testimonials/new"
              className="inline-flex items-center px-5 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-500 hover:bg-orange-600 transition-colors"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              Add Your First Testimonial
            </Link>
          </div>
        </div>
      ) : (
        <>
          {/* Desktop Table View */}
          <div className="admin-table-mobile-fallback bg-white shadow-sm rounded-lg overflow-hidden">
            <div className="admin-table-container">
              <table className="admin-table">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Client
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Project
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Rating
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Date
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredTestimonials.map((testimonial) => (
                    <tr key={testimonial.id} className="hover:bg-gray-50 transition-colors duration-200">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10">
                            {testimonial.clientImage ? (
                              <Image
                                className="h-10 w-10 rounded-full object-cover"
                                src={testimonial.clientImage}
                                alt={testimonial.name}
                                width={40}
                                height={40}
                              />
                            ) : (
                              <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                                <UserIcon className="h-6 w-6 text-gray-400" />
                              </div>
                            )}
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">{testimonial.name}</div>
                            <div className="text-sm text-gray-500">{testimonial.company || testimonial.location}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 max-w-xs truncate">{testimonial.project}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          {renderStars(testimonial.rating)}
                          <span className="ml-1 text-sm text-gray-500">({testimonial.rating})</span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          testimonial.active
                            ? 'bg-green-100 text-green-800'
                            : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {testimonial.active ? 'Published' : 'Draft'}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(testimonial.createdAt).toLocaleDateString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="admin-button-group">
                          <Link
                            href={`/admin/testimonials/${testimonial.id}/edit`}
                            className="text-indigo-600 hover:text-indigo-900 transition-colors mr-3"
                            aria-label="Edit testimonial"
                          >
                            <PencilIcon className="h-5 w-5" />
                          </Link>
                          <button
                            onClick={() => handleDeleteClick(testimonial.id)}
                            disabled={loading}
                            className="text-red-600 hover:text-red-900 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                            aria-label="Delete testimonial"
                          >
                            <TrashIcon className="h-5 w-5" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>

          {/* Mobile Card View */}
          <div className="admin-mobile-cards">
            {filteredTestimonials.map((testimonial) => (
              <div key={testimonial.id} className="admin-mobile-card">
                <div className="admin-mobile-card-header">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 h-12 w-12 mr-4">
                      {testimonial.clientImage ? (
                        <Image
                          className="h-12 w-12 rounded-full object-cover"
                          src={testimonial.clientImage}
                          alt={testimonial.name}
                          width={48}
                          height={48}
                        />
                      ) : (
                        <div className="h-12 w-12 rounded-full bg-gray-200 flex items-center justify-center">
                          <UserIcon className="h-6 w-6 text-gray-400" />
                        </div>
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="admin-mobile-card-title">{testimonial.name}</h3>
                      <p className="text-sm text-gray-500">{testimonial.company || testimonial.location}</p>
                      <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full mt-1 ${
                        testimonial.active
                          ? 'bg-green-100 text-green-800'
                          : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {testimonial.active ? 'Published' : 'Draft'}
                      </span>
                    </div>
                  </div>
                </div>
                <div className="admin-mobile-card-content">
                  <p><span className="font-medium">Project:</span> {testimonial.project}</p>
                  <div className="flex items-center">
                    <span className="font-medium mr-2">Rating:</span>
                    <div className="flex items-center">
                      {renderStars(testimonial.rating)}
                      <span className="ml-1 text-sm text-gray-500">({testimonial.rating})</span>
                    </div>
                  </div>
                  <p><span className="font-medium">Date:</span> {new Date(testimonial.createdAt).toLocaleDateString()}</p>
                  {testimonial.testimonial && (
                    <p className="text-gray-600 italic mt-2 line-clamp-3">"{testimonial.testimonial}"</p>
                  )}
                </div>
                <div className="admin-mobile-card-actions">
                  <Link
                    href={`/admin/testimonials/${testimonial.id}/edit`}
                    className="text-indigo-600 hover:text-indigo-900 transition-colors p-2 rounded-md hover:bg-indigo-50"
                    aria-label="Edit testimonial"
                  >
                    <PencilIcon className="h-4 w-4" />
                  </Link>
                  <button
                    onClick={() => handleDeleteClick(testimonial.id)}
                    disabled={loading}
                    className="text-red-600 hover:text-red-900 transition-colors p-2 rounded-md hover:bg-red-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    aria-label="Delete testimonial"
                  >
                    <TrashIcon className="h-4 w-4" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </>
      )}

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        isOpen={deleteId !== null}
        onClose={() => setDeleteId(null)}
        onConfirm={handleDeleteConfirm}
        title="Delete Testimonial"
        message="Are you sure you want to delete this testimonial? This action cannot be undone."
        confirmText="Delete"
        confirmButtonClass="bg-red-600 hover:bg-red-700 focus:ring-red-500"
      />
    </div>
  );
}
