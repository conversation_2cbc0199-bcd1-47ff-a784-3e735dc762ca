'use client';

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { useRouter } from 'next/navigation';
import {
  Calendar as CalendarIcon,
  Plus,
  ChevronLeft,
  ChevronRight,
  Clock,
  MapPin,
  Users,
  Video,
  Phone,
  Mail,
  MoreVertical,
  Edit,
  Trash2,
  Eye,
  Filter,
  Grid3X3,
  List,
  X,
  AlertTriangle,
  CheckCircle,
  Circle,
  Link2,
  Copy,
  ExternalLink,
  Settings,
  Calendar,
  Webcam,
  Monitor,
  Smartphone,
} from 'lucide-react';

// Status and priority configurations
const statusConfig = {
  scheduled: { color: 'bg-blue-100 text-blue-800', label: 'Scheduled' },
  confirmed: { color: 'bg-green-100 text-green-800', label: 'Confirmed' },
  cancelled: { color: 'bg-red-100 text-red-800', label: 'Cancelled' },
  completed: { color: 'bg-gray-100 text-gray-800', label: 'Completed' },
};

const typeConfig = {
  meeting: { color: 'bg-blue-100 text-blue-800', icon: Users, label: 'Meeting' },
  call: { color: 'bg-green-100 text-green-800', icon: Phone, label: 'Call' },
  deadline: { color: 'bg-red-100 text-red-800', icon: AlertTriangle, label: 'Deadline' },
  reminder: { color: 'bg-yellow-100 text-yellow-800', icon: Clock, label: 'Reminder' },
  appointment: { color: 'bg-purple-100 text-purple-800', icon: Calendar, label: 'Appointment' },
};

// Create Event Modal Component
interface CreateEventModalProps {
  isOpen: boolean;
  onClose: () => void;
  onEventCreated: (event: CalendarEvent) => void;
  defaultDate?: Date;
}

function CreateEventModal({ isOpen, onClose, onEventCreated, defaultDate }: CreateEventModalProps) {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    type: 'meeting' as CalendarEvent['type'],
    status: 'scheduled' as CalendarEvent['status'],
    startTime: '',
    endTime: '',
    location: '',
    isVirtual: false,
    meetingLink: '',
    meetingPlatform: undefined,
    projectId: '',
    clientId: '',
    attendeeEmails: [] as string[],
    meetingDetails: {
      recordingEnabled: false,
      waitingRoom: true,
      requireAuth: false,
    }
  });
  
  const [projects, setProjects] = useState<Array<{id: string, name: string}>>([]);
  const [clients, setClients] = useState<Array<{id: string, firstName: string, lastName: string, email: string, company?: string}>>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [emailInput, setEmailInput] = useState('');

  useEffect(() => {
    if (isOpen) {
      // Set default dates if provided
      if (defaultDate) {
        const startTime = new Date(defaultDate);
        startTime.setMinutes(0, 0, 0); // Round to nearest hour
        const endTime = new Date(startTime);
        endTime.setHours(endTime.getHours() + 1); // Default 1 hour duration
        
        setFormData(prev => ({
          ...prev,
          startTime: formatDateTimeLocal(startTime),
          endTime: formatDateTimeLocal(endTime),
        }));
      }
      
      loadProjects();
      loadClients();
    }
  }, [isOpen, defaultDate]);

  const formatDateTimeLocal = (date: Date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    return `${year}-${month}-${day}T${hours}:${minutes}`;
  };

  const loadProjects = async () => {
    try {
      const response = await fetch('/api/admin/projects');
      if (response.ok) {
        const data = await response.json();
        setProjects(data.projects || []);
      }
    } catch (error) {
      console.error('Error loading projects:', error);
    }
  };

  const loadClients = async () => {
    try {
      const response = await fetch('/api/admin/clients');
      if (response.ok) {
        const data = await response.json();
        setClients(data.clients || []);
      }
    } catch (error) {
      console.error('Error loading clients:', error);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      if (name.startsWith('meetingDetails.')) {
        const field = name.split('.')[1];
        setFormData(prev => ({
          ...prev,
          meetingDetails: {
            ...prev.meetingDetails,
            [field]: checked
          }
        }));
      } else {
        setFormData(prev => ({ ...prev, [name]: checked }));
      }
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleAddAttendee = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && emailInput.trim()) {
      e.preventDefault();
      const email = emailInput.trim();
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      
      if (emailRegex.test(email) && !formData.attendeeEmails.includes(email)) {
        setFormData(prev => ({
          ...prev,
          attendeeEmails: [...prev.attendeeEmails, email]
        }));
        setEmailInput('');
      }
    }
  };

  const handleRemoveAttendee = (emailToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      attendeeEmails: prev.attendeeEmails.filter(email => email !== emailToRemove)
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Find selected project and client
      const selectedProject = projects.find(p => p.id === formData.projectId);
      const selectedClient = clients.find(c => c.id === formData.clientId);

      const eventData = {
        title: formData.title,
        description: formData.description,
        type: formData.type,
        status: formData.status,
        startTime: new Date(formData.startTime).toISOString(),
        endTime: new Date(formData.endTime).toISOString(),
        location: formData.location,
        isVirtual: formData.isVirtual,
        meetingLink: formData.meetingLink || undefined,
        meetingPlatform: formData.meetingPlatform || undefined,
        meetingDetails: formData.isVirtual ? formData.meetingDetails : undefined,
        project: selectedProject ? {
          id: selectedProject.id,
          name: selectedProject.name,
          client: selectedClient ? {
            firstName: selectedClient.firstName,
            lastName: selectedClient.lastName,
            company: selectedClient.company
          } : undefined
        } : undefined,
        client: selectedClient ? {
          id: selectedClient.id,
          firstName: selectedClient.firstName,
          lastName: selectedClient.lastName,
          company: selectedClient.company,
          email: selectedClient.email
        } : undefined,
        attendees: formData.attendeeEmails.map(email => ({
          id: `attendee-${email}`,
          name: email.split('@')[0],
          email,
          status: 'pending' as const
        }))
      };

      const response = await fetch('/api/admin/calendar/events', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(eventData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create event');
      }

      const newEvent = await response.json();
      onEventCreated(newEvent);
      
      // Reset form
      setFormData({
        title: '',
        description: '',
        type: 'meeting',
        status: 'scheduled',
        startTime: '',
        endTime: '',
        location: '',
        isVirtual: false,
        meetingLink: '',
        meetingPlatform: undefined,
        projectId: '',
        clientId: '',
        attendeeEmails: [],
        meetingDetails: {
          recordingEnabled: false,
          waitingRoom: true,
          requireAuth: false,
        }
      });
      setEmailInput('');
    } catch (error) {
      console.error('Error creating event:', error);
      alert('Failed to create event. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <div 
          className="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" 
          onClick={onClose}
        ></div>
        
        <div className="inline-block px-4 pt-5 pb-4 overflow-hidden text-left align-bottom transition-all transform bg-white rounded-lg shadow-xl sm:my-8 sm:align-middle sm:max-w-3xl sm:w-full sm:p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Create New Event</h3>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
            >
              <X className="h-5 w-5" />
            </button>
          </div>
          
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Basic Information */}
            <div className="space-y-4">
              <div>
                <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-2">
                  Event Title *
                </label>
                <input
                  type="text"
                  id="title"
                  name="title"
                  required
                  value={formData.title}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary"
                  placeholder="Enter event title"
                />
              </div>

              <div>
                <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                  Description
                </label>
                <textarea
                  id="description"
                  name="description"
                  rows={3}
                  value={formData.description}
                  onChange={handleInputChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary"
                  placeholder="Enter event description"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label htmlFor="type" className="block text-sm font-medium text-gray-700 mb-2">
                    Event Type *
                  </label>
                  <select
                    id="type"
                    name="type"
                    required
                    value={formData.type}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary"
                  >
                    <option value="meeting">Meeting</option>
                    <option value="call">Call</option>
                    <option value="deadline">Deadline</option>
                    <option value="reminder">Reminder</option>
                    <option value="appointment">Appointment</option>
                  </select>
                </div>

                <div>
                  <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-2">
                    Status
                  </label>
                  <select
                    id="status"
                    name="status"
                    value={formData.status}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary"
                  >
                    <option value="scheduled">Scheduled</option>
                    <option value="confirmed">Confirmed</option>
                    <option value="cancelled">Cancelled</option>
                    <option value="completed">Completed</option>
                  </select>
                </div>

                <div className="flex items-center">
                  <label className="flex items-center space-x-2 mt-6">
                    <input
                      type="checkbox"
                      name="isVirtual"
                      checked={formData.isVirtual}
                      onChange={handleInputChange}
                      className="rounded border-gray-300 text-primary focus:ring-primary"
                    />
                    <span className="text-sm font-medium text-gray-700">Virtual Meeting</span>
                  </label>
                </div>
              </div>

              {/* Date & Time */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="startTime" className="block text-sm font-medium text-gray-700 mb-2">
                    Start Time *
                  </label>
                  <input
                    type="datetime-local"
                    id="startTime"
                    name="startTime"
                    required
                    value={formData.startTime}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary"
                  />
                </div>

                <div>
                  <label htmlFor="endTime" className="block text-sm font-medium text-gray-700 mb-2">
                    End Time *
                  </label>
                  <input
                    type="datetime-local"
                    id="endTime"
                    name="endTime"
                    required
                    value={formData.endTime}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary"
                  />
                </div>
              </div>

              {/* Location / Virtual Meeting Details */}
              {!formData.isVirtual && (
                <div>
                  <label htmlFor="location" className="block text-sm font-medium text-gray-700 mb-2">
                    Location
                  </label>
                  <input
                    type="text"
                    id="location"
                    name="location"
                    value={formData.location}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary"
                    placeholder="Enter meeting location"
                  />
                </div>
              )}

              {formData.isVirtual && (
                <div className="space-y-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
                  <h4 className="text-sm font-medium text-blue-900">Virtual Meeting Settings</h4>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label htmlFor="meetingPlatform" className="block text-sm font-medium text-gray-700 mb-2">
                        Platform
                      </label>
                      <select
                        id="meetingPlatform"
                        name="meetingPlatform"
                        value={formData.meetingPlatform}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary"
                      >
                        <option value="">Select Platform</option>
                        <option value="zoom">Zoom</option>
                        <option value="google-meet">Google Meet</option>
                        <option value="teams">Microsoft Teams</option>
                        <option value="webex">Cisco Webex</option>
                        <option value="custom">Custom</option>
                      </select>
                    </div>

                    <div>
                      <label htmlFor="meetingLink" className="block text-sm font-medium text-gray-700 mb-2">
                        Meeting Link
                      </label>
                      <input
                        type="url"
                        id="meetingLink"
                        name="meetingLink"
                        value={formData.meetingLink}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary"
                        placeholder="https://..."
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        name="meetingDetails.recordingEnabled"
                        checked={formData.meetingDetails.recordingEnabled}
                        onChange={handleInputChange}
                        className="rounded border-gray-300 text-primary focus:ring-primary"
                      />
                      <span className="text-sm text-gray-700">Enable Recording</span>
                    </label>

                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        name="meetingDetails.waitingRoom"
                        checked={formData.meetingDetails.waitingRoom}
                        onChange={handleInputChange}
                        className="rounded border-gray-300 text-primary focus:ring-primary"
                      />
                      <span className="text-sm text-gray-700">Waiting Room</span>
                    </label>

                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        name="meetingDetails.requireAuth"
                        checked={formData.meetingDetails.requireAuth}
                        onChange={handleInputChange}
                        className="rounded border-gray-300 text-primary focus:ring-primary"
                      />
                      <span className="text-sm text-gray-700">Require Auth</span>
                    </label>
                  </div>
                </div>
              )}

              {/* Project & Client */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label htmlFor="projectId" className="block text-sm font-medium text-gray-700 mb-2">
                    Related Project
                  </label>
                  <select
                    id="projectId"
                    name="projectId"
                    value={formData.projectId}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary"
                  >
                    <option value="">No project</option>
                    {projects.map((project) => (
                      <option key={project.id} value={project.id}>
                        {project.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label htmlFor="clientId" className="block text-sm font-medium text-gray-700 mb-2">
                    Client
                  </label>
                  <select
                    id="clientId"
                    name="clientId"
                    value={formData.clientId}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary"
                  >
                    <option value="">No client</option>
                    {clients.map((client) => (
                      <option key={client.id} value={client.id}>
                        {client.firstName} {client.lastName} {client.company ? `(${client.company})` : ''}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Attendees */}
              <div>
                <label htmlFor="attendees" className="block text-sm font-medium text-gray-700 mb-2">
                  Attendees
                </label>
                <div className="space-y-2">
                  <input
                    type="email"
                    value={emailInput}
                    onChange={(e) => setEmailInput(e.target.value)}
                    onKeyDown={handleAddAttendee}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary"
                    placeholder="Type an email address and press Enter"
                  />
                  {formData.attendeeEmails.length > 0 && (
                    <div className="flex flex-wrap gap-2">
                      {formData.attendeeEmails.map((email, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary/10 text-primary"
                        >
                          {email}
                          <button
                            type="button"
                            onClick={() => handleRemoveAttendee(email)}
                            className="ml-1 text-primary hover:text-primary/80"
                          >
                            <X className="h-3 w-3" />
                          </button>
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Actions */}
            <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isSubmitting}
                className="px-4 py-2 text-sm font-medium text-white bg-primary rounded-lg hover:bg-primary/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? 'Creating...' : 'Create Event'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

// Types
interface CalendarEvent {
  id: string;
  title: string;
  description?: string;
  startTime: string;
  endTime: string;
  type: 'meeting' | 'call' | 'deadline' | 'reminder' | 'appointment';
  status: 'scheduled' | 'confirmed' | 'cancelled' | 'completed';
  location?: string;
  isVirtual: boolean;
  meetingLink?: string;
  meetingPlatform?: 'zoom' | 'google-meet' | 'teams' | 'webex' | 'custom';
  meetingDetails?: {
    meetingId?: string;
    passcode?: string;
    dialInNumbers?: string[];
    recordingEnabled?: boolean;
    waitingRoom?: boolean;
    requireAuth?: boolean;
  };
  attendees: Array<{
    id: string;
    name: string;
    email: string;
    status: 'pending' | 'accepted' | 'declined' | 'tentative';
  }>;
  project?: {
    id: string;
    name: string;
    client?: {
      firstName: string;
      lastName: string;
      company?: string;
    };
  };
  client?: {
    id: string;
    firstName: string;
    lastName: string;
    company?: string;
    email: string;
  };
  createdBy: {
    id: string;
    name: string;
    email: string;
  };
  createdAt: string;
  updatedAt: string;
}

const eventTypeColors = {
  meeting: 'bg-primary/10 text-primary border-primary/20',
  call: 'bg-secondary/10 text-secondary border-secondary/20',
  deadline: 'bg-red-100 text-red-800 border-red-200',
  reminder: 'bg-orange-100 text-orange-800 border-orange-200',
  appointment: 'bg-accent/10 text-accent border-accent/20',
};

const statusColors = {
  scheduled: 'bg-gray-100 text-gray-800',
  confirmed: 'bg-secondary/10 text-secondary',
  cancelled: 'bg-red-100 text-red-800',
  completed: 'bg-primary/10 text-primary',
};

const typeIcons = {
  meeting: Users,
  call: Phone,
  deadline: AlertTriangle,
  reminder: Clock,
  appointment: CalendarIcon,
};

// Video conferencing platform configurations
const meetingPlatforms = {
  zoom: {
    name: 'Zoom',
    icon: Video,
    color: 'bg-blue-500',
    textColor: 'text-blue-700',
    bgColor: 'bg-blue-50',
    borderColor: 'border-blue-200',
    joinText: 'Join Zoom Meeting',
    features: ['Waiting Room', 'Recording', 'Screen Share', 'Breakout Rooms']
  },
  'google-meet': {
    name: 'Google Meet',
    icon: Webcam,
    color: 'bg-green-500',
    textColor: 'text-green-700',
    bgColor: 'bg-green-50',
    borderColor: 'border-green-200',
    joinText: 'Join Google Meet',
    features: ['Live Captions', 'Recording', 'Screen Share', 'Attendance Tracking']
  },
  teams: {
    name: 'Microsoft Teams',
    icon: Monitor,
    color: 'bg-purple-500',
    textColor: 'text-purple-700',
    bgColor: 'bg-purple-50',
    borderColor: 'border-purple-200',
    joinText: 'Join Teams Meeting',
    features: ['Recording', 'Screen Share', 'Collaborative Docs', 'Chat']
  },
  webex: {
    name: 'Cisco Webex',
    icon: Smartphone,
    color: 'bg-orange-500',
    textColor: 'text-orange-700',
    bgColor: 'bg-orange-50',
    borderColor: 'border-orange-200',
    joinText: 'Join Webex Meeting',
    features: ['Recording', 'Screen Share', 'Whiteboard', 'Polling']
  },
  custom: {
    name: 'Custom Platform',
    icon: Link2,
    color: 'bg-gray-500',
    textColor: 'text-gray-700',
    bgColor: 'bg-gray-50',
    borderColor: 'border-gray-200',
    joinText: 'Join Meeting',
    features: ['Custom Integration']
  }
};

export default function CalendarPage() {
  const { user, isLoading } = useAuth();
  const router = useRouter();
  
  // State management
  const [events, setEvents] = useState<CalendarEvent[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentDate, setCurrentDate] = useState(new Date());
  const [viewMode, setViewMode] = useState<'month' | 'week' | 'day' | 'agenda'>('month');
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEventModal, setShowEventModal] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState<CalendarEvent | null>(null);
  const [showDropdown, setShowDropdown] = useState<string | null>(null);
  const [typeFilter, setTypeFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  const [isEditing, setIsEditing] = useState(false);
  const [editForm, setEditForm] = useState<Partial<CalendarEvent>>({});



  // Check mobile state
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  // Initial data load
  useEffect(() => {
    if (isLoading) return;
    if (!user) {
      router.push('/admin/login');
      return;
    }
    loadEvents();
  }, [user, isLoading, router, currentDate, viewMode]);

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (!target.closest('.dropdown-container')) {
        setShowDropdown(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const loadEvents = async () => {
    try {
      setLoading(true);
      
      const startDate = getViewStartDate();
      const endDate = getViewEndDate();
      
      const params = new URLSearchParams({
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        ...(typeFilter && { type: typeFilter }),
        ...(statusFilter && { status: statusFilter }),
        ...(searchTerm && { search: searchTerm }),
      });

      const response = await fetch(`/api/admin/calendar/events?${params}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch events');
      }
      
      const data = await response.json();
      setEvents(data.events || []);
    } catch (error) {
      console.error('Error loading events:', error);
      setError('Failed to load calendar events');
      setEvents([]);
    } finally {
      setLoading(false);
    }
  };

  const getViewStartDate = () => {
    const date = new Date(currentDate);
    switch (viewMode) {
      case 'month':
        date.setDate(1);
        date.setDate(date.getDate() - date.getDay()); // Start of week containing first day of month
        break;
      case 'week':
        date.setDate(date.getDate() - date.getDay()); // Start of current week
        break;
      case 'day':
        // Current day
        break;
      case 'agenda':
        // Current date
        break;
    }
    date.setHours(0, 0, 0, 0);
    return date;
  };

  const getViewEndDate = () => {
    const date = new Date(currentDate);
    switch (viewMode) {
      case 'month':
        date.setMonth(date.getMonth() + 1, 0); // Last day of current month
        date.setDate(date.getDate() + (6 - date.getDay())); // End of week containing last day of month
        break;
      case 'week':
        date.setDate(date.getDate() - date.getDay() + 6); // End of current week
        break;
      case 'day':
        // Current day
        break;
      case 'agenda':
        date.setDate(date.getDate() + 30); // Next 30 days
        break;
    }
    date.setHours(23, 59, 59, 999);
    return date;
  };

  const formatDate = (dateString: string) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    }).format(new Date(dateString));
  };

  const formatTime = (dateString: string) => {
    return new Intl.DateTimeFormat('en-US', {
      hour: '2-digit',
      minute: '2-digit',
    }).format(new Date(dateString));
  };

  const formatDateTime = (dateString: string) => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    }).format(new Date(dateString));
  };

  const getEventDuration = (startTime: string, endTime: string) => {
    const start = new Date(startTime);
    const end = new Date(endTime);
    const diffMs = end.getTime() - start.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
    
    if (diffHours > 0) {
      return `${diffHours}h ${diffMinutes > 0 ? `${diffMinutes}m` : ''}`;
    }
    return `${diffMinutes}m`;
  };

  const getClientName = (client: CalendarEvent['client']) => {
    if (!client) return null;
    return `${client.firstName} ${client.lastName}`.trim();
  };

  // Video conferencing utility functions
  const getMeetingPlatformInfo = (platform?: string) => {
    if (!platform || !meetingPlatforms[platform as keyof typeof meetingPlatforms]) {
      return meetingPlatforms.custom;
    }
    return meetingPlatforms[platform as keyof typeof meetingPlatforms];
  };

  const copyMeetingLink = async (link: string) => {
    try {
      await navigator.clipboard.writeText(link);
      // You could add a toast notification here
      console.log('Meeting link copied to clipboard');
    } catch (err) {
      console.error('Failed to copy meeting link:', err);
    }
  };

  const joinMeeting = (event: CalendarEvent) => {
    if (event.meetingLink) {
      window.open(event.meetingLink, '_blank', 'noopener,noreferrer');
    }
  };

  const generateMeetingDetails = (event: CalendarEvent) => {
    if (!event.isVirtual || !event.meetingLink) return null;

    const platform = getMeetingPlatformInfo(event.meetingPlatform);
    return {
      platform,
      link: event.meetingLink,
      details: event.meetingDetails,
      canJoin: true // You could add logic for meeting start time
    };
  };

  const navigateDate = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);
    
    switch (viewMode) {
      case 'month':
        newDate.setMonth(newDate.getMonth() + (direction === 'next' ? 1 : -1));
        break;
      case 'week':
        newDate.setDate(newDate.getDate() + (direction === 'next' ? 7 : -7));
        break;
      case 'day':
        newDate.setDate(newDate.getDate() + (direction === 'next' ? 1 : -1));
        break;
      case 'agenda':
        newDate.setDate(newDate.getDate() + (direction === 'next' ? 30 : -30));
        break;
    }
    
    setCurrentDate(newDate);
  };

  const goToToday = () => {
    setCurrentDate(new Date());
  };

  const handleSearch = (value: string) => {
    setSearchTerm(value);
  };

  const clearFilters = () => {
    setSearchTerm('');
    setTypeFilter('');
    setStatusFilter('');
  };

  const handleEventClick = (event: CalendarEvent) => {
    setSelectedEvent(event);
    setShowEventModal(true);
  };

  const handleViewEvent = async (eventId: string) => {
    try {
      const response = await fetch(`/api/admin/calendar/events/${eventId}`, {
        method: 'GET',
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to fetch event details');
      }

      const data = await response.json();
      setSelectedEvent(data.event);
      setShowEventModal(true);
      
      console.log('Event details loaded successfully');
    } catch (error) {
      console.error('Error fetching event details:', error);
      setError('Failed to load event details');
    }
  };

  const handleUpdateEvent = async (eventId: string, updatedData: Partial<CalendarEvent>) => {
    try {
      const response = await fetch(`/api/admin/calendar/events/${eventId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify(updatedData),
      });

      if (!response.ok) {
        throw new Error('Failed to update event');
      }

      const data = await response.json();
      
      // Update local state
      setEvents(prev => prev.map(event => 
        event.id === eventId ? { ...event, ...updatedData, updatedAt: new Date().toISOString() } : event
      ));
      
      setSelectedEvent(data.event);
      
      console.log('Event updated successfully');
    } catch (error) {
      console.error('Error updating event:', error);
      setError('Failed to update event');
    }
  };

  const startEditEvent = (event: CalendarEvent) => {
    setEditForm({
      title: event.title,
      description: event.description,
      startTime: event.startTime,
      endTime: event.endTime,
      type: event.type,
      status: event.status,
      location: event.location,
      isVirtual: event.isVirtual,
      meetingLink: event.meetingLink,
      meetingPlatform: event.meetingPlatform,
    });
    setIsEditing(true);
  };

  const cancelEdit = () => {
    setIsEditing(false);
    setEditForm({});
  };

  const saveEdit = async () => {
    if (!selectedEvent) return;
    
    await handleUpdateEvent(selectedEvent.id, editForm);
    setIsEditing(false);
    setEditForm({});
  };

  const handleDeleteEvent = async (eventId: string) => {
    if (!confirm('Are you sure you want to delete this event?')) return;
    
    try {
      const response = await fetch(`/api/admin/calendar/events/${eventId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      });

      if (!response.ok) {
        throw new Error('Failed to delete event');
      }

      // Remove from local state
      setEvents(prev => prev.filter(event => event.id !== eventId));
      setShowEventModal(false);
      setSelectedEvent(null);
      
      console.log('Event deleted successfully');
    } catch (error) {
      console.error('Error deleting event:', error);
      setError('Failed to delete event');
    }
  };

  if (isLoading || loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="bg-white p-8 rounded-lg shadow">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600 text-center">Loading calendar...</p>
        </div>
      </div>
    );
  }

  if (!user) return null;

  // Calculate stats
  const today = new Date();
  const todayEvents = events.filter(event => {
    const eventDate = new Date(event.startTime);
    return eventDate.toDateString() === today.toDateString();
  });

  const upcomingEvents = events.filter(event => {
    const eventDate = new Date(event.startTime);
    return eventDate > today;
  }).slice(0, 5);

  const stats = {
    total: events.length,
    today: todayEvents.length,
    upcoming: upcomingEvents.length,
    meetings: events.filter(e => e.type === 'meeting').length,
    deadlines: events.filter(e => e.type === 'deadline').length,
  };

  // Filter options
  const filterOptions = [
    {
      label: 'Type',
      value: typeFilter,
      onChange: setTypeFilter,
      options: [
        { value: '', label: 'All Types' },
        { value: 'meeting', label: 'Meeting' },
        { value: 'call', label: 'Call' },
        { value: 'deadline', label: 'Deadline' },
        { value: 'reminder', label: 'Reminder' },
        { value: 'appointment', label: 'Appointment' },
      ]
    },
    {
      label: 'Status',
      value: statusFilter,
      onChange: setStatusFilter,
      options: [
        { value: '', label: 'All Statuses' },
        { value: 'scheduled', label: 'Scheduled' },
        { value: 'confirmed', label: 'Confirmed' },
        { value: 'cancelled', label: 'Cancelled' },
        { value: 'completed', label: 'Completed' },
      ]
    }
  ];

  const getViewTitle = () => {
    const date = currentDate;
    switch (viewMode) {
      case 'month':
        return date.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
      case 'week':
        const weekStart = new Date(date);
        weekStart.setDate(date.getDate() - date.getDay());
        const weekEnd = new Date(weekStart);
        weekEnd.setDate(weekStart.getDate() + 6);
        return `${weekStart.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} - ${weekEnd.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })}`;
      case 'day':
        return date.toLocaleDateString('en-US', { weekday: 'long', month: 'long', day: 'numeric', year: 'numeric' });
      case 'agenda':
        return 'Agenda View';
      default:
        return '';
    }
  };

  // Helper function to get calendar grid dates
  const getCalendarDates = () => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay());
    
    const dates = [];
    const endDate = new Date(lastDay);
    endDate.setDate(endDate.getDate() + (6 - lastDay.getDay()));
    
    for (let date = new Date(startDate); date <= endDate; date.setDate(date.getDate() + 1)) {
      dates.push(new Date(date));
    }
    
    return dates;
  };

  // Helper function to get events for a specific date
  const getEventsForDate = (date: Date) => {
    const dateString = date.toISOString().split('T')[0];
    return events.filter(event => {
      const eventDate = new Date(event.startTime).toISOString().split('T')[0];
      return eventDate === dateString;
    });
  };

  // Helper function to get week dates
  const getWeekDates = () => {
    const startOfWeek = new Date(currentDate);
    startOfWeek.setDate(currentDate.getDate() - currentDate.getDay());
    
    const dates = [];
    for (let i = 0; i < 7; i++) {
      const date = new Date(startOfWeek);
      date.setDate(startOfWeek.getDate() + i);
      dates.push(date);
    }
    return dates;
  };

  // Helper function to get hour slots for day/week view
  const getHourSlots = () => {
    const hours = [];
    for (let i = 0; i < 24; i++) {
      const hour = i === 0 ? 12 : i > 12 ? i - 12 : i;
      const ampm = i < 12 ? 'AM' : 'PM';
      hours.push({
        value: i,
        label: `${hour}:00 ${ampm}`,
        fullLabel: `${hour.toString().padStart(2, '0')}:00 ${ampm}`
      });
    }
    return hours;
  };

  // Month View Component
  const MonthView = () => {
    const calendarDates = getCalendarDates();
    const today = new Date();
    const currentMonth = currentDate.getMonth();

    return (
      <div className="bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 overflow-hidden">
        {/* Days of Week Header */}
        <div className="grid grid-cols-7 bg-gradient-to-r from-primary/5 to-accent/5 border-b border-gray-200/50">
          {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
            <div key={day} className="p-4 text-center">
              <span className="text-sm font-semibold text-gray-700 uppercase tracking-wide">{day}</span>
            </div>
          ))}
        </div>

        {/* Calendar Grid */}
        <div className="grid grid-cols-7">
          {calendarDates.map((date, index) => {
            const isToday = date.toDateString() === today.toDateString();
            const isCurrentMonth = date.getMonth() === currentMonth;
            const dayEvents = getEventsForDate(date);
            
            return (
              <div
                key={index}
                className={`min-h-32 p-2 border-r border-b border-gray-100/50 hover:bg-primary/5 transition-all duration-200 cursor-pointer group ${
                  !isCurrentMonth ? 'bg-gray-50/50' : ''
                } ${isToday ? 'bg-gradient-to-br from-secondary/10 to-orange-400/10' : ''}`}
                onClick={() => {
                  setCurrentDate(date);
                  setViewMode('day');
                }}
              >
                <div className="flex items-center justify-between mb-2">
                  <span className={`text-sm font-medium ${
                    isToday 
                      ? 'bg-secondary text-white w-7 h-7 rounded-full flex items-center justify-center' 
                      : !isCurrentMonth 
                        ? 'text-gray-400' 
                        : 'text-gray-900'
                  }`}>
                    {date.getDate()}
                  </span>
                  {dayEvents.length > 0 && (
                    <span className="text-xs bg-primary/20 text-primary px-2 py-1 rounded-full font-medium">
                      {dayEvents.length}
                    </span>
                  )}
                </div>
                
                {/* Events Preview */}
                <div className="space-y-1">
                  {dayEvents.slice(0, 3).map((event, eventIndex) => {
                    const TypeIcon = typeIcons[event.type];
                    return (
                      <div
                        key={eventIndex}
                        className={`text-xs p-2 rounded-lg border cursor-pointer hover:shadow-sm transition-all duration-200 ${eventTypeColors[event.type]}`}
                        onClick={(e) => {
                          e.stopPropagation();
                          handleEventClick(event);
                        }}
                      >
                        <div className="flex items-center space-x-1">
                          <TypeIcon className="w-3 h-3" />
                          <span className="truncate font-medium">{event.title}</span>
                        </div>
                        <div className="text-xs opacity-75 mt-1">
                          {formatTime(event.startTime)}
                        </div>
                      </div>
                    );
                  })}
                  {dayEvents.length > 3 && (
                    <div className="text-xs text-center py-1 text-gray-500 font-medium">
                      +{dayEvents.length - 3} more
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  // Week View Component
  const WeekView = () => {
    const weekDates = getWeekDates();
    const hourSlots = getHourSlots();
    const today = new Date();

    return (
      <div className="bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 overflow-hidden">
        {/* Week Header */}
        <div className="grid grid-cols-8 bg-gradient-to-r from-primary/5 to-accent/5 border-b border-gray-200/50">
          <div className="p-4"></div> {/* Empty cell for time column */}
          {weekDates.map((date, index) => {
            const isToday = date.toDateString() === today.toDateString();
            return (
              <div key={index} className={`p-4 text-center border-l border-gray-200/50 ${isToday ? 'bg-secondary/10' : ''}`}>
                <div className="text-sm font-semibold text-gray-700">
                  {date.toLocaleDateString('en-US', { weekday: 'short' })}
                </div>
                <div className={`text-lg font-bold mt-1 ${
                  isToday 
                    ? 'bg-secondary text-white w-8 h-8 rounded-full flex items-center justify-center mx-auto' 
                    : 'text-gray-900'
                }`}>
                  {date.getDate()}
                </div>
              </div>
            );
          })}
        </div>

        {/* Time Grid */}
        <div className="max-h-96 overflow-y-auto calendar-events-scroll">
          {hourSlots.map((hour, hourIndex) => (
            <div key={hourIndex} className="grid grid-cols-8 border-b border-gray-100/50 hover:bg-gray-50/30 transition-colors">
              {/* Time Label */}
              <div className="p-3 text-right text-sm text-gray-500 font-medium border-r border-gray-200/50 bg-gray-50/30">
                {hour.label}
              </div>
              
              {/* Day Columns */}
              {weekDates.map((date, dayIndex) => {
                const dayEvents = getEventsForDate(date).filter(event => {
                  const eventHour = new Date(event.startTime).getHours();
                  return eventHour === hour.value;
                });
                
                return (
                  <div key={dayIndex} className="p-2 border-l border-gray-200/50 min-h-16 relative group">
                    {dayEvents.map((event, eventIndex) => {
                      const TypeIcon = typeIcons[event.type];
                      return (
                        <div
                          key={eventIndex}
                          className={`text-xs p-2 rounded-lg border cursor-pointer hover:shadow-md transition-all duration-200 mb-1 ${eventTypeColors[event.type]}`}
                          onClick={() => handleEventClick(event)}
                        >
                          <div className="flex items-center space-x-1">
                            <TypeIcon className="w-3 h-3" />
                            <span className="truncate font-medium">{event.title}</span>
                          </div>
                          <div className="text-xs opacity-75 mt-1">
                            {formatTime(event.startTime)} - {formatTime(event.endTime)}
                          </div>
                        </div>
                      );
                    })}
                    
                    {/* Add Event Button on Hover */}
                    <button
                      className="absolute inset-0 bg-primary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center"
                      onClick={() => {
                        const newDate = new Date(date);
                        newDate.setHours(hour.value);
                        setCurrentDate(newDate);
                        setShowCreateModal(true);
                      }}
                    >
                      <Plus className="w-4 h-4 text-primary" />
                    </button>
                  </div>
                );
              })}
            </div>
          ))}
        </div>
      </div>
    );
  };

  // Meeting Card Component with Video Conferencing Features
  const MeetingCard = ({ event, className = '' }: { event: CalendarEvent; className?: string }) => {
    const meetingInfo = generateMeetingDetails(event);
    const TypeIcon = typeIcons[event.type];

    return (
      <div className={`${className} calendar-event-card`}>
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-3">
            <div className="p-2 rounded-lg bg-white/50">
              <TypeIcon className="w-4 h-4" />
            </div>
            <div>
              <h4 className="font-semibold">{event.title}</h4>
              <p className="text-sm opacity-75">
                {formatTime(event.startTime)} - {formatTime(event.endTime)}
              </p>
            </div>
          </div>
          <span className={`px-3 py-1 rounded-full text-xs font-medium ${statusColors[event.status]}`}>
            {event.status}
          </span>
        </div>

        {/* Video Conferencing Section */}
        {meetingInfo && (
          <div className={`mb-4 p-4 rounded-xl border-2 ${meetingInfo.platform.borderColor} ${meetingInfo.platform.bgColor}`}>
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center space-x-2">
                <div className={`p-2 rounded-lg ${meetingInfo.platform.color} text-white`}>
                  <meetingInfo.platform.icon className="w-4 h-4" />
                </div>
                <div>
                  <span className={`font-semibold ${meetingInfo.platform.textColor}`}>
                    {meetingInfo.platform.name}
                  </span>
                  {event.meetingDetails?.meetingId && (
                    <p className="text-xs text-gray-600">
                      ID: {event.meetingDetails.meetingId}
                    </p>
                  )}
                </div>
              </div>
              
              <div className="flex space-x-2">
                <button
                  onClick={() => copyMeetingLink(meetingInfo.link)}
                  className="p-2 rounded-lg bg-white/50 hover:bg-white/80 transition-colors"
                  title="Copy meeting link"
                >
                  <Copy className="w-4 h-4 text-gray-600" />
                </button>
                <button
                  onClick={() => joinMeeting(event)}
                  className={`px-4 py-2 rounded-lg ${meetingInfo.platform.color} text-white font-medium hover:opacity-90 transition-opacity flex items-center space-x-2`}
                >
                  <ExternalLink className="w-4 h-4" />
                  <span>Join</span>
                </button>
              </div>
            </div>

            {/* Meeting Details */}
            {event.meetingDetails && (
              <div className="space-y-2 text-sm">
                {event.meetingDetails.passcode && (
                  <div className="flex items-center space-x-2">
                    <span className="font-medium text-gray-700">Passcode:</span>
                    <code className="bg-white/70 px-2 py-1 rounded text-xs font-mono">
                      {event.meetingDetails.passcode}
                    </code>
                  </div>
                )}
                
                {event.meetingDetails.dialInNumbers && event.meetingDetails.dialInNumbers.length > 0 && (
                  <div>
                    <span className="font-medium text-gray-700">Dial-in Numbers:</span>
                    <div className="mt-1 space-y-1">
                      {event.meetingDetails.dialInNumbers.slice(0, 2).map((number, index) => (
                        <div key={index} className="text-xs text-gray-600">
                          📞 {number}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Meeting Features */}
                <div className="flex flex-wrap gap-2 mt-3">
                  {event.meetingDetails.recordingEnabled && (
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-red-100 text-red-700">
                      🔴 Recording
                    </span>
                  )}
                  {event.meetingDetails.waitingRoom && (
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-yellow-100 text-yellow-700">
                      ⏳ Waiting Room
                    </span>
                  )}
                  {event.meetingDetails.requireAuth && (
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-700">
                      🔒 Auth Required
                    </span>
                  )}
                  {meetingInfo.platform.features.slice(0, 2).map((feature, index) => (
                    <span key={index} className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-700">
                      {feature}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Regular Event Details */}
        {event.description && (
          <p className="text-sm mt-2 opacity-75 line-clamp-2 mb-3">
            {event.description}
          </p>
        )}

        {(event.location || event.client) && (
          <div className="flex items-center space-x-4 mt-3 text-sm opacity-75">
            {event.location && (
              <div className="flex items-center space-x-1">
                <MapPin className="w-3 h-3" />
                <span>{event.location}</span>
              </div>
            )}
            {event.client && (
              <div className="flex items-center space-x-1">
                <Mail className="w-3 h-3" />
                <span>{getClientName(event.client)}</span>
              </div>
            )}
          </div>
        )}

        {/* Attendees */}
        {event.attendees && event.attendees.length > 0 && (
          <div className="mt-3 p-3 bg-gray-50/50 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700">
                Attendees ({event.attendees.length})
              </span>
              <Users className="w-4 h-4 text-gray-500" />
            </div>
            <div className="flex -space-x-2">
              {event.attendees.slice(0, 5).map((attendee, index) => (
                <div
                  key={index}
                  className="w-8 h-8 rounded-full bg-primary/10 border-2 border-white flex items-center justify-center text-xs font-medium text-primary"
                  title={attendee.name}
                >
                  {attendee.name.charAt(0).toUpperCase()}
                </div>
              ))}
              {event.attendees.length > 5 && (
                <div className="w-8 h-8 rounded-full bg-gray-200 border-2 border-white flex items-center justify-center text-xs font-medium text-gray-600">
                  +{event.attendees.length - 5}
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    );
  };

  // Day View Component
  const DayView = () => {
    const hourSlots = getHourSlots();
    const dayEvents = getEventsForDate(currentDate);
    const today = new Date();
    const isToday = currentDate.toDateString() === today.toDateString();

    return (
      <div className="bg-white/80 backdrop-blur-xl rounded-2xl shadow-xl border border-white/20 overflow-hidden">
        {/* Day Header */}
        <div className={`p-6 border-b border-gray-200/50 ${isToday ? 'bg-gradient-to-r from-secondary/10 to-orange-400/10' : 'bg-gradient-to-r from-primary/5 to-accent/5'}`}>
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-xl font-bold text-gray-900">
                {currentDate.toLocaleDateString('en-US', { weekday: 'long', month: 'long', day: 'numeric', year: 'numeric' })}
              </h3>
              <p className="text-sm text-gray-600 mt-1">
                {dayEvents.length} event{dayEvents.length !== 1 ? 's' : ''} scheduled
                {isToday && <span className="ml-2 text-secondary font-medium">• Today</span>}
              </p>
            </div>
            <button
              onClick={() => setShowCreateModal(true)}
              className="flex items-center px-4 py-2 bg-primary hover:bg-primary/90 text-white rounded-lg font-medium transition-colors"
            >
              <Plus className="w-4 h-4 mr-2" />
              Add Event
            </button>
          </div>
        </div>

        {/* Time Slots */}
        <div className="max-h-96 overflow-y-auto calendar-events-scroll">
          {hourSlots.map((hour, hourIndex) => {
            const hourEvents = dayEvents.filter(event => {
              const eventHour = new Date(event.startTime).getHours();
              return eventHour === hour.value;
            });
            
            return (
              <div key={hourIndex} className="grid grid-cols-12 border-b border-gray-100/50 hover:bg-gray-50/30 transition-colors">
                {/* Time Label */}
                <div className="col-span-2 p-4 text-right text-sm text-gray-500 font-medium border-r border-gray-200/50 bg-gray-50/30">
                  {hour.label}
                </div>
                
                {/* Event Area */}
                <div className="col-span-10 p-3 relative group min-h-20">
                  {hourEvents.map((event, eventIndex) => (
                    <MeetingCard
                      key={eventIndex}
                      event={event}
                      className={`p-4 rounded-xl border cursor-pointer hover:shadow-lg transition-all duration-200 mb-2 ${eventTypeColors[event.type]}`}
                    />
                  ))}
                  
                  {/* Add Event Button on Hover */}
                  {hourEvents.length === 0 && (
                    <button
                      className="absolute inset-0 bg-primary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center rounded-lg border-2 border-dashed border-primary/20 group-hover:border-primary/40"
                      onClick={() => {
                        const newDate = new Date(currentDate);
                        newDate.setHours(hour.value);
                        setCurrentDate(newDate);
                        setShowCreateModal(true);
                      }}
                    >
                      <div className="text-center">
                        <Plus className="w-6 h-6 text-primary mx-auto mb-1" />
                        <span className="text-sm text-primary font-medium">Add Event</span>
                      </div>
                    </button>
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <CalendarIcon className="h-8 w-8 text-blue-600" />
              <div className="ml-3">
                <h1 className="text-xl font-semibold text-gray-900">Calendar & Scheduling</h1>
                <p className="text-sm text-gray-500">Manage appointments, meetings, and deadlines</p>
        </div>
                </div>
            <div className="flex items-center space-x-3">
              <div className="hidden sm:flex items-center space-x-2">
                <span className="text-sm text-gray-500">Total: {stats.total}</span>
                <span className="text-sm text-gray-500">•</span>
                <span className="text-sm text-gray-500">Upcoming: {stats.upcoming}</span>
              </div>
              
              {/* View Mode Toggles */}
              <div className="hidden md:flex items-center bg-gray-100 rounded-lg p-1">
                {['month', 'week', 'day', 'agenda'].map((mode) => (
                  <button
                    key={mode}
                    onClick={() => setViewMode(mode as any)}
                    className={`px-3 py-1.5 rounded-md text-sm font-medium transition-colors capitalize ${
                      viewMode === mode 
                        ? 'bg-white shadow text-blue-600' 
                        : 'text-gray-500 hover:text-gray-700'
                    }`}
                  >
                    {mode}
                  </button>
                ))}
              </div>
              
              <button
                onClick={() => setShowCreateModal(true)}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <Plus className="h-4 w-4 mr-2" />
                New Event
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Filters Overlay */}
      {isMobile && showFilters && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
            <div 
              className="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" 
              onClick={() => setShowFilters(false)}
            ></div>
            
            <div className="inline-block px-4 pt-5 pb-4 overflow-hidden text-left align-bottom transition-all transform bg-white rounded-lg shadow-xl sm:my-8 sm:align-middle sm:max-w-lg sm:w-full sm:p-6">
              <div className="mb-4">
                <h3 className="text-lg font-medium text-gray-900">Filter Events</h3>
              </div>
              
          <div className="space-y-4">
                <div>
                  <input
                    type="text"
              placeholder="Search events..."
                    value={searchTerm}
                    onChange={(e) => handleSearch(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
                </div>
            
              <div className="space-y-3">
                {filterOptions.map((filter, index) => (
                  <select
                    key={index}
                    value={filter.value}
                    onChange={(e) => filter.onChange(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    {filter.options.map((option) => (
                      <option key={option.value} value={option.value}>
                        {option.label}
                      </option>
                    ))}
                  </select>
                ))}
              </div>
                
                <div className="flex justify-end space-x-3 pt-4">
                  <button
                    onClick={clearFilters}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
                  >
                    Clear Filters
                  </button>
                  <button
                    onClick={() => setShowFilters(false)}
                    className="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700"
                  >
                    Done
                  </button>
                  </div>
                  </div>
                </div>
                </div>
              </div>
      )}
          
      {/* Stats Cards */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-6">
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CalendarIcon className="h-8 w-8 text-blue-600" />
                  </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Total Events</dt>
                  <dd className="text-lg font-medium text-gray-900">{stats.total}</dd>
                </dl>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Clock className="h-8 w-8 text-orange-600" />
                  </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Today</dt>
                  <dd className="text-lg font-medium text-gray-900">{stats.today}</dd>
                </dl>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Circle className="h-8 w-8 text-blue-600" />
                  </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Upcoming</dt>
                  <dd className="text-lg font-medium text-gray-900">{stats.upcoming}</dd>
                </dl>
              </div>
            </div>
          </div>
          
          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Users className="h-8 w-8 text-green-600" />
                  </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Meetings</dt>
                  <dd className="text-lg font-medium text-gray-900">{stats.meetings}</dd>
                </dl>
                  </div>
                </div>
                </div>

          <div className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <AlertTriangle className="h-8 w-8 text-red-600" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Deadlines</dt>
                  <dd className="text-lg font-medium text-gray-900">{stats.deadlines}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        {/* Calendar Navigation */}
        <div className="bg-white shadow rounded-lg mb-6">
          <div className="p-4 sm:p-6">
            <div className="flex flex-col sm:flex-row gap-4">
                {/* Date Navigation */}
                <div className="flex items-center space-x-3">
                  <button
                    onClick={() => navigateDate('prev')}
                  className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
                  >
                  <ChevronLeft className="w-5 h-5" />
                  </button>
                  
                <div className="px-4 py-2 bg-gray-50 rounded-lg">
                  <h2 className="text-lg font-semibold text-gray-900 min-w-0 text-center">
                      {getViewTitle()}
                    </h2>
                  </div>
                  
                  <button
                    onClick={() => navigateDate('next')}
                  className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
                  >
                  <ChevronRight className="w-5 h-5" />
                  </button>
                </div>
                
                {/* Today Button */}
                <button
                  onClick={goToToday}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors"
                >
                Today
                </button>

              {/* Search */}
              <div className="flex-1">
                  <div className="relative">
                    <input
                      type="text"
                      value={searchTerm}
                      onChange={(e) => handleSearch(e.target.value)}
                    placeholder="Search events..."
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
                    <Filter className="h-4 w-4 text-gray-400" />
                    </div>
                    {searchTerm && (
                      <button
                        onClick={() => handleSearch('')}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 p-1 rounded-full hover:bg-gray-200 transition-colors"
                      >
                        <X className="h-4 w-4 text-gray-400" />
                      </button>
                    )}
                  </div>
              </div>
            </div>
          </div>
        </div>

        {/* Error Alert */}
        {error && (
          <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-center">
              <AlertTriangle className="w-4 h-4 text-red-600 mr-2" />
              <span className="text-red-800">{error}</span>
            </div>
          </div>
        )}

        {/* Calendar Content */}
        {loading ? (
          <div className="bg-white rounded-lg shadow p-6">
            <div className="animate-pulse space-y-4">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="h-4 bg-gray-200 rounded"></div>
              ))}
            </div>
                </div>
        ) : events.length === 0 ? (
          <div className="bg-white shadow rounded-lg">
            <div className="text-center py-12">
              <CalendarIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No events found</h3>
              <p className="text-gray-500 mb-6">
                {searchTerm || typeFilter || statusFilter
                  ? 'Try adjusting your search criteria or filters to find what you\'re looking for.'
                  : 'Get started by creating your first event to organize your schedule.'}
              </p>
              <button
                onClick={() => setShowCreateModal(true)}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
              >
                <Plus className="w-4 h-4 mr-2" />
                Create First Event
              </button>
            </div>
          </div>
        ) : viewMode === 'agenda' || isMobile ? (
          // Agenda/Mobile View - List of Events
          <div className="space-y-4">
            {events.map((event) => {
              const TypeIcon = typeConfig[event.type]?.icon || Calendar;
              const isToday = new Date(event.startTime).toDateString() === new Date().toDateString();
              
              return (
                <div
                  key={event.id}
                  onClick={() => handleEventClick(event)}
                  className="bg-white shadow rounded-lg p-6 cursor-pointer hover:shadow-md transition-shadow"
                >
                  <div className="flex items-start justify-between mb-4">
                      <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-3 mb-2">
                        <div className="flex-shrink-0">
                          <TypeIcon className="h-5 w-5 text-blue-600" />
                          </div>
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${typeConfig[event.type]?.color || 'bg-gray-100 text-gray-800'}`}>
                          {typeConfig[event.type]?.label || event.type}
                            </span>
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusConfig[event.status]?.color || 'bg-gray-100 text-gray-800'}`}>
                          {statusConfig[event.status]?.label || event.status}
                            </span>
                            {isToday && (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                            Today
                              </span>
                            )}
                        </div>
                        
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">
                          {event.title}
                        </h3>
                        
                        {event.description && (
                        <p className="text-gray-600 text-sm mb-3">
                            {event.description}
                          </p>
                        )}
                      </div>
                      
                    <div className="ml-4">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            setShowDropdown(showDropdown === event.id ? null : event.id);
                          }}
                        className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
                      >
                        <MoreVertical className="w-4 h-4" />
                          </button>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
                    <div className="flex items-center text-gray-500">
                      <Clock className="w-4 h-4 mr-2" />
                      <span>{formatDateTime(event.startTime)} - {formatTime(event.endTime)}</span>
                    </div>

                    {event.location && (
                      <div className="flex items-center text-gray-500">
                        <MapPin className="w-4 h-4 mr-2" />
                        <span>{event.location}</span>
                                  </div>
                    )}
                    
                    {event.attendees && event.attendees.length > 0 && (
                      <div className="flex items-center text-gray-500">
                        <Users className="w-4 h-4 mr-2" />
                        <span>{event.attendees.length} attendees</span>
                                    </div>
                                  )}
                                  
                    {event.isVirtual && event.meetingLink && (
                      <div className="flex items-center text-blue-600">
                        <Video className="w-4 h-4 mr-2" />
                        <span>Virtual Meeting</span>
                                    </div>
                                  )}
                                </div>

                  {event.isVirtual && event.meetingLink && (
                    <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <Video className="w-4 h-4 text-blue-600 mr-2" />
                          <span className="text-sm font-medium text-blue-900">
                            {getMeetingPlatformInfo(event.meetingPlatform)?.name || 'Virtual Meeting'}
                                  </span>
                              </div>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            joinMeeting(event);
                          }}
                          className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-blue-600 hover:bg-blue-700"
                        >
                          Join Meeting
                        </button>
                            </div>
                      </div>
                    )}
                </div>
              );
            })}
          </div>
        ) : (
          // Enhanced Calendar Grid Views
          <div className="space-y-6">
            {viewMode === 'month' && <MonthView />}
            {viewMode === 'week' && <WeekView />}
            {viewMode === 'day' && <DayView />}
          </div>
        )}
      </div>

      {/* Create Event Modal */}
        <CreateEventModal 
          isOpen={showCreateModal}
          onClose={() => setShowCreateModal(false)}
          onEventCreated={(newEvent) => {
            setEvents(prev => [...prev, newEvent]);
            setShowCreateModal(false);
          }}
          defaultDate={currentDate}
        />

      {/* Event Details Modal */}
      {selectedEvent && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" onClick={() => setSelectedEvent(null)}></div>
            
            <span className="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
            
            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full">
              <div className="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium text-gray-900">Event Details</h3>
                  <button
                    onClick={() => setSelectedEvent(null)}
                    className="text-gray-400 hover:text-gray-600"
                  >
                    <X className="w-5 h-5" />
                  </button>
                  </div>
                
                <div className="space-y-4">
                  <div>
                    <h4 className="text-xl font-semibold text-gray-900">{selectedEvent.title}</h4>
                  {selectedEvent.description && (
                      <p className="text-gray-600 mt-2">{selectedEvent.description}</p>
                  )}
                </div>

                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div className="flex items-center text-gray-500">
                      <Clock className="w-4 h-4 mr-2" />
                      <span>{formatDateTime(selectedEvent.startTime)} - {formatTime(selectedEvent.endTime)}</span>
                  </div>

                  {selectedEvent.location && (
                      <div className="flex items-center text-gray-500">
                        <MapPin className="w-4 h-4 mr-2" />
                        <span>{selectedEvent.location}</span>
                    </div>
                  )}

                  {selectedEvent.attendees && selectedEvent.attendees.length > 0 && (
                      <div className="flex items-center text-gray-500">
                        <Users className="w-4 h-4 mr-2" />
                        <span>{selectedEvent.attendees.length} attendees</span>
                    </div>
                  )}

                    {selectedEvent.isVirtual && selectedEvent.meetingLink && (
                      <div className="flex items-center text-blue-600">
                        <Video className="w-4 h-4 mr-2" />
                        <span>Virtual Meeting</span>
                    </div>
                  )}
                </div>

                  {selectedEvent.isVirtual && selectedEvent.meetingLink && (
                    <div className="p-3 bg-blue-50 rounded-lg">
                      <div className="flex items-center justify-between">
                  <div className="flex items-center">
                          <Video className="w-4 h-4 text-blue-600 mr-2" />
                          <span className="text-sm font-medium text-blue-900">
                            {getMeetingPlatformInfo(selectedEvent.meetingPlatform || 'custom')?.name || 'Virtual Meeting'}
                          </span>
                  </div>
                        <button
                          onClick={() => joinMeeting(selectedEvent)}
                          className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded text-white bg-blue-600 hover:bg-blue-700"
                        >
                          Join Meeting
                        </button>
                    </div>
                  </div>
                )}
              </div>
                
                <div className="mt-6 flex justify-end space-x-3">
                  <button
                    onClick={() => setSelectedEvent(null)}
                    className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
                  >
                    Close
                  </button>
                  <button
                    onClick={() => {
                      startEditEvent(selectedEvent);
                      setSelectedEvent(null);
                    }}
                    className="px-4 py-2 bg-blue-600 border border-transparent rounded-md text-sm font-medium text-white hover:bg-blue-700"
                  >
                    Edit Event
                  </button>
            </div>
          </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 