'use client';

import { useState, useEffect, useCallback } from 'react';
import {
  ArrowPathIcon,
  PlusIcon,
  ShoppingBagIcon,
  PencilIcon,
  TrashIcon,
  XCircleIcon,
  CheckIcon,
  ExclamationTriangleIcon,
  MagnifyingGlassIcon
} from '@heroicons/react/24/outline';
import { useNotification } from '@/contexts/NotificationContext';
import AdminCard, { AdminCardList } from '@/components/admin/AdminCard';

interface Service {
  id: string;
  name: string;
  description: string | null;
  price: number;
  category: string;
  createdAt: string;
  updatedAt: string;
}

interface Category {
  id: string;
  name: string;
  slug: string;
  description: string;
  createdAt: string;
  updatedAt: string;
}

export default function ServicesPage() {
  const [services, setServices] = useState<Service[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const { showNotification } = useNotification();

  // Bulk actions state
  const [selectedServices, setSelectedServices] = useState<Set<string>>(new Set());
  const [bulkActionLoading, setBulkActionLoading] = useState(false);
  const [showBulkActions, setShowBulkActions] = useState(false);

  // Delete confirmation state
  const [deleteConfirmation, setDeleteConfirmation] = useState<{
    isOpen: boolean;
    type: 'single' | 'bulk';
    serviceId?: string;
    serviceName?: string;
    count?: number;
  }>({ isOpen: false, type: 'single' });

  // Form state
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingService, setEditingService] = useState<Service | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    price: '',
    category: 'Design'
  });

  const fetchServices = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/admin/services', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        cache: 'no-store'
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('Response error:', response.status, errorData);
        throw new Error(errorData.error || 'Failed to fetch services');
      }

      const result = await response.json();
      console.log('Services API response:', result);

      // Handle new API response format
      const services = result.success ? result.data : result;
      console.log('Services loaded:', services?.length || 0);
      setServices(services || []);
    } catch (err) {
      console.error('Error fetching services:', err);
      setError(err instanceof Error ? err.message : 'Failed to load services');
      showNotification('error', 'Failed to load services');
      // Set empty array to prevent rendering issues
      setServices([]);
    } finally {
      setLoading(false);
    }
  }, [showNotification]);

  const fetchCategories = useCallback(async () => {
    try {
      const timestamp = new Date().getTime();
      const response = await fetch(`/api/admin/categories?t=${timestamp}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        cache: 'no-store'
      });

      if (!response.ok) {
        console.error('Failed to fetch categories:', response.status);
        return;
      }

      const result = await response.json();
      const categoriesList = result.success ? result.data : result;
      console.log('Categories loaded:', categoriesList?.length || 0);
      setCategories(categoriesList || []);
    } catch (err) {
      console.error('Error fetching categories:', err);
      // If categories fail to load, we'll fall back to hardcoded ones
      setCategories([{ id: '1', name: 'Design', slug: 'design', description: '', createdAt: '', updatedAt: '' }]);
    }
  }, []);

  useEffect(() => {
    const initializePage = async () => {
      // Fetch CSRF token first
      // Then fetch services and categories in parallel
      await Promise.all([fetchServices(), fetchCategories()]);
    };
    
    initializePage();
  }, [fetchServices, fetchCategories]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const resetForm = () => {
    const defaultCategory = categories?.[0]?.name || 'Design';
    setFormData({
      name: '',
      description: '',
      price: '',
      category: defaultCategory
    });
    setEditingService(null);
  };

  const handleOpenForm = (service?: Service) => {
    if (service) {
      setEditingService(service);
      // Check if the service's category still exists in our categories list
      const categoryExists = Array.isArray(categories) && categories.some(cat => cat.name === service.category);
      const defaultCategory = categories?.[0]?.name || 'Design';
      
      setFormData({
        name: service.name,
        description: service.description || '',
        price: service.price.toString(),
        category: categoryExists ? service.category : defaultCategory
      });
    } else {
      resetForm();
    }
    setIsFormOpen(true);
  };

  const handleCloseForm = () => {
    setIsFormOpen(false);
    resetForm();
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setLoading(true);

      const serviceData = {
        name: formData.name.trim(),
        description: formData.description?.trim() || null,
        price: parseFloat(formData.price),
        category: formData.category
      };

      // Validate required fields
      if (!serviceData.name || isNaN(serviceData.price) || !serviceData.category) {
        showNotification('error', 'Please fill in all required fields');
        return;
      }

      let response;

      if (editingService) {
        // Update existing service
        response = await fetch(`/api/admin/services?id=${editingService.id}`, {
          method: 'PATCH',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            id: editingService.id,
            ...serviceData
          })
        });
      } else {
        // Create new service
        response = await fetch('/api/admin/services', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(serviceData)
        });
      }

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save service');
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to save service');
      }

      showNotification('success', editingService ? 'Service updated successfully' : 'Service created successfully');
      handleCloseForm();
      fetchServices();
    } catch (error) {
      console.error('Error saving service:', error);
      showNotification('error', error instanceof Error ? error.message : 'Failed to save service');
    } finally {
      setLoading(false);
    }
  };

  // Bulk action functions
  const handleSelectAll = () => {
    if (selectedServices.size === filteredServices.length) {
      setSelectedServices(new Set());
    } else {
      setSelectedServices(new Set(filteredServices.map(service => service.id)));
    }
  };

  const handleSelectService = (serviceId: string) => {
    const newSelected = new Set(selectedServices);
    if (newSelected.has(serviceId)) {
      newSelected.delete(serviceId);
    } else {
      newSelected.add(serviceId);
    }
    setSelectedServices(newSelected);
  };

  const handleBulkDelete = () => {
    if (selectedServices.size === 0) return;
    
    setDeleteConfirmation({
      isOpen: true,
      type: 'bulk',
      count: selectedServices.size
    });
  };

  const executeBulkDelete = async () => {
    if (selectedServices.size === 0) return;

    try {
      setBulkActionLoading(true);
      
      console.log(`[Bulk Delete] Starting deletion of ${selectedServices.size} services:`, Array.from(selectedServices));
      
      // First, validate that all selected services still exist
      const selectedArray = Array.from(selectedServices);
      const existingServices = services.filter(service => selectedArray.includes(service.id));
      
      if (existingServices.length !== selectedArray.length) {
        const missingIds = selectedArray.filter(id => !services.some(s => s.id === id));
        console.warn(`[Bulk Delete] Some selected services no longer exist:`, missingIds);
        
        // Update selection to only include existing services
        setSelectedServices(new Set(existingServices.map(s => s.id)));
        
        if (existingServices.length === 0) {
          showNotification('info', 'Selected services no longer exist. Please refresh and try again.');
          await fetchServices();
          setDeleteConfirmation({ isOpen: false, type: 'single' });
          return;
        }
        
        showNotification('info', `${missingIds.length} selected service(s) no longer exist. Proceeding with ${existingServices.length} remaining service(s).`);
      }
      
      const deletePromises = existingServices.map(async (service) => {
        console.log(`[Bulk Delete] Attempting to delete service: ${service.id} (${service.name})`);
        
        try {
          const response = await fetch(`/api/admin/services/${service.id}`, {
            method: 'DELETE',
            headers: { 'Content-Type': 'application/json' },
            credentials: 'include',
          });
          
          console.log(`[Bulk Delete] Response status for ${service.id}: ${response.status} ${response.statusText}`);
          
          let responseData;
          try {
            const responseText = await response.text();
            console.log(`[Bulk Delete] Raw response for ${service.id}:`, responseText);
            
            if (responseText) {
              responseData = JSON.parse(responseText);
            } else {
              responseData = { error: 'Empty response from server' };
            }
          } catch (parseError) {
            console.error(`[Bulk Delete] Failed to parse response for ${service.id}:`, parseError);
            responseData = { error: 'Invalid JSON response from server' };
          }
          
          console.log(`[Bulk Delete] Parsed response for ${service.id}:`, responseData);
          
          if (!response.ok) {
            console.error(`[Bulk Delete] Failed to delete ${service.id} (${service.name}):`, responseData);
            
            // Handle empty or malformed error responses
            let errorMessage = 'Unknown error';
            if (responseData && typeof responseData === 'object') {
              errorMessage = responseData.error || responseData.message || responseData.detail || 'Server returned error without details';
            } else if (typeof responseData === 'string') {
              errorMessage = responseData;
            }
            
            throw new Error(`${errorMessage} (Status: ${response.status})`);
          }
          
          console.log(`[Bulk Delete] Successfully deleted service: ${service.id} (${service.name})`);
          return service.id;
        } catch (networkError) {
          console.error(`[Bulk Delete] Network error for ${service.id}:`, networkError);
          const errorMessage = networkError instanceof Error ? networkError.message : 'Unknown network error';
          throw new Error(`Failed to delete "${service.name}": Network error - ${errorMessage}`);
        }
      });

      const results = await Promise.allSettled(deletePromises);
      
      // Process results
      const successful = results.filter((r): r is PromiseFulfilledResult<string> => r.status === 'fulfilled');
      const failed = results.filter((r): r is PromiseRejectedResult => r.status === 'rejected');
      
      console.log(`[Bulk Delete] Deletion results - Success: ${successful.length}, Failed: ${failed.length}`);
      
      if (successful.length > 0) {
        // Refresh services list
        await fetchServices();
        
        // Clear selection
        setSelectedServices(new Set());
        setDeleteConfirmation({ isOpen: false, type: 'single' });
        
        if (failed.length === 0) {
          showNotification('success', `Successfully deleted ${successful.length} service(s)`);
        } else {
          showNotification('info', `Successfully deleted ${successful.length} service(s), but ${failed.length} failed`);
        }
      }
      
      if (failed.length > 0) {
        const errorMessages = failed.map(f => f.reason.message);
        console.error('[Bulk Delete] Failed deletions:', errorMessages);
        
        // Show specific error messages
        showNotification('error', `Failed to delete some services: ${errorMessages.join('; ')}`);
      }
    } catch (err) {
      console.error('[Bulk Delete] Error during bulk delete:', err);
      
      // Show specific error message if available
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete services. Please try again.';
      showNotification('error', errorMessage);
      
      // Refresh the list to show current state
      await fetchServices();
    } finally {
      setBulkActionLoading(false);
    }
  };

  // Updated delete function with confirmation
  const handleDelete = (service: Service) => {
    setDeleteConfirmation({
      isOpen: true,
      type: 'single',
      serviceId: service.id,
      serviceName: service.name
    });
  };

  const executeSingleDelete = async () => {
    if (!deleteConfirmation.serviceId) return;

    try {
      setBulkActionLoading(true);

      const response = await fetch(`/api/admin/services?id=${deleteConfirmation.serviceId}`, {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete service');
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Failed to delete service');
      }

      showNotification('success', 'Service deleted successfully');
      setDeleteConfirmation({ isOpen: false, type: 'single' });
      fetchServices();
    } catch (error) {
      console.error('Error deleting service:', error);
      showNotification('error', error instanceof Error ? error.message : 'Failed to delete service');
    } finally {
      setBulkActionLoading(false);
    }
  };

  const closeDeleteConfirmation = () => {
    setDeleteConfirmation({ isOpen: false, type: 'single' });
  };

  // Filter services based on search term
  const filteredServices = services.filter((service) =>
    service.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    service.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (service.description && service.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div className="flex items-center">
          <div className="p-2 bg-blue-50 text-blue-500 rounded-lg mr-3">
            <ShoppingBagIcon className="h-6 w-6" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-slate-800">Services</h1>
            {selectedServices.size > 0 && (
              <p className="text-sm text-slate-600">
                {selectedServices.size} service(s) selected
              </p>
            )}
          </div>
        </div>
        <div className="flex space-x-2">
          {/* Bulk Actions */}
          {selectedServices.size > 0 && (
            <>
              <button
                onClick={handleBulkDelete}
                disabled={bulkActionLoading}
                className="inline-flex items-center px-4 py-2 border border-red-300 rounded-md shadow-sm text-sm font-medium text-red-700 bg-red-50 hover:bg-red-100 disabled:opacity-50"
              >
                <TrashIcon className="h-4 w-4 mr-1" />
                Delete Selected ({selectedServices.size})
              </button>
              <button
                onClick={() => setSelectedServices(new Set())}
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                Clear Selection
              </button>
            </>
          )}
          
          <button
            onClick={() => {
              fetchServices();
              fetchCategories();
            }}
            className="p-2 rounded-lg text-slate-500 hover:text-slate-700 hover:bg-slate-100"
            disabled={loading}
            title="Refresh"
          >
            <ArrowPathIcon className={`h-5 w-5 ${loading ? 'animate-spin' : ''}`} />
          </button>
          <button
            onClick={() => handleOpenForm()}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-500 hover:bg-orange-600"
          >
            <PlusIcon className="h-4 w-4 mr-1" />
            Add Service
          </button>
        </div>
      </div>

      {/* Search Bar */}
      <div className="bg-white shadow rounded-lg p-4">
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
          </div>
          <input
            type="text"
            placeholder="Search services by name, category, or description..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="block w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          />
          {searchTerm && (
            <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
              <button
                onClick={() => setSearchTerm('')}
                className="text-gray-400 hover:text-gray-600"
                title="Clear search"
              >
                <XCircleIcon className="h-5 w-5" />
              </button>
            </div>
          )}
        </div>
        {searchTerm && (
          <div className="mt-2 text-sm text-gray-600">
            {filteredServices.length} of {services.length} services match your search
          </div>
        )}
      </div>

      {error && (
        <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <XCircleIcon className="h-5 w-5 text-red-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Service Form Modal */}
      {isFormOpen && (
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">
              {editingService ? 'Edit Service' : 'Add New Service'}
            </h2>

            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                  Service Name
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  required
                />
              </div>

              <div>
                <label htmlFor="description" className="block text-sm font-medium text-gray-700">
                  Description
                </label>
                <textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  rows={3}
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                />
              </div>

              <div>
                <label htmlFor="price" className="block text-sm font-medium text-gray-700">
                  Price (KES)
                </label>
                <input
                  type="number"
                  id="price"
                  name="price"
                  value={formData.price}
                  onChange={handleInputChange}
                  min="0"
                  step="0.01"
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  required
                />
              </div>

              <div>
                <label htmlFor="category" className="block text-sm font-medium text-gray-700">
                  Category
                </label>
                <select
                  id="category"
                  name="category"
                  value={formData.category}
                  onChange={handleInputChange}
                  className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  required
                >
                  {categories.length > 0 ? (
                    categories.map((category) => (
                      <option key={category.id} value={category.name}>
                        {category.name}
                      </option>
                    ))
                  ) : (
                    // Fallback options if categories haven't loaded yet
                    <>
                      <option value="Design">Design</option>
                      <option value="Development">Development</option>
                      <option value="Marketing">Marketing</option>
                      <option value="Branding">Branding</option>
                      <option value="Other">Other</option>
                    </>
                  )}
                </select>
              </div>

              <div className="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={handleCloseForm}
                  className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  {editingService ? 'Update' : 'Create'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {deleteConfirmation.isOpen && (
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full p-6">
            <div className="flex items-center mb-4">
              <div className="flex-shrink-0">
                <ExclamationTriangleIcon className="h-6 w-6 text-red-600" />
              </div>
              <div className="ml-3">
                <h3 className="text-lg font-medium text-gray-900">
                  {deleteConfirmation.type === 'single' ? 'Delete Service' : 'Delete Multiple Services'}
                </h3>
              </div>
            </div>
            
            <div className="mb-4">
              <p className="text-sm text-gray-500">
                {deleteConfirmation.type === 'single' 
                  ? `Are you sure you want to delete "${deleteConfirmation.serviceName}"? This action cannot be undone.`
                  : `Are you sure you want to delete ${deleteConfirmation.count} selected service(s)? This action cannot be undone.`
                }
              </p>
            </div>

            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={closeDeleteConfirmation}
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={deleteConfirmation.type === 'single' ? executeSingleDelete : executeBulkDelete}
                disabled={loading || bulkActionLoading}
                className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 disabled:opacity-50"
              >
                {(loading || bulkActionLoading) ? 'Deleting...' : 'Delete'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Desktop Table View */}
      <div className="hidden md:block bg-white shadow rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th scope="col" className="px-6 py-3 text-left">
                  <input
                    type="checkbox"
                    checked={filteredServices.length > 0 && selectedServices.size === filteredServices.length}
                    onChange={handleSelectAll}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Name
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Category
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Price
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Description
                </th>
                <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {loading && filteredServices.length === 0 ? (
                <tr>
                  <td colSpan={6} className="px-6 py-4 text-center text-sm text-gray-500">
                    Loading services...
                  </td>
                </tr>
              ) : filteredServices.length === 0 ? (
                <tr>
                  <td colSpan={6} className="px-6 py-4 text-center text-sm text-gray-500">
                    {searchTerm ? 'No services match your search' : 'No services found'}
                  </td>
                </tr>
              ) : (
                filteredServices.map((service) => (
                  <tr key={service.id} className={`hover:bg-gray-50 ${selectedServices.has(service.id) ? 'bg-blue-50' : ''}`}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <input
                        type="checkbox"
                        checked={selectedServices.has(service.id)}
                        onChange={() => handleSelectService(service.id)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {service.name}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {service.category}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      KES {service.price.toLocaleString()}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-500 max-w-xs truncate">
                      {service.description || '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button
                        onClick={() => handleOpenForm(service)}
                        className="text-blue-600 hover:text-blue-900 mr-3"
                        title="Edit service"
                      >
                        <PencilIcon className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleDelete(service)}
                        className="text-red-600 hover:text-red-900"
                        title="Delete service"
                      >
                        <TrashIcon className="h-4 w-4" />
                      </button>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Mobile Card View */}
      <div className="md:hidden space-y-4">
        {filteredServices.length > 0 && (
          <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={filteredServices.length > 0 && selectedServices.size === filteredServices.length}
                onChange={handleSelectAll}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2"
              />
              <span className="text-sm text-gray-700">Select All</span>
            </label>
            {selectedServices.size > 0 && (
              <span className="text-sm font-medium text-blue-600">
                {selectedServices.size} selected
              </span>
            )}
          </div>
        )}
        
        {loading && filteredServices.length === 0 ? (
          <div className="text-center py-4 text-gray-500">Loading services...</div>
        ) : filteredServices.length === 0 ? (
          <div className="text-center py-4 text-gray-500">
            {searchTerm ? 'No services match your search' : 'No services found'}
          </div>
        ) : (
          filteredServices.map((service) => (
            <div key={service.id} className={`relative ${selectedServices.has(service.id) ? 'ring-2 ring-blue-500' : ''}`}>
              {/* Selection checkbox overlay */}
              <div className="absolute top-3 left-3 z-10">
                <input
                  type="checkbox"
                  checked={selectedServices.has(service.id)}
                  onChange={() => handleSelectService(service.id)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
              </div>
              
              <AdminCard
                title={service.name}
                icon={<ShoppingBagIcon className="h-5 w-5" />}
                className={selectedServices.has(service.id) ? 'bg-blue-50' : ''}
                actions={
                  <div className="flex space-x-2">
                    <button
                      onClick={() => handleOpenForm(service)}
                      className="p-1.5 text-blue-600 hover:text-blue-900 hover:bg-blue-50 rounded-full"
                      aria-label="Edit service"
                    >
                      <PencilIcon className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => handleDelete(service)}
                      className="p-1.5 text-red-600 hover:text-red-900 hover:bg-red-50 rounded-full"
                      aria-label="Delete service"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </button>
                  </div>
                }
              >
                <AdminCardList
                  items={[
                    { label: "Category", value: service.category },
                    { label: "Price", value: `KES ${service.price.toLocaleString()}`, valueClassName: "font-medium" },
                    { label: "Description", value: service.description || "-" }
                  ]}
                />
              </AdminCard>
            </div>
          ))
        )}
      </div>
    </div>
  );
}
