'use client'

import { useEffect } from 'react'
import { signOut } from 'next-auth/react'
import { useRouter } from 'next/navigation'

export default function LogoutPage() {
  const router = useRouter()

  useEffect(() => {
    const handleLogout = async () => {
      try {
        await signOut({
          redirect: false,
          callbackUrl: '/admin/login'
        })
        router.push('/admin/login')
      } catch (error) {
        console.error('Logout error:', error)
        // Redirect anyway in case of error
        router.push('/admin/login')
      }
    }

    handleLogout()
  }, [router])

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">Signing Out</h3>
        <p className="text-sm text-gray-600">
          Please wait while we log you out...
        </p>
      </div>
    </div>
  )
} 