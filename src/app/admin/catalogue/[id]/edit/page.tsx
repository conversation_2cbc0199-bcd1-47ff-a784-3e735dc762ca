'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeftIcon, PlusIcon } from '@heroicons/react/24/outline';
import Link from 'next/link';
import MultiImageUpload from '@/components/admin/MultiImageUpload';
import { useCatalogueCategories } from '@/hooks/useCatalogueCategories';

interface CatalogueParams {
  params: Promise<{
    id: string;
  }>;
}

interface FormData {
  service: string;
  price: string;
  designFee: string;
  description: string;
  popular: boolean;
  features: string[];
  category: string;
}

export default function EditCataloguePage({ params }: CatalogueParams) {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(true);
  const [catalogueId, setCatalogueId] = useState<string>('');
  const { categories, loading: categoriesLoading } = useCatalogueCategories();
  const [formData, setFormData] = useState<FormData>({
    service: '',
    price: '',
    designFee: '',
    description: '',
    popular: false,
    features: [],
    category: 'Other'
  });
  const [images, setImages] = useState<{ url: string; isFeatured: boolean }[]>([]);
  const [newFeature, setNewFeature] = useState('');

  // Get catalogue ID from params
  useEffect(() => {
    const getParams = async () => {
      const resolvedParams = await params;
      setCatalogueId(resolvedParams.id);
    };
    getParams();
  }, [params]);

  // Fetch existing catalogue data
  useEffect(() => {
    if (!catalogueId) return;

    const fetchCatalogue = async () => {
      try {
        const response = await fetch(`/api/admin/catalogue-v3/${catalogueId}`);
        if (!response.ok) {
          throw new Error('Failed to fetch catalogue item');
        }

        const result = await response.json();
        // Extract data from API response structure
        const data = result.success && result.data ? result.data : result;

          setFormData({
          service: data.service || '',
          price: data.price?.toString() || '',
          designFee: data.designFee?.toString() || '',
            description: data.description || '',
            popular: data.popular || false,
          features: data.features || [],
            category: data.category || 'Other'
          });

        // Set up images
        const existingImages: { url: string; isFeatured: boolean }[] = [];
        if (data.imageUrl) {
          existingImages.push({ url: data.imageUrl, isFeatured: true });
        }
        if (data.imageUrl2) {
          existingImages.push({ url: data.imageUrl2, isFeatured: false });
        }
        if (data.imageUrl3) {
          existingImages.push({ url: data.imageUrl3, isFeatured: false });
        }
        setImages(existingImages);

      } catch (error) {
        console.error('Error fetching catalogue:', error);
        alert('Failed to load catalogue item');
        router.push('/admin/catalogue');
      } finally {
        setInitialLoading(false);
      }
    };

    fetchCatalogue();
  }, [catalogueId, router]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));
  };

  const addFeature = () => {
    if (newFeature.trim() && !formData.features.includes(newFeature.trim())) {
      setFormData(prev => ({
        ...prev,
        features: [...prev.features, newFeature.trim()]
      }));
      setNewFeature('');
    }
  };

  const removeFeature = (index: number) => {
    setFormData(prev => ({
      ...prev,
      features: prev.features.filter((_, i) => i !== index)
    }));
  };

  const handleImagesChange = useCallback((newImages: { url: string; isFeatured: boolean }[]) => {
    setImages(newImages);
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.service.trim()) {
      alert('Please enter a service name');
      return;
    }

    if (!formData.price || parseFloat(formData.price) < 0) {
      alert('Please enter a valid print price (can be 0 for meter-based pricing)');
      return;
    }

    if (!formData.designFee || parseFloat(formData.designFee) < 0) {
      alert('Please enter a valid design fee (cannot be negative)');
      return;
    }

    setLoading(true);

    try {
      // Prepare the data for submission
      const featuredImage = images.find(img => img.isFeatured);
      const otherImages = images.filter(img => !img.isFeatured);
      
      const catalogueData = {
        service: formData.service.trim(),
        price: parseFloat(formData.price),
        designFee: parseFloat(formData.designFee),
        description: formData.description.trim(),
        category: formData.category,

        popular: formData.popular,
        features: formData.features,
        imageUrl: featuredImage?.url || images[0]?.url || null,
        imageUrl2: otherImages[0]?.url || null,
        imageUrl3: otherImages[1]?.url || null
      };

      console.log('Updating catalogue data:', catalogueData);

      const response = await fetch(`/api/admin/catalogue-v3/${catalogueId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(catalogueData),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      console.log('Catalogue updated successfully:', result);

      // Redirect to catalogue list
      router.push('/admin/catalogue');
      
    } catch (error) {
      console.error('Error updating catalogue item:', error);
      alert(error instanceof Error ? error.message : 'Failed to update catalogue item');
    } finally {
      setLoading(false);
    }
  };

  if (initialLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading catalogue item...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-4 mb-4">
        <Link
          href="/admin/catalogue"
              className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700"
        >
          <ArrowLeftIcon className="h-4 w-4 mr-1" />
          Back to Catalogue
        </Link>
      </div>
          <h1 className="text-3xl font-bold text-gray-900">Edit Service</h1>
          <p className="mt-2 text-gray-600">Update your service information</p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Basic Information */}
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-6">Basic Information</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
                <label htmlFor="service" className="block text-sm font-medium text-gray-700 mb-2">
                  Service Name *
            </label>
            <input
              type="text"
                  id="service"
              name="service"
                  value={formData.service}
                  onChange={handleInputChange}
              required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="e.g., Logo Design"
            />
          </div>

          <div>
                <label htmlFor="price" className="block text-sm font-medium text-gray-700 mb-2">
                  Base Print Price (KES) *
            </label>
            <input
              type="number"
                  id="price"
              name="price"
                  value={formData.price}
                  onChange={handleInputChange}
              required
                  min="0"
                  step="0.01"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="0.00"
            />
                <p className="mt-1 text-xs text-gray-500">Base price for quantity-based products (use 0 for meter-based products)</p>
          </div>

          <div>
                <label htmlFor="designFee" className="block text-sm font-medium text-gray-700 mb-2">
                  Design Fee (KES) *
            </label>
            <input
              type="number"
                  id="designFee"
              name="designFee"
                  value={formData.designFee}
                  onChange={handleInputChange}
              required
                  min="0"
                  step="0.01"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="0.00"
            />
                <p className="mt-1 text-xs text-gray-500">Fee for design-only services</p>
          </div>

          <div>
                <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-2">
              Category
            </label>
            <select
              id="category"
              name="category"
              value={formData.category}
                  onChange={handleInputChange}
                  disabled={categoriesLoading}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
                  {categoriesLoading ? (
                    <option value="">Loading categories...</option>
                  ) : (
                    categories.map((category) => (
                      <option key={category} value={category}>
                        {category}
                      </option>
                    ))
                  )}
            </select>
                {categoriesLoading && (
                  <p className="mt-1 text-xs text-gray-500">Loading available categories...</p>
                )}
          </div>


            </div>

            <div className="mt-6">
              <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                Description
              </label>
              <textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Describe your service..."
              />
            </div>

            <div className="mt-6">
              <label className="flex items-center">
                  <input
                  type="checkbox"
                  name="popular"
                  checked={formData.popular}
                  onChange={handleInputChange}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <span className="ml-2 text-sm text-gray-700">Mark as popular service</span>
                  </label>
            </div>
          </div>

          {/* Features */}
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-6">Features</h2>
            
            <div className="flex space-x-2 mb-4">
              <input
                type="text"
                value={newFeature}
                onChange={(e) => setNewFeature(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addFeature())}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Add a feature..."
              />
              <button
                type="button"
                onClick={addFeature}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <PlusIcon className="h-4 w-4" />
              </button>
            </div>

            {formData.features.length > 0 && (
            <div className="space-y-2">
              {formData.features.map((feature, index) => (
                  <div key={index} className="flex items-center justify-between bg-gray-50 px-3 py-2 rounded-md">
                    <span className="text-sm text-gray-700">{feature}</span>
                    <button
                      type="button"
                      onClick={() => removeFeature(index)}
                      className="text-red-600 hover:text-red-800 text-sm"
                    >
                      Remove
                    </button>
                </div>
              ))}
            </div>
            )}
          </div>

          {/* Images */}
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-6">Images</h2>
            <p className="text-sm text-gray-600 mb-4">
              Upload images for your service. Click the star to set a featured image.
            </p>
            
            <MultiImageUpload
              maxImages={10}
              maxFileSize={50}
              onImagesChange={handleImagesChange}
              initialImages={images}
              category="catalogue"
            />
          </div>

          {/* Submit Button */}
          <div className="flex justify-end space-x-4">
            <Link
              href="/admin/catalogue"
              className="px-6 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Cancel
            </Link>
            <button
              type="submit"
              disabled={loading}
              className="px-6 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Updating...' : 'Update Service'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}