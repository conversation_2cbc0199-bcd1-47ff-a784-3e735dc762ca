'use client';

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  ArrowLeftIcon,
  PencilIcon,
  TrashIcon,
  TagIcon,
  CurrencyDollarIcon,
  PhotoIcon,
  DocumentTextIcon,
  StarIcon
} from '@heroicons/react/24/outline';
import { useNotification } from '@/contexts/NotificationContext';

interface CatalogueItem {
  id: string;
  service: string;
  price: number;
  designFee?: number;
  description: string;
  features?: string[];

  popular?: boolean;
  imageUrl?: string;
  imageUrl2?: string;
  imageUrl3?: string;
  category?: string;
  createdAt: string;
  updatedAt: string;
}

export default function CatalogueViewPage() {
  const params = useParams();
  const router = useRouter();
  const { showNotification } = useNotification();
  const [item, setItem] = useState<CatalogueItem | null>(null);
  const [loading, setLoading] = useState(true);
  const [deleting, setDeleting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const itemId = params?.id as string;

  useEffect(() => {
    if (itemId) {
      fetchCatalogueItem();
    }
  }, [itemId]);

  const fetchCatalogueItem = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/admin/catalogue-v3/${itemId}`, {
        credentials: 'include',
      });

      if (!response.ok) {
        if (response.status === 401) {
          // Authentication required - redirect to login
          router.push('/admin/login');
          return;
        }
        throw new Error('Failed to fetch catalogue item');
      }

      const result = await response.json();
      if (result.success && result.data) {
        setItem(result.data);
      } else if (result && !result.success) {
        // Handle API error response
        throw new Error(result.message || result.error || 'Catalogue item not found');
      } else {
        // Handle direct data response (fallback)
        setItem(result);
      }
    } catch (error) {
      console.error('Error fetching catalogue item:', error);
      setError(error instanceof Error ? error.message : 'Failed to load catalogue item');
      showNotification('error', 'Failed to load catalogue item');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async () => {
    if (!item) return;

    if (!window.confirm(`Are you sure you want to delete "${item.service}"? This action cannot be undone.`)) {
      return;
    }

    try {
      setDeleting(true);

      const response = await fetch(`/api/admin/catalogue-v3/${itemId}`, {
        method: 'DELETE',
        credentials: 'include',
      });

      if (!response.ok) {
        if (response.status === 401) {
          // Authentication required - redirect to login
          router.push('/admin/login');
          return;
        }
        throw new Error('Failed to delete catalogue item');
      }

      showNotification('success', 'Catalogue item deleted successfully');
      router.push('/admin/catalogue?refresh=true');
    } catch (error) {
      console.error('Error deleting catalogue item:', error);
      showNotification('error', 'Failed to delete catalogue item');
    } finally {
      setDeleting(false);
    }
  };

  const formatPrice = (price: number): string => {
    return price.toLocaleString('en-KE', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    });
  };

  const formatDate = (dateString: string): string => {
    try {
      return new Date(dateString).toLocaleDateString('en-KE', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch {
      return 'Invalid Date';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-orange-600"></div>
      </div>
    );
  }

  if (error || !item) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Link
            href="/admin/catalogue"
            className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-1" />
            Back to Catalogue
          </Link>
        </div>

        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error Loading Catalogue Item</h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{error || 'Catalogue item not found'}</p>
              </div>
              <div className="mt-4">
                <button
                  onClick={fetchCatalogueItem}
                  className="bg-red-100 px-3 py-2 rounded-md text-sm font-medium text-red-800 hover:bg-red-200"
                >
                  Try Again
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  const images = [item.imageUrl, item.imageUrl2, item.imageUrl3].filter(Boolean);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link
            href="/admin/catalogue"
            className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-1" />
            Back to Catalogue
          </Link>
          <div className="h-4 border-l border-gray-300"></div>
          <h1 className="text-2xl font-bold text-gray-900">Service Details</h1>
        </div>

        <div className="flex space-x-3">
          <Link
            href={`/admin/catalogue/${itemId}/edit`}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
          >
            <PencilIcon className="h-4 w-4 mr-2" />
            Edit
          </Link>
          <button
            onClick={handleDelete}
            disabled={deleting}
            className="inline-flex items-center px-4 py-2 border border-red-300 rounded-md shadow-sm text-sm font-medium text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
          >
            <TrashIcon className="h-4 w-4 mr-2" />
            {deleting ? 'Deleting...' : 'Delete'}
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Left Column - Main Details */}
        <div className="lg:col-span-2 space-y-6">
          {/* Service Info Card */}
          <div className="bg-white shadow rounded-lg overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h2 className="text-lg font-medium text-gray-900">Service Information</h2>
                {item.popular && (
                  <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-orange-100 text-orange-800">
                    <StarIcon className="h-4 w-4 mr-1" />
                    Popular
                  </span>
                )}
              </div>
            </div>
            <div className="px-6 py-4 space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-500 mb-1">Service Name</label>
                <p className="text-lg font-semibold text-gray-900">{item.service}</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-500 mb-1">
                    <CurrencyDollarIcon className="h-4 w-4 inline mr-1" />
                    Print Price
                  </label>
                  <p className="text-xl font-bold text-green-600">KES {formatPrice(item.price)}</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-500 mb-1">
                    <CurrencyDollarIcon className="h-4 w-4 inline mr-1" />
                    Design Fee
                  </label>
                  <p className="text-xl font-bold text-blue-600">KES {formatPrice(item.designFee || 0)}</p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-500 mb-1">
                    <TagIcon className="h-4 w-4 inline mr-1" />
                    Category
                  </label>
                  <p className="text-lg text-gray-900">{item.category || 'Other'}</p>
                </div>
              </div>

              {item.description && (
                <div>
                  <label className="block text-sm font-medium text-gray-500 mb-1">
                    <DocumentTextIcon className="h-4 w-4 inline mr-1" />
                    Description
                  </label>
                  <div className="prose prose-sm max-w-none">
                    <p className="text-gray-900 whitespace-pre-wrap">{item.description}</p>
                  </div>
                </div>
              )}

              {item.features && item.features.length > 0 && (
                <div>
                  <label className="block text-sm font-medium text-gray-500 mb-2">Features</label>
                  <ul className="list-disc list-inside space-y-1">
                    {item.features.map((feature, index) => (
                      <li key={index} className="text-gray-900">{feature}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </div>

          {/* Images Card */}
          {images.length > 0 && (
            <div className="bg-white shadow rounded-lg overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200">
                <h2 className="text-lg font-medium text-gray-900 flex items-center">
                  <PhotoIcon className="h-5 w-5 mr-2" />
                  Images ({images.length})
                </h2>
              </div>
              <div className="px-6 py-4">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {images.map((imageUrl, index) => (
                    <div key={index} className="aspect-square rounded-lg overflow-hidden bg-gray-100">
                      <img
                        src={imageUrl}
                        alt={`${item.service} - Image ${index + 1}`}
                        className="w-full h-full object-cover hover:scale-105 transition-transform duration-200 cursor-pointer"
                        onClick={() => window.open(imageUrl, '_blank')}
                      />
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Right Column - Metadata */}
        <div className="space-y-6">
          {/* Quick Actions */}
          <div className="bg-white shadow rounded-lg overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">Quick Actions</h2>
            </div>
            <div className="px-6 py-4 space-y-3">
              <Link
                href={`/admin/catalogue/${itemId}/edit`}
                className="w-full inline-flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                <PencilIcon className="h-4 w-4 mr-2" />
                Edit Service
              </Link>
              <button
                onClick={handleDelete}
                disabled={deleting}
                className="w-full inline-flex items-center justify-center px-4 py-2 border border-red-300 rounded-md shadow-sm text-sm font-medium text-red-700 bg-white hover:bg-red-50 disabled:opacity-50"
              >
                <TrashIcon className="h-4 w-4 mr-2" />
                {deleting ? 'Deleting...' : 'Delete Service'}
              </button>
            </div>
          </div>

          {/* Metadata */}
          <div className="bg-white shadow rounded-lg overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">Metadata</h2>
            </div>
            <div className="px-6 py-4 space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-500 mb-1">Service ID</label>
                <p className="text-sm font-mono text-gray-900 bg-gray-50 px-2 py-1 rounded">{item.id}</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-500 mb-1">Created</label>
                <p className="text-sm text-gray-900">{formatDate(item.createdAt)}</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-500 mb-1">Last Updated</label>
                <p className="text-sm text-gray-900">{formatDate(item.updatedAt)}</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-500 mb-1">Status</label>
                <div className="flex items-center space-x-2">
                  <div className="h-2 w-2 bg-green-400 rounded-full"></div>
                  <span className="text-sm text-gray-900">Published</span>
                </div>
              </div>
            </div>
          </div>

          {/* Statistics */}
          <div className="bg-white shadow rounded-lg overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">Statistics</h2>
            </div>
            <div className="px-6 py-4 space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-500">Images</span>
                <span className="text-sm font-medium text-gray-900">{images.length}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-500">Features</span>
                <span className="text-sm font-medium text-gray-900">{item.features?.length || 0}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-500">Popular</span>
                <span className={`text-sm font-medium ${item.popular ? 'text-green-600' : 'text-gray-400'}`}>
                  {item.popular ? 'Yes' : 'No'}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 