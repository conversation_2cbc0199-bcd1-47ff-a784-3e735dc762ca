'use client';

import React, { useState, useEffect } from 'react';
import {
  ArrowPathIcon,
  PlusIcon,
  MagnifyingGlassIcon,
  TrashIcon,
  XMarkIcon,
  FunnelIcon,
  EyeIcon,
  PencilIcon
} from '@heroicons/react/24/outline';
import Link from 'next/link';
import { useSearchParams, useRouter } from 'next/navigation';

interface CatalogueItem {
  id: string;
  service: string;
  price: number;
  designFee?: number;
  description: string;
  features?: string[];

  popular?: boolean;
  imageUrl?: string;
  imageUrl2?: string;
  imageUrl3?: string;
  category?: string;
  createdAt: string;
  updatedAt: string;
}

export default function CataloguePage() {
  const [loading, setLoading] = useState(false);
  const [catalogueItems, setCatalogueItems] = useState<CatalogueItem[]>([]);
  const [filteredItems, setFilteredItems] = useState<CatalogueItem[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('All');
  const [categories, setCategories] = useState<string[]>(['All']);
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [bulkDeleteLoading, setBulkDeleteLoading] = useState(false);
  const [deleting, setDeleting] = useState<string | null>(null);
  
  // Total items count
  const [totalItems, setTotalItems] = useState(0);

  // Get search params to detect when coming back from edit page
  const searchParams = useSearchParams();
  const refreshParam = searchParams?.get('refresh');
  const router = useRouter();

  const fetchCatalogueItems = async (search?: string, category?: string) => {
    const actualSearch = search !== undefined ? search : searchTerm;
    const actualCategory = category || selectedCategory;
    setLoading(true);
    try {
      const timestamp = Date.now();
      
      // Build query parameters - fetch ALL items (no pagination)
      const params = new URLSearchParams({
        limit: '1000', // High limit to get all items
        offset: '0',
        t: timestamp.toString()
      });
      
      if (actualSearch.trim()) {
        params.append('search', actualSearch.trim());
      }
      
      if (actualCategory !== 'All') {
        params.append('category', actualCategory);
      }
      
      const response = await fetch(`/api/admin/catalogue-v3?${params.toString()}`, {
        credentials: 'include',
      });
      
      if (response.ok) {
        const result = await response.json();
        const data = result.success ? result.data : [];
        
        setCatalogueItems(data);
        setFilteredItems(data);
        setTotalItems(data.length);

        // Extract unique categories from all data
        const uniqueCategories = Array.from(
          new Set((data as CatalogueItem[]).map((item: CatalogueItem) => item.category || 'Other'))
        ).sort();
        setCategories(['All', ...uniqueCategories]);
      } else if (response.status === 401) {
        // Authentication required - redirect to login
        router.push('/admin/login');
        return;
      }
    } catch (error) {
      console.error('Error fetching catalogue items:', error);
    } finally {
      setLoading(false);
    }
  };

  // Handle search input change with debouncing
  const [searchDebounce, setSearchDebounce] = useState<NodeJS.Timeout | null>(null);
  
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);
    
    // Clear existing debounce
    if (searchDebounce) {
      clearTimeout(searchDebounce);
    }
    
    // Set new debounce
    const newDebounce = setTimeout(() => {
      fetchCatalogueItems(value, selectedCategory);
    }, 500);
    
    setSearchDebounce(newDebounce);
  };
  
  // Handle category change
  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category);
    fetchCatalogueItems(searchTerm, category);
  };

  // Handle bulk delete
  const handleBulkDelete = async () => {
    if (selectedItems.length === 0) return;

    if (window.confirm(`Are you sure you want to delete ${selectedItems.length} selected catalogue items?`)) {
      setBulkDeleteLoading(true);
      try {
        const bulkDeleteResponse = await fetch('/api/admin/catalogue-v3', {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify({ ids: selectedItems }),
        });

        if (bulkDeleteResponse.ok) {
          const result = await bulkDeleteResponse.json();
          const deletedCount = result.success ? (result.data as any)?.deletedCount || 0 : 0;
          alert(`Successfully deleted ${deletedCount} catalogue items`);
          setSelectedItems([]);
          fetchCatalogueItems(searchTerm, selectedCategory);
        } else if (bulkDeleteResponse.status === 401) {
          // Authentication required - redirect to login
          router.push('/admin/login');
          return;
        } else {
          const error = await bulkDeleteResponse.json();
          alert(`Error: ${error.error || 'Failed to delete catalogue items'}`);
        }
      } catch (error) {
        console.error('Error bulk deleting catalogue items:', error);
        alert('An error occurred while deleting catalogue items');
      } finally {
        setBulkDeleteLoading(false);
      }
    }
  };

  // Handle individual delete
  const handleDeleteItem = async (itemId: string, itemName: string) => {
    if (!window.confirm(`Are you sure you want to delete "${itemName}"? This action cannot be undone.`)) {
      return;
    }

    const originalItems = [...catalogueItems];
    const originalFiltered = [...filteredItems];

    try {
      setDeleting(itemId);
      // Optimistically remove from UI
      setCatalogueItems(prev => prev.filter(item => item.id !== itemId));
      setFilteredItems(prev => prev.filter(item => item.id !== itemId));
      // Remove from selected items if it was selected
      if (selectedItems.includes(itemId)) {
        setSelectedItems(prev => prev.filter(id => id !== itemId));
      }

      const response = await fetch(`/api/admin/catalogue-v3/${itemId}`, {
        method: 'DELETE',
        credentials: 'include',
      });

      if (!response.ok) {
        if (response.status === 401) {
          // Authentication required - redirect to login
          router.push('/admin/login');
          return;
        }
        throw new Error('Failed to delete catalogue item');
      }

      alert('Catalogue item deleted successfully');
    } catch (error) {
      console.error('Error deleting catalogue item:', error);
      // Restore original state on error
      setCatalogueItems(originalItems);
      setFilteredItems(originalFiltered);
      alert('Failed to delete catalogue item');
    } finally {
      setDeleting(null);
    }
  };

  // Toggle item selection
  const toggleItemSelection = (id: string) => {
    setSelectedItems(prev =>
      prev.includes(id)
        ? prev.filter(itemId => itemId !== id)
        : [...prev, id]
    );
  };

  // Toggle all items selection
  const toggleAllSelection = () => {
    if (selectedItems.length === filteredItems.length) {
      // If all are selected, deselect all
      setSelectedItems([]);
    } else {
      // Otherwise, select all filtered items
      setSelectedItems(filteredItems.map(item => item.id));
    }
  };

  // Format price
  const formatPrice = (price: number): string => {
    return price.toLocaleString('en-KE', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    });
  };

  useEffect(() => {
    fetchCatalogueItems('', 'All');
    // When refreshParam changes, refetch catalogue items
  }, [refreshParam]);

  if (loading && catalogueItems.length === 0) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-orange-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-4 sm:space-y-6">
      {/* Page Header */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
            <div>
              <h1 className="text-xl sm:text-2xl font-bold text-gray-900">Service Catalogue</h1>
              <p className="text-sm sm:text-base text-gray-600 mt-1">
                Manage your service offerings and pricing
              </p>
            </div>
            <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
              <button
                onClick={() => {
                  setSearchTerm('');
                  setSelectedCategory('All');
                  fetchCatalogueItems('', 'All');
                }}
                disabled={loading}
                className="inline-flex items-center justify-center px-3 sm:px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
              >
                <ArrowPathIcon className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                Refresh
              </button>
              <Link
                href="/admin/catalogue/new"
                className="inline-flex items-center justify-center px-3 sm:px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
              >
                <PlusIcon className="h-4 w-4 mr-2" />
                Add Service
              </Link>
            </div>
          </div>
        </div>

        {/* Summary Stats */}
        <div className="px-4 sm:px-6 py-4 bg-gray-50 border-b border-gray-200">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3 sm:gap-4">
            <div className="text-center">
              <div className="text-lg sm:text-2xl font-bold text-orange-600">{catalogueItems.length}</div>
              <div className="text-xs sm:text-sm text-gray-600">Total Services</div>
            </div>
            <div className="text-center">
              <div className="text-lg sm:text-2xl font-bold text-green-600">
                {catalogueItems.filter(item => item.popular).length}
              </div>
              <div className="text-xs sm:text-sm text-gray-600">Popular</div>
            </div>
            <div className="text-center">
              <div className="text-lg sm:text-2xl font-bold text-blue-600">
                {categories.length - 1}
              </div>
              <div className="text-xs sm:text-sm text-gray-600">Categories</div>
            </div>
            <div className="text-center">
              <div className="text-lg sm:text-2xl font-bold text-gray-900">
                KES {formatPrice(catalogueItems.reduce((sum, item) => sum + item.price, 0))}
              </div>
              <div className="text-xs sm:text-sm text-gray-600">Total Value</div>
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filter Section */}
      <div className="bg-white rounded-lg shadow-sm p-4">
        <div className="flex flex-col md:flex-row gap-4 mb-4">
          {/* Search Bar */}
          <div className="relative flex-grow">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              value={searchTerm}
              onChange={handleSearchChange}
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
              placeholder="Search by name, description, price..."
            />
            {searchTerm && (
              <button
                onClick={() => setSearchTerm('')}
                className="absolute inset-y-0 right-0 pr-3 flex items-center"
              >
                <XMarkIcon className="h-5 w-5 text-gray-400 hover:text-gray-600" />
              </button>
            )}
          </div>

          {/* Category Filter */}
          <div className="relative min-w-[200px]">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <FunnelIcon className="h-5 w-5 text-gray-400" />
            </div>
            <select
              value={selectedCategory}
              onChange={(e) => handleCategoryChange(e.target.value)}
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
            >
              {categories.map((category) => (
                <option key={category} value={category}>
                  {category === 'All' ? 'All Categories' : category}
                </option>
              ))}
            </select>
          </div>

          {/* Bulk Delete Button */}
          {selectedItems.length > 0 && (
            <button
              onClick={handleBulkDelete}
              disabled={bulkDeleteLoading}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-500 hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
            >
              <TrashIcon className="h-4 w-4 mr-1" />
              Delete Selected ({selectedItems.length})
            </button>
          )}
        </div>

        {/* Filter Status & Pagination Info */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between text-sm text-gray-500">
          <div>
            {filteredItems.length === 0 && totalItems > 0 ? (
              <p>No items match your search criteria. <button onClick={() => { setSearchTerm(''); setSelectedCategory('All'); fetchCatalogueItems('', 'All'); }} className="text-orange-500 hover:text-orange-600">Clear filters</button></p>
            ) : (
              <p>Showing all {totalItems.toLocaleString()} catalogue items</p>
            )}
          </div>
        </div>
      </div>

      {/* Catalogue Items Display */}
      <div className="bg-white shadow rounded-lg overflow-hidden">
        {catalogueItems.length === 0 && !loading ? (
          <div className="px-4 sm:px-6 py-12 text-center text-gray-500">
            <div className="mx-auto h-12 w-12 text-gray-300 mb-4">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={1.5}
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M12 6v12m-3-2.818l.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            </div>
            <p className="text-base font-medium">No catalogue items found</p>
            <p className="text-sm mt-2">
              <Link
                href="/admin/catalogue/new"
                className="text-orange-600 hover:text-orange-500"
              >
                Create your first service
              </Link>
            </p>
          </div>
        ) : filteredItems.length === 0 ? (
          <div className="px-4 sm:px-6 py-12 text-center text-gray-500">
            <MagnifyingGlassIcon className="mx-auto h-12 w-12 text-gray-300 mb-4" />
            <p className="text-base font-medium">No matching items</p>
            <p className="text-sm mt-2">
              Try adjusting your search or filter to find what you're looking for
            </p>
                          <button
                onClick={() => { setSearchTerm(''); setSelectedCategory('All'); fetchCatalogueItems('', 'All'); }}
                className="mt-4 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-500 hover:bg-orange-600"
              >
                Clear Filters
              </button>
          </div>
        ) : (
          <>
            {/* Mobile Card View */}
            <div className="block sm:hidden divide-y divide-gray-200">
              {filteredItems.map((item) => (
                <div key={item.id} className="px-4 py-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <input
                        type="checkbox"
                        className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"
                        checked={selectedItems.includes(item.id)}
                        onChange={() => toggleItemSelection(item.id)}
                      />
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {item.service}
                        </p>
                        <p className="text-xs text-gray-500">
                          {item.category || 'Other'}
                        </p>
                      </div>
                    </div>
                    <div className="ml-3">
                      {item.popular && (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                          Popular
                        </span>
                      )}
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-3 gap-3 mb-3">
                    <div>
                      <p className="text-xs text-gray-500">Print Price</p>
                      <p className="text-sm font-medium text-gray-900">KES {formatPrice(item.price)}</p>
                    </div>
                    <div>
                      <p className="text-xs text-gray-500">Design Fee</p>
                      <p className="text-sm font-medium text-blue-600">KES {formatPrice(item.designFee || 0)}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-xs text-gray-500">Images</p>
                      <p className="text-sm text-gray-900">
                        {[item.imageUrl, item.imageUrl2, item.imageUrl3].filter(Boolean).length || 0}
                      </p>
                    </div>
                  </div>
                  
                  {item.description && (
                    <div className="mb-3">
                      <p className="text-xs text-gray-500">Description</p>
                      <p className="text-sm text-gray-900 truncate">{item.description}</p>
                    </div>
                  )}
                  
                  <div className="flex flex-wrap gap-2">
                    <Link
                      href={`/admin/catalogue/${item.id}`}
                      className="inline-flex items-center px-2.5 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
                    >
                      <EyeIcon className="h-3 w-3 mr-1" />
                      View
                    </Link>
                    <Link
                      href={`/admin/catalogue/${item.id}/edit`}
                      className="inline-flex items-center px-2.5 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
                    >
                      <PencilIcon className="h-3 w-3 mr-1" />
                      Edit
                    </Link>
                    <button
                      onClick={() => handleDeleteItem(item.id, item.service)}
                      disabled={deleting === item.id}
                      className="inline-flex items-center px-2.5 py-1.5 border border-red-300 shadow-sm text-xs font-medium rounded text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
                    >
                      <TrashIcon className="h-3 w-3 mr-1" />
                      {deleting === item.id ? 'Deleting...' : 'Delete'}
                    </button>
                  </div>
                </div>
              ))}
            </div>

            {/* Desktop Table View */}
            <div className="hidden sm:block overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 table-fixed">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="w-12 px-3 py-3 text-left text-xs font-medium text-gray-500">
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"
                          checked={selectedItems.length === filteredItems.length && filteredItems.length > 0}
                          onChange={toggleAllSelection}
                        />
                      </div>
                    </th>
                    <th className="w-1/3 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Service
                    </th>
                    <th className="w-24 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Print Price (KSh)
                    </th>
                    <th className="w-24 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Design Fee (KSh)
                    </th>
                    <th className="w-32 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Image
                    </th>
                    <th className="w-48 px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredItems.map((item) => (
                    <tr key={item.id} className={`hover:bg-gray-50 ${selectedItems.includes(item.id) ? 'bg-orange-50' : ''}`}>
                      <td className="px-3 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"
                            checked={selectedItems.includes(item.id)}
                            onChange={() => toggleItemSelection(item.id)}
                          />
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="max-w-xs">
                          <div className="text-sm font-medium text-gray-900 truncate" title={item.service}>
                            {item.service}
                            {item.popular && (
                              <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                                Popular
                              </span>
                            )}
                          </div>
                          <div className="text-sm text-gray-500 truncate">
                            {item.category || 'Other'}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          KES {formatPrice(item.price)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-blue-600">
                          KES {formatPrice(item.designFee || 0)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {item.imageUrl || item.imageUrl2 || item.imageUrl3 ? (
                          <div className="flex space-x-1">
                            {item.imageUrl && (
                              <div className="h-10 w-10 rounded overflow-hidden bg-gray-100">
                                <img
                                  src={item.imageUrl}
                                  alt={`${item.service} - primary`}
                                  className="h-full w-full object-cover"
                                  title="Primary image"
                                />
                              </div>
                            )}
                            {item.imageUrl2 && (
                              <div className="h-10 w-10 rounded overflow-hidden bg-gray-100">
                                <img
                                  src={item.imageUrl2}
                                  alt={`${item.service} - secondary`}
                                  className="h-full w-full object-cover"
                                  title="Secondary image"
                                />
                              </div>
                            )}
                            {item.imageUrl3 && (
                              <div className="h-10 w-10 rounded overflow-hidden bg-gray-100">
                                <img
                                  src={item.imageUrl3}
                                  alt={`${item.service} - tertiary`}
                                  className="h-full w-full object-cover"
                                  title="Tertiary image"
                                />
                              </div>
                            )}
                          </div>
                        ) : (
                          <span className="text-gray-400 text-sm">No images</span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex justify-end gap-1 min-w-max">
                          <Link
                            href={`/admin/catalogue/${item.id}`}
                            className="inline-flex items-center px-2 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
                            title="View details"
                          >
                            <EyeIcon className="h-3 w-3 mr-1" />
                            View
                          </Link>
                          <Link
                            href={`/admin/catalogue/${item.id}/edit`}
                            className="inline-flex items-center px-2 py-1 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
                            title="Edit item"
                          >
                            <PencilIcon className="h-3 w-3 mr-1" />
                            Edit
                          </Link>
                          <button
                            onClick={() => handleDeleteItem(item.id, item.service)}
                            disabled={deleting === item.id}
                            className="inline-flex items-center px-2 py-1 border border-red-300 shadow-sm text-xs font-medium rounded text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
                            title="Delete item"
                          >
                            <TrashIcon className="h-3 w-3 mr-1" />
                            {deleting === item.id ? 'Del...' : 'Delete'}
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

          </>
        )}
      </div>
    </div>
  );
}