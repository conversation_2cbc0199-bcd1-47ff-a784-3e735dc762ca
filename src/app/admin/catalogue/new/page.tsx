'use client';

import React, { useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeftIcon, PlusIcon } from '@heroicons/react/24/outline';
import Link from 'next/link';
import EnhancedMultiImageUpload from '@/components/admin/EnhancedMultiImageUpload';
import { useCatalogueCategories } from '@/hooks/useCatalogueCategories';
import { useUnits } from '@/hooks/useUnits';
import CategoryManager from '@/components/admin/CategoryManager';

interface FormData {
  service: string;
  price: string;
  designFee: string;
  description: string;
  popular: boolean;
  features: string[];
  category: string;
  unitId: string;
  pricingType: string;
  pricePerMeter: string;
  minQuantity: string;
  maxQuantity: string;
  minMeters: string;
  maxMeters: string;
}

export default function NewCataloguePage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const { categories, loading: categoriesLoading, refetch: refetchCategories } = useCatalogueCategories();
  const { units, loading: unitsLoading } = useUnits();
  const [localCategories, setLocalCategories] = useState<string[]>([]);
  const [formData, setFormData] = useState<FormData>({
    service: '',
    price: '',
    designFee: '',
    description: '',
    popular: false,
    features: [],
    category: 'Other',
    unitId: '',
    pricingType: 'fixed',
    pricePerMeter: '',
    minQuantity: '1',
    maxQuantity: '',
    minMeters: '',
    maxMeters: ''
  });
  const [images, setImages] = useState<{ url: string; isFeatured: boolean }[]>([]);
  const [newFeature, setNewFeature] = useState('');

  // Update local categories when categories change
  React.useEffect(() => {
    setLocalCategories(categories);
  }, [categories]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));
  };

  const addFeature = () => {
    if (newFeature.trim() && !formData.features.includes(newFeature.trim())) {
      setFormData(prev => ({
        ...prev,
        features: [...prev.features, newFeature.trim()]
      }));
      setNewFeature('');
    }
  };

  const removeFeature = (index: number) => {
    setFormData(prev => ({
      ...prev,
      features: prev.features.filter((_, i) => i !== index)
    }));
  };

  const handleImagesChange = useCallback((newImages: { url: string; isFeatured: boolean }[]) => {
    setImages(newImages);
  }, []);

  const handleCategoryChange = (category: string) => {
    setFormData(prev => ({
      ...prev,
      category
    }));
  };

  const handleCategoryAdded = (newCategory: string) => {
    setLocalCategories(prev => [...prev, newCategory]);
    // Optionally refetch categories from server
    refetchCategories();
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.service.trim()) {
      alert('Please enter a service name');
      return;
    }

    if (!formData.price || parseFloat(formData.price) <= 0) {
      alert('Please enter a valid print price');
      return;
    }

    if (!formData.designFee || parseFloat(formData.designFee) <= 0) {
      alert('Please enter a valid design fee');
      return;
    }

    if (!formData.unitId) {
      alert('Please select a unit type');
      return;
    }

    if (formData.pricingType === 'per_meter' && (!formData.pricePerMeter || parseFloat(formData.pricePerMeter) <= 0)) {
      alert('Please enter a valid price per unit for meter-based pricing');
      return;
    }

    if (images.length === 0) {
      alert('Please upload at least one image');
      return;
    }

    setLoading(true);

    try {
      // Prepare the data for submission
      const featuredImage = images.find(img => img.isFeatured);
      const otherImages = images.filter(img => !img.isFeatured);
      
      const catalogueData = {
        service: formData.service.trim(),
        price: parseFloat(formData.price),
        designFee: parseFloat(formData.designFee),
        description: formData.description.trim(),
        category: formData.category,
        popular: formData.popular,
        features: formData.features,
        imageUrl: featuredImage?.url || images[0]?.url || null,
        imageUrl2: otherImages[0]?.url || null,
        imageUrl3: otherImages[1]?.url || null,
        // Unit and pricing configuration
        unitId: formData.unitId,
        pricingType: formData.pricingType,
        pricePerMeter: formData.pricingType === 'per_meter' ? parseFloat(formData.pricePerMeter) : null,
        minQuantity: formData.minQuantity ? parseInt(formData.minQuantity) : 1,
        maxQuantity: formData.maxQuantity ? parseInt(formData.maxQuantity) : null,
        minMeters: formData.minMeters ? parseFloat(formData.minMeters) : null,
        maxMeters: formData.maxMeters ? parseFloat(formData.maxMeters) : null
      };

      console.log('Submitting catalogue data:', catalogueData);

      const response = await fetch('/api/admin/catalogue-v3', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(catalogueData),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      console.log('Catalogue created successfully:', result);

      // Redirect to catalogue list
      router.push('/admin/catalogue');
      
    } catch (error) {
      console.error('Error creating catalogue item:', error);
      alert(error instanceof Error ? error.message : 'Failed to create catalogue item');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-4 mb-4">
            <Link
              href="/admin/catalogue"
              className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700"
            >
              <ArrowLeftIcon className="h-4 w-4 mr-1" />
              Back to Catalogue
            </Link>
          </div>
          <h1 className="text-3xl font-bold text-gray-900">Add New Service</h1>
          <p className="mt-2 text-gray-600">Create a new service for your catalogue</p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Basic Information */}
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-6">Basic Information</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label htmlFor="service" className="block text-sm font-medium text-gray-700 mb-2">
                  Service Name *
                </label>
                <input
                  type="text"
                  id="service"
                  name="service"
                  value={formData.service}
                  onChange={handleInputChange}
                  required
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="e.g., Logo Design"
                />
              </div>

              <div>
                <label htmlFor="price" className="block text-sm font-medium text-gray-700 mb-2">
                  Print Price (KES) *
                </label>
                <input
                  type="number"
                  id="price"
                  name="price"
                  value={formData.price}
                  onChange={handleInputChange}
                  required
                  min="0"
                  step="0.01"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="0.00"
                />
                <p className="mt-1 text-xs text-gray-500">Price for printing/production services</p>
              </div>

              <div>
                <label htmlFor="designFee" className="block text-sm font-medium text-gray-700 mb-2">
                  Design Fee (KES) *
                </label>
                <input
                  type="number"
                  id="designFee"
                  name="designFee"
                  value={formData.designFee}
                  onChange={handleInputChange}
                  required
                  min="0"
                  step="0.01"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="0.00"
                />
                <p className="mt-1 text-xs text-gray-500">Fee for design-only services</p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Category
                </label>
                <CategoryManager
                  categories={localCategories}
                  selectedCategory={formData.category}
                  onCategoryChange={handleCategoryChange}
                  onCategoryAdded={handleCategoryAdded}
                  disabled={categoriesLoading}
                  className="w-full"
                />
                {categoriesLoading && (
                  <p className="mt-1 text-xs text-gray-500">Loading available categories...</p>
                )}
              </div>


            </div>

            {/* Pricing Configuration */}
            <div className="mt-6 pt-6 border-t border-gray-200">
              <h3 className="text-md font-medium text-gray-900 mb-4">Pricing Configuration</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="pricingType" className="block text-sm font-medium text-gray-700 mb-2">
                    Pricing Type *
                  </label>
                  <select
                    id="pricingType"
                    name="pricingType"
                    value={formData.pricingType}
                    onChange={handleInputChange}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="fixed">Fixed Price (per copy/item)</option>
                    <option value="per_meter">Per Meter/Unit</option>
                  </select>
                  <p className="mt-1 text-xs text-gray-500">
                    Choose how pricing is calculated for this service
                  </p>
                </div>

                <div>
                  <label htmlFor="unitId" className="block text-sm font-medium text-gray-700 mb-2">
                    Unit Type *
                  </label>
                  <select
                    id="unitId"
                    name="unitId"
                    value={formData.unitId}
                    onChange={handleInputChange}
                    required
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    disabled={unitsLoading}
                  >
                    <option value="">Select a unit...</option>
                    {units.map((unit) => (
                      <option key={unit.id} value={unit.id}>
                        {unit.displayName} ({unit.plural})
                        {unit.shortForm && ` - ${unit.shortForm}`}
                      </option>
                    ))}
                  </select>
                  <p className="mt-1 text-xs text-gray-500">
                    {unitsLoading ? 'Loading units...' : 'Select the unit for pricing (e.g., per copy, per meter)'}
                  </p>
                </div>

                {formData.pricingType === 'per_meter' && (
                  <>
                    <div>
                      <label htmlFor="pricePerMeter" className="block text-sm font-medium text-gray-700 mb-2">
                        Price Per Unit (KES)
                      </label>
                      <input
                        type="number"
                        id="pricePerMeter"
                        name="pricePerMeter"
                        value={formData.pricePerMeter}
                        onChange={handleInputChange}
                        min="0"
                        step="0.01"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="0.00"
                      />
                      <p className="mt-1 text-xs text-gray-500">
                        Price per unit (meter, square foot, etc.)
                      </p>
                    </div>

                    <div className="grid grid-cols-2 gap-3">
                      <div>
                        <label htmlFor="minMeters" className="block text-sm font-medium text-gray-700 mb-2">
                          Min Units
                        </label>
                        <input
                          type="number"
                          id="minMeters"
                          name="minMeters"
                          value={formData.minMeters}
                          onChange={handleInputChange}
                          min="0"
                          step="0.1"
                          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="0.5"
                        />
                      </div>
                      <div>
                        <label htmlFor="maxMeters" className="block text-sm font-medium text-gray-700 mb-2">
                          Max Units
                        </label>
                        <input
                          type="number"
                          id="maxMeters"
                          name="maxMeters"
                          value={formData.maxMeters}
                          onChange={handleInputChange}
                          min="0"
                          step="0.1"
                          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                          placeholder="100"
                        />
                      </div>
                    </div>
                  </>
                )}

                {formData.pricingType === 'fixed' && (
                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <label htmlFor="minQuantity" className="block text-sm font-medium text-gray-700 mb-2">
                        Min Quantity
                      </label>
                      <input
                        type="number"
                        id="minQuantity"
                        name="minQuantity"
                        value={formData.minQuantity}
                        onChange={handleInputChange}
                        min="1"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="1"
                      />
                    </div>
                    <div>
                      <label htmlFor="maxQuantity" className="block text-sm font-medium text-gray-700 mb-2">
                        Max Quantity
                      </label>
                      <input
                        type="number"
                        id="maxQuantity"
                        name="maxQuantity"
                        value={formData.maxQuantity}
                        onChange={handleInputChange}
                        min="1"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="10000"
                      />
                    </div>
                  </div>
                )}
              </div>
            </div>

            <div className="mt-6">
              <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                Description
              </label>
              <textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Describe your service..."
              />
            </div>

            <div className="mt-6">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  name="popular"
                  checked={formData.popular}
                  onChange={handleInputChange}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <span className="ml-2 text-sm text-gray-700">Mark as popular service</span>
              </label>
            </div>
          </div>

          {/* Features */}
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-6">Features</h2>
            
            <div className="flex space-x-2 mb-4">
              <input
                type="text"
                value={newFeature}
                onChange={(e) => setNewFeature(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addFeature())}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Add a feature..."
              />
              <button
                type="button"
                onClick={addFeature}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <PlusIcon className="h-4 w-4" />
              </button>
            </div>

            {formData.features.length > 0 && (
              <div className="space-y-2">
                {formData.features.map((feature, index) => (
                  <div key={index} className="flex items-center justify-between bg-gray-50 px-3 py-2 rounded-md">
                    <span className="text-sm text-gray-700">{feature}</span>
                    <button
                      type="button"
                      onClick={() => removeFeature(index)}
                      className="text-red-600 hover:text-red-800 text-sm"
                    >
                      Remove
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Images */}
          <div className="bg-white shadow rounded-lg p-6">
            <h2 className="text-lg font-medium text-gray-900 mb-6">Images</h2>
            <p className="text-sm text-gray-600 mb-4">
              Upload images for your service. Click the star to set a featured image.
            </p>
            
            <EnhancedMultiImageUpload
              maxImages={10}
              maxFileSize={200}
              onImagesChange={handleImagesChange}
              category="catalogue"
            />
          </div>

          {/* Submit Button */}
          <div className="flex justify-end space-x-4">
            <Link
              href="/admin/catalogue"
              className="px-6 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Cancel
            </Link>
            <button
              type="submit"
              disabled={loading}
              className="px-6 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Creating...' : 'Create Service'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
