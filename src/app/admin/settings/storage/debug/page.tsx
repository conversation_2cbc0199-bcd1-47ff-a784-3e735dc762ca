'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { ArrowPathIcon, CloudIcon } from '@heroicons/react/24/outline';

export default function StorageDebugPage() {
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [initializing, setInitializing] = useState(false);
  const [initSuccess, setInitSuccess] = useState<string | null>(null);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);
      setInitSuccess(null);
      const response = await fetch('/api/admin/settings/storage/debug');

      if (!response.ok) {
        throw new Error(`Error ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      setData(result);
    } catch (err) {
      console.error('Error fetching debug data:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    } finally {
      setLoading(false);
    }
  };

  const initializeFromEnv = async () => {
    try {
      setInitializing(true);
      setError(null);
      setInitSuccess(null);

      const response = await fetch('/api/admin/settings/storage/init-from-env', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to initialize from environment variables');
      }

      const result = await response.json();
      setInitSuccess('Storage configuration initialized successfully!');

      // Refresh the debug data
      await fetchData();
    } catch (err) {
      console.error('Error initializing from env:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    } finally {
      setInitializing(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  return (
    <div className="p-6 max-w-4xl mx-auto bg-white rounded-lg shadow-sm">
      <div className="mb-6 flex justify-between items-center">
        <h1 className="text-2xl font-bold text-slate-800">Storage Configs Debug</h1>
        <div className="flex space-x-2">
          <button
            onClick={fetchData}
            className="p-2 rounded-lg text-slate-500 hover:text-slate-700 hover:bg-slate-100"
            disabled={loading}
            title="Refresh debug data"
          >
            <ArrowPathIcon className={`h-5 w-5 ${loading ? 'animate-spin' : ''}`} />
          </button>
          <Link
            href="/admin/settings/storage"
            className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
          >
            Back to Storage Settings
          </Link>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border-l-4 border-red-500 p-4 my-4">
          <p className="text-red-700">Error: {error}</p>
        </div>
      )}

      {initSuccess && (
        <div className="bg-green-50 border-l-4 border-green-500 p-4 my-4">
          <p className="text-green-700">{initSuccess}</p>
        </div>
      )}

      {data?.envVarsAvailable && data?.configs?.length === 0 && (
        <div className="bg-blue-50 border-l-4 border-blue-500 p-4 my-4 flex justify-between items-center">
          <p className="text-blue-700">Environment variables are available but no storage configuration exists. Initialize from environment variables?</p>
          <button
            onClick={initializeFromEnv}
            disabled={initializing}
            className="ml-4 bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 flex items-center"
          >
            {initializing ? (
              <>
                <div className="animate-spin h-4 w-4 border-2 border-white border-t-transparent rounded-full mr-2"></div>
                Initializing...
              </>
            ) : (
              <>
                <CloudIcon className="h-4 w-4 mr-2" />
                Initialize
              </>
            )}
          </button>
        </div>
      )}

      {loading ? (
        <div className="text-center py-10">
          <div className="animate-spin h-10 w-10 border-4 border-blue-500 border-t-transparent rounded-full mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading data...</p>
        </div>
      ) : !error && (
        <div>
          <div className="bg-blue-50 p-4 mb-6 rounded">
            <h2 className="text-lg font-semibold mb-2">Debug Information</h2>
            <p>Timestamp: {data?.debug?.timestamp}</p>
            <p>Environment: {data?.debug?.environment}</p>
            <p>Total Configs: {data?.count}</p>
            <p>Database Error: {data?.dbError ? <span className="text-red-600">{data.dbError}</span> : <span className="text-green-600">None</span>}</p>
            <p>Environment Variables Available: {data?.envVarsAvailable ? <span className="text-green-600">Yes</span> : <span className="text-red-600">No</span>}</p>
            <p>Prisma Client Initialized: {data?.debug?.prismaClientInitialized ? <span className="text-green-600">Yes</span> : <span className="text-red-600">No</span>}</p>
            <p>StorageConfig Model Exists: {data?.debug?.storageConfigModelExists ? <span className="text-green-600">Yes</span> : <span className="text-red-600">No</span>}</p>
            <p>Database URL: {data?.debug?.databaseUrl || 'Not available'}</p>
          </div>

          {data?.configs?.length === 0 ? (
            <div className="text-center py-8 bg-gray-50 rounded">
              <p className="text-gray-600">No storage configurations found in the database.</p>
            </div>
          ) : (
            <div>
              <h2 className="text-lg font-semibold mb-4">Storage Configurations</h2>
              {data?.configs?.map((config: any) => (
                <div key={config.id} className="border rounded-lg p-4 mb-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <p><span className="font-medium">ID:</span> {config.id}</p>
                      <p><span className="font-medium">Provider:</span> {config.provider}</p>
                      <p><span className="font-medium">Region:</span> {config.region}</p>
                      <p><span className="font-medium">Endpoint:</span> {config.endpoint}</p>
                    </div>
                    <div>
                      <p><span className="font-medium">Bucket:</span> {config.bucketName}</p>
                      <p><span className="font-medium">Access Key:</span> {config.accessKeyId.substring(0, 4)}...</p>
                      <p><span className="font-medium">Secret Key:</span> {"*".repeat(10)}</p>
                      <p><span className="font-medium">Default:</span> {config.isDefault ? "Yes" : "No"}</p>
                    </div>
                  </div>
                  <div className="mt-2">
                    <p><span className="font-medium">Created:</span> {new Date(config.createdAt).toLocaleString()}</p>
                    <p><span className="font-medium">Updated:</span> {new Date(config.updatedAt).toLocaleString()}</p>
                  </div>
                </div>
              ))}
            </div>
          )}

          <div className="mt-6">
            <h3 className="text-lg font-semibold mb-2">Raw Data</h3>
            <pre className="bg-gray-100 p-4 rounded-lg overflow-auto max-h-96">
              {JSON.stringify(data, null, 2)}
            </pre>
          </div>
        </div>
      )}
    </div>
  );
}