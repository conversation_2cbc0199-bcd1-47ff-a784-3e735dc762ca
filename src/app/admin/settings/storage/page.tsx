'use client';

import React, { useState, useEffect } from 'react';
import {
  ArrowPathIcon,
  PlusIcon,
  TrashIcon,
  CheckIcon,
  ExclamationCircleIcon,
  PencilIcon,
  ServerIcon,
  CloudIcon,
  InformationCircleIcon,
  ShieldCheckIcon,
  Cog6ToothIcon,
  CodeBracketIcon
} from '@heroicons/react/24/outline';
import { CheckCircleIcon } from '@heroicons/react/24/solid';
import { StorageConfig } from '@/lib/storageConfig';
import Link from 'next/link';
import { Tooltip } from '@/components/ui/Tooltip';

export default function StorageSettingsPage() {
  const [configs, setConfigs] = useState<StorageConfig[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [editingId, setEditingId] = useState<string | null>(null);
  const [showForm, setShowForm] = useState(false);

  const [formData, setFormData] = useState({
    provider: 'S3' as 'S3' | 'LOCAL' | 'LINODE' | 'CLOUDINARY',
    region: '',
    endpoint: '',
    bucketName: '',
    accessKeyId: '',
    secretAccessKey: '',
    isDefault: false
  });

  const fetchConfigs = async () => {
    try {
      setLoading(true);
      setError('');

      // Add cache-busting parameter to prevent browser caching
      const timestamp = new Date().getTime();
      const res = await fetch(`/api/admin/settings/storage?t=${timestamp}`, {
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      });

      if (!res.ok) {
        throw new Error('Failed to fetch storage configurations');
      }

      const data = await res.json();
      setConfigs(data);
    } catch (err) {
      console.error('Error fetching storage configs:', err);
      setError('Failed to load storage configurations. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchConfigs();
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target as HTMLInputElement;

    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setSaving(true);
      setError('');
      setSuccess('');

      // Add cache-busting parameter to prevent browser caching
      const timestamp = new Date().getTime();
      const baseUrl = editingId
        ? `/api/admin/settings/storage/${editingId}`
        : '/api/admin/settings/storage';

      const url = `${baseUrl}?t=${timestamp}`;
      const method = editingId ? 'PUT' : 'POST';

      const res = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        },
        body: JSON.stringify(formData)
      });

      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.error || 'Failed to save storage configuration');
      }

      setSuccess('Storage configuration saved successfully!');
      await fetchConfigs();

      // Clear form
      resetForm();

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess('');
      }, 3000);
    } catch (err: any) {
      console.error('Error saving storage config:', err);
      setError(err.message || 'Failed to save storage configuration. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const deleteConfig = async (id: string) => {
    if (!confirm('Are you sure you want to delete this storage configuration?')) {
      return;
    }

    try {
      setError('');

      // Add cache-busting parameter to prevent browser caching
      const timestamp = new Date().getTime();
      const res = await fetch(`/api/admin/settings/storage/${id}?t=${timestamp}`, {
        method: 'DELETE',
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      });

      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.error || 'Failed to delete storage configuration');
      }

      setSuccess('Storage configuration deleted successfully!');
      await fetchConfigs();

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess('');
      }, 3000);
    } catch (err: any) {
      console.error('Error deleting storage config:', err);
      setError(err.message || 'Failed to delete storage configuration. Please try again.');
    }
  };

  const setDefaultConfig = async (id: string) => {
    try {
      setError('');

      // Add cache-busting parameter to prevent browser caching
      const timestamp = new Date().getTime();
      const res = await fetch(`/api/admin/settings/storage/${id}/default?t=${timestamp}`, {
        method: 'PUT',
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        }
      });

      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.error || 'Failed to set default storage configuration');
      }

      setSuccess('Default storage configuration updated successfully!');
      await fetchConfigs();

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess('');
      }, 3000);
    } catch (err: any) {
      console.error('Error setting default storage config:', err);
      setError(err.message || 'Failed to set default storage configuration. Please try again.');
    }
  };

  const editConfig = (config: StorageConfig) => {
    setFormData({
      provider: config.provider as 'S3' | 'LOCAL' | 'LINODE' | 'CLOUDINARY',
      region: config.region,
      endpoint: config.endpoint,
      bucketName: config.bucketName,
              accessKeyId: config.accessKey,
        secretAccessKey: config.secretKey,
      isDefault: config.isDefault
    });

    setEditingId(config.id);
    setShowForm(true);

    // Scroll to form
    setTimeout(() => {
      document.getElementById('storage-form')?.scrollIntoView({ behavior: 'smooth' });
    }, 100);
  };

  const resetForm = () => {
    setFormData({
      provider: 'S3',
      region: '',
      endpoint: '',
      bucketName: '',
      accessKeyId: '',
      secretAccessKey: '',
      isDefault: false
    });

    setEditingId(null);
    setShowForm(false);
  };

  const initializeFromEnv = async () => {
    try {
      setLoading(true);
      setError('');

      // Add cache-busting parameter to prevent browser caching
      const timestamp = new Date().getTime();
      const res = await fetch(`/api/admin/settings/storage/init-from-env?t=${timestamp}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        },
        credentials: 'include', // Include cookies for session authentication
      });

      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.error || 'Failed to initialize from environment variables');
      }

      setSuccess('Storage configuration initialized from environment variables');
      await fetchConfigs();

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccess('');
      }, 3000);
    } catch (err: any) {
      console.error('Error initializing from env:', err);
      setError(err.message || 'Failed to initialize from environment variables. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-slate-800">Storage Settings</h1>
          <p className="text-slate-600 mt-1">Configure your file storage providers and buckets</p>
        </div>

        <div className="flex flex-col sm:flex-row gap-2 sm:gap-3">
          <button
            onClick={() => {
              setShowForm(!showForm);
              setEditingId(null);
              resetForm();
            }}
            className="flex items-center justify-center px-4 py-2 text-orange-600 bg-orange-50 rounded-lg hover:bg-orange-100 transition-colors min-h-[44px] sm:min-h-[40px]"
          >
            <PlusIcon className="h-5 w-5 mr-1" />
            {showForm ? 'Cancel' : 'Add New'}
          </button>
        </div>
      </div>

      {/* Settings Navigation */}
      <div className="bg-white shadow-sm rounded-lg p-3 sm:p-4 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
        <Link href="/admin/settings" className="p-3 sm:p-4 border rounded-lg hover:bg-orange-50 hover:border-orange-200 transition-colors">
          <div className="flex items-start">
            <Cog6ToothIcon className="h-6 w-6 text-orange-500 mr-3 mt-0.5 flex-shrink-0" />
            <div className="min-w-0 flex-1">
              <h3 className="font-medium text-slate-800 text-sm sm:text-base">General Settings</h3>
              <p className="text-xs sm:text-sm text-slate-500 mt-1 line-clamp-2">Configure basic site information and contact details</p>
            </div>
          </div>
        </Link>
        <Link href="/admin/settings/storage" className="p-3 sm:p-4 border rounded-lg bg-orange-50 border-orange-200 transition-colors">
          <div className="flex items-start">
            <ServerIcon className="h-6 w-6 text-orange-500 mr-3 mt-0.5 flex-shrink-0" />
            <div className="min-w-0 flex-1">
              <h3 className="font-medium text-slate-800 text-sm sm:text-base">Storage Settings</h3>
              <p className="text-xs sm:text-sm text-slate-500 mt-1 line-clamp-2">Configure file storage providers and buckets</p>
            </div>
          </div>
        </Link>
        <Link href="/admin/settings/cache" className="p-3 sm:p-4 border rounded-lg hover:bg-orange-50 hover:border-orange-200 transition-colors">
          <div className="flex items-start">
            <ArrowPathIcon className="h-6 w-6 text-orange-500 mr-3 mt-0.5 flex-shrink-0" />
            <div className="min-w-0 flex-1">
              <h3 className="font-medium text-slate-800 text-sm sm:text-base">Cache Management</h3>
              <p className="text-xs sm:text-sm text-slate-500 mt-1 line-clamp-2">Manage cache settings and clear cached data</p>
            </div>
          </div>
        </Link>
        <Link href="/admin/settings/scripts" className="p-3 sm:p-4 border rounded-lg hover:bg-orange-50 hover:border-orange-200 transition-colors">
          <div className="flex items-start">
            <CodeBracketIcon className="h-6 w-6 text-orange-500 mr-3 mt-0.5 flex-shrink-0" />
            <div className="min-w-0 flex-1">
              <h3 className="font-medium text-slate-800 text-sm sm:text-base">Site Scripts</h3>
              <p className="text-xs sm:text-sm text-slate-500 mt-1 line-clamp-2">Manage custom scripts for analytics, tracking, and more</p>
            </div>
          </div>
        </Link>
      </div>

      <div className="bg-white p-3 sm:p-4 rounded-lg shadow-sm mb-6">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          <div className="flex items-center">
            <ServerIcon className="h-5 w-5 text-slate-500 mr-2" />
            <span className="text-sm font-medium text-slate-700">Storage Provider Tools</span>
          </div>

          <div className="flex flex-col sm:flex-row gap-2 sm:gap-3">
            <Link
              href="/admin/settings/storage/debug"
              className="flex items-center justify-center px-3 py-2 text-sm text-blue-600 bg-blue-50 rounded-md hover:bg-blue-100 transition-colors min-h-[44px] sm:min-h-[36px]"
            >
              <InformationCircleIcon className="h-4 w-4 mr-1" />
              View Debug Info
            </Link>

            <Link
              href="/admin/settings/storage/test-endpoint"
              className="flex items-center justify-center px-3 py-2 text-sm text-purple-600 bg-purple-50 rounded-md hover:bg-purple-100 transition-colors min-h-[44px] sm:min-h-[36px]"
            >
              <ShieldCheckIcon className="h-4 w-4 mr-1" />
              Test API Endpoints
            </Link>

            <button
              onClick={initializeFromEnv}
              className="flex items-center justify-center px-3 py-2 text-sm text-green-600 bg-green-50 rounded-md hover:bg-green-100 transition-colors min-h-[44px] sm:min-h-[36px]"
              disabled={loading}
            >
              <CloudIcon className="h-4 w-4 mr-1" />
              Initialize from .env
            </button>
          </div>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border-l-4 border-red-400 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <ExclamationCircleIcon className="h-5 w-5 text-red-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700">{error}</p>
            </div>
          </div>
        </div>
      )}

      {success && (
        <div className="bg-green-50 border-l-4 border-green-400 p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <CheckIcon className="h-5 w-5 text-green-400" />
            </div>
            <div className="ml-3">
              <p className="text-sm text-green-700">{success}</p>
            </div>
          </div>
        </div>
      )}

      {showForm && (
        <div id="storage-form" className="bg-white shadow-sm rounded-lg overflow-hidden border border-gray-200">
          <div className="px-4 py-4 sm:px-6 sm:py-5 bg-gradient-to-r from-orange-50 to-white border-b border-gray-200">
            <div className="flex items-start">
              <ServerIcon className="h-6 w-6 text-orange-500 mr-3 mt-0.5 flex-shrink-0" />
              <div className="min-w-0 flex-1">
                <h3 className="text-base sm:text-lg font-medium leading-6 text-gray-900">
                  {editingId ? 'Edit Storage Configuration' : 'Add New Storage Configuration'}
                </h3>
                <p className="mt-1 text-sm text-gray-500">
                  Configure your file storage provider settings for portfolio images and uploads.
                </p>
              </div>
            </div>
          </div>

          <div className="px-4 py-4 sm:px-6 sm:py-6">
            <form onSubmit={handleSubmit} className="space-y-5">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="provider" className="block text-sm font-medium text-gray-700 mb-1">
                    Provider Type
                  </label>
                  <select
                    id="provider"
                    name="provider"
                    value={formData.provider}
                    onChange={handleChange}
                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500 text-sm sm:text-base min-h-[44px] sm:min-h-[40px]"
                    required
                  >
                    <option value="S3">Amazon S3 Compatible</option>
                    <option value="LINODE">Linode Object Storage</option>
                    <option value="CLOUDINARY">Cloudinary</option>
                    <option value="LOCAL">Local Storage</option>
                  </select>
                  <p className="mt-1 text-xs text-gray-500">Select your storage provider type</p>
                </div>

                <div>
                  <label htmlFor="bucketName" className="block text-sm font-medium text-gray-700 mb-1">
                    Bucket Name
                  </label>
                  <input
                    type="text"
                    id="bucketName"
                    name="bucketName"
                    value={formData.bucketName}
                    onChange={handleChange}
                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500 text-sm sm:text-base min-h-[44px] sm:min-h-[40px]"
                    placeholder="e.g. my-website-files"
                    required
                  />
                  <p className="mt-1 text-xs text-gray-500">The name of your storage bucket</p>
                </div>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="region" className="block text-sm font-medium text-gray-700 mb-1">
                    Region
                  </label>
                  <input
                    type="text"
                    id="region"
                    name="region"
                    value={formData.region}
                    onChange={handleChange}
                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500 text-sm sm:text-base min-h-[44px] sm:min-h-[40px]"
                    placeholder="e.g. us-east-1, fr-par-1"
                    required
                  />
                  <p className="mt-1 text-xs text-gray-500">The region where your bucket is located</p>
                </div>

                <div>
                  <label htmlFor="endpoint" className="block text-sm font-medium text-gray-700 mb-1">
                    Endpoint URL
                  </label>
                  <input
                    type="url"
                    id="endpoint"
                    name="endpoint"
                    value={formData.endpoint}
                    onChange={handleChange}
                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500 text-sm sm:text-base min-h-[44px] sm:min-h-[40px]"
                    placeholder="e.g. https://fr-par-1.linodeobjects.com"
                    required
                  />
                  <p className="mt-1 text-xs text-gray-500">The endpoint URL for your storage provider</p>
                </div>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="accessKeyId" className="block text-sm font-medium text-gray-700 mb-1">
                    Access Key ID
                  </label>
                  <input
                    type="text"
                    id="accessKeyId"
                    name="accessKeyId"
                    value={formData.accessKeyId}
                    onChange={handleChange}
                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500 text-sm sm:text-base min-h-[44px] sm:min-h-[40px]"
                    required
                  />
                  <p className="mt-1 text-xs text-gray-500">Your storage provider access key</p>
                </div>

                <div>
                  <label htmlFor="secretAccessKey" className="block text-sm font-medium text-gray-700 mb-1">
                    Secret Access Key
                  </label>
                  <input
                    type="password"
                    id="secretAccessKey"
                    name="secretAccessKey"
                    value={formData.secretAccessKey}
                    onChange={handleChange}
                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500 text-sm sm:text-base min-h-[44px] sm:min-h-[40px]"
                    required
                  />
                  <p className="mt-1 text-xs text-gray-500">Your storage provider secret key</p>
                </div>
              </div>

              <div className="mt-4 pt-4 border-t border-gray-200">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="isDefault"
                    name="isDefault"
                    checked={formData.isDefault}
                    onChange={handleChange}
                    className="h-5 w-5 rounded border-gray-300 text-orange-600 focus:ring-orange-500"
                  />
                  <label htmlFor="isDefault" className="ml-2 block text-sm font-medium text-gray-700">
                    Set as default storage provider
                  </label>
                </div>
                <p className="mt-1 text-xs text-gray-500 ml-7">This provider will be used for all file uploads and retrievals</p>
              </div>

              <div className="flex flex-col sm:flex-row sm:justify-end gap-3 sm:gap-3 pt-6 border-t border-gray-200 mt-6">
                <button
                  type="button"
                  onClick={resetForm}
                  className="w-full sm:w-auto px-6 py-3 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 min-h-[44px]"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={saving}
                  className="w-full sm:w-auto inline-flex justify-center items-center px-6 py-3 border border-transparent text-sm font-medium rounded-md text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 shadow-sm min-h-[44px]"
                >
                  {saving ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Saving...
                    </>
                  ) : (
                    <>{editingId ? 'Update Configuration' : 'Save Configuration'}</>
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {loading ? (
        <div className="text-center py-12 bg-white rounded-lg shadow-sm">
          <svg className="animate-spin h-8 w-8 text-orange-500 mx-auto" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <p className="mt-2 text-sm text-slate-500">Loading storage configurations...</p>
        </div>
      ) : configs.length === 0 ? (
        <div className="bg-white shadow-sm rounded-lg p-8 text-center">
          <ServerIcon className="h-12 w-12 text-slate-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-slate-700 mb-2">No Storage Configurations</h3>
          <p className="text-gray-500 mb-6 max-w-md mx-auto">No storage configurations found. Add a new one to get started or initialize from environment variables.</p>
          <button
            onClick={initializeFromEnv}
            className="px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 shadow-sm flex items-center mx-auto"
            disabled={loading}
          >
            <CloudIcon className="h-5 w-5 mr-2" />
            Initialize from Environment Variables
          </button>
        </div>
      ) : (
        <div className="overflow-hidden bg-white shadow-sm rounded-lg border border-gray-200">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gradient-to-r from-gray-50 to-white">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Provider
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Bucket
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Region
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Default
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {configs.map((config) => (
                  <tr key={config.id} className={config.isDefault ? 'bg-orange-50' : 'hover:bg-gray-50'}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        {config.provider === 'S3' && (
                          <CloudIcon className="h-5 w-5 text-amber-500 mr-2" />
                        )}
                        {config.provider === 'LINODE' && (
                          <ServerIcon className="h-5 w-5 text-blue-500 mr-2" />
                        )}
                        {config.provider === 'CLOUDINARY' && (
                          <CloudIcon className="h-5 w-5 text-purple-500 mr-2" />
                        )}
                        {config.provider === 'LOCAL' && (
                          <ServerIcon className="h-5 w-5 text-gray-500 mr-2" />
                        )}
                        <span className="text-sm font-medium text-gray-900">{config.provider}</span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <Tooltip content={`Bucket: ${config.bucketName}`}>
                        <span className="text-sm text-gray-500">{config.bucketName}</span>
                      </Tooltip>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <Tooltip content={`Region: ${config.region}`}>
                        <span className="text-sm text-gray-500">{config.region}</span>
                      </Tooltip>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {config.isDefault ? (
                        <div className="flex items-center">
                          <CheckCircleIcon className="h-5 w-5 text-green-500 mr-1" />
                          <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                            Default
                          </span>
                        </div>
                      ) : (
                        <button
                          onClick={() => setDefaultConfig(config.id)}
                          className="text-xs bg-blue-50 hover:bg-blue-100 text-blue-600 px-2 py-1 rounded-md transition-colors"
                        >
                          Set as default
                        </button>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end space-x-2">
                        <Tooltip content="Edit configuration">
                          <button
                            onClick={() => editConfig(config)}
                            className="p-1 rounded-md text-orange-600 hover:text-orange-900 hover:bg-orange-50"
                          >
                            <PencilIcon className="h-5 w-5" />
                          </button>
                        </Tooltip>

                        {!config.isDefault && (
                          <Tooltip content="Delete configuration">
                            <button
                              onClick={() => deleteConfig(config.id)}
                              className="p-1 rounded-md text-red-600 hover:text-red-900 hover:bg-red-50"
                            >
                              <TrashIcon className="h-5 w-5" />
                            </button>
                          </Tooltip>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
}