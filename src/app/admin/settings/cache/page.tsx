'use client';

import React, { useState, useEffect, useCallback } from 'react';
import {
  ArrowPathIcon,
  TrashIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  PhotoIcon,
  FolderIcon,
  ServerIcon,
  CloudIcon,
  ChartBarIcon,
  ClockIcon,
  FireIcon,
  CpuChipIcon,
  EyeIcon,
  WrenchScrewdriverIcon,
  Cog6ToothIcon,
  CodeBracketIcon,
} from '@heroicons/react/24/outline';
import { toast } from 'react-hot-toast';
import Link from 'next/link';

interface CacheStatus {
  redis: {
    connected: boolean;
    keys: number;
    memory?: string;
    hitRatio?: number;
  };
  memory: {
    logos: boolean;
    portfolio: boolean;
    images: boolean;
  };
  lastCleared: string | null;
  performance?: {
    averageResponseTime: number;
    totalRequests: number;
    cacheHits: number;
    cacheMisses: number;
  };
  size?: {
    total: string;
    logos: string;
    portfolio: string;
    images: string;
  };
}

interface CacheOperation {
  type: string;
  name: string;
  description: string;
  icon: any;
  color: string;
  priority: 'low' | 'medium' | 'high';
  estimatedTime?: string;
}

const cacheOperations: CacheOperation[] = [
  {
    type: 'logos',
    name: 'Logo Cache',
    description: 'Clear cached logo images, portfolio metadata, and refresh logos page (97 logos in portfolio)',
    icon: PhotoIcon,
    color: 'blue',
    priority: 'medium',
    estimatedTime: '~30s'
  },
  {
    type: 'portfolio',
    name: 'Portfolio Cache',
    description: 'Clear all portfolio items, S3 metadata cache, and refresh portfolio/logos pages (includes all categories)',
    icon: FolderIcon,
    color: 'green',
    priority: 'high',
    estimatedTime: '~45s'
  },
  {
    type: 'images',
    name: 'Image Cache',
    description: 'Clear all cached images, thumbnails, and refresh image-related pages',
    icon: PhotoIcon,
    color: 'purple',
    priority: 'medium',
    estimatedTime: '~60s'
  },
  {
    type: 'redis',
    name: 'Redis Cache',
    description: 'Clear all Redis cache entries (does not refresh pages)',
    icon: ServerIcon,
    color: 'red',
    priority: 'low',
    estimatedTime: '~15s'
  }
];

export default function CacheManagementPage() {
  const [loading, setLoading] = useState(false);
  const [cacheStatus, setCacheStatus] = useState<CacheStatus | null>(null);
  const [operationLoading, setOperationLoading] = useState<string | null>(null);
  const [autoRefresh, setAutoRefresh] = useState(false);
  const [refreshInterval, setRefreshInterval] = useState<NodeJS.Timeout | null>(null);
  const [warmingCache, setWarmingCache] = useState(false);
  const [lastRefresh, setLastRefresh] = useState<string>('');

  // Enhanced fetch cache status with Redis integration
  const fetchCacheStatus = useCallback(async (silent = false) => {
    try {
      if (!silent) setLoading(true);
      
      // Fetch from both old cache API and Redis API
      const [oldCacheResponse, redisResponse] = await Promise.all([
        fetch('/api/admin/cache', {
          headers: {
            'Cache-Control': 'no-cache, no-store, must-revalidate',
          }
        }),
        fetch('/api/admin/cache/redis', {
          headers: {
            'Cache-Control': 'no-cache, no-store, must-revalidate',
          }
        }).catch(() => null) // Don't fail if Redis API is not available
      ]);

      if (!oldCacheResponse.ok) {
        throw new Error('Failed to fetch cache status');
      }

      const oldData = await oldCacheResponse.json();
      let redisData = null;
      
      if (redisResponse && redisResponse.ok) {
        redisData = await redisResponse.json();
      }

      // Merge cache status with Redis information
      const mergedStatus = {
        redis: {
          connected: redisData?.success && redisData?.data?.status?.connected || oldData.cacheStatus?.redis?.connected || false,
          keys: redisData?.data?.statistics?.totalKeys || oldData.cacheStatus?.redis?.keys || 0,
          memory: oldData.cacheStatus?.redis?.memory,
          hitRatio: oldData.cacheStatus?.redis?.hitRatio
        },
        memory: oldData.cacheStatus?.memory || {
          logos: false,
          portfolio: false,
          images: false,
        },
        lastCleared: oldData.cacheStatus?.lastCleared || null,
        performance: oldData.cacheStatus?.performance,
        size: oldData.cacheStatus?.size
      };

      setCacheStatus(mergedStatus);
      setLastRefresh(new Date().toLocaleTimeString());
    } catch (error) {
      console.error('Error fetching cache status:', error);
      if (!silent) {
        toast.error('Failed to load cache status');
      }
      
      // Fallback status when services are not available
      setCacheStatus({
        redis: {
          connected: false,
          keys: 0,
        },
        memory: {
          logos: false,
          portfolio: false,
          images: false,
        },
        lastCleared: null,
      });
    } finally {
      if (!silent) setLoading(false);
    }
  }, []);

  // Auto-refresh functionality
  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(() => {
        fetchCacheStatus(true);
      }, 10000); // Refresh every 10 seconds
      setRefreshInterval(interval);
    } else {
      if (refreshInterval) {
        clearInterval(refreshInterval);
        setRefreshInterval(null);
      }
    }

    return () => {
      if (refreshInterval) {
        clearInterval(refreshInterval);
      }
    };
  }, [autoRefresh, fetchCacheStatus, refreshInterval]);

  // Clear specific cache with enhanced Redis support
  const clearCache = async (cacheType: string) => {
    try {
      setOperationLoading(cacheType);

      // Use Redis API for redis cache type, otherwise use legacy API
      const endpoint = cacheType === 'redis' ? '/api/admin/cache/redis' : '/api/admin/cache';
      const body = cacheType === 'redis' 
        ? { action: 'flush' }
        : { cacheType };

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
        },
        body: JSON.stringify(body)
      });

      if (!response.ok) {
        throw new Error('Failed to clear cache');
      }

      const data = await response.json();

      if (data.success) {
        toast.success(`${cacheType} cache cleared successfully`);
        
        // Also warm up caches if clearing portfolio
        if (cacheType === 'portfolio') {
          try {
            await fetch('/api/admin/cache/warm', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
            });
            console.log('Portfolio cache warmed up after clearing');
          } catch (warmupError) {
            console.warn('Failed to warm up portfolio cache:', warmupError);
          }
        }
        
        await fetchCacheStatus(true); // Refresh status silently
      } else {
        throw new Error(data.error || 'Unknown error');
      }
    } catch (error) {
      console.error('Error clearing cache:', error);
      toast.error(`Failed to clear ${cacheType} cache`);
    } finally {
      setOperationLoading(null);
    }
  };

  // Warm cache functionality
  const warmCache = async () => {
    try {
      setWarmingCache(true);
      
      const response = await fetch('/api/admin/cache/warm', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (!response.ok) {
        throw new Error('Failed to warm cache');
      }

      const data = await response.json();
      if (data.success) {
        toast.success('Cache warming completed successfully');
        await fetchCacheStatus(true);
      } else {
        throw new Error(data.error || 'Unknown error');
      }
    } catch (error) {
      console.error('Error warming cache:', error);
      toast.error('Failed to warm cache');
    } finally {
      setWarmingCache(false);
    }
  };

  // Clear all caches with enhanced confirmation
  const clearAllCaches = async () => {
    if (!confirm('⚠️ Are you sure you want to clear ALL caches?\n\nThis will:\n• Temporarily slow down the website\n• Clear all cached data\n• Require time to rebuild caches\n\nThis action cannot be undone.')) {
      return;
    }

    try {
      setOperationLoading('all');

      const response = await fetch('/api/admin/cache', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
        },
        body: JSON.stringify({ cacheType: 'all' })
      });

      if (!response.ok) {
        throw new Error('Failed to clear all caches');
      }

      const data = await response.json();

      if (data.success) {
        toast.success('All caches cleared successfully');
        await fetchCacheStatus(true);
      } else {
        throw new Error(data.error || 'Unknown error');
      }
    } catch (error) {
      console.error('Error clearing all caches:', error);
      toast.error('Failed to clear all caches');
    } finally {
      setOperationLoading(null);
    }
  };

  useEffect(() => {
    fetchCacheStatus();
  }, [fetchCacheStatus]);

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-red-600 bg-red-50';
      case 'medium': return 'text-yellow-600 bg-yellow-50';
      case 'low': return 'text-green-600 bg-green-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  return (
    <div className="space-y-6">
      {/* Header with Cache Controls */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 lg:gap-6">
        <div>
          <div>
            <h1 className="text-2xl sm:text-3xl font-semibold text-[#0A2647]">Cache Management</h1>
            <p className="text-slate-600 text-sm mt-1">
              Optimize performance and manage cached data for Mocky Digital
            </p>
          </div>
        </div>
        <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3">
          {/* Auto-refresh toggle */}
          <div className="flex items-center gap-2">
            <label className="flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={autoRefresh}
                onChange={(e) => setAutoRefresh(e.target.checked)}
                className="sr-only"
              />
              <div className={`relative w-10 h-5 rounded-full transition-colors ${
                autoRefresh ? 'bg-[#FF5400]' : 'bg-gray-300'
              }`}>
                <div className={`absolute top-0.5 left-0.5 w-4 h-4 bg-white rounded-full transition-transform ${
                  autoRefresh ? 'translate-x-5' : 'translate-x-0'
                }`} />
              </div>
              <span className="ml-2 text-sm text-slate-600">Auto-refresh</span>
            </label>
          </div>
          
          {/* Manual refresh button */}
          <button
            onClick={() => fetchCacheStatus()}
            disabled={loading}
            className="p-2 sm:p-2 rounded-lg text-slate-500 hover:text-slate-700 hover:bg-slate-100 transition-colors relative min-h-[44px] min-w-[44px] sm:min-h-[40px] sm:min-w-[40px] flex items-center justify-center"
            aria-label="Refresh cache status"
            title="Refresh cache status"
          >
            <ArrowPathIcon className={`h-5 w-5 ${loading ? 'animate-spin' : ''}`} />
            {lastRefresh && (
              <span className="absolute -bottom-6 left-1/2 transform -translate-x-1/2 text-xs text-slate-400 whitespace-nowrap">
                {lastRefresh}
              </span>
            )}
          </button>

          {/* Cache warming button */}
          <button
            onClick={warmCache}
            disabled={warmingCache}
            className="flex items-center justify-center px-4 py-2 bg-gradient-to-r from-[#FF5400] to-[#E54D00] text-white rounded-lg hover:from-[#E54D00] hover:to-[#D44600] transition-all shadow-sm disabled:opacity-50 disabled:cursor-not-allowed min-h-[44px] sm:min-h-[40px]"
            title="Warm cache by preloading critical data"
          >
            {warmingCache ? (
              <ArrowPathIcon className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <FireIcon className="h-4 w-4 mr-2" />
            )}
            <span className="text-sm font-medium">{warmingCache ? 'Warming...' : 'Warm Cache'}</span>
          </button>
        </div>
      </div>

      {/* Settings Navigation */}
      <div className="bg-white shadow-sm rounded-lg p-3 sm:p-4 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
        <Link href="/admin/settings" className="p-3 sm:p-4 border rounded-lg hover:bg-orange-50 hover:border-orange-200 transition-colors">
          <div className="flex items-start">
            <Cog6ToothIcon className="h-6 w-6 text-orange-500 mr-3 mt-0.5 flex-shrink-0" />
            <div className="min-w-0 flex-1">
              <h3 className="font-medium text-slate-800 text-sm sm:text-base">General Settings</h3>
              <p className="text-xs sm:text-sm text-slate-500 mt-1 line-clamp-2">Configure basic site information and contact details</p>
            </div>
          </div>
        </Link>
        <Link href="/admin/settings/storage" className="p-3 sm:p-4 border rounded-lg hover:bg-orange-50 hover:border-orange-200 transition-colors">
          <div className="flex items-start">
            <ServerIcon className="h-6 w-6 text-orange-500 mr-3 mt-0.5 flex-shrink-0" />
            <div className="min-w-0 flex-1">
              <h3 className="font-medium text-slate-800 text-sm sm:text-base">Storage Settings</h3>
              <p className="text-xs sm:text-sm text-slate-500 mt-1 line-clamp-2">Configure file storage providers and buckets</p>
            </div>
          </div>
        </Link>
        <Link href="/admin/settings/cache" className="p-3 sm:p-4 border rounded-lg bg-orange-50 border-orange-200 transition-colors">
          <div className="flex items-start">
            <ArrowPathIcon className="h-6 w-6 text-orange-500 mr-3 mt-0.5 flex-shrink-0" />
            <div className="min-w-0 flex-1">
              <h3 className="font-medium text-slate-800 text-sm sm:text-base">Cache Management</h3>
              <p className="text-xs sm:text-sm text-slate-500 mt-1 line-clamp-2">Manage cache settings and clear cached data</p>
            </div>
          </div>
        </Link>
        <Link href="/admin/settings/scripts" className="p-3 sm:p-4 border rounded-lg hover:bg-orange-50 hover:border-orange-200 transition-colors">
          <div className="flex items-start">
            <CodeBracketIcon className="h-6 w-6 text-orange-500 mr-3 mt-0.5 flex-shrink-0" />
            <div className="min-w-0 flex-1">
              <h3 className="font-medium text-slate-800 text-sm sm:text-base">Site Scripts</h3>
              <p className="text-xs sm:text-sm text-slate-500 mt-1 line-clamp-2">Manage custom scripts for analytics, tracking, and more</p>
            </div>
          </div>
        </Link>
      </div>

      {/* Enhanced Cache Status Overview */}
      {cacheStatus && (
        <div className="bg-white rounded-lg shadow-sm p-4 sm:p-6 border border-slate-200">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-4">
            <h2 className="text-lg font-semibold text-[#0A2647] flex items-center">
              <ChartBarIcon className="h-5 w-5 mr-2 text-[#FF5400]" />
              Cache Status & Performance
            </h2>
            
            {/* Quick actions */}
            <div className="flex flex-col sm:flex-row gap-2">
              <button
                onClick={async () => {
                  try {
                    await fetch('/api/admin/redis/init', {
                      method: 'POST',
                      headers: { 'Content-Type': 'application/json' },
                      body: JSON.stringify({})
                    });
                    toast.success('Redis services initialized');
                    await fetchCacheStatus();
                  } catch (error) {
                    toast.error('Failed to initialize Redis services');
                  }
                }}
                className="px-3 py-2 text-xs sm:text-sm bg-blue-50 text-blue-600 rounded-md hover:bg-blue-100 transition-colors min-h-[44px] sm:min-h-[32px] flex items-center justify-center"
              >
                Initialize Redis
              </button>
              
              <button
                onClick={async () => {
                  try {
                    await fetch('/api/admin/cache/warm', {
                      method: 'POST',
                      headers: { 'Content-Type': 'application/json' },
                    });
                    toast.success('Cache warmup completed');
                    await fetchCacheStatus();
                  } catch (error) {
                    toast.error('Failed to warm up caches');
                  }
                }}
                className="px-3 py-2 text-xs sm:text-sm bg-orange-50 text-orange-600 rounded-md hover:bg-orange-100 transition-colors min-h-[44px] sm:min-h-[32px] flex items-center justify-center"
              >
                Warm Up Caches
              </button>
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
            {/* Redis Status */}
            <div className="space-y-3">
              <h3 className="font-medium text-slate-700 flex items-center">
                <ServerIcon className="h-4 w-4 mr-2" />
                Redis Cache
              </h3>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-slate-600">Connection</span>
                  <div className="flex items-center">
                    {cacheStatus.redis.connected ? (
                      <CheckCircleIcon className="h-4 w-4 text-green-500 mr-1" />
                    ) : (
                      <ExclamationTriangleIcon className="h-4 w-4 text-red-500 mr-1" />
                    )}
                    <span className={`text-sm font-medium ${cacheStatus.redis.connected ? 'text-green-600' : 'text-red-600'}`}>
                      {cacheStatus.redis.connected ? 'Connected' : 'Disconnected'}
                    </span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-slate-600">Keys</span>
                  <span className="text-sm font-medium text-slate-800">{cacheStatus.redis.keys.toLocaleString()}</span>
                </div>
                {cacheStatus?.redis.memory && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-slate-600">Memory</span>
                    <span className="text-sm font-medium text-slate-800">{cacheStatus.redis.memory}</span>
                  </div>
                )}
                {cacheStatus?.redis.hitRatio !== undefined && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-slate-600">Hit Ratio</span>
                    <span className={`text-sm font-medium ${
                      (cacheStatus?.redis.hitRatio || 0) > 0.8 ? 'text-green-600' : 
                      (cacheStatus?.redis.hitRatio || 0) > 0.6 ? 'text-yellow-600' : 'text-red-600'
                    }`}>
                      {((cacheStatus?.redis.hitRatio || 0) * 100).toFixed(1)}%
                    </span>
                  </div>
                )}
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-slate-600">Quick Actions</span>
                  <div className="flex gap-1">
                    <button
                      onClick={() => window.open('/admin/analytics', '_blank')}
                      className="px-2 py-1 text-xs bg-purple-50 text-purple-600 rounded hover:bg-purple-100 transition-colors"
                      title="View Analytics Dashboard"
                    >
                      Analytics
                    </button>
                    <button
                      onClick={async () => {
                        try {
                          const response = await fetch('/api/admin/health/redis');
                          const data = await response.json();
                          if (data.success) {
                            const health = data.data.currentHealth;
                            toast.success(`Redis Health: ${health?.status || 'Unknown'} (${health?.responseTime || 0}ms)`);
                          }
                        } catch (error) {
                          toast.error('Failed to check Redis health');
                        }
                      }}
                      className="px-2 py-1 text-xs bg-green-50 text-green-600 rounded hover:bg-green-100 transition-colors"
                      title="Check Redis Health"
                    >
                      Health
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* Performance Metrics */}
            {cacheStatus?.performance && (
              <div className="space-y-3">
                <h3 className="font-medium text-slate-700 flex items-center">
                  <CpuChipIcon className="h-4 w-4 mr-2" />
                  Performance
                </h3>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-slate-600">Avg Response</span>
                    <span className="text-sm font-medium text-slate-800">
                      {cacheStatus.performance.averageResponseTime}ms
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-slate-600">Total Requests</span>
                    <span className="text-sm font-medium text-slate-800">
                      {cacheStatus.performance.totalRequests.toLocaleString()}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-slate-600">Cache Hits</span>
                    <span className="text-sm font-medium text-green-600">
                      {cacheStatus.performance.cacheHits.toLocaleString()}
                    </span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-slate-600">Cache Misses</span>
                    <span className="text-sm font-medium text-red-600">
                      {cacheStatus.performance.cacheMisses.toLocaleString()}
                    </span>
                  </div>
                </div>
              </div>
            )}

            {/* Cache Sizes */}
            {cacheStatus?.size && (
              <div className="space-y-3">
                <h3 className="font-medium text-slate-700 flex items-center">
                  <CloudIcon className="h-4 w-4 mr-2" />
                  Cache Sizes
                </h3>
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-slate-600">Total</span>
                    <span className="text-sm font-medium text-slate-800">{cacheStatus.size.total}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-slate-600">Logos</span>
                    <span className="text-sm font-medium text-slate-800">{cacheStatus.size.logos}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-slate-600">Portfolio</span>
                    <span className="text-sm font-medium text-slate-800">{cacheStatus.size.portfolio}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-slate-600">Images</span>
                    <span className="text-sm font-medium text-slate-800">{cacheStatus.size.images}</span>
                  </div>
                </div>
              </div>
            )}

            {/* Last Activity */}
            <div className="space-y-3">
              <h3 className="font-medium text-slate-700 flex items-center">
                <ClockIcon className="h-4 w-4 mr-2" />
                Activity
              </h3>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-slate-600">Last Cleared</span>
                  <span className="text-sm text-slate-800">
                    {cacheStatus.lastCleared ? new Date(cacheStatus.lastCleared).toLocaleString() : 'Never'}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-slate-600">Auto Refresh</span>
                  <span className={`text-sm font-medium ${autoRefresh ? 'text-green-600' : 'text-slate-400'}`}>
                    {autoRefresh ? 'Active' : 'Inactive'}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Enhanced Individual Cache Operations */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {cacheOperations.map((operation) => (
          <div key={operation.type} className="bg-white rounded-lg shadow-sm p-6 border border-slate-200 hover:border-[#FF5400]/20 transition-all duration-300">
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center flex-1">
                <div className={`p-2 rounded-lg mr-3 ${
                  operation.color === 'blue' ? 'bg-blue-50 text-blue-500' :
                  operation.color === 'green' ? 'bg-green-50 text-green-500' :
                  operation.color === 'purple' ? 'bg-purple-50 text-purple-500' :
                  operation.color === 'red' ? 'bg-red-50 text-red-500' :
                  'bg-gray-50 text-gray-500'
                }`}>
                  <operation.icon className="h-5 w-5" />
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-1">
                    <h3 className="font-semibold text-[#0A2647]">{operation.name}</h3>
                    <span className={`px-2 py-0.5 text-xs font-medium rounded-full ${getPriorityColor(operation.priority)}`}>
                      {operation.priority}
                    </span>
                  </div>
                  <p className="text-sm text-slate-600 mb-2">{operation.description}</p>
                  {operation.estimatedTime && (
                    <p className="text-xs text-slate-500 flex items-center">
                      <ClockIcon className="h-3 w-3 mr-1" />
                      Est. time: {operation.estimatedTime}
                    </p>
                  )}
                </div>
              </div>
            </div>

            <button
              onClick={() => clearCache(operation.type)}
              disabled={operationLoading === operation.type}
              className={`w-full flex items-center justify-center px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300
                ${operationLoading === operation.type
                  ? 'bg-slate-100 text-slate-400 cursor-not-allowed'
                  : operation.color === 'blue' ? 'bg-blue-50 text-blue-600 hover:bg-blue-100 hover:shadow-sm' :
                    operation.color === 'green' ? 'bg-green-50 text-green-600 hover:bg-green-100 hover:shadow-sm' :
                    operation.color === 'purple' ? 'bg-purple-50 text-purple-600 hover:bg-purple-100 hover:shadow-sm' :
                    operation.color === 'red' ? 'bg-red-50 text-red-600 hover:bg-red-100 hover:shadow-sm' :
                    'bg-gray-50 text-gray-600 hover:bg-gray-100 hover:shadow-sm'
                }`}
            >
              {operationLoading === operation.type ? (
                <ArrowPathIcon className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <TrashIcon className="h-4 w-4 mr-2" />
              )}
              {operationLoading === operation.type ? 'Clearing...' : 'Clear Cache'}
            </button>
          </div>
        ))}
      </div>

      {/* Enhanced Redis Management */}
      {cacheStatus?.redis.connected && (
        <div className="bg-white rounded-lg shadow-sm p-6 border border-slate-200">
          <h3 className="text-lg font-semibold text-[#0A2647] mb-4 flex items-center">
            <ServerIcon className="h-5 w-5 mr-2 text-green-500" />
            Redis Management
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <button
              onClick={async () => {
                try {
                  await fetch('/api/admin/health/redis', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ action: 'start_monitoring' })
                  });
                  toast.success('Redis monitoring started');
                } catch (error) {
                  toast.error('Failed to start monitoring');
                }
              }}
              className="flex items-center justify-center px-4 py-3 bg-green-50 text-green-600 rounded-lg hover:bg-green-100 transition-colors"
            >
              <CheckCircleIcon className="h-4 w-4 mr-2" />
              Start Monitoring
            </button>
            
            <button
              onClick={async () => {
                try {
                  const response = await fetch('/api/admin/cache/redis', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ action: 'cleanup' })
                  });
                  const data = await response.json();
                  if (data.success) {
                    toast.success('Redis cleanup completed');
                    await fetchCacheStatus();
                  }
                } catch (error) {
                  toast.error('Failed to cleanup Redis');
                }
              }}
              className="flex items-center justify-center px-4 py-3 bg-blue-50 text-blue-600 rounded-lg hover:bg-blue-100 transition-colors"
            >
              <ArrowPathIcon className="h-4 w-4 mr-2" />
              Cleanup Expired
            </button>
            
            <button
              onClick={() => window.open('/admin/analytics', '_blank')}
              className="flex items-center justify-center px-4 py-3 bg-purple-50 text-purple-600 rounded-lg hover:bg-purple-100 transition-colors"
            >
              <InformationCircleIcon className="h-4 w-4 mr-2" />
              View Analytics
            </button>
          </div>
        </div>
      )}

      {/* Enhanced Clear All Caches Section */}
      <div className="bg-white rounded-lg shadow-sm p-6 border border-slate-200">
        <div className="flex items-start justify-between">
          <div className="flex items-center flex-1">
            <div className="p-2 bg-gradient-to-r from-red-50 to-orange-50 text-red-500 rounded-lg mr-3">
              <CloudIcon className="h-5 w-5" />
            </div>
            <div>
              <h3 className="font-semibold text-[#0A2647] mb-1">Clear All Caches</h3>
              <p className="text-sm text-slate-600 mb-2">
                Clear all cached data at once. This may temporarily slow down the website while caches rebuild.
              </p>
              <div className="flex items-center gap-4 text-xs text-slate-500">
                <span className="flex items-center">
                  <WrenchScrewdriverIcon className="h-3 w-3 mr-1" />
                  High Impact Operation
                </span>
                <span className="flex items-center">
                  <ClockIcon className="h-3 w-3 mr-1" />
                  Est. time: ~2-3 minutes
                </span>
              </div>
            </div>
          </div>

          <button
            onClick={clearAllCaches}
            disabled={operationLoading === 'all'}
            className={`flex items-center px-6 py-2 rounded-lg text-sm font-medium transition-all duration-300 shadow-sm
              ${operationLoading === 'all'
                ? 'bg-slate-100 text-slate-400 cursor-not-allowed'
                : 'bg-gradient-to-r from-red-50 to-orange-50 text-red-600 hover:from-red-100 hover:to-orange-100 hover:shadow-md border border-red-200'
              }`}
          >
            {operationLoading === 'all' ? (
              <ArrowPathIcon className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <TrashIcon className="h-4 w-4 mr-2" />
            )}
            {operationLoading === 'all' ? 'Clearing All...' : 'Clear All Caches'}
          </button>
        </div>
      </div>

      {/* Enhanced Information Panel with Mocky Digital Branding */}
      <div className="bg-gradient-to-r from-[#FF5400]/5 to-[#E54D00]/5 rounded-lg p-6 border border-[#FF5400]/20">
        <div className="flex items-start">
          <InformationCircleIcon className="h-5 w-5 text-[#FF5400] mr-3 mt-0.5 flex-shrink-0" />
          <div>
            <h3 className="font-semibold text-[#0A2647] mb-3 flex items-center">
              Enhanced Cache Management with Redis
              <span className="ml-2 px-2 py-1 bg-[#FF5400] text-white text-xs font-medium rounded">
                Mocky Digital
              </span>
            </h3>
            <div className="text-sm text-slate-700 space-y-3">
              <p>
                This system now uses Redis for high-performance caching, session management, rate limiting, and real-time analytics. Clear caches when:
              </p>
              
              <div className="grid md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-semibold text-[#0A2647] mb-1">When to clear caches:</h4>
                  <ul className="list-disc list-inside space-y-1">
                    <li>You've updated images in S3 storage</li>
                    <li>Portfolio items aren't showing latest changes</li>
                    <li>Website content appears outdated</li>
                    <li>After making storage configuration changes</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold text-[#0A2647] mb-1">New Redis Features:</h4>
                  <ul className="list-disc list-inside space-y-1">
                    <li>Logo designs need immediate refresh</li>
                    <li>Client feedback requires quick updates</li>
                    <li>After server maintenance or deployment</li>
                    <li>Performance issues are detected</li>
                  </ul>
                </div>
              </div>
              
              <div className="bg-white/60 rounded-lg p-4 mt-4">
                <h4 className="font-semibold text-[#0A2647] mb-2 flex items-center">
                  <EyeIcon className="h-4 w-4 mr-2" />
                  Enhanced Features
                </h4>
                <div className="space-y-2 text-sm">
                  <p><strong>Auto-refresh:</strong> Real-time cache status monitoring every 10 seconds</p>
                  <p><strong>Cache Warming:</strong> Proactively preload critical data for optimal performance</p>
                  <p><strong>Performance Metrics:</strong> Monitor cache hit ratios and response times</p>
                  <p><strong>Priority-based Operations:</strong> Operations are labeled by impact and estimated completion time</p>
                  <p><strong>Redis Integration:</strong> Advanced request throttling and session management</p>
                  <p><strong>Real-time Analytics:</strong> Track performance metrics and user behavior</p>
                </div>
              </div>
              
              <p className="text-[#0A2647] font-medium mt-4">
                💡 <strong>Pro Tip:</strong> Use "Initialize Redis" if the connection shows as disconnected, then "Warm Up Caches" for optimal performance and minimal impact on your clients' experience.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
