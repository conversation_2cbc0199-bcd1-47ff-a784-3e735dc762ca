'use client';

import React, { useState, useEffect } from 'react';
import {
  ArrowPathIcon,
  PlusIcon,
  CodeBracketIcon,
  CheckIcon,
  XMarkIcon,
  PencilIcon,
  TrashIcon,
  Cog6ToothIcon,
  ServerIcon,
} from '@heroicons/react/24/outline';
import Link from 'next/link';
import { toast } from 'react-hot-toast';

interface Script {
  id: string;
  name: string;
  description: string | null;
  scriptType: 'head' | 'body' | 'footer';
  content: string;
  isActive: boolean;
  position: number;
  createdAt: string;
  updatedAt: string;
}

interface FormData {
  name: string;
  description: string;
  scriptType: 'head' | 'body' | 'footer';
  content: string;
  isActive: boolean;
  position: number;
}

export default function ScriptsPage() {
  const [scripts, setScripts] = useState<Script[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [showForm, setShowForm] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [saving, setSaving] = useState(false);
  const [formData, setFormData] = useState<FormData>({
    name: '',
    description: '',
    scriptType: 'head',
    content: '',
    isActive: true,
    position: 0,
  });

  // Fetch scripts
  const fetchScripts = async () => {
    try {
      setLoading(true);
      setError('');

      const response = await fetch('/api/admin/scripts');
      if (!response.ok) {
        throw new Error('Failed to fetch scripts');
      }

      const data = await response.json();
      setScripts(data);
    } catch (err) {
      console.error('Error fetching scripts:', err);
      setError('Failed to load scripts');
      toast.error('Failed to load scripts');
    } finally {
      setLoading(false);
    }
  };

  // Initial fetch
  useEffect(() => {
    fetchScripts();
  }, []);

  // Handle form input changes
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData(prev => ({ ...prev, [name]: checked }));
    } else if (name === 'position') {
      setFormData(prev => ({ ...prev, [name]: parseInt(value) || 0 }));
    } else {
      setFormData(prev => ({ ...prev, [name]: value }));
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      setSaving(true);
      setError('');
      
      const url = editingId 
        ? `/api/admin/scripts/${editingId}`
        : '/api/admin/scripts';
      
      const method = editingId ? 'PUT' : 'POST';
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save script');
      }
      
      // Refresh scripts list
      await fetchScripts();
      
      // Reset form
      resetForm();
      
      // Show success message
      toast.success(editingId ? 'Script updated successfully' : 'Script created successfully');
    } catch (err) {
      console.error('Error saving script:', err);
      setError('Failed to save script');
      toast.error('Failed to save script');
    } finally {
      setSaving(false);
    }
  };

  // Edit script
  const handleEdit = (script: Script) => {
    setFormData({
      name: script.name,
      description: script.description || '',
      scriptType: script.scriptType,
      content: script.content,
      isActive: script.isActive,
      position: script.position,
    });
    
    setEditingId(script.id);
    setShowForm(true);
    
    // Scroll to form
    setTimeout(() => {
      document.getElementById('script-form')?.scrollIntoView({ behavior: 'smooth' });
    }, 100);
  };

  // Delete script
  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this script?')) {
      return;
    }
    
    try {
      setLoading(true);
      
      const response = await fetch(`/api/admin/scripts/${id}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        throw new Error('Failed to delete script');
      }
      
      // Refresh scripts list
      await fetchScripts();
      
      // Show success message
      toast.success('Script deleted successfully');
    } catch (err) {
      console.error('Error deleting script:', err);
      toast.error('Failed to delete script');
    } finally {
      setLoading(false);
    }
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      scriptType: 'head',
      content: '',
      isActive: true,
      position: 0,
    });
    
    setEditingId(null);
    setShowForm(false);
  };

  // Group scripts by type
  const headScripts = scripts.filter(script => script.scriptType === 'head');
  const bodyScripts = scripts.filter(script => script.scriptType === 'body');
  const footerScripts = scripts.filter(script => script.scriptType === 'footer');

  return (
    <div className="space-y-6 sm:space-y-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center gap-4">
        <div className="flex items-center">
          <div className="p-2 bg-blue-50 text-blue-500 rounded-lg mr-3 flex-shrink-0">
            <CodeBracketIcon className="h-6 w-6" />
          </div>
          <div>
            <h1 className="text-2xl sm:text-3xl font-semibold text-slate-800">Site Scripts</h1>
            <p className="text-slate-600 text-sm mt-1">Manage custom scripts for analytics, tracking, and more</p>
          </div>
        </div>
      </div>

      {/* Settings Navigation */}
      <div className="bg-white shadow-sm rounded-lg p-3 sm:p-4 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
        <Link href="/admin/settings" className="p-3 sm:p-4 border rounded-lg hover:bg-orange-50 hover:border-orange-200 transition-colors">
          <div className="flex items-start">
            <Cog6ToothIcon className="h-6 w-6 text-orange-500 mr-3 mt-0.5 flex-shrink-0" />
            <div className="min-w-0 flex-1">
              <h3 className="font-medium text-slate-800 text-sm sm:text-base">General Settings</h3>
              <p className="text-xs sm:text-sm text-slate-500 mt-1 line-clamp-2">Configure basic site information and contact details</p>
            </div>
          </div>
        </Link>
        <Link href="/admin/settings/storage" className="p-3 sm:p-4 border rounded-lg hover:bg-orange-50 hover:border-orange-200 transition-colors">
          <div className="flex items-start">
            <ServerIcon className="h-6 w-6 text-orange-500 mr-3 mt-0.5 flex-shrink-0" />
            <div className="min-w-0 flex-1">
              <h3 className="font-medium text-slate-800 text-sm sm:text-base">Storage Settings</h3>
              <p className="text-xs sm:text-sm text-slate-500 mt-1 line-clamp-2">Configure storage providers for file uploads</p>
            </div>
          </div>
        </Link>
        <Link href="/admin/settings/cache" className="p-3 sm:p-4 border rounded-lg hover:bg-orange-50 hover:border-orange-200 transition-colors">
          <div className="flex items-start">
            <ArrowPathIcon className="h-6 w-6 text-orange-500 mr-3 mt-0.5 flex-shrink-0" />
            <div className="min-w-0 flex-1">
              <h3 className="font-medium text-slate-800 text-sm sm:text-base">Cache Management</h3>
              <p className="text-xs sm:text-sm text-slate-500 mt-1 line-clamp-2">Manage cache settings and clear cached data</p>
            </div>
          </div>
        </Link>
        <Link href="/admin/settings/scripts" className="p-3 sm:p-4 border rounded-lg bg-orange-50 border-orange-200 transition-colors">
          <div className="flex items-start">
            <CodeBracketIcon className="h-6 w-6 text-orange-500 mr-3 mt-0.5 flex-shrink-0" />
            <div className="min-w-0 flex-1">
              <h3 className="font-medium text-slate-800 text-sm sm:text-base">Site Scripts</h3>
              <p className="text-xs sm:text-sm text-slate-500 mt-1 line-clamp-2">Manage custom scripts for analytics, tracking, and more</p>
            </div>
          </div>
        </Link>
      </div>

      {/* Actions */}
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
        <div>
          <h2 className="text-xl font-semibold text-slate-800">Manage Scripts</h2>
          <p className="text-sm text-slate-500 mt-1">
            Add custom scripts to your site for analytics, tracking, and more.
          </p>
        </div>
        <div className="flex flex-col sm:flex-row gap-2 sm:gap-3">
          <button
            onClick={() => fetchScripts()}
            className="flex items-center justify-center p-2 rounded-lg text-slate-500 hover:text-slate-700 hover:bg-slate-100 transition-colors min-h-[44px] min-w-[44px] sm:min-h-[40px] sm:min-w-[40px]"
            disabled={loading}
            title="Refresh scripts"
          >
            <ArrowPathIcon className={`h-5 w-5 ${loading ? 'animate-spin' : ''}`} />
          </button>
          <button
            onClick={() => {
              resetForm();
              setShowForm(!showForm);
            }}
            className="flex items-center justify-center px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors min-h-[44px] sm:min-h-[40px]"
          >
            {showForm ? (
              <>
                <XMarkIcon className="h-5 w-5 mr-2" />
                <span>Cancel</span>
              </>
            ) : (
              <>
                <PlusIcon className="h-5 w-5 mr-2" />
                <span>Add Script</span>
              </>
            )}
          </button>
        </div>
      </div>

      {/* Script Form */}
      {showForm && (
        <div id="script-form" className="bg-white rounded-lg shadow-sm p-4 sm:p-6 border border-slate-200">
          <h3 className="text-lg font-semibold text-slate-800 mb-4">
            {editingId ? 'Edit Script' : 'Add New Script'}
          </h3>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                  Script Name
                </label>
                <input
                  type="text"
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm sm:text-base min-h-[44px] sm:min-h-[40px]"
                  required
                />
              </div>
              <div>
                <label htmlFor="scriptType" className="block text-sm font-medium text-gray-700 mb-1">
                  Script Type
                </label>
                <select
                  id="scriptType"
                  name="scriptType"
                  value={formData.scriptType}
                  onChange={handleChange}
                  className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm sm:text-base min-h-[44px] sm:min-h-[40px]"
                  required
                >
                  <option value="head">Head (top of page)</option>
                  <option value="body">Body (start of body)</option>
                  <option value="footer">Footer (end of body)</option>
                </select>
              </div>
            </div>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="lg:col-span-2">
                <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <input
                  type="text"
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleChange}
                  className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm sm:text-base min-h-[44px] sm:min-h-[40px]"
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label htmlFor="position" className="block text-sm font-medium text-gray-700 mb-1">
                    Position
                  </label>
                  <input
                    type="number"
                    id="position"
                    name="position"
                    value={formData.position}
                    onChange={handleChange}
                    className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm sm:text-base min-h-[44px] sm:min-h-[40px]"
                    min="0"
                  />
                </div>
                <div className="flex items-center pt-6">
                  <input
                    type="checkbox"
                    id="isActive"
                    name="isActive"
                    checked={formData.isActive}
                    onChange={(e) => setFormData(prev => ({ ...prev, isActive: e.target.checked }))}
                    className="h-5 w-5 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <label htmlFor="isActive" className="ml-2 block text-sm text-gray-700">
                    Active
                  </label>
                </div>
              </div>
            </div>
            <div>
              <label htmlFor="content" className="block text-sm font-medium text-gray-700 mb-1">
                Script Content
              </label>
              <textarea
                id="content"
                name="content"
                value={formData.content}
                onChange={handleChange}
                rows={8}
                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm sm:text-base font-mono resize-y"
                placeholder="Enter your script code here..."
                required
              />
              <p className="mt-1 text-xs text-gray-500">Add your JavaScript, HTML, or other script content</p>
            </div>
            <div className="flex flex-col sm:flex-row sm:justify-end gap-3 sm:gap-3 pt-4 border-t border-gray-200">
              <button
                type="button"
                onClick={resetForm}
                className="w-full sm:w-auto px-6 py-3 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 min-h-[44px]"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={saving}
                className="w-full sm:w-auto inline-flex justify-center items-center px-6 py-3 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 min-h-[44px] disabled:opacity-50"
              >
                {saving ? (
                  <>
                    <ArrowPathIcon className="animate-spin -ml-1 mr-2 h-4 w-4" />
                    Saving...
                  </>
                ) : (
                  <>
                    <CheckIcon className="-ml-1 mr-2 h-4 w-4" />
                    {editingId ? 'Update Script' : 'Save Script'}
                  </>
                )}
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Scripts List */}
      {loading && scripts.length === 0 ? (
        <div className="bg-white rounded-lg shadow-sm p-8 text-center">
          <div className="animate-spin h-10 w-10 border-4 border-blue-500 border-t-transparent rounded-full mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading scripts...</p>
        </div>
      ) : error && scripts.length === 0 ? (
        <div className="bg-white rounded-lg shadow-sm p-8 text-center">
          <p className="text-red-500 mb-4">{error}</p>
          <button
            onClick={fetchScripts}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            Try Again
          </button>
        </div>
      ) : scripts.length === 0 ? (
        <div className="bg-white rounded-lg shadow-sm p-8 text-center">
          <p className="text-gray-600 mb-4">No scripts found. Add your first script to get started.</p>
          <button
            onClick={() => setShowForm(true)}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            <PlusIcon className="h-5 w-5 inline mr-1" />
            Add Script
          </button>
        </div>
      ) : (
        <div className="space-y-6">
          {/* Head Scripts */}
          <div className="bg-white rounded-lg shadow-sm overflow-hidden">
            <div className="px-6 py-4 bg-blue-50 border-b border-blue-100">
              <h3 className="text-lg font-medium text-blue-800">Head Scripts</h3>
              <p className="text-sm text-blue-600">These scripts are added to the &lt;head&gt; section of your site.</p>
            </div>
            {headScripts.length === 0 ? (
              <div className="p-6 text-center text-gray-500">No head scripts found.</div>
            ) : (
              <div className="divide-y divide-gray-200">
                {headScripts.map((script) => (
                  <div key={script.id} className="p-6 hover:bg-gray-50 transition-colors">
                    <div className="flex flex-wrap justify-between items-start gap-4">
                      <div>
                        <h4 className="text-lg font-medium text-gray-900 flex items-center">
                          {script.name}
                          {!script.isActive && (
                            <span className="ml-2 px-2 py-0.5 text-xs rounded-full bg-gray-100 text-gray-500">
                              Inactive
                            </span>
                          )}
                        </h4>
                        {script.description && (
                          <p className="mt-1 text-sm text-gray-500">{script.description}</p>
                        )}
                        <div className="mt-2 text-xs text-gray-400">
                          Position: {script.position} | Created: {new Date(script.createdAt).toLocaleDateString()}
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleEdit(script)}
                          className="p-2 text-blue-600 hover:bg-blue-50 rounded-full"
                          title="Edit script"
                        >
                          <PencilIcon className="h-5 w-5" />
                        </button>
                        <button
                          onClick={() => handleDelete(script.id)}
                          className="p-2 text-red-600 hover:bg-red-50 rounded-full"
                          title="Delete script"
                        >
                          <TrashIcon className="h-5 w-5" />
                        </button>
                      </div>
                    </div>
                    <div className="mt-3 p-3 bg-gray-50 rounded-md overflow-auto max-h-32">
                      <pre className="text-xs text-gray-700 font-mono whitespace-pre-wrap">
                        {script.content.length > 300
                          ? `${script.content.substring(0, 300)}...`
                          : script.content}
                      </pre>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Body Scripts */}
          <div className="bg-white rounded-lg shadow-sm overflow-hidden">
            <div className="px-6 py-4 bg-green-50 border-b border-green-100">
              <h3 className="text-lg font-medium text-green-800">Body Scripts</h3>
              <p className="text-sm text-green-600">These scripts are added at the beginning of the &lt;body&gt; section.</p>
            </div>
            {bodyScripts.length === 0 ? (
              <div className="p-6 text-center text-gray-500">No body scripts found.</div>
            ) : (
              <div className="divide-y divide-gray-200">
                {bodyScripts.map((script) => (
                  <div key={script.id} className="p-6 hover:bg-gray-50 transition-colors">
                    <div className="flex flex-wrap justify-between items-start gap-4">
                      <div>
                        <h4 className="text-lg font-medium text-gray-900 flex items-center">
                          {script.name}
                          {!script.isActive && (
                            <span className="ml-2 px-2 py-0.5 text-xs rounded-full bg-gray-100 text-gray-500">
                              Inactive
                            </span>
                          )}
                        </h4>
                        {script.description && (
                          <p className="mt-1 text-sm text-gray-500">{script.description}</p>
                        )}
                        <div className="mt-2 text-xs text-gray-400">
                          Position: {script.position} | Created: {new Date(script.createdAt).toLocaleDateString()}
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleEdit(script)}
                          className="p-2 text-blue-600 hover:bg-blue-50 rounded-full"
                          title="Edit script"
                        >
                          <PencilIcon className="h-5 w-5" />
                        </button>
                        <button
                          onClick={() => handleDelete(script.id)}
                          className="p-2 text-red-600 hover:bg-red-50 rounded-full"
                          title="Delete script"
                        >
                          <TrashIcon className="h-5 w-5" />
                        </button>
                      </div>
                    </div>
                    <div className="mt-3 p-3 bg-gray-50 rounded-md overflow-auto max-h-32">
                      <pre className="text-xs text-gray-700 font-mono whitespace-pre-wrap">
                        {script.content.length > 300
                          ? `${script.content.substring(0, 300)}...`
                          : script.content}
                      </pre>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Footer Scripts */}
          <div className="bg-white rounded-lg shadow-sm overflow-hidden">
            <div className="px-6 py-4 bg-purple-50 border-b border-purple-100">
              <h3 className="text-lg font-medium text-purple-800">Footer Scripts</h3>
              <p className="text-sm text-purple-600">These scripts are added at the end of the &lt;body&gt; section.</p>
            </div>
            {footerScripts.length === 0 ? (
              <div className="p-6 text-center text-gray-500">No footer scripts found.</div>
            ) : (
              <div className="divide-y divide-gray-200">
                {footerScripts.map((script) => (
                  <div key={script.id} className="p-6 hover:bg-gray-50 transition-colors">
                    <div className="flex flex-wrap justify-between items-start gap-4">
                      <div>
                        <h4 className="text-lg font-medium text-gray-900 flex items-center">
                          {script.name}
                          {!script.isActive && (
                            <span className="ml-2 px-2 py-0.5 text-xs rounded-full bg-gray-100 text-gray-500">
                              Inactive
                            </span>
                          )}
                        </h4>
                        {script.description && (
                          <p className="mt-1 text-sm text-gray-500">{script.description}</p>
                        )}
                        <div className="mt-2 text-xs text-gray-400">
                          Position: {script.position} | Created: {new Date(script.createdAt).toLocaleDateString()}
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        <button
                          onClick={() => handleEdit(script)}
                          className="p-2 text-blue-600 hover:bg-blue-50 rounded-full"
                          title="Edit script"
                        >
                          <PencilIcon className="h-5 w-5" />
                        </button>
                        <button
                          onClick={() => handleDelete(script.id)}
                          className="p-2 text-red-600 hover:bg-red-50 rounded-full"
                          title="Delete script"
                        >
                          <TrashIcon className="h-5 w-5" />
                        </button>
                      </div>
                    </div>
                    <div className="mt-3 p-3 bg-gray-50 rounded-md overflow-auto max-h-32">
                      <pre className="text-xs text-gray-700 font-mono whitespace-pre-wrap">
                        {script.content.length > 300
                          ? `${script.content.substring(0, 300)}...`
                          : script.content}
                      </pre>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
