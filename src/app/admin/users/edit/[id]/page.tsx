'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  ArrowLeftIcon,
  ExclamationTriangleIcon,
  UserCircleIcon,
  LockClosedIcon,
  ClockIcon,
} from '@heroicons/react/24/outline';

// Custom hooks
import { useUser } from '@/hooks/useUser';
import { useUserForm } from '@/hooks/useUserForm';

// Components
import { UserFormFields } from '@/components/admin/UserFormFields';
import { 
  UserOverview, 
  UserHeader, 
  RolePermissions, 
  ActivityLogs 
} from '@/components/admin/UserInfoDisplay';

// Types
import { User } from '@/types/user';

interface EditUserPageProps {
  params: Promise<{ id: string }>;
}

export default function EditUserPage({ params }: EditUserPageProps) {
  const router = useRouter();
  
  // State for resolved params
  const [userId, setUserId] = useState<string>('');
  const [activeTab, setActiveTab] = useState<'profile' | 'security' | 'activity'>('profile');
  const [showPasswords, setShowPasswords] = useState({ password: false, confirmPassword: false });
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  // Resolve params
  useEffect(() => {
    params.then((resolvedParams) => {
      setUserId(resolvedParams.id);
    });
  }, [params]);

  // Custom hooks
  const {
    user,
    roles,
    activityLogs,
    isLoading: isLoadingUser,
    error: userError,
    deleteUser,
  } = useUser({
    userId,
    includeActivityLogs: true,
    onError: (error) => {
      if (error === 'User not found') {
        router.push('/admin/users');
      }
    },
  });

  const {
    formData,
    errors,
    isLoading: isSubmitting,
    hasChanges,
    updateField,
    submitForm,
    resetForm,
  } = useUserForm({
    user: user || undefined,
    onSuccess: (updatedUser: User) => {
      // Optionally redirect or stay on page
      console.log('User updated successfully:', updatedUser);
    },
  });

  // Handlers
  const handleTogglePassword = (field: 'password' | 'confirmPassword') => {
    setShowPasswords(prev => ({
      ...prev,
      [field]: !prev[field],
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!userId) return;
    
    const success = await submitForm(`/api/admin/users/${userId}`, 'PUT');
    if (success && activeTab !== 'profile') {
      setActiveTab('profile'); // Switch back to profile tab after successful update
    }
  };

  const handleDeleteUser = async () => {
    const success = await deleteUser();
    if (success) {
      router.push('/admin/users');
    }
    setShowDeleteModal(false);
  };

  // Loading state
  if (isLoadingUser || !user || !userId) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-orange-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading user information...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (userError) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <div className="text-center">
          <ExclamationTriangleIcon className="h-16 w-16 text-red-500 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Error Loading User</h2>
          <p className="text-gray-600 mb-4">{userError}</p>
          <div className="space-x-3">
            <Link
              href="/admin/users"
              className="inline-flex items-center gap-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              <ArrowLeftIcon className="h-4 w-4" />
              Back to Users
            </Link>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
            >
              Try Again
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <UserHeader user={user} hasChanges={hasChanges} />
        <div className="flex items-center gap-3">
          <Link
            href="/admin/users"
            className="inline-flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-xl text-gray-700 bg-white hover:bg-gray-50 transition-all duration-200 shadow-sm hover:shadow-md"
          >
            <ArrowLeftIcon className="h-5 w-5" />
            Back to Users
          </Link>
          {hasChanges && (
            <div className="flex items-center gap-2 px-3 py-2 bg-amber-50 border border-amber-200 rounded-xl">
              <ExclamationTriangleIcon className="h-5 w-5 text-amber-500" />
              <span className="text-sm text-amber-700 font-medium">Unsaved changes</span>
            </div>
          )}
        </div>
      </div>

      {/* User Overview */}
      <UserOverview user={user} />

      {/* Tabs Navigation */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100">
        <div className="border-b border-gray-200">
          <nav className="flex space-x-8 px-6" aria-label="Tabs">
            <button
              onClick={() => setActiveTab('profile')}
              className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${
                activeTab === 'profile'
                  ? 'border-orange-500 text-orange-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <div className="flex items-center gap-2">
                <UserCircleIcon className="h-5 w-5" />
                Profile Settings
              </div>
            </button>
            <button
              onClick={() => setActiveTab('security')}
              className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${
                activeTab === 'security'
                  ? 'border-orange-500 text-orange-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <div className="flex items-center gap-2">
                <LockClosedIcon className="h-5 w-5" />
                Security & Permissions
              </div>
            </button>
            <button
              onClick={() => setActiveTab('activity')}
              className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200 ${
                activeTab === 'activity'
                  ? 'border-orange-500 text-orange-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <div className="flex items-center gap-2">
                <ClockIcon className="h-5 w-5" />
                Activity Log
              </div>
            </button>
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {activeTab === 'profile' && (
            <form onSubmit={handleSubmit} className="space-y-8">
              <UserFormFields
                formData={formData}
                errors={errors}
                roles={roles}
                showPasswords={showPasswords}
                onFieldChange={updateField}
                onTogglePassword={handleTogglePassword}
                isEditMode={true}
              />

              {/* Form Actions */}
              <div className="flex items-center justify-between pt-6 border-t border-gray-200">
                <button
                  type="button"
                  onClick={() => setShowDeleteModal(true)}
                  className="inline-flex items-center gap-2 px-4 py-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-xl transition-all duration-200"
                >
                  <ExclamationTriangleIcon className="h-5 w-5" />
                  Delete User
                </button>
                
                <div className="flex items-center gap-3">
                  <button
                    type="button"
                    onClick={resetForm}
                    disabled={!hasChanges || isSubmitting}
                    className="px-6 py-3 border border-gray-300 rounded-xl text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 font-medium"
                  >
                    Reset
                  </button>
                  <button
                    type="submit"
                    disabled={isSubmitting || !hasChanges}
                    className="px-6 py-3 bg-gradient-to-r from-orange-500 to-red-500 text-white rounded-xl hover:from-orange-600 hover:to-red-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 font-medium shadow-lg hover:shadow-xl"
                  >
                    {isSubmitting ? (
                      <div className="flex items-center gap-2">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                        Saving...
                      </div>
                    ) : (
                      'Save Changes'
                    )}
                  </button>
                </div>
              </div>
            </form>
          )}

          {activeTab === 'security' && (
            <div className="space-y-8">
              {/* Password Change Form */}
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="max-w-2xl">
                  <UserFormFields
                    formData={formData}
                    errors={errors}
                    roles={roles}
                    showPasswords={showPasswords}
                    onFieldChange={updateField}
                    onTogglePassword={handleTogglePassword}
                    isEditMode={true}
                  />
                </div>

                <div className="flex justify-end">
                  <button
                    type="submit"
                    disabled={isSubmitting || (!formData.password && !formData.confirmPassword)}
                    className="px-6 py-3 bg-gradient-to-r from-orange-500 to-red-500 text-white rounded-xl hover:from-orange-600 hover:to-red-600 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 font-medium shadow-lg hover:shadow-xl"
                  >
                    {isSubmitting ? (
                      <div className="flex items-center gap-2">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                        Updating...
                      </div>
                    ) : (
                      'Update Security Settings'
                    )}
                  </button>
                </div>
              </form>

              {/* Role Permissions Display */}
              <RolePermissions role={user.role} />
            </div>
          )}

          {activeTab === 'activity' && (
            <ActivityLogs activityLogs={activityLogs} />
          )}
        </div>
      </div>

      {/* Delete Confirmation Modal */}
      {showDeleteModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl shadow-xl max-w-md w-full p-6">
            <div className="flex items-center gap-3 mb-4">
              <ExclamationTriangleIcon className="h-8 w-8 text-red-500" />
              <h3 className="text-lg font-semibold text-gray-900">Delete User</h3>
            </div>
            <p className="text-gray-600 mb-6">
              Are you sure you want to delete <strong>{user.name || user.username}</strong>? 
              This action cannot be undone and will permanently remove all user data and access.
            </p>
            <div className="flex items-center justify-end gap-3">
              <button
                onClick={() => setShowDeleteModal(false)}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 hover:bg-gray-100 rounded-lg transition-all duration-200"
              >
                Cancel
              </button>
              <button
                onClick={handleDeleteUser}
                disabled={isSubmitting}
                className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 transition-all duration-200"
              >
                {isSubmitting ? 'Deleting...' : 'Delete User'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 