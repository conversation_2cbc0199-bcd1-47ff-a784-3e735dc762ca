'use client';

import { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import { useSession } from 'next-auth/react';
import {
  ArrowPathIcon,
  PlusIcon,
  DocumentTextIcon,
  CheckCircleIcon,
  ClockIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  TrashIcon
} from '@heroicons/react/24/outline';
import { useNotification } from '@/contexts/NotificationContext';
import { format } from 'date-fns';

interface QuoteItem {
  id: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  description: string | null;
  itemName?: string | null;
  itemType?: string;
  serviceId?: string | null;
  serviceName?: string;
}

interface Quote {
  id: string;
  quoteNumber: string;
  totalAmount: number;
  subtotalAmount?: number;
  discountType?: string | null;
  discountValue?: number | null;
  discountAmount?: number | null;
  customerName: string;
  phoneNumber: string;
  email: string | null;
  status: string;
  notes: string | null;
  createdAt: string;
  issuedAt: string;
  validUntil: string;
  items: QuoteItem[];
  pdfUrl?: string;
}

export default function QuotesPage() {
  const [quotes, setQuotes] = useState<Quote[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deleting, setDeleting] = useState<string | null>(null);

  const { showNotification } = useNotification();
  const { data: session, status } = useSession();

  const fetchQuotes = useCallback(async () => {
    if (status === 'loading') return;
    if (!session) {
      setError('Authentication required');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/admin/quotes', {
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch quotes');
      }

      const result = await response.json();
      console.log('Quotes API response:', result);

      // Handle new API response format
      if (result.success) {
        const quotes = result.data || [];
        setQuotes(Array.isArray(quotes) ? quotes : []);
      } else {
        // API returned an error
        console.error('Quotes API error:', result);
        setQuotes([]);
        throw new Error(result.error || result.message || 'Failed to fetch quotes');
      }
    } catch (err) {
      console.error('Error fetching quotes:', err);
      setError('Failed to load quotes. Please try again.');
      showNotification('error', 'Failed to load quotes');
      // Ensure quotes is always an array to prevent map errors
      setQuotes([]);
    } finally {
      setLoading(false);
    }
  }, [showNotification, session, status]);

  useEffect(() => {
    fetchQuotes();
  }, [fetchQuotes]);

  const handleDelete = async (quoteId: string) => {
    if (!session) {
      showNotification('error', 'Authentication required');
      return;
    }

    if (!confirm('Are you sure you want to delete this quote? This action cannot be undone.')) {
      return;
    }

    try {
      setDeleting(quoteId);

      const response = await fetch(`/api/admin/quotes/${quoteId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || data.message || 'Failed to delete quote');
      }

      if (!data.success) {
        throw new Error(data.error || 'Failed to delete quote');
      }

      // Remove the quote from the list
      setQuotes(prev => prev.filter(q => q.id !== quoteId));
      showNotification('success', data.message || 'Quote deleted successfully');
    } catch (err) {
      console.error('Error deleting quote:', err);
      showNotification('error', err instanceof Error ? err.message : 'Failed to delete quote');
      throw err; // Re-throw to trigger error boundary
    } finally {
      setDeleting(null);
    }
  };

  const formatSafeDate = (dateString: string | null | undefined, formatStr: string = 'dd/MM/yyyy') => {
    if (!dateString) {
      return 'N/A';
    }

    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        console.warn('Invalid date string:', dateString);
        return 'Invalid Date';
      }
      return format(date, formatStr);
    } catch (error) {
      console.error('Error formatting date:', dateString, error);
      return 'Invalid Date';
    }
  };

  const formatSafeAmount = (amount: any): string => {
    try {
      // Handle null, undefined, or non-numeric values
      if (amount === null || amount === undefined || amount === '') {
        return '0';
      }

      // Convert to number if it's a string
      const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;

      // Check if it's a valid number
      if (isNaN(numAmount) || !isFinite(numAmount)) {
        console.warn('Invalid amount value:', amount);
        return '0';
      }

      // Format the number
      return numAmount.toLocaleString('en-KE', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
      });
    } catch (error) {
      console.error('Error formatting amount:', amount, error);
      return '0';
    }
  };

  const getStatusBadge = (status: string | undefined | null) => {
    if (!status) {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
          Unknown
        </span>
      );
    }

    switch (status.toLowerCase()) {
      case 'accepted':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            <CheckCircleIcon className="mr-1 h-3 w-3" />
            Accepted
          </span>
        );
      case 'pending':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
            <ClockIcon className="mr-1 h-3 w-3" />
            Pending
          </span>
        );
      case 'rejected':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
            <XCircleIcon className="mr-1 h-3 w-3" />
            Rejected
          </span>
        );
      case 'expired':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            <ExclamationTriangleIcon className="mr-1 h-3 w-3" />
            Expired
          </span>
        );
      default:
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            {status}
          </span>
        );
    }
  };

  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-orange-600 mx-auto mb-4"></div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Loading</h3>
          <p className="text-sm text-gray-600">
            Please wait...
          </p>
        </div>
      </div>
    );
  }

  if (status === 'unauthenticated') {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <ExclamationTriangleIcon className="h-16 w-16 text-orange-600 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Authentication Required</h3>
          <p className="text-sm text-gray-600 mb-4">
            Please log in to access this page.
          </p>
          <Link
            href="/admin/login"
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
          >
            Go to Login
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4 sm:space-y-6">
      {/* Page Header */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
            <div>
              <h1 className="text-xl sm:text-2xl font-bold text-gray-900">Quotes</h1>
              <p className="text-sm sm:text-base text-gray-600 mt-1">
                Manage all customer quotes and proposals
              </p>
            </div>
            <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
              <button
                onClick={fetchQuotes}
                disabled={loading}
                className="inline-flex items-center justify-center px-3 sm:px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <ArrowPathIcon className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                Refresh
              </button>
              <Link
                href="/admin/quotes/new"
                className="inline-flex items-center justify-center px-3 sm:px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <PlusIcon className="h-4 w-4 mr-2" />
                New Quote
              </Link>
            </div>
          </div>
        </div>

        {/* Summary Stats */}
        <div className="px-4 sm:px-6 py-4 bg-gray-50 border-b border-gray-200">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3 sm:gap-4">
            <div className="text-center">
              <div className="text-lg sm:text-2xl font-bold text-blue-600">{quotes.length}</div>
              <div className="text-xs sm:text-sm text-gray-600">Total Quotes</div>
            </div>
            <div className="text-center">
              <div className="text-lg sm:text-2xl font-bold text-green-600">
                {quotes.filter(q => q.status === 'accepted').length}
              </div>
              <div className="text-xs sm:text-sm text-gray-600">Accepted</div>
            </div>
            <div className="text-center">
              <div className="text-lg sm:text-2xl font-bold text-yellow-600">
                {quotes.filter(q => q.status === 'pending').length}
              </div>
              <div className="text-xs sm:text-sm text-gray-600">Pending</div>
            </div>
            <div className="text-center">
              <div className="text-lg sm:text-2xl font-bold text-gray-900">
                KES {quotes.reduce((sum, q) => sum + q.totalAmount, 0).toLocaleString()}
              </div>
              <div className="text-xs sm:text-sm text-gray-600">Total Value</div>
            </div>
          </div>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <XCircleIcon className="h-5 w-5 text-red-400" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error Loading Quotes</h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{error}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Quotes Display */}
      <div className="bg-white shadow rounded-lg overflow-hidden">
        {loading ? (
          <div className="px-4 sm:px-6 py-12 text-center text-gray-500">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-base font-medium">Loading quotes...</p>
          </div>
        ) : !Array.isArray(quotes) || quotes.length === 0 ? (
          <div className="px-4 sm:px-6 py-12 text-center text-gray-500">
            <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-300 mb-4" />
            <p className="text-base font-medium">No quotes found</p>
            <p className="text-sm mt-2">
              <Link
                href="/admin/quotes/new"
                className="text-blue-600 hover:text-blue-500"
              >
                Create your first quote
              </Link>
            </p>
          </div>
        ) : (
          <>
            {/* Mobile Card View */}
            <div className="block sm:hidden divide-y divide-gray-200">
              {quotes.map((quote) => (
                <div key={quote.id} className="px-4 py-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {quote.quoteNumber}
                      </p>
                      <p className="text-xs text-gray-500">
                        {quote.items?.length || 0} item{(quote.items?.length || 0) !== 1 ? 's' : ''}
                      </p>
                    </div>
                    <div className="ml-3">
                      {getStatusBadge(quote.status)}
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-3 mb-3">
                    <div>
                      <p className="text-xs text-gray-500">Customer</p>
                      <p className="text-sm font-medium text-gray-900 truncate">{quote.customerName}</p>
                      <p className="text-xs text-gray-500">{quote.phoneNumber}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-xs text-gray-500">Amount</p>
                      <p className="text-sm font-medium text-gray-900">KES {formatSafeAmount(quote.totalAmount)}</p>
                      {quote.discountAmount && quote.discountAmount > 0 && (
                        <p className="text-xs text-green-600">Discount: KES {formatSafeAmount(quote.discountAmount)}</p>
                      )}
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-3 mb-3">
                    <div>
                      <p className="text-xs text-gray-500">Issued</p>
                      <p className="text-sm text-gray-900">{formatSafeDate(quote.issuedAt)}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-xs text-gray-500">Valid Until</p>
                      <p className="text-sm text-gray-900">{formatSafeDate(quote.validUntil)}</p>
                    </div>
                  </div>
                  
                  <div className="flex flex-wrap gap-2">
                    <Link
                      href={`/admin/quotes/${quote.id}`}
                      className="inline-flex items-center px-2.5 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
                    >
                      View
                    </Link>
                    <a
                      href={`/api/quotes/${quote.id}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="inline-flex items-center px-2.5 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
                    >
                      Print
                    </a>
                    {quote.status === 'pending' && (
                      <Link
                        href={`/admin/invoices/new?quoteId=${quote.id}`}
                        className="inline-flex items-center px-2.5 py-1.5 border border-orange-300 shadow-sm text-xs font-medium rounded text-orange-700 bg-white hover:bg-orange-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
                      >
                        Convert
                      </Link>
                    )}
                    <button
                      onClick={() => handleDelete(quote.id)}
                      disabled={deleting === quote.id}
                      className="inline-flex items-center px-2.5 py-1.5 border border-red-300 shadow-sm text-xs font-medium rounded text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
                    >
                      <TrashIcon className="h-3 w-3 mr-1" />
                      {deleting === quote.id ? 'Deleting...' : 'Delete'}
                    </button>
                  </div>
                </div>
              ))}
            </div>

            {/* Desktop Table View */}
            <div className="hidden sm:block overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Quote #
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Customer
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Amount
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Date
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Valid Until
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {quotes.map((quote) => (
                    <tr key={quote.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {quote.quoteNumber}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <div className="text-sm font-medium text-gray-900">
                          {quote.customerName}
                        </div>
                        <div className="text-sm text-gray-500">
                          {quote.phoneNumber}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <div className="text-sm font-medium text-gray-900">
                          KES {formatSafeAmount(quote.totalAmount)}
                        </div>
                        {quote.discountAmount && quote.discountAmount > 0 && (
                          <div className="text-sm text-green-600">
                            Discount: KES {formatSafeAmount(quote.discountAmount)}
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatSafeDate(quote.issuedAt)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatSafeDate(quote.validUntil)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {getStatusBadge(quote.status)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <div className="flex flex-wrap justify-end gap-2">
                          <Link
                            href={`/admin/quotes/${quote.id}`}
                            className="inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                          >
                            View
                          </Link>
                          <a
                            href={`/api/quotes/${quote.id}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                          >
                            Print
                          </a>
                          {quote.status === 'pending' && (
                            <Link
                              href={`/admin/invoices/new?quoteId=${quote.id}`}
                              className="inline-flex items-center px-3 py-1.5 border border-orange-300 shadow-sm text-xs font-medium rounded text-orange-700 bg-white hover:bg-orange-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
                            >
                              Convert
                            </Link>
                          )}
                          <button
                            onClick={() => handleDelete(quote.id)}
                            disabled={deleting === quote.id}
                            className="inline-flex items-center px-3 py-1.5 border border-red-300 shadow-sm text-xs font-medium rounded text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
                          >
                            <TrashIcon className="h-3 w-3 mr-1" />
                            {deleting === quote.id ? 'Deleting...' : 'Delete'}
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </>
        )}
      </div>
    </div>
  );
}
