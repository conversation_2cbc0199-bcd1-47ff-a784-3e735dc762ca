'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  ArrowLeftIcon,
  DocumentTextIcon,
  PlusIcon,
  TrashIcon,
  XCircleIcon
} from '@heroicons/react/24/outline';
import { useNotification } from '@/contexts/NotificationContext';
import { useAuth } from '@/hooks/useAuth';
import { format, addDays } from 'date-fns';

interface Service {
  id: string;
  name: string;
  description: string | null;
  price: number;
  category: string;
}

interface QuoteItem {
  serviceId?: string;
  quantity: number;
  unitPrice: number;
  description?: string;
  itemName?: string;
  itemType: 'service' | 'custom';
}

export default function NewQuotePage() {
  const router = useRouter();
  const { user, isLoading } = useAuth();
  const { showNotification } = useNotification();

  // Form state
  const [customerName, setCustomerName] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [email, setEmail] = useState('');
  const [validUntil, setValidUntil] = useState(format(addDays(new Date(), 30), 'yyyy-MM-dd'));
  const [notes, setNotes] = useState('');
  const [discountType, setDiscountType] = useState<'percentage' | 'fixed' | ''>('');
  const [discountValue, setDiscountValue] = useState(0);

  // Items state
  const [items, setItems] = useState<QuoteItem[]>([{
    itemType: 'service',
    quantity: 1,
    unitPrice: 0,
    description: ''
  }]);

  // Other state
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch services
  const fetchServices = useCallback(async () => {
    try {
      const response = await fetch('/api/admin/services');
      if (!response.ok) {
        throw new Error('Failed to fetch services');
      }
      const result = await response.json();
      setServices(result.success ? result.data : []);
    } catch (err) {
      console.error('Error fetching services:', err);
      showNotification('error', 'Failed to load services');
    } finally {
      setLoading(false);
    }
  }, [showNotification]);

  useEffect(() => {
    if (user) {
      fetchServices();
    } else if (!isLoading && !user) {
      router.push('/admin/login');
    }
  }, [user, isLoading, fetchServices, router]);

  // Item handlers
  const handleServiceChange = (index: number, serviceId: string) => {
    const service = services.find(s => s.id === serviceId);
    setItems(prev => prev.map((item, i) => 
      i === index 
        ? { 
            ...item, 
            serviceId, 
            unitPrice: service ? service.price : 0,
            description: service ? service.description || '' : ''
          }
        : item
    ));
  };

  const handleItemTypeChange = (index: number, itemType: 'service' | 'custom') => {
    setItems(prev => prev.map((item, i) => 
      i === index 
        ? { 
            ...item, 
            itemType,
            serviceId: itemType === 'service' ? item.serviceId : undefined,
            itemName: itemType === 'custom' ? item.itemName : undefined,
            unitPrice: itemType === 'service' ? item.unitPrice : 0
          }
        : item
    ));
  };

  const handleItemNameChange = (index: number, itemName: string) => {
    setItems(prev => prev.map((item, i) => 
      i === index ? { ...item, itemName } : item
    ));
  };

  const handleQuantityChange = (index: number, quantity: number) => {
    setItems(prev => prev.map((item, i) => 
      i === index ? { ...item, quantity } : item
    ));
  };

  const handleUnitPriceChange = (index: number, unitPrice: number) => {
    setItems(prev => prev.map((item, i) => 
      i === index ? { ...item, unitPrice } : item
    ));
  };

  const handleDescriptionChange = (index: number, description: string) => {
    setItems(prev => prev.map((item, i) => 
      i === index ? { ...item, description } : item
    ));
  };

  const addItem = () => {
    setItems(prev => [...prev, {
      itemType: 'service',
      quantity: 1,
      unitPrice: 0,
      description: ''
    }]);
  };

  const removeItem = (index: number) => {
    if (items.length > 1) {
      setItems(prev => prev.filter((_, i) => i !== index));
    }
  };

  // Calculations
  const calculateSubtotal = () => {
    return items.reduce((sum, item) => sum + (item.quantity * item.unitPrice), 0);
  };

  const calculateDiscount = () => {
    const subtotal = calculateSubtotal();
    if (discountType === 'percentage') {
      return (subtotal * discountValue) / 100;
    } else if (discountType === 'fixed') {
      return discountValue;
    }
    return 0;
  };

  const calculateTotal = () => {
    return calculateSubtotal() - calculateDiscount();
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);
    setError(null);

    try {
      const quoteData = {
        customerName,
        phoneNumber,
        email: email || null,
        validUntil,
        notes: notes || null,
        discountType: discountType || null,
        discountValue: discountType ? discountValue : null,
        items: items.map(item => ({
          itemType: item.itemType,
          serviceId: item.itemType === 'service' ? item.serviceId : null,
          itemName: item.itemType === 'custom' ? item.itemName : null,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          description: item.description || null
        }))
      };

      const response = await fetch('/api/admin/quotes', {
        method: 'POST',
        body: JSON.stringify(quoteData),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || 'Failed to create quote');
      }

      const result = await response.json();
      showNotification('success', 'Quote created successfully');
      
      // Navigate to the created quote
      if (result.success && result.data?.id) {
        router.push(`/admin/quotes/${result.data.id}`);
      } else {
        router.push('/admin/quotes');
      }
    } catch (err) {
      console.error('Error creating quote:', err);
      setError(err instanceof Error ? err.message : 'Failed to create quote');
      showNotification('error', 'Failed to create quote');
    } finally {
      setSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <p className="text-red-600">Not authenticated. Redirecting to login...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4 sm:space-y-6">
      {/* Header */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
          <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-4 space-y-2 sm:space-y-0">
            <Link
              href="/admin/quotes"
              className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 self-start"
            >
              <ArrowLeftIcon className="h-4 w-4 mr-1" />
              Back to Quotes
            </Link>
            <div>
              <h1 className="text-xl sm:text-2xl font-bold text-gray-900">Create New Quote</h1>
              <p className="text-sm sm:text-base text-gray-600">Generate a professional quote for your customer</p>
            </div>
          </div>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <XCircleIcon className="h-5 w-5 text-red-400" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error Creating Quote</h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{error}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Quote Form */}
      <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-6">
        <div className="grid grid-cols-1 xl:grid-cols-3 gap-4 sm:gap-6">
          {/* Main Form */}
          <div className="xl:col-span-2 space-y-4 sm:space-y-6">
            {/* Customer Information */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
                <h2 className="text-base sm:text-lg font-medium text-gray-900">Customer Information</h2>
              </div>
              <div className="px-4 sm:px-6 py-4">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
                  <div className="sm:col-span-2">
                    <label htmlFor="customerName" className="block text-sm font-medium text-gray-700">
                      Customer Name *
                    </label>
                    <input
                      type="text"
                      id="customerName"
                      name="customerName"
                      required
                      value={customerName}
                      onChange={(e) => setCustomerName(e.target.value)}
                      className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 text-sm sm:text-base"
                      placeholder="Enter customer name"
                    />
                  </div>
                  <div>
                    <label htmlFor="phoneNumber" className="block text-sm font-medium text-gray-700">
                      Phone Number *
                    </label>
                    <input
                      type="tel"
                      id="phoneNumber"
                      name="phoneNumber"
                      required
                      value={phoneNumber}
                      onChange={(e) => setPhoneNumber(e.target.value)}
                      className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 text-sm sm:text-base"
                      placeholder="e.g., +254 700 000 000"
                    />
                  </div>
                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                      Email Address
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 text-sm sm:text-base"
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Quote Details */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
                <h2 className="text-base sm:text-lg font-medium text-gray-900">Quote Details</h2>
              </div>
              <div className="px-4 sm:px-6 py-4">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
                  <div>
                    <label htmlFor="issuedAt" className="block text-sm font-medium text-gray-700">
                      Issue Date *
                    </label>
                    <input
                      type="date"
                      id="issuedAt"
                      name="issuedAt"
                      required
                      value={format(new Date(), 'yyyy-MM-dd')}
                      readOnly
                      className="mt-1 block w-full border-gray-300 rounded-md shadow-sm bg-gray-50 text-sm sm:text-base"
                    />
                  </div>
                  <div>
                    <label htmlFor="validUntil" className="block text-sm font-medium text-gray-700">
                      Valid Until *
                    </label>
                    <input
                      type="date"
                      id="validUntil"
                      name="validUntil"
                      required
                      value={validUntil}
                      onChange={(e) => setValidUntil(e.target.value)}
                      className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 text-sm sm:text-base"
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Quote Items */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
                  <h2 className="text-base sm:text-lg font-medium text-gray-900">Quote Items</h2>
                  <button
                    type="button"
                    onClick={addItem}
                    className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    <PlusIcon className="h-4 w-4 mr-1" />
                    Add Item
                  </button>
                </div>
              </div>
              <div className="px-4 sm:px-6 py-4">
                {items.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-300 mb-4" />
                    <p className="text-sm">No items added yet</p>
                    <button
                      type="button"
                      onClick={addItem}
                      className="mt-2 text-blue-600 hover:text-blue-500 text-sm"
                    >
                      Add your first item
                    </button>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {items.map((item, index) => (
                      <div key={index} className="border border-gray-200 rounded-lg p-4">
                        <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between space-y-2 sm:space-y-0 mb-4">
                          <h3 className="text-sm font-medium text-gray-900">Item {index + 1}</h3>
                          <button
                            type="button"
                            onClick={() => removeItem(index)}
                            disabled={items.length === 1}
                            className="inline-flex items-center text-sm text-red-600 hover:text-red-500 self-start sm:self-auto disabled:opacity-50"
                          >
                            <TrashIcon className="h-4 w-4 mr-1" />
                            Remove
                          </button>
                        </div>
                        
                        <div className="space-y-4">
                          <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                              Item Type
                            </label>
                            <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4">
                              <label className="inline-flex items-center">
                                <input
                                  type="radio"
                                  name={`itemType-${index}`}
                                  value="service"
                                  checked={item.itemType === 'service'}
                                  onChange={(e) => handleItemTypeChange(index, e.target.value as 'service' | 'custom')}
                                  className="form-radio h-4 w-4 text-blue-600"
                                />
                                <span className="ml-2 text-sm text-gray-700">Service</span>
                              </label>
                              <label className="inline-flex items-center">
                                <input
                                  type="radio"
                                  name={`itemType-${index}`}
                                  value="custom"
                                  checked={item.itemType === 'custom'}
                                  onChange={(e) => handleItemTypeChange(index, e.target.value as 'service' | 'custom')}
                                  className="form-radio h-4 w-4 text-blue-600"
                                />
                                <span className="ml-2 text-sm text-gray-700">Custom Item</span>
                              </label>
                            </div>
                          </div>

                          {item.itemType === 'service' ? (
                            <div>
                              <label className="block text-sm font-medium text-gray-700">
                                Select Service
                              </label>
                              <select
                                value={item.serviceId || ''}
                                onChange={(e) => handleServiceChange(index, e.target.value)}
                                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 text-sm sm:text-base"
                              >
                                <option value="">Select a service...</option>
                                {services.map((service) => (
                                  <option key={service.id} value={service.id}>
                                    {service.name} - KES {service.price.toLocaleString()}
                                  </option>
                                ))}
                              </select>
                            </div>
                          ) : (
                            <div>
                              <label className="block text-sm font-medium text-gray-700">
                                Item Name
                              </label>
                              <input
                                type="text"
                                value={item.itemName || ''}
                                onChange={(e) => handleItemNameChange(index, e.target.value)}
                                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 text-sm sm:text-base"
                                placeholder="Enter item name"
                              />
                            </div>
                          )}

                          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                            <div>
                              <label className="block text-sm font-medium text-gray-700">
                                Quantity
                              </label>
                              <input
                                type="number"
                                min="1"
                                value={item.quantity}
                                onChange={(e) => handleQuantityChange(index, parseInt(e.target.value) || 1)}
                                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 text-sm sm:text-base"
                              />
                            </div>
                            <div>
                              <label className="block text-sm font-medium text-gray-700">
                                Unit Price (KES)
                              </label>
                              <input
                                type="number"
                                min="0"
                                step="0.01"
                                value={item.unitPrice}
                                onChange={(e) => handleUnitPriceChange(index, parseFloat(e.target.value) || 0)}
                                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 text-sm sm:text-base"
                                readOnly={item.itemType === 'service' && !!item.serviceId}
                              />
                            </div>
                            <div>
                              <label className="block text-sm font-medium text-gray-700">
                                Total
                              </label>
                              <div className="mt-1 px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-sm sm:text-base text-gray-900">
                                KES {(item.quantity * item.unitPrice).toLocaleString()}
                              </div>
                            </div>
                          </div>

                          <div>
                            <label className="block text-sm font-medium text-gray-700">
                              Description
                            </label>
                            <textarea
                              value={item.description || ''}
                              onChange={(e) => handleDescriptionChange(index, e.target.value)}
                              rows={2}
                              className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 text-sm sm:text-base"
                              placeholder="Optional description..."
                            />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>

            {/* Discount Section */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
                <h2 className="text-base sm:text-lg font-medium text-gray-900">Discount (Optional)</h2>
              </div>
              <div className="px-4 sm:px-6 py-4">
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      Discount Type
                    </label>
                    <select
                      name="discountType"
                      value={discountType}
                      onChange={(e) => setDiscountType(e.target.value as 'percentage' | 'fixed' | '')}
                      className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 text-sm sm:text-base"
                    >
                      <option value="">No Discount</option>
                      <option value="percentage">Percentage</option>
                      <option value="fixed">Fixed Amount</option>
                    </select>
                  </div>
                  {discountType && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        {discountType === 'percentage' ? 'Percentage (%)' : 'Amount (KES)'}
                      </label>
                      <input
                        type="number"
                        name="discountValue"
                        min="0"
                        max={discountType === 'percentage' ? '100' : undefined}
                        step={discountType === 'percentage' ? '0.1' : '0.01'}
                        value={discountValue}
                        onChange={(e) => setDiscountValue(parseFloat(e.target.value) || 0)}
                        className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 text-sm sm:text-base"
                      />
                    </div>
                  )}
                  {discountType && discountValue > 0 && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Discount Amount
                      </label>
                      <div className="mt-1 px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-sm sm:text-base text-gray-900">
                        KES {calculateDiscount().toLocaleString()}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Notes */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
                <h2 className="text-base sm:text-lg font-medium text-gray-900">Notes (Optional)</h2>
              </div>
              <div className="px-4 sm:px-6 py-4">
                <textarea
                  name="notes"
                  value={notes}
                  onChange={(e) => setNotes(e.target.value)}
                  rows={4}
                  className="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 text-sm sm:text-base"
                  placeholder="Add any additional notes or terms..."
                />
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-4 sm:space-y-6">
            {/* Quote Summary */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
                <h2 className="text-base sm:text-lg font-medium text-gray-900">Quote Summary</h2>
              </div>
              <div className="px-4 sm:px-6 py-4">
                <dl className="space-y-3">
                  <div className="flex justify-between">
                    <dt className="text-sm text-gray-500">Items</dt>
                    <dd className="text-sm font-medium text-gray-900">{items.length}</dd>
                  </div>
                  <div className="flex justify-between">
                    <dt className="text-sm text-gray-500">Subtotal</dt>
                    <dd className="text-sm font-medium text-gray-900">
                      KES {calculateSubtotal().toLocaleString()}
                    </dd>
                  </div>
                  {discountType && discountValue > 0 && (
                    <div className="flex justify-between">
                      <dt className="text-sm text-gray-500">Discount</dt>
                      <dd className="text-sm font-medium text-green-600">
                        -KES {calculateDiscount().toLocaleString()}
                      </dd>
                    </div>
                  )}
                  <div className="border-t border-gray-200 pt-3">
                    <div className="flex justify-between">
                      <dt className="text-base font-medium text-gray-900">Total Amount</dt>
                      <dd className="text-base font-medium text-gray-900">
                        KES {calculateTotal().toLocaleString()}
                      </dd>
                    </div>
                  </div>
                </dl>
              </div>
            </div>

            {/* Actions */}
            <div className="bg-white shadow rounded-lg">
              <div className="px-4 sm:px-6 py-4">
                <div className="space-y-3">
                  <button
                    type="submit"
                    disabled={submitting || loading || items.length === 0}
                    className="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                  >
                    {submitting ? 'Creating Quote...' : 'Create Quote'}
                  </button>
                  <Link
                    href="/admin/quotes"
                    className="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    Cancel
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  );
} 