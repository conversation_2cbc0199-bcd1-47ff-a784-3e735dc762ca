'use client';
import React from 'react';

import { useState, useEffect, useCallback } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  ArrowLeftIcon,
  DocumentTextIcon,
  XCircleIcon,
  CheckCircleIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  PrinterIcon
} from '@heroicons/react/24/outline';
import { useNotification } from '@/contexts/NotificationContext';
import { useAuth } from '@/hooks/useAuth';
import { format } from 'date-fns';

interface QuoteItem {
  id: string;
  quantity: number | null | undefined;
  unitPrice: number | null | undefined;
  totalPrice: number | null | undefined;
  description: string | null;
  itemName?: string | null;
  itemType?: string;
  serviceId?: string | null;
  serviceName?: string;
}

interface Quote {
  id: string;
  quoteNumber: string;
  totalAmount: number | null | undefined;
  subtotalAmount?: number | null | undefined;
  discountType?: string | null;
  discountValue?: number | null;
  discountAmount?: number | null;
  customerName: string;
  phoneNumber: string;
  email: string | null;
  status: string;
  notes: string | null;
  createdAt: string;
  issuedAt: string;
  validUntil: string;
  items: QuoteItem[];
  pdfUrl?: string;
}

export default function QuoteDetailPage() {
  const params = useParams();
  const router = useRouter();
  const quoteId = params?.id as string;
  const { showNotification } = useNotification();
  const { user, isLoading } = useAuth();

  const [quote, setQuote] = useState<Quote | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [submitting, setSubmitting] = useState(false);

  const fetchQuote = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      console.log('Fetching quote with ID:', quoteId);
      console.log('Quote ID type:', typeof quoteId);
      console.log('Quote ID length:', quoteId?.length);
      console.log('Current URL:', window.location.href);

      // First attempt with admin API
      let response = await fetch(`/api/admin/quotes/${quoteId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      });

      console.log('Quote fetch response status:', response.status);
      console.log('Quote fetch response headers:', Object.fromEntries(response.headers.entries()));

      // If admin API fails with auth error, try the public quote API as fallback
      if (!response.ok && (response.status === 401 || response.status === 403)) {
        console.log('Admin API failed with auth error, trying public API...');

        response = await fetch(`/api/quotes/${quoteId}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
        });

        console.log('Public API response status:', response.status);
      }

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Quote fetch error response:', errorText);

        // Try to parse as JSON to get more detailed error
        try {
          const errorData = JSON.parse(errorText);
          console.error('Parsed error data:', errorData);
          setError(errorData.error || `Failed to fetch quote: ${response.status} ${response.statusText}`);
        } catch (parseError) {
          console.error('Could not parse error response as JSON:', parseError);
          setError(`Failed to fetch quote: ${response.status} ${response.statusText}`);
        }

        showNotification('error', 'Failed to load quote');
        return;
      }

      const data = await response.json();
      console.log('Quote data received:', data);

      // Handle different response formats
      if (typeof data === 'string') {
        // If we get HTML from the public API, show an error
        console.error('Received HTML response instead of JSON');
        setError('Quote data format error');
        showNotification('error', 'Failed to load quote data');
        return;
      }

      // Ensure numeric fields are properly set
      const sanitizedData = {
        ...data,
        totalAmount: typeof data.totalAmount === 'number' ? data.totalAmount : 0,
        items: Array.isArray(data.items) ? data.items.map((item: any) => ({
          ...item,
          quantity: typeof item.quantity === 'number' ? item.quantity : 0,
          unitPrice: typeof item.unitPrice === 'number' ? item.unitPrice : 0,
          totalPrice: typeof item.totalPrice === 'number' ? item.totalPrice : 0,
        })) : []
      };

      setQuote(sanitizedData);
    } catch (err) {
      console.error('Error fetching quote:', err);
      console.error('Error type:', typeof err);
      console.error('Error message:', err instanceof Error ? err.message : String(err));
      setError('Failed to load quote. Please try again.');
      showNotification('error', 'Failed to load quote');
    } finally {
      setLoading(false);
    }
  }, [quoteId, showNotification]);

  useEffect(() => {
    console.log('useEffect triggered - isLoading:', isLoading, 'user:', !!user, 'quoteId:', quoteId);

    // Validate quote ID first
    if (!quoteId || quoteId === 'undefined' || quoteId === 'null') {
      console.error('Invalid quote ID:', quoteId);
      setError('Invalid quote ID');
      setLoading(false);
      return;
    }

    // Wait for auth to finish loading
    if (isLoading) {
      console.log('Auth still loading...');
      return;
    }

    // Only fetch data when user is authenticated
    if (user) {
      console.log('User authenticated, fetching quote...');
      fetchQuote();
    } else {
      console.log('User not authenticated, redirecting to login');
      router.push('/admin/login');
    }
  }, [isLoading, user, fetchQuote, router, quoteId]);

  const handleStatusChange = async (status: string) => {
    setSubmitting(true);

    try {
      const response = await fetch('/api/admin/quotes', {
        method: 'PATCH',
        body: JSON.stringify({
          id: quoteId,
          status,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update status');
      }

      const updatedQuote = await response.json();
      setQuote(updatedQuote);
      showNotification('success', 'Quote status updated successfully');
    } catch (err) {
      console.error('Error updating status:', err);
      showNotification('error', err instanceof Error ? err.message : 'Failed to update quote status');
    } finally {
      setSubmitting(false);
    }
  };

  const handleConvertToInvoice = async () => {
    setSubmitting(true);

    try {
      const response = await fetch('/api/admin/quotes/convert', {
        method: 'POST',
        body: JSON.stringify({
          quoteId,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to convert quote to invoice');
      }

      const invoice = await response.json();
      showNotification('success', 'Quote converted to invoice successfully');
      router.push(`/admin/invoices/${invoice.id}`);
    } catch (err) {
      console.error('Error converting quote to invoice:', err);
      showNotification('error', err instanceof Error ? err.message : 'Failed to convert quote to invoice');
    } finally {
      setSubmitting(false);
    }
  };

  const formatSafeDate = (dateString: string | null | undefined, formatStr: string = 'dd/MM/yyyy') => {
    if (!dateString) {
      return 'N/A';
    }

    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        console.warn('Invalid date string:', dateString);
        return 'Invalid Date';
      }
      return format(date, formatStr);
    } catch (error) {
      console.error('Error formatting date:', dateString, error);
      return 'Invalid Date';
    }
  };

  const formatSafeNumber = (value: number | null | undefined) => {
    try {
      if (value === null || value === undefined || isNaN(value) || !isFinite(value)) {
        return '0';
      }
      return value.toLocaleString('en-KE', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
      });
    } catch (error) {
      console.error('Error formatting number:', value, error);
      return '0';
    }
  };

  const getStatusBadge = (status: string | undefined | null) => {
    if (!status) {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
          Unknown
        </span>
      );
    }

    switch (status.toLowerCase()) {
      case 'accepted':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            <CheckCircleIcon className="mr-1 h-3 w-3" />
            Accepted
          </span>
        );
      case 'pending':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
            <ClockIcon className="mr-1 h-3 w-3" />
            Pending
          </span>
        );
      case 'rejected':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
            <XCircleIcon className="mr-1 h-3 w-3" />
            Rejected
          </span>
        );
      case 'expired':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            <ExclamationTriangleIcon className="mr-1 h-3 w-3" />
            Expired
          </span>
        );
      default:
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            {status}
          </span>
        );
    }
  };

  return (
    <div className="space-y-4 sm:space-y-6">
      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <XCircleIcon className="h-5 w-5 text-red-400" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error Loading Quote</h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{error}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Loading State */}
      {loading ? (
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 sm:px-6 py-12 text-center text-gray-500">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-base font-medium">Loading quote...</p>
          </div>
        </div>
      ) : !quote ? (
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 sm:px-6 py-12 text-center text-gray-500">
            <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-300 mb-4" />
            <p className="text-base font-medium">Quote not found</p>
            <p className="text-sm mt-2">
              <Link
                href="/admin/quotes"
                className="text-blue-600 hover:text-blue-500"
              >
                Back to quotes
              </Link>
            </p>
          </div>
        </div>
      ) : (
        <>
          {/* Header */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
                <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-4 space-y-2 sm:space-y-0">
                  <Link
                    href="/admin/quotes"
                    className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 self-start"
                  >
                    <ArrowLeftIcon className="h-4 w-4 mr-1" />
                    Back to Quotes
                  </Link>
                  <div>
                    <h1 className="text-xl sm:text-2xl font-bold text-gray-900">Quote {quote.quoteNumber}</h1>
                    <p className="text-sm sm:text-base text-gray-600">Created on {formatSafeDate(quote.createdAt)}</p>
                  </div>
                </div>
                <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
                  <a
                    href={`/api/quotes/${quote.id}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center justify-center px-3 sm:px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                  >
                    <PrinterIcon className="h-4 w-4 mr-2" />
                    Print
                  </a>
                  {quote.status === 'pending' && (
                    <Link
                      href={`/admin/invoices/new?quoteId=${quote.id}`}
                      className="inline-flex items-center justify-center px-3 sm:px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-600 hover:bg-orange-700"
                    >
                      <CheckCircleIcon className="h-4 w-4 mr-2" />
                      Convert to Invoice
                    </Link>
                  )}
                </div>
              </div>
            </div>

            {/* Status Display */}
            <div className="px-4 sm:px-6 py-4 bg-gray-50 border-b border-gray-200">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
                <div className="flex items-center space-x-4">
                  <span className="text-sm text-gray-500">Status:</span>
                  {getStatusBadge(quote.status)}
                </div>
                <div className="text-sm text-gray-600">
                  Valid until: {formatSafeDate(quote.validUntil)}
                </div>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 xl:grid-cols-3 gap-4 sm:gap-6">
            {/* Quote Details */}
            <div className="xl:col-span-2 space-y-4 sm:space-y-6">
              {/* Customer Information */}
              <div className="bg-white shadow rounded-lg">
                <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
                  <h2 className="text-base sm:text-lg font-medium text-gray-900">Customer Information</h2>
                </div>
                <div className="px-4 sm:px-6 py-4">
                  <dl className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Name</dt>
                      <dd className="mt-1 text-sm text-gray-900 break-words">{quote.customerName}</dd>
                    </div>
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Phone</dt>
                      <dd className="mt-1 text-sm text-gray-900">{quote.phoneNumber}</dd>
                    </div>
                    {quote.email && (
                      <div className="sm:col-span-2">
                        <dt className="text-sm font-medium text-gray-500">Email</dt>
                        <dd className="mt-1 text-sm text-gray-900 break-all">{quote.email}</dd>
                      </div>
                    )}
                  </dl>
                </div>
              </div>

              {/* Quote Items */}
              <div className="bg-white shadow rounded-lg">
                <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
                  <h2 className="text-base sm:text-lg font-medium text-gray-900">Quote Items</h2>
                </div>
                {/* Mobile-friendly items display */}
                <div className="block sm:hidden">
                  <div className="divide-y divide-gray-200">
                    {quote.items && Array.isArray(quote.items) ? quote.items.map((item) => (
                      <div key={item.id} className="px-4 py-4">
                        <div className="flex justify-between items-start mb-2">
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-gray-900 truncate">
                              {item.itemName || item.serviceName || 'Unnamed Item'}
                            </p>
                            <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full mt-1 ${
                              item.itemType === 'custom' 
                                ? 'bg-purple-100 text-purple-800' 
                                : 'bg-blue-100 text-blue-800'
                            }`}>
                              {item.itemType === 'custom' ? 'Custom' : 'Service'}
                            </span>
                          </div>
                          <div className="ml-2 text-right">
                            <p className="text-sm font-medium text-gray-900">
                              KES {formatSafeNumber(item.totalPrice)}
                            </p>
                          </div>
                        </div>
                        {item.description && (
                          <p className="text-xs text-gray-500 mb-2">{item.description}</p>
                        )}
                        <div className="flex justify-between text-xs text-gray-500">
                          <span>Qty: {item.quantity || 0} × KES {formatSafeNumber(item.unitPrice)}</span>
                        </div>
                      </div>
                    )) : (
                      <div className="px-4 py-8 text-center text-sm text-gray-500">
                        {quote.items === undefined ? 'Loading items...' : 'No items found'}
                      </div>
                    )}
                  </div>
                </div>
                {/* Desktop table display */}
                <div className="hidden sm:block overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Item
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Type
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Quantity
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Unit Price
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Total
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {quote.items && Array.isArray(quote.items) ? quote.items.map((item) => (
                        <tr key={item.id}>
                          <td className="px-6 py-4 text-sm text-gray-900">
                            <div>
                              <div className="font-medium">
                                {item.itemName || item.serviceName || 'Unnamed Item'}
                              </div>
                              {item.description && (
                                <div className="text-gray-500 text-xs mt-1">
                                  {item.description}
                                </div>
                              )}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                              item.itemType === 'custom' 
                                ? 'bg-purple-100 text-purple-800' 
                                : 'bg-blue-100 text-blue-800'
                            }`}>
                              {item.itemType === 'custom' ? 'Custom' : 'Service'}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {item.quantity || 0}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            KES {formatSafeNumber(item.unitPrice)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium text-gray-900">
                            KES {formatSafeNumber(item.totalPrice)}
                          </td>
                        </tr>
                      )) : (
                        <tr>
                          <td colSpan={5} className="px-6 py-4 text-center text-sm text-gray-500">
                            {quote.items === undefined ? 'Loading items...' : 'No items found'}
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>

                {/* Totals Section */}
                <div className="bg-gray-50 px-4 sm:px-6 py-4">
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Subtotal</span>
                      <span className="text-sm font-medium text-gray-900">
                        KES {formatSafeNumber(quote.subtotalAmount || quote.totalAmount)}
                      </span>
                    </div>
                    {quote.discountType && quote.discountAmount && quote.discountAmount > 0 && (
                      <div className="flex justify-between">
                        <span className="text-sm text-gray-600">
                          Discount ({quote.discountType === 'percentage' ? `${quote.discountValue}%` : 'Fixed'})
                        </span>
                        <span className="text-sm font-medium text-green-600">
                          -KES {formatSafeNumber(quote.discountAmount)}
                        </span>
                      </div>
                    )}
                    <div className="border-t border-gray-200 pt-2">
                      <div className="flex justify-between">
                        <span className="text-base font-medium text-gray-900">Total Amount</span>
                        <span className="text-base font-medium text-gray-900">
                          KES {formatSafeNumber(quote.totalAmount)}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Notes */}
              {quote.notes && (
                <div className="bg-white shadow rounded-lg">
                  <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
                    <h2 className="text-base sm:text-lg font-medium text-gray-900">Notes</h2>
                  </div>
                  <div className="px-4 sm:px-6 py-4">
                    <p className="text-sm text-gray-700 break-words">{quote.notes}</p>
                  </div>
                </div>
              )}
            </div>

            {/* Sidebar */}
            <div className="space-y-4 sm:space-y-6">
              {/* Quote Information */}
              <div className="bg-white shadow rounded-lg">
                <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
                  <h2 className="text-base sm:text-lg font-medium text-gray-900">Quote Information</h2>
                </div>
                <div className="px-4 sm:px-6 py-4">
                  <dl className="space-y-3">
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Quote Number</dt>
                      <dd className="mt-1 text-sm text-gray-900 break-all">{quote.quoteNumber}</dd>
                    </div>
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Issue Date</dt>
                      <dd className="mt-1 text-sm text-gray-900">{formatSafeDate(quote.issuedAt)}</dd>
                    </div>
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Valid Until</dt>
                      <dd className="mt-1 text-sm text-gray-900">{formatSafeDate(quote.validUntil)}</dd>
                    </div>
                  </dl>
                </div>
              </div>

              {/* Quote Summary */}
              <div className="bg-white shadow rounded-lg">
                <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
                  <h2 className="text-base sm:text-lg font-medium text-gray-900">Quote Summary</h2>
                </div>
                <div className="px-4 sm:px-6 py-4">
                  <dl className="space-y-3">
                    <div className="flex justify-between">
                      <dt className="text-sm text-gray-500">Subtotal</dt>
                      <dd className="text-sm font-medium text-gray-900">
                        KES {formatSafeNumber(quote.subtotalAmount || quote.totalAmount)}
                      </dd>
                    </div>
                    {quote.discountType && quote.discountAmount && quote.discountAmount > 0 && (
                      <div className="flex justify-between">
                        <dt className="text-sm text-gray-500">Discount</dt>
                        <dd className="text-sm font-medium text-green-600">
                          -KES {formatSafeNumber(quote.discountAmount)}
                        </dd>
                      </div>
                    )}
                    <div className="border-t border-gray-200 pt-3">
                      <div className="flex justify-between">
                        <dt className="text-base font-medium text-gray-900">Total Amount</dt>
                        <dd className="text-base font-medium text-gray-900">
                          KES {formatSafeNumber(quote.totalAmount)}
                        </dd>
                      </div>
                    </div>
                  </dl>
                </div>
              </div>

              {/* Status Actions */}
              {quote.status === 'pending' && (
                <div className="bg-white shadow rounded-lg">
                  <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
                    <h2 className="text-base sm:text-lg font-medium text-gray-900">Actions</h2>
                  </div>
                  <div className="px-4 sm:px-6 py-4 space-y-3">
                    <button
                      onClick={() => handleStatusChange('accepted')}
                      disabled={submitting}
                      className="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 disabled:opacity-50"
                    >
                      {submitting ? 'Updating...' : 'Accept Quote'}
                    </button>
                    <button
                      onClick={() => handleStatusChange('rejected')}
                      disabled={submitting}
                      className="w-full inline-flex justify-center items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                    >
                      {submitting ? 'Updating...' : 'Reject Quote'}
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </>
      )}
    </div>
  );
}
