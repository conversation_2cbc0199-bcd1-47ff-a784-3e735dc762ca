'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import SmartPrice from '@/components/SmartPrice';

interface LogoPackage {
  id: string;
  name: string;
  price: number;
  description: string | null;
  features: string[];
  isActive: boolean;
  isPopular: boolean;
  sortOrder: number;
  whatsappMessage: string | null;
  createdAt: string;
  updatedAt: string;
}

interface PackageFormData {
  name: string;
  price: string;
  description: string;
  features: string[];
  isActive: boolean;
  isPopular: boolean;
  sortOrder: string;
  whatsappMessage: string;
}

export default function LogoPackagesAdmin() {
  const [packages, setPackages] = useState<LogoPackage[]>([]);
  const [loading, setLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [editingPackage, setEditingPackage] = useState<LogoPackage | null>(null);
  const [formData, setFormData] = useState<PackageFormData>({
    name: '',
    price: '',
    description: '',
    features: [],
    isActive: true,
    isPopular: false,
    sortOrder: '0',
    whatsappMessage: ''
  });
  const [newFeature, setNewFeature] = useState('');

  // Fetch packages
  const fetchPackages = async () => {
    try {
      const response = await fetch('/api/admin/logo-packages');
      const result = await response.json();
      if (result.success) {
        setPackages(result.data);
      }
    } catch (error) {
      console.error('Error fetching packages:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPackages();
  }, []);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      const url = editingPackage 
        ? `/api/admin/logo-packages/${editingPackage.id}`
        : '/api/admin/logo-packages';
      
      const method = editingPackage ? 'PUT' : 'POST';
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          price: parseFloat(formData.price),
          sortOrder: parseInt(formData.sortOrder),
        }),
      });

      const result = await response.json();
      
      if (result.success) {
        await fetchPackages();
        resetForm();
        setShowForm(false);
        setEditingPackage(null);
      } else {
        alert('Error saving package: ' + result.error);
      }
    } catch (error) {
      console.error('Error saving package:', error);
      alert('Error saving package');
    }
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      name: '',
      price: '',
      description: '',
      features: [],
      isActive: true,
      isPopular: false,
      sortOrder: '0',
      whatsappMessage: ''
    });
    setNewFeature('');
  };

  // Edit package
  const handleEdit = (pkg: LogoPackage) => {
    setEditingPackage(pkg);
    setFormData({
      name: pkg.name,
      price: pkg.price.toString(),
      description: pkg.description || '',
      features: [...pkg.features],
      isActive: pkg.isActive,
      isPopular: pkg.isPopular,
      sortOrder: pkg.sortOrder.toString(),
      whatsappMessage: pkg.whatsappMessage || ''
    });
    setShowForm(true);
  };

  // Delete package
  const handleDelete = async (id: string, name: string) => {
    if (!confirm(`Are you sure you want to delete "${name}"?`)) return;
    
    try {
      const response = await fetch(`/api/admin/logo-packages/${id}`, {
        method: 'DELETE',
      });
      
      const result = await response.json();
      
      if (result.success) {
        await fetchPackages();
      } else {
        alert('Error deleting package: ' + result.error);
      }
    } catch (error) {
      console.error('Error deleting package:', error);
      alert('Error deleting package');
    }
  };

  // Add feature
  const addFeature = () => {
    if (newFeature.trim() && !formData.features.includes(newFeature.trim())) {
      setFormData(prev => ({
        ...prev,
        features: [...prev.features, newFeature.trim()]
      }));
      setNewFeature('');
    }
  };

  // Remove feature
  const removeFeature = (index: number) => {
    setFormData(prev => ({
      ...prev,
      features: prev.features.filter((_, i) => i !== index)
    }));
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#FF5400]"></div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-8">
        <div className="flex justify-between items-center mb-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Logo Packages</h1>
            <p className="text-gray-600 mt-2">Manage your logo design packages</p>
          </div>
          <button
            onClick={() => {
              resetForm();
              setEditingPackage(null);
              setShowForm(true);
            }}
            className="bg-[#FF5400] text-white px-6 py-2 rounded-lg hover:bg-[#E54D00] transition-colors"
          >
            Add New Package
          </button>
        </div>
      </div>

      {/* Package Form Modal */}
      <AnimatePresence>
        {showForm && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
          >
            <motion.div
              initial={{ scale: 0.95, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              exit={{ scale: 0.95, opacity: 0 }}
              className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto"
            >
              <div className="p-6">
                <h2 className="text-2xl font-bold mb-6">
                  {editingPackage ? 'Edit Package' : 'Add New Package'}
                </h2>
                
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Package Name
                      </label>
                      <input
                        type="text"
                        value={formData.name}
                        onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                        className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-[#FF5400] focus:border-transparent"
                        required
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Price (KSH)
                      </label>
                      <input
                        type="number"
                        value={formData.price}
                        onChange={(e) => setFormData(prev => ({ ...prev, price: e.target.value }))}
                        className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-[#FF5400] focus:border-transparent"
                        required
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Description
                    </label>
                    <textarea
                      value={formData.description}
                      onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-[#FF5400] focus:border-transparent"
                      rows={3}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Features
                    </label>
                    <div className="flex gap-2 mb-2">
                      <input
                        type="text"
                        value={newFeature}
                        onChange={(e) => setNewFeature(e.target.value)}
                        placeholder="Add a feature..."
                        className="flex-1 border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-[#FF5400] focus:border-transparent"
                        onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addFeature())}
                      />
                      <button
                        type="button"
                        onClick={addFeature}
                        className="bg-gray-200 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-300 transition-colors"
                      >
                        Add
                      </button>
                    </div>
                    <div className="space-y-2">
                      {formData.features.map((feature, index) => (
                        <div key={index} className="flex items-center justify-between bg-gray-50 p-2 rounded">
                          <span className="text-sm">{feature}</span>
                          <button
                            type="button"
                            onClick={() => removeFeature(index)}
                            className="text-red-500 hover:text-red-700"
                          >
                            Remove
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Sort Order
                      </label>
                      <input
                        type="number"
                        value={formData.sortOrder}
                        onChange={(e) => setFormData(prev => ({ ...prev, sortOrder: e.target.value }))}
                        className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-[#FF5400] focus:border-transparent"
                      />
                    </div>
                    
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="isActive"
                        checked={formData.isActive}
                        onChange={(e) => setFormData(prev => ({ ...prev, isActive: e.target.checked }))}
                        className="mr-2"
                      />
                      <label htmlFor="isActive" className="text-sm font-medium text-gray-700">
                        Active
                      </label>
                    </div>
                    
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="isPopular"
                        checked={formData.isPopular}
                        onChange={(e) => setFormData(prev => ({ ...prev, isPopular: e.target.checked }))}
                        className="mr-2"
                      />
                      <label htmlFor="isPopular" className="text-sm font-medium text-gray-700">
                        Popular
                      </label>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      WhatsApp Message Template
                    </label>
                    <textarea
                      value={formData.whatsappMessage}
                      onChange={(e) => setFormData(prev => ({ ...prev, whatsappMessage: e.target.value }))}
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-[#FF5400] focus:border-transparent"
                      rows={6}
                      placeholder="Template message for WhatsApp orders..."
                    />
                  </div>

                  <div className="flex justify-end space-x-4">
                    <button
                      type="button"
                      onClick={() => setShowForm(false)}
                      className="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      className="px-6 py-2 bg-[#FF5400] text-white rounded-lg hover:bg-[#E54D00] transition-colors"
                    >
                      {editingPackage ? 'Update' : 'Create'} Package
                    </button>
                  </div>
                </form>
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Packages List */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {packages.map((pkg) => (
          <motion.div
            key={pkg.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className={`bg-white rounded-lg border shadow-sm p-6 ${
              pkg.isPopular ? 'ring-2 ring-[#FF5400]' : ''
            }`}
          >
            <div className="flex justify-between items-start mb-4">
              <div>
                <h3 className="text-xl font-semibold text-gray-900">{pkg.name}</h3>
                <div className="mt-2">
                  <SmartPrice kesAmount={pkg.price} size="lg" />
                </div>
              </div>
              <div className="flex space-x-2">
                {pkg.isPopular && (
                  <span className="bg-[#FF5400] text-white text-xs px-2 py-1 rounded-full">
                    Popular
                  </span>
                )}
                {!pkg.isActive && (
                  <span className="bg-gray-400 text-white text-xs px-2 py-1 rounded-full">
                    Inactive
                  </span>
                )}
              </div>
            </div>

            <p className="text-gray-600 text-sm mb-4">{pkg.description}</p>

            <div className="mb-4">
              <h4 className="font-medium text-gray-900 mb-2">Features:</h4>
              <ul className="space-y-1">
                {pkg.features.slice(0, 3).map((feature, index) => (
                  <li key={index} className="text-sm text-gray-600 flex items-start">
                    <span className="text-green-500 mr-2">✓</span>
                    {feature}
                  </li>
                ))}
                {pkg.features.length > 3 && (
                  <li className="text-sm text-gray-500">
                    +{pkg.features.length - 3} more features
                  </li>
                )}
              </ul>
            </div>

            <div className="flex space-x-2">
              <button
                onClick={() => handleEdit(pkg)}
                className="flex-1 bg-blue-50 text-blue-600 py-2 px-4 rounded-lg text-sm hover:bg-blue-100 transition-colors"
              >
                Edit
              </button>
              <button
                onClick={() => handleDelete(pkg.id, pkg.name)}
                className="flex-1 bg-red-50 text-red-600 py-2 px-4 rounded-lg text-sm hover:bg-red-100 transition-colors"
              >
                Delete
              </button>
            </div>

            <div className="mt-4 text-xs text-gray-500">
              Created: {new Date(pkg.createdAt).toLocaleDateString()}
            </div>
          </motion.div>
        ))}
      </div>

      {packages.length === 0 && (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No packages found</h3>
          <p className="text-gray-500 mb-4">Get started by creating your first logo package.</p>
          <button
            onClick={() => {
              resetForm();
              setEditingPackage(null);
              setShowForm(true);
            }}
            className="bg-[#FF5400] text-white px-6 py-2 rounded-lg hover:bg-[#E54D00] transition-colors"
          >
            Add First Package
          </button>
        </div>
      )}
    </div>
  );
} 