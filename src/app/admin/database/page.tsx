'use client';

import React, { useState, useEffect } from 'react';
import { 
  ArrowDownTrayIcon, 
  TrashIcon, 
  CircleStackIcon,
  CloudIcon,
  LockClosedIcon,
  ArchiveBoxIcon,
  EyeIcon,
  EyeSlashIcon,
  ArrowPathIcon,
  ShieldCheckIcon,
  ClockIcon,
  ServerIcon,
  CheckIcon
} from '@heroicons/react/24/outline';
import { toast } from 'react-hot-toast';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  Database, 
  Download, 
  Trash2, 
  RefreshCw, 
  Cloud, 
  Clock, 
  HardDrive,
  Shield,
  AlertTriangle,
  CheckCircle,
  Upload,
  RotateCcw
} from 'lucide-react';
import { formatBytes, formatDate } from '@/utils/formatters';

interface BackupFile {
  id: string;
  filename: string;
  description: string | null;
  size: number;
  path: string;
  type: string;
  status: string;
  createdBy: string | null;
  createdAt: string;
  updatedAt: string;
}

interface BackupMetrics {
  totalBackups: number;
  totalSize: number;
  successRate: number;
  averageTime: number;
  cloudBackups: number;
  encryptedBackups: number;
}

interface EnterpriseBackupOptions {
  description: string;
  encrypt: boolean;
  compress: boolean;
  uploadToCloud: boolean;
  verifyIntegrity: boolean;
}

interface BackupData {
  id: string;
  filename: string;
  description: string;
  size: number;
  createdAt: string;
  type: 'manual' | 'scheduled';
  status: 'completed' | 'failed' | 'in_progress';
}

interface BackupStats {
  totalBackups: number;
  totalSize: number;
  lastBackup: string | null;
}

export default function DatabasePage() {
  const [backups, setBackups] = useState<BackupData[]>([]);
  const [isLoadingBackups, setIsLoadingBackups] = useState(false);
  const [metrics, setMetrics] = useState<BackupMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [backupLoading, setBackupLoading] = useState(false);
  const [restoreLoading, setRestoreLoading] = useState(false);
  const [enterpriseMode, setEnterpriseMode] = useState(true);
  const [backupOptions, setBackupOptions] = useState<EnterpriseBackupOptions>({
    description: '',
    encrypt: true,
    compress: true,
    uploadToCloud: true,
    verifyIntegrity: true,
  });
  const [s3Backups, setS3Backups] = useState<any[]>([]);
  const [loadingS3Backups, setLoadingS3Backups] = useState(false);
  const [showS3Backups, setShowS3Backups] = useState(false);
  const [s3RestoreLoading, setS3RestoreLoading] = useState(false);
  const [lastRefreshed, setLastRefreshed] = useState<Date | null>(null);
  
  // Bulk delete state
  const [selectedBackups, setSelectedBackups] = useState<Set<string>>(new Set());
  const [bulkDeleteLoading, setBulkDeleteLoading] = useState(false);

  const [stats, setStats] = useState<BackupStats>({ totalBackups: 0, totalSize: 0, lastBackup: null });
  const [creating, setCreating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [operationLoading, setOperationLoading] = useState<string | null>(null);

  // Format file size
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Auto-refresh backups every 30 seconds when not loading
  useEffect(() => {
    let interval: NodeJS.Timeout;
    
    if (!isLoadingBackups && !backupLoading) {
      interval = setInterval(() => {
        console.log('🔄 Auto-refreshing backup list...');
        fetchBackups();
      }, 30000); // 30 seconds
    }
    
    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [isLoadingBackups, backupLoading]);

  // Initial load
  useEffect(() => {
    fetchBackups();
    if (enterpriseMode) {
      fetchMetrics();
    }
  }, [enterpriseMode]);

  // Fetch backups
  const fetchBackups = async () => {
    setIsLoadingBackups(true);
    try {
      const response = await fetch('/api/admin/database/s3-backup');
      const data = await response.json();
      
      if (data.success) {
        setBackups(data.data);
        setStats(data.stats);
        setLastRefreshed(new Date());
      } else {
        setError(data.error || 'Failed to fetch backups');
      }
    } catch (err) {
      setError('Failed to connect to backup service');
      console.error('Error fetching backups:', err);
    } finally {
      setIsLoadingBackups(false);
      setLoading(false);
    }
  };

  // Fetch metrics
  const fetchMetrics = async () => {
    try {
      const response = await fetch('/api/admin/database/enterprise-backup');
      if (response.ok) {
        const data = await response.json();
        setMetrics(data.metrics);
      }
    } catch (error) {
      console.error('Error fetching metrics:', error);
    }
  };

  // Fetch S3 backups
  const fetchS3Backups = async () => {
    setLoadingS3Backups(true);
    try {
      const response = await fetch('/api/admin/database/s3-backups');
      if (response.ok) {
        const data = await response.json();
        setS3Backups(data.backups || []);
      } else {
        toast.error('Failed to fetch S3 backups');
      }
    } catch (error) {
      console.error('Error fetching S3 backups:', error);
      toast.error('Error fetching S3 backups');
    } finally {
      setLoadingS3Backups(false);
    }
  };

  const createBackup = async () => {
    try {
      setCreating(true);
      setError(null);
      const response = await fetch('/api/admin/database/s3-backup', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ description: backupOptions.description || 'Manual backup' })
      });
      
      const data = await response.json();
      
      if (data.success) {
        setBackupOptions(prev => ({ ...prev, description: '' }));
        await fetchBackups();
      } else {
        setError(data.error || 'Failed to create backup');
      }
    } catch (err) {
      setError('Failed to create backup');
      console.error('Error creating backup:', err);
    } finally {
      setCreating(false);
    }
  };

  const downloadBackup = async (backupId: string) => {
    try {
      setOperationLoading(backupId);
      const response = await fetch(`/api/admin/database/s3-backup/${backupId}?action=download`);
      const data = await response.json();
      
      if (data.success) {
        // Open download URL in new tab
        window.open(data.downloadUrl, '_blank');
      } else {
        setError(data.error || 'Failed to download backup');
      }
    } catch (err) {
      setError('Failed to download backup');
      console.error('Error downloading backup:', err);
    } finally {
      setOperationLoading(null);
    }
  };

  const deleteBackup = async (backupId: string) => {
    if (!confirm('Are you sure you want to delete this backup? This action cannot be undone.')) {
      return;
    }

    try {
      setOperationLoading(backupId);
      const response = await fetch(`/api/admin/database/s3-backup/${backupId}`, {
        method: 'DELETE'
      });
      
      const data = await response.json();
      
      if (data.success) {
        await fetchBackups();
      } else {
        setError(data.error || 'Failed to delete backup');
      }
    } catch (err) {
      setError('Failed to delete backup');
      console.error('Error deleting backup:', err);
    } finally {
      setOperationLoading(null);
    }
  };

  const restoreBackup = async (backupId: string) => {
    if (!confirm('Are you sure you want to restore this backup? This will overwrite the current database and cannot be undone.')) {
      return;
    }

    try {
      setOperationLoading(backupId);
      const response = await fetch(`/api/admin/database/s3-backup/${backupId}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'restore' })
      });
      
      const data = await response.json();
      
      if (data.success) {
        alert('Database restored successfully. Please refresh the page.');
        window.location.reload();
      } else {
        setError(data.error || 'Failed to restore backup');
      }
    } catch (err) {
      setError('Failed to restore backup');
      console.error('Error restoring backup:', err);
    } finally {
      setOperationLoading(null);
    }
  };

  const getStatusBadge = (status: string) => {
    const baseClasses = "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium";
    let statusClasses = "";
    let statusText = "";
    
    switch (status) {
      case 'completed':
        statusClasses = `${baseClasses} bg-green-100 text-green-800`;
        statusText = "Completed";
        break;
      case 'failed':
        statusClasses = `${baseClasses} bg-red-100 text-red-800`;
        statusText = "Failed";
        break;
      case 'in_progress':
        statusClasses = `${baseClasses} bg-yellow-100 text-yellow-800`;
        statusText = "In Progress";
        break;
      default:
        statusClasses = `${baseClasses} bg-gray-100 text-gray-800`;
        statusText = "Unknown";
    }
    
    return (
      <span className={statusClasses}>
        {statusText}
      </span>
    );
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'automatic': return <ServerIcon className="h-4 w-4" />;
      case 'manual': return <CircleStackIcon className="h-4 w-4" />;
      case 'pre-restore': return <ArrowPathIcon className="h-4 w-4" />;
      case 'scheduled': return <ClockIcon className="h-4 w-4" />;
      default: return <CircleStackIcon className="h-4 w-4" />;
    }
  };

  // Toggle individual backup selection
  const toggleBackupSelection = (backupId: string) => {
    const newSelected = new Set(selectedBackups);
    if (newSelected.has(backupId)) {
      newSelected.delete(backupId);
    } else {
      newSelected.add(backupId);
    }
    setSelectedBackups(newSelected);
  };

  // Toggle select all backups
  const toggleSelectAll = () => {
    if (selectedBackups.size === backups.length) {
      setSelectedBackups(new Set());
    } else {
      setSelectedBackups(new Set(backups.map(backup => backup.id)));
    }
  };

  // Clear selection when backups change
  useEffect(() => {
    setSelectedBackups(new Set());
  }, [backups]);

  const bulkDeleteBackups = async () => {
    if (selectedBackups.size === 0) return;
    
    if (!confirm(`Delete ${selectedBackups.size} backup(s) permanently?`)) return;

    setBulkDeleteLoading(true);
    const toastId = toast.loading(`Deleting ${selectedBackups.size} backup(s)...`);

    try {
      console.log(`🗑️ Bulk deleting ${selectedBackups.size} backups:`, Array.from(selectedBackups));
      
      // Delete backups in parallel for better performance
      const deletePromises = Array.from(selectedBackups).map(backupId =>
        fetch(`/api/admin/database/backup/${backupId}`, { method: 'DELETE' })
          .then(response => ({ backupId, response }))
      );

      const results = await Promise.all(deletePromises);
      
      let successCount = 0;
      let errorCount = 0;
      const errors: string[] = [];

      for (const { backupId, response } of results) {
        if (response.ok) {
          successCount++;
        } else {
          errorCount++;
          const data = await response.json();
          errors.push(`${backupId}: ${data.error || 'Unknown error'}`);
        }
      }

      if (successCount > 0) {
        const message = errorCount > 0 
          ? `${successCount} backup(s) deleted successfully, ${errorCount} failed`
          : `${successCount} backup(s) deleted successfully`;
        toast.success(message, { id: toastId });
        
        // Immediate refresh after bulk deletion to show only remaining backups
        console.log(`✅ Bulk delete completed (${successCount} successful, ${errorCount} failed), refreshing backup list...`);
        await fetchBackups();
        
        if (enterpriseMode) {
          await fetchMetrics();
        }
        
        // Clear all selections
        setSelectedBackups(new Set());
      } else {
        toast.error(`All ${errorCount} delete operations failed`, { id: toastId });
      }

      if (errors.length > 0) {
        console.error('Bulk delete errors:', errors);
      }

    } catch (error) {
      console.error('Bulk delete error:', error);
      toast.error('Bulk delete failed - Network or server error', { id: toastId });
    } finally {
      setBulkDeleteLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Database Management</h1>
          <p className="text-muted-foreground mt-1">
            Enterprise-grade S3 backup and restore operations
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
            <Cloud className="w-3 h-3 mr-1" />
            S3 Storage
          </Badge>
        </div>
      </div>

      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-red-700">
              <AlertTriangle className="w-5 h-5" />
              <span>{error}</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Backups</p>
                <p className="text-2xl font-bold">{stats.totalBackups}</p>
              </div>
              <Database className="w-8 h-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Size</p>
                <p className="text-2xl font-bold">{formatBytes(stats.totalSize)}</p>
              </div>
              <HardDrive className="w-8 h-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Cloud Storage</p>
                <p className="text-2xl font-bold text-green-600">Active</p>
              </div>
              <Cloud className="w-8 h-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Last Backup</p>
                <p className="text-sm font-bold">
                  {stats.lastBackup ? formatDate(stats.lastBackup) : 'Never'}
                </p>
              </div>
              <Clock className="w-8 h-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Create Backup */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Upload className="w-5 h-5 text-blue-500" />
              Create Backup
            </CardTitle>
            <CardDescription>Create a new database backup to S3 storage</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Input
              placeholder="Enter backup description (optional)"
              value={backupOptions.description}
              onChange={(e) => setBackupOptions(prev => ({ ...prev, description: e.target.value }))}
              disabled={creating}
            />
            <Button 
              onClick={createBackup} 
              disabled={creating}
              className="w-full"
            >
              {creating ? (
                <>
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                  Creating Backup...
                </>
              ) : (
                <>
                  <Upload className="w-4 h-4 mr-2" />
                  Create Backup
                </>
              )}
            </Button>
          </CardContent>
        </Card>

        {/* Quick Stats */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="w-5 h-5 text-green-500" />
              Backup Status
            </CardTitle>
            <CardDescription>Current backup system status</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <span className="text-sm">S3 Connection</span>
              <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                <CheckCircle className="w-3 h-3 mr-1" />
                Connected
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">Encryption</span>
              <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                <Shield className="w-3 h-3 mr-1" />
                Enabled
              </Badge>
            </div>
            <Button 
              variant="outline" 
              onClick={fetchBackups}
              disabled={loading}
              className="w-full"
            >
              {loading ? (
                <>
                  <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                  Refreshing...
                </>
              ) : (
                <>
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Refresh Backups
                </>
              )}
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Backup List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="w-5 h-5" />
            Recent Backups
          </CardTitle>
          <CardDescription>
            {backups.length} backup(s) found • Last updated: {formatDate(new Date().toISOString())}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <RefreshCw className="w-6 h-6 animate-spin" />
              <span className="ml-2">Loading backups...</span>
            </div>
          ) : backups.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <Database className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <p>No backups found. Create your first backup to get started.</p>
            </div>
          ) : (
            <div className="space-y-3">
              {backups.map((backup) => (
                <div
                  key={backup.id}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-center gap-4">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <Database className="w-5 h-5 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="font-medium">{backup.filename}</h3>
                      <p className="text-sm text-muted-foreground">{backup.description}</p>
                      <div className="flex items-center gap-4 mt-1">
                        <span className="text-xs text-muted-foreground">
                          {formatBytes(backup.size)}
                        </span>
                        <span className="text-xs text-muted-foreground">
                          {formatDate(backup.createdAt)}
                        </span>
                        <Badge 
                          variant={backup.status === 'completed' ? 'default' : 'destructive'}
                          className="text-xs"
                        >
                          {backup.status}
                        </Badge>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => downloadBackup(backup.id)}
                      disabled={operationLoading === backup.id}
                    >
                      <Download className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => restoreBackup(backup.id)}
                      disabled={operationLoading === backup.id}
                    >
                      <RotateCcw className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => deleteBackup(backup.id)}
                      disabled={operationLoading === backup.id}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
