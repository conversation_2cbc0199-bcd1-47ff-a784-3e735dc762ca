'use client';
import React from 'react';

import { useState, useEffect, useCallback, useMemo } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import {
  PlusIcon,
  ArrowPathIcon,
  PencilIcon,
  TrashIcon,
  EyeIcon,
  StarIcon,

  MagnifyingGlassIcon,
  FunnelIcon,
  Squares2X2Icon,
  ListBulletIcon
} from '@heroicons/react/24/outline';
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';
import { PortfolioItem, PORTFOLIO_CATEGORIES, PortfolioCategory } from '@/types/portfolio';
import { useNotification } from '@/contexts/NotificationContext';
import { withErrorHandling } from '@/utils/errorHandling';
import { sortPortfolioItems } from '@/utils/portfolioUtils';

// Types for enhanced functionality
interface FilterOptions {
  category: string;
  featured: 'all' | 'featured' | 'not-featured';
  sortBy: 'title' | 'category' | 'createdAt' | 'featured';
  sortOrder: 'asc' | 'desc';
}

interface ViewOptions {
  layout: 'grid' | 'list';
  itemsPerPage: number;
}

// Constants
const ITEMS_PER_PAGE_OPTIONS = [12, 24, 48, 96];
const DEFAULT_FILTER_OPTIONS: FilterOptions = {
  category: 'all',
  featured: 'all',
  sortBy: 'createdAt',
  sortOrder: 'desc'
};
const DEFAULT_VIEW_OPTIONS: ViewOptions = {
  layout: 'grid',
  itemsPerPage: 24
};

export default function PortfolioPage() {
  // Core state
  const [portfolioItems, setPortfolioItems] = useState<PortfolioItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [deleteItemId, setDeleteItemId] = useState<string | null>(null);


  // Enhanced state
  const [searchQuery, setSearchQuery] = useState('');
  const [filterOptions, setFilterOptions] = useState<FilterOptions>(DEFAULT_FILTER_OPTIONS);
  const [viewOptions, setViewOptions] = useState<ViewOptions>(DEFAULT_VIEW_OPTIONS);
  const [currentPage, setCurrentPage] = useState(1);
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());
  const [bulkActionLoading, setBulkActionLoading] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);

  const { showNotification } = useNotification();

  // Memoized filtered and sorted items
  const filteredAndSortedItems = useMemo(() => {
    let filtered = portfolioItems;

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase().trim();
      filtered = filtered.filter(item =>
        item.title.toLowerCase().includes(query) ||
        item.description?.toLowerCase().includes(query) ||
        item.category.toLowerCase().includes(query)
      );
    }

    // Apply category filter
    if (filterOptions.category !== 'all') {
      filtered = filtered.filter(item => item.category === filterOptions.category);
    }

    // Apply featured filter
    if (filterOptions.featured !== 'all') {
      filtered = filtered.filter(item => {
        const isFeatured = !!item.featured;
        return filterOptions.featured === 'featured' ? isFeatured : !isFeatured;
      });
    }

    // Apply sorting
    return sortPortfolioItems(filtered, filterOptions.sortBy, filterOptions.sortOrder);
  }, [portfolioItems, searchQuery, filterOptions]);

  // Pagination calculations
  const totalPages = Math.ceil(filteredAndSortedItems.length / viewOptions.itemsPerPage);
  const startIndex = (currentPage - 1) * viewOptions.itemsPerPage;
  const endIndex = startIndex + viewOptions.itemsPerPage;
  const paginatedItems = filteredAndSortedItems.slice(startIndex, endIndex);

  // Reset page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [searchQuery, filterOptions, viewOptions.itemsPerPage]);

  // Enhanced fetch function with better error handling and caching
  const fetchPortfolioItems = useCallback(
    withErrorHandling(
      async (category?: string, retryCount = 0) => {
        const maxRetries = 3;
        setLoading(true);
        setError('');

        try {
          // Build URL with parameters
          let url = '/api/admin/portfolio';
          const params = new URLSearchParams();

          // Add category parameter if provided and not 'all'
          if (category && category !== 'all') {
            params.append('category', category);
          }



          // Add timestamp to prevent caching
          params.append('t', Date.now().toString());

          // Append parameters to URL
          if (params.toString()) {
            url += `?${params.toString()}`;
          }

          console.log(`Fetching portfolio items from: ${url} (attempt ${retryCount + 1}/${maxRetries + 1})`);

          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 second timeout

          const response = await fetch(url, {
            cache: 'no-store',
            signal: controller.signal,
            headers: {
              'Cache-Control': 'no-cache, no-store, must-revalidate',
              'Pragma': 'no-cache',
              'Accept': 'application/json',
            }
          });

          clearTimeout(timeoutId);

          if (!response.ok) {
            let errorMessage = `Failed to fetch portfolio items: ${response.status} ${response.statusText}`;

            try {
              const errorData = await response.json();
              if (errorData.error) {
                errorMessage = errorData.error;
              }
            } catch (parseError) {
              console.error('Could not parse error response:', parseError);
            }

            throw new Error(errorMessage);
          }

          const data = await response.json();

          if (!Array.isArray(data)) {
            throw new Error('Invalid response format: expected an array of portfolio items');
          }

          console.log(`Successfully received ${data.length} portfolio items`);
          setPortfolioItems(data);
          setError('');

          return data;

        } catch (err) {
          console.error(`Error fetching portfolio items (attempt ${retryCount + 1}):`, err);

          let errorMessage = 'Failed to load portfolio items';

          if (err instanceof Error) {
            if (err.name === 'AbortError') {
              errorMessage = 'Request timed out. Please check your connection and try again.';
            } else {
              errorMessage = err.message;
            }
          }

          // Retry logic
          if (retryCount < maxRetries && err instanceof Error && err.name !== 'AbortError') {
            console.log(`Retrying in ${(retryCount + 1) * 1000}ms...`);
            setTimeout(() => {
              fetchPortfolioItems(category, retryCount + 1);
            }, (retryCount + 1) * 1000); // Exponential backoff
            return;
          }

          setError(errorMessage);

          // Show notification for persistent errors
          if (retryCount >= maxRetries) {
            showNotification('error', 'Error', `${errorMessage} (after ${maxRetries + 1} attempts)`);
          }

          throw err;
        } finally {
          setLoading(false);
        }
      },
      'Failed to fetch portfolio items',
      {
        showToast: false, // We handle notifications manually
        onError: (error) => {
          console.error('Portfolio fetch error:', error);
        }
      }
    ),
    [showNotification]
  );

  useEffect(() => {
    fetchPortfolioItems(filterOptions.category);
  }, [fetchPortfolioItems, filterOptions.category]);

  // Enhanced filter handlers
  const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  }, []);

  const handleFilterChange = useCallback((key: keyof FilterOptions, value: any) => {
    setFilterOptions(prev => ({ ...prev, [key]: value }));
  }, []);

  const handleViewChange = useCallback((key: keyof ViewOptions, value: any) => {
    setViewOptions(prev => ({ ...prev, [key]: value }));
  }, []);

  // Bulk selection handlers
  const handleSelectAll = useCallback(() => {
    if (selectedItems.size === paginatedItems.length) {
      setSelectedItems(new Set());
    } else {
      setSelectedItems(new Set(paginatedItems.map(item => item.id)));
    }
  }, [selectedItems.size, paginatedItems]);

  const handleSelectItem = useCallback((id: string) => {
    setSelectedItems(prev => {
      const newSet = new Set(prev);
      if (newSet.has(id)) {
        newSet.delete(id);
      } else {
        newSet.add(id);
      }
      return newSet;
    });
  }, []);

  // Bulk actions
  const handleBulkDelete = useCallback(async () => {
    if (selectedItems.size === 0) return;

    const confirmed = window.confirm(
      `Are you sure you want to delete ${selectedItems.size} selected items? This action cannot be undone.`
    );

    if (!confirmed) return;

    setBulkActionLoading(true);
    try {
      const results: { id: string; title: string; success: boolean; error?: string; s3Warning?: string }[] = [];
      for (const id of selectedItems) {
        const item = portfolioItems.find(item => item.id === id);
        const itemTitle = item?.title || id;
        try {
          const response = await fetch(`/api/admin/portfolio/${id}`, {
            method: 'DELETE',
            headers: { 'Content-Type': 'application/json' },
          });
          if (!response.ok) {
            let errorMsg = `Failed to delete item ${id}`;
            try {
              const errorData = await response.json();
              if (errorData.error) errorMsg = errorData.error;
            } catch {}
            results.push({ id, title: itemTitle, success: false, error: errorMsg });
            continue;
          }
          const result = await response.json();
          results.push({ id, title: itemTitle, success: true, s3Warning: result.s3Warning });
        } catch (err) {
          results.push({ id, title: itemTitle, success: false, error: err instanceof Error ? err.message : String(err) });
        }
      }

      // Update state
      const deletedIds = results.filter(r => r.success).map(r => r.id);
      setPortfolioItems(prev => prev.filter(item => !deletedIds.includes(item.id)));
      setSelectedItems(new Set());

      // Show summary notification
      const successCount = results.filter(r => r.success).length;
      const failCount = results.filter(r => !r.success).length;
      const s3WarnCount = results.filter(r => r.s3Warning).length;
      let summary = `Deleted: ${successCount}`;
      if (failCount) summary += `, Failed: ${failCount}`;
      if (s3WarnCount) summary += `, S3 Warnings: ${s3WarnCount}`;
      showNotification(
        failCount ? 'error' : 'success',
        'Bulk Delete Results',
        summary
      );
      // Optionally, show details in console
      if (failCount || s3WarnCount) {
        console.warn('Bulk delete details:', results);
      }
    } catch (error) {
      console.error('Bulk delete error:', error);
      showNotification('error', 'Error', 'Failed to delete some items. Please try again.');
    } finally {
      setBulkActionLoading(false);
    }
  }, [selectedItems, portfolioItems, showNotification]);

  const handleBulkToggleFeatured = useCallback(async (featured: boolean) => {
    if (selectedItems.size === 0) return;

    setBulkActionLoading(true);
    try {
      const updatePromises = Array.from(selectedItems).map(async (id) => {
        const item = portfolioItems.find(item => item.id === id);
        if (!item) return null;

        const response = await fetch(`/api/admin/portfolio/${id}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ ...item, featured }),
        });

        if (!response.ok) {
          throw new Error(`Failed to update item ${id}`);
        }

        return await response.json();
      });

      const updatedItems = (await Promise.all(updatePromises)).filter(Boolean);

      // Update state
      setPortfolioItems(prev =>
        prev.map(item => {
          const updated = updatedItems.find(updated => updated.id === item.id);
          return updated || item;
        })
      );

      setSelectedItems(new Set());

      showNotification(
        'success',
        'Success',
        `Successfully ${featured ? 'featured' : 'unfeatured'} ${updatedItems.length} items`
      );
    } catch (error) {
      console.error('Bulk toggle featured error:', error);
      showNotification('error', 'Error', 'Failed to update some items. Please try again.');
    } finally {
      setBulkActionLoading(false);
    }
  }, [selectedItems, portfolioItems, showNotification]);

  const handleDeleteClick = useCallback((id: string) => {
    setDeleteItemId(id);
  }, []);

  const handleConfirmDelete = useCallback(async () => {
    if (!deleteItemId) return;

    // Find the item to delete - it should exist since we're showing the modal
    const itemToDelete = portfolioItems.find(item => item.id === deleteItemId);

    if (!itemToDelete) {
      console.error('Item to delete not found in current portfolio items:', deleteItemId);
      showNotification('error', 'Error', 'Portfolio item not found. Please refresh the page and try again.');
      setDeleteItemId(null);
      return;
    }

    const itemTitle = itemToDelete.title || 'Unknown item';

    try {
      setDeleteLoading(true);
      console.log(`Attempting to delete portfolio item: ${deleteItemId} (${itemTitle})`);

      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout

      const response = await fetch(`/api/admin/portfolio/${deleteItemId}`, {
        method: 'DELETE',
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ updatedAt: itemToDelete.updatedAt }),
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        let errorMessage = `Failed to delete portfolio item: ${response.status} ${response.statusText}`;

        if (response.status === 409) {
          showNotification('error', 'Conflict', 'This item was modified by another user. Please refresh and try again.');
          setDeleteItemId(null);
          setDeleteLoading(false);
          return;
        }

        try {
          const errorData = await response.json();
          if (errorData.error) {
            errorMessage = errorData.error;
          }
          if (errorData.details) {
            console.error('Delete error details:', errorData.details);
          }
        } catch (parseError) {
          console.error('Could not parse error response:', parseError);
        }

        throw new Error(errorMessage);
      }

      const result = await response.json();
      console.log('Delete operation successful:', result);

      // Remove the deleted item from the state
      setPortfolioItems(prevItems => prevItems.filter(item => item.id !== deleteItemId));

      // Also remove from selected items if it was selected
      setSelectedItems(prevSelected => {
        const newSelected = new Set(prevSelected);
        newSelected.delete(deleteItemId);
        return newSelected;
      });

      // Close the modal
      setDeleteItemId(null);

      // Force refresh the portfolio data to ensure consistency
      setTimeout(() => {
        fetchPortfolioItems(filterOptions.category);
      }, 500);

      showNotification(
        'success',
        'Success',
        `Portfolio item "${itemTitle}" deleted successfully`
      );

      if (result.s3Warning) {
        showNotification(
          'info',
          'Storage Warning',
          result.s3Warning
        );
      }
    } catch (err) {
      console.error('Error deleting portfolio item:', err);

      let errorMessage = 'Failed to delete portfolio item';

      if (err instanceof Error) {
        if (err.name === 'AbortError') {
          errorMessage = 'Delete operation timed out. Please try again.';
        } else {
          errorMessage = err.message;
        }
      }

      showNotification('error', 'Error', errorMessage);
      // Don't close the modal on error so user can retry
    } finally {
      setDeleteLoading(false);
    }
  }, [deleteItemId, portfolioItems, showNotification]);

  const handleCancelDelete = useCallback(() => {
    setDeleteItemId(null);
  }, []);



  const handleToggleFeatured = useCallback(async (id: string, currentFeatured: boolean) => {
    const item = portfolioItems.find(item => item.id === id);
    if (!item) return;

    // Optimistic update
    const optimisticUpdate = { ...item, featured: !currentFeatured };
    setPortfolioItems(prevItems =>
      prevItems.map(item => item.id === id ? optimisticUpdate : item)
    );

    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000);

      const response = await fetch(`/api/admin/portfolio/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...item,
          featured: !currentFeatured,
        }),
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw new Error(`Failed to update portfolio item: ${response.status} ${response.statusText}`);
      }

      const updatedItem = await response.json();

      // Confirm the update with server response
      setPortfolioItems(prevItems =>
        prevItems.map(item => item.id === id ? updatedItem : item)
      );

      showNotification(
        'success',
        'Success',
        `Item ${updatedItem.featured ? 'marked as featured' : 'removed from featured'}`
      );
    } catch (err) {
      console.error('Error updating portfolio item:', err);

      // Rollback optimistic update
      setPortfolioItems(prevItems =>
        prevItems.map(item => item.id === id ? { ...item, featured: currentFeatured } : item)
      );

      const errorMessage = err instanceof Error ? err.message : 'Failed to update portfolio item';
      showNotification('error', 'Error', errorMessage);
    }
  }, [portfolioItems, showNotification]);

  // Pagination handlers
  const handlePageChange = useCallback((page: number) => {
    setCurrentPage(page);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }, []);

  const handlePreviousPage = useCallback(() => {
    if (currentPage > 1) {
      handlePageChange(currentPage - 1);
    }
  }, [currentPage, handlePageChange]);

  const handleNextPage = useCallback(() => {
    if (currentPage < totalPages) {
      handlePageChange(currentPage + 1);
    }
  }, [currentPage, totalPages, handlePageChange]);

  const isRefreshing = loading || deleteLoading;

  return (
    <div className="space-y-8">
      {/* Header Section */}
      <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-4 mb-6 mt-2">
        <div>
          <h1 className="text-2xl font-bold text-slate-800">Portfolio Management</h1>
          <p className="text-sm text-slate-600 mt-1">
            Manage your portfolio items stored in S3 with advanced filtering and bulk operations
          </p>
        </div>

        <div className="flex flex-wrap items-center gap-3">
          <button
            onClick={() => fetchPortfolioItems(filterOptions.category)}
            className="p-2 rounded-lg text-slate-500 hover:text-slate-700 hover:bg-slate-100 transition-colors"
            disabled={loading}
            aria-label="Refresh portfolio items"
          >
            <ArrowPathIcon className={`h-5 w-5 ${loading ? 'animate-spin' : ''}`} />
          </button>



          <Link
            href="/admin/portfolio/new"
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-500 hover:bg-orange-600 transition-colors"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Add Portfolio Item
          </Link>
        </div>
      </div>



      {/* Enhanced Search and Filter Section */}
      <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
        <div className="space-y-4">
          {/* Search Bar */}
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Search portfolio items..."
              value={searchQuery}
              onChange={handleSearchChange}
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-orange-500 focus:border-orange-500 text-sm"
            />
          </div>

          {/* Filters Row */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Category Filter */}
            <div>
              <label htmlFor="category-filter" className="block text-sm font-medium text-gray-700 mb-1">
                Category
              </label>
              <select
                id="category-filter"
                value={filterOptions.category}
                onChange={(e) => handleFilterChange('category', e.target.value)}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500 text-sm"
              >
                <option value="all">All Categories</option>
                {PORTFOLIO_CATEGORIES.map(category => (
                  <option key={category.value} value={category.value}>
                    {category.label}
                  </option>
                ))}
              </select>
            </div>

            {/* Featured Filter */}
            <div>
              <label htmlFor="featured-filter" className="block text-sm font-medium text-gray-700 mb-1">
                Featured Status
              </label>
              <select
                id="featured-filter"
                value={filterOptions.featured}
                onChange={(e) => handleFilterChange('featured', e.target.value)}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500 text-sm"
              >
                <option value="all">All Items</option>
                <option value="featured">Featured Only</option>
                <option value="not-featured">Not Featured</option>
              </select>
            </div>

            {/* Sort By */}
            <div>
              <label htmlFor="sort-by" className="block text-sm font-medium text-gray-700 mb-1">
                Sort By
              </label>
              <select
                id="sort-by"
                value={filterOptions.sortBy}
                onChange={(e) => handleFilterChange('sortBy', e.target.value)}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500 text-sm"
              >
                <option value="createdAt">Date Created</option>
                <option value="title">Title</option>
                <option value="category">Category</option>
                <option value="featured">Featured Status</option>
              </select>
            </div>

            {/* Sort Order */}
            <div>
              <label htmlFor="sort-order" className="block text-sm font-medium text-gray-700 mb-1">
                Sort Order
              </label>
              <select
                id="sort-order"
                value={filterOptions.sortOrder}
                onChange={(e) => handleFilterChange('sortOrder', e.target.value)}
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500 text-sm"
              >
                <option value="desc">Descending</option>
                <option value="asc">Ascending</option>
              </select>
            </div>
          </div>

          {/* View Options and Results Info */}
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 pt-4 border-t border-gray-200">
            <div className="flex flex-wrap items-center gap-6">
              {/* Layout Toggle */}
              <div className="flex items-center gap-3">
                <span className="text-sm font-medium text-gray-700 whitespace-nowrap">View:</span>
                <div className="flex rounded-md shadow-sm border border-gray-300">
                  <button
                    onClick={() => handleViewChange('layout', 'grid')}
                    className={`px-3 py-2 text-sm font-medium rounded-l-md border-0 transition-colors ${
                      viewOptions.layout === 'grid'
                        ? 'bg-orange-50 text-orange-700 ring-1 ring-orange-500'
                        : 'bg-white text-gray-700 hover:bg-gray-50'
                    }`}
                    title="Grid view"
                  >
                    <Squares2X2Icon className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => handleViewChange('layout', 'list')}
                    className={`px-3 py-2 text-sm font-medium rounded-r-md border-0 border-l border-gray-300 transition-colors ${
                      viewOptions.layout === 'list'
                        ? 'bg-orange-50 text-orange-700 ring-1 ring-orange-500'
                        : 'bg-white text-gray-700 hover:bg-gray-50'
                    }`}
                    title="List view"
                  >
                    <ListBulletIcon className="h-4 w-4" />
                  </button>
                </div>
              </div>

              {/* Items Per Page */}
              <div className="flex items-center gap-3">
                <span className="text-sm font-medium text-gray-700 whitespace-nowrap">Show:</span>
                <div className="relative">
                  <select
                    value={viewOptions.itemsPerPage}
                    onChange={(e) => handleViewChange('itemsPerPage', parseInt(e.target.value))}
                    className="appearance-none px-3 py-2 pr-8 border border-gray-300 rounded-md text-sm bg-white focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-orange-500 transition-colors min-w-[70px]"
                  >
                    {ITEMS_PER_PAGE_OPTIONS.map(option => (
                      <option key={option} value={option}>{option}</option>
                    ))}
                  </select>
                  <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                    <svg className="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </div>
                </div>
              </div>
            </div>

            {/* Results Info */}
            <div className="text-sm text-gray-600 whitespace-nowrap">
              Showing {startIndex + 1}-{Math.min(endIndex, filteredAndSortedItems.length)} of {filteredAndSortedItems.length} items
              {searchQuery && ` (filtered from ${portfolioItems.length} total)`}
            </div>
          </div>
        </div>
      </div>

      {/* Bulk Actions Bar */}
      {selectedItems.size > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
            <div className="flex items-center gap-3">
              <span className="text-sm font-medium text-blue-900">
                {selectedItems.size} item{selectedItems.size !== 1 ? 's' : ''} selected
              </span>
              <button
                onClick={() => setSelectedItems(new Set())}
                className="text-sm text-blue-700 hover:text-blue-900 underline"
              >
                Clear selection
              </button>
            </div>

            <div className="flex flex-wrap items-center gap-2">
              <button
                onClick={() => handleBulkToggleFeatured(true)}
                disabled={bulkActionLoading}
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-yellow-700 bg-yellow-100 hover:bg-yellow-200 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <StarIcon className="h-4 w-4 mr-1" />
                Mark Featured
              </button>

              <button
                onClick={() => handleBulkToggleFeatured(false)}
                disabled={bulkActionLoading}
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-gray-700 bg-gray-100 hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <StarIcon className="h-4 w-4 mr-1" />
                Remove Featured
              </button>

              <button
                onClick={handleBulkDelete}
                disabled={bulkActionLoading}
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-red-700 bg-red-100 hover:bg-red-200 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <TrashIcon className="h-4 w-4 mr-1" />
                Delete Selected
              </button>
            </div>
          </div>
        </div>
      )}

      {error && (
        <div className="bg-red-50 border-l-4 border-red-400 p-5 rounded-md shadow-sm mb-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-700 font-medium">{error}</p>
            </div>
          </div>
        </div>
      )}

      {loading && portfolioItems.length === 0 ? (
        <div className="text-center py-16 bg-white rounded-lg shadow-sm border border-gray-200">
          <svg className="animate-spin h-10 w-10 text-orange-500 mx-auto" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <p className="mt-4 text-sm font-medium text-slate-500">Loading portfolio items...</p>
        </div>
      ) : filteredAndSortedItems.length === 0 ? (
        <div className="text-center py-16 bg-white rounded-lg shadow-sm border border-gray-200">
          <svg className="mx-auto h-16 w-16 text-slate-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
          <h3 className="mt-4 text-lg font-medium text-slate-800">No portfolio items found</h3>
          <p className="mt-2 text-slate-500 max-w-md mx-auto">
            {searchQuery || filterOptions.category !== 'all' || filterOptions.featured !== 'all'
              ? 'No items match your current filters. Try adjusting your search or filter criteria.'
              : 'Get started by adding your first portfolio item.'}
          </p>
          <div className="mt-8 space-x-3">
            {(searchQuery || filterOptions.category !== 'all' || filterOptions.featured !== 'all') && (
              <button
                onClick={() => {
                  setSearchQuery('');
                  setFilterOptions(DEFAULT_FILTER_OPTIONS);
                }}
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors"
              >
                Clear Filters
              </button>
            )}
            <Link
              href="/admin/portfolio/new"
              className="inline-flex items-center px-5 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-500 hover:bg-orange-600 transition-colors"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              Add Portfolio Item
            </Link>
          </div>
        </div>
      ) : (
        <div className="bg-white shadow-sm rounded-lg overflow-hidden border border-gray-200 relative">
          {isRefreshing && (
            <div className="fixed top-0 left-0 w-full z-50 flex items-center justify-center bg-black bg-opacity-30 h-16">
              <div className="flex items-center space-x-2">
                <svg className="animate-spin h-6 w-6 text-orange-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span className="text-orange-700 font-medium">Refreshing portfolio...</span>
              </div>
            </div>
          )}

          {/* Bulk Selection Header */}
          {paginatedItems.length > 0 && (
            <div className="px-6 py-3 bg-gray-50 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={selectedItems.size === paginatedItems.length && paginatedItems.length > 0}
                    onChange={handleSelectAll}
                    className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"
                  />
                  <span className="ml-2 text-sm text-gray-700">
                    Select all on this page
                  </span>
                </label>
                <span className="text-sm text-gray-500">
                  {selectedItems.size} of {filteredAndSortedItems.length} selected
                </span>
              </div>
            </div>
          )}

          {/* Portfolio Items Grid/List */}
          <div className={`p-6 ${
            viewOptions.layout === 'grid'
              ? 'grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4'
              : 'space-y-4'
          }`}>
            {paginatedItems.map((item) => (
              <div
                key={item.id}
                className={`relative bg-white rounded-lg overflow-hidden shadow-sm border border-gray-200 hover:shadow-md transition-all duration-200 ${
                  selectedItems.has(item.id) ? 'ring-2 ring-orange-500 border-orange-500' : ''
                } ${viewOptions.layout === 'list' ? 'flex items-center' : ''}`}
              >
                {/* Selection Checkbox */}
                <div className="absolute top-2 left-2 z-10">
                  <input
                    type="checkbox"
                    checked={selectedItems.has(item.id)}
                    onChange={() => handleSelectItem(item.id)}
                    className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded shadow-sm"
                  />
                </div>

                {/* Image Section */}
                <div className={`relative bg-gray-100 ${
                  viewOptions.layout === 'grid' ? 'aspect-square' : 'w-24 h-24 flex-shrink-0'
                }`}>
                  <Image
                    src={item.imageSrc}
                    alt={item.alt || item.title}
                    fill
                    className="object-cover"
                    sizes={viewOptions.layout === 'grid'
                      ? "(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1024px) 33vw, 25vw"
                      : "96px"
                    }
                  />

                  {/* Featured Badge */}
                  {item.featured && (
                    <div className="absolute top-2 right-2">
                      <div className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs font-medium flex items-center">
                        <StarIconSolid className="h-3 w-3 mr-1" />
                        Featured
                      </div>
                    </div>
                  )}
                </div>

                {/* Content Section */}
                <div className={`p-4 ${viewOptions.layout === 'list' ? 'flex-1' : ''}`}>
                  <div className={viewOptions.layout === 'list' ? 'flex justify-between items-start' : ''}>
                    <div className={viewOptions.layout === 'list' ? 'flex-1 min-w-0' : ''}>
                      <h3 className={`font-medium text-gray-900 ${
                        viewOptions.layout === 'grid' ? 'text-lg truncate' : 'text-base'
                      }`}>
                        {item.title}
                      </h3>
                      <p className="text-sm text-gray-500 mt-1 capitalize">{item.category}</p>

                      {viewOptions.layout === 'list' && item.description && (
                        <p className="text-sm text-gray-600 mt-2 line-clamp-2">
                          {item.description}
                        </p>
                      )}

                      <div className={`${viewOptions.layout === 'grid' ? 'mt-4' : 'mt-2'} text-xs text-gray-500`}>
                        Created: {new Date(item.createdAt).toLocaleDateString()}
                      </div>
                    </div>

                    {/* Actions */}
                    <div className={`flex ${
                      viewOptions.layout === 'grid'
                        ? 'justify-between items-center mt-4'
                        : 'items-center space-x-2 ml-4'
                    }`}>
                      {viewOptions.layout === 'grid' && (
                        <button
                          onClick={() => handleToggleFeatured(item.id, !!item.featured)}
                          className={`p-1.5 rounded-full ${
                            item.featured ? 'bg-yellow-100 text-yellow-600' : 'bg-gray-100 text-gray-500'
                          } hover:bg-gray-200 transition-colors`}
                          title={item.featured ? 'Remove from featured' : 'Mark as featured'}
                        >
                          {item.featured ? (
                            <StarIconSolid className="h-4 w-4" />
                          ) : (
                            <StarIcon className="h-4 w-4" />
                          )}
                        </button>
                      )}

                      <div className="flex space-x-1">
                        {viewOptions.layout === 'list' && (
                          <button
                            onClick={() => handleToggleFeatured(item.id, !!item.featured)}
                            className={`p-2 rounded-full ${
                              item.featured ? 'bg-yellow-100 text-yellow-600' : 'bg-gray-100 text-gray-500'
                            } hover:bg-gray-200 transition-colors`}
                            title={item.featured ? 'Remove from featured' : 'Mark as featured'}
                          >
                            {item.featured ? (
                              <StarIconSolid className="h-4 w-4" />
                            ) : (
                              <StarIcon className="h-4 w-4" />
                            )}
                          </button>
                        )}

                        <Link
                          href={`/portfolio?item=${item.id}`}
                          target="_blank"
                          className="text-slate-400 hover:text-slate-600 transition-colors p-2 rounded-full hover:bg-slate-100"
                          aria-label="View portfolio item"
                        >
                          <EyeIcon className="h-4 w-4" />
                        </Link>

                        <Link
                          href={`/admin/portfolio/${item.id}/edit`}
                          className="text-blue-400 hover:text-blue-600 transition-colors p-2 rounded-full hover:bg-blue-50"
                          aria-label="Edit portfolio item"
                        >
                          <PencilIcon className="h-4 w-4" />
                        </Link>

                        <button
                          onClick={() => handleDeleteClick(item.id)}
                          disabled={loading}
                          className="text-red-400 hover:text-red-600 transition-colors p-2 rounded-full hover:bg-red-50 disabled:opacity-50 disabled:cursor-not-allowed"
                          aria-label="Delete portfolio item"
                          title="Delete this portfolio item"
                        >
                          <TrashIcon className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6 rounded-lg shadow-sm">
          <div className="flex-1 flex justify-between sm:hidden">
            <button
              onClick={handlePreviousPage}
              disabled={currentPage === 1}
              className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>
            <button
              onClick={handleNextPage}
              disabled={currentPage === totalPages}
              className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>

          <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p className="text-sm text-gray-700">
                Showing page <span className="font-medium">{currentPage}</span> of{' '}
                <span className="font-medium">{totalPages}</span>
              </p>
            </div>

            <div>
              <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                <button
                  onClick={handlePreviousPage}
                  disabled={currentPage === 1}
                  className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <span className="sr-only">Previous</span>
                  <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </button>

                {/* Page Numbers */}
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  let pageNumber;
                  if (totalPages <= 5) {
                    pageNumber = i + 1;
                  } else if (currentPage <= 3) {
                    pageNumber = i + 1;
                  } else if (currentPage >= totalPages - 2) {
                    pageNumber = totalPages - 4 + i;
                  } else {
                    pageNumber = currentPage - 2 + i;
                  }

                  return (
                    <button
                      key={pageNumber}
                      onClick={() => handlePageChange(pageNumber)}
                      className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                        pageNumber === currentPage
                          ? 'z-10 bg-orange-50 border-orange-500 text-orange-600'
                          : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                      }`}
                    >
                      {pageNumber}
                    </button>
                  );
                })}

                <button
                  onClick={handleNextPage}
                  disabled={currentPage === totalPages}
                  className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  <span className="sr-only">Next</span>
                  <svg className="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                  </svg>
                </button>
              </nav>
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {deleteItemId && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-md w-full p-6 shadow-xl">
            <div className="flex items-center mb-4">
              <div className="flex-shrink-0">
                <svg className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <h3 className="ml-3 text-lg font-medium text-gray-900">Confirm Deletion</h3>
            </div>

            {(() => {
              const itemToDelete = portfolioItems.find(item => item.id === deleteItemId);
              return (
                <div className="mb-6">
                  <p className="text-gray-500 mb-4">
                    Are you sure you want to delete this portfolio item? This action cannot be undone.
                  </p>

                  {itemToDelete && (
                    <div className="bg-gray-50 rounded-lg p-4 border">
                      <div className="flex items-center space-x-3">
                        <div className="relative w-16 h-16 bg-gray-200 rounded-lg overflow-hidden flex-shrink-0">
                          <Image
                            src={itemToDelete.imageSrc}
                            alt={itemToDelete.alt || itemToDelete.title}
                            fill
                            className="object-cover"
                            sizes="64px"
                          />
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900 truncate">
                            {itemToDelete.title}
                          </p>
                          <p className="text-sm text-gray-500 capitalize">
                            {itemToDelete.category}
                          </p>
                          <p className="text-xs text-gray-400">
                            Created: {new Date(itemToDelete.createdAt).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                    </div>
                  )}

                  <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                    <div className="flex">
                      <div className="flex-shrink-0">
                        <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                        </svg>
                      </div>
                      <div className="ml-3">
                        <p className="text-sm text-yellow-700">
                          <strong>Warning:</strong> This will also delete the associated image from storage.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })()}

            <div className="flex justify-end space-x-3">
              <button
                onClick={handleCancelDelete}
                disabled={deleteLoading}
                className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Cancel
              </button>
              <button
                onClick={handleConfirmDelete}
                disabled={deleteLoading}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
              >
                {deleteLoading ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Deleting...
                  </>
                ) : (
                  'Delete'
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
