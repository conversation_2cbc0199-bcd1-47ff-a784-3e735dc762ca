'use client';

import React, { useState, useRef, FormEvent } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { ArrowLeftIcon, PhotoIcon, FolderOpenIcon } from '@heroicons/react/24/outline';
import { PortfolioFormData, PORTFOLIO_CATEGORIES, PortfolioCategory } from '@/types/portfolio';
import { useNotification } from '@/contexts/NotificationContext';

import S3ImageBrowser from '@/components/admin/S3ImageBrowser';

export default function NewPortfolioItemPage() {
  const router = useRouter();
  const { showNotification } = useNotification();
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [formData, setFormData] = useState<PortfolioFormData>({
    title: '',
    description: '',
    category: 'logos',
    image: null,
    alt: '',
    featured: false
  });

  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [selectedImageUrl, setSelectedImageUrl] = useState<string | null>(null);
  const [imageSource, setImageSource] = useState<'upload' | 's3'>('upload');
  const [showS3Browser, setShowS3Browser] = useState(false);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [recentUpload, setRecentUpload] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear error for this field
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFormData(prev => ({ ...prev, [name]: checked }));
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;

    if (file) {
      // Validate file type (match server-side validation)
      const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif'];
      if (!validTypes.includes(file.type)) {
        setErrors(prev => ({
          ...prev,
          image: 'Invalid file type. Please upload a JPEG, JPG, PNG, WebP, or GIF image.'
        }));
        return;
      }

      // Validate file size (15MB max to match server-side)
      const maxSize = 15 * 1024 * 1024; // 15MB
      if (file.size > maxSize) {
        setErrors(prev => ({
          ...prev,
          image: 'File size exceeds 15MB. Please upload a smaller image.'
        }));
        return;
      }

      // Warn for large files
      if (file.size > 5 * 1024 * 1024) {
        console.warn(`Large file detected (${Math.round(file.size/1024/1024)}MB). Upload may take longer.`);
      }

      // Create preview
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result as string);
      };
      reader.readAsDataURL(file);

      // Update form data and clear S3 selection
      setFormData(prev => ({ ...prev, image: file }));
      setSelectedImageUrl(null);
      setImageSource('upload');
      setRecentUpload(true);

      // Clear error
      if (errors.image) {
        setErrors(prev => {
          const newErrors = { ...prev };
          delete newErrors.image;
          return newErrors;
        });
      }
    } else {
      setImagePreview(null);
      setFormData(prev => ({ ...prev, image: null }));
    }
  };

  const handleS3ImageSelect = (imageUrl: string, filename: string) => {
    setSelectedImageUrl(imageUrl);
    setImagePreview(imageUrl);
    setImageSource('s3');

    // Clear file upload
    setFormData(prev => ({ ...prev, image: null }));
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }

    // Auto-fill title if empty
    if (!formData.title) {
      const displayName = filename
        .replace(/\.(jpg|jpeg|png|webp|gif|svg)$/i, '')
        .replace(/[-_]/g, ' ')
        .replace(/\b\w/g, (char) => char.toUpperCase());
      setFormData(prev => ({ ...prev, title: displayName }));
    }

    // Clear error
    if (errors.image) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors.image;
        return newErrors;
      });
    }
  };

  const handleImageSourceChange = (source: 'upload' | 's3') => {
    setImageSource(source);

    if (source === 's3') {
      // Clear upload data
      setFormData(prev => ({ ...prev, image: null }));
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
      if (!selectedImageUrl) {
        setImagePreview(null);
      }
    } else {
      // Clear S3 selection
      setSelectedImageUrl(null);
      if (!formData.image) {
        setImagePreview(null);
      }
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Title is required';
    }

    if (!formData.category) {
      newErrors.category = 'Category is required';
    }

    if (!formData.image && !selectedImageUrl) {
      newErrors.image = 'Image is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setLoading(true);

    try {
      let imageSrc: string;

      if (imageSource === 's3' && selectedImageUrl) {
        // Use the selected S3 image URL directly
        imageSrc = selectedImageUrl;
      } else if (imageSource === 'upload' && formData.image) {
        // Upload the new image
        const fileSizeMB = Math.round(formData.image.size / 1024 / 1024 * 100) / 100;
        console.log('Starting image upload:', {
          fileName: formData.image.name,
          fileSize: fileSizeMB > 1 ? `${fileSizeMB}MB` : `${Math.round(formData.image.size / 1024)}KB`,
          fileType: formData.image.type,
          category: formData.category
        });

        const imageFormData = new FormData();
        imageFormData.append('file', formData.image as File);
        imageFormData.append('category', formData.category);

        // Set longer timeout for large files
        const timeoutMs = fileSizeMB > 1 ? 120000 : 30000; // 2 minutes for large files, 30s for small
        
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeoutMs);

        try {
          const uploadResponse = await fetch('/api/admin/upload', {
            method: 'POST',
            body: imageFormData,
            signal: controller.signal,
          });
          
          clearTimeout(timeoutId);

          if (!uploadResponse.ok) {
            const errorData = await uploadResponse.json();
            console.error('Upload failed:', {
              status: uploadResponse.status,
              statusText: uploadResponse.statusText,
              error: errorData
            });
            throw new Error(errorData.error || `Upload failed with status ${uploadResponse.status}`);
          }

          const uploadResult = await uploadResponse.json();
          console.log('Upload successful:', uploadResult);
          imageSrc = uploadResult.url;
        } catch (uploadError) {
          clearTimeout(timeoutId);
          if (uploadError instanceof Error && uploadError.name === 'AbortError') {
            throw new Error(`Upload timeout after ${fileSizeMB > 1 ? '2 minutes' : '30 seconds'}. Please try again with a smaller file or check your connection.`);
          }
          throw uploadError;
        }
      } else {
        throw new Error('No image selected');
      }

      // Create the portfolio item
      const portfolioItemData = {
        title: formData.title,
        description: formData.description,
        category: formData.category,
        imageSrc: imageSrc,
        alt: formData.alt || formData.title,
        featured: formData.featured,
      };

      const createResponse = await fetch('/api/admin/portfolio', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(portfolioItemData),
      });

      if (!createResponse.ok) {
        // Log the actual response for debugging
        const responseText = await createResponse.text();
        console.error('API Response Status:', createResponse.status);
        console.error('API Response Headers:', Object.fromEntries(createResponse.headers.entries()));
        console.error('API Response Text:', responseText);
        
        // Try to parse as JSON if possible
        let errorData;
        try {
          errorData = JSON.parse(responseText);
        } catch (parseError) {
          console.error('Failed to parse response as JSON:', parseError);
          console.error('Raw response:', responseText.substring(0, 500));
          throw new Error(`Server returned HTML instead of JSON. Status: ${createResponse.status}`);
        }
        
        throw new Error(errorData.error || 'Failed to create portfolio item');
      }

      showNotification('success', 'Success', 'Portfolio item created successfully');
      router.push('/admin/portfolio');
    } catch (error) {
      console.error('Error creating portfolio item:', error);
      showNotification('error', 'Error', error instanceof Error ? error.message : 'Failed to create portfolio item');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-8">
      <div className="flex items-center justify-between mb-6 mt-2">
        <div className="flex items-center">
          <Link
            href="/admin/portfolio"
            className="mr-4 p-2 rounded-lg text-slate-500 hover:text-slate-700 hover:bg-slate-100 transition-colors"
          >
            <ArrowLeftIcon className="h-5 w-5" />
          </Link>
          <h1 className="text-2xl font-bold text-slate-800">Add Portfolio Item</h1>
        </div>
      </div>

      <div className="bg-white shadow-sm rounded-lg overflow-hidden border border-gray-200">
        <form onSubmit={handleSubmit} className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Left Column - Form Fields */}
            <div className="space-y-6">
              {/* Title */}
              <div>
                <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">
                  Title <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  id="title"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  className={`block w-full rounded-md border ${
                    errors.title ? 'border-red-300' : 'border-gray-300'
                  } shadow-sm py-2 px-3 focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm`}
                />
                {errors.title && (
                  <p className="mt-1 text-sm text-red-600">{errors.title}</p>
                )}
              </div>

              {/* Description */}
              <div>
                <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <textarea
                  id="description"
                  name="description"
                  rows={4}
                  value={formData.description}
                  onChange={handleInputChange}
                  className="block w-full rounded-md border border-gray-300 shadow-sm py-2 px-3 focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
                />
              </div>

              {/* Category */}
              <div>
                <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-1">
                  Category <span className="text-red-500">*</span>
                </label>
                <select
                  id="category"
                  name="category"
                  value={formData.category}
                  onChange={handleInputChange}
                  className={`block w-full rounded-md border ${
                    errors.category ? 'border-red-300' : 'border-gray-300'
                  } shadow-sm py-2 px-3 focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm`}
                >
                  {PORTFOLIO_CATEGORIES.map(category => (
                    <option key={category.value} value={category.value}>
                      {category.label}
                    </option>
                  ))}
                </select>
                {errors.category && (
                  <p className="mt-1 text-sm text-red-600">{errors.category}</p>
                )}
              </div>

              {/* Alt Text */}
              <div>
                <label htmlFor="alt" className="block text-sm font-medium text-gray-700 mb-1">
                  Alt Text
                </label>
                <input
                  type="text"
                  id="alt"
                  name="alt"
                  value={formData.alt}
                  onChange={handleInputChange}
                  className="block w-full rounded-md border border-gray-300 shadow-sm py-2 px-3 focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
                  placeholder="Describe the image for accessibility"
                />
              </div>

              {/* Featured */}
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="featured"
                  name="featured"
                  checked={formData.featured}
                  onChange={handleCheckboxChange}
                  className="h-4 w-4 text-orange-500 focus:ring-orange-500 border-gray-300 rounded"
                />
                <label htmlFor="featured" className="ml-2 block text-sm text-gray-700">
                  Feature this item on the portfolio page
                </label>
              </div>
            </div>

            {/* Right Column - Image Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Image <span className="text-red-500">*</span>
              </label>

              {/* Image Source Toggle */}
              <div className="mb-4">
                <div className="flex rounded-md shadow-sm">
                  <button
                    type="button"
                    onClick={() => handleImageSourceChange('upload')}
                    className={`flex-1 px-4 py-2 text-sm font-medium rounded-l-md border ${
                      imageSource === 'upload'
                        ? 'bg-orange-50 border-orange-500 text-orange-700'
                        : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    <PhotoIcon className="h-4 w-4 inline mr-2" />
                    Upload New
                  </button>
                  <button
                    type="button"
                    onClick={() => handleImageSourceChange('s3')}
                    className={`flex-1 px-4 py-2 text-sm font-medium rounded-r-md border-t border-r border-b ${
                      imageSource === 's3'
                        ? 'bg-orange-50 border-orange-500 text-orange-700'
                        : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    <FolderOpenIcon className="h-4 w-4 inline mr-2" />
                    Select from S3
                  </button>
                </div>
              </div>

              {/* Image Preview/Upload Area */}
              <div
                className={`flex justify-center px-6 pt-5 pb-6 border-2 border-dashed rounded-md ${
                  errors.image ? 'border-red-300 bg-red-50' : 'border-gray-300'
                } transition-colors`}
              >
                <div className="space-y-1 text-center">
                  {imagePreview ? (
                    <div className="relative w-full aspect-square max-w-md mx-auto">
                      <Image
                        src={imagePreview}
                        alt="Image preview"
                        fill
                        className="object-contain rounded-md"
                      />
                      {imageSource === 's3' && (
                        <div className="absolute top-2 right-2 bg-blue-500 text-white text-xs px-2 py-1 rounded">
                          From S3
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="mx-auto h-24 w-24 text-gray-400">
                      <PhotoIcon className="h-full w-full" />
                    </div>
                  )}

                  {imageSource === 'upload' ? (
                    <div>
                      <div className="flex text-sm text-gray-600">
                        <label
                          htmlFor="image"
                          className="relative cursor-pointer rounded-md font-medium text-orange-500 hover:text-orange-600"
                        >
                          <span>{imagePreview ? 'Change image' : 'Upload an image'}</span>
                          <input
                            ref={fileInputRef}
                            id="image"
                            name="image"
                            type="file"
                            className="sr-only"
                            accept="image/jpeg,image/jpg,image/png,image/webp,image/gif,image/avif"
                            onChange={handleImageChange}
                          />
                        </label>
                      </div>
                      <p className="text-xs text-gray-500">
                        PNG, JPG, WebP, or GIF up to 15MB
                      </p>
                    </div>
                  ) : (
                    <div>
                      <button
                        type="button"
                        onClick={() => setShowS3Browser(true)}
                        className="text-orange-500 hover:text-orange-600 font-medium text-sm"
                      >
                        {selectedImageUrl ? 'Change S3 image' : 'Browse S3 images'}
                      </button>
                      <p className="text-xs text-gray-500">
                        Select from existing images in S3 storage
                      </p>
                    </div>
                  )}
                </div>
              </div>
              {errors.image && (
                <p className="mt-1 text-sm text-red-600">{errors.image}</p>
              )}
            </div>
          </div>

          {/* Form Actions */}
          <div className="mt-8 flex justify-end space-x-3">
            <Link
              href="/admin/portfolio"
              className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
            >
              Cancel
            </Link>
            <button
              type="submit"
              disabled={loading}
              className="inline-flex justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-500 hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:opacity-75"
            >
              {loading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Saving...
                </>
              ) : (
                'Save Portfolio Item'
              )}
            </button>
          </div>
        </form>
      </div>

      {/* S3 Image Browser Modal */}
      <S3ImageBrowser
        isOpen={showS3Browser}
        onClose={() => setShowS3Browser(false)}
        onSelectImage={handleS3ImageSelect}
        selectedCategory={formData.category}
        forceRefresh={recentUpload}
      />
    </div>
  );
}
