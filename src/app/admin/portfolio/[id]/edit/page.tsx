'use client';

import React, { useState, useRef, FormEvent, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import { ArrowLeftIcon, PhotoIcon, FolderOpenIcon } from '@heroicons/react/24/outline';
import { PortfolioItem, PORTFOLIO_CATEGORIES, PortfolioCategory } from '@/types/portfolio';
import { useNotification } from '@/contexts/NotificationContext';
import S3ImageBrowser from '@/components/admin/S3ImageBrowser';

interface EditPortfolioItemPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default function EditPortfolioItemPage({ params }: EditPortfolioItemPageProps) {
  const router = useRouter();
  const { showNotification } = useNotification();
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [portfolioItemId, setPortfolioItemId] = useState<string>('');
  const [portfolioItem, setPortfolioItem] = useState<PortfolioItem | null>(null);
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [category, setCategory] = useState<PortfolioCategory>('logos');
  const [alt, setAlt] = useState('');
  const [featured, setFeatured] = useState(false);
  const [newImage, setNewImage] = useState<File | null>(null);
  const [selectedImageUrl, setSelectedImageUrl] = useState<string | null>(null);
  const [imageSource, setImageSource] = useState<'upload' | 's3' | 'current'>('current');
  const [showS3Browser, setShowS3Browser] = useState(false);
  const [recentUpload, setRecentUpload] = useState(false);

  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [fetchLoading, setFetchLoading] = useState(true);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Fetch portfolio item data
  useEffect(() => {
    const fetchPortfolioItem = async () => {
      try {
        setFetchLoading(true);

        // Resolve params Promise
        const resolvedParams = await params;
        const id = resolvedParams.id;
        setPortfolioItemId(id);

        const response = await fetch(`/api/admin/portfolio/${id}`);

        if (!response.ok) {
          throw new Error(`Failed to fetch portfolio item: ${response.statusText}`);
        }

        const data = await response.json();
        setPortfolioItem(data);

        // Set form fields
        setTitle(data.title);
        setDescription(data.description || '');
        setCategory(data.category as PortfolioCategory);
        setAlt(data.alt || data.title);
        setFeatured(!!data.featured);
        setImagePreview(data.imageSrc);
      } catch (err) {
        console.error('Error fetching portfolio item:', err);
        showNotification('error', 'Error', 'Failed to load portfolio item');
        router.push('/admin/portfolio');
      } finally {
        setFetchLoading(false);
      }
    };

    fetchPortfolioItem();
  }, [params, router, showNotification]);

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;

    if (file) {
      // Validate file type (match server-side validation)
      const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif', 'image/avif'];
      if (!validTypes.includes(file.type)) {
        setErrors(prev => ({
          ...prev,
          image: 'Invalid file type. Please upload a JPEG, JPG, PNG, WebP, GIF, or AVIF image.'
        }));
        return;
      }

      // Validate file size (15MB max to match server-side)
      const maxSize = 15 * 1024 * 1024; // 15MB
      if (file.size > maxSize) {
        setErrors(prev => ({
          ...prev,
          image: 'File size exceeds 15MB. Please upload a smaller image.'
        }));
        return;
      }

      // Warn for large files
      if (file.size > 5 * 1024 * 1024) {
        console.warn(`Large file detected (${Math.round(file.size/1024/1024)}MB). Upload may take longer.`);
      }

      // Create preview
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result as string);
      };
      reader.readAsDataURL(file);

      // Update form data and clear S3 selection
      setNewImage(file);
      setSelectedImageUrl(null);
      setImageSource('upload');
      setRecentUpload(true);

      // Clear error
      if (errors.image) {
        setErrors(prev => {
          const newErrors = { ...prev };
          delete newErrors.image;
          return newErrors;
        });
      }
    } else {
      // If no new file is selected, revert to the original image
      setImagePreview(portfolioItem?.imageSrc || null);
      setNewImage(null);
      setImageSource('current');
    }
  };

  const handleS3ImageSelect = (imageUrl: string, filename: string) => {
    setSelectedImageUrl(imageUrl);
    setImagePreview(imageUrl);
    setImageSource('s3');

    // Clear file upload
    setNewImage(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }

    // Clear error
    if (errors.image) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors.image;
        return newErrors;
      });
    }
  };

  const handleImageSourceChange = (source: 'upload' | 's3' | 'current') => {
    setImageSource(source);

    if (source === 's3') {
      // Clear upload data
      setNewImage(null);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
      if (!selectedImageUrl) {
        setImagePreview(portfolioItem?.imageSrc || null);
      }
      // Open S3 browser with force refresh if there was a recent upload
      if (recentUpload) {
        setShowS3Browser(true);
        setRecentUpload(false);
      }
    } else if (source === 'upload') {
      // Clear S3 selection
      setSelectedImageUrl(null);
      if (!newImage) {
        setImagePreview(portfolioItem?.imageSrc || null);
      }
    } else if (source === 'current') {
      // Clear both upload and S3 selection
      setNewImage(null);
      setSelectedImageUrl(null);
      setImagePreview(portfolioItem?.imageSrc || null);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!title.trim()) {
      newErrors.title = 'Title is required';
    }

    if (!category) {
      newErrors.category = 'Category is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();

    if (!validateForm() || !portfolioItem) {
      return;
    }

    setLoading(true);

    try {
      let imageSrc = portfolioItem.imageSrc;

      // Determine which image to use based on the source
      if (imageSource === 's3' && selectedImageUrl) {
        // Use the selected S3 image URL directly
        imageSrc = selectedImageUrl;
      } else if (imageSource === 'upload' && newImage) {
        // Upload the new image
        console.log('Starting image upload:', {
          fileName: newImage.name,
          fileSize: `${Math.round(newImage.size / 1024)}KB`,
          fileType: newImage.type,
          category: category
        });

        const imageFormData = new FormData();
        imageFormData.append('file', newImage);
        imageFormData.append('category', category);

        const uploadResponse = await fetch('/api/admin/upload', {
          method: 'POST',
          body: imageFormData,
        });

        if (!uploadResponse.ok) {
          const errorData = await uploadResponse.json();
          console.error('Upload failed:', {
            status: uploadResponse.status,
            statusText: uploadResponse.statusText,
            error: errorData
          });
          throw new Error(errorData.error || `Upload failed with status ${uploadResponse.status}`);
        }

        const uploadResult = await uploadResponse.json();
        console.log('Upload successful:', uploadResult);
        imageSrc = uploadResult.url;
      }
      // If imageSource is 'current', keep the existing imageSrc

      // Update the portfolio item
      const portfolioItemData = {
        title,
        description,
        category,
        imageSrc,
        alt: alt || title,
        featured,
      };

      const response = await fetch(`/api/admin/portfolio/${portfolioItemId}/edit`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ ...portfolioItemData, updatedAt: portfolioItem.updatedAt }),
      });

      if (response.status === 409) {
        showNotification('error', 'Conflict', 'This item was modified by another user. Please refresh and try again.');
        setLoading(false);
        return;
      }

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update portfolio item');
      }

      showNotification('success', 'Success', 'Portfolio item updated successfully');
      router.push('/admin/portfolio');
    } catch (error) {
      console.error('Error updating portfolio item:', error);
      showNotification('error', 'Error', error instanceof Error ? error.message : 'Failed to update portfolio item');
    } finally {
      setLoading(false);
    }
  };

  if (fetchLoading) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <div className="animate-spin h-10 w-10 border-4 border-orange-500 rounded-full border-t-transparent"></div>
      </div>
    );
  }

  if (!portfolioItem) {
    return (
      <div className="text-center py-12">
        <h2 className="text-xl font-medium text-gray-900">Portfolio item not found</h2>
        <p className="mt-2 text-gray-500">The portfolio item you are looking for does not exist.</p>
        <Link
          href="/admin/portfolio"
          className="mt-4 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-500 hover:bg-orange-600"
        >
          <ArrowLeftIcon className="h-4 w-4 mr-2" />
          Back to Portfolio
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      <div className="flex items-center justify-between mb-6 mt-2">
        <div className="flex items-center">
          <Link
            href="/admin/portfolio"
            className="mr-4 p-2 rounded-lg text-slate-500 hover:text-slate-700 hover:bg-slate-100 transition-colors"
          >
            <ArrowLeftIcon className="h-5 w-5" />
          </Link>
          <h1 className="text-2xl font-bold text-slate-800">Edit Portfolio Item</h1>
        </div>
      </div>

      <div className="bg-white shadow-sm rounded-lg overflow-hidden border border-gray-200">
        <form onSubmit={handleSubmit} className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Left Column - Form Fields */}
            <div className="space-y-6">
              {/* Title */}
              <div>
                <label htmlFor="title" className="block text-sm font-medium text-gray-700 mb-1">
                  Title <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  id="title"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  className={`block w-full rounded-md border ${
                    errors.title ? 'border-red-300' : 'border-gray-300'
                  } shadow-sm py-2 px-3 focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm`}
                />
                {errors.title && (
                  <p className="mt-1 text-sm text-red-600">{errors.title}</p>
                )}
              </div>

              {/* Description */}
              <div>
                <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <textarea
                  id="description"
                  rows={4}
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  className="block w-full rounded-md border border-gray-300 shadow-sm py-2 px-3 focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
                />
              </div>

              {/* Category */}
              <div>
                <label htmlFor="category" className="block text-sm font-medium text-gray-700 mb-1">
                  Category <span className="text-red-500">*</span>
                </label>
                <select
                  id="category"
                  value={category}
                  onChange={(e) => setCategory(e.target.value as PortfolioCategory)}
                  className={`block w-full rounded-md border ${
                    errors.category ? 'border-red-300' : 'border-gray-300'
                  } shadow-sm py-2 px-3 focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm`}
                >
                  {PORTFOLIO_CATEGORIES.map(cat => (
                    <option key={cat.value} value={cat.value}>
                      {cat.label}
                    </option>
                  ))}
                </select>
                {errors.category && (
                  <p className="mt-1 text-sm text-red-600">{errors.category}</p>
                )}
              </div>

              {/* Alt Text */}
              <div>
                <label htmlFor="alt" className="block text-sm font-medium text-gray-700 mb-1">
                  Alt Text
                </label>
                <input
                  type="text"
                  id="alt"
                  value={alt}
                  onChange={(e) => setAlt(e.target.value)}
                  className="block w-full rounded-md border border-gray-300 shadow-sm py-2 px-3 focus:outline-none focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
                  placeholder="Describe the image for accessibility"
                />
              </div>

              {/* Featured */}
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="featured"
                  checked={featured}
                  onChange={(e) => setFeatured(e.target.checked)}
                  className="h-4 w-4 text-orange-500 focus:ring-orange-500 border-gray-300 rounded"
                />
                <label htmlFor="featured" className="ml-2 block text-sm text-gray-700">
                  Feature this item on the portfolio page
                </label>
              </div>
            </div>

            {/* Right Column - Image Selection */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Image
              </label>

              {/* Image Source Toggle */}
              <div className="mb-4">
                <div className="flex rounded-md shadow-sm">
                  <button
                    type="button"
                    onClick={() => handleImageSourceChange('current')}
                    className={`flex-1 px-3 py-2 text-sm font-medium rounded-l-md border ${
                      imageSource === 'current'
                        ? 'bg-orange-50 border-orange-500 text-orange-700'
                        : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    Keep Current
                  </button>
                  <button
                    type="button"
                    onClick={() => handleImageSourceChange('upload')}
                    className={`flex-1 px-3 py-2 text-sm font-medium border-t border-b ${
                      imageSource === 'upload'
                        ? 'bg-orange-50 border-orange-500 text-orange-700'
                        : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    <PhotoIcon className="h-4 w-4 inline mr-1" />
                    Upload New
                  </button>
                  <button
                    type="button"
                    onClick={() => handleImageSourceChange('s3')}
                    className={`flex-1 px-3 py-2 text-sm font-medium rounded-r-md border-t border-r border-b ${
                      imageSource === 's3'
                        ? 'bg-orange-50 border-orange-500 text-orange-700'
                        : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    <FolderOpenIcon className="h-4 w-4 inline mr-1" />
                    Select from S3
                  </button>
                </div>
              </div>

              {/* Image Preview/Upload Area */}
              <div
                className={`flex justify-center px-6 pt-5 pb-6 border-2 border-dashed rounded-md ${
                  errors.image ? 'border-red-300 bg-red-50' : 'border-gray-300'
                } transition-colors`}
              >
                <div className="space-y-1 text-center">
                  {imagePreview ? (
                    <div className="relative w-full aspect-square max-w-md mx-auto">
                      <Image
                        src={imagePreview}
                        alt="Image preview"
                        fill
                        className="object-contain rounded-md"
                      />
                      {imageSource === 's3' && (
                        <div className="absolute top-2 right-2 bg-blue-500 text-white text-xs px-2 py-1 rounded">
                          From S3
                        </div>
                      )}
                      {imageSource === 'upload' && (
                        <div className="absolute top-2 right-2 bg-green-500 text-white text-xs px-2 py-1 rounded">
                          New Upload
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="mx-auto h-24 w-24 text-gray-400">
                      <PhotoIcon className="h-full w-full" />
                    </div>
                  )}

                  {imageSource === 'upload' ? (
                    <div>
                      <div className="flex text-sm text-gray-600">
                        <label
                          htmlFor="image"
                          className="relative cursor-pointer rounded-md font-medium text-orange-500 hover:text-orange-600"
                        >
                          <span>Upload new image</span>
                          <input
                            ref={fileInputRef}
                            id="image"
                            type="file"
                            className="sr-only"
                            accept="image/jpeg,image/jpg,image/png,image/webp,image/gif,image/avif"
                            onChange={handleImageChange}
                          />
                        </label>
                      </div>
                      <p className="text-xs text-gray-500">
                        PNG, JPG, WebP, or GIF up to 15MB
                      </p>
                    </div>
                  ) : imageSource === 's3' ? (
                    <div>
                      <button
                        type="button"
                        onClick={() => setShowS3Browser(true)}
                        className="text-orange-500 hover:text-orange-600 font-medium text-sm"
                      >
                        {selectedImageUrl ? 'Change S3 image' : 'Browse S3 images'}
                      </button>
                      <p className="text-xs text-gray-500">
                        Select from existing images in S3 storage
                      </p>
                    </div>
                  ) : (
                    <div>
                      <p className="text-sm text-gray-600">Current image will be kept</p>
                      <p className="text-xs text-gray-500">
                        Choose "Upload New" or "Select from S3" to change
                      </p>
                    </div>
                  )}
                </div>
              </div>
              {errors.image && (
                <p className="mt-1 text-sm text-red-600">{errors.image}</p>
              )}
            </div>
          </div>

          {/* Form Actions */}
          <div className="mt-8 flex justify-end space-x-3">
            <Link
              href="/admin/portfolio"
              className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500"
            >
              Cancel
            </Link>
            <button
              type="submit"
              disabled={loading}
              className="inline-flex justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-500 hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:opacity-75"
            >
              {loading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Saving...
                </>
              ) : (
                'Update Portfolio Item'
              )}
            </button>
          </div>
        </form>
      </div>

      {/* S3 Image Browser Modal */}
      <S3ImageBrowser
        isOpen={showS3Browser}
        onClose={() => setShowS3Browser(false)}
        onSelectImage={handleS3ImageSelect}
        selectedCategory={category}
        forceRefresh={recentUpload}
      />
    </div>
  );
}
