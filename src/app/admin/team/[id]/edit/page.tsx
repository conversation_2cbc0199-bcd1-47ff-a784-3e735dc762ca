'use client';

import React, { useState, useRef, FormEvent, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeftIcon, PhotoIcon, FolderOpenIcon } from '@heroicons/react/24/outline';
import Link from 'next/link';
import Image from 'next/image';
import { toast } from 'react-hot-toast';
import { TeamMember } from '@/types/team';
import S3ImageBrowser from '@/components/admin/S3ImageBrowser';

// Client Component - handles the UI and interactions
export default function EditTeamMemberPage({ params }: { params: Promise<{ id: string }> }) {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [fetchLoading, setFetchLoading] = useState(true);
  const [teamMember, setTeamMember] = useState<TeamMember | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [selectedImageUrl, setSelectedImageUrl] = useState<string | null>(null);
  const [imageSource, setImageSource] = useState<'upload' | 's3' | 'current'>('current');
  const [showS3Browser, setShowS3Browser] = useState(false);
  const [recentUpload, setRecentUpload] = useState(false);
  const [id, setId] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    // Handle async params
    const resolveParams = async () => {
      const resolvedParams = await params;
      setId(resolvedParams.id);
    };
    resolveParams();
  }, [params]);

  useEffect(() => {
    if (!id) return;

    const fetchTeamMember = async () => {
      try {
        setFetchLoading(true);
        const response = await fetch(`/api/admin/team/${id}`);

        if (!response.ok) {
          throw new Error(`Error: ${response.status}`);
        }

        const data = await response.json();
        setTeamMember(data);
        setImagePreview(data.imageSrc);
      } catch (err) {
        console.error('Failed to load team member:', err);
        toast.error('Failed to load team member');
      } finally {
        setFetchLoading(false);
      }
    };

    fetchTeamMember();
  }, [id]);

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!id) return;
    
    setLoading(true);

    try {
      const formData = new FormData(e.currentTarget);

      // Validate required fields
      const name = formData.get('name') as string;
      const role = formData.get('role') as string;
      const bio = formData.get('bio') as string;

      if (!name || !role || !bio) {
        toast.error('Please fill in all required fields');
        setLoading(false);
        return;
      }

      // Submit the form data with CSRF protection
      console.log('Updating team member with CSRF protection...');
      
      // Add S3 image URL if S3 source is selected
      if (imageSource === 's3' && selectedImageUrl) {
        formData.append('s3ImageUrl', selectedImageUrl);
      }
      
      const response = await fetch(`/api/admin/team/${id}`, {
        method: 'PUT',
        body: formData,
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to update team member');
      }

      // Check if there was a warning about image upload
      if (data.warning) {
        toast.success('Team member updated successfully, but image upload failed');
        console.warn('Image upload warning:', data.warning);
      } else {
        toast.success('Team member updated successfully');
      }

      // Update the local state with the updated team member data
      setTeamMember(data);
      
      // Update the preview based on image source
      if (imageSource === 's3' && selectedImageUrl) {
        // Use selected S3 image with cache busting
        const imageUrlWithCacheBusting = selectedImageUrl.includes('?') 
          ? `${selectedImageUrl}&t=${Date.now()}` 
          : `${selectedImageUrl}?t=${Date.now()}`;
        setImagePreview(imageUrlWithCacheBusting);
      } else if (data.imageSrc) {
        // Use uploaded/updated image with cache busting
        const imageUrlWithCacheBusting = data.imageSrc.includes('?') 
          ? `${data.imageSrc}&t=${Date.now()}` 
          : `${data.imageSrc}?t=${Date.now()}`;
        setImagePreview(imageUrlWithCacheBusting);
      }

      // Clear any selected file since the update is complete
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }

      // Redirect after a short delay to allow user to see the changes
      setTimeout(() => {
        router.push('/admin/team');
      }, 1500);
    } catch (error) {
      console.error('Error updating team member:', error);
      toast.error('Failed to update team member');
    } finally {
      setLoading(false);
    }
  };

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result as string);
      };
      reader.readAsDataURL(file);
      setImageSource('upload');
      setSelectedImageUrl(null);
      setRecentUpload(true);
    }
  };

  const handleS3ImageSelect = (imageUrl: string, filename: string) => {
    setSelectedImageUrl(imageUrl);
    setImagePreview(imageUrl);
    setImageSource('s3');

    // Clear file upload
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleImageSourceChange = (source: 'upload' | 's3' | 'current') => {
    setImageSource(source);

    if (source === 's3') {
      // Clear upload data
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
      if (!selectedImageUrl) {
        setImagePreview(teamMember?.imageSrc || null);
      }
      // Open S3 browser with force refresh if there was a recent upload
      if (recentUpload) {
        setShowS3Browser(true);
        setRecentUpload(false);
      }
    } else if (source === 'upload') {
      // Clear S3 selection
      setSelectedImageUrl(null);
      if (!fileInputRef.current?.files?.[0]) {
        setImagePreview(teamMember?.imageSrc || null);
      }
    } else if (source === 'current') {
      // Clear both upload and S3 selection
      setSelectedImageUrl(null);
      setImagePreview(teamMember?.imageSrc || null);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  if (fetchLoading || !id) {
    return (
      <div className="flex justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-orange-500"></div>
      </div>
    );
  }

  if (!teamMember) {
    return (
      <div className="bg-red-50 p-4 rounded-md">
        <p className="text-red-800">Team member not found</p>
        <Link href="/admin/team" className="text-red-800 underline mt-2 inline-block">
          Back to team members
        </Link>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Link
            href="/admin/team"
            className="p-2 rounded-lg text-slate-500 hover:text-slate-700 hover:bg-slate-100"
          >
            <ArrowLeftIcon className="h-5 w-5" />
          </Link>
          <h1 className="text-2xl font-bold text-slate-800">Edit Team Member</h1>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-sm p-6">
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-6">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                  Name <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  name="name"
                  id="name"
                  required
                  defaultValue={teamMember.name}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500"
                />
              </div>

              <div>
                <label htmlFor="role" className="block text-sm font-medium text-gray-700">
                  Role <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  name="role"
                  id="role"
                  required
                  defaultValue={teamMember.role}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500"
                />
              </div>

              <div>
                <label htmlFor="order" className="block text-sm font-medium text-gray-700">
                  Display Order
                </label>
                <input
                  type="number"
                  name="order"
                  id="order"
                  defaultValue={teamMember.order}
                  min="0"
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500"
                />
                <p className="mt-1 text-sm text-gray-500">Lower numbers appear first</p>
              </div>

              <div>
                <label htmlFor="image" className="block text-sm font-medium text-gray-700">
                  Profile Image
                </label>

                {/* Image Source Selection */}
                <div className="mt-2 mb-4">
                  <div className="flex rounded-md shadow-sm">
                    <button
                      type="button"
                      onClick={() => handleImageSourceChange('current')}
                      className={`flex-1 px-3 py-2 text-sm font-medium rounded-l-md border-t border-l border-b ${
                        imageSource === 'current'
                          ? 'bg-orange-50 border-orange-500 text-orange-700'
                          : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                      }`}
                    >
                      Keep Current
                    </button>
                    <button
                      type="button"
                      onClick={() => handleImageSourceChange('upload')}
                      className={`flex-1 px-3 py-2 text-sm font-medium border-t border-b ${
                        imageSource === 'upload'
                          ? 'bg-orange-50 border-orange-500 text-orange-700'
                          : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                      }`}
                    >
                      <PhotoIcon className="h-4 w-4 inline mr-1" />
                      Upload New
                    </button>
                    <button
                      type="button"
                      onClick={() => handleImageSourceChange('s3')}
                      className={`flex-1 px-3 py-2 text-sm font-medium rounded-r-md border-t border-r border-b ${
                        imageSource === 's3'
                          ? 'bg-orange-50 border-orange-500 text-orange-700'
                          : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                      }`}
                    >
                      <FolderOpenIcon className="h-4 w-4 inline mr-1" />
                      Select from S3
                    </button>
                  </div>
                </div>

                {/* Image Preview/Upload Area */}
                <div className="flex justify-center px-6 pt-5 pb-6 border-2 border-dashed rounded-md border-gray-300 transition-colors">
                  <div className="space-y-1 text-center">
                    {imagePreview ? (
                      <div className="relative w-full aspect-square max-w-xs mx-auto">
                        <Image
                          src={imagePreview}
                          alt="Image preview"
                          fill
                          className="object-cover rounded-md"
                        />
                        {imageSource === 's3' && (
                          <div className="absolute top-2 right-2 bg-blue-500 text-white text-xs px-2 py-1 rounded">
                            From S3
                          </div>
                        )}
                        {imageSource === 'upload' && (
                          <div className="absolute top-2 right-2 bg-green-500 text-white text-xs px-2 py-1 rounded">
                            New Upload
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="mx-auto h-24 w-24 text-gray-400">
                        <PhotoIcon className="h-full w-full" />
                      </div>
                    )}

                    {imageSource === 'upload' ? (
                      <div>
                        <div className="flex text-sm text-gray-600">
                          <label
                            htmlFor="image"
                            className="relative cursor-pointer rounded-md font-medium text-orange-500 hover:text-orange-600"
                          >
                            <span>{imagePreview && imageSource === 'upload' ? 'Change image' : 'Upload an image'}</span>
                            <input
                              type="file"
                              name="image"
                              id="image"
                              ref={fileInputRef}
                              onChange={handleImageChange}
                              accept="image/jpeg,image/png,image/gif,image/webp,image/avif"
                              className="sr-only"
                            />
                          </label>
                        </div>
                        <p className="text-xs text-gray-500">
                          PNG, JPG, WebP, or GIF up to 15MB
                        </p>
                      </div>
                    ) : imageSource === 's3' ? (
                      <div>
                        <button
                          type="button"
                          onClick={() => setShowS3Browser(true)}
                          className="text-orange-500 hover:text-orange-600 font-medium text-sm"
                        >
                          {selectedImageUrl ? 'Change S3 image' : 'Browse S3 images'}
                        </button>
                        <p className="text-xs text-gray-500">
                          Select from existing images in S3 storage
                        </p>
                      </div>
                    ) : (
                      <div>
                        <p className="text-sm text-gray-600">Current image will be kept</p>
                        <p className="text-xs text-gray-500">
                          Choose "Upload New" or "Select from S3" to change
                        </p>
                      </div>
                    )}
                  </div>
                </div>

                <p className="mt-1 text-sm text-gray-500">Leave empty to keep current image</p>
                <p className="mt-1 text-sm text-gray-500">Maximum size: 15MB • Accepted: JPG, PNG, GIF, WebP</p>
              </div>
            </div>

            <div className="space-y-6">
              <div>
                <label htmlFor="bio" className="block text-sm font-medium text-gray-700">
                  Bio <span className="text-red-500">*</span>
                </label>
                <textarea
                  name="bio"
                  id="bio"
                  rows={4}
                  required
                  defaultValue={teamMember.bio}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500"
                ></textarea>
              </div>

              <div>
                <label htmlFor="linkedinUrl" className="block text-sm font-medium text-gray-700">
                  LinkedIn URL
                </label>
                <input
                  type="url"
                  name="linkedinUrl"
                  id="linkedinUrl"
                  defaultValue={teamMember.linkedinUrl || ''}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500"
                />
              </div>

              <div>
                <label htmlFor="twitterUrl" className="block text-sm font-medium text-gray-700">
                  Twitter URL
                </label>
                <input
                  type="url"
                  name="twitterUrl"
                  id="twitterUrl"
                  defaultValue={teamMember.twitterUrl || ''}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500"
                />
              </div>

              <div>
                <label htmlFor="githubUrl" className="block text-sm font-medium text-gray-700">
                  GitHub URL
                </label>
                <input
                  type="url"
                  name="githubUrl"
                  id="githubUrl"
                  defaultValue={teamMember.githubUrl || ''}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500"
                />
              </div>

              <div>
                <label htmlFor="emailAddress" className="block text-sm font-medium text-gray-700">
                  Email Address
                </label>
                <input
                  type="email"
                  name="emailAddress"
                  id="emailAddress"
                  defaultValue={teamMember.emailAddress || ''}
                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500"
                />
              </div>
            </div>
          </div>

          <div className="flex justify-end space-x-3">
            <Link
              href="/admin/team"
              className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
            >
              Cancel
            </Link>
            <button
              type="submit"
              disabled={loading}
              className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-500 hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Saving...' : 'Save Changes'}
            </button>
          </div>
        </form>
      </div>

      {/* S3 Image Browser Modal */}
      <S3ImageBrowser
        isOpen={showS3Browser}
        onClose={() => setShowS3Browser(false)}
        onSelectImage={handleS3ImageSelect}
        selectedCategory="all"
        forceRefresh={recentUpload}
        prefix="images/team"
      />
    </div>
  );
}