'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import {
  PlusIcon,
  ArrowPathIcon,
  PencilIcon,
  TrashIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { TeamMember } from '@/types/team';
import { toast } from 'react-hot-toast';
import ConfirmDialog from '@/components/admin/ConfirmDialog';

export default function TeamMembersPage() {
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [deleteDialog, setDeleteDialog] = useState<{
    isOpen: boolean;
    member: TeamMember | null;
    loading: boolean;
  }>({
    isOpen: false,
    member: null,
    loading: false
  });

  const fetchTeamMembers = async () => {
    try {
      setLoading(true);
      setError('');

      const response = await fetch('/api/admin/team');

      if (!response.ok) {
        throw new Error(`Error: ${response.status}`);
      }

      const data = await response.json();
      
      // Ensure data is an array
      if (Array.isArray(data)) {
        setTeamMembers(data);
      } else if (data.data && Array.isArray(data.data)) {
        setTeamMembers(data.data);
      } else {
        console.error('Unexpected data format:', data);
        setTeamMembers([]);
        setError('Unexpected data format received');
      }
    } catch (err) {
      setError('Failed to load team members');
      setTeamMembers([]);
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTeamMembers();
  }, []);

  const handleDeleteClick = (member: TeamMember) => {
    setDeleteDialog({
      isOpen: true,
      member,
      loading: false
    });
  };

  const handleDeleteConfirm = async () => {
    if (!deleteDialog.member) return;

    setDeleteDialog(prev => ({ ...prev, loading: true }));

    try {
      console.log('Deleting team member with CSRF protection...');
      
      const response = await fetch(`/api/admin/team/${deleteDialog.member.id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        let errorMessage = `Error: ${response.status}`;
        try {
          const errorData = await response.json();
          errorMessage = errorData.error || errorMessage;
        } catch (jsonError) {
          console.error('Failed to parse error response:', jsonError);
        }
        console.error('Team member deletion failed:', errorMessage);
        throw new Error(errorMessage);
      }

      let result = null;
      try {
        result = await response.json();
        console.log('Team member deleted successfully:', result);
      } catch (jsonError) {
        console.log('Team member deleted successfully (no response body)');
      }
      
      toast.success(`${deleteDialog.member.name} has been removed from the team`);
      
      // Close dialog
      setDeleteDialog({ isOpen: false, member: null, loading: false });
      
      // Force refresh the team members list
      await fetchTeamMembers();
      
    } catch (err) {
      console.error('Error deleting team member:', err);
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      toast.error(`Failed to delete team member: ${errorMessage}`);
      setDeleteDialog(prev => ({ ...prev, loading: false }));
    }
  };

  const handleDeleteCancel = () => {
    setDeleteDialog({ isOpen: false, member: null, loading: false });
  };

  const handleReorder = async (id: string, direction: 'up' | 'down') => {
    const currentIndex = teamMembers.findIndex(member => member.id === id);
    if (
      currentIndex === -1 ||
      (direction === 'up' && currentIndex === 0) ||
      (direction === 'down' && currentIndex === teamMembers.length - 1)
    ) {
      return;
    }

    const newOrder = [...teamMembers];
    const targetIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;

    // Ensure both indices are valid
    if (
      currentIndex < 0 || currentIndex >= newOrder.length ||
      targetIndex < 0 || targetIndex >= newOrder.length
    ) {
      return;
    }

    const currentMember = newOrder[currentIndex];
    const targetMember = newOrder[targetIndex];

    if (!currentMember || !targetMember) {
      return;
    }

    // Swap the order values
    const currentOrder = currentMember.order;
    currentMember.order = targetMember.order;
    targetMember.order = currentOrder;

    // Update both team members
    try {
      const formData1 = new FormData();
      formData1.append('name', currentMember.name);
      formData1.append('role', currentMember.role);
      formData1.append('bio', currentMember.bio);
      formData1.append('order', currentMember.order.toString());

      if (currentMember.linkedinUrl) {
        formData1.append('linkedinUrl', currentMember.linkedinUrl);
      }
      if (currentMember.twitterUrl) {
        formData1.append('twitterUrl', currentMember.twitterUrl);
      }
      if (currentMember.githubUrl) {
        formData1.append('githubUrl', currentMember.githubUrl);
      }
      if (currentMember.emailAddress) {
        formData1.append('emailAddress', currentMember.emailAddress);
      }

      const formData2 = new FormData();
      formData2.append('name', targetMember.name);
      formData2.append('role', targetMember.role);
      formData2.append('bio', targetMember.bio);
      formData2.append('order', targetMember.order.toString());

      if (targetMember.linkedinUrl) {
        formData2.append('linkedinUrl', targetMember.linkedinUrl);
      }
      if (targetMember.twitterUrl) {
        formData2.append('twitterUrl', targetMember.twitterUrl);
      }
      if (targetMember.githubUrl) {
        formData2.append('githubUrl', targetMember.githubUrl);
      }
      if (targetMember.emailAddress) {
        formData2.append('emailAddress', targetMember.emailAddress);
      }

      console.log('Updating team member order with CSRF protection...');
      await Promise.all([
        fetch(`/api/admin/team/${currentMember.id}`, {
          method: 'PUT',
          body: formData1,
        }),
        fetch(`/api/admin/team/${targetMember.id}`, {
          method: 'PUT',
          body: formData2,
        })
      ]);

      toast.success('Team member order updated');
      fetchTeamMembers();
    } catch (err) {
      toast.error('Failed to update team member order');
      console.error(err);
    }
  };

  const formatDate = (dateString: string | Date) => {
    const date = typeof dateString === 'string' ? new Date(dateString) : dateString;
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    }).format(date);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold text-slate-800">Team Members</h1>
        <div className="flex space-x-2">
          <button
            onClick={fetchTeamMembers}
            className="p-2 rounded-lg text-slate-500 hover:text-slate-700 hover:bg-slate-100"
            disabled={loading}
          >
            <ArrowPathIcon className={`h-5 w-5 ${loading ? 'animate-spin' : ''}`} />
          </button>
          <Link
            href="/admin/team/new"
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-500 hover:bg-orange-600"
          >
            <PlusIcon className="h-4 w-4 mr-1" />
            New Team Member
          </Link>
        </div>
      </div>

      {loading ? (
        <div className="flex justify-center py-8">
          <ArrowPathIcon className="h-8 w-8 animate-spin text-orange-500" />
        </div>
      ) : error ? (
        <div className="bg-red-50 p-4 rounded-md">
          <p className="text-red-800">{error}</p>
        </div>
      ) : teamMembers.length === 0 ? (
        <div className="text-center py-12 bg-white rounded-lg shadow-sm">
          <svg
            className="mx-auto h-12 w-12 text-gray-400"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
            aria-hidden="true"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
          </svg>
          <h3 className="mt-2 text-lg font-medium text-slate-800">No team members found</h3>
          <p className="mt-1 text-slate-500">Get started by adding your first team member.</p>
          <div className="mt-6">
            <Link
              href="/admin/team/new"
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-500 hover:bg-orange-600"
            >
              <PlusIcon className="h-4 w-4 mr-1" />
              Add your first team member
            </Link>
          </div>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {teamMembers.map((member) => (
            <div key={member.id} className="bg-white rounded-lg shadow-sm overflow-hidden">
              <div className="p-6">
                <div className="flex items-start">
                  <div className="flex-shrink-0 h-16 w-16 relative rounded-full overflow-hidden bg-gray-100">
                    <Image
                      src={member.imageSrc}
                      alt={member.name}
                      width={64}
                      height={64}
                      className="object-cover"
                    />
                  </div>
                  <div className="ml-4 flex-1">
                    <h3 className="text-lg font-medium text-gray-900">{member.name}</h3>
                    <p className="text-sm text-gray-500">{member.role}</p>
                    <p className="mt-1 text-sm text-gray-600">{member.bio}</p>
                  </div>
                </div>
                <div className="mt-4 flex justify-between items-center">
                  <div className="text-sm text-gray-500">
                    Added {formatDate(member.createdAt)}
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={() => handleReorder(member.id, 'up')}
                      className="text-gray-400 hover:text-gray-600"
                      disabled={teamMembers.indexOf(member) === 0}
                    >
                      <ArrowUpIcon className="h-5 w-5" />
                    </button>
                    <button
                      onClick={() => handleReorder(member.id, 'down')}
                      className="text-gray-400 hover:text-gray-600"
                      disabled={teamMembers.indexOf(member) === teamMembers.length - 1}
                    >
                      <ArrowDownIcon className="h-5 w-5" />
                    </button>
                    <Link href={`/admin/team/${member.id}/edit`} className="text-blue-400 hover:text-blue-600">
                      <PencilIcon className="h-5 w-5" />
                    </Link>
                    <button
                      onClick={() => handleDeleteClick(member)}
                      className="text-red-400 hover:text-red-600"
                      disabled={deleteDialog.loading && deleteDialog.member?.id === member.id}
                    >
                      <TrashIcon className="h-5 w-5" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Enhanced Delete Confirmation Dialog */}
      <ConfirmDialog
        isOpen={deleteDialog.isOpen}
        onClose={handleDeleteCancel}
        onConfirm={handleDeleteConfirm}
        title="Remove Team Member"
        message={
          deleteDialog.member 
            ? `Are you sure you want to remove ${deleteDialog.member.name} from the team? This action cannot be undone.`
            : 'Are you sure you want to remove this team member?'
        }
        confirmText="Remove Member"
        cancelText="Keep Member"
        confirmButtonClass="bg-red-600 hover:bg-red-700 focus:ring-red-500"
        cancelButtonClass="bg-white hover:bg-gray-50 focus:ring-orange-500 text-gray-700 border-gray-300"
        icon={
          <div className="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
            <ExclamationTriangleIcon className="h-6 w-6 text-red-600" aria-hidden="true" />
          </div>
        }
        loading={deleteDialog.loading}
      />
    </div>
  );
}
