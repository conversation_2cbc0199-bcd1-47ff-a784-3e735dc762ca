'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'react-hot-toast';
import { PlusIcon, CalendarIcon, ClockIcon, TrashIcon, PencilIcon, PlayIcon } from '@heroicons/react/24/outline';
import AdminLayout from '@/components/admin/AdminLayout';
import { format } from 'date-fns';

interface ScheduledBlogPost {
  id: string;
  category: string | null;
  tone: string | null;
  length: string | null;
  targetAudience: string | null;
  scheduledDate: string;
  status: 'pending' | 'completed' | 'failed';
  blogPostId: string | null;
  createdAt: string;
  updatedAt: string;
}

export default function ScheduledBlogPostsPage() {
  const router = useRouter();
  const [scheduledPosts, setScheduledPosts] = useState<ScheduledBlogPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [runningNow, setRunningNow] = useState(false);
  const [loadingOperations, setLoadingOperations] = useState<Set<string>>(new Set());

  // Helper to manage loading states
  const setOperationLoading = (operation: string, isLoading: boolean) => {
    setLoadingOperations(prev => {
      const newSet = new Set(prev);
      if (isLoading) {
        newSet.add(operation);
      } else {
        newSet.delete(operation);
      }
      return newSet;
    });
  };

  // Fetch scheduled blog posts
  const fetchScheduledPosts = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await fetch('/api/admin/scheduled-blog-posts');
      
      if (!response.ok) {
        throw new Error('Failed to fetch scheduled blog posts');
      }
      
      const data = await response.json();
      setScheduledPosts(data);
    } catch (error) {
      setError(error instanceof Error ? error.message : 'An error occurred');
      toast.error('Failed to load scheduled blog posts');
    } finally {
      setLoading(false);
    }
  };

  // Run blog post generation now
  const runNow = async () => {
    try {
      setRunningNow(true);
      
      const response = await fetch('/api/admin/scheduled-blog-posts/run-now', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          runType: 'api',
          publishImmediately: true
        }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to run blog post generation');
      }
      
      const data = await response.json();
      
      if (data.success) {
        toast.success('Blog post generated successfully');
        fetchScheduledPosts(); // Refresh the list
      } else {
        throw new Error(data.message || 'Failed to generate blog post');
      }
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'An error occurred');
    } finally {
      setRunningNow(false);
    }
  };

  // Delete a scheduled blog post
  const deleteScheduledPost = async (id: string) => {
    if (!confirm('Are you sure you want to delete this scheduled blog post?')) {
      return;
    }

    // Store the original post for potential rollback
    const postToDelete = scheduledPosts.find(p => p.id === id);
    if (!postToDelete) return;
    
    try {
      // Set loading state
      setOperationLoading(`delete-${id}`, true);

      const response = await fetch(`/api/admin/scheduled-blog-posts/${id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      if (!response.ok) {
        throw new Error('Failed to delete scheduled blog post');
      }
      
      // Success - remove the post from the UI immediately
      setScheduledPosts(prevPosts => prevPosts.filter(post => post.id !== id));
      toast.success('Scheduled blog post deleted successfully');
    } catch (error) {
      console.error('Error deleting scheduled blog post:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to delete scheduled blog post. Please try again.');
    } finally {
      // Clear loading state
      setOperationLoading(`delete-${id}`, false);
    }
  };

  // Load data on component mount
  useEffect(() => {
    fetchScheduledPosts();
  }, []);

  return (
    <AdminLayout>
      <div className="px-4 sm:px-6 lg:px-8 py-8">
        <div className="sm:flex sm:items-center">
          <div className="sm:flex-auto">
            <h1 className="text-2xl font-semibold text-gray-900">Scheduled Blog Posts</h1>
            <p className="mt-2 text-sm text-gray-700">
              Manage automated blog post generation schedule
            </p>
          </div>
          <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none space-x-2">
            <button
              type="button"
              onClick={runNow}
              disabled={runningNow}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:bg-indigo-400"
            >
              <PlayIcon className="h-4 w-4 mr-2" />
              {runningNow ? 'Generating...' : 'Generate Now'}
            </button>
            <button
              type="button"
              onClick={() => router.push('/admin/scheduled-blog-posts/new')}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              Schedule New Post
            </button>
          </div>
        </div>

        {error && (
          <div className="mt-6 bg-red-50 border border-red-200 rounded-md p-4 text-red-700">
            {error}
          </div>
        )}

        <div className="mt-8 flex flex-col">
          <div className="-my-2 -mx-4 overflow-x-auto sm:-mx-6 lg:-mx-8">
            <div className="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8">
              <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                <table className="min-w-full divide-y divide-gray-300">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">
                        Scheduled Date
                      </th>
                      <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                        Category
                      </th>
                      <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                        Settings
                      </th>
                      <th scope="col" className="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">
                        Status
                      </th>
                      <th scope="col" className="relative py-3.5 pl-3 pr-4 sm:pr-6">
                        <span className="sr-only">Actions</span>
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200 bg-white">
                    {loading ? (
                      <tr>
                        <td colSpan={5} className="py-10 text-center text-gray-500">
                          Loading scheduled blog posts...
                        </td>
                      </tr>
                    ) : scheduledPosts.length === 0 ? (
                      <tr>
                        <td colSpan={5} className="py-10 text-center text-gray-500">
                          No scheduled blog posts found
                        </td>
                      </tr>
                    ) : (
                      scheduledPosts.map((post) => (
                        <tr key={post.id}>
                          <td className="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6">
                            <div className="flex items-center">
                              <CalendarIcon className="h-5 w-5 text-gray-400 mr-2" />
                              {format(new Date(post.scheduledDate), 'MMM d, yyyy')}
                            </div>
                            <div className="text-xs text-gray-500 mt-1 flex items-center">
                              <ClockIcon className="h-3 w-3 mr-1" />
                              {format(new Date(post.scheduledDate), 'h:mm a')}
                            </div>
                          </td>
                          <td className="whitespace-nowrap px-3 py-4 text-sm text-gray-500">
                            {post.category || 'Random'}
                          </td>
                          <td className="px-3 py-4 text-sm text-gray-500">
                            <div>Tone: {post.tone || 'Default'}</div>
                            <div>Length: {post.length || 'Default'}</div>
                          </td>
                          <td className="whitespace-nowrap px-3 py-4 text-sm">
                            <span className={`inline-flex rounded-full px-2 text-xs font-semibold leading-5 ${
                              post.status === 'completed' 
                                ? 'bg-green-100 text-green-800' 
                                : post.status === 'failed'
                                ? 'bg-red-100 text-red-800'
                                : 'bg-yellow-100 text-yellow-800'
                            }`}>
                              {post.status}
                            </span>
                            {post.blogPostId && (
                              <div className="text-xs text-blue-600 mt-1">
                                <a href={`/admin/blog/${post.blogPostId}/edit`} target="_blank" rel="noopener noreferrer">
                                  View Post
                                </a>
                              </div>
                            )}
                          </td>
                          <td className="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6">
                            <button
                              onClick={() => router.push(`/admin/scheduled-blog-posts/${post.id}/edit`)}
                              className="text-indigo-600 hover:text-indigo-900 mr-4"
                            >
                              <PencilIcon className="h-5 w-5" />
                              <span className="sr-only">Edit</span>
                            </button>
                            <button
                              onClick={() => deleteScheduledPost(post.id)}
                              disabled={loadingOperations.has(`delete-${post.id}`)}
                              className="text-red-600 hover:text-red-900 disabled:opacity-50 disabled:cursor-not-allowed"
                            >
                              {loadingOperations.has(`delete-${post.id}`) ? (
                                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-red-600"></div>
                              ) : (
                                <TrashIcon className="h-5 w-5" />
                              )}
                              <span className="sr-only">Delete</span>
                            </button>
                          </td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}
