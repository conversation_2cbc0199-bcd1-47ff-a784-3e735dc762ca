'use client';
import React from 'react';

import { useState, useEffect, useCallback } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Link from 'next/link';
import {
  ArrowLeftIcon,
  PrinterIcon,
  PencilIcon,
  TrashIcon,
  DocumentArrowDownIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  CreditCardIcon
} from '@heroicons/react/24/outline';
import { useNotification } from '@/contexts/NotificationContext';
import { format } from 'date-fns';
import AddPaymentForm from '@/components/admin/AddPaymentForm';

interface ReceiptItem {
  id: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  description: string | null;
  discount: number;
  discountType: string;
  isCustomService: boolean;
  serviceId: string | null;
  service?: {
    name: string;
  };
}

interface Receipt {
  id: string;
  receiptNumber: string;
  totalAmount: number;
  amountPaid: number;
  balance: number;
  customerName: string;
  phoneNumber: string;
  email: string | null;
  status: string;
  notes: string | null;
  createdAt: string;
  updatedAt: string;
  issuedAt: string;
  paidAt: string | null;
  transactionId: string;
  items: ReceiptItem[];
  pdfUrl?: string;
  transaction?: {
    transactionId: string;
    rawMessage: string;
    transactionDate: string;
  };
}

export default function ReceiptDetailPage() {
  const params = useParams();
  const router = useRouter();
  const receiptId = params?.id as string;
  const { showNotification } = useNotification();

  const [receipt, setReceipt] = useState<Receipt | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deleting, setDeleting] = useState(false);
  const [updating, setUpdating] = useState(false);
  const [showAddPaymentForm, setShowAddPaymentForm] = useState(false);

  const fetchReceipt = useCallback(async () => {
    if (!receiptId) return;

    try {
      setLoading(true);
      setError(null);

      console.log('[Receipt Detail] Fetching receipt:', receiptId);
      
      const response = await fetch(`/api/admin/comprehensive-receipts/${receiptId}`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch receipt: ${response.status}`);
      }

      const result = await response.json();
      console.log('[Receipt Detail] API response:', result);
      
      // Extract receipt from data property
      const receipt = result.data || result;
      console.log('[Receipt Detail] Loaded receipt:', receipt);
      setReceipt(receipt || null);
      
    } catch (err) {
      console.error('[Receipt Detail] Error fetching receipt:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to load receipt';
      setError(errorMessage);
      showNotification('error', `Failed to load receipt: ${errorMessage}`);
    } finally {
      setLoading(false);
    }
  }, [receiptId, showNotification]);

  useEffect(() => {
    fetchReceipt();
  }, [fetchReceipt]);

  const handlePrint = () => {
    if (!receipt) return;
    window.open(`/api/receipts/${receipt.id}`, '_blank');
  };

  const handleDownload = () => {
    if (!receipt) return;
    const link = document.createElement('a');
    link.href = `/api/receipts/${receipt.id}`;
    link.download = `${receipt.receiptNumber}.pdf`;
    link.click();
  };

  const handleUpdateStatus = async (newStatus: string) => {
    if (!receipt) return;

    try {
      setUpdating(true);

      const response = await fetch(`/api/admin/comprehensive-receipts/${receipt.id}`, {
        method: 'PUT',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus })
      });

      if (!response.ok) {
        throw new Error(`Failed to update status: ${response.status}`);
      }

      setReceipt(prev => prev ? { ...prev, status: newStatus } : null);
      showNotification('success', `Receipt status updated to ${newStatus}`);
      
    } catch (err) {
      console.error('[Receipt Detail] Error updating status:', err);
      showNotification('error', 'Failed to update receipt status');
    } finally {
      setUpdating(false);
    }
  };

  const handleDelete = async () => {
    if (!receipt) return;

    if (!window.confirm(`Are you sure you want to delete receipt ${receipt.receiptNumber}? This action cannot be undone.`)) {
      return;
    }

    try {
      setDeleting(true);

      const response = await fetch(`/api/admin/comprehensive-receipts/${receipt.id}`, {
        method: 'DELETE',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to delete receipt: ${response.status}`);
      }

      showNotification('success', 'Receipt deleted successfully');
      router.push('/admin/comprehensive-receipts/list');
      
    } catch (err) {
      console.error('[Receipt Detail] Error deleting receipt:', err);
      showNotification('error', 'Failed to delete receipt');
    } finally {
      setDeleting(false);
    }
  };

  const handlePaymentAdded = (data: any) => {
    // Update receipt with new payment information
    console.log('[Receipt Detail] Payment added callback received:', data);
    if (data.receipt) {
      console.log('[Receipt Detail] Updating receipt with new data:', {
        id: data.receipt.id,
        totalAmount: data.receipt.totalAmount,
        amountPaid: data.receipt.amountPaid,
        balance: data.receipt.balance
      });
      setReceipt(data.receipt);
      // Close the add payment form
      setShowAddPaymentForm(false);
      showNotification('success', `Payment of KES ${data.paymentSummary.additionalPayment.toLocaleString()} added successfully`);
    }
  };

  const formatDate = (dateString: string | null | undefined) => {
    if (!dateString) return 'N/A';
    try {
      return format(new Date(dateString), 'dd/MM/yyyy HH:mm');
    } catch {
      return 'Invalid Date';
    }
  };

  const formatAmount = (amount: any): string => {
    try {
      const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
      if (isNaN(numAmount)) return '0.00';
      return numAmount.toLocaleString('en-KE', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });
    } catch {
      return '0.00';
    }
  };

  const getStatusBadge = (status: string) => {
    const badges = {
      issued: { bg: 'bg-yellow-100', text: 'text-yellow-800', icon: ClockIcon, label: 'Issued' },
      paid: { bg: 'bg-green-100', text: 'text-green-800', icon: CheckCircleIcon, label: 'Paid' },
      overpaid: { bg: 'bg-blue-100', text: 'text-blue-800', icon: CheckCircleIcon, label: 'Overpaid' },
      cancelled: { bg: 'bg-red-100', text: 'text-red-800', icon: XCircleIcon, label: 'Cancelled' },
      overdue: { bg: 'bg-red-100', text: 'text-red-800', icon: ClockIcon, label: 'Overdue' }
    };

    const badge = badges[status as keyof typeof badges] || badges.issued;
    const Icon = badge.icon;

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${badge.bg} ${badge.text}`}>
        <Icon className="h-3 w-3 mr-1" />
        {badge.label}
      </span>
    );
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Error Loading Receipt</h1>
          <p className="text-gray-600 mb-6">{error}</p>
          <Link
            href="/admin/comprehensive-receipts/list"
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Back to Receipts
          </Link>
        </div>
      </div>
    );
  }

  if (!receipt) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Receipt Not Found</h1>
          <p className="text-gray-600 mb-6">The receipt you're looking for doesn't exist.</p>
          <Link
            href="/admin/comprehensive-receipts/list"
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
          >
            <ArrowLeftIcon className="h-4 w-4 mr-2" />
            Back to Receipts
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4 sm:space-y-6">
      {/* Header */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
            <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-4 space-y-2 sm:space-y-0">
              <Link
                href="/admin/comprehensive-receipts/list"
                className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 self-start"
              >
                <ArrowLeftIcon className="h-4 w-4 mr-1" />
                Back to Receipts
              </Link>
              <div>
                <h1 className="text-xl sm:text-2xl font-bold text-gray-900">Receipt {receipt.receiptNumber}</h1>
                <p className="text-sm sm:text-base text-gray-600">Created on {formatDate(receipt.createdAt)}</p>
              </div>
            </div>
            <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
              <button
                onClick={handlePrint}
                className="inline-flex items-center justify-center px-3 sm:px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                <PrinterIcon className="h-4 w-4 mr-2" />
                Print
              </button>
              <button
                onClick={handleDownload}
                className="inline-flex items-center justify-center px-3 sm:px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                <DocumentArrowDownIcon className="h-4 w-4 mr-2" />
                Download
              </button>
              <button
                onClick={handleDelete}
                disabled={deleting}
                className="inline-flex items-center justify-center px-3 sm:px-4 py-2 border border-red-300 rounded-md shadow-sm text-sm font-medium text-red-700 bg-white hover:bg-red-50 disabled:opacity-50"
              >
                <TrashIcon className="h-4 w-4 mr-2" />
                {deleting ? 'Deleting...' : 'Delete'}
              </button>
            </div>
          </div>
        </div>

        {/* Status and Actions */}
        <div className="px-4 sm:px-6 py-4 bg-gray-50 border-b border-gray-200">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-500">Status:</span>
              {getStatusBadge(receipt.status)}
            </div>
            <div className="flex flex-wrap gap-2">
              {receipt.status !== 'cancelled' && (
                <button
                  onClick={() => setShowAddPaymentForm(true)}
                  className="inline-flex items-center px-3 py-1.5 border border-blue-300 rounded text-xs font-medium text-blue-700 bg-white hover:bg-blue-50"
                >
                  <CreditCardIcon className="h-3 w-3 mr-1" />
                  Add Payment
                </button>
              )}
              {receipt.status === 'issued' && (
                <button
                  onClick={() => handleUpdateStatus('paid')}
                  disabled={updating}
                  className="inline-flex items-center px-3 py-1.5 border border-green-300 rounded text-xs font-medium text-green-700 bg-white hover:bg-green-50 disabled:opacity-50"
                >
                  Mark as Paid
                </button>
              )}
              {receipt.status !== 'cancelled' && (
                <button
                  onClick={() => handleUpdateStatus('cancelled')}
                  disabled={updating}
                  className="inline-flex items-center px-3 py-1.5 border border-red-300 rounded text-xs font-medium text-red-700 bg-white hover:bg-red-50 disabled:opacity-50"
                >
                  Cancel
                </button>
              )}
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 xl:grid-cols-3 gap-4 sm:gap-6">
        {/* Receipt Details */}
        <div className="xl:col-span-2 space-y-4 sm:space-y-6">
          {/* Customer Information */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
              <h2 className="text-base sm:text-lg font-medium text-gray-900">Customer Information</h2>
            </div>
            <div className="px-4 sm:px-6 py-4">
              <dl className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                <div>
                  <dt className="text-sm font-medium text-gray-500">Name</dt>
                  <dd className="mt-1 text-sm text-gray-900 break-words">{receipt.customerName}</dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">Phone Number</dt>
                  <dd className="mt-1 text-sm text-gray-900">{receipt.phoneNumber}</dd>
                </div>
                {receipt.email && (
                  <div className="sm:col-span-2">
                    <dt className="text-sm font-medium text-gray-500">Email</dt>
                    <dd className="mt-1 text-sm text-gray-900 break-all">{receipt.email}</dd>
                  </div>
                )}
              </dl>
            </div>
          </div>

          {/* Receipt Items */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
              <h2 className="text-base sm:text-lg font-medium text-gray-900">Receipt Items</h2>
            </div>
            {/* Mobile-friendly items display */}
            <div className="block sm:hidden">
              {(!receipt.items || receipt.items.length === 0) ? (
                <div className="px-4 py-8 text-center text-gray-500">
                  <p>No items found for this receipt.</p>
                </div>
              ) : (
                <div className="divide-y divide-gray-200">
                  {receipt.items.map((item) => (
                  <div key={item.id} className="px-4 py-4">
                    <div className="flex justify-between items-start mb-2">
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {item.description || item.service?.name || 'Service'}
                        </p>
                        {item.isCustomService && (
                          <p className="text-xs text-blue-600">Custom Service</p>
                        )}
                      </div>
                      <div className="ml-2 text-right">
                        <p className="text-sm font-medium text-gray-900">
                          KES {formatAmount(item.totalPrice)}
                        </p>
                      </div>
                    </div>
                    <div className="flex justify-between text-xs text-gray-500">
                      <span>Qty: {item.quantity} × KES {formatAmount(item.unitPrice)}</span>
                      {item.discount > 0 && (
                        <span className="text-green-600">
                          Discount: {item.discountType === 'percentage' ? `${item.discount}%` : `KES ${formatAmount(item.discount)}`}
                        </span>
                      )}
                    </div>
                  </div>
                ))}
                </div>
              )}
            </div>
            {/* Desktop table display */}
            <div className="hidden sm:block overflow-x-auto">
              {(!receipt.items || receipt.items.length === 0) ? (
                <div className="px-6 py-8 text-center text-gray-500">
                  <p>No items found for this receipt.</p>
                </div>
              ) : (
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Item
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Quantity
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Unit Price
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Discount
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Total
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {receipt.items.map((item) => (
                    <tr key={item.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {item.description || item.service?.name || 'Service'}
                        </div>
                        {item.isCustomService && (
                          <div className="text-xs text-blue-600">Custom Service</div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {item.quantity}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        KES {formatAmount(item.unitPrice)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {item.discount > 0 ? (
                          <span>
                            {item.discountType === 'percentage' ? `${item.discount}%` : `KES ${formatAmount(item.discount)}`}
                          </span>
                        ) : (
                          '-'
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium text-gray-900">
                        KES {formatAmount(item.totalPrice)}
                      </td>
                    </tr>
                    ))}
                  </tbody>
                </table>
              )}
            </div>
          </div>

          {/* Notes */}
          {receipt.notes && (
            <div className="bg-white shadow rounded-lg">
              <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
                <h2 className="text-base sm:text-lg font-medium text-gray-900">Notes</h2>
              </div>
              <div className="px-4 sm:px-6 py-4">
                <p className="text-sm text-gray-700 break-words">{receipt.notes}</p>
              </div>
            </div>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-4 sm:space-y-6">
          {/* Payment Summary */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
              <h2 className="text-base sm:text-lg font-medium text-gray-900">Payment Summary</h2>
            </div>
            <div className="px-4 sm:px-6 py-4">
              <dl className="space-y-3">
                <div className="flex justify-between">
                  <dt className="text-sm text-gray-500">Subtotal</dt>
                  <dd className="text-sm font-medium text-gray-900">KES {formatAmount(receipt.totalAmount)}</dd>
                </div>
                <div className="flex justify-between">
                  <dt className="text-sm text-gray-500">Amount Paid</dt>
                  <dd className="text-sm font-medium text-green-600">KES {formatAmount(receipt.amountPaid)}</dd>
                </div>
                <div className="border-t border-gray-200 pt-3">
                  <div className="flex justify-between">
                    <dt className="text-base font-medium text-gray-900">
                      {receipt.balance < 0 ? 'Credit Balance' : 'Outstanding Balance'}
                    </dt>
                    <dd className={`text-base font-medium ${
                      receipt.balance > 0 ? 'text-red-600' : 
                      receipt.balance < 0 ? 'text-blue-600' : 'text-green-600'
                    }`}>
                      {receipt.balance < 0 ? '+' : ''}KES {formatAmount(Math.abs(receipt.balance))}
                    </dd>
                  </div>
                  {receipt.balance < 0 && (
                    <div className="mt-2 text-xs text-blue-600">
                      💡 This receipt has a credit balance (overpayment/tip)
                    </div>
                  )}
                </div>
              </dl>
            </div>
          </div>

          {/* Receipt Information */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
              <h2 className="text-base sm:text-lg font-medium text-gray-900">Receipt Information</h2>
            </div>
            <div className="px-4 sm:px-6 py-4">
              <dl className="space-y-3">
                <div>
                  <dt className="text-sm font-medium text-gray-500">Receipt Number</dt>
                  <dd className="mt-1 text-sm text-gray-900 break-all">{receipt.receiptNumber}</dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">Transaction ID</dt>
                  <dd className="mt-1 text-sm text-gray-900 break-all">{receipt.transactionId}</dd>
                </div>
                <div>
                  <dt className="text-sm font-medium text-gray-500">Issued Date</dt>
                  <dd className="mt-1 text-sm text-gray-900">{formatDate(receipt.issuedAt)}</dd>
                </div>
                {receipt.paidAt && (
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Fully Paid Date</dt>
                    <dd className="mt-1 text-sm text-gray-900">{formatDate(receipt.paidAt)}</dd>
                  </div>
                )}
                {receipt.amountPaid > 0 && !receipt.paidAt && (
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Last Payment Date</dt>
                    <dd className="mt-1 text-sm text-gray-900">{formatDate(receipt.updatedAt || receipt.createdAt)}</dd>
                  </div>
                )}
              </dl>
            </div>
          </div>

          {/* Payment History */}
          {receipt.notes && receipt.notes.includes('Additional payment:') && (
            <div className="bg-white shadow rounded-lg">
              <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
                <h2 className="text-base sm:text-lg font-medium text-gray-900">Payment History</h2>
              </div>
              <div className="px-4 sm:px-6 py-4">
                <div className="space-y-3">
                  {receipt.notes.split('\n\n').map((note, index) => {
                    if (note.includes('Additional payment:')) {
                      // Try to match with date first, then fallback to without date
                      const paymentMatchWithDate = note.match(/Additional payment: ([A-Z0-9]+) - KES ([0-9,]+(?:\.[0-9]{2})?) \(([^)]+)\)/);
                      const paymentMatchWithoutDate = note.match(/Additional payment: ([A-Z0-9]+) - KES ([0-9,]+(?:\.[0-9]{2})?)/);
                      
                      const match = paymentMatchWithDate || paymentMatchWithoutDate;
                      if (match) {
                        const transactionId = match[1];
                        const amount = match[2];
                        const paymentDate = match[3]; // Will be undefined if no date captured
                        
                        return (
                          <div key={index} className="flex justify-between items-center py-2 border-b border-gray-100 last:border-b-0">
                            <div>
                              <div className="text-sm font-medium text-gray-900">Payment Received</div>
                              <div className="text-xs text-gray-500">Transaction: {transactionId}</div>
                              {paymentDate && (
                                <div className="text-xs text-gray-400">Date: {paymentDate}</div>
                              )}
                            </div>
                            <div className="text-sm font-medium text-green-600">+KES {amount}</div>
                          </div>
                        );
                      }
                    }
                    return null;
                  }).filter(Boolean)}
                </div>
              </div>
            </div>
          )}

          {/* Transaction Details */}
          {receipt.transaction && (
            <div className="bg-white shadow rounded-lg">
              <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
                <h2 className="text-base sm:text-lg font-medium text-gray-900">M-Pesa Transaction</h2>
              </div>
              <div className="px-4 sm:px-6 py-4">
                <dl className="space-y-3">
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Transaction Date</dt>
                    <dd className="mt-1 text-sm text-gray-900">{formatDate(receipt.transaction.transactionDate)}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Original Message</dt>
                    <dd className="mt-1 text-xs text-gray-700 bg-gray-50 p-2 rounded font-mono break-all">
                      {receipt.transaction.rawMessage}
                    </dd>
                  </div>
                </dl>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Add Payment Form Modal */}
      {receipt && (
        <AddPaymentForm
          receipt={receipt}
          isOpen={showAddPaymentForm}
          onClose={() => setShowAddPaymentForm(false)}
          onPaymentAdded={handlePaymentAdded}
        />
      )}
    </div>
  );
} 