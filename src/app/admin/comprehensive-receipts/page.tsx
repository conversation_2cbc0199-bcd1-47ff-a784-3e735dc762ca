import { Metadata } from 'next';
import Link from 'next/link';
import ComprehensiveReceiptForm from '@/components/admin/ComprehensiveReceiptForm';

export const metadata: Metadata = {
  title: 'Create Receipt | Admin Dashboard',
  description: 'Create receipts from M-Pesa transactions',
};

export default function ComprehensiveReceiptsPage() {
  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Create Receipt</h1>
              <p className="text-gray-600 mt-1">
                Generate receipts from M-Pesa transaction messages
              </p>
            </div>
            <div>
              <Link
                href="/admin/comprehensive-receipts/list"
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <svg className="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                View All Receipts
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Main Form */}
      <ComprehensiveReceiptForm />
    </div>
  );
} 