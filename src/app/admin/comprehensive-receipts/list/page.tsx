'use client';
import React from 'react';

import { useState, useEffect, useCallback } from 'react';
import Link from 'next/link';
import {
  ArrowPathIcon,
  ReceiptRefundIcon,
  EyeIcon,
  PrinterIcon,
  TrashIcon,
  PlusIcon
} from '@heroicons/react/24/outline';
import { useNotification } from '@/contexts/NotificationContext';
import { format } from 'date-fns';

interface ReceiptItem {
  id: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  description: string | null;
  discount: number;
  discountType: string;
  isCustomService: boolean;
  serviceId: string | null;
  service?: {
    name: string;
  };
}

interface Receipt {
  id: string;
  receiptNumber: string;
  totalAmount: number;
  amountPaid: number;
  balance: number;
  customerName: string;
  phoneNumber: string;
  email: string | null;
  status: string;
  notes: string | null;
  createdAt: string;
  issuedAt: string;
  paidAt: string | null;
  transactionId: string;
  items: ReceiptItem[];
  pdfUrl?: string;
}

export default function ReceiptListPage() {
  const [receipts, setReceipts] = useState<Receipt[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deleting, setDeleting] = useState<string | null>(null);

  const { showNotification } = useNotification();

  const fetchReceipts = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('[Receipts] Fetching comprehensive receipts...');
      
      const response = await fetch('/api/admin/comprehensive-receipts', {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch receipts: ${response.status}`);
      }

      const result = await response.json();
      const receipts = result.data || [];
      console.log('[Receipts] Loaded:', receipts?.length || 0, 'receipts');
      setReceipts(receipts);
      
    } catch (err) {
      console.error('[Receipts] Error fetching receipts:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to load receipts';
      setError(errorMessage);
      showNotification('error', `Failed to load receipts: ${errorMessage}`);
      setReceipts([]);
    } finally {
      setLoading(false);
    }
  }, [showNotification]);

  useEffect(() => {
    fetchReceipts();
  }, [fetchReceipts]);

  const handleDeleteReceipt = async (receiptId: string, receiptNumber: string) => {
    if (!window.confirm(`Are you sure you want to delete receipt ${receiptNumber}? This action cannot be undone.`)) {
      return;
    }

    const originalReceipts = [...receipts];

    try {
      setDeleting(receiptId);
      setReceipts(prev => prev.filter(receipt => receipt.id !== receiptId));

      console.log(`[Receipts] Deleting receipt ${receiptId}...`);
      
      const response = await fetch(`/api/admin/comprehensive-receipts/${receiptId}`, {
        method: 'DELETE',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to delete receipt: ${response.status}`);
      }

      showNotification('success', 'Receipt deleted successfully');
      
    } catch (err) {
      console.error('[Receipts] Error deleting receipt:', err);
      setReceipts(originalReceipts);
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete receipt';
      showNotification('error', `Failed to delete receipt: ${errorMessage}`);
    } finally {
      setDeleting(null);
    }
  };

  const handlePrintReceipt = (receiptId: string) => {
    window.open(`/api/receipts/${receiptId}`, '_blank');
  };

  const formatSafeDate = (dateString: string | null | undefined) => {
    if (!dateString) return 'N/A';
    try {
      return format(new Date(dateString), 'dd/MM/yyyy HH:mm');
    } catch {
      return 'Invalid Date';
    }
  };

  const formatAmount = (amount: any): string => {
    try {
      const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
      if (isNaN(numAmount)) return '0';
      return numAmount.toLocaleString('en-KE', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
      });
    } catch {
      return '0';
    }
  };

  const getStatusBadge = (status: string) => {
    const badges = {
      issued: { bg: 'bg-yellow-100', text: 'text-yellow-800', label: 'Issued' },
      paid: { bg: 'bg-green-100', text: 'text-green-800', label: 'Paid' },
      cancelled: { bg: 'bg-red-100', text: 'text-red-800', label: 'Cancelled' },
      overdue: { bg: 'bg-orange-100', text: 'text-orange-800', label: 'Overdue' },
    };
    
    const badge = badges[status as keyof typeof badges] || { bg: 'bg-gray-100', text: 'text-gray-800', label: status || 'Unknown' };
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${badge.bg} ${badge.text}`}>
        {badge.label}
      </span>
    );
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-4 sm:space-y-6">
      {/* Page Header */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
            <div>
              <h1 className="text-xl sm:text-2xl font-bold text-gray-900">Comprehensive Receipts</h1>
              <p className="text-sm sm:text-base text-gray-600 mt-1">
                Manage all receipts generated from M-Pesa transactions
              </p>
            </div>
            <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
              <button
                onClick={fetchReceipts}
                disabled={loading}
                className="inline-flex items-center justify-center px-3 sm:px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <ArrowPathIcon className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                Refresh
              </button>
              <Link
                href="/admin/comprehensive-receipts"
                className="inline-flex items-center justify-center px-3 sm:px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <PlusIcon className="h-4 w-4 mr-2" />
                Create Receipt
              </Link>
            </div>
          </div>
        </div>

        {/* Summary Stats */}
        <div className="px-4 sm:px-6 py-4 bg-gray-50 border-b border-gray-200">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3 sm:gap-4">
            <div className="text-center">
              <div className="text-lg sm:text-2xl font-bold text-blue-600">{receipts.length}</div>
              <div className="text-xs sm:text-sm text-gray-600">Total Receipts</div>
            </div>
            <div className="text-center">
              <div className="text-lg sm:text-2xl font-bold text-green-600">
                {receipts.filter(r => r.status === 'paid').length}
              </div>
              <div className="text-xs sm:text-sm text-gray-600">Paid</div>
            </div>
            <div className="text-center">
              <div className="text-lg sm:text-2xl font-bold text-yellow-600">
                {receipts.filter(r => r.status === 'issued').length}
              </div>
              <div className="text-xs sm:text-sm text-gray-600">Issued</div>
            </div>
            <div className="text-center">
              <div className="text-lg sm:text-2xl font-bold text-gray-900">
                KES {formatAmount(receipts.reduce((sum, r) => sum + r.totalAmount, 0))}
              </div>
              <div className="text-xs sm:text-sm text-gray-600">Total Value</div>
            </div>
          </div>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error Loading Receipts</h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{error}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Receipts Display */}
      <div className="bg-white shadow rounded-lg overflow-hidden">
        {receipts.length === 0 ? (
          <div className="px-4 sm:px-6 py-12 text-center text-gray-500">
            <ReceiptRefundIcon className="mx-auto h-12 w-12 text-gray-300 mb-4" />
            <p className="text-base font-medium">No receipts found</p>
            <p className="text-sm mt-2">
              <Link
                href="/admin/comprehensive-receipts"
                className="text-blue-600 hover:text-blue-500"
              >
                Create your first receipt
              </Link>
            </p>
          </div>
        ) : (
          <>
            {/* Mobile Card View */}
            <div className="block sm:hidden divide-y divide-gray-200">
              {receipts.map((receipt) => (
                <div key={receipt.id} className="px-4 py-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {receipt.receiptNumber}
                      </p>
                      <p className="text-xs text-gray-500">
                        {receipt.items.length} item{receipt.items.length !== 1 ? 's' : ''}
                      </p>
                    </div>
                    <div className="ml-3">
                      {getStatusBadge(receipt.status)}
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-3 mb-3">
                    <div>
                      <p className="text-xs text-gray-500">Customer</p>
                      <p className="text-sm font-medium text-gray-900 truncate">{receipt.customerName}</p>
                      <p className="text-xs text-gray-500">{receipt.phoneNumber}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-xs text-gray-500">Amount</p>
                      <p className="text-sm font-medium text-gray-900">KES {formatAmount(receipt.totalAmount)}</p>
                      <p className="text-xs text-gray-500">Paid: KES {formatAmount(receipt.amountPaid)}</p>
                    </div>
                  </div>
                  
                  <div className="mb-3">
                    <p className="text-xs text-gray-500">Date</p>
                    <p className="text-sm text-gray-900">{formatSafeDate(receipt.issuedAt)}</p>
                  </div>
                  
                  <div className="flex flex-wrap gap-2">
                    <Link
                      href={`/admin/comprehensive-receipts/${receipt.id}`}
                      className="inline-flex items-center px-2.5 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
                    >
                      <EyeIcon className="h-3 w-3 mr-1" />
                      View
                    </Link>
                    <button
                      onClick={() => handlePrintReceipt(receipt.id)}
                      className="inline-flex items-center px-2.5 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50"
                    >
                      <PrinterIcon className="h-3 w-3 mr-1" />
                      Print
                    </button>
                    <button
                      onClick={() => handleDeleteReceipt(receipt.id, receipt.receiptNumber)}
                      disabled={deleting === receipt.id}
                      className="inline-flex items-center px-2.5 py-1.5 border border-red-300 shadow-sm text-xs font-medium rounded text-red-700 bg-white hover:bg-red-50 disabled:opacity-50"
                    >
                      <TrashIcon className="h-3 w-3 mr-1" />
                      {deleting === receipt.id ? 'Deleting...' : 'Delete'}
                    </button>
                  </div>
                </div>
              ))}
            </div>

            {/* Desktop Table View */}
            <div className="hidden sm:block overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Receipt
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Customer
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Amount
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Date
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {receipts.map((receipt) => (
                    <tr key={receipt.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {receipt.receiptNumber}
                        </div>
                        <div className="text-sm text-gray-500">
                          {receipt.items.length} item{receipt.items.length !== 1 ? 's' : ''}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {receipt.customerName}
                        </div>
                        <div className="text-sm text-gray-500">
                          {receipt.phoneNumber}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          KES {formatAmount(receipt.totalAmount)}
                        </div>
                        <div className="text-sm text-gray-500">
                          Paid: KES {formatAmount(receipt.amountPaid)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getStatusBadge(receipt.status)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {formatSafeDate(receipt.issuedAt)}
                        </div>
                        <div className="text-sm text-gray-500">
                          Created: {formatSafeDate(receipt.createdAt)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex flex-wrap justify-end gap-2">
                          <Link
                            href={`/admin/comprehensive-receipts/${receipt.id}`}
                            className="inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                          >
                            <EyeIcon className="h-3 w-3 mr-1" />
                            View
                          </Link>
                          <button
                            onClick={() => handlePrintReceipt(receipt.id)}
                            className="inline-flex items-center px-3 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                          >
                            <PrinterIcon className="h-3 w-3 mr-1" />
                            Print
                          </button>
                          <button
                            onClick={() => handleDeleteReceipt(receipt.id, receipt.receiptNumber)}
                            disabled={deleting === receipt.id}
                            className="inline-flex items-center px-3 py-1.5 border border-red-300 shadow-sm text-xs font-medium rounded text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50"
                          >
                            <TrashIcon className="h-3 w-3 mr-1" />
                            {deleting === receipt.id ? 'Deleting...' : 'Delete'}
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </>
        )}
      </div>
    </div>
  );
} 