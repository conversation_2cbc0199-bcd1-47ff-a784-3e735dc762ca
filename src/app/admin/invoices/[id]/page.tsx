'use client';
import React from 'react';

import { useState, useEffect, useCallback } from 'react';
import { useParams } from 'next/navigation';
import Link from 'next/link';
import {
  ArrowLeftIcon,
  DocumentTextIcon,
  XCircleIcon,
  CheckCircleIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  PrinterIcon
} from '@heroicons/react/24/outline';
import { useNotification } from '@/contexts/NotificationContext';
import { format } from 'date-fns';

interface InvoiceItem {
  id: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  description: string | null;
  serviceId: string;
  serviceName?: string;
}

interface Invoice {
  id: string;
  invoiceNumber: string;
  totalAmount: number;
  amountPaid: number;
  balance: number;
  customerName: string;
  phoneNumber: string;
  email: string | null;
  status: string;
  notes: string | null;
  createdAt: string;
  issuedAt: string;
  dueDate: string;
  paidAt: string | null;
  items: InvoiceItem[];
  pdfUrl?: string;
  quoteId?: string | null;
}

export default function InvoiceDetailPage() {
  const params = useParams();
  const { showNotification } = useNotification();
  const [invoiceId, setInvoiceId] = useState<string | null>(null);

  // Handle NextJS 15 compatibility - params might be a Promise
  useEffect(() => {
    const resolveParams = async () => {
      try {
        const resolvedParams = await Promise.resolve(params);
        if (resolvedParams && typeof resolvedParams === 'object' && 'id' in resolvedParams) {
          const id = resolvedParams.id as string;
          console.log('Invoice details page - resolved params:', resolvedParams);
          console.log('Invoice details page - invoiceId:', id);
          console.log('Invoice details page - invoiceId type:', typeof id);
          setInvoiceId(id);
        } else {
          throw new Error('Invalid params structure');
        }
      } catch (error) {
        console.error('Error resolving params:', error);
        // Fallback for non-promise params
        const id = (params as any).id as string;
        console.log('Invoice details page - fallback params:', params);
        console.log('Invoice details page - fallback invoiceId:', id);
        setInvoiceId(id);
      }
    };

    resolveParams();
  }, [params]);

  const [invoice, setInvoice] = useState<Invoice | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [amountPaid, setAmountPaid] = useState<number>(0);
  const [showPaymentForm, setShowPaymentForm] = useState(false);
  const [submitting, setSubmitting] = useState(false);

  const fetchInvoice = useCallback(async () => {
    console.log('fetchInvoice called with invoiceId:', invoiceId);

    if (!invoiceId || invoiceId === 'undefined') {
      console.error('Invalid invoice ID:', invoiceId);
      setError('Invalid invoice ID');
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      console.log('Fetching invoice from:', `/api/admin/invoices/${invoiceId}`);
      const response = await fetch(`/api/admin/invoices/${invoiceId}`);

      console.log('Response status:', response.status);
      console.log('Response ok:', response.ok);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Response error:', errorText);
        throw new Error('Failed to fetch invoice');
      }

      const result = await response.json();
      console.log('Invoice API response received:', result);
      
      if (result.success && result.data) {
        console.log('Invoice data extracted:', result.data);
        setInvoice(result.data);
        setAmountPaid(result.data.amountPaid || 0);
      } else {
        throw new Error(result.error || 'Failed to fetch invoice data');
      }
    } catch (err) {
      console.error('Error fetching invoice:', err);
      setError('Failed to load invoice. Please try again.');
      showNotification('error', 'Failed to load invoice');
    } finally {
      setLoading(false);
    }
  }, [invoiceId, showNotification]);

  useEffect(() => {
    if (invoiceId) {
      fetchInvoice();
    }
  }, [fetchInvoice, invoiceId]);

  const handlePaymentSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);

    try {
      console.log('Submitting payment update with CSRF protection...');
      const response = await fetch('/api/admin/invoices', {
        method: 'PATCH',
        body: JSON.stringify({
          id: invoiceId,
          amountPaid,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Payment update failed:', errorData);
        throw new Error(errorData.error || 'Failed to update payment');
      }

      const updatedInvoice = await response.json();
      console.log('Payment updated successfully:', updatedInvoice);
      setInvoice(updatedInvoice);
      setShowPaymentForm(false);
      showNotification('success', 'Payment updated successfully');
    } catch (err) {
      console.error('Error updating payment:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to update payment';
      showNotification('error', errorMessage);
    } finally {
      setSubmitting(false);
    }
  };

  const formatSafeDate = (dateString: string | null | undefined, formatStr: string = 'dd/MM/yyyy') => {
    if (!dateString) {
      return 'N/A';
    }

    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) {
        console.warn('Invalid date string:', dateString);
        return 'Invalid Date';
      }
      return format(date, formatStr);
    } catch (error) {
      console.error('Error formatting date:', dateString, error);
      return 'Invalid Date';
    }
  };

  const formatSafeAmount = (amount: any): string => {
    try {
      // Handle null, undefined, or non-numeric values
      if (amount === null || amount === undefined || amount === '') {
        return '0';
      }

      // Convert to number if it's a string
      const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;

      // Check if it's a valid number
      if (isNaN(numAmount) || !isFinite(numAmount)) {
        console.warn('Invalid amount value:', amount);
        return '0';
      }

      // Format the number
      return numAmount.toLocaleString('en-KE', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
      });
    } catch (error) {
      console.error('Error formatting amount:', amount, error);
      return '0';
    }
  };

  const getStatusBadge = (status: string | undefined | null) => {
    if (!status) {
      return (
        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
          Unknown
        </span>
      );
    }

    switch (status.toLowerCase()) {
      case 'paid':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            <CheckCircleIcon className="mr-1 h-3 w-3" />
            Paid
          </span>
        );
      case 'pending':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
            <ClockIcon className="mr-1 h-3 w-3" />
            Pending
          </span>
        );
      case 'cancelled':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
            <XCircleIcon className="mr-1 h-3 w-3" />
            Cancelled
          </span>
        );
      case 'overdue':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
            <ExclamationTriangleIcon className="mr-1 h-3 w-3" />
            Overdue
          </span>
        );
      case 'partial':
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            <DocumentTextIcon className="mr-1 h-3 w-3" />
            Partial
          </span>
        );
      default:
        return (
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            {status}
          </span>
        );
    }
  };

  return (
    <div className="space-y-4 sm:space-y-6">
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <XCircleIcon className="h-5 w-5 text-red-400" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error Loading Invoice</h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{error}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {loading ? (
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 sm:px-6 py-12 text-center text-gray-500">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-base font-medium">Loading invoice...</p>
          </div>
        </div>
      ) : !invoice ? (
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 sm:px-6 py-12 text-center text-gray-500">
            <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-300 mb-4" />
            <p className="text-base font-medium">Invoice not found</p>
            <p className="text-sm mt-2">
              <Link
                href="/admin/invoices"
                className="text-blue-600 hover:text-blue-500"
              >
                Back to invoices
              </Link>
            </p>
          </div>
        </div>
      ) : (
        <>
          {/* Header */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
                <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-4 space-y-2 sm:space-y-0">
                  <Link
                    href="/admin/invoices"
                    className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 self-start"
                  >
                    <ArrowLeftIcon className="h-4 w-4 mr-1" />
                    Back to Invoices
                  </Link>
                  <div>
                    <h1 className="text-xl sm:text-2xl font-bold text-gray-900">Invoice {invoice.invoiceNumber}</h1>
                    <p className="text-sm sm:text-base text-gray-600">Created on {formatSafeDate(invoice.createdAt)}</p>
                  </div>
                </div>
                <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3">
                  <a
                    href={`/api/invoices/${invoice.id}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center justify-center px-3 sm:px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                  >
                    <PrinterIcon className="h-4 w-4 mr-2" />
                    Print
                  </a>
                  {invoice.status !== 'paid' && (
                    <button
                      onClick={() => setShowPaymentForm(!showPaymentForm)}
                      className="inline-flex items-center justify-center px-3 sm:px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700"
                    >
                      <CheckCircleIcon className="h-4 w-4 mr-2" />
                      Record Payment
                    </button>
                  )}
                </div>
              </div>
            </div>

            {/* Status Display */}
            <div className="px-4 sm:px-6 py-4 bg-gray-50 border-b border-gray-200">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
                <div className="flex items-center space-x-4">
                  <span className="text-sm text-gray-500">Status:</span>
                  {getStatusBadge(invoice.status)}
                </div>
                <div className="text-sm text-gray-600">
                  {invoice.balance > 0 && (
                    <span className="text-red-600 font-medium">
                      Outstanding: KES {formatSafeAmount(invoice.balance)}
                    </span>
                  )}
                  {invoice.balance === 0 && (
                    <span className="text-green-600 font-medium">Fully Paid</span>
                  )}
                </div>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 xl:grid-cols-3 gap-4 sm:gap-6">
            {/* Invoice Details */}
            <div className="xl:col-span-2 space-y-4 sm:space-y-6">
              {/* Customer Information */}
              <div className="bg-white shadow rounded-lg">
                <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
                  <h2 className="text-base sm:text-lg font-medium text-gray-900">Customer Information</h2>
                </div>
                <div className="px-4 sm:px-6 py-4">
                  <dl className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Name</dt>
                      <dd className="mt-1 text-sm text-gray-900 break-words">{invoice.customerName}</dd>
                    </div>
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Phone</dt>
                      <dd className="mt-1 text-sm text-gray-900">{invoice.phoneNumber}</dd>
                    </div>
                    {invoice.email && (
                      <div className="sm:col-span-2">
                        <dt className="text-sm font-medium text-gray-500">Email</dt>
                        <dd className="mt-1 text-sm text-gray-900 break-all">{invoice.email}</dd>
                      </div>
                    )}
                  </dl>
                </div>
              </div>

              {/* Invoice Items */}
              <div className="bg-white shadow rounded-lg">
                <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
                  <h2 className="text-base sm:text-lg font-medium text-gray-900">Invoice Items</h2>
                </div>
                {/* Mobile-friendly items display */}
                <div className="block sm:hidden">
                  <div className="divide-y divide-gray-200">
                    {invoice.items && Array.isArray(invoice.items) ? invoice.items.map((item) => (
                      <div key={item.id} className="px-4 py-4">
                        <div className="flex justify-between items-start mb-2">
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-gray-900 truncate">
                              {item.description || item.serviceName || 'No description'}
                            </p>
                          </div>
                          <div className="ml-2 text-right">
                            <p className="text-sm font-medium text-gray-900">
                              KES {formatSafeAmount(item.totalPrice)}
                            </p>
                          </div>
                        </div>
                        <div className="flex justify-between text-xs text-gray-500">
                          <span>Qty: {item.quantity || 0} × KES {formatSafeAmount(item.unitPrice)}</span>
                        </div>
                      </div>
                    )) : (
                      <div className="px-4 py-8 text-center text-sm text-gray-500">
                        {invoice.items === undefined ? 'Loading items...' : 'No items found'}
                      </div>
                    )}
                  </div>
                </div>
                {/* Desktop table display */}
                <div className="hidden sm:block overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Description
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Quantity
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Unit Price
                        </th>
                        <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Total
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {invoice.items && Array.isArray(invoice.items) ? invoice.items.map((item) => (
                        <tr key={item.id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {item.description || item.serviceName || 'No description'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {item.quantity || 0}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            KES {formatSafeAmount(item.unitPrice)}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium text-gray-900">
                            KES {formatSafeAmount(item.totalPrice)}
                          </td>
                        </tr>
                      )) : (
                        <tr>
                          <td colSpan={4} className="px-6 py-4 text-center text-sm text-gray-500">
                            {invoice.items === undefined ? 'Loading items...' : 'No items found'}
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>

                {/* Totals Section */}
                <div className="bg-gray-50 px-4 sm:px-6 py-4">
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Total Amount</span>
                      <span className="text-sm font-medium text-gray-900">KES {formatSafeAmount(invoice.totalAmount)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600">Amount Paid</span>
                      <span className="text-sm font-medium text-green-600">KES {formatSafeAmount(invoice.amountPaid)}</span>
                    </div>
                    <div className="border-t border-gray-200 pt-2">
                      <div className="flex justify-between">
                        <span className="text-base font-medium text-gray-900">Balance Due</span>
                        <span className={`text-base font-medium ${
                          invoice.balance > 0 ? 'text-red-600' : 'text-green-600'
                        }`}>
                          KES {formatSafeAmount(invoice.balance)}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Notes */}
              {invoice.notes && (
                <div className="bg-white shadow rounded-lg">
                  <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
                    <h2 className="text-base sm:text-lg font-medium text-gray-900">Notes</h2>
                  </div>
                  <div className="px-4 sm:px-6 py-4">
                    <p className="text-sm text-gray-700 break-words">{invoice.notes}</p>
                  </div>
                </div>
              )}
            </div>

            {/* Sidebar */}
            <div className="space-y-4 sm:space-y-6">
              {/* Invoice Information */}
              <div className="bg-white shadow rounded-lg">
                <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
                  <h2 className="text-base sm:text-lg font-medium text-gray-900">Invoice Information</h2>
                </div>
                <div className="px-4 sm:px-6 py-4">
                  <dl className="space-y-3">
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Invoice Number</dt>
                      <dd className="mt-1 text-sm text-gray-900 break-all">{invoice.invoiceNumber}</dd>
                    </div>
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Issue Date</dt>
                      <dd className="mt-1 text-sm text-gray-900">{formatSafeDate(invoice.issuedAt)}</dd>
                    </div>
                    <div>
                      <dt className="text-sm font-medium text-gray-500">Due Date</dt>
                      <dd className="mt-1 text-sm text-gray-900">{formatSafeDate(invoice.dueDate)}</dd>
                    </div>
                    {invoice.paidAt && (
                      <div>
                        <dt className="text-sm font-medium text-gray-500">Paid Date</dt>
                        <dd className="mt-1 text-sm text-gray-900">{formatSafeDate(invoice.paidAt)}</dd>
                      </div>
                    )}
                    {invoice.quoteId && (
                      <div>
                        <dt className="text-sm font-medium text-gray-500">From Quote</dt>
                        <dd className="mt-1 text-sm text-blue-600">
                          <Link href={`/admin/quotes/${invoice.quoteId}`}>View Quote</Link>
                        </dd>
                      </div>
                    )}
                  </dl>
                </div>
              </div>

              {/* Payment Summary */}
              <div className="bg-white shadow rounded-lg">
                <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
                  <h2 className="text-base sm:text-lg font-medium text-gray-900">Payment Summary</h2>
                </div>
                <div className="px-4 sm:px-6 py-4">
                  <dl className="space-y-3">
                    <div className="flex justify-between">
                      <dt className="text-sm text-gray-500">Total Amount</dt>
                      <dd className="text-sm font-medium text-gray-900">KES {formatSafeAmount(invoice.totalAmount)}</dd>
                    </div>
                    <div className="flex justify-between">
                      <dt className="text-sm text-gray-500">Amount Paid</dt>
                      <dd className="text-sm font-medium text-green-600">KES {formatSafeAmount(invoice.amountPaid)}</dd>
                    </div>
                    <div className="border-t border-gray-200 pt-3">
                      <div className="flex justify-between">
                        <dt className="text-base font-medium text-gray-900">Balance Due</dt>
                        <dd className={`text-base font-medium ${
                          invoice.balance > 0 ? 'text-red-600' : 'text-green-600'
                        }`}>
                          KES {formatSafeAmount(invoice.balance)}
                        </dd>
                      </div>
                    </div>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          {/* Payment Form Modal */}
          {showPaymentForm && (
            <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" onClick={() => setShowPaymentForm(false)}>
              <div className="relative top-20 mx-auto p-4 sm:p-5 border border-gray-300 w-full max-w-md sm:max-w-lg bg-white rounded-lg shadow-lg" onClick={e => e.stopPropagation()}>
                <div className="flex items-center justify-between pb-3 border-b border-gray-200">
                  <h3 className="text-lg font-semibold text-gray-900">Record Payment</h3>
                  <button
                    onClick={() => setShowPaymentForm(false)}
                    className="text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    <XCircleIcon className="h-6 w-6" />
                  </button>
                </div>

                <form onSubmit={handlePaymentSubmit} className="mt-4 space-y-4">
                  <div>
                    <label htmlFor="amountPaid" className="block text-sm font-medium text-gray-700 mb-2">
                      Amount Paid (KES)
                    </label>
                    <input
                      type="number"
                      id="amountPaid"
                      min="0"
                      step="0.01"
                      value={amountPaid}
                      onChange={(e) => setAmountPaid(parseFloat(e.target.value) || 0)}
                      className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                      placeholder="0.00"
                      required
                    />
                  </div>

                  <div className="flex flex-col sm:flex-row gap-3 pt-4">
                    <button
                      type="button"
                      onClick={() => setShowPaymentForm(false)}
                      className="flex-1 inline-flex justify-center items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      disabled={submitting}
                      className="flex-1 inline-flex justify-center items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
                    >
                      {submitting ? 'Recording...' : 'Record Payment'}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
}
