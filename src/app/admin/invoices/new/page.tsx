'use client';
import React from 'react';

import { useState, useEffect, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '@/hooks/useAuth';
import Link from 'next/link';
import {
  ArrowLeftIcon,
  DocumentTextIcon,
  PlusIcon,
  MinusIcon,
  XCircleIcon
} from '@heroicons/react/24/outline';
import { useNotification } from '@/contexts/NotificationContext';
import { addDays, format } from 'date-fns';

interface Service {
  id: string;
  name: string;
  description: string | null;
  price: number;
  category: string;
}

interface InvoiceItem {
  serviceId: string;
  quantity: number;
  unitPrice: number;
  description?: string;
}

export default function NewInvoicePage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const quoteId = searchParams?.get('quoteId');
  const { showNotification } = useNotification();
  const { user, isLoading } = useAuth();

  const [customerName, setCustomerName] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [email, setEmail] = useState('');
  const [notes, setNotes] = useState('');
  const [dueDate, setDueDate] = useState(format(addDays(new Date(), 14), 'yyyy-MM-dd'));
  const [items, setItems] = useState<InvoiceItem[]>([{ serviceId: '', quantity: 1, unitPrice: 0 }]);
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [quoteData, setQuoteData] = useState<any | null>(null);
  const [csrfToken, setCsrfToken] = useState<string | null>(null);



  const fetchServices = useCallback(async () => {
    try {
      console.log('Fetching services...');
      const response = await fetch('/api/admin/services', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include', // Ensure cookies are sent
      });

      console.log('Response status:', response.status);
      console.log('Response headers:', Object.fromEntries(response.headers.entries()));

      if (!response.ok) {
        const errorText = await response.text();
        console.error('API Error Response:', errorText);
        throw new Error(`Failed to fetch services: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      console.log('Services API response:', result);
      console.log('Result type:', typeof result);
      console.log('Result.success:', result.success);
      console.log('Result.data:', result.data);

      // Handle new API response format
      const services = result.success ? result.data : result;
      console.log('Extracted services:', services);
      console.log('Services type:', typeof services);
      console.log('Services is array:', Array.isArray(services));
      console.log('Services loaded:', services?.length || 0);
      setServices(services || []);
    } catch (err) {
      console.error('Error fetching services:', err);
      showNotification('error', 'Failed to load services');
      // Set empty array to prevent rendering issues
      setServices([]);
    } finally {
      setLoading(false);
    }
  }, [showNotification]);

  const fetchQuoteData = useCallback(async () => {
    if (!quoteId) return;

    try {
      const response = await fetch(`/api/admin/quotes/${quoteId}`);

      if (!response.ok) {
        throw new Error('Failed to fetch quote');
      }

      const data = await response.json();
      setQuoteData(data);

      // Pre-fill form with quote data
      setCustomerName(data.customerName);
      setPhoneNumber(data.phoneNumber);
      setEmail(data.email || '');
      setNotes(data.notes || '');

      // Convert quote items to invoice items
      const invoiceItems = data.items && Array.isArray(data.items) ? data.items.map((item: any) => ({
        serviceId: item.serviceId,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        description: item.description || ''
      })) : [];

      setItems(invoiceItems);
    } catch (err) {
      console.error('Error fetching quote:', err);
      showNotification('error', 'Failed to load quote data');
    }
  }, [quoteId, showNotification]);

  // Check if invoice already exists for this quote
  const checkExistingInvoice = useCallback(async () => {
    if (!quoteId) return false;

    try {
      console.log('Checking if invoice already exists for quote:', quoteId);
      const response = await fetch(`/api/admin/invoices/by-quote/${quoteId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success && result.data) {
          console.log('Invoice already exists for this quote:', result.data.invoiceNumber);
          setError(`Invoice ${result.data.invoiceNumber} already exists for this quote. Redirecting to existing invoice...`);
          showNotification('info', `Invoice ${result.data.invoiceNumber} already exists for this quote. Redirecting...`);
          
          // Use standardized response extraction pattern
          const invoice = result.success ? result.data : result;
          if (invoice?.id) {
            setTimeout(() => {
              router.push(`/admin/invoices/${invoice.id}`);
            }, 2000);
          } else {
            console.error('Missing invoice ID in response:', result);
            setError('Invoice found but unable to redirect to details page');
          }
          return true;
        }
      } else if (response.status !== 404) {
        console.error('Error checking existing invoice:', response.status);
      }
    } catch (error) {
      console.error('Error checking existing invoice:', error);
    }
    return false;
  }, [quoteId, router, showNotification]);

  useEffect(() => {
    // Only fetch data when user is authenticated
    if (user) {
      fetchServices();

      // If we have a quoteId, check if invoice already exists first
      if (quoteId) {
        checkExistingInvoice().then((invoiceExists) => {
          if (!invoiceExists) {
            fetchQuoteData();
          }
        });
      } else {
        // No quote ID, just fetch quote data (which will do nothing)
        fetchQuoteData();
      }
    } else if (!isLoading) {
      console.log('User not authenticated, redirecting to login');
      router.push('/admin/login');
    }
  }, [fetchServices, fetchQuoteData, checkExistingInvoice, user, isLoading, router, quoteId]);

  const handleServiceChange = (index: number, serviceId: string) => {
    const newItems = [...items];
    const service = Array.isArray(services) ? services.find(s => s.id === serviceId) : null;

    if (service) {
      newItems[index] = {
        ...newItems[index],
        serviceId,
        unitPrice: service.price,
        description: service.description || undefined
      };
    } else {
      newItems[index] = {
        ...newItems[index],
        serviceId,
        unitPrice: 0
      };
    }

    setItems(newItems);
  };

  const handleQuantityChange = (index: number, quantity: number) => {
    const newItems = [...items];
    newItems[index] = { ...newItems[index], quantity };
    setItems(newItems);
  };

  const handleUnitPriceChange = (index: number, unitPrice: number) => {
    const newItems = [...items];
    newItems[index] = { ...newItems[index], unitPrice };
    setItems(newItems);
  };

  const handleDescriptionChange = (index: number, description: string) => {
    const newItems = [...items];
    newItems[index] = { ...newItems[index], description };
    setItems(newItems);
  };

  const addItem = () => {
    setItems([...items, { serviceId: '', quantity: 1, unitPrice: 0 }]);
  };

  const removeItem = (index: number) => {
    if (items.length === 1) {
      return;
    }

    const newItems = [...items];
    newItems.splice(index, 1);
    setItems(newItems);
  };

  const calculateTotal = () => {
    return items.reduce((total, item) => total + (item.quantity * item.unitPrice), 0);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);
    setError(null);

    // Validate form
    if (!customerName.trim()) {
      setError('Customer name is required');
      setSubmitting(false);
      return;
    }

    if (!phoneNumber.trim()) {
      setError('Phone number is required');
      setSubmitting(false);
      return;
    }

    if (!dueDate) {
      setError('Due date is required');
      setSubmitting(false);
      return;
    }

    if (items.length === 0) {
      setError('At least one item is required');
      setSubmitting(false);
      return;
    }

    // Validate each item
    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      if (!item.serviceId) {
        setError(`Service is required for item ${i + 1}`);
        setSubmitting(false);
        return;
      }
      if (!item.quantity || item.quantity <= 0) {
        setError(`Valid quantity is required for item ${i + 1}`);
        setSubmitting(false);
        return;
      }
      if (item.unitPrice === undefined || item.unitPrice < 0) {
        setError(`Valid unit price is required for item ${i + 1}`);
        setSubmitting(false);
        return;
      }
    }

    const total = calculateTotal();
    if (total <= 0) {
      setError('Invoice total must be greater than 0');
      setSubmitting(false);
      return;
    }

    try {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
      };

      // Add CSRF token if available
      if (csrfToken) {
        headers['x-csrf-token'] = csrfToken;
        console.log('Adding CSRF token to headers:', csrfToken);
      } else {
        console.warn('No CSRF token available!');
      }

      console.log('Request headers:', headers);

      const requestBody = {
        customerName: customerName.trim(),
        phoneNumber: phoneNumber.trim(),
        email: email?.trim() || undefined,
        notes: notes?.trim() || undefined,
        dueDate,
        items: items.map(item => ({
          serviceId: item.serviceId,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          description: item.description?.trim() || undefined
        })),
        quoteId: quoteId || undefined
      };

      console.log('Request body:', JSON.stringify(requestBody, null, 2));

      const response = await fetch('/api/admin/invoices', {
        method: 'POST',
        headers,
        credentials: 'include',
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        let errorMessage = 'Failed to create invoice';
        try {
          const errorData = await response.json();
          console.error('API Error Response:', errorData);
          errorMessage = errorData.error || errorData.message || `HTTP ${response.status}: ${response.statusText}`;
        } catch (parseError) {
          console.error('Could not parse error response:', parseError);
          errorMessage = `HTTP ${response.status}: ${response.statusText}`;
        }
        throw new Error(errorMessage);
      }

      const result = await response.json();
      console.log('Invoice creation response:', result);
      console.log('Result type:', typeof result);
      console.log('Result keys:', Object.keys(result));

      // Use standardized response extraction pattern
      const invoice = result.success ? result.data : result;
      console.log('Extracted invoice:', invoice);
      console.log('Invoice type:', typeof invoice);
      console.log('Invoice keys:', invoice ? Object.keys(invoice) : 'null');
      console.log('Invoice ID:', invoice?.id);

      if (!invoice || !invoice.id) {
        console.error('Missing invoice ID - Full response:', JSON.stringify(result, null, 2));
        throw new Error('Invalid response: missing invoice ID');
      }

      console.log('About to redirect to:', `/admin/invoices/${invoice.id}`);
      console.log('Invoice ID type:', typeof invoice.id);
      console.log('Invoice ID value:', JSON.stringify(invoice.id));

      showNotification('success', 'Invoice created successfully');

      // Add a small delay to ensure the notification is shown
      setTimeout(() => {
        console.log('Executing redirect now...');
        router.push(`/admin/invoices/${invoice.id}`);
      }, 100);
    } catch (err: unknown) {
      console.error('Error creating invoice:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to create invoice';
      setError(errorMessage);
      showNotification('error', 'Failed to create invoice');
    } finally {
      setSubmitting(false);
    }
  };

  // Debug logging
  console.log('Render - user:', user);
  console.log('Render - isLoading:', isLoading);
  console.log('Render - CSRF token:', csrfToken);
  console.log('Render - services state:', services);
  console.log('Render - services type:', typeof services);
  console.log('Render - services is array:', Array.isArray(services));
  console.log('Render - loading:', loading);

  // Show loading while user is being checked
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  // Show error if not authenticated
  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <p className="text-red-600">Not authenticated. Redirecting to login...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4 sm:space-y-6">
      {/* Header */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-3 sm:space-y-0">
            <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-4 space-y-2 sm:space-y-0">
              <Link
                href="/admin/invoices"
                className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 self-start"
              >
                <ArrowLeftIcon className="h-4 w-4 mr-1" />
                Back to Invoices
              </Link>
              <div>
                <h1 className="text-xl sm:text-2xl font-bold text-gray-900">Create New Invoice</h1>
                <p className="text-sm sm:text-base text-gray-600">Generate a new invoice for your customer</p>
              </div>
            </div>
            <div className="flex items-center text-sm text-gray-500">
              <DocumentTextIcon className="h-5 w-5 mr-2" />
              New
            </div>
          </div>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <XCircleIcon className="h-5 w-5 text-red-400" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error</h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{error}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Quote Notice */}
      {quoteData && (
        <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <DocumentTextIcon className="h-5 w-5 text-blue-400" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-blue-800">Creating from Quote</h3>
              <div className="mt-2 text-sm text-blue-700">
                <p>This invoice is being created from Quote #{quoteData.quoteNumber}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Loading State */}
      {loading ? (
        <div className="bg-white shadow rounded-lg">
          <div className="px-4 sm:px-6 py-12 text-center text-gray-500">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-base font-medium">Loading form...</p>
          </div>
        </div>
      ) : (
        <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-6">
          {/* Customer Information */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
              <h2 className="text-base sm:text-lg font-medium text-gray-900">Customer Information</h2>
            </div>
            <div className="px-4 sm:px-6 py-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
                <div>
                  <label htmlFor="customerName" className="block text-sm font-medium text-gray-700 mb-1">
                    Customer Name <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    id="customerName"
                    value={customerName}
                    onChange={(e) => setCustomerName(e.target.value)}
                    className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    required
                  />
                </div>
                <div>
                  <label htmlFor="phoneNumber" className="block text-sm font-medium text-gray-700 mb-1">
                    Phone Number <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    id="phoneNumber"
                    value={phoneNumber}
                    onChange={(e) => setPhoneNumber(e.target.value)}
                    className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    required
                  />
                </div>
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                    Email Address
                  </label>
                  <input
                    type="email"
                    id="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>
                <div>
                  <label htmlFor="dueDate" className="block text-sm font-medium text-gray-700 mb-1">
                    Due Date <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="date"
                    id="dueDate"
                    value={dueDate}
                    onChange={(e) => setDueDate(e.target.value)}
                    className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    required
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Invoice Items */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
                <h2 className="text-base sm:text-lg font-medium text-gray-900">Invoice Items</h2>
                <button
                  type="button"
                  onClick={addItem}
                  className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  <PlusIcon className="h-4 w-4 mr-2" />
                  Add Item
                </button>
              </div>
            </div>

            <div className="px-4 sm:px-6 py-4 space-y-4">
              {items.map((item, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-sm font-medium text-gray-900">Item #{index + 1}</h3>
                    {items.length > 1 && (
                      <button
                        type="button"
                        onClick={() => removeItem(index)}
                        className="text-red-600 hover:text-red-800"
                      >
                        <MinusIcon className="h-4 w-4" />
                      </button>
                    )}
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div className="sm:col-span-2">
                      <label htmlFor={`service-${index}`} className="block text-sm font-medium text-gray-700 mb-1">
                        Service <span className="text-red-500">*</span>
                      </label>
                      <select
                        id={`service-${index}`}
                        value={item.serviceId}
                        onChange={(e) => handleServiceChange(index, e.target.value)}
                        className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        required
                      >
                        <option value="">Select a service</option>
                        {Array.isArray(services) && services.map((service) => (
                          <option key={service.id} value={service.id}>
                            {service.name} - KES {service.price.toLocaleString()}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <label htmlFor={`quantity-${index}`} className="block text-sm font-medium text-gray-700 mb-1">
                        Quantity <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="number"
                        id={`quantity-${index}`}
                        value={item.quantity}
                        onChange={(e) => handleQuantityChange(index, parseInt(e.target.value))}
                        className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        min="1"
                        required
                      />
                    </div>

                    <div>
                      <label htmlFor={`unitPrice-${index}`} className="block text-sm font-medium text-gray-700 mb-1">
                        Unit Price <span className="text-red-500">*</span>
                      </label>
                      <input
                        type="number"
                        id={`unitPrice-${index}`}
                        value={item.unitPrice}
                        onChange={(e) => handleUnitPriceChange(index, parseFloat(e.target.value))}
                        className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        min="0"
                        step="0.01"
                        required
                      />
                    </div>
                  </div>

                  {item.description && (
                    <div className="mt-4">
                      <label htmlFor={`description-${index}`} className="block text-sm font-medium text-gray-700 mb-1">
                        Description
                      </label>
                      <textarea
                        id={`description-${index}`}
                        value={item.description}
                        onChange={(e) => handleDescriptionChange(index, e.target.value)}
                        className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                        rows={2}
                      />
                    </div>
                  )}

                  <div className="mt-4 flex justify-between items-center">
                    <span className="text-sm text-gray-500">
                      {item.quantity} × KES {item.unitPrice.toLocaleString()}
                    </span>
                    <span className="text-sm font-medium text-gray-900">
                      Total: KES {(item.quantity * item.unitPrice).toLocaleString()}
                    </span>
                  </div>
                </div>
              ))}
            </div>

            {/* Total Summary */}
            <div className="bg-gray-50 px-4 sm:px-6 py-4 border-t border-gray-200">
              <div className="flex justify-between items-center">
                <span className="text-base font-medium text-gray-900">Total Amount</span>
                <span className="text-lg font-bold text-gray-900">
                  KES {calculateTotal().toLocaleString()}
                </span>
              </div>
            </div>
          </div>

          {/* Notes */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-4 sm:px-6 py-4 border-b border-gray-200">
              <h2 className="text-base sm:text-lg font-medium text-gray-900">Additional Notes</h2>
            </div>
            <div className="px-4 sm:px-6 py-4">
              <textarea
                id="notes"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                placeholder="Enter any additional notes or terms..."
                className="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                rows={3}
              />
            </div>
          </div>

          {/* Action Buttons */}
          <div className="bg-white shadow rounded-lg px-4 sm:px-6 py-4">
            <div className="flex flex-col sm:flex-row gap-3 sm:justify-end">
              <Link
                href="/admin/invoices"
                className="inline-flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Cancel
              </Link>
              <button
                type="submit"
                disabled={submitting || loading}
                className="inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
              >
                {submitting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Creating...
                  </>
                ) : (
                  'Create Invoice'
                )}
              </button>
            </div>
          </div>
        </form>
      )}
    </div>
  );
}
