import AdminLayoutClient from '@/components/admin/AdminLayoutClient';
import ErrorBoundary from '@/components/ErrorBoundary';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Admin Dashboard | Mocky Digital',
  description: 'Admin dashboard for Mocky Digital website',
  robots: {
    index: false,
    follow: false,
  },
};

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <ErrorBoundary showDetails={process.env.NODE_ENV === 'development'}>
      <AdminLayoutClient>{children}</AdminLayoutClient>
    </ErrorBoundary>
  );
}
