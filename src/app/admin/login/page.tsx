'use client';

import { useState, Suspense } from 'react'
import { signIn } from 'next-auth/react'
import { useRouter, useSearchParams } from 'next/navigation'
import { toast } from 'react-hot-toast'
import Image from 'next/image'
import Link from 'next/link'
import { EyeIcon, EyeSlashIcon, LockClosedIcon, UserIcon, HomeIcon } from '@heroicons/react/24/outline'
import { sanitizeText } from '@/utils/inputSanitization'

function LoginForm() {
  const [credentials, setCredentials] = useState({
    username: '',
    password: '',
  })
  const [isLoading, setIsLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const router = useRouter()
  const searchParams = useSearchParams()
  const callbackUrl = searchParams?.get('callbackUrl') || '/admin/dashboard'

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!credentials.username || !credentials.password) {
      toast.error('Please fill in all fields')
      return
    }

    setIsLoading(true)

    try {
      // Sanitize inputs before sending to authentication
      const sanitizedUsername = sanitizeText(credentials.username);
      const sanitizedPassword = credentials.password; // Don't sanitize password content, just validate length
      
      if (!sanitizedUsername || sanitizedUsername.length < 3 || sanitizedUsername.length > 50) {
        toast.error('Username must be between 3 and 50 characters')
        setIsLoading(false)
        return
      }
      
      if (credentials.password.length < 1 || credentials.password.length > 128) {
        toast.error('Password is invalid')
        setIsLoading(false)
        return
      }
      
      console.log('Attempting login with:', { 
        username: sanitizedUsername,
        callbackUrl
      })
      
      const result = await signIn('credentials', {
        username: sanitizedUsername,
        password: sanitizedPassword,
        redirect: false,
        callbackUrl
      })

      console.log('Login result:', {
        ok: result?.ok,
        error: result?.error,
        url: result?.url,
        status: result?.status
      })

      if (!result) {
        console.error('No result from signIn')
        toast.error('An unexpected error occurred')
        return
      }

      if (result.error) {
        console.error('Login error:', result.error)
        
        // Handle specific error cases
        switch (result.error) {
          case 'CredentialsSignin':
            toast.error('Invalid username or password')
            break
          case 'AccessDenied':
            toast.error('Access denied. Please contact an administrator.')
            break
          default:
            toast.error(`Login failed: ${result.error}`)
        }
      } else if (result.url) {
        console.log('Login successful, redirecting to:', result.url)
        toast.success('Login successful!')
        router.push(result.url)
        router.refresh()
      } else {
        console.error('No URL in successful result')
        toast.error('Login succeeded but redirect failed')
      }
    } catch (error) {
      console.error('Login error:', error)
      toast.error('An error occurred during login. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-white relative overflow-hidden">
      {/* Background with animated gradient and patterns */}
      <div className="absolute inset-0">
        {/* Gradient background */}
        <div className="absolute inset-0 bg-gradient-to-br from-orange-50 via-white to-red-50" />
        
        {/* Animated gradient orbs */}
        <div className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-orange-400/20 to-red-400/20 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-0 right-0 w-96 h-96 bg-gradient-to-tl from-red-400/20 to-orange-400/20 rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1s' }} />
        
        {/* Geometric patterns */}
        <div className="absolute inset-0 opacity-5">
          <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
            <defs>
              <pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse">
                <path d="M 10 0 L 0 0 0 10" fill="none" stroke="currentColor" strokeWidth="0.5"/>
              </pattern>
            </defs>
            <rect width="100" height="100" fill="url(#grid)" />
          </svg>
        </div>
      </div>

      {/* Main content */}
      <div className="relative z-10 min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-md w-full">
          {/* Logo and welcome section */}
          <div className="text-center mb-8">
            <div className="flex justify-center mb-6">
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-r from-orange-500 to-red-500 rounded-2xl blur-lg opacity-30 animate-pulse" />
                <div className="relative bg-white/90 backdrop-blur-lg rounded-2xl p-4 shadow-xl border border-white/20">
                  <Image
                    src="/images/logo.png"
                    alt="Mocky Digital Logo"
                    width={80}
                    height={80}
                    className="mx-auto"
                    priority
                  />
                </div>
              </div>
            </div>
            
            <h1 className="text-3xl font-bold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent mb-2">
              Welcome Back
            </h1>
            <p className="text-gray-600 text-lg font-medium">
              Mocky Digital Admin Portal
            </p>
            <p className="text-gray-500 text-sm mt-1">
              Sign in to access your dashboard
            </p>
          </div>

          {/* Login form with glassmorphism */}
          <div className="relative">
            {/* Glass card background */}
            <div className="absolute inset-0 bg-white/70 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/30" />
            <div className="absolute inset-0 bg-gradient-to-br from-white/50 via-white/30 to-transparent rounded-3xl" />
            
            {/* Form content */}
            <div className="relative p-8">
              <form className="space-y-6" onSubmit={handleSubmit}>
                {/* Username field */}
                <div className="space-y-2">
                  <label htmlFor="username" className="block text-sm font-semibold text-gray-700">
                    Username
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <UserIcon className="h-5 w-5 text-gray-400" />
                    </div>
                    <input
                      id="username"
                      name="username"
                      type="text"
                      required
                      className="block w-full pl-10 pr-4 py-3 bg-white/50 backdrop-blur-sm border border-gray-200/50 rounded-xl text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-orange-500/50 focus:border-orange-500/50 transition-all duration-300 shadow-sm"
                      placeholder="Enter your username"
                      value={credentials.username}
                      onChange={(e) => {
                        const sanitized = sanitizeText(e.target.value);
                        setCredentials({ ...credentials, username: sanitized });
                      }}
                    />
                  </div>
                </div>

                {/* Password field */}
                <div className="space-y-2">
                  <label htmlFor="password" className="block text-sm font-semibold text-gray-700">
                    Password
                  </label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <LockClosedIcon className="h-5 w-5 text-gray-400" />
                    </div>
                    <input
                      id="password"
                      name="password"
                      type={showPassword ? 'text' : 'password'}
                      required
                      className="block w-full pl-10 pr-12 py-3 bg-white/50 backdrop-blur-sm border border-gray-200/50 rounded-xl text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-orange-500/50 focus:border-orange-500/50 transition-all duration-300 shadow-sm"
                      placeholder="Enter your password"
                      value={credentials.password}
                      onChange={(e) =>
                        setCredentials({ ...credentials, password: e.target.value })
                      }
                    />
                    <button
                      type="button"
                      className="absolute inset-y-0 right-0 pr-3 flex items-center"
                      onClick={() => setShowPassword(!showPassword)}
                      tabIndex={-1}
                    >
                      {showPassword ? (
                        <EyeSlashIcon className="h-5 w-5 text-gray-400 hover:text-gray-600 transition-colors" />
                      ) : (
                        <EyeIcon className="h-5 w-5 text-gray-400 hover:text-gray-600 transition-colors" />
                      )}
                    </button>
                  </div>
                </div>

                {/* Submit button */}
                <button
                  type="submit"
                  disabled={isLoading}
                  className={`w-full flex justify-center items-center py-3 px-4 border border-transparent rounded-xl shadow-sm text-base font-medium text-white bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 transition-all duration-300 ${
                    isLoading ? 'opacity-75 cursor-not-allowed' : ''
                  }`}
                >
                  {isLoading ? (
                    <>
                      <svg
                        className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                      >
                        <circle
                          className="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          strokeWidth="4"
                        ></circle>
                        <path
                          className="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                      </svg>
                      Signing in...
                    </>
                  ) : (
                    'Sign in'
                  )}
                </button>
              </form>
            </div>
          </div>

          {/* Homepage button */}
          <div className="mt-6 text-center">
            <Link
              href="/"
              className="inline-flex items-center gap-2 px-6 py-3 bg-white/70 backdrop-blur-xl border border-gray-200/50 rounded-xl text-gray-700 hover:text-orange-600 hover:border-orange-500/50 transition-all duration-300 shadow-sm hover:shadow-md group"
            >
              <HomeIcon className="h-5 w-5 group-hover:text-orange-600 transition-colors duration-300" />
              <span className="font-medium">Back to Homepage</span>
            </Link>
          </div>
        </div>
      </div>

      {/* Floating elements for visual interest */}
      <div className="absolute top-20 left-10 w-2 h-2 bg-orange-400 rounded-full opacity-60 animate-bounce" style={{ animationDelay: '0s' }} />
      <div className="absolute top-32 right-20 w-1 h-1 bg-red-400 rounded-full opacity-60 animate-bounce" style={{ animationDelay: '0.5s' }} />
      <div className="absolute bottom-20 left-20 w-3 h-3 bg-orange-300 rounded-full opacity-40 animate-bounce" style={{ animationDelay: '1s' }} />
      <div className="absolute bottom-32 right-10 w-1.5 h-1.5 bg-red-300 rounded-full opacity-40 animate-bounce" style={{ animationDelay: '1.5s' }} />
    </div>
  )
}

// Loading fallback component
function LoginLoading() {
  return (
    <div className="min-h-screen bg-white flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-orange-600 mx-auto mb-4"></div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">Loading</h3>
        <p className="text-sm text-gray-600">
          Please wait...
        </p>
      </div>
    </div>
  )
}

export default function LoginPage() {
  return (
    <Suspense fallback={<LoginLoading />}>
      <LoginForm />
    </Suspense>
  )
}
