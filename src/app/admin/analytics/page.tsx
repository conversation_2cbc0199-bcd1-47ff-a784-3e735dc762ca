'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { useRouter } from 'next/navigation';
import { 
  ChartBarIcon, 
  EyeIcon, 
  UserGroupIcon, 
  ArrowDownTrayIcon,
  ChatBubbleLeftIcon,
  ClockIcon,
  ServerIcon,
  CpuChipIcon,
  SignalIcon,
  ArrowPathIcon
} from '@heroicons/react/24/outline';
import { AnalyticsData, PerformanceMetrics } from '@/services/analyticsService';

interface AnalyticsResponse {
  success: boolean;
  data: AnalyticsData | PerformanceMetrics;
  period: string;
  metric: string;
  generatedAt: string;
}

export default function AnalyticsPage() {
  const { user, isLoading } = useAuth();
  const router = useRouter();
  
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [performanceData, setPerformanceData] = useState<PerformanceMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedPeriod, setSelectedPeriod] = useState('today');
  const [lastUpdated, setLastUpdated] = useState<string | null>(null);
  const [autoRefresh, setAutoRefresh] = useState(true);

  // Redirect if not authenticated
  useEffect(() => {
    if (isLoading) return;
    if (!user) {
      router.push('/admin/login');
      return;
    }
  }, [user, isLoading, router]);

  // Fetch analytics data
  const fetchAnalyticsData = async (period: string = selectedPeriod) => {
    try {
      setError(null);
      
      // Fetch dashboard analytics
      const analyticsResponse = await fetch(`/api/admin/analytics?metric=dashboard&period=${period}`, {
        credentials: 'include',
      });
      
      if (!analyticsResponse.ok) {
        throw new Error('Failed to fetch analytics data');
      }
      
      const analyticsResult: AnalyticsResponse = await analyticsResponse.json();
      setAnalyticsData(analyticsResult.data as AnalyticsData);
      
      // Fetch performance metrics
      const performanceResponse = await fetch(`/api/admin/analytics?metric=performance`, {
        credentials: 'include',
      });
      
      if (performanceResponse.ok) {
        const performanceResult: AnalyticsResponse = await performanceResponse.json();
        setPerformanceData(performanceResult.data as PerformanceMetrics);
      }
      
      setLastUpdated(new Date().toLocaleTimeString());
    } catch (err) {
      console.error('Error fetching analytics:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch analytics data');
    } finally {
      setLoading(false);
    }
  };

  // Initial data fetch
  useEffect(() => {
    if (user) {
      fetchAnalyticsData();
    }
  }, [user, selectedPeriod]);

  // Auto-refresh every 30 seconds
  useEffect(() => {
    if (!autoRefresh) return;
    
    const interval = setInterval(() => {
      if (user) {
        fetchAnalyticsData();
      }
    }, 30000);

    return () => clearInterval(interval);
  }, [user, selectedPeriod, autoRefresh]);

  // Handle period change
  const handlePeriodChange = (period: string) => {
    setSelectedPeriod(period);
    setLoading(true);
  };

  // Handle manual refresh
  const handleRefresh = () => {
    setLoading(true);
    fetchAnalyticsData();
  };

  // Clear analytics cache
  const handleClearCache = async () => {
    try {
      const response = await fetch('/api/admin/analytics', {
        method: 'DELETE',
        credentials: 'include',
      });
      
      if (response.ok) {
        // Refresh data after clearing cache
        handleRefresh();
      }
    } catch (err) {
      console.error('Error clearing cache:', err);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-orange-500"></div>
      </div>
    );
  }

  if (!user) {
    return null; // Will redirect in useEffect
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Analytics Dashboard</h1>
          <p className="text-gray-600 mt-1">Real-time insights powered by Redis</p>
        </div>
        
        <div className="flex items-center space-x-4">
          {/* Auto-refresh toggle */}
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={autoRefresh}
              onChange={(e) => setAutoRefresh(e.target.checked)}
              className="rounded border-gray-300 text-orange-500 focus:ring-orange-500"
            />
            <span className="ml-2 text-sm text-gray-700">Auto-refresh</span>
          </label>
          
          {/* Manual refresh */}
          <button
            onClick={handleRefresh}
            disabled={loading}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:opacity-50"
          >
            <ArrowPathIcon className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </button>
          
          {/* Clear cache */}
          <button
            onClick={handleClearCache}
            className="inline-flex items-center px-4 py-2 border border-red-300 rounded-md shadow-sm text-sm font-medium text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
          >
            Clear Cache
          </button>
        </div>
      </div>

      {/* Period Selection */}
      <div className="flex space-x-1 bg-gray-100 rounded-lg p-1">
        {['today', 'week', 'month'].map((period) => (
          <button
            key={period}
            onClick={() => handlePeriodChange(period)}
            className={`flex-1 px-4 py-2 text-sm font-medium rounded-md transition-colors ${
              selectedPeriod === period
                ? 'bg-white text-orange-600 shadow-sm'
                : 'text-gray-500 hover:text-gray-700'
            }`}
          >
            {period.charAt(0).toUpperCase() + period.slice(1)}
          </button>
        ))}
      </div>

      {/* Last Updated */}
      {lastUpdated && (
        <div className="text-sm text-gray-500">
          Last updated: {lastUpdated}
          {autoRefresh && ' (Auto-refreshing every 30s)'}
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error loading analytics</h3>
              <div className="mt-2 text-sm text-red-700">{error}</div>
            </div>
          </div>
        </div>
      )}

      {/* Loading State */}
      {loading && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(8)].map((_, i) => (
            <div key={i} className="bg-white p-6 rounded-lg shadow animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-8 bg-gray-200 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      )}

      {/* Analytics Data */}
      {!loading && analyticsData && (
        <>
          {/* Main Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <MetricCard
              title="Page Views"
              value={analyticsData.pageViews.toLocaleString()}
              icon={EyeIcon}
              color="blue"
            />
            <MetricCard
              title="Unique Visitors"
              value={analyticsData.uniqueVisitors.toLocaleString()}
              icon={UserGroupIcon}
              color="green"
            />
            <MetricCard
              title="Portfolio Views"
              value={analyticsData.portfolioViews.toLocaleString()}
              icon={ChartBarIcon}
              color="purple"
            />
            <MetricCard
              title="Downloads"
              value={analyticsData.downloadCount.toLocaleString()}
              icon={ArrowDownTrayIcon}
              color="orange"
            />
          </div>

          {/* Secondary Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <MetricCard
              title="Blog Views"
              value={analyticsData.blogViews.toLocaleString()}
              icon={ChatBubbleLeftIcon}
              color="indigo"
            />
            <MetricCard
              title="Contact Submissions"
              value={analyticsData.contactSubmissions.toLocaleString()}
              icon={ChatBubbleLeftIcon}
              color="red"
            />
            <MetricCard
              title="Avg Session Duration"
              value={`${Math.round(analyticsData.averageSessionDuration)}s`}
              icon={ClockIcon}
              color="yellow"
            />
          </div>

          {/* Device Types */}
          {analyticsData.deviceTypes.length > 0 && (
            <div className="bg-white p-6 rounded-lg shadow">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Device Types</h3>
              <div className="space-y-3">
                {analyticsData.deviceTypes.map((device) => (
                  <div key={device.type} className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="w-3 h-3 bg-orange-500 rounded-full mr-3"></div>
                      <span className="text-sm font-medium text-gray-900 capitalize">{device.type}</span>
                    </div>
                    <span className="text-sm text-gray-500">{device.count.toLocaleString()}</span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </>
      )}

      {/* Performance Metrics */}
      {!loading && performanceData && (
        <>
          <h2 className="text-2xl font-bold text-gray-900 mt-12">Performance Metrics</h2>
          
          {/* System Health */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <MetricCard
              title="Memory Usage"
              value={`${performanceData.systemHealth.memoryUsage}%`}
              icon={CpuChipIcon}
              color="red"
            />
            <MetricCard
              title="Uptime"
              value={`${Math.round(performanceData.systemHealth.uptime / 3600)}h`}
              icon={ServerIcon}
              color="green"
            />
            <MetricCard
              title="API Endpoints"
              value={performanceData.apiResponseTimes.length.toString()}
              icon={SignalIcon}
              color="blue"
            />
            <MetricCard
              title="Cache Services"
              value={performanceData.cacheHitRates.length.toString()}
              icon={ChartBarIcon}
              color="purple"
            />
          </div>

          {/* API Response Times */}
          {performanceData.apiResponseTimes.length > 0 && (
            <div className="bg-white p-6 rounded-lg shadow">
              <h3 className="text-lg font-medium text-gray-900 mb-4">API Response Times</h3>
              <div className="space-y-3">
                {performanceData.apiResponseTimes.map((api) => (
                  <div key={api.endpoint} className="flex items-center justify-between">
                    <div>
                      <span className="text-sm font-medium text-gray-900">{api.endpoint}</span>
                      <span className="text-xs text-gray-500 ml-2">({api.requestCount} requests)</span>
                    </div>
                    <span className="text-sm text-gray-500">{api.avgTime.toFixed(2)}ms</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Cache Hit Rates */}
          {performanceData.cacheHitRates.length > 0 && (
            <div className="bg-white p-6 rounded-lg shadow">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Cache Hit Rates</h3>
              <div className="space-y-3">
                {performanceData.cacheHitRates.map((cache) => (
                  <div key={cache.service} className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-900 capitalize">{cache.service}</span>
                    <div className="flex items-center">
                      <div className="w-24 bg-gray-200 rounded-full h-2 mr-3">
                        <div 
                          className="bg-green-500 h-2 rounded-full" 
                          style={{ width: `${Math.min(100, cache.hitRate)}%` }}
                        ></div>
                      </div>
                      <span className="text-sm text-gray-500">{cache.hitRate.toFixed(1)}%</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </>
      )}
    </div>
  );
}

// Metric Card Component
interface MetricCardProps {
  title: string;
  value: string;
  icon: React.ElementType;
  color: 'blue' | 'green' | 'purple' | 'orange' | 'indigo' | 'red' | 'yellow';
}

function MetricCard({ title, value, icon: Icon, color }: MetricCardProps) {
  const colorClasses = {
    blue: 'bg-blue-500',
    green: 'bg-green-500',
    purple: 'bg-purple-500',
    orange: 'bg-orange-500',
    indigo: 'bg-indigo-500',
    red: 'bg-red-500',
    yellow: 'bg-yellow-500',
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow">
      <div className="flex items-center">
        <div className={`p-2 rounded-md ${colorClasses[color]}`}>
          <Icon className="h-6 w-6 text-white" />
        </div>
        <div className="ml-4">
          <p className="text-sm font-medium text-gray-500">{title}</p>
          <p className="text-2xl font-semibold text-gray-900">{value}</p>
        </div>
      </div>
    </div>
  );
} 