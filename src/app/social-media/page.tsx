'use client';

import { useState, useEffect } from 'react';
import ModernProcessSection from '@/components/ModernProcessSection';
import ModernFAQSection from '@/components/ModernFAQSection';
import { SparklesIcon } from '@heroicons/react/24/outline';

// Social media platforms data
const platforms = [
  {
    name: 'Instagram',
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <rect x="2" y="2" width="20" height="20" rx="5" ry="5"/>
        <path d="m16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"/>
        <line x1="17.5" y1="6.5" x2="17.51" y2="6.5"/>
      </svg>
    ),
    services: ['Feed Strategy & Planning', 'Stories & Reels Creation', 'IGTV Content', 'Visual Branding'],
    color: 'bg-gradient-to-br from-purple-600 to-pink-500'
  },
  {
    name: 'Facebook',
    icon: (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
      </svg>
    ),
    services: ['Page Management', 'Content Creation', 'Ad Campaigns', 'Community Engagement'],
    color: 'bg-[#1877F2]'
  },
  {
    name: 'LinkedIn',
    icon: (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
      </svg>
    ),
    services: ['Company Page Setup', 'B2B Marketing', 'Lead Generation', 'Professional Content'],
    color: 'bg-[#0A66C2]'
  },
  {
    name: 'TikTok',
    icon: (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <path d="M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z"/>
      </svg>
    ),
    services: ['Viral Content Strategy', 'Trending Hashtags', 'Short Video Creation', 'Influencer Outreach'],
    color: 'bg-gradient-to-br from-black to-red-500'
  },
  {
    name: 'Twitter/X',
    icon: (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <path d="M18.901 1.153h3.68l-8.04 9.19L24 22.846h-7.406l-5.8-7.584-6.638 7.584H.474l8.6-9.83L0 1.154h7.594l5.243 6.932ZM17.61 20.644h2.039L6.486 3.24H4.298Z"/>
      </svg>
    ),
    services: ['Tweet Strategy', 'Trend Analysis', 'Community Building', 'Real-time Engagement'],
    color: 'bg-black'
  },
  {
    name: 'YouTube',
    icon: (
      <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
        <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
      </svg>
    ),
    services: ['Channel Strategy', 'Video Content Creation', 'SEO Optimization', 'Analytics & Growth'],
    color: 'bg-[#FF0000]'
  }
];

// Social media services data
const socialMediaServices = [
  {
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z" />
      </svg>
    ),
    title: 'Content Creation & Strategy',
    description: 'Engaging, platform-optimized content that resonates with your audience and drives engagement.',
    features: [
      'Custom Graphics & Video Content',
      'Copywriting & Caption Creation',
      'Content Calendar Planning',
      'Brand Voice Development'
    ]
  },
  {
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
      </svg>
    ),
    title: 'Community Management',
    description: 'Build and nurture your online community with professional engagement and relationship building.',
    features: [
      'Daily Community Engagement',
      'Comment & DM Management',
      'Crisis Communication',
      'Follower Growth Strategies'
    ]
  },
  {
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122" />
      </svg>
    ),
    title: 'Social Media Advertising',
    description: 'Strategic paid campaigns across social platforms that maximize ROI and drive conversions.',
    features: [
      'Campaign Strategy & Setup',
      'Creative Ad Design',
      'Audience Targeting & Optimization',
      'Performance Tracking & Reporting'
    ]
  },
  {
    icon: (
      <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
      </svg>
    ),
    title: 'Analytics & Reporting',
    description: 'Comprehensive tracking and analysis of your social media performance with actionable insights.',
    features: [
      'Performance Dashboards',
      'Monthly Analytics Reports',
      'ROI Measurement',
      'Competitor Analysis'
    ]
  }
];

// Process steps for social media marketing
const processSteps = [
  {
    step: '1',
    title: 'Strategy Development',
    description: 'We analyze your brand, audience, and competitors to create a comprehensive social media strategy.'
  },
  {
    step: '2',
    title: 'Content Planning',
    description: 'Develop a content calendar with engaging posts, stories, and campaigns tailored to each platform.'
  },
  {
    step: '3',
    title: 'Content Creation',
    description: 'Create high-quality graphics, videos, and copy that align with your brand and engage your audience.'
  },
  {
    step: '4',
    title: 'Publishing & Engagement',
    description: 'Schedule posts at optimal times and actively engage with your community to build relationships.'
  },
  {
    step: '5',
    title: 'Monitor & Optimize',
    description: 'Track performance metrics, analyze results, and continuously optimize for better engagement and growth.'
  }
];

// FAQ data for social media
const faqData = [
  {
    question: "How many posts will you create per week?",
    answer: "This depends on your package. Our Starter package includes 4 posts per week per platform, while our Growth package includes daily posting. We also create additional content for stories, reels, and special campaigns."
  },
  {
    question: "Do you create custom graphics and videos?",
    answer: "Yes! We create all content from scratch, including custom graphics, videos, animations, and written content. Everything is tailored to your brand identity and optimized for each specific platform."
  },
  {
    question: "Which social media platforms do you manage?",
    answer: "We manage all major platforms including Instagram, Facebook, LinkedIn, TikTok, Twitter/X, and YouTube. We recommend platforms based on your target audience and business goals."
  },
  {
    question: "How do you handle negative comments or reviews?",
    answer: "We have a comprehensive crisis management protocol. We respond professionally and promptly to negative feedback, escalate serious issues to you, and help turn negative situations into positive outcomes when possible."
  },
  {
    question: "Can I see content before it's published?",
    answer: "Absolutely! We provide content calendars for approval and can set up approval workflows based on your preferences. You'll have full visibility and control over what gets published."
  }
];

export default function SocialMediaPage() {
  const [mounted, setMounted] = useState(false);
  const [selectedPackage, setSelectedPackage] = useState('');
  const [selectedPlatforms, setSelectedPlatforms] = useState<string[]>([]);

  useEffect(() => {
    setMounted(true);
  }, []);

  const handlePackageSelect = (packageName: string, price: string) => {
    const whatsappMessage = `Hello Mocky Digital! I'm interested in the ${packageName} package (KES ${price}). Please provide more information about social media marketing services.`;
    const whatsappUrl = `https://wa.me/254741590670?text=${encodeURIComponent(whatsappMessage)}`;
    window.open(whatsappUrl, '_blank');
  };

  const handlePackageChange = (packageType: string) => {
    setSelectedPackage(packageType);
    
    // Auto-select platforms based on package
    if (packageType === 'starter') {
      setSelectedPlatforms(['instagram', 'facebook']);
    } else if (packageType === 'growth') {
      setSelectedPlatforms(['instagram', 'facebook', 'linkedin', 'tiktok']);
    } else if (packageType === 'enterprise') {
      setSelectedPlatforms(['instagram', 'facebook', 'linkedin', 'tiktok', 'twitter', 'youtube']);
    } else {
      setSelectedPlatforms([]);
    }
  };

  const handleCustomQuote = () => {
    const platformsText = selectedPlatforms.length > 0 ? selectedPlatforms.join(', ') : 'to be discussed';
    const whatsappMessage = `Hello Mocky Digital! I'm interested in a custom social media marketing package. 

Platforms I'm interested in: ${platformsText}

Please provide a custom quote and discuss my specific needs.`;
    const whatsappUrl = `https://wa.me/254741590670?text=${encodeURIComponent(whatsappMessage)}`;
    window.open(whatsappUrl, '_blank');
  };

  if (!mounted) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-slate-50 via-white via-gray-50/30 to-orange-50/20 pt-16">
      {/* Hero Section - Matching Catalogue Design */}
      <div className="relative min-h-[60vh] sm:min-h-[70vh] lg:min-h-[80vh] bg-gradient-to-b from-slate-50 via-white to-gray-50/30 overflow-hidden flex items-center">
        {/* Dynamic Background Pattern */}
        <div className="absolute inset-0 opacity-[0.03]">
          <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
            <defs>
              <pattern id="social-media-grid" width="15" height="15" patternUnits="userSpaceOnUse">
                <path d="M 15 0 L 0 0 0 15" fill="none" stroke="#FF5400" strokeWidth="0.3"/>
                <circle cx="7.5" cy="7.5" r="0.5" fill="#FF5400" opacity="0.1"/>
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#social-media-grid)" />
          </svg>
        </div>

        {/* Enhanced Floating Elements - Responsive */}
        <div className="absolute top-10 sm:top-20 left-4 sm:left-10 w-20 sm:w-32 lg:w-40 h-20 sm:h-32 lg:h-40 bg-gradient-to-br from-orange-200/40 to-red-200/40 rounded-full blur-xl sm:blur-2xl animate-pulse"></div>
        <div className="absolute top-16 sm:top-32 right-4 sm:right-16 w-16 sm:w-24 lg:w-32 h-16 sm:h-24 lg:h-32 bg-gradient-to-br from-blue-200/30 to-purple-200/30 rounded-full blur-lg sm:blur-xl animate-pulse" style={{ animationDelay: '1.5s' }}></div>
        <div className="absolute bottom-20 sm:bottom-40 left-1/4 w-14 sm:w-20 lg:w-28 h-14 sm:h-20 lg:h-28 bg-gradient-to-br from-green-200/25 to-teal-200/25 rounded-full blur-lg sm:blur-xl animate-pulse" style={{ animationDelay: '2.5s' }}></div>
        <div className="absolute bottom-12 sm:bottom-24 right-1/4 sm:right-1/3 w-18 sm:w-28 lg:w-36 h-18 sm:h-28 lg:h-36 bg-gradient-to-br from-pink-200/30 to-rose-200/30 rounded-full blur-xl sm:blur-2xl animate-pulse" style={{ animationDelay: '0.8s' }}></div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 sm:py-16 lg:py-20">
          <div className="text-center space-y-8 sm:space-y-10 lg:space-y-12">
            {/* Premium Badge with Animation */}
            <div className="inline-flex items-center gap-2 sm:gap-3 bg-white/90 backdrop-blur-xl text-[#FF5400] px-4 sm:px-6 lg:px-8 py-3 sm:py-4 rounded-full text-xs sm:text-sm font-bold border border-orange-200/60 shadow-2xl hover:shadow-3xl transition-all duration-500 hover:scale-105 group">
              <SparklesIcon className="h-4 w-4 sm:h-5 sm:w-5 group-hover:rotate-12 transition-transform duration-300" />
              <span className="bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">
                SOCIAL MEDIA MARKETING SERVICES
              </span>
            </div>

            {/* Revolutionary Title */}
            <div className="space-y-4 sm:space-y-6">
              <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-black text-gray-900 leading-[0.9] tracking-tight px-2">
                Grow Your{' '}
                <span className="relative inline-block">
                  <span className="bg-gradient-to-r from-[#FF5400] via-orange-500 to-red-500 bg-clip-text text-transparent">
                    Social
                  </span>
                  <div className="absolute -bottom-1 sm:-bottom-2 left-0 right-0 h-0.5 sm:h-1 bg-gradient-to-r from-orange-400 to-red-400 rounded-full opacity-30"></div>
                </span>
              </h1>
              <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-black text-gray-800 leading-tight px-2">
                Media Presence
              </h2>
            </div>

            <p className="text-base sm:text-lg md:text-xl lg:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed font-light px-4">
              Build a powerful social media presence that engages your audience and drives business growth.
              <span className="font-semibold text-gray-800"> From content creation to community management, we handle it all</span>
            </p>

            {/* Action Buttons */}
            <div className="flex flex-wrap gap-4 sm:gap-6 justify-center px-4">
              <a
                href="#services"
                className="inline-flex items-center gap-2 bg-[#FF5400] hover:bg-[#e64900] text-white px-6 sm:px-8 py-3 sm:py-4 rounded-full font-semibold text-sm sm:text-base transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl"
              >
                Explore Services
              </a>
              <a
                href="#pricing"
                className="inline-flex items-center gap-2 bg-white/90 backdrop-blur-xl hover:bg-white text-gray-900 px-6 sm:px-8 py-3 sm:py-4 rounded-full font-semibold text-sm sm:text-base transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl border border-gray-200/60"
              >
                View Pricing
              </a>
            </div>
          </div>
        </div>
      </div>

    <main>

      {/* Value Proposition Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-[#FF5400]/10 border border-[#FF5400]/20 mb-6">
              <i className="fas fa-chart-line text-[#FF5400]"></i>
              <span className="text-sm font-medium text-[#FF5400]">WHY SOCIAL MEDIA MARKETING</span>
            </div>
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-black text-gray-900 mb-6">
              Connect, Engage, and Grow Your <span className="text-[#FF5400]">Brand</span>
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Social media is where your customers spend their time. We help you create meaningful connections, 
              build brand loyalty, and drive real business results through strategic social media marketing.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                icon: (
                  <svg className="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                ),
                title: 'Build Community',
                description: 'Create a loyal community of engaged followers who become brand advocates'
              },
              {
                icon: (
                  <svg className="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                ),
                title: 'Increase Brand Awareness',
                description: 'Reach new audiences and increase visibility across all major social platforms'
              },
              {
                icon: (
                  <svg className="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                  </svg>
                ),
                title: 'Drive Sales',
                description: 'Convert social media engagement into actual sales and revenue for your business'
              },
              {
                icon: (
                  <svg className="w-10 h-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                ),
                title: 'Measurable Results',
                description: 'Track engagement, reach, and ROI with detailed analytics and reporting'
              }
            ].map((benefit, index) => (
              <div
                key={index}
                className="text-center p-6 rounded-xl hover:shadow-lg transition-shadow"
              >
                <div className="flex justify-center mb-4 text-[#FF5400]">{benefit.icon}</div>
                <h3 className="text-xl font-bold mb-3 text-gray-900">{benefit.title}</h3>
                <p className="text-gray-600">{benefit.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Platforms Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-[#FF5400]/10 border border-[#FF5400]/20 mb-6">
              <i className="fas fa-share-alt text-[#FF5400]"></i>
              <span className="text-sm font-medium text-[#FF5400]">SOCIAL PLATFORMS</span>
            </div>
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-black text-gray-900 mb-6">
              Platforms We <span className="text-[#FF5400]">Master</span>
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              We manage your presence across all major social media platforms with platform-specific strategies and content.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {platforms.map((platform, index) => (
              <div
                key={platform.name}
                className="bg-white p-8 rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 group"
              >
                <div className={`w-16 h-16 ${platform.color} rounded-xl flex items-center justify-center mb-6 text-white group-hover:scale-110 transition-transform`}>
                  {platform.icon}
                </div>
                <h3 className="text-xl font-bold mb-4 text-gray-900">{platform.name}</h3>
                <ul className="space-y-2">
                  {platform.services.map((service, serviceIndex) => (
                    <li key={serviceIndex} className="flex items-center gap-3">
                      <div className="w-2 h-2 rounded-full bg-[#FF5400]"></div>
                      <span className="text-gray-600 text-sm">{service}</span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section id="services" className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-[#FF5400]/10 border border-[#FF5400]/20 mb-6">
              <i className="fas fa-cogs text-[#FF5400]"></i>
              <span className="text-sm font-medium text-[#FF5400]">OUR SERVICES</span>
            </div>
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-black text-gray-900 mb-6">
              Comprehensive Social Media <span className="text-[#FF5400]">Solutions</span>
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              From strategy to execution, we provide end-to-end social media marketing services that drive engagement and growth.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            {socialMediaServices.map((service, index) => (
              <div
                key={index}
                className="bg-gray-50 p-8 rounded-xl hover:shadow-lg transition-all duration-300"
              >
                <div className="flex justify-center mb-6 text-[#FF5400]">{service.icon}</div>
                <h3 className="text-xl font-bold mb-4 text-gray-900 text-center">{service.title}</h3>
                <p className="text-gray-600 mb-6 text-center">{service.description}</p>
                <ul className="space-y-2">
                  {service.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center gap-3">
                      <div className="w-2 h-2 rounded-full bg-[#FF5400]"></div>
                      <span className="text-gray-600 text-sm">{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section id="pricing" className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-[#FF5400]/10 border border-[#FF5400]/20 mb-6">
              <i className="fas fa-tag text-[#FF5400]"></i>
              <span className="text-sm font-medium text-[#FF5400]">PRICING PACKAGES</span>
            </div>
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-black text-gray-900 mb-6">
              Social Media Marketing <span className="text-[#FF5400]">Packages</span>
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Choose the perfect package for your social media needs. All packages include content creation, posting, and monthly reporting.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto">
            {[
              {
                name: 'Starter Social',
                price: '15,000',
                period: '/month',
                description: 'Perfect for small businesses starting their social media journey',
                features: [
                  '4 posts per week (per platform)',
                  '2 Social Media Platforms',
                  'Content Creation & Design',
                  'Basic Analytics Reporting',
                  'Community Management',
                  'Email Support'
                ],
                platforms: ['Instagram', 'Facebook'],
                popular: false,
                buttonText: 'Get Started',
                buttonStyle: 'bg-gray-900 hover:bg-gray-800 text-white'
              },
              {
                name: 'Growth Social',
                price: '25,000',
                period: '/month',
                description: 'Comprehensive social media management for growing businesses',
                features: [
                  '6 posts per week (per platform)',
                  '4 Social Media Platforms',
                  'Advanced Content Creation',
                  'Stories & Reels Content',
                  'Community Management',
                  'Monthly Strategy Reviews',
                  'Detailed Analytics Reports',
                  'Priority Support'
                ],
                platforms: ['Instagram', 'Facebook', 'LinkedIn', 'TikTok'],
                popular: true,
                buttonText: 'Most Popular',
                buttonStyle: 'bg-[#FF5400] hover:bg-[#e84a00] text-white'
              },
              {
                name: 'Enterprise Social',
                price: '45,000',
                period: '/month',
                description: 'Full-scale social media marketing for established businesses',
                features: [
                  'Daily content posting',
                  '6+ Social Media Platforms',
                  'Premium Content Creation',
                  'Video Content & Animation',
                  'Influencer Collaborations',
                  'Social Media Advertising',
                  'Weekly Strategy Sessions',
                  'Dedicated Account Manager'
                ],
                platforms: ['Instagram', 'Facebook', 'LinkedIn', 'TikTok', 'Twitter', 'YouTube'],
                popular: false,
                buttonText: 'Contact Us',
                buttonStyle: 'bg-gray-900 hover:bg-gray-800 text-white'
              }
            ].map((pkg, index) => (
              <div
                key={index}
                className={`relative bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 ${
                  pkg.popular ? 'border-2 border-[#FF5400] transform scale-105' : 'border border-gray-200'
                }`}
              >
                {pkg.popular && (
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <span className="bg-[#FF5400] text-white px-4 py-1 rounded-full text-sm font-medium">
                      Most Popular
                    </span>
                  </div>
                )}
                <div className="p-8">
                  <h3 className="text-2xl font-bold text-gray-900 mb-2">{pkg.name}</h3>
                  <p className="text-gray-600 mb-6">{pkg.description}</p>
                  <div className="mb-6">
                    <span className="text-4xl font-bold text-gray-900">KES {pkg.price}</span>
                    <span className="text-gray-600">{pkg.period}</span>
                  </div>
                  
                  {/* Platform indicators */}
                  <div className="mb-6">
                    <p className="text-sm font-medium text-gray-700 mb-2">Included Platforms:</p>
                    <div className="flex flex-wrap gap-2">
                      {pkg.platforms.map((platform) => (
                        <span key={platform} className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs">
                          {platform}
                        </span>
                      ))}
                    </div>
                  </div>

                  <ul className="space-y-3 mb-8">
                    {pkg.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center gap-3">
                        <svg className="w-5 h-5 text-green-500 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                        <span className="text-gray-600">{feature}</span>
                      </li>
                    ))}
                  </ul>
                  <button
                    onClick={() => handlePackageSelect(pkg.name, pkg.price)}
                    className={`w-full py-3 px-6 rounded-lg font-semibold transition-colors ${pkg.buttonStyle}`}
                  >
                    {pkg.buttonText}
                  </button>
                  
                  <div className="mt-4 text-center">
                    <p className="text-xs text-gray-500">
                      <svg className="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                      Paid ads/boost posts available (additional cost based on your budget)
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Custom Package Section */}
          <div className="mt-12 text-center">
            <div className="bg-white rounded-2xl shadow-lg p-8 max-w-2xl mx-auto">
              <h3 className="text-2xl font-bold text-gray-900 mb-4">Need a Custom Package?</h3>
              <p className="text-gray-600 mb-6">
                Every business is unique. Let us create a custom social media package tailored specifically to your needs and budget.
              </p>
              <button
                onClick={handleCustomQuote}
                className="bg-[#FF5400] hover:bg-[#e84a00] text-white px-8 py-3 rounded-lg font-semibold transition-colors"
              >
                Get Custom Quote
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Process Section */}
      <ModernProcessSection
        title="Our Social Media Process"
        description="From strategy to execution, we follow a proven process that builds engagement and grows your following"
        steps={processSteps}
        badge="OUR PROCESS"
        badgeIcon="fas fa-route"
        highlightWord="Process"
      />

      {/* CTA Section */}
      <section className="py-20 bg-[#0A2647]">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-black text-white mb-6">
            Ready to Transform Your Social Media <span className="text-[#FF5400]">Presence?</span>
          </h2>
          <p className="text-xl text-gray-300 mb-8 max-w-2xl mx-auto">
            Let's create a social media strategy that builds community, drives engagement, and grows your business. 
            Contact us today to get started.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button
              onClick={() => {
                const whatsappMessage = "Hello Mocky Digital! I'm interested in your social media marketing services. Please provide more information.";
                const whatsappUrl = `https://wa.me/254741590670?text=${encodeURIComponent(whatsappMessage)}`;
                window.open(whatsappUrl, '_blank');
              }}
              className="bg-[#FF5400] hover:bg-[#e84a00] text-white px-8 py-4 rounded-lg font-semibold transition-colors inline-flex items-center justify-center gap-3"
            >
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.306"/>
              </svg>
              Start Your Social Journey
            </button>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <ModernFAQSection
        faqs={faqData}
      />
    </main>
    </div>
  );
}