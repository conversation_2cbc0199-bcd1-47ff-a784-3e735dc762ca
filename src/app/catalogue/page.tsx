'use client';

import { useState, useEffect } from 'react';
import SimplePricingCard from '@/components/SimplePricingCard';
import { 
  MagnifyingGlassIcon, 
  SparklesIcon,
  ChevronDownIcon,
  AdjustmentsHorizontalIcon,
  XMarkIcon,
  Bars3Icon
} from '@heroicons/react/24/outline';

interface Unit {
  id: string;
  name: string;
  displayName: string;
  plural: string;
  shortForm?: string;
  category: string;
}

interface CatalogueItem {
  id: string;
  service: string;
  price: number;
  designFee?: number;
  description?: string;
  features?: string[];
  icon?: string;
  popular?: boolean;
  imageUrl?: string;
  imageUrl2?: string;
  imageUrl3?: string;
  category?: string;
  pricingType?: string;
  unitType?: string; // Deprecated - for backward compatibility
  unitId?: string;
  unit?: Unit;
  pricePerMeter?: number;
  createdAt: string;
  updatedAt: string;
}

interface FilterState {
  categories: string[];
  priceRange: {
    min: number;
    max: number;
  };
  designFeeRange: {
    min: number;
    max: number;
  };
  popular: boolean;
  newest: boolean;
}

export default function CataloguePage() {
  const [catalogueItems, setCatalogueItems] = useState<CatalogueItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'name' | 'price-low' | 'price-high' | 'newest' | 'popular'>('name');
  
  // Filter state
  const [filters, setFilters] = useState<FilterState>({
    categories: [],
    priceRange: { min: 0, max: 50000 },
    designFeeRange: { min: 0, max: 10000 },
    popular: false,
    newest: false
  });

  // Fetch catalogue items
  useEffect(() => {
    const fetchCatalogueItems = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/catalogue?t=${Date.now()}`);
        if (response.ok) {
          const data = await response.json();
          const items = Array.isArray(data) ? data : (data.items || data.data || []);
          setCatalogueItems(items);
          
          // Set initial price range based on actual data
          if (items.length > 0) {
            const prices = items.map((item: CatalogueItem) => item.price);
            const designFees = items.map((item: CatalogueItem) => item.designFee || 0).filter((fee: number) => fee > 0);
            
            setFilters(prev => ({
              ...prev,
              priceRange: {
                min: 0,
                max: Math.max(...prices)
              },
              designFeeRange: {
                min: 0,
                max: designFees.length > 0 ? Math.max(...designFees) : 10000
              }
            }));
          }
        } else {
          console.error('Failed to fetch catalogue items:', response.status, response.statusText);
        }
      } catch (error) {
        console.error('Error fetching catalogue items:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchCatalogueItems();
  }, []);

  // Get unique categories
  const categories = Array.from(new Set(catalogueItems.map(item => item.category || 'Other'))).sort();

  // Filter and sort items
  const filteredAndSortedItems = catalogueItems
    .filter(item => {
      // Search filter
      const matchesSearch = !searchTerm || 
        item.service.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (item.description && item.description.toLowerCase().includes(searchTerm.toLowerCase()));

      // Category filter
      const matchesCategory = filters.categories.length === 0 || 
        filters.categories.includes(item.category || 'Other');

      // Price filter
      const matchesPrice = item.price >= filters.priceRange.min && item.price <= filters.priceRange.max;

      // Design fee filter
      const designFee = item.designFee || 0;
      const matchesDesignFee = designFee >= filters.designFeeRange.min && designFee <= filters.designFeeRange.max;

      // Popular filter
      const matchesPopular = !filters.popular || item.popular;

      // Newest filter (items from last 30 days)
      const matchesNewest = !filters.newest || 
        new Date(item.createdAt) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

      return matchesSearch && matchesCategory && matchesPrice && matchesDesignFee && matchesPopular && matchesNewest;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'price-low':
          return a.price - b.price;
        case 'price-high':
          return b.price - a.price;
        case 'newest':
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        case 'popular':
          return (b.popular ? 1 : 0) - (a.popular ? 1 : 0);
        case 'name':
        default:
          return a.service.localeCompare(b.service);
      }
    });

  // Handle category filter
  const toggleCategory = (category: string) => {
    setFilters(prev => ({
      ...prev,
      categories: prev.categories.includes(category)
        ? prev.categories.filter(c => c !== category)
        : [...prev.categories, category]
    }));
  };

  // Clear all filters
  const clearFilters = () => {
    setFilters({
      categories: [],
      priceRange: { min: 0, max: Math.max(...catalogueItems.map(item => item.price)) },
      designFeeRange: { min: 0, max: Math.max(...catalogueItems.map(item => item.designFee || 0)) },
      popular: false,
      newest: false
    });
  };

  // Get active filter count
  const activeFilterCount = 
    filters.categories.length + 
    (filters.popular ? 1 : 0) + 
    (filters.newest ? 1 : 0) +
    (filters.priceRange.min > 0 || filters.priceRange.max < Math.max(...catalogueItems.map(item => item.price)) ? 1 : 0) +
    (filters.designFeeRange.min > 0 || filters.designFeeRange.max < Math.max(...catalogueItems.map(item => item.designFee || 0)) ? 1 : 0);

  return (
    <div className="min-h-screen bg-gradient-to-b from-slate-50 via-white via-gray-50/30 to-orange-50/20 pt-16">
      {/* Revolutionary Hero Section */}
      <div className="relative min-h-[60vh] sm:min-h-[70vh] lg:min-h-[80vh] bg-gradient-to-b from-slate-50 via-white to-gray-50/30 overflow-hidden flex items-center">
        {/* Dynamic Background Pattern */}
        <div className="absolute inset-0 opacity-[0.03]">
          <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
            <defs>
              <pattern id="catalogue-grid" width="15" height="15" patternUnits="userSpaceOnUse">
                <path d="M 15 0 L 0 0 0 15" fill="none" stroke="#FF5400" strokeWidth="0.3"/>
                <circle cx="7.5" cy="7.5" r="0.5" fill="#FF5400" opacity="0.1"/>
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#catalogue-grid)" />
          </svg>
        </div>

        {/* Enhanced Floating Elements - Responsive */}
        <div className="absolute top-10 sm:top-20 left-4 sm:left-10 w-20 sm:w-32 lg:w-40 h-20 sm:h-32 lg:h-40 bg-gradient-to-br from-orange-200/40 to-red-200/40 rounded-full blur-xl sm:blur-2xl animate-pulse"></div>
        <div className="absolute top-16 sm:top-32 right-4 sm:right-16 w-16 sm:w-24 lg:w-32 h-16 sm:h-24 lg:h-32 bg-gradient-to-br from-blue-200/30 to-purple-200/30 rounded-full blur-lg sm:blur-xl animate-pulse" style={{ animationDelay: '1.5s' }}></div>
        <div className="absolute bottom-20 sm:bottom-40 left-1/4 w-14 sm:w-20 lg:w-28 h-14 sm:h-20 lg:h-28 bg-gradient-to-br from-green-200/25 to-teal-200/25 rounded-full blur-lg sm:blur-xl animate-pulse" style={{ animationDelay: '2.5s' }}></div>
        <div className="absolute bottom-12 sm:bottom-24 right-1/4 sm:right-1/3 w-18 sm:w-28 lg:w-36 h-18 sm:h-28 lg:h-36 bg-gradient-to-br from-pink-200/30 to-rose-200/30 rounded-full blur-xl sm:blur-2xl animate-pulse" style={{ animationDelay: '0.8s' }}></div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 sm:py-16 lg:py-20">
          <div className="text-center space-y-8 sm:space-y-10 lg:space-y-12">
            {/* Premium Badge with Animation */}
            <div className="inline-flex items-center gap-2 sm:gap-3 bg-white/90 backdrop-blur-xl text-[#FF5400] px-4 sm:px-6 lg:px-8 py-3 sm:py-4 rounded-full text-xs sm:text-sm font-bold border border-orange-200/60 shadow-2xl hover:shadow-3xl transition-all duration-500 hover:scale-105 group">
              <SparklesIcon className="h-4 w-4 sm:h-5 sm:w-5 group-hover:rotate-12 transition-transform duration-300" />
              <span className="bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">
                PREMIUM SERVICES CATALOGUE
              </span>
            </div>

            {/* Revolutionary Title */}
            <div className="space-y-4 sm:space-y-6">
              <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-black text-gray-900 leading-[0.9] tracking-tight px-2">
                Discover{' '}
                <span className="relative inline-block">
                  <span className="bg-gradient-to-r from-[#FF5400] via-orange-500 to-red-500 bg-clip-text text-transparent">
                    Professional
                  </span>
                  <div className="absolute -bottom-1 sm:-bottom-2 left-0 right-0 h-0.5 sm:h-1 bg-gradient-to-r from-orange-400 to-red-400 rounded-full opacity-30"></div>
                </span>
              </h1>
              <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-black text-gray-800 leading-tight px-2">
                Services & Solutions
              </h2>
            </div>

            <p className="text-base sm:text-lg md:text-xl lg:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed font-light px-4">
              Explore our comprehensive range of premium services and innovative solutions
              designed to <span className="font-semibold text-gray-800">transform your business vision into reality</span>
            </p>

            {/* Revolutionary Search Bar */}
            <div className="max-w-4xl mx-auto relative group px-4">
              <div className="absolute inset-0 bg-gradient-to-r from-orange-400/30 to-red-400/30 rounded-2xl sm:rounded-3xl blur-xl sm:blur-2xl transition-all duration-500 group-hover:blur-2xl sm:group-hover:blur-3xl group-focus-within:blur-2xl sm:group-focus-within:blur-3xl"></div>
              <div className="relative flex items-center bg-white/95 backdrop-blur-xl rounded-2xl sm:rounded-3xl border border-white/30 shadow-2xl overflow-hidden hover:shadow-3xl transition-all duration-500">
                <div className="pl-4 sm:pl-6 lg:pl-8 pr-2 sm:pr-4 py-2">
                  <MagnifyingGlassIcon className="h-5 w-5 sm:h-6 sm:w-6 lg:h-7 lg:w-7 text-gray-400 group-focus-within:text-orange-500 transition-colors duration-300" />
                </div>
                <input
                  type="text"
                  placeholder="Search for services, categories, solutions..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="flex-1 px-2 sm:px-4 py-4 sm:py-5 lg:py-6 text-sm sm:text-lg lg:text-xl text-gray-900 placeholder-gray-500 bg-transparent border-0 focus:outline-none focus:ring-0 font-medium"
                />
                {searchTerm && (
                  <button
                    onClick={() => setSearchTerm('')}
                    className="p-2 sm:p-3 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full transition-all duration-300 mr-2 sm:mr-4"
                  >
                    <XMarkIcon className="h-4 w-4 sm:h-5 sm:w-5 lg:h-6 lg:w-6" />
                  </button>
                )}
              </div>
            </div>


          </div>
        </div>
      </div>



      {/* Premium Content Area */}
      <section className="pb-20 bg-gradient-to-b from-white via-gray-50/30 to-orange-50/20">
        <div className="container mx-auto px-4">
          {/* Loading State */}
          {loading && (
            <div className="flex flex-col justify-center items-center py-20">
              <div className="relative">
                <div className="animate-spin rounded-full h-16 w-16 border-4 border-gray-200 border-t-[#FF5400]"></div>
                <div className="absolute inset-0 rounded-full bg-gradient-to-r from-[#FF5400]/20 to-red-400/20 blur-lg"></div>
              </div>
              <div className="mt-6 text-center">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Loading Services</h3>
                <p className="text-gray-600">Discovering amazing solutions for you...</p>
              </div>
            </div>
          )}

          {/* Premium Services Grid */}
          {!loading && (
            <>
              {filteredAndSortedItems.length > 0 ? (
                <div className="grid sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 gap-8 lg:gap-12">
                  {filteredAndSortedItems.map((item, index) => (
                    <div
                      key={item.id}
                      className="group transform hover:scale-[1.03] transition-all duration-500 hover:z-10 relative"
                      style={{
                        animationDelay: `${index * 150}ms`,
                        opacity: 0,
                        animation: 'fadeInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards'
                      }}
                    >
                      {/* Enhanced Card Wrapper with Glow Effect */}
                      <div className="absolute inset-0 bg-gradient-to-r from-orange-400/0 via-orange-400/5 to-red-400/0 rounded-3xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 -z-10"></div>

                      <SimplePricingCard
                        id={item.id}
                        service={item.service}
                        price={item.price}
                        designFee={item.designFee}
                        description={item.description}
                        features={item.features}
                        icon={item.icon}
                        popular={item.popular}
                        imageUrl={item.imageUrl}
                        imageUrl2={item.imageUrl2}
                        imageUrl3={item.imageUrl3}
                        category={item.category}
                        pricingType={item.pricingType}
                        unitType={item.unitType}
                        unitId={item.unitId}
                        unit={item.unit}
                        pricePerMeter={item.pricePerMeter}
                      />
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-24">
                  <div className="max-w-lg mx-auto">
                    {/* Enhanced Empty State */}
                    <div className="relative mb-12">
                      <div className="bg-gradient-to-br from-orange-100 via-red-50 to-pink-100 rounded-full p-12 w-40 h-40 mx-auto flex items-center justify-center shadow-2xl">
                        <MagnifyingGlassIcon className="h-20 w-20 text-orange-400" />
                      </div>
                      <div className="absolute inset-0 bg-gradient-to-r from-orange-400/20 to-red-400/20 rounded-full blur-2xl"></div>
                    </div>

                    <h3 className="text-3xl font-black text-gray-900 mb-6">No Services Found</h3>
                    <p className="text-xl text-gray-600 mb-12 leading-relaxed font-light">
                      We couldn't find any services matching your criteria.
                      <span className="font-semibold text-gray-800"> Try adjusting your search or filters</span> to discover more amazing options.
                    </p>

                    <div className="space-y-6">
                      <button
                        onClick={clearFilters}
                        className="bg-gradient-to-r from-[#FF5400] to-red-500 hover:from-[#e84a00] hover:to-red-600 text-white px-6 py-2 font-bold transition-all duration-300 shadow-2xl hover:shadow-3xl transform hover:scale-105"
                      >
                        🔄 Clear All Filters
                      </button>
                      <p className="text-base text-gray-500 font-medium">or try searching for something else</p>
                    </div>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </section>

      <style jsx>{`
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(40px) scale(0.95);
          }
          to {
            opacity: 1;
            transform: translateY(0) scale(1);
          }
        }

        @keyframes float {
          0%, 100% {
            transform: translateY(0px);
          }
          50% {
            transform: translateY(-10px);
          }
        }

        @keyframes pulse-glow {
          0%, 100% {
            box-shadow: 0 0 20px rgba(255, 84, 0, 0.1);
          }
          50% {
            box-shadow: 0 0 40px rgba(255, 84, 0, 0.2);
          }
        }

        @keyframes shimmer {
          0% {
            background-position: -200% 0;
          }
          100% {
            background-position: 200% 0;
          }
        }

        .animate-float {
          animation: float 6s ease-in-out infinite;
        }

        .animate-pulse-glow {
          animation: pulse-glow 3s ease-in-out infinite;
        }

        .animate-shimmer {
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
          background-size: 200% 100%;
          animation: shimmer 2s infinite;
        }

        @media (prefers-reduced-motion: reduce) {
          * {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
          }
        }
      `}</style>
    </div>
  );
}
