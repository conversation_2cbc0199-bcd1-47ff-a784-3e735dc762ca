import { MetadataRoute } from 'next';
import { getAllBlogPosts } from '@/services/blogService';
import { getBlogPostUrl, getCanonicalUrl } from '@/utils/url';

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  try {
    // Get all published blog posts
    const posts = await getAllBlogPosts();
    const publishedPosts = posts.filter(post => post.status === 'published');

    // Generate sitemap entries for blog posts
    const blogPostEntries: MetadataRoute.Sitemap = publishedPosts.map((post) => ({
      url: getBlogPostUrl(post.slug),
      lastModified: new Date(post.updatedAt),
      changeFrequency: 'weekly',
      priority: 0.8,
    }));

    // Add the main blog page
    const blogMainEntry: MetadataRoute.Sitemap = [
      {
        url: getCanonicalUrl('/blog'),
        lastModified: new Date(),
        changeFrequency: 'daily',
        priority: 0.9,
      },
    ];

    return [...blogMainEntry, ...blogPostEntries];
  } catch (error) {
    console.error('Error generating blog sitemap:', error);
    // Return at least the main blog page if there's an error
    return [
      {
        url: getCanonicalUrl('/blog'),
        lastModified: new Date(),
        changeFrequency: 'daily',
        priority: 0.9,
      },
    ];
  }
} 