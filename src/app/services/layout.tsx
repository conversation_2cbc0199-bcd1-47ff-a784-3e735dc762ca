import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Our Creative Services | Graphic Design, Web Development & Branding – Nairobi',
  description: 'Discover expert services from logos and posters to websites and branding strategy—all tailored for Nairobi-based businesses and startups.',
  keywords: 'creative services nairobi, graphic design services, web development services, branding services nairobi, logo design nairobi, website design nairobi, digital marketing services, professional design nairobi, business services kenya',
  openGraph: {
    title: 'Our Creative Services | Graphic Design, Web Development & Branding – Nairobi',
    description: 'Discover expert services from logos and posters to websites and branding strategy—all tailored for Nairobi-based businesses and startups.',
    type: 'website',
    url: 'https://mocky.co.ke/services',
    siteName: 'Mocky Digital',
    locale: 'en_KE',
    images: [
      {
        url: '/images/services-og.jpg',
        width: 1200,
        height: 630,
        alt: 'Creative Services in Nairobi - Mocky Digital',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Our Creative Services | Graphic Design, Web Development & Branding – Nairobi',
    description: 'Discover expert services from logos and posters to websites and branding strategy—all tailored for Nairobi-based businesses and startups.',
    images: ['/images/services-og.jpg'],
    creator: '@mockydigital',
    site: '@mockydigital',
  },
  alternates: {
    canonical: '/services',
  },
  robots: {
    index: true,
    follow: true,
  },
};

export default function ServicesLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return children;
}
