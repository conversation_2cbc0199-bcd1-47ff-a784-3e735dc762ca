'use client';

import React, { useState, useEffect } from 'react';
import { MagnifyingGlassIcon, FunnelIcon } from '@heroicons/react/24/outline';

interface Service {
  id: string;
  name: string;
  description: string;
  price: number;
  category: string;
  createdAt: string;
  updatedAt: string;
}

interface ServicesResponse {
  success: boolean;
  data: Service[];
  categories: string[];
  pagination: {
    total: number;
    limit: number;
    offset: number;
    hasMore: boolean;
  };
  filters: {
    search: string;
    category: string;
  };
}

export default function ServicesPage() {
  const [services, setServices] = useState<Service[]>([]);
  const [categories, setCategories] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [showFilters, setShowFilters] = useState(false);

  // Fetch services from API
  const fetchServices = async (search = '', category = 'all') => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams();
      if (search) params.append('search', search);
      if (category !== 'all') params.append('category', category);

      const response = await fetch(`/api/services?${params.toString()}`);
      const data: ServicesResponse = await response.json();

      if (data.success) {
        setServices(data.data);
        setCategories(data.categories);
      } else {
        setError('Failed to load services');
      }
    } catch (err) {
      setError('Failed to load services');
      console.error('Error fetching services:', err);
    } finally {
      setLoading(false);
    }
  };

  // Initial load
  useEffect(() => {
    fetchServices();
  }, []);

  // Handle search
  const handleSearch = (term: string) => {
    setSearchTerm(term);
    fetchServices(term, selectedCategory);
  };

  // Handle category filter
  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category);
    fetchServices(searchTerm, category);
  };

  // Format price for display
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-KE', {
      style: 'currency',
      currency: 'KES',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price);
  };

  return (
    <main className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-b from-[#1a2942] to-[#121f35] text-white py-20 page-header-padding">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">Our Services</h1>
            <p className="text-lg md:text-xl text-gray-300 mb-8">
              Professional digital solutions to grow your business
            </p>
          </div>
        </div>
      </section>

      {/* Search and Filter Section */}
      <section className="py-8 bg-white border-b">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
            {/* Search Bar */}
            <div className="relative flex-1 max-w-md">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                type="text"
                placeholder="Search services..."
                value={searchTerm}
                onChange={(e) => handleSearch(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#FF5400] focus:border-transparent"
              />
            </div>

            {/* Filter Toggle */}
            <button
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center gap-2 px-4 py-3 bg-[#0A2647] text-white rounded-lg hover:bg-[#1a3a6d] transition-colors"
            >
              <FunnelIcon className="h-5 w-5" />
              Filters
            </button>
          </div>

          {/* Category Filters */}
          {showFilters && (
            <div className="mt-4 p-4 bg-gray-50 rounded-lg">
              <h3 className="font-semibold mb-3">Filter by Category:</h3>
              <div className="flex flex-wrap gap-2">
                <button
                  onClick={() => handleCategoryChange('all')}
                  className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                    selectedCategory === 'all'
                      ? 'bg-[#FF5400] text-white'
                      : 'bg-white text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  All Services
                </button>
                {categories.map((category) => (
                  <button
                    key={category}
                    onClick={() => handleCategoryChange(category)}
                    className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                      selectedCategory === category
                        ? 'bg-[#FF5400] text-white'
                        : 'bg-white text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    {category}
                  </button>
                ))}
              </div>
            </div>
          )}
        </div>
      </section>

      {/* Services Grid Section */}
      <section className="py-12 bg-gray-50">
        <div className="container mx-auto px-4">
          {/* Loading State */}
          {loading && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(6)].map((_, index) => (
                <div key={index} className="bg-white rounded-2xl p-6 shadow-lg animate-pulse">
                  <div className="h-6 bg-gray-200 rounded mb-4"></div>
                  <div className="h-4 bg-gray-200 rounded mb-2"></div>
                  <div className="h-4 bg-gray-200 rounded mb-4"></div>
                  <div className="h-8 bg-gray-200 rounded"></div>
                </div>
              ))}
            </div>
          )}

          {/* Error State */}
          {error && (
            <div className="text-center py-12">
              <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
                <h3 className="text-red-800 font-semibold mb-2">Error Loading Services</h3>
                <p className="text-red-600 mb-4">{error}</p>
                <button
                  onClick={() => fetchServices(searchTerm, selectedCategory)}
                  className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                >
                  Try Again
                </button>
              </div>
            </div>
          )}

          {/* Services Grid */}
          {!loading && !error && (
            <>
              {services.length === 0 ? (
                <div className="text-center py-12">
                  <div className="bg-gray-100 rounded-lg p-8 max-w-md mx-auto">
                    <h3 className="text-gray-800 font-semibold mb-2">No Services Found</h3>
                    <p className="text-gray-600">
                      {searchTerm || selectedCategory !== 'all'
                        ? 'Try adjusting your search or filters'
                        : 'No services are currently available'}
                    </p>
                  </div>
                </div>
              ) : (
                <>
                  <div className="text-center mb-8">
                    <h2 className="text-3xl md:text-4xl font-bold text-[#0A2647] mb-4">
                      Available Services
                    </h2>
                    <p className="text-gray-600 text-lg">
                      {services.length} service{services.length !== 1 ? 's' : ''} found
                    </p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {services.map((service, index) => (
                      <div
                        key={service.id}
                        className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border border-gray-100 hover:border-[#FF5400]/30 group"
                      >
                        {/* Service Header */}
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex-1">
                            <h3 className="text-xl font-bold text-[#0A2647] mb-2 group-hover:text-[#FF5400] transition-colors">
                              {service.name}
                            </h3>
                            <span className="inline-block px-3 py-1 bg-[#0A2647]/10 text-[#0A2647] text-sm font-medium rounded-full">
                              {service.category}
                            </span>
                          </div>
                        </div>

                        {/* Service Description */}
                        <p className="text-gray-600 mb-6 line-clamp-3">
                          {service.description || 'Professional service tailored to your needs.'}
                        </p>

                        {/* Price and CTA */}
                        <div className="flex items-center justify-between">
                          <div className="text-2xl font-bold text-[#FF5400]">
                            {formatPrice(service.price)}
                          </div>
                          <a
                            href={`https://wa.me/254741590670?text=Hi%20Mocky%20Digital,%20I'm%20interested%20in%20ordering%20${encodeURIComponent(service.name)}%20for%20${encodeURIComponent(formatPrice(service.price))}.%0A%0AService%20Details:%0A${encodeURIComponent(service.description || 'Professional service tailored to your needs.')}.%0A%0APlease%20let%20me%20know%20the%20next%20steps.`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="px-6 py-2 bg-[#0A2647] text-white rounded-lg hover:bg-[#FF5400] transition-colors font-medium"
                          >
                            Order Now
                          </a>
                        </div>
                      </div>
                    ))}
                  </div>
                </>
              )}
            </>
          )}
        </div>
      </section>
    </main>
  );
}