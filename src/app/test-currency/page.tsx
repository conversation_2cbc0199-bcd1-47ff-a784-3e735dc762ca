'use client';

import React, { useState, useEffect } from 'react';
import SmartPrice from '@/components/SmartPrice';
import CurrencySelector from '@/components/CurrencySelector';
import { useCurrency } from '@/hooks/useCurrency';

export default function TestCurrencyPage() {
  const { currency, location, isLoading, setCurrency } = useCurrency();
  const [forceUpdate, setForceUpdate] = useState(0);
  const [debugInfo, setDebugInfo] = useState<string>('');

  const testPrices = [1000, 5000, 15000, 30000, 50000];

  const handleCurrencyTest = (testCurrency: string) => {
    console.log(`Testing currency change to: ${testCurrency}`);
    setCurrency(testCurrency);
    setForceUpdate(prev => prev + 1); // Force re-render
    setDebugInfo(`Changed to ${testCurrency} at ${new Date().toLocaleTimeString()}`);
  };

  // Monitor currency changes
  useEffect(() => {
    setDebugInfo(`Currency updated to: ${currency} at ${new Date().toLocaleTimeString()}`);
  }, [currency]);

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">Currency Conversion Test</h1>
          <p className="text-gray-600">Test the intelligent currency conversion system</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Currency Info */}
          <div className="bg-white rounded-lg p-6 shadow-sm">
            <h2 className="text-xl font-semibold mb-4">Current Settings</h2>
            <div className="space-y-4">
              <div>
                <p className="text-sm text-gray-600">Detected Location:</p>
                <p className="font-medium">
                  {isLoading ? 'Detecting...' : location ? `${location.country} (${location.countryCode})` : 'Unknown'}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-600">Current Currency:</p>
                <p className="font-medium text-lg text-blue-600">{currency}</p>
                <p className="text-xs text-gray-500">Force Update: {forceUpdate}</p>
                <p className="text-xs text-green-600">{debugInfo}</p>
              </div>
            </div>

            <div className="mt-4">
              <p className="text-sm text-gray-600 mb-2">Change Currency:</p>
              <CurrencySelector />
              
              {/* Quick Test Buttons */}
              <div className="mt-4">
                <p className="text-sm text-gray-600 mb-2">Quick Test:</p>
                <div className="flex flex-wrap gap-2">
                  <button 
                    onClick={() => handleCurrencyTest('USD')}
                    className="px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600"
                  >
                    USD
                  </button>
                  <button 
                    onClick={() => handleCurrencyTest('EUR')}
                    className="px-3 py-1 bg-green-500 text-white rounded text-sm hover:bg-green-600"
                  >
                    EUR
                  </button>
                  <button 
                    onClick={() => handleCurrencyTest('GBP')}
                    className="px-3 py-1 bg-purple-500 text-white rounded text-sm hover:bg-purple-600"
                  >
                    GBP
                  </button>
                  <button 
                    onClick={() => handleCurrencyTest('KES')}
                    className="px-3 py-1 bg-orange-500 text-white rounded text-sm hover:bg-orange-600"
                  >
                    KES
                  </button>
                </div>
              </div>

              {/* Real-time Currency Monitor */}
              <div className="mt-4 p-3 bg-gray-100 rounded">
                <p className="text-xs font-mono">Live Currency: {currency}</p>
                <p className="text-xs font-mono">Timestamp: {new Date().toLocaleTimeString()}</p>
                <p className="text-xs font-mono">Loading: {isLoading ? 'Yes' : 'No'}</p>
              </div>
            </div>
          </div>

          {/* Test Prices */}
          <div className="lg:col-span-2">
            <h2 className="text-xl font-semibold mb-4">Price Conversion Tests</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {testPrices.map((price) => (
                <div key={`${price}-${currency}-${forceUpdate}`} className="border rounded-lg p-4">
                  <p className="text-sm text-gray-600 mb-2">KES {price.toLocaleString()}</p>
                  <SmartPrice 
                    key={`smart-price-${price}-${currency}-${forceUpdate}`}
                    kesAmount={price} 
                    size="lg" 
                    showOriginal={true}
                    showCurrencyName={true}
                  />
                  <div className="mt-2 text-xs text-gray-500">
                    <p>Currency: {currency}</p>
                    <p>Update: {forceUpdate}</p>
                  </div>
                </div>
              ))}
            </div>

            {/* Simple Manual Test */}
            <div className="mt-8 bg-white rounded-lg p-6 shadow-sm">
              <h3 className="text-lg font-semibold mb-4">Manual Conversion Test</h3>
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-4">
                <p className="text-sm text-gray-600 mb-2">Logo Package - Basic (KES 5,000)</p>
                <div className="text-2xl font-bold text-[#FF5400]">
                  <SmartPrice 
                    key={`manual-test-${currency}-${forceUpdate}`}
                    kesAmount={5000} 
                    size="xl" 
                    showOriginal={true}
                    showCurrencyName={true}
                  />
                </div>
                <p className="text-xs text-gray-500 mt-2">
                  Should change when currency is switched above
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 