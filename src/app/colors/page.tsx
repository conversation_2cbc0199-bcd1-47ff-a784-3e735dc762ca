'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { ArrowPathIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';
import ColorPalette from '@/components/ColorPalette';
import PageHero from '@/components/PageHero';
import toast from 'react-hot-toast';

interface ColorPaletteData {
  id: string;
  name: string;
  colors: string[];
  likes?: number;
  tags?: string[];
  createdAt: Date;
  updatedAt: Date;
}

interface ApiResponse {
  success: boolean;
  palettes: ColorPaletteData[];
  cached?: boolean;
  fallback?: boolean;
  error?: string;
  timestamp: Date;
}

export default function ColorsPage() {
  const [palettes, setPalettes] = useState<ColorPaletteData[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);
  const [isFallback, setIsFallback] = useState(false);

  const fetchPalettes = async (forceRefresh = false) => {
    try {
      if (forceRefresh) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }
      
      setError(null);
      
      const url = forceRefresh 
        ? '/api/colors/trending?refresh=true'
        : '/api/colors/trending';
      
      const response = await fetch(url);
      const data: ApiResponse = await response.json();
      
      if (data.success) {
        setPalettes(data.palettes);
        setLastUpdated(new Date(data.timestamp));
        setIsFallback(data.fallback || false);
        
        if (data.cached) {
          toast.success('Loaded cached color palettes');
        } else if (data.fallback) {
          toast('Using fallback palettes - live data unavailable', {
            icon: '⚠️',
            duration: 4000
          });
        } else {
          toast.success('Fresh color palettes loaded!');
        }
      } else {
        setError(data.error || 'Failed to load color palettes');
        setPalettes(data.palettes || []); // Use fallback palettes if available
        setIsFallback(true);
        toast.error('Failed to load fresh palettes, showing fallback');
      }
    } catch (err) {
      console.error('Error fetching palettes:', err);
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
      toast.error('Failed to load color palettes');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchPalettes();
  }, []);

  const handleRefresh = () => {
    fetchPalettes(true);
  };

  const handleLike = (paletteId: string) => {
    // Update local state optimistically
    setPalettes(prev => 
      prev.map(palette => 
        palette.id === paletteId 
          ? { ...palette, likes: (palette.likes || 0) + 1 }
          : palette
      )
    );
  };

  const handleShare = (paletteId: string) => {
    // Analytics or tracking could be added here
    console.log(`Shared palette: ${paletteId}`);
  };

  if (loading) {
    return (
      <div className="pt-24 bg-gray-50 min-h-screen">
        <PageHero 
          title="Trending Color Palettes"
          subtitle="Discover the latest trending color combinations from around the web"
        />
        
        <div className="container mx-auto px-4 py-12">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {[...Array(8)].map((_, index) => (
              <div key={index} className="bg-white rounded-xl shadow-lg overflow-hidden animate-pulse">
                <div className="h-40 bg-gray-200"></div>
                <div className="p-4">
                  <div className="h-4 bg-gray-200 rounded mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="pt-24 bg-gray-50 min-h-screen">
      <PageHero 
        title="Trending Color Palettes"
        subtitle="Discover the latest trending color combinations from around the web"
      />

      {/* Status Bar */}
      <div className="bg-white border-b">
        <div className="container mx-auto px-4 py-4">
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">
                {palettes.length} palettes
              </span>
              {lastUpdated && (
                <span className="text-sm text-gray-500">
                  Updated: {lastUpdated.toLocaleTimeString()}
                </span>
              )}
              {isFallback && (
                <div className="flex items-center space-x-1 text-amber-600">
                  <ExclamationTriangleIcon className="w-4 h-4" />
                  <span className="text-sm">Fallback data</span>
                </div>
              )}
            </div>
            
            <button
              onClick={handleRefresh}
              disabled={refreshing}
              className="flex items-center space-x-2 px-4 py-2 bg-[#1A237E] text-white rounded-lg hover:bg-[#0D47A1] transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ArrowPathIcon className={`w-4 h-4 ${refreshing ? 'animate-spin' : ''}`} />
              <span>{refreshing ? 'Refreshing...' : 'Refresh'}</span>
            </button>
          </div>
        </div>
      </div>

      {/* Error Message */}
      {error && !isFallback && (
        <div className="container mx-auto px-4 py-4">
          <div className="bg-red-50 border border-red-200 rounded-lg p-4">
            <div className="flex items-center space-x-2">
              <ExclamationTriangleIcon className="w-5 h-5 text-red-500" />
              <span className="text-red-700">{error}</span>
            </div>
          </div>
        </div>
      )}

      {/* Palettes Grid */}
      <div className="container mx-auto px-4 py-12">
        {palettes.length > 0 ? (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
          >
            {palettes.map((palette, index) => (
              <motion.div
                key={palette.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
              >
                <ColorPalette
                  id={palette.id}
                  name={palette.name}
                  colors={palette.colors}
                  likes={palette.likes}
                  tags={palette.tags}
                  onLike={handleLike}
                  onShare={handleShare}
                />
              </motion.div>
            ))}
          </motion.div>
        ) : (
          <div className="text-center py-12">
            <ExclamationTriangleIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No palettes available</h3>
            <p className="text-gray-500 mb-4">
              We couldn't load any color palettes at the moment.
            </p>
            <button
              onClick={handleRefresh}
              className="px-4 py-2 bg-[#1A237E] text-white rounded-lg hover:bg-[#0D47A1] transition-colors duration-200"
            >
              Try Again
            </button>
          </div>
        )}
      </div>

      {/* Info Section */}
      <div className="bg-white py-12">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              About These Color Palettes
            </h2>
            <p className="text-gray-600 mb-6">
              These trending color palettes are sourced from popular design communities and updated regularly. 
              Click on any color to copy its hex code, or use the "Copy All Colors" button to get the entire palette.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 text-sm">
              <div className="text-center">
                <div className="w-12 h-12 bg-[#1A237E] rounded-full mx-auto mb-2 flex items-center justify-center">
                  <span className="text-white font-bold">1</span>
                </div>
                <h3 className="font-medium text-gray-900 mb-1">Click to Copy</h3>
                <p className="text-gray-600">Click any color swatch to copy its hex code</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-[#FF5400] rounded-full mx-auto mb-2 flex items-center justify-center">
                  <span className="text-white font-bold">2</span>
                </div>
                <h3 className="font-medium text-gray-900 mb-1">Save Favorites</h3>
                <p className="text-gray-600">Like palettes to keep track of your favorites</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 bg-gray-600 rounded-full mx-auto mb-2 flex items-center justify-center">
                  <span className="text-white font-bold">3</span>
                </div>
                <h3 className="font-medium text-gray-900 mb-1">Share & Export</h3>
                <p className="text-gray-600">Share palettes or copy all colors at once</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
