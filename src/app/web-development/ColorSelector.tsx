'use client';
import React from 'react';

import { useState } from 'react';
import { motion } from 'framer-motion';

type ColorScheme = {
  name: string;
  description: string;
  colors: string[];
};

const predefinedSchemes: ColorScheme[] = [
  {
    name: "Professional & Corporate",
    description: "Clean and trustworthy color combinations ideal for business websites",
    colors: ["#0A1929", "#1E3A8A", "#FFFFFF", "#F3F4F6", "#3B82F6"]
  },
  {
    name: "Creative & Vibrant",
    description: "Bold and energetic colors perfect for creative agencies and portfolios",
    colors: ["#7C3AED", "#EC4899", "#FFFFFF", "#F9FAFB", "#8B5CF6"]
  },
  {
    name: "Eco & Natural",
    description: "Earthy tones suitable for environmental and wellness brands",
    colors: ["#065F46", "#059669", "#FFFFFF", "#ECFDF5", "#34D399"]
  },
  {
    name: "Modern & Minimal",
    description: "Subtle and sophisticated colors for contemporary designs",
    colors: ["#111827", "#374151", "#FFFFFF", "#F9FAFB", "#6B7280"]
  },
  {
    name: "Warm & Welcoming",
    description: "Friendly and approachable colors for hospitality and service businesses",
    colors: ["#B45309", "#D97706", "#FFFFFF", "#FEF3C7", "#FBBF24"]
  }
];

export default function ColorSelector() {
  const [selectedScheme, setSelectedScheme] = useState<ColorScheme | null>(null);
  const [customColors, setCustomColors] = useState<string[]>(['#000000']);
  const [isCustom, setIsCustom] = useState(false);

  const handleAddColor = () => {
    setCustomColors([...customColors, '#000000']);
  };

  const handleColorChange = (index: number, color: string) => {
    const newColors = [...customColors];
    newColors[index] = color;
    setCustomColors(newColors);
  };

  const handleSubmit = () => {
    const colors = isCustom ? customColors : (selectedScheme?.colors || []);
    const message = `
*Color Scheme Selection for Website*

${isCustom ? '*Custom Color Scheme:*' : `*Selected Scheme: ${selectedScheme?.name}*`}
${colors.map((color, index) => `Color ${index + 1}: ${color}`).join('\n')}

*Additional Notes:*
${isCustom ? 'Custom colors selected by client' : selectedScheme?.description}

*Sent from the Color Selection Tool*
    `.trim();

    const whatsappUrl = `https://wa.me/254741590670?text=${encodeURIComponent(message)}`;
    window.open(whatsappUrl, '_blank');
  };

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="mb-8">
        <h3 className="text-2xl font-bold mb-4">Choose Your Color Scheme</h3>
        <div className="flex gap-4 mb-6">
          <button
            onClick={() => setIsCustom(false)}
            className={`px-4 py-2 rounded-full ${!isCustom ? 'bg-primary text-white' : 'bg-gray-100'}`}
          >
            Pre-defined Schemes
          </button>
          <button
            onClick={() => setIsCustom(true)}
            className={`px-4 py-2 rounded-full ${isCustom ? 'bg-primary text-white' : 'bg-gray-100'}`}
          >
            Custom Colors
          </button>
        </div>
      </div>

      {!isCustom ? (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {predefinedSchemes.map((scheme) => (
            <motion.div
              key={scheme.name}
              className={`p-6 rounded-xl border-2 cursor-pointer transition-all ${
                selectedScheme?.name === scheme.name ? 'border-primary' : 'border-gray-200'
              }`}
              onClick={() => setSelectedScheme(scheme)}
              whileHover={{ scale: 1.02 }}
            >
              <h4 className="font-semibold mb-2">{scheme.name}</h4>
              <p className="text-sm text-gray-600 mb-4">{scheme.description}</p>
              <div className="flex gap-2">
                {scheme.colors.map((color) => (
                  <div
                    key={color}
                    className="w-8 h-8 rounded-full border border-gray-200"
                    style={{ backgroundColor: color }}
                  />
                ))}
              </div>
            </motion.div>
          ))}
        </div>
      ) : (
        <div className="space-y-4">
          {customColors.map((color, index) => (
            <div key={index} className="flex items-center gap-4">
              <input
                type="color"
                value={color}
                onChange={(e) => handleColorChange(index, e.target.value)}
                className="w-12 h-12 rounded cursor-pointer"
              />
              <input
                type="text"
                value={color}
                onChange={(e) => handleColorChange(index, e.target.value)}
                className="px-4 py-2 border rounded"
                placeholder="Color hex code"
              />
            </div>
          ))}
          <button
            onClick={handleAddColor}
            className="px-4 py-2 text-primary hover:text-primary-dark"
          >
            + Add Another Color
          </button>
        </div>
      )}

      <div className="mt-8">
        <button
          onClick={handleSubmit}
          className="px-6 py-3 bg-primary text-white rounded-full hover:bg-primary-dark transition-colors"
        >
          Submit Color Selection
        </button>
      </div>
    </div>
  );
} 