'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import WebDevelopmentPortfolio from '@/components/WebDevelopmentPortfolio';
import { SparklesIcon, ArrowRightIcon, ArrowDownIcon } from '@heroicons/react/24/outline';
import { motion, HTMLMotionProps } from 'framer-motion';

// Types for our technologies section
type TechStack = {
  id: string;
  category: string;
  icon: string;
  description: string;
  items: {
    name: string;
    icon: string;
    level: 'Basic' | 'Advanced' | 'Expert';
    experience: string;
  }[];
};

// Technologies data with updated styling
const technologies: TechStack[] = [
  {
    id: 'frontend',
    category: 'Frontend',
    icon: 'fas fa-laptop-code',
    description: 'Modern and responsive user interfaces',
    items: [
      {
        name: 'React/Next.js',
        icon: 'fab fa-react',
        level: 'Expert',
        experience: '5+ years'
      },
      {
        name: 'TypeScript',
        icon: 'fas fa-code',
        level: 'Expert',
        experience: '4+ years'
      },
      {
        name: 'Tailwind CSS',
        icon: 'fas fa-paint-brush',
        level: 'Expert',
        experience: '3+ years'
      },
      {
        name: 'Vue.js',
        icon: 'fab fa-vuejs',
        level: 'Advanced',
        experience: '3+ years'
      }
    ]
  },
  {
    id: 'backend',
    category: 'Backend',
    icon: 'fas fa-server',
    description: 'Scalable and secure server solutions',
    items: [
      {
        name: 'Node.js',
        icon: 'fab fa-node-js',
        level: 'Expert',
        experience: '5+ years'
      },
      {
        name: 'Python',
        icon: 'fab fa-python',
        level: 'Advanced',
        experience: '4+ years'
      },
      {
        name: 'PHP/Laravel',
        icon: 'fab fa-php',
        level: 'Expert',
        experience: '5+ years'
      },
      {
        name: 'Express',
        icon: 'fas fa-server',
        level: 'Expert',
        experience: '4+ years'
      }
    ]
  },
  {
    id: 'database',
    category: 'Database',
    icon: 'fas fa-database',
    description: 'Reliable data storage solutions',
    items: [
      {
        name: 'MySQL',
        icon: 'fas fa-database',
        level: 'Expert',
        experience: '6+ years'
      },
      {
        name: 'PostgreSQL',
        icon: 'fas fa-database',
        level: 'Advanced',
        experience: '4+ years'
      },
      {
        name: 'MongoDB',
        icon: 'fas fa-leaf',
        level: 'Expert',
        experience: '4+ years'
      },
      {
        name: 'Redis',
        icon: 'fas fa-bolt',
        level: 'Advanced',
        experience: '3+ years'
      }
    ]
  },
  {
    id: 'cms',
    category: 'CMS',
    icon: 'fas fa-file-code',
    description: 'Content management solutions',
    items: [
      {
        name: 'WordPress',
        icon: 'fab fa-wordpress',
        level: 'Expert',
        experience: '7+ years'
      },
      {
        name: 'Strapi',
        icon: 'fas fa-box',
        level: 'Advanced',
        experience: '3+ years'
      },
      {
        name: 'Sanity',
        icon: 'fas fa-cube',
        level: 'Advanced',
        experience: '2+ years'
      },
      {
        name: 'Contentful',
        icon: 'fas fa-feather',
        level: 'Advanced',
        experience: '3+ years'
      }
    ]
  }
];

// Motion component variants
const fadeInUp = {
  initial: { opacity: 0, y: 20 },
  whileInView: { opacity: 1, y: 0 },
  transition: { duration: 0.6 },
  viewport: { once: true }
};

const fadeInUpDelay = (delay: number) => ({
  initial: { opacity: 0, y: 20 },
  whileInView: { opacity: 1, y: 0 },
  transition: { duration: 0.6, delay },
  viewport: { once: true }
});

// Create motion components with proper types
type MotionDivProps = HTMLMotionProps<"div">;
type MotionH2Props = HTMLMotionProps<"h2">;
type MotionPProps = HTMLMotionProps<"p">;

const MotionDiv = (props: MotionDivProps) => <motion.div {...props} />;
const MotionH2 = (props: MotionH2Props) => <motion.h2 {...props} />;
const MotionP = (props: MotionPProps) => <motion.p {...props} />;

export default function WebDevelopment() {
  const [activeIndex, setActiveIndex] = useState<number>(0);
  const [showRequestForm, setShowRequestForm] = useState<boolean>(false);
  const [activeFaq, setActiveFaq] = useState<number | null>(null);
  const [mounted, setMounted] = useState<boolean>(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const toggleFaq = (index: number) => {
    setActiveFaq(activeFaq === index ? null : index);
  };

  if (!mounted) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-slate-50 via-white via-gray-50/30 to-orange-50/20 pt-16">
      {/* Revolutionary Hero Section */}
      <div className="relative min-h-[60vh] sm:min-h-[70vh] lg:min-h-[80vh] bg-gradient-to-b from-slate-50 via-white to-gray-50/30 overflow-hidden flex items-center">
        {/* Dynamic Background Pattern */}
        <div className="absolute inset-0 opacity-[0.03]">
          <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
            <defs>
              <pattern id="web-dev-grid" width="15" height="15" patternUnits="userSpaceOnUse">
                <path d="M 15 0 L 0 0 0 15" fill="none" stroke="#FF5400" strokeWidth="0.3"/>
                <circle cx="7.5" cy="7.5" r="0.5" fill="#FF5400" opacity="0.1"/>
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#web-dev-grid)" />
          </svg>
        </div>

        {/* Floating Elements */}
        <div className="absolute top-20 right-10 w-32 h-32 bg-gradient-to-br from-blue-200/40 to-purple-200/40 rounded-full blur-2xl"></div>
        <div className="absolute bottom-20 left-10 w-40 h-40 bg-gradient-to-br from-orange-200/40 to-red-200/40 rounded-full blur-3xl"></div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 sm:py-16 lg:py-20">
          <div className="text-center space-y-8 sm:space-y-10 lg:space-y-12">
            {/* Premium Badge with Animation */}
            <div className="inline-flex items-center gap-2 sm:gap-3 bg-white/90 backdrop-blur-xl text-[#FF5400] px-4 sm:px-6 lg:px-8 py-3 sm:py-4 rounded-full text-xs sm:text-sm font-bold border border-orange-200/60 shadow-2xl hover:shadow-3xl transition-all duration-500 hover:scale-105 group">
              <SparklesIcon className="h-4 w-4 sm:h-5 sm:w-5 group-hover:rotate-12 transition-transform duration-300" />
              <span className="bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">
                WEB DEVELOPMENT SERVICES
              </span>
            </div>

            {/* Revolutionary Title */}
            <div className="space-y-4 sm:space-y-6">
              <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-black text-gray-900 leading-[0.9] tracking-tight px-2">
                Crafting{' '}
                <span className="relative inline-block">
                  <span className="bg-gradient-to-r from-[#FF5400] via-orange-500 to-red-500 bg-clip-text text-transparent">
                    Digital
                  </span>
                  <div className="absolute -bottom-1 sm:-bottom-2 left-0 right-0 h-0.5 sm:h-1 bg-gradient-to-r from-orange-400 to-red-400 rounded-full opacity-30"></div>
                </span>
              </h1>
              <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-black text-gray-800 leading-tight px-2">
                Experiences & Solutions
              </h2>
            </div>

            <p className="text-base sm:text-lg md:text-xl lg:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed font-light px-4">
              From simple landing pages to complex web applications, we build 
              <span className="font-semibold text-gray-800"> digital experiences that drive growth</span>
            </p>

            {/* CTA Buttons with Enhanced Styling */}
            <div className="flex flex-col sm:flex-row gap-5 justify-center">
              <a
                href="https://wa.me/254741590670?text=Hello%20Mocky%20Digital!%20I'm%20interested%20in%20web%20development%20services."
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center gap-2 bg-[#FF5400] hover:bg-[#FF5400]/90 text-white px-8 py-4 rounded-full font-medium text-center transition-all duration-500 hover:scale-105 shadow-xl hover:shadow-2xl group"
              >
                <span>Start Your Project</span>
                <ArrowRightIcon className="h-5 w-5 group-hover:translate-x-1 transition-transform" />
              </a>
              <Link
                href="#portfolio"
                className="inline-flex items-center gap-2 border-2 border-gray-200 hover:border-[#FF5400]/30 text-gray-800 px-8 py-4 rounded-full font-medium text-center transition-all duration-500 hover:scale-105 group"
              >
                <span>View Portfolio</span>
                <ArrowDownIcon className="h-5 w-5 group-hover:translate-y-1 transition-transform" />
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Services Overview */}
      <section className="py-20 bg-gradient-to-br from-gray-50 to-white relative overflow-hidden">
        {/* Background decoration */}
        <div className="absolute top-0 right-0 w-96 h-96 bg-[#FF5400]/5 rounded-full blur-3xl"></div>
        <div className="absolute bottom-0 left-0 w-96 h-96 bg-[#0A1929]/5 rounded-full blur-3xl"></div>
        
        <div className="container mx-auto px-6 relative z-10">
          <div className="text-center mb-20">
            <MotionDiv {...fadeInUp} className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-[#FF5400]/10 border border-[#FF5400]/20 mb-6">
              <i className="fas fa-code text-[#FF5400]"></i>
              <span className="text-sm font-medium text-[#FF5400]">Our Expertise</span>
            </MotionDiv>
            
            <MotionH2 {...fadeInUpDelay(0.1)} className="text-3xl md:text-4xl font-bold text-[#0A1929] mb-6">
              What We <span className="text-[#FF5400]">Build</span>
            </MotionH2>
            
            <MotionP {...fadeInUpDelay(0.2)} className="text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed">
              From concept to launch, we create digital solutions that make an impact. 
              Our expertise spans across various web technologies to bring your vision to life.
            </MotionP>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
            {[
              {
                icon: 'fas fa-globe',
                title: 'Business Websites',
                description: 'Professional websites that establish your online presence and build credibility with your audience.',
                color: 'from-[#0A1929] to-[#1a2332]',
                bgColor: 'bg-blue-50',
                features: ['Responsive Design', 'SEO Optimized', 'Fast Loading']
              },
              {
                icon: 'fas fa-shopping-cart',
                title: 'E-commerce Stores',
                description: 'Complete online stores with payment integration, inventory management, and order processing.',
                color: 'from-[#FF5400] to-[#FF7A00]',
                bgColor: 'bg-green-50',
                features: ['Payment Gateway', 'Inventory System', 'Order Management']
              },
              {
                icon: 'fas fa-mobile-alt',
                title: 'Web Applications',
                description: 'Custom web applications tailored to your business processes and user requirements.',
                color: 'from-[#0A1929] to-[#1a2332]',
                bgColor: 'bg-purple-50',
                features: ['Custom Features', 'User Authentication', 'Real-time Updates']
              },
              {
                icon: 'fas fa-chart-line',
                title: 'Landing Pages',
                description: 'High-converting landing pages designed to capture leads and drive specific actions.',
                color: 'from-[#FF5400] to-[#FF7A00]',
                bgColor: 'bg-orange-50',
                features: ['Lead Capture', 'A/B Testing', 'Conversion Tracking']
              },
              {
                icon: 'fas fa-blog',
                title: 'Content Management',
                description: 'Easy-to-manage websites with powerful CMS solutions for content updates.',
                color: 'from-[#0A1929] to-[#1a2332]',
                bgColor: 'bg-indigo-50',
                features: ['Easy Updates', 'Media Library', 'User Roles']
              },
              {
                icon: 'fas fa-rocket',
                title: 'Performance Optimization',
                description: 'Fast-loading, SEO-optimized websites that rank well and provide excellent user experience.',
                color: 'from-[#FF5400] to-[#FF7A00]',
                bgColor: 'bg-red-50',
                features: ['Speed Optimization', 'SEO Ready', 'Analytics']
              }
            ].map((service, index) => (
              <MotionDiv
                key={index}
                {...fadeInUpDelay(0.1 + index * 0.1)}
                className="group relative"
              >
                <div className="bg-white rounded-3xl p-8 border border-gray-100 hover:border-[#FF5400]/30 hover:shadow-2xl transition-all duration-500 h-full relative overflow-hidden">
                  {/* Gradient background on hover */}
                  <div className="absolute inset-0 bg-gradient-to-br from-[#FF5400]/5 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>
                  
                  {/* Icon with gradient background */}
                  <div className="relative mb-6">
                    <div className={`w-12 h-12 rounded-xl bg-gradient-to-br ${service.color} flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg`}>
                      <i className={`${service.icon} text-white text-lg`}></i>
                    </div>
                    <div className="absolute -top-2 -right-2 w-6 h-6 bg-[#FF5400] rounded-full opacity-0 group-hover:opacity-100 transition-all duration-300 animate-pulse"></div>
                  </div>
                  
                  <div className="relative">
                    <h3 className="text-xl font-bold text-[#0A1929] mb-4 group-hover:text-[#FF5400] transition-colors duration-300">
                      {service.title}
                    </h3>
                    <p className="text-gray-600 leading-relaxed mb-6">
                      {service.description}
                    </p>
                    
                    {/* Feature tags */}
                    <div className="flex flex-wrap gap-2">
                      {service.features.map((feature, idx) => (
                        <span 
                          key={idx}
                          className="px-3 py-1 text-xs font-medium bg-gray-100 text-gray-700 rounded-full group-hover:bg-[#FF5400]/10 group-hover:text-[#FF5400] transition-all duration-300"
                        >
                          {feature}
                        </span>
                      ))}
                    </div>
                  </div>
                  
                  {/* Hover arrow */}
                  <div className="absolute bottom-6 right-6 w-8 h-8 bg-[#FF5400] rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transform translate-x-4 group-hover:translate-x-0 transition-all duration-300">
                    <i className="fas fa-arrow-right text-white text-sm"></i>
                  </div>
                </div>
              </MotionDiv>
            ))}
          </div>

          {/* Bottom CTA */}
          <MotionDiv {...fadeInUpDelay(0.8)} className="text-center mt-16">
            <div className="bg-white rounded-2xl p-4 md:p-6 shadow-lg border border-gray-100 max-w-2xl mx-auto">
              {/* Mobile Layout */}
              <div className="flex flex-col sm:hidden gap-4">
                <div className="flex items-center justify-center gap-4">
                  <div className="flex -space-x-2">
                    <div className="w-8 h-8 bg-gradient-to-br from-[#FF5400] to-[#FF7A00] rounded-full flex items-center justify-center">
                      <i className="fas fa-code text-white text-xs"></i>
                    </div>
                    <div className="w-8 h-8 bg-gradient-to-br from-[#0A1929] to-[#1a2332] rounded-full flex items-center justify-center">
                      <i className="fas fa-palette text-white text-xs"></i>
                    </div>
                    <div className="w-8 h-8 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center">
                      <i className="fas fa-rocket text-white text-xs"></i>
                    </div>
                  </div>
                  <div className="text-center">
                    <p className="text-sm font-medium text-[#0A1929]">Ready to start your project?</p>
                    <p className="text-xs text-gray-600">Let's discuss your requirements</p>
                  </div>
                </div>
                <a
                  href="https://wa.me/254741590670?text=Hello%20Mocky%20Digital!%20I'm%20interested%20in%20discussing%20my%20web%20development%20project."
                  target="_blank"
                  rel="noopener noreferrer"
                  className="bg-[#FF5400] hover:bg-[#FF5400]/90 text-white px-6 py-3 rounded-xl font-medium transition-colors flex items-center justify-center gap-2 w-full"
                >
                  <i className="fas fa-paper-plane"></i>
                  Get Started
                </a>
              </div>

              {/* Desktop Layout */}
              <div className="hidden sm:flex items-center gap-4">
                <div className="flex -space-x-2">
                  <div className="w-10 h-10 bg-gradient-to-br from-[#FF5400] to-[#FF7A00] rounded-full flex items-center justify-center">
                    <i className="fas fa-code text-white text-sm"></i>
                  </div>
                  <div className="w-10 h-10 bg-gradient-to-br from-[#0A1929] to-[#1a2332] rounded-full flex items-center justify-center">
                    <i className="fas fa-palette text-white text-sm"></i>
                  </div>
                  <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center">
                    <i className="fas fa-rocket text-white text-sm"></i>
                  </div>
                </div>
                <div className="text-left flex-1">
                  <p className="text-sm font-medium text-[#0A1929]">Ready to start your project?</p>
                  <p className="text-xs text-gray-600">Let's discuss your requirements</p>
                </div>
                <a
                  href="https://wa.me/254741590670?text=Hello%20Mocky%20Digital!%20I'm%20interested%20in%20discussing%20my%20web%20development%20project."
                  target="_blank"
                  rel="noopener noreferrer"
                  className="bg-[#FF5400] hover:bg-[#FF5400]/90 text-white px-6 py-3 rounded-xl font-medium transition-colors flex items-center gap-2 flex-shrink-0"
                >
                  <i className="fas fa-paper-plane"></i>
                  Get Started
                </a>
              </div>
            </div>
          </MotionDiv>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="py-24 bg-gradient-to-b from-gray-50 to-white relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-[0.03]">
          <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
            <defs>
              <pattern id="pricing-grid" width="15" height="15" patternUnits="userSpaceOnUse">
                <path d="M 15 0 L 0 0 0 15" fill="none" stroke="#FF5400" strokeWidth="0.3"/>
                <circle cx="7.5" cy="7.5" r="0.5" fill="#FF5400" opacity="0.1"/>
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#pricing-grid)" />
          </svg>
        </div>

        {/* Floating Elements */}
        <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-br from-purple-200/40 to-pink-200/40 rounded-full blur-2xl"></div>
        <div className="absolute bottom-20 right-10 w-40 h-40 bg-gradient-to-br from-orange-200/40 to-red-200/40 rounded-full blur-3xl"></div>

        <div className="container mx-auto px-6 relative z-10">
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-[#FF5400]/10 border border-[#FF5400]/20 mb-6">
              <i className="fas fa-tags text-[#FF5400]"></i>
              <span className="text-sm font-medium text-[#FF5400]">Pricing</span>
            </div>
            
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-black text-gray-900 mb-6">
              Transparent <span className="text-[#FF5400]">Pricing</span>
            </h2>
            
            <p className="text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed mb-8">
              Choose the perfect package for your business needs
            </p>

            {/* Important Note */}
            <div className="bg-orange-50 border border-orange-100 rounded-2xl p-6 max-w-3xl mx-auto">
              <div className="flex items-center justify-center gap-2 mb-3">
                <i className="fas fa-info-circle text-[#FF5400] text-xl"></i>
                <span className="font-semibold text-[#FF5400]">Important Note</span>
              </div>
              <p className="text-gray-700 leading-relaxed">
                The prices shown are <span className="font-semibold">estimated ranges</span> and not fixed prices. Each project is unique and will be <span className="font-semibold">individually quoted</span> based on your specific requirements, complexity, and additional features needed. Contact us for a personalized quote tailored to your project.
              </p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-7xl mx-auto">
            {[
              {
                title: "Basic Website",
                price: "20,000",
                priceRange: "35,000",
                features: [
                  "Essential Pages (Home, About, Services, Contact)",
                  "Domain Registration (.com/.co.ke)",
                  "1 Year Web Hosting",
                  "SSL Certificate",
                  "Mobile Responsive Design",
                  "Basic SEO Setup"
                ]
              },
              {
                title: "Business Website",
                price: "35,000",
                priceRange: "75,000",
                features: [
                  "Up to 8 Custom Pages",
                  "Premium Domain & Hosting (1 Year)",
                  "Advanced Functionalities",
                  "Blog Integration",
                  "Advanced SEO Optimization",
                  "Social Media Integration"
                ],
                popular: true
              },
              {
                title: "E-commerce",
                price: "75,000",
                priceRange: "150,000",
                features: [
                  "Full E-commerce Website",
                  "Premium Domain & Hosting (1 Year)",
                  "Product Catalog (Up to 100 Products)",
                  "Payment Gateway Integration",
                  "Order Management System",
                  "Inventory Management"
                ]
              },
              {
                title: "Premium Custom",
                price: "150,000",
                priceRange: "+",
                features: [
                  "Custom Design & Features",
                  "Premium Domain & Hosting (1 Year)",
                  "Advanced Integrations",
                  "Custom Plugins Development",
                  "Unlimited Pages & Products",
                  "Priority Support"
                ]
              }
            ].map((plan, index) => {
              const whatsappMessage = `Hello! I'm interested in the ${plan.title} package (KSH ${plan.price}${plan.priceRange ? ` - ${plan.priceRange}` : ''}).

Features included:
${plan.features.map(feature => `✓ ${feature}`).join('\n')}

Please provide more information about this package.`;

              const whatsappUrl = `https://wa.me/254741590670?text=${encodeURIComponent(whatsappMessage)}`;

              return (
                <MotionDiv
                  key={index}
                  {...fadeInUpDelay(0.1 + index * 0.1)}
                  className={`group relative bg-white rounded-3xl border-2 ${
                    plan.popular 
                      ? 'border-[#FF5400] shadow-lg shadow-[#FF5400]/10' 
                      : 'border-gray-100 hover:border-[#FF5400]/30'
                  } hover:shadow-2xl transition-all duration-500 overflow-hidden`}
                >
                  {plan.popular && (
                    <div className="absolute -top-px -right-px">
                      <div className="bg-[#FF5400] text-white text-xs font-medium px-4 py-2 rounded-bl-2xl rounded-tr-2xl flex items-center gap-1">
                        <i className="fas fa-star text-xs"></i>
                        Most Popular
                      </div>
                    </div>
                  )}

                  <div className="p-8">
                    {/* Header */}
                    <div className="mb-8">
                      <div className="w-12 h-12 bg-gradient-to-br from-[#FF5400] to-[#FF7A00] rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <i className={`fas fa-${
                          plan.title === "Basic Website" ? "globe" :
                          plan.title === "Business Website" ? "briefcase" :
                          plan.title === "E-commerce" ? "shopping-cart" :
                          "crown"
                        } text-white text-xl`}></i>
                      </div>
                      <h3 className="text-xl font-bold text-[#0A1929] group-hover:text-[#FF5400] transition-colors duration-300 mb-2">
                        {plan.title}
                      </h3>
                      <div className="flex items-baseline gap-1 mb-2">
                        <span className="text-sm font-medium text-gray-500">KSH</span>
                        <span className="text-3xl font-bold text-[#FF5400]">{plan.price}</span>
                        {plan.priceRange && (
                          <>
                            <span className="text-lg font-medium text-gray-400 mx-1">-</span>
                            <span className="text-xl font-bold text-[#FF5400]">{plan.priceRange}</span>
                          </>
                        )}
                      </div>
                    </div>

                    {/* Features */}
                    <ul className="space-y-4 mb-8">
                      {plan.features.map((feature, idx) => (
                        <li key={idx} className="flex items-start gap-3">
                          <div className="w-5 h-5 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5 group-hover:scale-110 transition-transform duration-300">
                            <i className="fas fa-check text-green-600 text-xs"></i>
                          </div>
                          <span className="text-gray-600 text-sm leading-relaxed">{feature}</span>
                        </li>
                      ))}
                    </ul>

                    {/* Action Button */}
                    <a
                      href={whatsappUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className={`w-full flex items-center justify-center gap-2 py-3 px-6 rounded-xl font-medium transition-all duration-300 hover:scale-105 ${
                        plan.popular
                          ? 'bg-[#FF5400] text-white hover:bg-[#FF5400]/90'
                          : 'bg-[#0A1929] text-white hover:bg-[#0A1929]/90'
                      }`}
                    >
                                      <i className="fas fa-paper-plane"></i>
                Get Started
                    </a>
                  </div>
                </MotionDiv>
              );
            })}
          </div>
        </div>
      </section>

      {/* Server Solutions CTA */}
      <section className="relative py-12 md:py-24 bg-gradient-to-b from-gray-900 to-gray-800 overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-[0.03]">
          <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
            <defs>
              <pattern id="server-grid" width="15" height="15" patternUnits="userSpaceOnUse">
                <path d="M 15 0 L 0 0 0 15" fill="none" stroke="#FF5400" strokeWidth="0.3"/>
                <circle cx="7.5" cy="7.5" r="0.5" fill="#FF5400" opacity="0.1"/>
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#server-grid)" />
          </svg>
        </div>

        {/* Floating Elements */}
        <motion.div
          className="absolute top-20 left-10 w-24 h-24 md:w-32 md:h-32 bg-gradient-to-br from-primary/30 to-primary/10 rounded-full blur-xl"
          animate={{
            scale: [1, 1.2, 1],
            rotate: [0, 90, 0],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "linear"
          }}
        />

        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center justify-between gap-8 md:gap-12 bg-gray-800/50 backdrop-blur-lg rounded-3xl p-6 md:p-12 shadow-xl border border-gray-700/50 relative z-10">
            <div className="flex items-center gap-4 md:gap-6">
              <div className="flex shrink-0">
                <span className="flex items-center justify-center w-12 h-12 md:w-16 md:h-16 bg-gradient-to-br from-primary to-primary/80 rounded-2xl shadow-lg">
                  <i className="fas fa-server text-xl md:text-2xl text-white"></i>
                </span>
                <span className="flex items-center justify-center w-12 h-12 md:w-16 md:h-16 bg-gradient-to-br from-blue-600 to-blue-500 rounded-2xl shadow-lg -ml-6">
                  <i className="fas fa-cloud text-xl md:text-2xl text-white"></i>
                </span>
                <span className="flex items-center justify-center w-12 h-12 md:w-16 md:h-16 bg-gradient-to-br from-green-600 to-green-500 rounded-2xl shadow-lg -ml-6">
                  <i className="fas fa-code-branch text-xl md:text-2xl text-white"></i>
                </span>
              </div>
              <div className="flex-1">
                <h3 className="text-lg md:text-2xl font-bold text-white mb-1 md:mb-2">Need Professional Server Setup?</h3>
                <p className="text-sm md:text-base text-gray-300">VPS configuration, cloud infrastructure & DevOps solutions</p>
              </div>
            </div>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="w-full md:w-auto px-6 py-3 md:py-4 bg-gradient-to-r from-primary to-primary/90 text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center gap-2 text-base md:text-lg font-semibold group"
            >
              <i className="fas fa-paper-plane group-hover:rotate-12 transition-transform duration-300"></i>
              Get Server Help
            </motion.button>
          </div>
        </div>
      </section>

      {/* Domain & Hosting CTA */}
      <section className="relative py-12 md:py-24 bg-gradient-to-b from-gray-50 to-white overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-[0.03]">
          <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
            <defs>
              <pattern id="domain-grid" width="15" height="15" patternUnits="userSpaceOnUse">
                <path d="M 15 0 L 0 0 0 15" fill="none" stroke="#FF5400" strokeWidth="0.3"/>
                <circle cx="7.5" cy="7.5" r="0.5" fill="#FF5400" opacity="0.1"/>
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#domain-grid)" />
          </svg>
        </div>

        {/* Floating Elements */}
        <motion.div
          className="absolute top-20 right-10 w-24 h-24 md:w-32 md:h-32 bg-gradient-to-br from-primary/30 to-primary/10 rounded-full blur-xl"
          animate={{
            scale: [1, 1.2, 1],
            rotate: [0, -90, 0],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "linear"
          }}
        />

        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center justify-between gap-8 md:gap-12 bg-white rounded-3xl p-6 md:p-12 shadow-xl relative z-10">
            <div className="flex items-center gap-4 md:gap-6">
              <div className="flex shrink-0">
                <span className="flex items-center justify-center w-12 h-12 md:w-16 md:h-16 bg-gradient-to-br from-primary to-primary/80 rounded-2xl shadow-lg">
                  <i className="fas fa-globe text-xl md:text-2xl text-white"></i>
                </span>
                <span className="flex items-center justify-center w-12 h-12 md:w-16 md:h-16 bg-gradient-to-br from-blue-600 to-blue-500 rounded-2xl shadow-lg -ml-6">
                  <i className="fas fa-server text-xl md:text-2xl text-white"></i>
                </span>
              </div>
              <div className="flex-1">
                <h3 className="text-lg md:text-2xl font-bold text-gray-900 mb-1 md:mb-2">Need Domain & Hosting?</h3>
                <p className="text-sm md:text-base text-gray-600">Get your website online with our professional setup</p>
              </div>
            </div>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="w-full md:w-auto px-6 py-3 md:py-4 bg-gradient-to-r from-primary to-primary/90 text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center gap-2 text-base md:text-lg font-semibold group"
            >
              <i className="fas fa-paper-plane group-hover:rotate-12 transition-transform duration-300"></i>
              Get Started
            </motion.button>
          </div>
        </div>
      </section>

      {/* Technologies Section */}
      <section className="py-24 bg-gradient-to-b from-gray-50 to-white relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-[0.03]">
          <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
            <defs>
              <pattern id="tech-grid" width="15" height="15" patternUnits="userSpaceOnUse">
                <path d="M 15 0 L 0 0 0 15" fill="none" stroke="#FF5400" strokeWidth="0.3"/>
                <circle cx="7.5" cy="7.5" r="0.5" fill="#FF5400" opacity="0.1"/>
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#tech-grid)" />
          </svg>
        </div>

        {/* Floating Elements */}
        <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-br from-purple-200/40 to-pink-200/40 rounded-full blur-2xl"></div>
        <div className="absolute bottom-20 right-10 w-40 h-40 bg-gradient-to-br from-orange-200/40 to-red-200/40 rounded-full blur-3xl"></div>

        <div className="container mx-auto px-6 relative z-10">
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-[#FF5400]/10 border border-[#FF5400]/20 mb-6">
              <i className="fas fa-code text-[#FF5400]"></i>
              <span className="text-sm font-medium text-[#FF5400]">Tech Stack</span>
            </div>
            
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-black text-gray-900 mb-6">
              Technologies We <span className="text-[#FF5400]">Use</span>
            </h2>
            
            <p className="text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Modern tools and frameworks to build robust, scalable web solutions. Our expertise spans across the entire development stack.
            </p>
          </div>

          {/* Tech Stack Grid */}
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {technologies.map((tech, index) => (
              <MotionDiv
                key={tech.id}
                {...fadeInUpDelay(0.15 + index * 0.15)}
                className="group relative"
              >
                <div className="bg-white rounded-3xl p-8 border border-gray-100 hover:border-[#FF5400]/30 hover:shadow-2xl transition-all duration-500 h-full relative overflow-hidden">
                  {/* Gradient background on hover */}
                  <div className="absolute inset-0 bg-gradient-to-br from-[#0A1929]/5 to-[#FF5400]/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-3xl"></div>
                  
                  {/* Header with icon */}
                  <div className="relative mb-8">
                    <div className="flex items-center gap-4 mb-6">
                      <div className="w-16 h-16 bg-gradient-to-br from-[#FF5400] to-[#FF7A00] rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-lg">
                        <i className={`${tech.icon} text-white text-2xl`}></i>
                      </div>
                      <div>
                        <h3 className="text-xl font-bold text-[#0A1929] group-hover:text-[#FF5400] transition-colors duration-300">
                          {tech.category}
                        </h3>
                        <div className="w-12 h-1 bg-gradient-to-r from-[#FF5400] to-[#FF7A00] rounded-full mt-2 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300"></div>
                      </div>
                    </div>
                    <p className="text-gray-600 text-sm leading-relaxed">
                      {tech.description}
                    </p>
                  </div>
                  
                  {/* Technologies list */}
                  <div className="relative space-y-4">
                    {tech.items.map((item, idx) => (
                      <motion.div 
                        key={idx}
                        initial={{ opacity: 0, x: -20 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        transition={{ duration: 0.4, delay: (index * 0.15) + (idx * 0.1) }}
                        viewport={{ once: true }}
                        className="flex items-center justify-between p-3 rounded-xl bg-gray-50 group-hover:bg-white transition-colors duration-300 border border-transparent group-hover:border-[#FF5400]/20"
                      >
                        <div className="flex items-center gap-3">
                          <div className="w-8 h-8 bg-white rounded-lg flex items-center justify-center shadow-sm group-hover:shadow-md transition-shadow duration-300">
                            <i className={`${item.icon} text-[#FF5400] text-sm`}></i>
                          </div>
                          <span className="text-sm font-medium text-gray-800 group-hover:text-[#0A1929] transition-colors duration-300">
                            {item.name}
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <span className={`text-xs px-3 py-1.5 rounded-full font-medium transition-all duration-300 ${
                            item.level === 'Expert' 
                              ? 'bg-green-100 text-green-700 group-hover:bg-green-500 group-hover:text-white'
                              : item.level === 'Advanced'
                              ? 'bg-blue-100 text-blue-700 group-hover:bg-blue-500 group-hover:text-white'
                              : 'bg-gray-100 text-gray-700 group-hover:bg-gray-500 group-hover:text-white'
                          }`}>
                            {item.level}
                          </span>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                  
                  {/* Experience indicator */}
                  <div className="absolute top-6 right-6 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <div className="w-8 h-8 bg-[#FF5400] rounded-full flex items-center justify-center">
                      <i className="fas fa-star text-white text-sm"></i>
                    </div>
                  </div>
                </div>
              </MotionDiv>
            ))}
          </div>
        </div>
      </section>

      {/* Portfolio Section */}
      <section id="portfolio" className="py-20 bg-gradient-to-b from-gray-50 to-white relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-[0.03]">
          <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
            <defs>
              <pattern id="portfolio-grid" width="15" height="15" patternUnits="userSpaceOnUse">
                <path d="M 15 0 L 0 0 0 15" fill="none" stroke="#FF5400" strokeWidth="0.3"/>
                <circle cx="7.5" cy="7.5" r="0.5" fill="#FF5400" opacity="0.1"/>
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#portfolio-grid)" />
          </svg>
        </div>

        {/* Floating Elements */}
        <div className="absolute top-20 left-10 w-32 h-32 bg-gradient-to-br from-purple-200/40 to-pink-200/40 rounded-full blur-2xl"></div>
        <div className="absolute bottom-20 right-10 w-40 h-40 bg-gradient-to-br from-orange-200/40 to-red-200/40 rounded-full blur-3xl"></div>

        <WebDevelopmentPortfolio />
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-gradient-to-b from-white to-gray-50 relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-[0.03]">
          <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
            <defs>
              <pattern id="faq-grid" width="15" height="15" patternUnits="userSpaceOnUse">
                <path d="M 15 0 L 0 0 0 15" fill="none" stroke="#FF5400" strokeWidth="0.3"/>
                <circle cx="7.5" cy="7.5" r="0.5" fill="#FF5400" opacity="0.1"/>
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#faq-grid)" />
          </svg>
        </div>

        {/* Floating Elements */}
        <div className="absolute top-20 right-10 w-32 h-32 bg-gradient-to-br from-blue-200/40 to-indigo-200/40 rounded-full blur-2xl"></div>
        <div className="absolute bottom-20 left-10 w-40 h-40 bg-gradient-to-br from-orange-200/40 to-red-200/40 rounded-full blur-3xl"></div>

        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-[#FF5400]/10 border border-[#FF5400]/20 mb-6">
              <i className="fas fa-question-circle text-[#FF5400]"></i>
              <span className="text-sm font-medium text-[#FF5400]">FAQ</span>
            </div>
            
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-black text-gray-900 mb-6">
              Frequently Asked <span className="text-[#FF5400]">Questions</span>
            </h2>
            
            <p className="text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Common questions about our web development services
            </p>
          </div>

          <div className="max-w-7xl mx-auto">
            <div className="grid md:grid-cols-2 gap-6">
              {[
                {
                  question: "How long does it take to build a website?",
                  answer: "Timeline varies based on complexity. A basic website takes 1-2 weeks, business websites 2-4 weeks, and e-commerce sites 4-8 weeks. We'll provide a detailed timeline during consultation."
                },
                {
                  question: "What's the difference between shared hosting and VPS?",
                  answer: "Shared hosting means your website shares server resources with other sites, while VPS (Virtual Private Server) gives you dedicated resources and better performance. VPS is ideal for high-traffic sites, e-commerce stores, or applications requiring specific server configurations."
                },
                {
                  question: "Do you provide VPS server setup and management?",
                  answer: "Yes, we offer complete VPS server setup including Ubuntu/CentOS installation, security hardening, performance optimization, and ongoing server management. We handle everything from initial configuration to regular maintenance and monitoring."
                },
                {
                  question: "Which cloud platforms do you work with?",
                  answer: "We work with major cloud providers including AWS, Google Cloud Platform, DigitalOcean, and Linode. We'll recommend the best platform based on your specific needs, budget, and technical requirements."
                },
                {
                  question: "Do you provide ongoing maintenance?",
                  answer: "Yes, we offer maintenance packages including security updates, content updates, backups, server monitoring, and 24/7 technical support to keep your website and server running smoothly."
                },
                {
                  question: "Will my website be mobile-friendly?",
                  answer: "Absolutely! All our websites are built with responsive design, ensuring they look and work perfectly on all devices - desktop, tablet, and mobile."
                },
                {
                  question: "Can you help with database optimization and management?",
                  answer: "Yes, we provide comprehensive database services including MySQL/PostgreSQL setup, performance optimization, automated backups, and database security configuration. We ensure your data is secure and your queries run efficiently."
                },
                {
                  question: "Do you offer DevOps and CI/CD pipeline setup?",
                  answer: "Absolutely! We set up complete CI/CD pipelines using tools like GitHub Actions, Docker containers, and automated deployment systems. This streamlines your development process and ensures reliable, consistent deployments."
                },
                {
                  question: "What security measures do you implement?",
                  answer: "We implement comprehensive security including SSL certificates, firewall configuration, regular security updates, malware scanning, DDoS protection, and continuous monitoring. For VPS setups, we also include server hardening and intrusion detection."
                }
              ].map((faq, index) => (
                <div
                  key={index}
                  className="group bg-white rounded-3xl p-6 border border-gray-100 hover:border-[#FF5400]/30 hover:shadow-xl transition-all duration-500"
                >
                  <button
                    onClick={() => toggleFaq(index)}
                    className="w-full text-left"
                  >
                    <div className="flex items-start justify-between">
                      <h3 className="text-lg font-semibold text-gray-900 pr-4">{faq.question}</h3>
                      <i className={`fas fa-chevron-${activeFaq === index ? 'up' : 'down'} text-[#FF5400] transition-transform duration-300 flex-shrink-0 mt-1`}></i>
                    </div>
                    {activeFaq === index && (
                      <div className="mt-4 pt-4 border-t border-gray-100">
                        <p className="text-gray-600 leading-relaxed">{faq.answer}</p>
                      </div>
                    )}
                  </button>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Contact CTA */}
      <section className="relative py-12 md:py-24 bg-gradient-to-b from-gray-900 to-gray-800 overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-[0.03]">
          <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
            <defs>
              <pattern id="contact-grid" width="15" height="15" patternUnits="userSpaceOnUse">
                <path d="M 15 0 L 0 0 0 15" fill="none" stroke="#FF5400" strokeWidth="0.3"/>
                <circle cx="7.5" cy="7.5" r="0.5" fill="#FF5400" opacity="0.1"/>
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#contact-grid)" />
          </svg>
        </div>

        {/* Floating Elements */}
        <motion.div
          className="absolute bottom-20 left-10 w-24 h-24 md:w-32 md:h-32 bg-gradient-to-br from-primary/30 to-primary/10 rounded-full blur-xl"
          animate={{
            scale: [1, 1.2, 1],
            rotate: [0, 90, 0],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "linear"
          }}
        />

        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row items-center justify-between gap-8 md:gap-12 bg-gray-800/50 backdrop-blur-lg rounded-3xl p-6 md:p-12 shadow-xl border border-gray-700/50 relative z-10">
            <div className="flex items-center gap-4 md:gap-6">
              <div className="flex shrink-0">
                <span className="flex items-center justify-center w-12 h-12 md:w-16 md:h-16 bg-gradient-to-br from-primary to-primary/80 rounded-2xl shadow-lg">
                  <i className="fas fa-comments text-xl md:text-2xl text-white"></i>
                </span>
                <span className="flex items-center justify-center w-12 h-12 md:w-16 md:h-16 bg-gradient-to-br from-blue-600 to-blue-500 rounded-2xl shadow-lg -ml-6">
                  <i className="fas fa-envelope text-xl md:text-2xl text-white"></i>
                </span>
              </div>
              <div className="flex-1">
                <h3 className="text-lg md:text-2xl font-bold text-white mb-1 md:mb-2">Have a Project in Mind?</h3>
                <p className="text-sm md:text-base text-gray-300">Let's discuss your requirements and create something amazing</p>
              </div>
            </div>
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="w-full md:w-auto px-6 py-3 md:py-4 bg-gradient-to-r from-primary to-primary/90 text-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center gap-2 text-base md:text-lg font-semibold group"
            >
              <i className="fas fa-paper-plane group-hover:rotate-12 transition-transform duration-300"></i>
              Get in Touch
            </motion.button>
          </div>
        </div>
      </section>


    </div>
  );
}