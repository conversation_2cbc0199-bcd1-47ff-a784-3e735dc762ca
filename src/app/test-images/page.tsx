'use client';

import React, { useState, useEffect } from 'react';
import { normalizeImageUrl } from '@/utils/imageUtils';

export default function TestImagesPage() {
  const [testResults, setTestResults] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  // Test URLs
  const testUrls = [
    // Normal URLs
    'https://fr-par-1.linodeobjects.com/mocky/images/portfolio/logos/1.jpg',
    'http://fr-par-1.linodeobjects.com/mocky/images/portfolio/logos/2.jpg',
    
    // URLs without protocol
    'fr-par-1.linodeobjects.com/mocky/images/portfolio/logos/3.jpg',
    
    // URLs with double slashes
    'fr-par-1.linodeobjects.com//mocky/images/portfolio/logos/4.jpg',
    'https://fr-par-1.linodeobjects.com//mocky//images/portfolio/logos/5.jpg',
    
    // Relative URLs
    '/images/placeholder.jpg',
    
    // Invalid URLs
    'invalid-url'
  ];

  // Local normalize function (from edit page)
  const localNormalizeImageUrl = (url: string): string => {
    if (!url) return '';
    
    // If it's already a relative URL, return as is
    if (url.startsWith('/')) {
      return url;
    }
    
    // Handle Linode Object Storage URLs without protocol
    if (url.includes('linodeobjects.com') && !url.startsWith('http')) {
      return `https://${url}`;
    }
    
    // Convert HTTP to HTTPS for Linode URLs
    if (url.startsWith('http://') && url.includes('linodeobjects.com')) {
      return url.replace('http://', 'https://');
    }
    
    // Handle URLs with double slashes (except after protocol)
    if (url.includes('//')) {
      // Fix double slashes in the path portion only
      const fixedUrl = url.replace(/([^:])\/\/+/g, '$1/');
      
      // If it's a Linode URL, also ensure it has a protocol
      if (fixedUrl.includes('linodeobjects.com') && !fixedUrl.startsWith('http')) {
        return `https://${fixedUrl}`;
      }
      
      return fixedUrl;
    }
    
    return url;
  };

  useEffect(() => {
    const results: any[] = [];
    
    // Test both normalization functions
    testUrls.forEach((url) => {
      try {
        const utilNormalized = normalizeImageUrl(url);
        const localNormalized = localNormalizeImageUrl(url);
        
        results.push({
          original: url,
          utilNormalized,
          localNormalized,
          match: utilNormalized === localNormalized
        });
      } catch (error) {
        console.error(`Error testing URL ${url}:`, error);
        results.push({
          original: url,
          error: String(error)
        });
      }
    });
    
    setTestResults(results);
    setLoading(false);
  }, []);

  // Handle image error
  const handleImageError = (e: React.SyntheticEvent<HTMLImageElement>, url: string) => {
    console.error('Image failed to load:', url);
    const img = e.currentTarget;
    
    // Try multiple fixes in sequence
    if (url.includes('linodeobjects.com')) {
      // Fix 1: Convert HTTP to HTTPS
      if (url.startsWith('http://')) {
        console.log('Attempting to fix image URL by converting HTTP to HTTPS');
        img.src = url.replace('http://', 'https://');
        return; // Try this fix first
      }
      
      // Fix 2: Add HTTPS protocol if missing
      if (!url.startsWith('https://')) {
        console.log('Attempting to fix image URL by adding HTTPS protocol');
        img.src = `https://${url.replace(/^http:\/\//, '')}`;
        return;
      }
      
      // Fix 3: Fix double slashes in path
      if (url.includes('//')) {
        const fixedUrl = url.replace(/([^:])\/\/+/g, '$1/');
        if (fixedUrl !== url) {
          console.log('Attempting to fix image URL by removing double slashes');
          img.src = fixedUrl;
          return;
        }
      }
    }
    
    // If all fixes fail, use a fallback image
    console.log('All fixes failed, using fallback image');
    img.src = '/images/placeholder.jpg';
  };

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Image URL Normalization Test</h1>
      
      {loading ? (
        <p>Loading test results...</p>
      ) : (
        <div>
          <h2 className="text-xl font-semibold mb-2">Test Results</h2>
          
          <table className="w-full border-collapse border border-gray-300 mb-8">
            <thead>
              <tr className="bg-gray-100">
                <th className="border border-gray-300 p-2">Original URL</th>
                <th className="border border-gray-300 p-2">Utils Normalized</th>
                <th className="border border-gray-300 p-2">Local Normalized</th>
                <th className="border border-gray-300 p-2">Match?</th>
              </tr>
            </thead>
            <tbody>
              {testResults.map((result, index) => (
                <tr key={index} className={index % 2 === 0 ? 'bg-gray-50' : ''}>
                  <td className="border border-gray-300 p-2 break-all">{result.original}</td>
                  <td className="border border-gray-300 p-2 break-all">{result.utilNormalized || result.error}</td>
                  <td className="border border-gray-300 p-2 break-all">{result.localNormalized || result.error}</td>
                  <td className="border border-gray-300 p-2 text-center">
                    {result.match ? '✅' : '❌'}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
          
          <h2 className="text-xl font-semibold mb-2">Image Loading Test</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {testResults.map((result, index) => (
              <div key={index} className="border border-gray-300 p-4 rounded">
                <h3 className="font-medium mb-2">Test {index + 1}</h3>
                <p className="text-sm mb-2 break-all">{result.original}</p>
                
                <div className="mb-4">
                  <h4 className="text-sm font-medium">Original URL:</h4>
                  <div className="h-32 w-full bg-gray-100 flex items-center justify-center">
                    <img
                      src={result.original}
                      alt={`Test ${index + 1} original`}
                      className="max-h-full max-w-full object-contain"
                      onError={(e) => handleImageError(e, result.original)}
                    />
                  </div>
                </div>
                
                <div className="mb-4">
                  <h4 className="text-sm font-medium">Utils Normalized:</h4>
                  <div className="h-32 w-full bg-gray-100 flex items-center justify-center">
                    <img
                      src={result.utilNormalized}
                      alt={`Test ${index + 1} utils normalized`}
                      className="max-h-full max-w-full object-contain"
                    />
                  </div>
                </div>
                
                <div>
                  <h4 className="text-sm font-medium">Local Normalized:</h4>
                  <div className="h-32 w-full bg-gray-100 flex items-center justify-center">
                    <img
                      src={result.localNormalized}
                      alt={`Test ${index + 1} local normalized`}
                      className="max-h-full max-w-full object-contain"
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
