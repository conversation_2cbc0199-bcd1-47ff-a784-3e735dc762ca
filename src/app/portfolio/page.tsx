import { <PERSON>ada<PERSON> } from 'next';
import Link from 'next/link';
import Image from 'next/image';
import { Suspense } from 'react';
import { SparklesIcon } from '@heroicons/react/24/outline';
import { ImageItem } from '@/utils/getImages';
import { readPortfolioMetadata } from '@/app/api/admin/portfolio/route';
import ClientGallerySection from '@/components/ClientGallerySection';
import { LoadingSpinner } from '@/components/LoadingSpinner';
import { unstable_cache } from 'next/cache';

// Enhanced caching configuration with ISR
export const revalidate = 300; // 5 minutes
export const dynamic = 'force-static';

// Cache version for busting cache when needed
const CACHE_VERSION = process.env.PORTFOLIO_CACHE_VERSION || 'v1';

// Function to shuffle array
function shuffleArray<T>(array: T[]): T[] {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
}

// Enhanced cached function for portfolio data with versioning
const getCachedPortfolioByCategory = unstable_cache(
  async (category: string, version: string): Promise<ImageItem[]> => {
    try {
      const portfolioItems = await readPortfolioMetadata();
      
      // Filter for the specific category and exclude soft-deleted items
      const categoryItems = portfolioItems.filter(item => 
        item.category === category && !item.deletedAt
      );
      
      // Convert to ImageItem format
      const images: ImageItem[] = categoryItems?.map((item, index) => ({
        id: index + 1,
        url: item.imageSrc,
        src: item.imageSrc,
        alt: item.alt || item.title || `${category} image`,
        title: item.title || '',
        category: category,
        createdAt: item.createdAt,
        updatedAt: item.updatedAt
      })) || [];
      
      console.log(`Loaded ${images.length} ${category} items from portfolio metadata (cached)`);
      return images;
    } catch (error) {
      console.error(`Error fetching ${category} from portfolio:`, error);
      return [];
    }
  },
  ['portfolio-category'],
  {
    revalidate: 300, // 5 minutes
    tags: ['portfolio-data'],
  }
);

// Function to get portfolio data by category with enhanced caching
async function getPortfolioByCategory(category: string): Promise<ImageItem[]> {
  return getCachedPortfolioByCategory(category, CACHE_VERSION);
}

// Enhanced cached function for all portfolio data
const getCachedPortfolioData = unstable_cache(
  async (version: string) => {
    console.log('Fetching fresh portfolio data...');
    
    // Get initial data for server-side rendering, with parallel requests for better performance
    const [cards, fliers, letterheads, logos, profiles, branding] = await Promise.all([
      getPortfolioByCategory('cards'),
      getPortfolioByCategory('fliers'),
      getPortfolioByCategory('letterheads'),
      getPortfolioByCategory('logos'),
      getPortfolioByCategory('profiles'),
      getPortfolioByCategory('branding')
    ]);

    return { cards, fliers, letterheads, logos, profiles, branding };
  },
  ['portfolio-all-data'],
  {
    revalidate: 300, // 5 minutes
    tags: ['portfolio-data', 'portfolio-all'],
  }
);

export default async function PortfolioPage() {
  let portfolioData: {
    cards: ImageItem[];
    fliers: ImageItem[];
    letterheads: ImageItem[];
    logos: ImageItem[];
    profiles: ImageItem[];
    branding: ImageItem[];
  } = {
    cards: [],
    fliers: [],
    letterheads: [],
    logos: [],
    profiles: [],
    branding: []
  };

  try {
    // Use cached data with version for cache busting
    portfolioData = await getCachedPortfolioData(CACHE_VERSION);
    console.log('Portfolio data loaded from cache');
  } catch (error) {
    console.error('Error loading portfolio data:', error);
    // Continue with empty arrays - the page will show "No images available" messages
  }

  // Shuffle each array to randomize display
  const shuffledCards = shuffleArray(portfolioData.cards);
  const shuffledFliers = shuffleArray(portfolioData.fliers);
  const shuffledLetterheads = shuffleArray(portfolioData.letterheads);
  const shuffledLogos = shuffleArray(portfolioData.logos);
  const shuffledProfiles = shuffleArray(portfolioData.profiles);
  const shuffledBranding = shuffleArray(portfolioData.branding);

  // Select a few random items for the featured section, ensuring we have items
  const allItems = [
    ...shuffledCards.slice(0, 2),
    ...shuffledFliers.slice(0, 2),
    ...shuffledLetterheads.slice(0, 2),
    ...shuffledLogos.slice(0, 2),
    ...shuffledProfiles.slice(0, 2),
    ...shuffledBranding.slice(0, 2)
  ].filter(item => item && item.src); // Filter out any invalid items

  const featuredItems = shuffleArray(allItems).slice(0, 8);

  return (
    <main className="min-h-screen bg-gradient-to-b from-slate-50 via-white via-gray-50/30 to-orange-50/20 pt-16">
      {/* Enhanced meta tags for cache control */}
      <meta httpEquiv="Cache-Control" content="public, max-age=300, stale-while-revalidate=3600" />
      
      {/* Modern Hero Section - Matching Catalogue Page Design */}
      <div className="relative min-h-[60vh] sm:min-h-[70vh] lg:min-h-[80vh] bg-gradient-to-b from-slate-50 via-white to-gray-50/30 overflow-hidden flex items-center">
        {/* Dynamic Background Pattern */}
        <div className="absolute inset-0 opacity-[0.03]">
          <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
            <defs>
              <pattern id="portfolio-grid" width="15" height="15" patternUnits="userSpaceOnUse">
                <path d="M 15 0 L 0 0 0 15" fill="none" stroke="#FF5400" strokeWidth="0.3"/>
                <circle cx="7.5" cy="7.5" r="0.5" fill="#FF5400" opacity="0.1"/>
              </pattern>
            </defs>
            <rect width="100%" height="100%" fill="url(#portfolio-grid)" />
          </svg>
        </div>

        {/* Enhanced Floating Elements - Responsive */}
        <div className="absolute top-10 sm:top-20 left-4 sm:left-10 w-20 sm:w-32 lg:w-40 h-20 sm:h-32 lg:h-40 bg-gradient-to-br from-orange-200/40 to-red-200/40 rounded-full blur-xl sm:blur-2xl animate-pulse"></div>
        <div className="absolute top-16 sm:top-32 right-4 sm:right-16 w-16 sm:w-24 lg:w-32 h-16 sm:h-24 lg:h-32 bg-gradient-to-br from-blue-200/30 to-purple-200/30 rounded-full blur-lg sm:blur-xl animate-pulse" style={{ animationDelay: '1.5s' }}></div>
        <div className="absolute bottom-20 sm:bottom-40 left-1/4 w-14 sm:w-20 lg:w-28 h-14 sm:h-20 lg:h-28 bg-gradient-to-br from-green-200/25 to-teal-200/25 rounded-full blur-lg sm:blur-xl animate-pulse" style={{ animationDelay: '2.5s' }}></div>
        <div className="absolute bottom-12 sm:bottom-24 right-1/4 sm:right-1/3 w-18 sm:w-28 lg:w-36 h-18 sm:h-28 lg:h-36 bg-gradient-to-br from-pink-200/30 to-rose-200/30 rounded-full blur-xl sm:blur-2xl animate-pulse" style={{ animationDelay: '0.8s' }}></div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 sm:py-16 lg:py-20">
          <div className="text-center space-y-8 sm:space-y-10 lg:space-y-12">
            {/* Premium Badge with Animation */}
            <div className="inline-flex items-center gap-2 sm:gap-3 bg-white/90 backdrop-blur-xl text-[#FF5400] px-4 sm:px-6 lg:px-8 py-3 sm:py-4 rounded-full text-xs sm:text-sm font-bold border border-orange-200/60 shadow-2xl hover:shadow-3xl transition-all duration-500 hover:scale-105 group">
              <SparklesIcon className="h-4 w-4 sm:h-5 sm:w-5 group-hover:rotate-12 transition-transform duration-300" />
              <span className="bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">
                CREATIVE PORTFOLIO SHOWCASE
              </span>
            </div>

            {/* Revolutionary Title */}
            <div className="space-y-4 sm:space-y-6">
              <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-black text-gray-900 leading-[0.9] tracking-tight px-2">
                Our Creative{' '}
                <span className="relative inline-block">
                  <span className="bg-gradient-to-r from-[#FF5400] via-orange-500 to-red-500 bg-clip-text text-transparent">
                    Portfolio
                  </span>
                  <div className="absolute -bottom-1 sm:-bottom-2 left-0 right-0 h-0.5 sm:h-1 bg-gradient-to-r from-orange-400 to-red-400 rounded-full opacity-30"></div>
                </span>
              </h1>
              <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-black text-gray-800 leading-tight px-2">
                Design Excellence
              </h2>
            </div>

            <p className="text-base sm:text-lg md:text-xl lg:text-2xl text-gray-600 max-w-4xl mx-auto leading-relaxed font-light px-4">
              Explore our comprehensive collection of creative work showcasing design excellence and innovative solutions
              that <span className="font-semibold text-gray-800">bring your brand vision to life</span>
            </p>

            {/* Navigation Pills */}
            <div className="flex flex-wrap justify-center gap-3 sm:gap-4 px-4">
              <Link
                href="#logos"
                className="px-4 sm:px-6 py-2 sm:py-3 bg-white/80 backdrop-blur-sm text-gray-700 hover:text-[#FF5400] hover:bg-white border border-gray-200 hover:border-[#FF5400]/30 rounded-full font-medium transition-all duration-300 hover:scale-105 shadow-sm hover:shadow-md text-sm sm:text-base"
              >
                Logos
              </Link>
              <Link
                href="#fliers"
                className="px-4 sm:px-6 py-2 sm:py-3 bg-white/80 backdrop-blur-sm text-gray-700 hover:text-[#FF5400] hover:bg-white border border-gray-200 hover:border-[#FF5400]/30 rounded-full font-medium transition-all duration-300 hover:scale-105 shadow-sm hover:shadow-md text-sm sm:text-base"
              >
                Fliers
              </Link>
              <Link
                href="#cards"
                className="px-4 sm:px-6 py-2 sm:py-3 bg-white/80 backdrop-blur-sm text-gray-700 hover:text-[#FF5400] hover:bg-white border border-gray-200 hover:border-[#FF5400]/30 rounded-full font-medium transition-all duration-300 hover:scale-105 shadow-sm hover:shadow-md text-sm sm:text-base"
              >
                Business Cards
              </Link>
              <Link
                href="#letterheads"
                className="px-4 sm:px-6 py-2 sm:py-3 bg-white/80 backdrop-blur-sm text-gray-700 hover:text-[#FF5400] hover:bg-white border border-gray-200 hover:border-[#FF5400]/30 rounded-full font-medium transition-all duration-300 hover:scale-105 shadow-sm hover:shadow-md text-sm sm:text-base"
              >
                Letterheads
              </Link>
              <Link
                href="#profiles"
                className="px-4 sm:px-6 py-2 sm:py-3 bg-white/80 backdrop-blur-sm text-gray-700 hover:text-[#FF5400] hover:bg-white border border-gray-200 hover:border-[#FF5400]/30 rounded-full font-medium transition-all duration-300 hover:scale-105 shadow-sm hover:shadow-md text-sm sm:text-base"
              >
                Company Profiles
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Featured Portfolio Section - Modern Design */}
      <section className="py-20 bg-gradient-to-br from-white to-gray-50/30">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <div className="flex items-center justify-center gap-3 mb-8">
              <div className="w-8 h-[2px] bg-[#FF5400]"></div>
              <span className="text-[#FF5400] uppercase tracking-wider font-semibold text-sm">
                Featured Work
              </span>
              <div className="w-8 h-[2px] bg-[#FF5400]"></div>
            </div>

            <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold text-[#0A1929] mb-6 leading-tight">
              Featured
              <span className="block text-[#FF5400] relative">
                Projects
                <span className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-20 h-1 bg-[#FF5400]/20 rounded-full"></span>
              </span>
            </h2>

            <p className="text-lg text-gray-600 max-w-3xl mx-auto leading-relaxed">
              Discover our most impactful creative projects that showcase innovation, quality, and exceptional design craftsmanship
            </p>
          </div>

          <Suspense fallback={
            <div className="py-20 text-center">
              <LoadingSpinner size="lg" />
              <p className="text-gray-500 mt-4">Loading featured projects...</p>
            </div>
          }>
            {featuredItems.length > 0 ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 lg:gap-8">
                {featuredItems.map((image, index) => (
                  <div
                    key={`featured-${index}`}
                    className="group relative aspect-square overflow-hidden rounded-2xl bg-white shadow-lg hover:shadow-xl transition-all duration-500 transform hover:-translate-y-2 border border-gray-100"
                  >
                    {/* Enhanced placeholder */}
                    <div className="absolute inset-0 bg-gradient-to-br from-gray-50 to-gray-100 rounded-2xl"></div>

                    {/* Image with enhanced loading */}
                    <Image
                      src={image.src}
                      alt={image.alt || 'Portfolio piece'}
                      fill
                      sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, (max-width: 1280px) 25vw, 25vw"
                      className="object-cover transition-all group-hover:scale-110 duration-700"
                      loading={index < 4 ? "eager" : "lazy"}
                      quality={85}
                      fetchPriority={index < 2 ? "high" : "auto"}
                    />

                    {/* Modern overlay with enhanced design */}
                    <div className="absolute inset-0 bg-gradient-to-t from-[#0A1929]/90 via-[#0A1929]/40 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-500 flex flex-col justify-end p-6">
                      <div className="transform translate-y-4 group-hover:translate-y-0 transition-transform duration-500">
                        <div className="inline-flex items-center px-3 py-1 bg-[#FF5400] text-white text-xs font-semibold rounded-full mb-3 shadow-lg">
                          <span className="mr-1">✨</span>
                          {image.category}
                        </div>
                        <h3 className="text-white text-lg font-bold mb-1 leading-tight">{image.alt}</h3>
                        <p className="text-white/80 text-sm">Featured Project</p>
                      </div>
                    </div>

                    {/* Subtle border glow effect */}
                    <div className="absolute inset-0 rounded-2xl ring-1 ring-white/10 group-hover:ring-[#FF5400]/30 transition-all duration-500"></div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="py-20 text-center">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <SparklesIcon className="w-8 h-8 text-gray-400" />
                </div>
                <h3 className="text-xl font-semibold text-gray-700 mb-2">Featured Projects Coming Soon</h3>
                <p className="text-gray-500 max-w-md mx-auto">
                  Our most impressive portfolio pieces will be showcased here once they're ready for display.
                </p>
              </div>
            )}
          </Suspense>
        </div>
      </section>

      {/* Logo Design Section - Modern Design */}
      <section id="logos" className="py-20 bg-gradient-to-br from-gray-50 to-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <div className="flex items-center justify-center gap-3 mb-8">
              <div className="w-8 h-[2px] bg-[#FF5400]"></div>
              <span className="text-[#FF5400] uppercase tracking-wider font-semibold text-sm">
                Brand Identity
              </span>
              <div className="w-8 h-[2px] bg-[#FF5400]"></div>
            </div>

            <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold text-[#0A1929] mb-6 leading-tight">
              Logo
              <span className="block text-[#FF5400] relative">
                Design
                <span className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-16 h-1 bg-[#FF5400]/20 rounded-full"></span>
              </span>
            </h2>

            <p className="text-lg text-gray-600 max-w-4xl mx-auto leading-relaxed mb-8">
              Professional logos that establish brand identity and make lasting impressions.
              Each design is crafted to represent your unique brand story and values.
            </p>

            <div className="flex flex-wrap justify-center gap-3 text-sm text-gray-500">
              <span className="px-4 py-2 bg-white rounded-full shadow-sm border">Brand Identity</span>
              <span className="px-4 py-2 bg-white rounded-full shadow-sm border">Vector Graphics</span>
              <span className="px-4 py-2 bg-white rounded-full shadow-sm border">Print Ready</span>
              <span className="px-4 py-2 bg-white rounded-full shadow-sm border">Scalable Design</span>
            </div>
          </div>

          <ClientGallerySection
            items={shuffledLogos}
            gridCols="grid-cols-2 md:grid-cols-4"
            aspectRatio="aspect-square"
            objectFit="object-cover"
            category="logos"
          />
        </div>
      </section>

      {/* Branding Section - Modern Design */}
      <section id="branding" className="py-20 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <div className="flex items-center justify-center gap-3 mb-8">
              <div className="w-8 h-[2px] bg-[#FF5400]"></div>
              <span className="text-[#FF5400] uppercase tracking-wider font-semibold text-sm">
                Complete Identity
              </span>
              <div className="w-8 h-[2px] bg-[#FF5400]"></div>
            </div>

            <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold text-[#0A1929] mb-6 leading-tight">
              Branding &
              <span className="block text-[#FF5400] relative">
                Identity
                <span className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-20 h-1 bg-[#FF5400]/20 rounded-full"></span>
              </span>
            </h2>

            <p className="text-lg text-gray-600 max-w-4xl mx-auto leading-relaxed mb-8">
              Comprehensive branding solutions that communicate your unique identity and values.
              From concept to execution, we create cohesive brand experiences that resonate with your audience.
            </p>

            <div className="flex flex-wrap justify-center gap-3 text-sm text-gray-500">
              <span className="px-4 py-2 bg-gray-50 rounded-full shadow-sm border">Brand Strategy</span>
              <span className="px-4 py-2 bg-gray-50 rounded-full shadow-sm border">Visual Identity</span>
              <span className="px-4 py-2 bg-gray-50 rounded-full shadow-sm border">Brand Guidelines</span>
              <span className="px-4 py-2 bg-gray-50 rounded-full shadow-sm border">Complete Package</span>
            </div>
          </div>

          <ClientGallerySection
            items={shuffledBranding}
            gridCols="grid-cols-2 md:grid-cols-4"
            aspectRatio="aspect-square"
            objectFit="object-cover"
            category="branding"
          />
        </div>
      </section>

      {/* Fliers Section - Modern Design */}
      <section id="fliers" className="py-20 bg-gradient-to-br from-gray-50 to-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <div className="flex items-center justify-center gap-3 mb-8">
              <div className="w-8 h-[2px] bg-[#FF5400]"></div>
              <span className="text-[#FF5400] uppercase tracking-wider font-semibold text-sm">
                Marketing Materials
              </span>
              <div className="w-8 h-[2px] bg-[#FF5400]"></div>
            </div>

            <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold text-[#0A1929] mb-6 leading-tight">
              Fliers &
              <span className="block text-[#FF5400] relative">
                Marketing Materials
                <span className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-24 h-1 bg-[#FF5400]/20 rounded-full"></span>
              </span>
            </h2>

            <p className="text-lg text-gray-600 max-w-4xl mx-auto leading-relaxed mb-8">
              Eye-catching fliers and marketing materials designed to capture attention and drive engagement.
              Our designs support both portrait and square formats to maximize impact across all platforms.
            </p>

            <div className="flex flex-wrap justify-center gap-3 text-sm text-gray-500">
              <span className="px-4 py-2 bg-white rounded-full shadow-sm border">Print Ready</span>
              <span className="px-4 py-2 bg-white rounded-full shadow-sm border">Digital Formats</span>
              <span className="px-4 py-2 bg-white rounded-full shadow-sm border">Social Media</span>
              <span className="px-4 py-2 bg-white rounded-full shadow-sm border">Square & Portrait</span>
            </div>
          </div>

          <ClientGallerySection
            items={shuffledFliers}
            gridCols="grid-cols-2 sm:grid-cols-3 md:grid-cols-4"
            aspectRatio="aspect-square"
            objectFit="object-cover"
            category="fliers"
          />
        </div>
      </section>

      {/* Business Cards Section - Modern Design */}
      <section id="cards" className="py-20 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <div className="flex items-center justify-center gap-3 mb-8">
              <div className="w-8 h-[2px] bg-[#FF5400]"></div>
              <span className="text-[#FF5400] uppercase tracking-wider font-semibold text-sm">
                Professional Cards
              </span>
              <div className="w-8 h-[2px] bg-[#FF5400]"></div>
            </div>

            <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold text-[#0A1929] mb-6 leading-tight">
              Business
              <span className="block text-[#FF5400] relative">
                Cards
                <span className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-16 h-1 bg-[#FF5400]/20 rounded-full"></span>
              </span>
            </h2>

            <p className="text-lg text-gray-600 max-w-4xl mx-auto leading-relaxed mb-8">
              Professional business cards that make memorable first impressions and represent your brand with style.
              Each design is crafted to leave a lasting impact and communicate your professional identity.
            </p>

            <div className="flex flex-wrap justify-center gap-3 text-sm text-gray-500">
              <span className="px-4 py-2 bg-gray-50 rounded-full shadow-sm border">Premium Quality</span>
              <span className="px-4 py-2 bg-gray-50 rounded-full shadow-sm border">Double-Sided</span>
              <span className="px-4 py-2 bg-gray-50 rounded-full shadow-sm border">Multiple Finishes</span>
              <span className="px-4 py-2 bg-gray-50 rounded-full shadow-sm border">Custom Design</span>
            </div>
          </div>

          <ClientGallerySection
            items={shuffledCards}
            gridCols="grid-cols-1 sm:grid-cols-2 lg:grid-cols-3"
            aspectRatio="aspect-[5/3]"
            objectFit="object-cover"
            category="cards"
          />
        </div>
      </section>

      {/* Letterheads Section - Modern Design */}
      <section id="letterheads" className="py-20 bg-gradient-to-br from-gray-50 to-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <div className="flex items-center justify-center gap-3 mb-8">
              <div className="w-8 h-[2px] bg-[#FF5400]"></div>
              <span className="text-[#FF5400] uppercase tracking-wider font-semibold text-sm">
                Corporate Stationery
              </span>
              <div className="w-8 h-[2px] bg-[#FF5400]"></div>
            </div>

            <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold text-[#0A1929] mb-6 leading-tight">
              Letterheads &
              <span className="block text-[#FF5400] relative">
                Stationery
                <span className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-20 h-1 bg-[#FF5400]/20 rounded-full"></span>
              </span>
            </h2>

            <p className="text-lg text-gray-600 max-w-4xl mx-auto leading-relaxed mb-8">
              Professional letterheads and stationery designs that enhance your business communications.
              Create a cohesive brand experience across all your corporate documents and correspondence.
            </p>

            <div className="flex flex-wrap justify-center gap-3 text-sm text-gray-500">
              <span className="px-4 py-2 bg-white rounded-full shadow-sm border">Corporate Identity</span>
              <span className="px-4 py-2 bg-white rounded-full shadow-sm border">Professional Layout</span>
              <span className="px-4 py-2 bg-white rounded-full shadow-sm border">Brand Consistency</span>
              <span className="px-4 py-2 bg-white rounded-full shadow-sm border">Print Ready</span>
            </div>
          </div>

          <ClientGallerySection
            items={shuffledLetterheads}
            gridCols="grid-cols-1 sm:grid-cols-2 md:grid-cols-3"
            aspectRatio="aspect-[3/4]"
            objectFit="object-cover"
            category="letterheads"
          />
        </div>
      </section>

      {/* Company Profiles Section - Modern Design */}
      <section id="profiles" className="py-20 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <div className="flex items-center justify-center gap-3 mb-8">
              <div className="w-8 h-[2px] bg-[#FF5400]"></div>
              <span className="text-[#FF5400] uppercase tracking-wider font-semibold text-sm">
                Corporate Profiles
              </span>
              <div className="w-8 h-[2px] bg-[#FF5400]"></div>
            </div>

            <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold text-[#0A1929] mb-6 leading-tight">
              Company
              <span className="block text-[#FF5400] relative">
                Profiles
                <span className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-20 h-1 bg-[#FF5400]/20 rounded-full"></span>
              </span>
            </h2>

            <p className="text-lg text-gray-600 max-w-4xl mx-auto leading-relaxed mb-8">
              Comprehensive company profiles that showcase your business story, values, and capabilities.
              Professional documents that communicate your company's mission and establish credibility with stakeholders.
            </p>

            <div className="flex flex-wrap justify-center gap-3 text-sm text-gray-500">
              <span className="px-4 py-2 bg-gray-50 rounded-full shadow-sm border">Company Story</span>
              <span className="px-4 py-2 bg-gray-50 rounded-full shadow-sm border">Professional Layout</span>
              <span className="px-4 py-2 bg-gray-50 rounded-full shadow-sm border">Brand Showcase</span>
              <span className="px-4 py-2 bg-gray-50 rounded-full shadow-sm border">Multi-Page Design</span>
            </div>
          </div>

          <ClientGallerySection
            items={shuffledProfiles}
            gridCols="grid-cols-1 sm:grid-cols-2 md:grid-cols-3"
            aspectRatio="aspect-[3/4]"
            objectFit="object-cover"
            category="profiles"
          />
        </div>
      </section>
    </main>
  );
}

export const metadata = {
  title: 'Portfolio | Mocky Digital Kenya',
  description: 'Browse our diverse portfolio of logos, branding, fliers, business cards, letterheads, and company profiles.',
  keywords: 'logo design, branding, identity design, fliers, business cards, letterheads, company profiles, graphic design, kenya, nairobi'
};