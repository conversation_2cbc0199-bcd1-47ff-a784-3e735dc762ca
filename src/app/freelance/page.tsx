'use client';

import { motion, useScroll, useTransform } from 'framer-motion';
import Image from 'next/image';
import { useRef, useEffect, useState } from 'react';
import Link from 'next/link';

const designProcess = [
  {
    step: 1,
    title: 'Project Brief',
    icon: 'far fa-comments',
    description: 'Comprehensive discussion with the client through meetings, calls, or digital communication to understand project requirements, goals, and expectations.'
  },
  {
    step: 2,
    title: 'Research & Planning',
    icon: 'far fa-lightbulb',
    description: 'In-depth analysis of your industry, competitors, target audience, and brand positioning to develop strategic design solutions.'
  },
  {
    step: 3,
    title: 'Creative Exploration',
    icon: 'far fa-edit',
    description: 'Multiple design concepts and sketches are created to explore various creative directions before selecting the strongest approaches.'
  },
  {
    step: 4,
    title: 'Design Development',
    icon: 'far fa-eye',
    description: 'Selected concepts are refined using professional design software with realistic mockups showing how designs will appear in real-world applications.'
  },
  {
    step: 5,
    title: 'Client Review',
    icon: 'far fa-sync',
    description: 'Collaborative revision process to fine-tune designs until they perfectly align with your vision and business objectives.'
  },
  {
    step: 6,
    title: 'Final Delivery',
    icon: 'far fa-check-circle',
    description: 'Complete file package delivery with ongoing support for any implementation questions or future design needs.'
  }
];

const services = [
  {
    title: 'Logo Design',
    description: 'Distinctive brand marks that capture your business essence and create lasting impressions.',
    icon: 'far fa-star',
    color: 'from-[#FF5400] to-[#ff7633]'
  },
  {
    title: 'Web Design',
    description: 'User-friendly, responsive websites that convert visitors into customers and grow your online presence.',
    icon: 'far fa-globe',
    color: 'from-[#0A2647] to-[#205295]'
  },
  {
    title: 'Brand Identity',
    description: 'Complete visual identity systems including business cards, letterheads, and brand guidelines.',
    icon: 'far fa-id-card',
    color: 'from-[#FF5400] to-[#ff7633]'
  },
  {
    title: 'Print Design',
    description: 'Professional brochures, flyers, posters, and marketing materials that drive engagement.',
    icon: 'far fa-file-alt',
    color: 'from-[#0A2647] to-[#205295]'
  },
  {
    title: 'Social Media Graphics',
    description: 'Eye-catching social media posts and campaigns that boost your digital presence.',
    icon: 'far fa-thumbs-up',
    color: 'from-[#FF5400] to-[#ff7633]'
  },
  {
    title: 'Company Profiles',
    description: 'Professional company profiles and catalogs that showcase your business effectively.',
    icon: 'far fa-building',
    color: 'from-[#0A2647] to-[#205295]'
  }
];

const whyFreelance = [
  {
    title: 'Cost-Effective Solutions',
    description: 'Lower overhead costs compared to agencies mean better value for your investment without compromising quality.',
    icon: 'far fa-dollar-sign'
  },
  {
    title: 'Personal Attention',
    description: 'Direct communication with your designer ensures your vision is understood and executed perfectly.',
    icon: 'far fa-user'
  },
  {
    title: 'Flexible Availability',
    description: 'Extended communication hours and responsive service to accommodate your schedule and urgent needs.',
    icon: 'far fa-clock'
  },
  {
    title: 'Consistent Quality',
    description: 'Work with one dedicated designer who understands your brand and maintains consistency across all projects.',
    icon: 'far fa-check-circle'
  }
];

// Custom animated counter component
function Counter({ from, to, duration = 2 }: { from: number; to: number; duration?: number }) {
  const [count, setCount] = useState(from);

  useEffect(() => {
    let startTime: number | undefined;
    let animationFrame: number;

    const updateCount = (timestamp: number) => {
      if (!startTime) startTime = timestamp;
      const progress = Math.min((timestamp - startTime) / (duration * 1000), 1);
      setCount(Math.floor(progress * (to - from) + from));

      if (progress < 1) {
        animationFrame = requestAnimationFrame(updateCount);
      }
    };

    animationFrame = requestAnimationFrame(updateCount);
    return () => cancelAnimationFrame(animationFrame);
  }, [from, to, duration]);

  return <span>{count}+</span>;
}

export default function FreelancePage() {
  const targetRef = useRef(null);
  const { scrollYProgress } = useScroll({
    target: targetRef,
    offset: ["start end", "end start"]
  });

  const opacity = useTransform(scrollYProgress, [0, 0.5], [0, 1]);
  const y = useTransform(scrollYProgress, [0, 0.5], [50, 0]);

  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.disconnect();
        }
      },
      { threshold: 0.1 }
    );

    if (targetRef.current) {
      observer.observe(targetRef.current);
    }

    return () => observer.disconnect();
  }, []);

  return (
    <main className="pt-24 overflow-hidden">
      {/* Hero Section */}
      <section className="relative min-h-[80vh] flex items-center justify-center pt-10 md:pt-16">
        {/* Background Elements */}
        <div className="absolute inset-0 -z-10 bg-[#0A1929] overflow-hidden">
          <div className="absolute w-full h-full bg-[url('/images/grid.svg')] opacity-[0.03]"></div>
          <div className="absolute top-1/4 -right-20 w-80 h-80 bg-[#0A2647] rounded-full mix-blend-multiply filter blur-[80px] opacity-20 animate-float-slow"></div>
          <div className="absolute top-3/4 -left-20 w-80 h-80 bg-[#FF5400] rounded-full mix-blend-multiply filter blur-[80px] opacity-15 animate-float-slow-reverse"></div>
          <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-[800px] h-[800px] bg-[#0A2647]/10 rounded-full mix-blend-multiply filter blur-[80px] opacity-10 animate-pulse-slow"></div>
        </div>

        <div className="container relative z-10">
          <div className="max-w-5xl mx-auto">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="text-center max-w-4xl mx-auto pt-6 sm:pt-10"
            >
              <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-white/10 backdrop-blur-md border border-white/10 text-white/80 text-sm font-medium mb-6">
                <div className="w-2 h-2 rounded-full bg-[#FF5400]"></div>
                Freelance Design Services
              </div>

              <h1 className="text-4xl sm:text-5xl lg:text-7xl font-bold mb-6 sm:mb-8 text-white leading-tight px-4 sm:px-0 pt-4 sm:pt-0">
                Freelance <span className="text-transparent bg-clip-text bg-gradient-to-r from-[#FF5400] to-[#ff7633]">Graphic Designer</span> in Nairobi, Kenya
              </h1>

              <p className="text-lg sm:text-xl text-white/70 leading-relaxed mb-8 sm:mb-10 max-w-3xl mx-auto px-4">
                Hi there! I'm Don OMondi, a professional freelance graphic designer based in Nairobi, Kenya. I specialize in helping businesses across Kenya attract more customers through exceptional graphic design solutions - from professional logo design and responsive web design to comprehensive branding and digital marketing materials.
              </p>

              <div className="flex flex-wrap gap-4 sm:gap-6 justify-center px-4">
                <Link href="/contact" className="px-6 sm:px-8 py-3 rounded-full bg-[#FF5400] text-white font-medium hover:bg-[#cc4300] hover:shadow-lg hover:shadow-[#FF5400]/20 transition-all duration-300 transform hover:-translate-y-1">
                  Start Your Project
                </Link>
                <Link href="/portfolio" className="px-6 sm:px-8 py-3 rounded-full bg-white/10 backdrop-blur-sm border border-white/10 text-white font-medium hover:bg-white/20 transition-all duration-300">
                  View Portfolio
                </Link>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Designer Profile Section */}
      <section className="py-16 md:py-24 relative overflow-hidden bg-gray-50">
        <div className="container relative z-10 mx-auto px-4">
          <div className="max-w-7xl mx-auto">
            <div className="grid lg:grid-cols-2 gap-12 lg:gap-16 items-center">
              {/* Designer Image */}
              <motion.div
                initial={{ opacity: 0, x: -40 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8 }}
                viewport={{ once: true }}
                className="relative order-2 lg:order-1"
              >
                <div className="relative group">
                  <div className="relative rounded-2xl overflow-hidden shadow-2xl bg-white p-2">
                    <div className="relative rounded-xl overflow-hidden">
                      <Image
                        src="/images/about/ceo.jpg"
                        alt="Don OMondi - Freelance Graphic Designer at Mocky Digital"
                        width={600}
                        height={700}
                        className="object-cover w-full aspect-[4/5] transition-transform duration-500 group-hover:scale-105"
                        style={{ objectPosition: "center 10%" }}
                        priority
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-[#0A2647]/80 via-transparent to-transparent"></div>
                    </div>
                  </div>

                  {/* Floating info card */}
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: 0.3 }}
                    viewport={{ once: true }}
                    className="absolute -bottom-6 -right-6 bg-white rounded-xl shadow-xl p-6 border border-gray-100"
                  >
                    <h3 className="text-xl font-bold text-[#0A2647] mb-1">Don OMondi</h3>
                    <p className="text-[#0A2647]/70 text-sm font-medium mb-4">Freelance Graphic Designer</p>
                    <div className="flex gap-2">
                      <a href="https://www.linkedin.com/in/don-omondi-242367206/" target="_blank" rel="noopener noreferrer" className="w-8 h-8 rounded-lg bg-[#0A2647]/5 hover:bg-[#0A66C2] hover:text-white flex items-center justify-center text-[#0A2647] transition-all duration-300">
                        <i className="fab fa-linkedin-in text-sm"></i>
                      </a>
                      <a href="https://x.com/onyango__omondi" target="_blank" rel="noopener noreferrer" className="w-8 h-8 rounded-lg bg-[#0A2647]/5 hover:bg-[#1DA1F2] hover:text-white flex items-center justify-center text-[#0A2647] transition-all duration-300">
                        <i className="fab fa-twitter text-sm"></i>
                      </a>
                    </div>
                  </motion.div>
                </div>
              </motion.div>

              {/* Content */}
              <motion.div
                initial={{ opacity: 0, x: 40 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
                viewport={{ once: true }}
                className="relative order-1 lg:order-2"
              >
                <div className="relative">
                  <div className="relative bg-white rounded-2xl shadow-xl p-8 md:p-10 border border-gray-100">
                    <div className="absolute -top-4 -left-4 w-12 h-12 bg-[#FF5400] rounded-full flex items-center justify-center shadow-lg">
                      <svg className="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M14.017 21v-7.391c0-5.704 3.731-9.57 8.983-10.609l.995 2.151c-2.432.917-3.995 3.638-3.995 5.849h4v10h-9.983zm-14.017 0v-7.391c0-5.704 3.748-9.57 9-10.609l.996 2.151c-2.433.917-3.996 3.638-3.996 5.849h4v10h-10z"/>
                      </svg>
                    </div>

                    <div className="space-y-6">
                      <div className="space-y-4">
                        <p className="text-lg md:text-xl text-[#0A2647]/80 leading-relaxed font-medium">
                          As a freelance graphic designer in Nairobi, I founded Mocky Digital with a passion for helping Kenyan businesses stand out through exceptional design. My approach combines creative expertise with strategic thinking to deliver graphic design solutions that not only look great but drive real business results for companies across Nairobi and Kenya.
                        </p>
                        <p className="text-lg md:text-xl text-[#0A2647]/80 leading-relaxed font-medium">
                          Every graphic design project is treated with the same dedication to quality and attention to detail. Whether it's logo design, web design, branding, or complete digital marketing materials, I work closely with clients throughout Nairobi to ensure their vision comes to life effectively and affordably.
                        </p>
                      </div>

                      <div className="flex items-center gap-4 pt-6 border-t border-gray-100">
                        <div className="flex-1 h-px bg-gradient-to-r from-[#FF5400] to-transparent"></div>
                        <div className="text-sm font-medium text-[#0A2647]/60">Don OMondi</div>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            </div>

            {/* Stats Section */}
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              viewport={{ once: true }}
              className="mt-16 md:mt-20"
            >
              <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
                {[
                  { number: '100+', label: 'Projects Completed' },
                  { number: '50+', label: 'Happy Clients' },
                  { number: '5+', label: 'Years Experience' },
                  { number: '24/7', label: 'Support Available' }
                ].map((stat, index) => (
                  <div key={index} className="text-center">
                    <div className="text-2xl md:text-3xl font-bold text-[#0A2647] mb-2">
                      {stat.number}
                    </div>
                    <div className="text-[#0A2647]/60 text-sm font-medium">
                      {stat.label}
                    </div>
                  </div>
                ))}
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Why Design Is Important Section */}
      <section className="py-24 relative overflow-hidden bg-white">
        <div className="container relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="text-center mb-20"
          >
            <h2 className="text-4xl lg:text-5xl font-bold text-[#0A2647] mb-6">
              Why Graphic Design is Important for Nairobi Businesses
            </h2>
            <div className="h-1 w-20 bg-gradient-to-r from-[#FF5400] to-[#ff7633] mx-auto mb-6" />
            <p className="text-[#0A2647]/70 text-lg max-w-3xl mx-auto">
              Professional graphic design is more than aesthetics - it's a powerful business tool that drives growth and success for companies across Nairobi and Kenya.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {[
              {
                title: 'First Impressions Count',
                description: 'You have seconds to make an impact. Professional design builds credibility and trust with potential customers from the very first interaction.',
                icon: 'far fa-eye',
                color: 'from-[#FF5400] to-[#ff7633]'
              },
              {
                title: 'Stand Out From Competition',
                description: 'In a crowded marketplace, distinctive design helps your business differentiate itself and capture customer attention effectively.',
                icon: 'far fa-star',
                color: 'from-[#0A2647] to-[#205295]'
              },
              {
                title: 'Build Brand Recognition',
                description: 'Consistent visual identity creates emotional connections with your audience, fostering loyalty and encouraging repeat business.',
                icon: 'far fa-heart',
                color: 'from-[#FF5400] to-[#ff7633]'
              },
              {
                title: 'Drive Conversions',
                description: 'Strategic design guides user behavior, encouraging visitors to take desired actions and converting them into customers.',
                icon: 'far fa-chart-line',
                color: 'from-[#0A2647] to-[#205295]'
              },
              {
                title: 'Save Time & Money',
                description: 'Investing in quality design upfront prevents costly rebrands and redesigns while building long-term brand equity.',
                icon: 'far fa-clock',
                color: 'from-[#FF5400] to-[#ff7633]'
              },
              {
                title: 'Tell Your Story',
                description: 'Design communicates your brand values and personality, helping customers understand what makes your business unique.',
                icon: 'far fa-book',
                color: 'from-[#0A2647] to-[#205295]'
              }
            ].map((benefit, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="relative group"
              >
                <div className="relative p-8 bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 h-full">
                  <div className={`inline-flex items-center justify-center w-12 h-12 rounded-lg bg-gradient-to-r ${benefit.color} text-white mb-6 group-hover:scale-110 transition-transform duration-300`}>
                    <i className={benefit.icon}></i>
                  </div>

                  <h3 className="text-xl font-bold text-[#0A2647] mb-4 group-hover:text-[#FF5400] transition-colors">
                    {benefit.title}
                  </h3>

                  <p className="text-[#0A2647]/70 leading-relaxed">
                    {benefit.description}
                  </p>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Why Hire a Freelancer Section */}
      <section className="py-24 relative overflow-hidden bg-gray-50">
        <div className="container relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="text-center mb-20"
          >
            <h2 className="text-4xl lg:text-5xl font-bold text-[#0A2647] mb-6">
              Why Choose a Freelance Graphic Designer in Nairobi
            </h2>
            <div className="h-1 w-20 bg-gradient-to-r from-[#FF5400] to-[#ff7633] mx-auto mb-6" />
            <p className="text-[#0A2647]/70 text-lg max-w-3xl mx-auto">
              Discover the advantages of working with a dedicated freelance graphic designer in Nairobi who puts your business success first with affordable, professional design services.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {whyFreelance.map((reason, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="relative group"
              >
                <div className="relative p-8 bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 h-full">
                  <div className="inline-flex items-center justify-center w-12 h-12 rounded-lg bg-gradient-to-r from-[#FF5400] to-[#ff7633] text-white mb-6 group-hover:scale-110 transition-transform duration-300">
                    <i className={reason.icon}></i>
                  </div>

                  <h3 className="text-xl font-bold text-[#0A2647] mb-4 group-hover:text-[#FF5400] transition-colors">
                    {reason.title}
                  </h3>

                  <p className="text-[#0A2647]/70 leading-relaxed">
                    {reason.description}
                  </p>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Design Process Section */}
      <section className="py-24 relative overflow-hidden bg-white">
        <div className="container relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="text-center mb-20"
          >
            <h2 className="text-4xl lg:text-5xl font-bold text-[#0A2647] mb-6">
              Professional Graphic Design Process in Nairobi
            </h2>
            <div className="h-1 w-20 bg-gradient-to-r from-[#FF5400] to-[#ff7633] mx-auto mb-6" />
            <p className="text-[#0A2647]/70 text-lg max-w-3xl mx-auto">
              A proven 6-step design process that ensures exceptional results for every graphic design project in Nairobi and across Kenya.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {designProcess.map((step, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="relative group"
              >
                <div className="relative p-8 bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 h-full">
                  <div className="flex items-center mb-6">
                    <div className={`inline-flex items-center justify-center w-12 h-12 rounded-lg bg-gradient-to-r ${index % 2 === 0 ? 'from-[#FF5400] to-[#ff7633]' : 'from-[#0A2647] to-[#205295]'} text-white mr-4 group-hover:scale-110 transition-transform duration-300`}>
                      <i className={step.icon}></i>
                    </div>
                    <div className="bg-[#FF5400] text-white rounded-full w-8 h-8 flex items-center justify-center text-sm font-bold">
                      {step.step}
                    </div>
                  </div>

                  <h3 className="text-xl font-bold text-[#0A2647] mb-4 group-hover:text-[#FF5400] transition-colors">
                    {step.title}
                  </h3>

                  <p className="text-[#0A2647]/70 leading-relaxed">
                    {step.description}
                  </p>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-24 relative overflow-hidden bg-gradient-to-b from-[#0A1929] to-[#0A2647]">
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-0 left-0 w-full h-full bg-[url('/images/grid.svg')] opacity-[0.03]"></div>
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-[#205295] rounded-full mix-blend-multiply filter blur-[80px] opacity-20"></div>
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-[#0A2647] rounded-full mix-blend-multiply filter blur-[80px] opacity-20"></div>
        </div>

        <div className="container relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="text-center mb-20"
          >
            <span className="inline-block px-4 py-1.5 rounded-full bg-white/10 backdrop-blur-md border border-white/10 text-white/80 text-xs font-medium mb-4">Freelance Services Nairobi</span>
            <h2 className="text-4xl lg:text-5xl font-bold text-white mb-6">
              Freelance Graphic Design Services in Nairobi
            </h2>
            <div className="h-1 w-20 bg-gradient-to-r from-[#FF5400] to-[#ff7633] mx-auto mb-6" />
            <p className="text-white/70 text-lg max-w-3xl mx-auto">
              Comprehensive graphic design solutions to elevate your brand and drive business growth across Nairobi, Kenya.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {services.map((service, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="relative group"
              >
                <div className="relative p-8 bg-white/5 backdrop-blur-md border border-white/10 rounded-xl hover:bg-white/10 transition-all duration-300 h-full">
                  <div className={`inline-flex items-center justify-center w-12 h-12 rounded-lg bg-gradient-to-r ${service.color} text-white mb-6 group-hover:scale-110 transition-transform duration-300`}>
                    <i className={service.icon}></i>
                  </div>

                  <h3 className="text-xl font-bold text-white mb-4 group-hover:text-[#FF5400] transition-colors">
                    {service.title}
                  </h3>

                  <p className="text-white/70 leading-relaxed">
                    {service.description}
                  </p>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* FAQ Section for SEO */}
      <section className="py-24 relative overflow-hidden bg-white">
        <div className="container relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            viewport={{ once: true }}
            className="text-center mb-20"
          >
            <h2 className="text-4xl lg:text-5xl font-bold text-[#0A2647] mb-6">
              Frequently Asked Questions - Freelance Graphic Design Nairobi
            </h2>
            <div className="h-1 w-20 bg-gradient-to-r from-[#FF5400] to-[#ff7633] mx-auto mb-6" />
            <p className="text-[#0A2647]/70 text-lg max-w-3xl mx-auto">
              Common questions about freelance graphic design services in Nairobi, Kenya.
            </p>
          </motion.div>

          <div className="max-w-4xl mx-auto space-y-6">
            {[
              {
                question: "What graphic design services do you offer in Nairobi?",
                answer: "As a freelance graphic designer in Nairobi, I offer comprehensive design services including logo design, web design, branding, business cards, brochures, social media graphics, poster design, company profiles, and digital marketing materials for businesses across Kenya."
              },
              {
                question: "How much do freelance graphic design services cost in Nairobi?",
                answer: "My freelance graphic design rates in Nairobi are competitive and affordable. Pricing varies based on project complexity, but I offer transparent pricing with no hidden costs. Contact me for a free quote tailored to your specific design needs and budget."
              },
              {
                question: "Why choose a freelance graphic designer over a design agency in Nairobi?",
                answer: "Choosing a freelance graphic designer in Nairobi offers several advantages: lower costs, direct communication, personalized service, flexible schedules, and consistent quality. You work directly with the designer (me) throughout the entire project."
              },
              {
                question: "How long does a typical graphic design project take in Nairobi?",
                answer: "Project timelines vary depending on complexity. Logo design typically takes 3-7 days, web design 1-3 weeks, and comprehensive branding projects 2-4 weeks. I always provide clear timelines upfront and keep clients updated throughout the design process."
              },
              {
                question: "Do you work with businesses outside Nairobi?",
                answer: "Yes! While I'm based in Nairobi, I work with clients throughout Kenya and internationally. Thanks to modern communication tools, I can effectively collaborate with businesses anywhere while maintaining the same high-quality service."
              }
            ].map((faq, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="bg-gray-50 rounded-xl p-6 hover:shadow-lg transition-shadow"
              >
                <h3 className="text-xl font-bold text-[#0A2647] mb-4">{faq.question}</h3>
                <p className="text-[#0A2647]/70 leading-relaxed">{faq.answer}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 relative overflow-hidden bg-gray-50">
        <div className="container relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
            className="text-center max-w-4xl mx-auto"
          >
            <h2 className="text-4xl lg:text-5xl font-bold text-[#0A2647] mb-6">
              Ready to Transform Your Brand with Professional Graphic Design?
            </h2>
            <p className="text-lg text-[#0A2647]/70 mb-8 max-w-2xl mx-auto">
              Let's discuss your graphic design project and create something amazing together. As a freelance graphic designer in Nairobi, I'm here to help your business stand out in the competitive digital landscape with affordable, professional design solutions.
            </p>

            <div className="flex flex-wrap gap-4 justify-center">
              <Link 
                href="/contact" 
                className="px-8 py-4 bg-[#FF5400] text-white font-medium rounded-full hover:bg-[#cc4300] hover:shadow-lg hover:shadow-[#FF5400]/20 transition-all duration-300 transform hover:-translate-y-1"
              >
                Start Your Project Today
              </Link>
              <Link 
                href="/portfolio" 
                className="px-8 py-4 bg-white border-2 border-[#0A2647] text-[#0A2647] font-medium rounded-full hover:bg-[#0A2647] hover:text-white transition-all duration-300"
              >
                View My Portfolio
              </Link>
            </div>

            <div className="mt-12 p-6 bg-white rounded-xl shadow-lg max-w-2xl mx-auto">
              <h3 className="text-xl font-bold text-[#0A2647] mb-4">Get in Touch</h3>
              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <a href="mailto:<EMAIL>" className="flex items-center gap-2 text-[#0A2647] hover:text-[#FF5400] transition-colors">
                  <i className="far fa-envelope"></i>
                  <EMAIL>
                </a>
                <a href="tel:+254741590670" className="flex items-center gap-2 text-[#0A2647] hover:text-[#FF5400] transition-colors">
                  <i className="far fa-phone"></i>
                  +254 741 590 670
                </a>
              </div>
            </div>
          </motion.div>
        </div>
      </section>
    </main>
  );
} 