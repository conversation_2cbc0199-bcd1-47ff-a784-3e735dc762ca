export interface CalendarEvent {
  id: string;
  title: string;
  description?: string;
  startTime: string;
  endTime: string;
  type: 'meeting' | 'call' | 'deadline' | 'reminder' | 'appointment';
  status: 'scheduled' | 'confirmed' | 'cancelled' | 'completed';
  location?: string;
  isVirtual: boolean;
  meetingLink?: string;
  meetingPlatform?: 'zoom' | 'google-meet' | 'teams' | 'webex' | 'custom';
  meetingDetails?: {
    meetingId?: string;
    passcode?: string;
    dialInNumbers?: string[];
    recordingEnabled?: boolean;
    waitingRoom?: boolean;
    requireAuth?: boolean;
  };
  attendees: Array<{
    id: string;
    name: string;
    email: string;
    status: 'pending' | 'accepted' | 'declined' | 'tentative';
  }>;
  project?: {
    id: string;
    name: string;
    client?: {
      firstName: string;
      lastName: string;
      company?: string;
    };
  };
  client?: {
    id: string;
    firstName: string;
    lastName: string;
    company?: string;
    email: string;
  };
  createdBy: {
    id: string;
    name: string;
    email: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface CreateEventModalProps {
  isOpen: boolean;
  onClose: () => void;
  onEventCreated: (event: CalendarEvent) => void;
  defaultDate?: Date;
}

export interface MeetingCardProps {
  event: CalendarEvent;
  className?: string;
  onEdit?: (event: CalendarEvent) => void;
  onDelete?: (eventId: string) => void;
  onView?: (event: CalendarEvent) => void;
}

export interface CalendarViewProps {
  events: CalendarEvent[];
  currentDate: Date;
  selectedEvent?: CalendarEvent | null;
  onEventClick?: (event: CalendarEvent) => void;
  onDateSelect?: (date: Date) => void;
  onCreateEvent?: (date?: Date) => void;
  loading?: boolean;
}

export type ViewMode = 'month' | 'week' | 'day' | 'agenda';

export type EventType = CalendarEvent['type'];
export type EventStatus = CalendarEvent['status'];
export type MeetingPlatform = NonNullable<CalendarEvent['meetingPlatform']>;

// Status and type configurations
export const statusConfig = {
  scheduled: { color: 'bg-blue-100 text-blue-800', label: 'Scheduled' },
  confirmed: { color: 'bg-green-100 text-green-800', label: 'Confirmed' },
  cancelled: { color: 'bg-red-100 text-red-800', label: 'Cancelled' },
  completed: { color: 'bg-gray-100 text-gray-800', label: 'Completed' },
} as const;

export const typeConfig = {
  meeting: { color: 'bg-blue-100 text-blue-800', label: 'Meeting' },
  call: { color: 'bg-green-100 text-green-800', label: 'Call' },
  deadline: { color: 'bg-red-100 text-red-800', label: 'Deadline' },
  reminder: { color: 'bg-yellow-100 text-yellow-800', label: 'Reminder' },
  appointment: { color: 'bg-purple-100 text-purple-800', label: 'Appointment' },
} as const;

export const eventTypeColors = {
  meeting: 'bg-primary/10 text-primary border-primary/20',
  call: 'bg-secondary/10 text-secondary border-secondary/20',
  deadline: 'bg-red-100 text-red-800 border-red-200',
  reminder: 'bg-orange-100 text-orange-800 border-orange-200',
  appointment: 'bg-accent/10 text-accent border-accent/20',
} as const;

export const statusColors = {
  scheduled: 'bg-gray-100 text-gray-800',
  confirmed: 'bg-secondary/10 text-secondary',
  cancelled: 'bg-red-100 text-red-800',
  completed: 'bg-primary/10 text-primary',
} as const;

// Video conferencing platform configurations
export const meetingPlatforms = {
  zoom: {
    name: 'Zoom',
    color: 'bg-blue-500',
    textColor: 'text-blue-700',
    bgColor: 'bg-blue-50',
    borderColor: 'border-blue-200',
    joinText: 'Join Zoom Meeting',
    features: ['Waiting Room', 'Recording', 'Screen Share', 'Breakout Rooms']
  },
  'google-meet': {
    name: 'Google Meet',
    color: 'bg-green-500',
    textColor: 'text-green-700',
    bgColor: 'bg-green-50',
    borderColor: 'border-green-200',
    joinText: 'Join Google Meet',
    features: ['Live Captions', 'Recording', 'Screen Share', 'Attendance Tracking']
  },
  teams: {
    name: 'Microsoft Teams',
    color: 'bg-purple-500',
    textColor: 'text-purple-700',
    bgColor: 'bg-purple-50',
    borderColor: 'border-purple-200',
    joinText: 'Join Teams Meeting',
    features: ['Recording', 'Screen Share', 'Collaborative Docs', 'Chat']
  },
  webex: {
    name: 'Cisco Webex',
    color: 'bg-orange-500',
    textColor: 'text-orange-700',
    bgColor: 'bg-orange-50',
    borderColor: 'border-orange-200',
    joinText: 'Join Webex Meeting',
    features: ['Recording', 'Screen Share', 'Whiteboard', 'Polling']
  },
  custom: {
    name: 'Custom Platform',
    color: 'bg-gray-500',
    textColor: 'text-gray-700',
    bgColor: 'bg-gray-50',
    borderColor: 'border-gray-200',
    joinText: 'Join Meeting',
    features: ['Custom Integration']
  }
} as const; 