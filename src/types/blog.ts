export interface BlogPost {
  id: string;
  title: string;
  slug: string;
  content: string;
  excerpt: string;
  category: string;
  status: BlogPostStatus;
  createdAt: string;
  updatedAt: string;
  publishedAt: string | null;
  author?: string;
  featuredImage?: string;
  tags?: string[];
  readingTime?: number;
  viewCount?: number;
  seoTitle?: string;
  seoDescription?: string;
  seoKeywords?: string[];
}

export type BlogPostStatus = 'draft' | 'published' | 'scheduled';

export interface BlogPostFormData {
  title: string;
  content: string;
  excerpt: string;
  category: string;
  status: BlogPostStatus;
  author: string;
  featuredImage?: string;
  tags: string[];
  seoTitle?: string;
  seoDescription?: string;
  seoKeywords?: string[];
  scheduledDate?: string;
}

export interface BlogCategory {
  id: string;
  name: string;
  slug: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
}

export interface BlogTag {
  id: string;
  name: string;
  slug: string;
  count: number;
  createdAt: string;
  updatedAt: string;
}

export interface BlogAuthor {
  id: string;
  name: string;
  email: string;
  bio?: string;
  avatar?: string;
  socialLinks?: {
    twitter?: string;
    linkedin?: string;
    website?: string;
  };
  postCount: number;
  createdAt: string;
  updatedAt: string;
}

export interface BlogStats {
  totalPosts: number;
  publishedPosts: number;
  draftPosts: number;
  scheduledPosts: number;
  totalViews: number;
  totalCategories: number;
  totalTags: number;
  totalAuthors: <AUTHORS>
}

// Default category for fallback
export const DEFAULT_CATEGORY = { value: 'uncategorized', label: 'Uncategorized' };
