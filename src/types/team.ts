import { z } from 'zod';

// Base TeamMember interface - standardized across the application
export interface TeamMember {
  id: string;
  name: string;
  role: string;
  bio: string;
  imageSrc: string;
  order: number;
  linkedinUrl?: string | null;
  twitterUrl?: string | null;
  githubUrl?: string | null;
  emailAddress?: string | null;
  createdAt: Date | string;
  updatedAt: Date | string;
}

// Form data interface for client-side forms
export interface TeamMemberFormData {
  name: string;
  role: string;
  bio: string;
  image: File | null;
  order: number;
  linkedinUrl?: string;
  twitterUrl?: string;
  githubUrl?: string;
  emailAddress?: string;
}

// Database model interface (matches Prisma schema exactly)
export interface TeamMemberDB {
  id: string;
  name: string;
  role: string;
  bio: string;
  imageKey: string;
  order: number;
  linkedinUrl: string | null;
  twitterUrl: string | null;
  githubUrl: string | null;
  emailAddress: string | null;
  createdAt: Date;
  updatedAt: Date;
}

// API request/response types
export interface CreateTeamMemberRequest {
  name: string;
  role: string;
  bio: string;
  order: number;
  linkedinUrl?: string;
  twitterUrl?: string;
  githubUrl?: string;
  emailAddress?: string;
  image: File;
}

export interface UpdateTeamMemberRequest {
  name?: string;
  role?: string;
  bio?: string;
  order?: number;
  linkedinUrl?: string;
  twitterUrl?: string;
  githubUrl?: string;
  emailAddress?: string;
  image?: File;
}

// Validation schemas using Zod
export const TeamMemberValidationSchema = z.object({
  name: z.string()
    .min(1, 'Name is required')
    .max(100, 'Name must be less than 100 characters')
    .regex(/^[a-zA-Z\s\-'\.]+$/, 'Name contains invalid characters'),

  role: z.string()
    .min(1, 'Role is required')
    .max(100, 'Role must be less than 100 characters')
    .regex(/^[a-zA-Z\s\-|&,\.]+$/, 'Role contains invalid characters'),

  bio: z.string()
    .min(10, 'Bio must be at least 10 characters')
    .max(1000, 'Bio must be less than 1000 characters'),

  order: z.number()
    .int('Order must be an integer')
    .min(0, 'Order must be non-negative')
    .max(999, 'Order must be less than 1000'),

  linkedinUrl: z.string()
    .url('Invalid LinkedIn URL')
    .regex(/^https:\/\/(www\.)?linkedin\.com\/in\/[a-zA-Z0-9\-]+\/?$/, 'Invalid LinkedIn profile URL')
    .optional()
    .or(z.literal('')),

  twitterUrl: z.string()
    .url('Invalid Twitter URL')
    .regex(/^https:\/\/(www\.)?(twitter\.com|x\.com)\/[a-zA-Z0-9_]+\/?$/, 'Invalid Twitter profile URL')
    .optional()
    .or(z.literal('')),

  githubUrl: z.string()
    .url('Invalid GitHub URL')
    .regex(/^https:\/\/(www\.)?github\.com\/[a-zA-Z0-9\-]+\/?$/, 'Invalid GitHub profile URL')
    .optional()
    .or(z.literal('')),

  emailAddress: z.string()
    .email('Invalid email address')
    .max(100, 'Email must be less than 100 characters')
    .optional()
    .or(z.literal(''))
});

export const CreateTeamMemberSchema = TeamMemberValidationSchema;

export const UpdateTeamMemberSchema = TeamMemberValidationSchema.partial();

// Image validation schema
export const ImageValidationSchema = z.object({
  size: z.number().max(15 * 1024 * 1024, 'Image must be less than 15MB'),
  type: z.string().regex(/^image\/(jpeg|jpg|png|gif|webp|avif)$/, 'Invalid image format. Only JPEG, PNG, GIF, WebP, and AVIF are allowed')
});

// API response types
export interface TeamMemberResponse {
  success: boolean;
  data?: TeamMember;
  error?: string;
  warnings?: string[];
}

export interface TeamMembersListResponse {
  success: boolean;
  data?: TeamMember[];
  error?: string;
  total?: number;
}

// Error types
export class TeamMemberValidationError extends Error {
  constructor(
    message: string,
    public field?: string,
    public code?: string
  ) {
    super(message);
    this.name = 'TeamMemberValidationError';
  }
}

export class TeamMemberNotFoundError extends Error {
  constructor(id: string) {
    super(`Team member with ID ${id} not found`);
    this.name = 'TeamMemberNotFoundError';
  }
}

export class TeamMemberImageUploadError extends Error {
  constructor(message: string, public originalError?: Error) {
    super(message);
    this.name = 'TeamMemberImageUploadError';
  }
}
