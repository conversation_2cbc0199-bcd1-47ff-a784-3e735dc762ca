import { z } from 'zod';

// Base User Interface
export interface User {
  id: string;
  username: string;
  email: string;
  name: string | null;
  active: boolean;
  lastLogin: string | null;
  createdAt: string;
  updatedAt: string;
  roleId: string;
  role: Role;
}

// Role Interface
export interface Role {
  id: string;
  name: string;
  description: string | null;
  permissions: string[];
  createdAt: string;
  updatedAt: string;
}

// Activity Log Interface
export interface ActivityLog {
  id: string;
  userId: string;
  action: string;
  details: string;
  ipAddress: string | null;
  userAgent: string | null;
  createdAt: string;
  metadata?: Record<string, any>;
}

// Form Data Types
export interface UserFormData {
  username: string;
  email: string;
  name: string;
  password: string;
  confirmPassword: string;
  roleId: string;
  active: boolean;
}

// API Response Types
export interface UserResponse {
  success: boolean;
  data?: User;
  error?: string;
  message?: string;
}

export interface UsersListResponse {
  success: boolean;
  data?: User[];
  error?: string;
  message?: string;
}

// Validation Schemas
export const UserUpdateSchema = z.object({
  username: z.string()
    .min(3, 'Username must be at least 3 characters')
    .max(50, 'Username must not exceed 50 characters')
    .regex(/^[a-zA-Z0-9_-]+$/, 'Username can only contain letters, numbers, underscores, and hyphens')
    .optional(),
  email: z.string()
    .email('Please enter a valid email address')
    .max(100, 'Email must not exceed 100 characters')
    .optional(),
  name: z.string()
    .min(1, 'Name is required')
    .max(100, 'Name must not exceed 100 characters')
    .optional()
    .or(z.literal('')),
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .max(128, 'Password must not exceed 128 characters')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain at least one uppercase letter, one lowercase letter, and one number')
    .optional(),
  roleId: z.string()
    .uuid('Please select a valid role')
    .optional(),
  active: z.boolean().optional(),
});

export const UserCreateSchema = UserUpdateSchema.extend({
  username: z.string()
    .min(3, 'Username must be at least 3 characters')
    .max(50, 'Username must not exceed 50 characters')
    .regex(/^[a-zA-Z0-9_-]+$/, 'Username can only contain letters, numbers, underscores, and hyphens'),
  email: z.string()
    .email('Please enter a valid email address')
    .max(100, 'Email must not exceed 100 characters'),
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .max(128, 'Password must not exceed 128 characters')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain at least one uppercase letter, one lowercase letter, and one number'),
  roleId: z.string()
    .uuid('Please select a valid role'),
});

// Form validation schema with password confirmation
export const UserFormSchema = z.object({
  username: z.string()
    .min(3, 'Username must be at least 3 characters')
    .max(50, 'Username must not exceed 50 characters')
    .regex(/^[a-zA-Z0-9_-]+$/, 'Username can only contain letters, numbers, underscores, and hyphens'),
  email: z.string()
    .email('Please enter a valid email address')
    .max(100, 'Email must not exceed 100 characters'),
  name: z.string()
    .max(100, 'Name must not exceed 100 characters')
    .optional()
    .or(z.literal('')),
  password: z.string()
    .min(8, 'Password must be at least 8 characters')
    .max(128, 'Password must not exceed 128 characters')
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, 'Password must contain at least one uppercase letter, one lowercase letter, and one number')
    .optional(),
  confirmPassword: z.string().optional(),
  roleId: z.string()
    .uuid('Please select a valid role'),
  active: z.boolean().default(true),
}).refine((data) => {
  if (data.password && data.password !== data.confirmPassword) {
    return false;
  }
  return true;
}, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

// Type exports for form validation
export type UserFormInput = z.infer<typeof UserFormSchema>;
export type UserUpdateInput = z.infer<typeof UserUpdateSchema>;
export type UserCreateInput = z.infer<typeof UserCreateSchema>; 