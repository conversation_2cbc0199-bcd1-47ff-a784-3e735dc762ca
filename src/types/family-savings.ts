export interface FamilyUser {
  id: string;
  email: string;
  username: string;
  firstName: string;
  lastName: string;
  phoneNumber?: string;
  isApproved: boolean;
  isActive: boolean;
  dateJoined: Date;
  lastLogin?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface FamilyTransaction {
  id: string;
  userId: string;
  transactionType: 'DEPOSIT' | 'WITHDRAWAL' | 'TRANSFER';
  transactionCode: string;
  amount: number;
  status: 'PENDING' | 'COMPLETED' | 'FAILED';
  reference?: string;
  description?: string;
  timestamp: Date;
  rawMessage?: string;
  senderName?: string;
  isApproved: boolean;
  approvedById?: string;
  approvedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  user?: FamilyUser;
  approvedBy?: FamilyUser;
}

export interface FamilyLoan {
  id: string;
  borrowerId: string;
  amount: number;
  approvedAmount?: number;
  termMonths?: number;
  purpose: string;
  status: 'PENDING' | 'APPROVED' | 'REJECTED' | 'PAID';
  applicationDate: Date;
  approvalDate?: Date;
  repaymentDeadline: Date;
  approvedById?: string;
  rejectionReason?: string;
  transactionMessage?: string;
  interestRate: number;
  interestType: 'SIMPLE' | 'COMPOUND';
  compoundingFrequency?: 'MONTHLY' | 'QUARTERLY' | 'ANNUALLY';
  createdAt: Date;
  updatedAt: Date;
  borrower?: FamilyUser;
  approvedBy?: FamilyUser;
  repayments?: LoanRepayment[];
}

export interface LoanRepayment {
  id: string;
  loanId: string;
  userId: string;
  amount: number;
  paymentDate: Date;
  transactionReference: string;
  status: 'PENDING' | 'APPROVED' | 'REJECTED';
  approvedById?: string;
  approvalDate?: Date;
  rejectionReason?: string;
  createdAt: Date;
  updatedAt: Date;
  loan?: FamilyLoan;
  user?: FamilyUser;
}

export interface FamilyBusiness {
  id: string;
  ownerId: string;
  name: string;
  businessType: 'RETAIL' | 'SERVICE' | 'WHOLESALE' | 'MANUFACTURING' | 'RESTAURANT' | 'AGRICULTURE' | 'OTHER';
  description?: string;
  registrationNumber?: string;
  location: string;
  createdAt: Date;
  updatedAt: Date;
  owner?: FamilyUser;
  transactions?: BusinessTransaction[];
  categories?: TransactionCategory[];
  inventoryItems?: BusinessInventory[];
  reports?: BusinessReport[];
}

export interface BusinessTransaction {
  id: string;
  businessId: string;
  categoryId?: string;
  transactionType: 'INCOME' | 'EXPENSE';
  amount: number;
  date: Date;
  description: string;
  paymentMethod: 'CASH' | 'MPESA' | 'BANK' | 'CARD' | 'OTHER';
  referenceNumber?: string;
  createdAt: Date;
  updatedAt: Date;
  business?: FamilyBusiness;
  category?: TransactionCategory;
}

export interface TransactionCategory {
  id: string;
  businessId: string;
  name: string;
  categoryType: 'INCOME' | 'EXPENSE';
  description?: string;
  isActive: boolean;
  createdAt: Date;
  business?: FamilyBusiness;
  transactions?: BusinessTransaction[];
}

export interface BusinessInventory {
  id: string;
  businessId: string;
  name: string;
  description?: string;
  quantity: number;
  unitPrice: number;
  reorderLevel: number;
  createdAt: Date;
  updatedAt: Date;
  business?: FamilyBusiness;
}

export interface BusinessReport {
  id: string;
  businessId: string;
  reportType: 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'CUSTOM';
  startDate: Date;
  endDate: Date;
  totalIncome: number;
  totalExpenses: number;
  netProfitLoss: number;
  notes?: string;
  generatedAt: Date;
  business?: FamilyBusiness;
}

export interface UserSavingsGoal {
  id: string;
  userId: string;
  title: string;
  monthlyTarget: number;
  startDate: Date;
  endDate: Date;
  description?: string;
  isActive: boolean;
  reminderTime: string;
  lastReminderSent?: Date;
  createdAt: Date;
  updatedAt: Date;
  user?: FamilyUser;
}

export interface FamilyNotification {
  id: string;
  userId: string;
  title: string;
  message: string;
  channel: 'email' | 'telegram' | 'whatsapp' | 'sms';
  status: 'PENDING' | 'SENT' | 'FAILED';
  recipient?: string;
  sentAt?: Date;
  errorMessage?: string;
  createdAt: Date;
  user?: FamilyUser;
}

export interface InterestRateConfig {
  id: string;
  durationType: 'SHORT' | 'MEDIUM' | 'LONG';
  interestType: 'SIMPLE' | 'COMPOUND';
  minMonths: number;
  maxMonths: number;
  interestRate: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface ContributionGoal {
  id: string;
  title: string;
  targetAmount: number;
  startDate: Date;
  endDate: Date;
  description: string;
  isActive: boolean;
  createdAt: Date;
}

// API Response Types
export interface DashboardStats {
  totalContributions: number;
  totalLoans: number;
  totalBusinessRevenue: number;
  activeUsers: number;
  pendingTransactions: number;
  monthlyGrowth: number;
}

export interface TransactionStats {
  totalDeposits: number;
  totalWithdrawals: number;
  pendingApprovals: number;
  monthlyVolume: number;
}

export interface LoanStats {
  totalLoanValue: number;
  activeLoanCount: number;
  overdueLoans: number;
  totalInterestGenerated: number;
}

// Form Types
export interface CreateTransactionData {
  amount: number;
  transactionType: 'DEPOSIT' | 'WITHDRAWAL' | 'TRANSFER';
  description?: string;
  reference?: string;
}

export interface CreateLoanData {
  amount: number;
  purpose: string;
  termMonths?: number;
  repaymentDeadline: Date;
}

export interface ApproveLoanData {
  approvedAmount: number;
  interestRate: number;
  termMonths: number;
  interestType: 'SIMPLE' | 'COMPOUND';
  compoundingFrequency?: 'MONTHLY' | 'QUARTERLY' | 'ANNUALLY';
}

export interface CreateBusinessData {
  name: string;
  businessType: 'RETAIL' | 'SERVICE' | 'WHOLESALE' | 'MANUFACTURING' | 'RESTAURANT' | 'AGRICULTURE' | 'OTHER';
  description?: string;
  registrationNumber?: string;
  location: string;
}

export interface CreateBusinessTransactionData {
  categoryId?: string;
  transactionType: 'INCOME' | 'EXPENSE';
  amount: number;
  date: Date;
  description: string;
  paymentMethod: 'CASH' | 'MPESA' | 'BANK' | 'CARD' | 'OTHER';
  referenceNumber?: string;
}

export interface CreateSavingsGoalData {
  title: string;
  monthlyTarget: number;
  startDate: Date;
  endDate: Date;
  description?: string;
  reminderTime: string;
}

// Filter and Search Types
export interface TransactionFilters {
  status?: 'PENDING' | 'COMPLETED' | 'FAILED';
  transactionType?: 'DEPOSIT' | 'WITHDRAWAL' | 'TRANSFER';
  startDate?: Date;
  endDate?: Date;
  userId?: string;
}

export interface LoanFilters {
  status?: 'PENDING' | 'APPROVED' | 'REJECTED' | 'PAID';
  borrowerId?: string;
  startDate?: Date;
  endDate?: Date;
}

export interface BusinessFilters {
  businessType?: string;
  ownerId?: string;
} 