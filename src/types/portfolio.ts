export interface PortfolioItem {
  id: string;
  title: string;
  description?: string;
  category: string;
  imageSrc: string;
  alt?: string;
  featured?: boolean;
  createdAt: string;
  updatedAt: string;
  url?: string; // For website portfolio items
  deletedAt?: string | null; // Soft delete timestamp
}

export type PortfolioCategory =
  | 'logos'
  | 'branding'
  | 'fliers'
  | 'cards'
  | 'letterheads'
  | 'profiles'
  | 'websites'
  | 'other';

export const PORTFOLIO_CATEGORIES: { value: PortfolioCategory; label: string }[] = [
  { value: 'logos', label: 'Logos' },
  { value: 'branding', label: 'Branding' },
  { value: 'fliers', label: 'Fliers' },
  { value: 'cards', label: 'Business Cards' },
  { value: 'letterheads', label: 'Letterheads' },
  { value: 'profiles', label: 'Company Profiles' },
  { value: 'websites', label: 'Websites' },
  { value: 'other', label: 'Other' }
];

export interface WebsitePortfolioItem {
  id: string;
  title: string;
  description?: string;
  url: string; // Maps to projectUrl in database
  imageKey?: string;
  imageSrc: string; // Full S3 URL for frontend display (maps to imageUrl in DB)
  category: string;
  featured: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface WebsitePortfolioFormData {
  title: string;
  description: string;
  category: string;
  image: File | null;
  url: string;
  featured: boolean;
}

export const WEBSITE_CATEGORIES = [
  { value: 'e-commerce', label: 'E-commerce' },
  { value: 'corporate', label: 'Corporate' },
  { value: 'nonprofit', label: 'Non-profit' },
  { value: 'portfolio', label: 'Portfolio' },
  { value: 'blog', label: 'Blog' },
  { value: 'landing-page', label: 'Landing Page' },
  { value: 'other', label: 'Other' }
] as const;

export type WebsiteCategory = typeof WEBSITE_CATEGORIES[number]['value'];

export interface PortfolioFormData {
  title: string;
  description: string;
  category: PortfolioCategory;
  image: File | null;
  alt: string;
  featured: boolean;
  url?: string; // For website portfolio items
}
