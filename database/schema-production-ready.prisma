// Production-Ready Prisma Schema for Mocky Digital
// Updated: January 2025
// This schema matches the exact database structure in production

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// ============================================================================
// AUTHENTICATION & USER MANAGEMENT
// ============================================================================

model User {
  id           String          @id @default(cuid())
  username     String          @unique @db.VarChar(50)
  email        String          @unique @db.VarChar(100)
  name         String?         @db.VarChar(100)
  passwordHash String          @db.VarChar(255)
  active       Boolean         @default(true)
  roleId       String
  lastLogin    DateTime?
  createdAt    DateTime        @default(now())
  updatedAt    DateTime        @updatedAt
  
  // Relations
  role              Role                    @relation(fields: [roleId], references: [id])
  activityLogs      ActivityLog[]
  invoices          Invoice[]
  projects          ProjectMember[]
  quotes            Quote[]
  receipts          Receipt[]
  tasks             Task[]
  timeEntries       TimeEntry[]
  designRequests    DesignRequest[]         @relation("DesignRequests")
  communications    ClientCommunication[]   @relation("CreatedCommunications")

  @@map("users")
}

model Role {
  id          String   @id @default(cuid())
  name        String   @unique @db.VarChar(50)
  description String?
  permissions String[] @default([])
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relations
  users       User[]

  @@map("roles")
}

model ActivityLog {
  id           String   @id @default(cuid())
  userId       String
  action       String   @db.VarChar(100)
  details      String?
  ipAddress    String?  @db.VarChar(50)
  userAgent    String?
  resourceType String?  @db.VarChar(50)
  resourceId   String?
  createdAt    DateTime @default(now())
  
  // Relations
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([action])
  @@index([createdAt])
  @@index([resourceType])
  @@map("activity_logs")
}

// ============================================================================
// SITE CONFIGURATION
// ============================================================================

model SiteSettings {
  id                String   @id @default(cuid())
  siteName          String   @default("Mocky Digital")
  siteDescription   String?  @db.VarChar(500)
  contactEmail      String?  @db.VarChar(100)
  phoneNumber       String?  @db.VarChar(50)
  address           String?  @db.VarChar(255)
  facebookUrl       String?  @db.VarChar(255)
  twitterUrl        String?  @db.VarChar(255)
  instagramUrl      String?  @db.VarChar(255)
  linkedinUrl       String?  @db.VarChar(255)
  tiktokUrl         String?  @db.VarChar(255)
  metaTitle         String?  @db.VarChar(255)
  metaDescription   String?  @db.VarChar(500)
  googleAnalyticsId String?  @db.VarChar(50)
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  @@map("site_settings")
}

// ============================================================================
// CONTENT MANAGEMENT
// ============================================================================

model BlogPost {
  id             Int              @id @default(autoincrement())
  title          String           @db.VarChar(255)
  slug           String           @unique @db.VarChar(255)
  content        String
  excerpt        String?
  author         String?          @db.VarChar(100)
  category       String?          @db.VarChar(100)
  categoryId     String?
  tags           String[]         @default([])
  status         String           @default("draft") @db.VarChar(20)
  featuredImage  String?          @db.VarChar(500)
  seoTitle       String?          @db.VarChar(255)
  seoDescription String?          @db.VarChar(500)
  seoKeywords    String[]         @default([])
  readingTime    Int?
  viewCount      Int              @default(0)
  publishedAt    DateTime?
  scheduledAt    DateTime?
  createdAt      DateTime         @default(now())
  updatedAt      DateTime         @updatedAt
  
  // Relations
  blogCategory   BlogCategory?    @relation("BlogPostCategory", fields: [categoryId], references: [id])

  @@index([status])
  @@index([category])
  @@index([categoryId])
  @@index([publishedAt])
  @@index([createdAt])
  @@map("blog_posts")
}

model Category {
  id          Int      @id @default(autoincrement())
  name        String   @db.VarChar(100)
  slug        String   @unique @db.VarChar(100)
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("categories")
}

model BlogCategory {
  id          String           @id @default(cuid())
  name        String           @db.VarChar(100)
  slug        String           @unique @db.VarChar(100)
  description String?
  parentId    String?
  order       Int              @default(0)
  active      Boolean          @default(true)
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt
  
  // Relations
  parent      BlogCategory?    @relation("BlogCategoryHierarchy", fields: [parentId], references: [id])
  children    BlogCategory[]   @relation("BlogCategoryHierarchy")
  posts       BlogPost[]       @relation("BlogPostCategory")

  @@index([slug])
  @@index([parentId])
  @@index([active])
  @@map("blog_categories")
}

model WebsitePortfolio {
  id          String   @id @default(cuid())
  title       String   @db.VarChar(255)
  description String?
  imageUrl    String?  @db.VarChar(500)
  projectUrl  String?  @db.VarChar(500)
  category    String?  @db.VarChar(100)
  tags        String[] @default([])
  featured    Boolean  @default(false)
  order       Int      @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@index([category])
  @@index([featured])
  @@map("website_portfolio")
}

model TeamMember {
  id           String   @id @default(cuid())
  name         String   @db.VarChar(100)
  role         String   @db.VarChar(100)
  bio          String
  imageKey     String
  linkedinUrl  String?  @db.VarChar(255)
  twitterUrl   String?  @db.VarChar(255)
  githubUrl    String?  @db.VarChar(255)
  emailAddress String?  @db.VarChar(100)
  order        Int      @default(0)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  @@map("team_members")
}

model Testimonial {
  id          Int      @id @default(autoincrement())
  name        String   @db.VarChar(100)
  company     String?  @db.VarChar(100)
  location    String   @db.VarChar(100)
  project     String   @db.VarChar(255)
  testimonial String
  rating      Int      @default(5)
  active      Boolean  @default(true)
  order       Int      @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("testimonials")
}

// ============================================================================
// SERVICES & PRODUCTS
// ============================================================================

model Service {
  id           String        @id @default(cuid())
  name         String        @db.VarChar(100)
  description  String?
  price        Decimal       @db.Decimal(10, 2)
  category     String        @db.VarChar(50)
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt
  
  // Relations
  invoiceItems InvoiceItem[]
  quoteItems   QuoteItem[]
  receiptItems ReceiptItem[]

  @@map("services")
}

model Catalogue {
  id          Int      @id @default(autoincrement())
  service     String   @db.VarChar(255)
  description String?
  price       Int      // Base price or price per unit
  category    String?  @default("Other") @db.VarChar(100)
  features    String[] @default([])
  icon        String?  @db.VarChar(100)
  
  // Image URLs - Fixed validation to allow null values
  imageUrl    String?
  imageUrl2   String?
  imageUrl3   String?
  
  popular     Boolean? @default(false)
  
  // Pricing configuration fields
  pricingType String   @default("fixed") @db.VarChar(50) // "fixed", "paper_print", "banner_meter", "custom"
  unitType    String?  @db.VarChar(50) // "piece", "meter", "sqm", etc.
  minQuantity Int?     @default(1)
  maxQuantity Int?
  
  // Paper print specific fields
  paperTypes  Json?    // Store paper type pricing rules
  
  // Banner/meter based fields  
  pricePerMeter Decimal? @db.Decimal(8, 2)
  minMeters     Decimal? @db.Decimal(5, 2)
  maxMeters     Decimal? @db.Decimal(8, 2)
  
  // Quantity-based pricing tiers
  pricingTiers Json?    // Store quantity-based pricing rules
  
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relations
  orders      Order[]

  @@index([category])
  @@index([popular])
  @@index([pricingType])
  @@map("catalogue")
}

model PaperType {
  id            String   @id @default(cuid())
  name          String   @db.VarChar(100) // e.g., "ART/MATT PAPER"
  grammage      String?  @db.VarChar(50)  // e.g., "130G"
  oneSidedPrice Decimal  @db.Decimal(8, 2) // Price for 1-sided printing
  twoSidedPrice Decimal  @db.Decimal(8, 2) // Price for 2-sided printing
  category      String   @default("paper") @db.VarChar(50)
  active        Boolean  @default(true)
  order         Int      @default(0)
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  @@index([category])
  @@index([active])
  @@map("paper_types")
}

model PricingRule {
  id        String   @id @default(cuid())
  name      String   @db.VarChar(100)
  type      String   @db.VarChar(50) // "paper_print", "banner_meter", "quantity_tier"
  category  String?  @db.VarChar(100) // Product category this applies to
  rules     Json     // Flexible JSON structure for pricing rules
  active    Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([type])
  @@index([category])
  @@index([active])
  @@map("pricing_rules")
}

// ============================================================================
// ORDERS & DESIGN REQUESTS
// ============================================================================

model DesignRequest {
  id          String   @id @default(cuid())
  customerName String  @db.VarChar(255)
  email       String   @db.VarChar(100)
  phone       String   @db.VarChar(50)
  productType String   @db.VarChar(100)
  quantity    Int
  specifications String? // Design brief/requirements
  urgency     String   @default("standard") @db.VarChar(50) // "urgent", "standard", "flexible"
  budget      Decimal? @db.Decimal(8, 2)
  designType  String   @db.VarChar(100) // "logo", "business_card", "flyer", "banner", etc.
  status      String   @default("pending") @db.VarChar(50) // "pending", "in_progress", "completed", "cancelled"
  
  // Design service pricing
  designFee   Decimal  @default(0) @db.Decimal(8, 2)
  printCost   Decimal  @default(0) @db.Decimal(8, 2)
  totalCost   Decimal  @default(0) @db.Decimal(8, 2)
  
  // Relationships
  orderId     String?  // Link to order if they proceed
  assignedTo  String?  // Designer assigned
  
  // File attachments - Enhanced for artwork management
  referenceFiles String[] @default([]) // Reference images/files
  designFiles    String[] @default([]) // Completed design files
  
  // Timestamps
  requestedAt DateTime @default(now())
  completedAt DateTime?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relations
  designer    User?    @relation("DesignRequests", fields: [assignedTo], references: [id])
  order       Order?   @relation(fields: [orderId], references: [id])

  @@index([status])
  @@index([designType])
  @@index([requestedAt])
  @@map("design_requests")
}

model Order {
  id            String   @id @default(cuid())
  orderNumber   String   @unique @db.VarChar(50)
  customerName  String   @db.VarChar(255)
  email         String   @db.VarChar(100)
  phone         String   @db.VarChar(50)
  
  // Product details
  productId     Int
  productName   String   @db.VarChar(255)
  quantity      Int
  customQuantity Boolean @default(false) // If user typed custom quantity
  
  // Pricing breakdown
  unitPrice     Decimal  @db.Decimal(8, 2)
  designFee     Decimal  @default(0) @db.Decimal(8, 2)
  subtotal      Decimal  @db.Decimal(8, 2)
  totalAmount   Decimal  @db.Decimal(8, 2)
  
  // Product specifications
  paperType     String?  @db.VarChar(100)
  printingSide  String?  @db.VarChar(50)
  meters        Decimal? @db.Decimal(5, 2)
  specifications Json?   // Additional specs
  
  // Design service integration
  needsDesign   Boolean  @default(false)
  designOnly    Boolean  @default(false) // True if only design service needed, no printing
  designBrief   String?  // Customer's design requirements
  
  // Enhanced file management system
  artworkFiles     String[] @default([])
  artworkFileCount Int      @default(0)
  
  // Status tracking
  status        String   @default("pending") @db.VarChar(50) // "pending", "confirmed", "in_production", "ready", "delivered"
  paymentStatus String   @default("pending") @db.VarChar(50)
  
  // Delivery
  deliveryMethod String? @db.VarChar(50) // "pickup", "delivery"
  deliveryAddress String?
  notes         String?
  
  // Timestamps
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt
  confirmedAt   DateTime?
  completedAt   DateTime?
  
  // Relations
  product       Catalogue       @relation(fields: [productId], references: [id])
  designRequests DesignRequest[]

  @@index([status])
  @@index([createdAt])
  @@index([customerName])
  @@map("orders")
}

// ============================================================================
// CLIENT MANAGEMENT & COMMUNICATIONS
// ============================================================================

model Client {
  id        String    @id @default(cuid())
  name      String    @db.VarChar(255)
  email     String?   @db.VarChar(100)
  phone     String?   @db.VarChar(50)
  company   String?   @db.VarChar(255)
  address   String?
  notes     String?
  status    String    @default("active") @db.VarChar(50)
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  
  // Relations
  invoices       Invoice[]
  projects       Project[]
  quotes         Quote[]
  receipts       Receipt[]
  communications ClientCommunication[]

  @@map("clients")
}

model ClientCommunication {
  id          String    @id @default(cuid())
  clientId    String
  type        String    @db.VarChar(50) // email, call, meeting, message, note
  subject     String?   @db.VarChar(255)
  content     String    // Message content
  direction   String    @default("outbound") @db.VarChar(20) // inbound, outbound
  status      String    @default("draft") @db.VarChar(50) // draft, sent, received, read, replied, completed, scheduled
  priority    String    @default("medium") @db.VarChar(20) // low, medium, high, urgent
  projectId   String?
  createdBy   String
  scheduledAt DateTime?
  completedAt DateTime?
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  
  // Relations
  client      Client                    @relation(fields: [clientId], references: [id], onDelete: Cascade)
  project     Project?                  @relation(fields: [projectId], references: [id])
  creator     User                      @relation("CreatedCommunications", fields: [createdBy], references: [id])
  attachments CommunicationAttachment[]

  @@index([clientId])
  @@index([createdBy])
  @@index([scheduledAt])
  @@index([status])
  @@map("client_communications")
}

model CommunicationAttachment {
  id               String              @id @default(cuid())
  communicationId  String
  fileName         String              @db.VarChar(255)
  originalName     String              @db.VarChar(255)
  fileSize         Int
  mimeType         String              @db.VarChar(100)
  fileUrl          String              @db.VarChar(500)
  createdAt        DateTime            @default(now())
  
  // Relations
  communication    ClientCommunication @relation(fields: [communicationId], references: [id], onDelete: Cascade)

  @@map("communication_attachments")
}

// ============================================================================
// PROJECT MANAGEMENT
// ============================================================================

model Project {
  id          String          @id @default(cuid())
  name        String          @db.VarChar(255)
  description String?
  clientId    String
  status      String          @default("planning") @db.VarChar(50)
  priority    String          @default("medium") @db.VarChar(20)
  budget      Decimal?        @db.Decimal(10, 2)
  startDate   DateTime?
  endDate     DateTime?
  createdAt   DateTime        @default(now())
  updatedAt   DateTime        @updatedAt
  
  // Relations
  members        ProjectMember[]
  client         Client                @relation(fields: [clientId], references: [id], onDelete: Cascade)
  tasks          Task[]
  timeEntries    TimeEntry[]
  communications ClientCommunication[]

  @@index([clientId])
  @@index([status])
  @@map("projects")
}

model ProjectMember {
  id        String    @id @default(cuid())
  projectId String
  userId    String
  role      String    @default("member") @db.VarChar(50)
  joinedAt  DateTime  @default(now())
  leftAt    DateTime?
  
  // Relations
  project   Project   @relation(fields: [projectId], references: [id], onDelete: Cascade)
  user      User      @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([projectId, userId])
  @@map("project_members")
}

model Task {
  id             String      @id @default(cuid())
  projectId      String
  title          String      @db.VarChar(255)
  description    String?
  assignedTo     String?
  status         String      @default("todo") @db.VarChar(50)
  priority       String      @default("medium") @db.VarChar(20)
  estimatedHours Decimal?    @db.Decimal(5, 2)
  actualHours    Decimal?    @db.Decimal(5, 2)
  startDate      DateTime?
  dueDate        DateTime?
  completedAt    DateTime?
  tags           String[]    @default([])
  dependencies   String[]    @default([])
  createdAt      DateTime    @default(now())
  updatedAt      DateTime    @updatedAt
  
  // Relations
  assignedUser   User?       @relation(fields: [assignedTo], references: [id])
  project        Project     @relation(fields: [projectId], references: [id], onDelete: Cascade)
  timeEntries    TimeEntry[]

  @@index([projectId])
  @@index([assignedTo])
  @@index([status])
  @@index([dueDate])
  @@map("tasks")
}

model TimeEntry {
  id          String    @id @default(cuid())
  projectId   String?
  taskId      String?
  userId      String
  description String?
  startTime   DateTime
  endTime     DateTime?
  duration    Int?
  billable    Boolean   @default(true)
  hourlyRate  Decimal?  @db.Decimal(8, 2)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt
  
  // Relations
  project     Project?  @relation(fields: [projectId], references: [id])
  task        Task?     @relation(fields: [taskId], references: [id])
  user        User      @relation(fields: [userId], references: [id])

  @@index([userId])
  @@index([projectId])
  @@index([startTime])
  @@map("time_entries")
}

// ============================================================================
// FINANCIAL MANAGEMENT - Enhanced with Schema Fixes
// ============================================================================

model Invoice {
  id             String        @id @default(cuid())
  invoiceNumber  String        @unique @db.VarChar(50)
  
  // Optional client/user for flexibility
  clientId       String?
  userId         String?
  
  // Core financial fields
  totalAmount    Decimal       @db.Decimal(10, 2)
  taxAmount      Decimal       @default(0) @db.Decimal(10, 2)
  discountAmount Decimal       @default(0) @db.Decimal(10, 2)
  status         String        @default("draft") @db.VarChar(20)
  notes          String?
  dueDate        DateTime
  issuedAt       DateTime      @default(now())
  paidAt         DateTime?
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt
  
  // Enhanced fields for direct customer invoices
  customerName   String?       @db.VarChar(255)
  phoneNumber    String?       @db.VarChar(50)
  email          String?       @db.VarChar(100)
  amountPaid     Decimal       @default(0) @db.Decimal(10, 2)
  balance        Decimal       @default(0) @db.Decimal(10, 2)
  pdfUrl         String?       @db.VarChar(500)
  quoteId        String?       @db.VarChar(100)
  
  // Relations
  items          InvoiceItem[]
  client         Client?       @relation(fields: [clientId], references: [id])
  user           User?         @relation(fields: [userId], references: [id])

  @@index([clientId])
  @@index([status])
  @@index([dueDate])
  @@map("invoices")
}

model InvoiceItem {
  id          String   @id @default(cuid())
  invoiceId   String
  serviceId   String?
  description String   @db.VarChar(255)
  quantity    Int      @default(1)
  unitPrice   Decimal  @db.Decimal(10, 2)
  totalPrice  Decimal  @db.Decimal(10, 2)
  
  // Relations
  invoice     Invoice  @relation(fields: [invoiceId], references: [id], onDelete: Cascade)
  service     Service? @relation(fields: [serviceId], references: [id])

  @@map("invoice_items")
}

model Quote {
  id             String      @id @default(cuid())
  quoteNumber    String      @unique @db.VarChar(50)
  
  // Optional client/user for flexibility
  clientId       String?
  userId         String?
  
  // Core financial fields
  totalAmount    Decimal     @db.Decimal(10, 2)
  taxAmount      Decimal     @default(0) @db.Decimal(10, 2)
  discountAmount Decimal     @default(0) @db.Decimal(10, 2)
  status         String      @default("draft") @db.VarChar(20)
  notes          String?
  validUntil     DateTime
  createdAt      DateTime    @default(now())
  updatedAt      DateTime    @updatedAt
  
  // Enhanced fields for direct customer quotes
  customerName   String?     @db.VarChar(255)
  phoneNumber    String?     @db.VarChar(50)
  email          String?     @db.VarChar(100)
  subtotalAmount Decimal     @default(0) @db.Decimal(10, 2)
  discountType   String?     @db.VarChar(20)
  discountValue  Decimal?    @db.Decimal(10, 4)
  pdfUrl         String?     @db.VarChar(500)
  issuedAt       DateTime    @default(now())
  
  // Relations
  items          QuoteItem[]
  client         Client?     @relation(fields: [clientId], references: [id])
  user           User?       @relation(fields: [userId], references: [id])

  @@index([clientId])
  @@index([status])
  @@map("quotes")
}

model QuoteItem {
  id          String   @id @default(cuid())
  quoteId     String
  serviceId   String?
  description String   @db.VarChar(255)
  quantity    Int      @default(1)
  unitPrice   Decimal  @db.Decimal(10, 2)
  totalPrice  Decimal  @db.Decimal(10, 2)
  
  // Enhanced fields for quote items
  itemName    String?  @db.VarChar(255)
  itemType    String   @default("service") @db.VarChar(50)
  
  // Relations
  quote       Quote    @relation(fields: [quoteId], references: [id], onDelete: Cascade)
  service     Service? @relation(fields: [serviceId], references: [id])

  @@map("quote_items")
}

model Receipt {
  id             String        @id @default(cuid())
  receiptNumber  String        @unique @db.VarChar(50)
  
  // Optional client/user for flexibility
  clientId       String?
  userId         String?
  
  // Core financial fields
  totalAmount    Decimal       @db.Decimal(10, 2)
  taxAmount      Decimal       @default(0) @db.Decimal(10, 2)
  discountAmount Decimal       @default(0) @db.Decimal(10, 2)
  paymentMethod  String?       @db.VarChar(50)
  notes          String?
  issuedAt       DateTime      @default(now())
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt
  
  // Enhanced fields for comprehensive M-Pesa receipts
  customerName   String?       @db.VarChar(255)
  phoneNumber    String?       @db.VarChar(50)
  email          String?       @db.VarChar(100)
  status         String?       @default("issued") @db.VarChar(20)
  amountPaid     Decimal?      @db.Decimal(10, 2)
  balance        Decimal?      @db.Decimal(10, 2)
  paidAt         DateTime?
  transactionId  String?       @db.VarChar(100)
  pdfUrl         String?       @db.VarChar(500)
  
  // Relations
  items          ReceiptItem[]
  client         Client?       @relation(fields: [clientId], references: [id])
  user           User?         @relation(fields: [userId], references: [id])

  @@index([clientId])
  @@index([issuedAt])
  @@map("receipts")
}

model ReceiptItem {
  id           String   @id @default(cuid())
  receiptId    String
  serviceId    String?
  description  String   @db.VarChar(255)
  quantity     Int      @default(1)
  unitPrice    Decimal  @db.Decimal(10, 2)
  totalPrice   Decimal  @db.Decimal(10, 2)
  
  // Enhanced discount support for comprehensive receipts
  discount      Decimal  @default(0) @db.Decimal(10, 2)
  discountType  String   @default("fixed") @db.VarChar(20)
  discountRate  Decimal  @default(0) @db.Decimal(5, 2)
  
  // Support for custom services
  isCustomService Boolean @default(false)
  
  // Relations
  receipt      Receipt  @relation(fields: [receiptId], references: [id], onDelete: Cascade)
  service      Service? @relation(fields: [serviceId], references: [id])

  @@map("receipt_items")
} 