generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model BlogPost {
  title       String    @db.VarChar(255)
  slug        String    @unique @db.VarChar(255)
  content     String
  excerpt     String?
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @default(now()) @updatedAt @map("updated_at")
  publishedAt DateTime? @map("published_at")
  author      String?   @db.VarChar(100)
  category    String?   @db.VarChar(100)
  status      String    @default("draft") @db.VarChar(20)
  tags        String[]  @default([])
  id          Int       @id @default(autoincrement())

  @@map("blog_posts")
}

model Category {
  name        String   @db.VarChar(100)
  slug        String   @unique @db.VarChar(100)
  description String?
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @default(now()) @updatedAt @map("updated_at")
  id          Int      @id @default(autoincrement())

  @@map("categories")
}

model WebsitePortfolio {
  id          String    @id @default(uuid())
  title       String    @db.VarChar(255)
  description String?
  url         String
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @default(now()) @updatedAt @map("updated_at")
  category    String    @db.VarChar(100)
  featured    Boolean   @default(false)
  createdBy   String?   @map("created_by")
  deletedAt   DateTime? @map("deleted_at")
  imageKey    String    @map("image_key")
  updatedBy   String?   @map("updated_by")

  @@map("website_portfolio")
}

model Pricing {
  description String?
  price       Int
  features    String[] @default([])
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @default(now()) @updatedAt @map("updated_at")
  icon        String?  @db.VarChar(100)
  popular     Boolean? @default(false)
  service     String   @db.VarChar(255)
  id          Int      @id @default(autoincrement())
  category    String?  @default("Other") @db.VarChar(100)
  imageUrl    String?
  imageUrl2   String?
  imageUrl3   String?

  @@map("pricing")
}

model technology_items {
  id                Int               @id @default(autoincrement())
  name              String            @db.VarChar(100)
  icon              String            @db.VarChar(50)
  level             String            @db.VarChar(20)
  experience        String            @db.VarChar(50)
  stackId           Int
  created_at        DateTime          @default(now())
  updated_at        DateTime          @default(now())
  technology_stacks technology_stacks @relation(fields: [stackId], references: [id])
}

model technology_stacks {
  id               Int                @id @default(autoincrement())
  category         String             @db.VarChar(100)
  icon             String             @db.VarChar(50)
  description      String
  bgColor          String             @db.VarChar(100)
  textColor        String             @db.VarChar(100)
  created_at       DateTime           @default(now())
  updated_at       DateTime           @default(now())
  technology_items technology_items[]
}

model StorageConfig {
  id              String   @id @default(uuid())
  provider        String   @db.VarChar(50)
  region          String   @db.VarChar(100)
  endpoint        String   @db.VarChar(255)
  bucketName      String   @db.VarChar(100)
  accessKeyId     String   @db.VarChar(255)
  secretAccessKey String   @db.VarChar(255)
  isDefault       Boolean  @default(false)
  createdAt       DateTime @default(now()) @map("created_at")
  updatedAt       DateTime @default(now()) @updatedAt @map("updated_at")

  @@map("storage_config")
}

model SiteSettings {
  id                String   @id @default(uuid())
  siteName          String   @db.VarChar(100)
  siteDescription   String?  @db.VarChar(255)
  contactEmail      String?  @db.VarChar(100)
  address           String?  @db.VarChar(255)
  facebookUrl       String?  @db.VarChar(255)
  twitterUrl        String?  @db.VarChar(255)
  instagramUrl      String?  @db.VarChar(255)
  linkedinUrl       String?  @db.VarChar(255)
  createdAt         DateTime @default(now()) @map("created_at")
  updatedAt         DateTime @default(now()) @updatedAt @map("updated_at")
  googleAnalyticsId String?  @db.VarChar(50)
  metaDescription   String?
  metaTitle         String?  @db.VarChar(255)
  phoneNumber       String?  @db.VarChar(50)
  tiktokUrl         String?  @db.VarChar(255)

  @@map("site_settings")
}

model TeamMember {
  id           String   @id @default(uuid())
  name         String   @db.VarChar(100)
  role         String   @db.VarChar(100)
  bio          String
  order        Int      @default(0)
  linkedinUrl  String?  @db.VarChar(255)
  twitterUrl   String?  @db.VarChar(255)
  githubUrl    String?  @db.VarChar(255)
  emailAddress String?  @db.VarChar(100)
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @default(now()) @updatedAt @map("updated_at")
  imageKey     String   @map("image_key")

  @@map("team_members")
}

model DatabaseBackup {
  id          String   @id @default(uuid())
  filename    String   @db.VarChar(255)
  description String?
  size        Int      @default(0)
  path        String   @db.VarChar(255)
  type        String   @db.VarChar(50)
  status      String   @default("completed") @db.VarChar(50)
  createdBy   String?  @db.VarChar(100)
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @default(now()) @updatedAt @map("updated_at")

  @@map("database_backups")
}

model ScheduledBlogPost {
  id             String   @id @default(uuid())
  category       String?  @db.VarChar(100)
  tone           String?  @db.VarChar(50)
  length         String?  @db.VarChar(20)
  targetAudience String?  @db.VarChar(255)
  scheduledDate  DateTime
  status         String   @default("pending") @db.VarChar(20)
  blogPostId     String?  @db.VarChar(255)
  createdAt      DateTime @default(now()) @map("created_at")
  updatedAt      DateTime @default(now()) @updatedAt @map("updated_at")

  @@map("scheduled_blog_posts")
}


