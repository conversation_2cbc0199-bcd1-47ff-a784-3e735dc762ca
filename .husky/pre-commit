#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

echo "🔍 Running Prisma consistency checks..."

# 1. Format and validate Prisma schema
echo "📝 Formatting Prisma schema..."
npx prisma format

echo "✅ Validating Prisma schema..."
npx prisma validate

# 2. Generate Prisma client (ensure it's up to date)
echo "🔄 Generating Prisma client..."
npx prisma generate

# 3. Run custom validation script
echo "🔍 Running consistency validation..."
node scripts/validate-prisma-consistency.js

# 4. Type checking to catch TypeScript errors
echo "📋 Running TypeScript type check..."
npm run type-check

echo "✅ All pre-commit checks passed!" 