name: Production Deployment with Prisma Migration

on:
  push:
    branches: [main]
    paths:
      - 'prisma/schema.prisma'
      - 'prisma/migrations/**'
  workflow_dispatch:
    inputs:
      skip_migration:
        description: 'Skip database migration'
        required: false
        default: 'false'

jobs:
  validate-schema:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Validate Prisma schema
        run: |
          npx prisma validate
          npm run db:validate

      - name: Generate migration preview
        run: |
          echo "## Migration Preview" >> $GITHUB_STEP_SUMMARY
          npx prisma migrate diff \
            --from-empty \
            --to-schema-datamodel prisma/schema.prisma \
            --script >> migration-preview.sql
          echo "```sql" >> $GITHUB_STEP_SUMMARY
          cat migration-preview.sql >> $GITHUB_STEP_SUMMARY
          echo "```" >> $GITHUB_STEP_SUMMARY

  # Manual approval gate for production deployment
  approve-production:
    needs: validate-schema
    runs-on: ubuntu-latest
    environment: production
    steps:
      - name: Manual approval checkpoint
        run: |
          echo "🚨 PRODUCTION DEPLOYMENT APPROVAL REQUIRED"
          echo "Schema validation passed - ready for production deployment"
          echo "Review the migration preview above before approving"

  deploy-production:
    needs: approve-production
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
      - name: Deploy to production server
        uses: appleboy/ssh-action@v1.1.0
        with:
          host: ${{ secrets.PRODUCTION_HOST }}
          username: ${{ secrets.PRODUCTION_USER }}
          key: ${{ secrets.PRODUCTION_SSH_KEY }}
          script: |
            cd /var/www/mocky
            
            # Create deployment log
            echo "$(date): Starting deployment from GitHub Actions" >> deployments.log
            
            # Pull latest changes
            git pull origin main
            
            # Install dependencies
            npm ci --production
            
            # Run our comprehensive deployment script
            ./scripts/production-deploy.sh
            
            # Log completion
            echo "$(date): Deployment completed successfully" >> deployments.log

      - name: Verify deployment
        uses: appleboy/ssh-action@v1.1.0
        with:
          host: ${{ secrets.PRODUCTION_HOST }}
          username: ${{ secrets.PRODUCTION_USER }}
          key: ${{ secrets.PRODUCTION_SSH_KEY }}
          script: |
            cd /var/www/mocky
            
            # Check application health
            curl -f http://localhost:3000/api/health || exit 1
            
            # Verify database connection
            npm run test:prisma
            
            echo "✅ Production deployment verified successfully"

  notify-completion:
    needs: deploy-production
    runs-on: ubuntu-latest
    if: always()
    steps:
      - name: Notify deployment status
        run: |
          if [ "${{ needs.deploy-production.result }}" == "success" ]; then
            echo "✅ Production deployment completed successfully"
          else
            echo "❌ Production deployment failed"
            exit 1
          fi 