import NextAuth from "next-auth"

declare module "next-auth" {
  interface Session {
    user: {
      id: string
      username: string
      email: string
      name?: string | null
      role: string
      active: boolean
      lastActivity?: number
    }
  }

  interface User {
    id: string
    username: string
    email: string
    name?: string | null
    role: string
    active: boolean
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    username: string
    role: string
    active: boolean
    lastActivity?: number
  }
} 